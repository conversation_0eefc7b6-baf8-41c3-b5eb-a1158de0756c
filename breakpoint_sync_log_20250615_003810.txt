解析源项目断点...
找到 208 个断点
解析目标项目断点...
目标项目当前有 208 个断点

开始同步断点...

处理断点 1/208
  文件: intern/cycles/blender/device.cpp
  源行号: 96
  时间戳: 1
    📍 源文件 device.cpp -> 第95行:                                DeviceInfo &preferences_device) | 第96行: { [目标行] | 第97行:   PointerRNA cscene = RNA_pointer_get(&b_scene.ptr, "cycles");
    🔍 找到4个精确匹配: [23, 34, 62, 86], 选择最接近的: 86
    ✅ 目标文件 device.cpp -> 第85行:                                DeviceInfo &preferences_device) | 第86行: { [目标行] | 第87行:   PointerRNA cscene = RNA_pointer_get(&b_scene.ptr, "cycles");
  ✅ 找到匹配行: 86
  📝 更新行号: 96 -> 86

处理断点 2/208
  文件: intern/cycles/device/device.cpp
  源行号: 187
  时间戳: 2
    📍 源文件 device.cpp -> 第187行:   vector<DeviceType> types;
    ✅ 目标文件 device.cpp -> 第193行:   vector<DeviceType> types;
  ✅ 找到匹配行: 193
  📝 更新行号: 187 -> 193

处理断点 3/208
  文件: source/blender/python/intern/bpy_props.cc
  源行号: 1991
  时间戳: 4
    📍 源文件 bpy_props.cc -> 第1990行:   int default_int_cmp = 0; | 第1991行:  [目标行] | 第1992行:   if (is_enum_flag) {
  ❌ 未找到匹配的行

处理断点 4/208
  文件: source/blender/python/intern/bpy_props.cc
  源行号: 2048
  时间戳: 5
    📍 源文件 bpy_props.cc -> 第2047行:                             py_long_as_int(PyTuple_GET_ITEM(item, 4), &tmp.value)))) | 第2048行:     { [目标行] | 第2049行:       if (is_enum_flag) {
    🔍 找到100个精确匹配: [174, 188, 212, 219, 225, 231, 244, 256, 267, 341, 376, 400, 413, 426, 437, 461, 534, 546, 554, 565, 578, 596, 651, 697, 736, 770, 790, 848, 892, 931, 965, 985, 1044, 1088, 1127, 1161, 1185, 1244, 1287, 1334, 1385, 1434, 1544, 1590, 1634, 1674, 1686, 1701, 1720, 1788, 1807, 1871, 1896, 1942, 1981, 2012, 2023, 2033, 2057, 2079, 2103, 2125, 2149, 2175, 2212, 2264, 2326, 2347, 2371, 2512, 2553, 2644, 2711, 2810, 2822, 2903, 3011, 3085, 3187, 3199, 3285, 3390, 3468, 3581, 3593, 3682, 3787, 3912, 4002, 4052, 4105, 4156, 4236, 4307, 4379, 4439, 4563, 4574, 4607, 4643], 选择最接近的: 2057
    ✅ 目标文件 bpy_props.cc -> 第2056行:                                                    PyObject *set_fn) | 第2057行: { [目标行] | 第2058行:   BooleanArrayPropertyGetFunc rna_get_fn = nullptr;
  ✅ 找到匹配行: 2057
  📝 更新行号: 2048 -> 2057

处理断点 5/208
  文件: source/blender/makesrna/intern/rna_access.cc
  源行号: 565
  时间戳: 6
    📍 源文件 rna_access.cc -> 第564行:     IDProperty *idprop = (IDProperty *)prop; | 第565行:  [目标行] | 第566行:     if (idprop->type == IDP_ARRAY) {
  ❌ 未找到匹配的行

处理断点 6/208
  文件: source/blender/makesrna/intern/rna_access.cc
  源行号: 769
  时间戳: 7
    📍 源文件 rna_access.cc -> 第769行:     /* most common case */
    ✅ 目标文件 rna_access.cc -> 第788行:     /* most common case */
  ✅ 找到匹配行: 788
  📝 更新行号: 769 -> 788

处理断点 7/208
  文件: source/blender/makesrna/intern/rna_access.cc
  源行号: 761
  时间戳: 10
    📍 源文件 rna_access.cc -> 第761行:     PointerRNA r_ptr; /* only support single level props */
    ✅ 目标文件 rna_access.cc -> 第780行:     PointerRNA r_ptr; /* only support single level props */
  ✅ 找到匹配行: 780
  📝 更新行号: 761 -> 780

处理断点 8/208
  文件: source/blender/makesrna/intern/rna_access.cc
  源行号: 674
  时间戳: 11
    📍 源文件 rna_access.cc -> 第673行: PropertyRNA *RNA_struct_iterator_property(StructRNA *type) | 第674行: { [目标行] | 第675行:   return type->iteratorproperty;
    🔍 找到349个精确匹配: [81, 89, 108, 114, 123, 128, 138, 152, 171, 176, 187, 198, 205, 213, 235, 240, 249, 257, 276, 282, 295, 309, 314, 332, 344, 359, 371, 393, 486, 565, 575, 582, 601, 609, 624, 640, 645, 650, 655, 660, 668, 673, 678, 683, 688, 693, 698, 703, 714, 719, 724, 729, 734, 739, 754, 776, 783, 801, 825, 846, 881, 897, 919, 934, 939, 945, 956, 991, 996, 1001, 1012, 1025, 1030, 1035, 1040, 1045, 1056, 1088, 1116, 1131, 1145, 1150, 1155, 1160, 1181, 1186, 1204, 1209, 1214, 1219, 1224, 1233, 1238, 1243, 1254, 1263, 1284, 1295, 1321, 1350, 1388, 1434, 1476, 1525, 1542, 1559, 1565, 1601, 1635, 1646, 1710, 1719, 1777, 1793, 1853, 1881, 1893, 1905, 1917, 1927, 1940, 1950, 1961, 1972, 1985, 2003, 2022, 2038, 2066, 2079, 2097, 2102, 2107, 2112, 2117, 2122, 2130, 2193, 2198, 2203, 2208, 2218, 2225, 2241, 2252, 2261, 2283, 2304, 2323, 2407, 2414, 2420, 2425, 2435, 2469, 2509, 2525, 2544, 2558, 2601, 2615, 2644, 2704, 2719, 2746, 2780, 2836, 2860, 2880, 2912, 2929, 2943, 2977, 2991, 3028, 3052, 3096, 3111, 3137, 3155, 3169, 3205, 3229, 3252, 3290, 3312, 3327, 3341, 3381, 3395, 3432, 3456, 3510, 3525, 3551, 3573, 3587, 3617, 3641, 3674, 3704, 3742, 3768, 3799, 3835, 3858, 3883, 3905, 3926, 3935, 3945, 3964, 3994, 4013, 4045, 4087, 4108, 4159, 4168, 4195, 4216, 4226, 4258, 4274, 4299, 4311, 4336, 4351, 4384, 4444, 4488, 4531, 4566, 4588, 4595, 4602, 4609, 4618, 4649, 4712, 4721, 4735, 4744, 4753, 4869, 4899, 4923, 5014, 5023, 5265, 5292, 5303, 5313, 5329, 5345, 5354, 5366, 5395, 5411, 5418, 5426, 5434, 5445, 5456, 5468, 5480, 5492, 5503, 5515, 5527, 5539, 5550, 5562, 5574, 5586, 5597, 5609, 5627, 5658, 5668, 5678, 5688, 5698, 5712, 5726, 5737, 5749, 5761, 5773, 5785, 5797, 5809, 5821, 5832, 5843, 5853, 5863, 5871, 5883, 5895, 5904, 5909, 5918, 5949, 5963, 5981, 6058, 6072, 6084, 6090, 6117, 6154, 6174, 6189, 6308, 6333, 6338, 6343, 6348, 6353, 6358, 6365, 6379, 6386, 6393, 6492, 6526, 6531, 6536, 6541, 6557, 6569, 6574, 6603, 6619, 6673, 6689, 6711, 6730, 6741, 6749, 6761, 6766, 6861, 6888, 6901, 6911, 6926, 6936], 选择最接近的: 673
    ✅ 目标文件 rna_access.cc -> 第672行: const char *RNA_struct_ui_description_raw(const StructRNA *type) | 第673行: { [目标行] | 第674行:   return type->description;
  ✅ 找到匹配行: 673
  📝 更新行号: 674 -> 673

处理断点 9/208
  文件: source/blender/makesrna/intern/rna_access.cc
  源行号: 4562
  时间戳: 12
    📍 源文件 rna_access.cc -> 第4562行:   int index;
    ✅ 目标文件 rna_access.cc -> 第4713行:   int index;
  ✅ 找到匹配行: 4713
  📝 更新行号: 4562 -> 4713

处理断点 10/208
  文件: source/blender/makesrna/intern/rna_access.cc
  源行号: 4498
  时间戳: 13
    📍 源文件 rna_access.cc -> 第4497行:     PointerRNA *ptr, PropertyRNA *prop, const char *key, PointerRNA *r_ptr, int *r_index) | 第4498行: { [目标行] | 第4499行:   CollectionPropertyRNA *cprop = (CollectionPropertyRNA *)rna_ensure_property(prop);
    🔍 找到349个精确匹配: [81, 89, 108, 114, 123, 128, 138, 152, 171, 176, 187, 198, 205, 213, 235, 240, 249, 257, 276, 282, 295, 309, 314, 332, 344, 359, 371, 393, 486, 565, 575, 582, 601, 609, 624, 640, 645, 650, 655, 660, 668, 673, 678, 683, 688, 693, 698, 703, 714, 719, 724, 729, 734, 739, 754, 776, 783, 801, 825, 846, 881, 897, 919, 934, 939, 945, 956, 991, 996, 1001, 1012, 1025, 1030, 1035, 1040, 1045, 1056, 1088, 1116, 1131, 1145, 1150, 1155, 1160, 1181, 1186, 1204, 1209, 1214, 1219, 1224, 1233, 1238, 1243, 1254, 1263, 1284, 1295, 1321, 1350, 1388, 1434, 1476, 1525, 1542, 1559, 1565, 1601, 1635, 1646, 1710, 1719, 1777, 1793, 1853, 1881, 1893, 1905, 1917, 1927, 1940, 1950, 1961, 1972, 1985, 2003, 2022, 2038, 2066, 2079, 2097, 2102, 2107, 2112, 2117, 2122, 2130, 2193, 2198, 2203, 2208, 2218, 2225, 2241, 2252, 2261, 2283, 2304, 2323, 2407, 2414, 2420, 2425, 2435, 2469, 2509, 2525, 2544, 2558, 2601, 2615, 2644, 2704, 2719, 2746, 2780, 2836, 2860, 2880, 2912, 2929, 2943, 2977, 2991, 3028, 3052, 3096, 3111, 3137, 3155, 3169, 3205, 3229, 3252, 3290, 3312, 3327, 3341, 3381, 3395, 3432, 3456, 3510, 3525, 3551, 3573, 3587, 3617, 3641, 3674, 3704, 3742, 3768, 3799, 3835, 3858, 3883, 3905, 3926, 3935, 3945, 3964, 3994, 4013, 4045, 4087, 4108, 4159, 4168, 4195, 4216, 4226, 4258, 4274, 4299, 4311, 4336, 4351, 4384, 4444, 4488, 4531, 4566, 4588, 4595, 4602, 4609, 4618, 4649, 4712, 4721, 4735, 4744, 4753, 4869, 4899, 4923, 5014, 5023, 5265, 5292, 5303, 5313, 5329, 5345, 5354, 5366, 5395, 5411, 5418, 5426, 5434, 5445, 5456, 5468, 5480, 5492, 5503, 5515, 5527, 5539, 5550, 5562, 5574, 5586, 5597, 5609, 5627, 5658, 5668, 5678, 5688, 5698, 5712, 5726, 5737, 5749, 5761, 5773, 5785, 5797, 5809, 5821, 5832, 5843, 5853, 5863, 5871, 5883, 5895, 5904, 5909, 5918, 5949, 5963, 5981, 6058, 6072, 6084, 6090, 6117, 6154, 6174, 6189, 6308, 6333, 6338, 6343, 6348, 6353, 6358, 6365, 6379, 6386, 6393, 6492, 6526, 6531, 6536, 6541, 6557, 6569, 6574, 6603, 6619, 6673, 6689, 6711, 6730, 6741, 6749, 6761, 6766, 6861, 6888, 6901, 6911, 6926, 6936], 选择最接近的: 4488
    ✅ 目标文件 rna_access.cc -> 第4487行: bool RNA_property_collection_move(PointerRNA *ptr, PropertyRNA *prop, int key, int pos) | 第4488行: { [目标行] | 第4489行:   IDProperty *idprop;
  ✅ 找到匹配行: 4488
  📝 更新行号: 4498 -> 4488

处理断点 11/208
  文件: source/blender/makesrna/intern/rna_access.cc
  源行号: 558
  时间戳: 14
    📍 源文件 rna_access.cc -> 第557行:   /* the quick version if we don't need the idproperty */ | 第558行:  [目标行] | 第559行:   if (prop->magic == RNA_MAGIC) {
  ❌ 未找到匹配的行

处理断点 12/208
  文件: source/blender/makesrna/intern/rna_access.cc
  源行号: 4510
  时间戳: 17
    📍 源文件 rna_access.cc -> 第4510行:     /* we have a callback defined, use it */
    🔍 找到3个精确匹配: [4624, 4661, 4727], 选择最接近的: 4624
    ✅ 目标文件 rna_access.cc -> 第4624行:     /* we have a callback defined, use it */
  ✅ 找到匹配行: 4624
  📝 更新行号: 4510 -> 4624

处理断点 13/208
  文件: intern/cycles/blender/python.cpp
  源行号: 898
  时间戳: 19
    📍 源文件 python.cpp -> 第898行:   PyTuple_SET_ITEM(list, 5, PyBool_FromLong(has_hiprt));
    ✅ 目标文件 python.cpp -> 第694行:   PyTuple_SET_ITEM(list, 5, PyBool_FromLong(has_hiprt));
  ✅ 找到匹配行: 694
  📝 更新行号: 898 -> 694

处理断点 14/208
  文件: source/blender/makesrna/intern/rna_access.cc
  源行号: 563
  时间戳: 20
    📍 源文件 rna_access.cc -> 第563行:   { [目标行] | 第564行:     IDProperty *idprop = (IDProperty *)prop;
    🔍 找到349个精确匹配: [81, 89, 108, 114, 123, 128, 138, 152, 171, 176, 187, 198, 205, 213, 235, 240, 249, 257, 276, 282, 295, 309, 314, 332, 344, 359, 371, 393, 486, 565, 575, 582, 601, 609, 624, 640, 645, 650, 655, 660, 668, 673, 678, 683, 688, 693, 698, 703, 714, 719, 724, 729, 734, 739, 754, 776, 783, 801, 825, 846, 881, 897, 919, 934, 939, 945, 956, 991, 996, 1001, 1012, 1025, 1030, 1035, 1040, 1045, 1056, 1088, 1116, 1131, 1145, 1150, 1155, 1160, 1181, 1186, 1204, 1209, 1214, 1219, 1224, 1233, 1238, 1243, 1254, 1263, 1284, 1295, 1321, 1350, 1388, 1434, 1476, 1525, 1542, 1559, 1565, 1601, 1635, 1646, 1710, 1719, 1777, 1793, 1853, 1881, 1893, 1905, 1917, 1927, 1940, 1950, 1961, 1972, 1985, 2003, 2022, 2038, 2066, 2079, 2097, 2102, 2107, 2112, 2117, 2122, 2130, 2193, 2198, 2203, 2208, 2218, 2225, 2241, 2252, 2261, 2283, 2304, 2323, 2407, 2414, 2420, 2425, 2435, 2469, 2509, 2525, 2544, 2558, 2601, 2615, 2644, 2704, 2719, 2746, 2780, 2836, 2860, 2880, 2912, 2929, 2943, 2977, 2991, 3028, 3052, 3096, 3111, 3137, 3155, 3169, 3205, 3229, 3252, 3290, 3312, 3327, 3341, 3381, 3395, 3432, 3456, 3510, 3525, 3551, 3573, 3587, 3617, 3641, 3674, 3704, 3742, 3768, 3799, 3835, 3858, 3883, 3905, 3926, 3935, 3945, 3964, 3994, 4013, 4045, 4087, 4108, 4159, 4168, 4195, 4216, 4226, 4258, 4274, 4299, 4311, 4336, 4351, 4384, 4444, 4488, 4531, 4566, 4588, 4595, 4602, 4609, 4618, 4649, 4712, 4721, 4735, 4744, 4753, 4869, 4899, 4923, 5014, 5023, 5265, 5292, 5303, 5313, 5329, 5345, 5354, 5366, 5395, 5411, 5418, 5426, 5434, 5445, 5456, 5468, 5480, 5492, 5503, 5515, 5527, 5539, 5550, 5562, 5574, 5586, 5597, 5609, 5627, 5658, 5668, 5678, 5688, 5698, 5712, 5726, 5737, 5749, 5761, 5773, 5785, 5797, 5809, 5821, 5832, 5843, 5853, 5863, 5871, 5883, 5895, 5904, 5909, 5918, 5949, 5963, 5981, 6058, 6072, 6084, 6090, 6117, 6154, 6174, 6189, 6308, 6333, 6338, 6343, 6348, 6353, 6358, 6365, 6379, 6386, 6393, 6492, 6526, 6531, 6536, 6541, 6557, 6569, 6574, 6603, 6619, 6673, 6689, 6711, 6730, 6741, 6749, 6761, 6766, 6861, 6888, 6901, 6911, 6926, 6936], 选择最接近的: 565
    ✅ 目标文件 rna_access.cc -> 第564行: IDProperty *rna_idproperty_check(PropertyRNA **prop, PointerRNA *ptr) | 第565行: { [目标行] | 第566行:   PropertyRNAOrID prop_rna_or_id;
  ✅ 找到匹配行: 565
  📝 更新行号: 563 -> 565

处理断点 15/208
  文件: source/blender/makesrna/intern/rna_access.cc
  源行号: 559
  时间戳: 21
    📍 源文件 rna_access.cc -> 第559行:   if (prop->magic == RNA_MAGIC) {
    🔍 找到10个精确匹配: [345, 360, 372, 498, 578, 602, 610, 627, 3156, 3574], 选择最接近的: 578
    ✅ 目标文件 rna_access.cc -> 第578行:   if (prop->magic == RNA_MAGIC) {
  ✅ 找到匹配行: 578
  📝 更新行号: 559 -> 578

处理断点 16/208
  文件: source/blender/python/intern/bpy_props.cc
  源行号: 2209
  时间戳: 26
    📍 源文件 bpy_props.cc -> 第2209行:         is_enum_flag = true;
  ❌ 未找到匹配的行

处理断点 17/208
  文件: intern/cycles/blender/python.cpp
  源行号: 402
  时间戳: 28
    📍 源文件 python.cpp -> 第400行:     return NULL; | 第402行:  [目标行] | 第403行:   DeviceType type = Device::type_from_string(type_name);
  ❌ 未找到匹配的行

处理断点 18/208
  文件: intern/cycles/device/device.cpp
  源行号: 226
  时间戳: 30
    📍 源文件 device.cpp -> 第225行:       devices_initialized_mask |= DEVICE_MASK_CUDA; | 第226行:     } [目标行] | 第227行:     if (mask & DEVICE_MASK_CUDA) {
    🔍 找到136个精确匹配: [59, 62, 63, 75, 87, 94, 102, 110, 122, 126, 129, 135, 138, 141, 144, 147, 150, 153, 156, 159, 165, 168, 171, 174, 177, 180, 183, 186, 189, 213, 216, 231, 233, 237, 238, 239, 247, 249, 252, 253, 261, 263, 266, 267, 275, 277, 280, 281, 288, 291, 292, 299, 301, 304, 305, 309, 317, 327, 336, 337, 338, 348, 349, 350, 360, 361, 362, 372, 373, 374, 378, 389, 420, 423, 424, 428, 429, 432, 440, 443, 454, 457, 462, 473, 479, 486, 492, 497, 502, 507, 519, 521, 535, 538, 539, 543, 556, 570, 587, 596, 601, 608, 609, 632, 635, 636, 637, 663, 675, 676, 687, 694, 700, 701, 707, 711, 712, 718, 726, 743, 746, 749, 755, 776, 779, 781, 786, 793, 799, 806, 807, 815, 823, 827, 828, 830], 选择最接近的: 231
    ✅ 目标文件 device.cpp -> 第230行:         device_cuda_info(cuda_devices); | 第231行:       } [目标行] | 第232行:       devices_initialized_mask |= DEVICE_MASK_CUDA;
  ✅ 找到匹配行: 231
  📝 更新行号: 226 -> 231

处理断点 19/208
  文件: intern/cycles/device/device.cpp
  源行号: 221
  时间戳: 31
    📍 源文件 device.cpp -> 第221行:     if (!(devices_initialized_mask & DEVICE_MASK_CUDA)) {
    ✅ 目标文件 device.cpp -> 第228行:     if (!(devices_initialized_mask & DEVICE_MASK_CUDA)) {
  ✅ 找到匹配行: 228
  📝 更新行号: 221 -> 228

处理断点 20/208
  文件: intern/cycles/device/device.cpp
  源行号: 228
  时间戳: 32
    📍 源文件 device.cpp -> 第228行:       foreach (DeviceInfo &info, cuda_devices) {
    🔍 找到1个上下文匹配, 选择最佳: 行235 (匹配度: 0.91)
    ✅ 目标文件 device.cpp -> 第235行:       for (DeviceInfo &info : cuda_devices) {
  ✅ 找到匹配行: 235
  📝 更新行号: 228 -> 235

处理断点 21/208
  文件: intern/cycles/device/cuda/device.cpp
  源行号: 103
  时间戳: 33
    📍 源文件 device.cpp -> 第103行: #ifdef WITH_CUDA
    🔍 找到5个精确匹配: [10, 70, 84, 108, 228], 选择最接近的: 108
    ✅ 目标文件 device.cpp -> 第108行: #ifdef WITH_CUDA
  ✅ 找到匹配行: 108
  📝 更新行号: 103 -> 108

处理断点 22/208
  文件: intern/cycles/device/cuda/device.cpp
  源行号: 33
  时间戳: 34
    📍 源文件 device.cpp -> 第33行:   initialized = true;
    ✅ 目标文件 device.cpp -> 第35行:   initialized = true;
  ✅ 找到匹配行: 35
  📝 更新行号: 33 -> 35

处理断点 23/208
  文件: extern/cuew/src/cuew.c
  源行号: 685
  时间戳: 35
    📍 源文件 cuew.c -> 第685行: int cuewInit(cuuint32_t flags) {
    ✅ 目标文件 cuew.c -> 第695行: int cuewInit(cuuint32_t flags) {
  ✅ 找到匹配行: 695
  📝 更新行号: 685 -> 695

处理断点 24/208
  文件: extern/cuew/src/cuew.c
  源行号: 348
  时间戳: 36
    📍 源文件 cuew.c -> 第347行:   int error, driver_version; | 第348行:  [目标行] | 第349行:   if (initialized) {
  ❌ 未找到匹配的行

处理断点 25/208
  文件: extern/cuew/src/cuew.c
  源行号: 315
  时间戳: 38
    📍 源文件 cuew.c -> 第315行:   while (paths[i] != NULL) {
    ✅ 目标文件 cuew.c -> 第320行:   while (paths[i] != NULL) {
  ✅ 找到匹配行: 320
  📝 更新行号: 315 -> 320

处理断点 26/208
  文件: source/blender/draw/intern/draw_manager_c.cc
  源行号: 1693
  时间戳: 45
  ❌ 目标文件不存在: source\blender\draw\intern\draw_manager_c.cc

处理断点 27/208
  文件: source/blender/editors/space_view3d/view3d_draw.cc
  源行号: 1763
  时间戳: 46
    📍 源文件 view3d_draw.cc -> 第1763行:   /* main drawing call */
    ✅ 目标文件 view3d_draw.cc -> 第1832行:   /* main drawing call */
  ✅ 找到匹配行: 1832
  📝 更新行号: 1763 -> 1832

处理断点 28/208
  文件: source/blender/editors/space_view3d/view3d_draw.cc
  源行号: 1619
  时间戳: 47
    📍 源文件 view3d_draw.cc -> 第1619行:   view3d_update_viewer_path(C);
    ✅ 目标文件 view3d_draw.cc -> 第1685行:   view3d_update_viewer_path(C);
  ✅ 找到匹配行: 1685
  📝 更新行号: 1619 -> 1685

处理断点 29/208
  文件: source/blender/draw/intern/draw_manager_c.cc
  源行号: 1826
  时间戳: 48
  ❌ 目标文件不存在: source\blender\draw\intern\draw_manager_c.cc

处理断点 30/208
  文件: source/blender/draw/engines/eevee_next/eevee_instance.cc
  源行号: 433
  时间戳: 49
  ❌ 目标文件不存在: source\blender\draw\engines\eevee_next\eevee_instance.cc

处理断点 31/208
  文件: source/blender/draw/engines/eevee_next/eevee_view.cc
  源行号: 136
  时间戳: 50
  ❌ 目标文件不存在: source\blender\draw\engines\eevee_next\eevee_view.cc

处理断点 32/208
  文件: source/blender/draw/intern/draw_manager_c.cc
  源行号: 1127
  时间戳: 51
  ❌ 目标文件不存在: source\blender\draw\intern\draw_manager_c.cc

处理断点 33/208
  文件: source/blender/draw/intern/draw_manager_c.cc
  源行号: 3845
  时间戳: 52
  ❌ 目标文件不存在: source\blender\draw\intern\draw_manager_c.cc

处理断点 34/208
  文件: source/blender/editors/space_action/action_draw.cc
  源行号: 133
  时间戳: 53
    📍 源文件 action_draw.cc -> 第132行:   float ymin = ymax - ystep; | 第133行:  [目标行] | 第134行:   for (bAnimListElem *ale = static_cast<bAnimListElem *>(anim_data->first); ale;
  ❌ 未找到匹配的行

处理断点 35/208
  文件: source/blender/editors/space_action/action_draw.cc
  源行号: 704
  时间戳: 54
    📍 源文件 action_draw.cc -> 第704行:   blender::ColorTheme4f color;
    ✅ 目标文件 action_draw.cc -> 第699行:   blender::ColorTheme4f color;
  ✅ 找到匹配行: 699
  📝 更新行号: 704 -> 699

处理断点 36/208
  文件: source/blender/editors/space_action/action_draw.cc
  源行号: 767
  时间戳: 55
    📍 源文件 action_draw.cc -> 第767行:   blender::Vector<int> status_change_frames;
    ✅ 目标文件 action_draw.cc -> 第760行:   blender::Vector<int> status_change_frames;
  ✅ 找到匹配行: 760
  📝 更新行号: 767 -> 760

处理断点 37/208
  文件: source/blender/editors/space_action/action_draw.cc
  源行号: 875
  时间戳: 56
    📍 源文件 action_draw.cc -> 第874行:     printf("Drawing cache -------- %s\n", pid->cache->name); | 第875行:  [目标行] | 第876行:     timeline_cache_draw_single(pid, y_offset, cache_draw_height, pos_id);
  ❌ 未找到匹配的行

处理断点 38/208
  文件: source/blender/editors/transform/transform_convert_action.cc
  源行号: 1252
  时间戳: 57
    📍 源文件 transform_convert_action.cc -> 第1252行:   if (ELEM(ac.datatype, ANIMCONT_DOPESHEET, ANIMCONT_SHAPEKEY, ANIMCONT_TIMELINE)) {
    ✅ 目标文件 transform_convert_action.cc -> 第1218行:   if (ELEM(ac.datatype, ANIMCONT_DOPESHEET, ANIMCONT_SHAPEKEY, ANIMCONT_TIMELINE)) {
  ✅ 找到匹配行: 1218
  📝 更新行号: 1252 -> 1218

处理断点 39/208
  文件: source/blender/editors/space_action/action_select.cc
  源行号: 98
  时间戳: 58
    📍 源文件 action_select.cc -> 第98行:   bDopeSheet *ads = nullptr;
    ✅ 目标文件 action_select.cc -> 第94行:   bDopeSheet *ads = nullptr;
  ✅ 找到匹配行: 94
  📝 更新行号: 98 -> 94

处理断点 40/208
  文件: source/blender/editors/space_action/action_draw.cc
  源行号: 684
  时间戳: 59
    📍 源文件 action_draw.cc -> 第682行:     return; | 第684行:  [目标行] | 第685行:   immBeginAtMost(GPU_PRIM_TRIS, segments_count * 6);
  ❌ 未找到匹配的行

处理断点 41/208
  文件: source/blender/editors/space_action/action_draw.cc
  源行号: 669
  时间戳: 60
    📍 源文件 action_draw.cc -> 第669行:   int segment_end;
    🔍 找到2个精确匹配: [664, 684], 选择最接近的: 664
    ✅ 目标文件 action_draw.cc -> 第664行:   int segment_end;
  ✅ 找到匹配行: 664
  📝 更新行号: 669 -> 664

处理断点 42/208
  文件: source/blender/editors/animation/anim_channels_edit.cc
  源行号: 2155
  时间戳: 61
    📍 源文件 anim_channels_edit.cc -> 第2155行:   /* get editor data */
    🔍 找到14个精确匹配: [2310, 2572, 2644, 2907, 3201, 3342, 3391, 3453, 3577, 3714, 3900, 4091, 4806, 4966], 选择最接近的: 2310
    ✅ 目标文件 anim_channels_edit.cc -> 第2310行:   /* get editor data */
  ✅ 找到匹配行: 2310
  📝 更新行号: 2155 -> 2310

处理断点 43/208
  文件: source/blender/editors/space_action/action_select.cc
  源行号: 69
  时间戳: 63
    📍 源文件 action_select.cc -> 第69行:   UI_view2d_region_to_view(v2d, region_x, region_y, &view_x, &view_y);
    ✅ 目标文件 action_select.cc -> 第67行:   UI_view2d_region_to_view(v2d, region_x, region_y, &view_x, &view_y);
  ✅ 找到匹配行: 67
  📝 更新行号: 69 -> 67

处理断点 44/208
  文件: source/blender/editors/space_sequencer/sequencer_channels_draw.cc
  源行号: 211
  时间戳: 64
    📍 源文件 sequencer_channels_draw.cc -> 第211行:   SpaceSeq *sseq = CTX_wm_space_seq(context->C);
    ✅ 目标文件 sequencer_channels_draw.cc -> 第216行:   SpaceSeq *sseq = CTX_wm_space_seq(context->C);
  ✅ 找到匹配行: 216
  📝 更新行号: 211 -> 216

处理断点 45/208
  文件: source/blender/editors/space_sequencer/sequencer_channels_draw.cc
  源行号: 304
  时间戳: 65
    📍 源文件 sequencer_channels_draw.cc -> 第303行:                                SeqChannelDrawContext *r_context) | 第304行: { [目标行] | 第305行:   r_context->C = C;
    🔍 找到18个精确匹配: [42, 47, 53, 60, 65, 70, 78, 93, 102, 140, 176, 181, 190, 215, 270, 301, 308, 329], 选择最接近的: 301
    ✅ 目标文件 sequencer_channels_draw.cc -> 第300行: static void draw_background() | 第301行: { [目标行] | 第302行:   UI_ThemeClearColor(TH_BACK);
  ✅ 找到匹配行: 301
  📝 更新行号: 304 -> 301

处理断点 46/208
  文件: source/blender/editors/space_sequencer/sequencer_channels_draw.cc
  源行号: 78
  时间戳: 66
    📍 源文件 sequencer_channels_draw.cc -> 第78行:   /* Channel 0 is not usable, so should never be drawn. */
    ✅ 目标文件 sequencer_channels_draw.cc -> 第79行:   /* Channel 0 is not usable, so should never be drawn. */
  ✅ 找到匹配行: 79
  📝 更新行号: 78 -> 79

处理断点 47/208
  文件: source/blender/editors/space_sequencer/sequencer_channels_draw.cc
  源行号: 102
  时间戳: 67
    📍 源文件 sequencer_channels_draw.cc -> 第102行:   const float width = icon_width_get(context);
    🔍 找到2个精确匹配: [105, 143], 选择最接近的: 105
    ✅ 目标文件 sequencer_channels_draw.cc -> 第105行:   const float width = icon_width_get(context);
  ✅ 找到匹配行: 105
  📝 更新行号: 102 -> 105

处理断点 48/208
  文件: source/blender/editors/space_action/space_action.cc
  源行号: 182
  时间戳: 70
    📍 源文件 space_action.cc -> 第182行:   /* time grid */
    ✅ 目标文件 space_action.cc -> 第218行:   /* time grid */
  ✅ 找到匹配行: 218
  📝 更新行号: 182 -> 218

处理断点 49/208
  文件: source/blender/editors/space_action/action_draw.cc
  源行号: 512
  时间戳: 72
    📍 源文件 action_draw.cc -> 第511行:   immUnbindProgram(); | 第512行:  [目标行] | 第513行:   draw_keyframes(ac, v2d, saction, anim_data);
  ❌ 未找到匹配的行

处理断点 50/208
  文件: source/blender/editors/space_action/action_draw.cc
  源行号: 353
  时间戳: 73
    📍 源文件 action_draw.cc -> 第353行:     /* Add channels to list to draw later. */
    ✅ 目标文件 action_draw.cc -> 第352行:     /* Add channels to list to draw later. */
  ✅ 找到匹配行: 352
  📝 更新行号: 353 -> 352

处理断点 51/208
  文件: source/blender/editors/space_action/action_draw.cc
  源行号: 463
  时间戳: 74
    📍 源文件 action_draw.cc -> 第463行:   /* Drawing happens in here. */
    ✅ 目标文件 action_draw.cc -> 第466行:   /* Drawing happens in here. */
  ✅ 找到匹配行: 466
  📝 更新行号: 463 -> 466

处理断点 52/208
  文件: source/blender/editors/space_action/action_draw.cc
  源行号: 339
  时间戳: 75
    📍 源文件 action_draw.cc -> 第339行:     /* check if visible */
    🔍 找到5个精确匹配: [77, 96, 140, 198, 340], 选择最接近的: 340
    ✅ 目标文件 action_draw.cc -> 第340行:     /* check if visible */
  ✅ 找到匹配行: 340
  📝 更新行号: 339 -> 340

处理断点 53/208
  文件: source/blender/editors/animation/anim_filter.cc
  源行号: 3709
  时间戳: 76
    📍 源文件 anim_filter.cc -> 第3709行:      */
    🔍 找到46个精确匹配: [7, 26, 221, 422, 497, 545, 586, 595, 613, 959, 1107, 1130, 1156, 1171, 1214, 1224, 1296, 1351, 1438, 1461, 1482, 1493, 1688, 1745, 1760, 1768, 1836, 1907, 2219, 2625, 2654, 2792, 2829, 2844, 3524, 3541, 3558, 3587, 3660, 3702, 3729, 3746, 3758, 3771, 3781, 3872], 选择最接近的: 3702
    ✅ 目标文件 anim_filter.cc -> 第3702行:    */
  ✅ 找到匹配行: 3702
  📝 更新行号: 3709 -> 3702

处理断点 54/208
  文件: source/blender/editors/animation/anim_filter.cc
  源行号: 3714
  时间戳: 78
    📍 源文件 anim_filter.cc -> 第3714行:         /* since we're still here, this object should be usable */
    ✅ 目标文件 anim_filter.cc -> 第3734行:         /* since we're still here, this object should be usable */
  ✅ 找到匹配行: 3734
  📝 更新行号: 3714 -> 3734

处理断点 55/208
  文件: source/blender/editors/physics/physics_pointcache.cc
  源行号: 177
  时间戳: 79
    📍 源文件 physics_pointcache.cc -> 第176行:       MEM_callocN(sizeof(PTCacheBaker), "PTCacheBaker")); | 第177行:  [目标行] | 第178行:   baker->bmain = CTX_data_main(C);
  ❌ 未找到匹配的行

处理断点 56/208
  文件: source/blender/editors/physics/physics_pointcache.cc
  源行号: 202
  时间戳: 80
    📍 源文件 physics_pointcache.cc -> 第201行:   bool all = STREQ(op->type->idname, "PTCACHE_OT_bake_all"); | 第202行:  [目标行] | 第203行:   PTCacheBaker *baker = ptcache_baker_create(C, op, all);
  ❌ 未找到匹配的行

处理断点 57/208
  文件: source/blender/editors/physics/physics_pointcache.cc
  源行号: 213
  时间戳: 81
    📍 源文件 physics_pointcache.cc -> 第212行:   bool all = STREQ(op->type->idname, "PTCACHE_OT_bake_all"); | 第213行:  [目标行] | 第214行:   PointCacheJob *job = static_cast<PointCacheJob *>(
  ❌ 未找到匹配的行

处理断点 58/208
  文件: source/blender/editors/physics/physics_pointcache.cc
  源行号: 248
  时间戳: 82
    📍 源文件 physics_pointcache.cc -> 第248行:   /* no running blender, remove handler and pass through */
    ✅ 目标文件 physics_pointcache.cc -> 第249行:   /* no running blender, remove handler and pass through */
  ✅ 找到匹配行: 249
  📝 更新行号: 248 -> 249

处理断点 59/208
  文件: source/blender/editors/physics/physics_pointcache.cc
  源行号: 335
  时间戳: 83
    📍 源文件 physics_pointcache.cc -> 第334行: static int ptcache_bake_from_cache_exec(bContext *C, wmOperator * /*op*/) | 第335行: { [目标行] | 第336行:   PointerRNA ptr = CTX_data_pointer_get_type(C, "point_cache", &RNA_PointCache);
    🔍 找到26个精确匹配: [38, 43, 68, 97, 104, 119, 131, 152, 163, 177, 202, 213, 246, 258, 267, 290, 309, 324, 336, 348, 367, 381, 396, 416, 436, 450], 选择最接近的: 336
    ✅ 目标文件 physics_pointcache.cc -> 第335行: static wmOperatorStatus ptcache_bake_from_cache_exec(bContext *C, wmOperator * /*op*/) | 第336行: { [目标行] | 第337行:   PointerRNA ptr = CTX_data_pointer_get_type(C, "point_cache", &RNA_PointCache);
  ✅ 找到匹配行: 336
  📝 更新行号: 335 -> 336

处理断点 60/208
  文件: source/blender/editors/physics/rigidbody_object.cc
  源行号: 296
  时间戳: 84
    📍 源文件 rigidbody_object.cc -> 第296行:   /* apply this to all selected objects... */
    🔍 找到2个精确匹配: [252, 297], 选择最接近的: 297
    ✅ 目标文件 rigidbody_object.cc -> 第297行:   /* apply this to all selected objects... */
  ✅ 找到匹配行: 297
  📝 更新行号: 296 -> 297

处理断点 61/208
  文件: source/blender/editors/physics/rigidbody_object.cc
  源行号: 509
  时间戳: 85
    📍 源文件 rigidbody_object.cc -> 第508行:                                          const PropertyRNA *prop) | 第509行: { [目标行] | 第510行:   const char *prop_id = RNA_property_identifier(prop);
    🔍 找到21个精确匹配: [46, 51, 58, 73, 90, 95, 105, 127, 152, 178, 198, 222, 247, 273, 293, 324, 420, 437, 453, 512, 530], 选择最接近的: 512
    ✅ 目标文件 rigidbody_object.cc -> 第511行:                                          const PropertyRNA *prop) | 第512行: { [目标行] | 第513行:   const char *prop_id = RNA_property_identifier(prop);
  ✅ 找到匹配行: 512
  📝 更新行号: 509 -> 512

处理断点 62/208
  文件: source/blender/editors/physics/rigidbody_object.cc
  源行号: 424
  时间戳: 86
    📍 源文件 rigidbody_object.cc -> 第424行:   /* add each preset to the list */
    ✅ 目标文件 rigidbody_object.cc -> 第426行:   /* add each preset to the list */
  ✅ 找到匹配行: 426
  📝 更新行号: 424 -> 426

处理断点 63/208
  文件: source/blender/editors/physics/rigidbody_object.cc
  源行号: 247
  时间戳: 87
    📍 源文件 rigidbody_object.cc -> 第247行:   Main *bmain = CTX_data_main(C);
    🔍 找到4个精确匹配: [106, 153, 199, 248], 选择最接近的: 248
    ✅ 目标文件 rigidbody_object.cc -> 第248行:   Main *bmain = CTX_data_main(C);
  ✅ 找到匹配行: 248
  📝 更新行号: 247 -> 248

处理断点 64/208
  文件: source/blender/editors/physics/rigidbody_object.cc
  源行号: 198
  时间戳: 88
    📍 源文件 rigidbody_object.cc -> 第198行:   Main *bmain = CTX_data_main(C);
    🔍 找到4个精确匹配: [106, 153, 199, 248], 选择最接近的: 199
    ✅ 目标文件 rigidbody_object.cc -> 第199行:   Main *bmain = CTX_data_main(C);
  ✅ 找到匹配行: 199
  📝 更新行号: 198 -> 199

处理断点 65/208
  文件: source/blender/editors/physics/rigidbody_object.cc
  源行号: 152
  时间戳: 90
    📍 源文件 rigidbody_object.cc -> 第152行:   Main *bmain = CTX_data_main(C);
    🔍 找到4个精确匹配: [106, 153, 199, 248], 选择最接近的: 153
    ✅ 目标文件 rigidbody_object.cc -> 第153行:   Main *bmain = CTX_data_main(C);
  ✅ 找到匹配行: 153
  📝 更新行号: 152 -> 153

处理断点 66/208
  文件: source/blender/editors/physics/rigidbody_object.cc
  源行号: 62
  时间戳: 91
    📍 源文件 rigidbody_object.cc -> 第60行:     return false; | 第62行:  [目标行] | 第63行:   if (ED_operator_object_active_editable(C)) {
  ❌ 未找到匹配的行

处理断点 67/208
  文件: source/blender/editors/physics/rigidbody_object.cc
  源行号: 45
  时间戳: 92
    📍 源文件 rigidbody_object.cc -> 第44行: static bool operator_rigidbody_editable_poll(Scene *scene) | 第45行: { [目标行] | 第46行:   if (scene == nullptr || !ID_IS_EDITABLE(scene) || ID_IS_OVERRIDE_LIBRARY(scene) ||
    🔍 找到21个精确匹配: [46, 51, 58, 73, 90, 95, 105, 127, 152, 178, 198, 222, 247, 273, 293, 324, 420, 437, 453, 512, 530], 选择最接近的: 46
    ✅ 目标文件 rigidbody_object.cc -> 第45行: static bool operator_rigidbody_editable_poll(Scene *scene) | 第46行: { [目标行] | 第47行:   if (scene == nullptr || !ID_IS_EDITABLE(scene) || ID_IS_OVERRIDE_LIBRARY(scene) ||
  ✅ 找到匹配行: 46
  📝 更新行号: 45 -> 46

处理断点 68/208
  文件: source/blender/editors/physics/rigidbody_world.cc
  源行号: 158
  时间戳: 93
    📍 源文件 rigidbody_world.cc -> 第157行: static int rigidbody_world_export_invoke(bContext *C, wmOperator *op, const wmEvent * /*event*/) | 第158行: { [目标行] | 第159行:   if (!RNA_struct_property_is_set(op->ptr, "relative_path")) {
    🔍 找到9个精确匹配: [41, 46, 57, 74, 91, 113, 133, 160, 177], 选择最接近的: 160
    ✅ 目标文件 rigidbody_world.cc -> 第159行:                                                       const wmEvent * /*event*/) | 第160行: { [目标行] | 第161行:   if (!RNA_struct_property_is_set(op->ptr, "relative_path")) {
  ✅ 找到匹配行: 160
  📝 更新行号: 158 -> 160

处理断点 69/208
  文件: source/blender/editors/physics/rigidbody_constraint.cc
  源行号: 45
  时间戳: 95
    📍 源文件 rigidbody_constraint.cc -> 第44行: static bool operator_rigidbody_constraints_editable_poll(Scene *scene) | 第45行: { [目标行] | 第46行:   if (scene == nullptr || !ID_IS_EDITABLE(scene) || ID_IS_OVERRIDE_LIBRARY(scene) ||
    🔍 找到10个精确匹配: [46, 51, 58, 72, 82, 110, 123, 152, 177, 199], 选择最接近的: 46
    ✅ 目标文件 rigidbody_constraint.cc -> 第45行: static bool operator_rigidbody_constraints_editable_poll(Scene *scene) | 第46行: { [目标行] | 第47行:   if (scene == nullptr || !ID_IS_EDITABLE(scene) || ID_IS_OVERRIDE_LIBRARY(scene) ||
  ✅ 找到匹配行: 46
  📝 更新行号: 45 -> 46

处理断点 70/208
  文件: source/blender/editors/physics/rigidbody_constraint.cc
  源行号: 57
  时间戳: 96
    📍 源文件 rigidbody_constraint.cc -> 第56行: static bool ED_operator_rigidbody_con_active_poll(bContext *C) | 第57行: { [目标行] | 第58行:   Scene *scene = CTX_data_scene(C);
    🔍 找到10个精确匹配: [46, 51, 58, 72, 82, 110, 123, 152, 177, 199], 选择最接近的: 58
    ✅ 目标文件 rigidbody_constraint.cc -> 第57行: static bool operator_rigidbody_con_active_poll(bContext *C) | 第58行: { [目标行] | 第59行:   Scene *scene = CTX_data_scene(C);
  ✅ 找到匹配行: 58
  📝 更新行号: 57 -> 58

处理断点 71/208
  文件: source/blender/editors/physics/rigidbody_constraint.cc
  源行号: 71
  时间戳: 97
    📍 源文件 rigidbody_constraint.cc -> 第70行: static bool ED_operator_rigidbody_con_add_poll(bContext *C) | 第71行: { [目标行] | 第72行:   Scene *scene = CTX_data_scene(C);
    🔍 找到10个精确匹配: [46, 51, 58, 72, 82, 110, 123, 152, 177, 199], 选择最接近的: 72
    ✅ 目标文件 rigidbody_constraint.cc -> 第71行: static bool operator_rigidbody_con_add_poll(bContext *C) | 第72行: { [目标行] | 第73行:   Scene *scene = CTX_data_scene(C);
  ✅ 找到匹配行: 72
  📝 更新行号: 71 -> 72

处理断点 72/208
  文件: source/blender/editors/physics/rigidbody_constraint.cc
  源行号: 81
  时间戳: 98
    📍 源文件 rigidbody_constraint.cc -> 第80行:     Main *bmain, Scene *scene, Object *ob, int type, ReportList *reports) | 第81行: { [目标行] | 第82行:   RigidBodyWorld *rbw = BKE_rigidbody_get_world(scene);
    🔍 找到10个精确匹配: [46, 51, 58, 72, 82, 110, 123, 152, 177, 199], 选择最接近的: 82
    ✅ 目标文件 rigidbody_constraint.cc -> 第81行:     Main *bmain, Scene *scene, Object *ob, int type, ReportList *reports) | 第82行: { [目标行] | 第83行:   RigidBodyWorld *rbw = BKE_rigidbody_get_world(scene);
  ✅ 找到匹配行: 82
  📝 更新行号: 81 -> 82

处理断点 73/208
  文件: source/blender/editors/physics/rigidbody_constraint.cc
  源行号: 109
  时间戳: 99
    📍 源文件 rigidbody_constraint.cc -> 第108行: void ED_rigidbody_constraint_remove(Main *bmain, Scene *scene, Object *ob) | 第109行: { [目标行] | 第110行:   BKE_rigidbody_remove_constraint(bmain, scene, ob, false);
    🔍 找到10个精确匹配: [46, 51, 58, 72, 82, 110, 123, 152, 177, 199], 选择最接近的: 110
    ✅ 目标文件 rigidbody_constraint.cc -> 第109行: void ED_rigidbody_constraint_remove(Main *bmain, Scene *scene, Object *ob) | 第110行: { [目标行] | 第111行:   BKE_rigidbody_remove_constraint(bmain, scene, ob, false);
  ✅ 找到匹配行: 110
  📝 更新行号: 109 -> 110

处理断点 74/208
  文件: source/blender/editors/physics/rigidbody_constraint.cc
  源行号: 123
  时间戳: 100
    📍 源文件 rigidbody_constraint.cc -> 第123行:   Main *bmain = CTX_data_main(C);
    🔍 找到2个精确匹配: [124, 178], 选择最接近的: 124
    ✅ 目标文件 rigidbody_constraint.cc -> 第124行:   Main *bmain = CTX_data_main(C);
  ✅ 找到匹配行: 124
  📝 更新行号: 123 -> 124

处理断点 75/208
  文件: source/blender/editors/physics/rigidbody_constraint.cc
  源行号: 177
  时间戳: 101
    📍 源文件 rigidbody_constraint.cc -> 第177行:   Main *bmain = CTX_data_main(C);
    🔍 找到2个精确匹配: [124, 178], 选择最接近的: 178
    ✅ 目标文件 rigidbody_constraint.cc -> 第178行:   Main *bmain = CTX_data_main(C);
  ✅ 找到匹配行: 178
  📝 更新行号: 177 -> 178

处理断点 76/208
  文件: source/blender/editors/physics/particle_object.cc
  源行号: 70
  时间戳: 102
    📍 源文件 particle_object.cc -> 第70行:   Main *bmain = CTX_data_main(C);
    🔍 找到6个精确匹配: [71, 104, 161, 217, 264, 1087], 选择最接近的: 71
    ✅ 目标文件 particle_object.cc -> 第71行:   Main *bmain = CTX_data_main(C);
  ✅ 找到匹配行: 71
  📝 更新行号: 70 -> 71

处理断点 77/208
  文件: source/blender/editors/physics/particle_object.cc
  源行号: 104
  时间戳: 103
    📍 源文件 particle_object.cc -> 第104行:   Object *ob = blender::ed::object::context_object(C);
    🔍 找到4个精确匹配: [72, 105, 644, 940], 选择最接近的: 105
    ✅ 目标文件 particle_object.cc -> 第105行:   Object *ob = blender::ed::object::context_object(C);
  ✅ 找到匹配行: 105
  📝 更新行号: 104 -> 105

处理断点 78/208
  文件: source/blender/editors/physics/particle_object.cc
  源行号: 161
  时间戳: 104
    📍 源文件 particle_object.cc -> 第161行:   ParticleSystem *psys;
    ✅ 目标文件 particle_object.cc -> 第162行:   ParticleSystem *psys;
  ✅ 找到匹配行: 162
  📝 更新行号: 161 -> 162

处理断点 79/208
  文件: source/blender/editors/physics/particle_object.cc
  源行号: 216
  时间戳: 105
    📍 源文件 particle_object.cc -> 第216行:   Main *bmain = CTX_data_main(C);
    🔍 找到6个精确匹配: [71, 104, 161, 217, 264, 1087], 选择最接近的: 217
    ✅ 目标文件 particle_object.cc -> 第217行:   Main *bmain = CTX_data_main(C);
  ✅ 找到匹配行: 217
  📝 更新行号: 216 -> 217

处理断点 80/208
  文件: source/blender/editors/physics/particle_object.cc
  源行号: 263
  时间戳: 106
    📍 源文件 particle_object.cc -> 第263行:   Main *bmain = CTX_data_main(C);
    🔍 找到6个精确匹配: [71, 104, 161, 217, 264, 1087], 选择最接近的: 264
    ✅ 目标文件 particle_object.cc -> 第264行:   Main *bmain = CTX_data_main(C);
  ✅ 找到匹配行: 264
  📝 更新行号: 263 -> 264

处理断点 81/208
  文件: source/blender/editors/physics/particle_object.cc
  源行号: 314
  时间戳: 107
    📍 源文件 particle_object.cc -> 第314行:   PointerRNA ptr = CTX_data_pointer_get_type(C, "particle_system", &RNA_ParticleSystem);
    🔍 找到10个精确匹配: [155, 218, 265, 315, 355, 394, 424, 463, 503, 549], 选择最接近的: 315
    ✅ 目标文件 particle_object.cc -> 第315行:   PointerRNA ptr = CTX_data_pointer_get_type(C, "particle_system", &RNA_ParticleSystem);
  ✅ 找到匹配行: 315
  📝 更新行号: 314 -> 315

处理断点 82/208
  文件: source/blender/editors/physics/particle_object.cc
  源行号: 354
  时间戳: 108
    📍 源文件 particle_object.cc -> 第354行:   PointerRNA ptr = CTX_data_pointer_get_type(C, "particle_system", &RNA_ParticleSystem);
    🔍 找到10个精确匹配: [155, 218, 265, 315, 355, 394, 424, 463, 503, 549], 选择最接近的: 355
    ✅ 目标文件 particle_object.cc -> 第355行:   PointerRNA ptr = CTX_data_pointer_get_type(C, "particle_system", &RNA_ParticleSystem);
  ✅ 找到匹配行: 355
  📝 更新行号: 354 -> 355

处理断点 83/208
  文件: source/blender/editors/physics/particle_object.cc
  源行号: 423
  时间戳: 109
    📍 源文件 particle_object.cc -> 第423行:   PointerRNA ptr = CTX_data_pointer_get_type(C, "particle_system", &RNA_ParticleSystem);
    🔍 找到10个精确匹配: [155, 218, 265, 315, 355, 394, 424, 463, 503, 549], 选择最接近的: 424
    ✅ 目标文件 particle_object.cc -> 第424行:   PointerRNA ptr = CTX_data_pointer_get_type(C, "particle_system", &RNA_ParticleSystem);
  ✅ 找到匹配行: 424
  📝 更新行号: 423 -> 424

处理断点 84/208
  文件: source/blender/editors/physics/particle_object.cc
  源行号: 462
  时间戳: 110
    📍 源文件 particle_object.cc -> 第462行:   PointerRNA ptr = CTX_data_pointer_get_type(C, "particle_system", &RNA_ParticleSystem);
    🔍 找到10个精确匹配: [155, 218, 265, 315, 355, 394, 424, 463, 503, 549], 选择最接近的: 463
    ✅ 目标文件 particle_object.cc -> 第463行:   PointerRNA ptr = CTX_data_pointer_get_type(C, "particle_system", &RNA_ParticleSystem);
  ✅ 找到匹配行: 463
  📝 更新行号: 462 -> 463

处理断点 85/208
  文件: source/blender/editors/physics/particle_object.cc
  源行号: 502
  时间戳: 111
    📍 源文件 particle_object.cc -> 第502行:   PointerRNA ptr = CTX_data_pointer_get_type(C, "particle_system", &RNA_ParticleSystem);
    🔍 找到10个精确匹配: [155, 218, 265, 315, 355, 394, 424, 463, 503, 549], 选择最接近的: 503
    ✅ 目标文件 particle_object.cc -> 第503行:   PointerRNA ptr = CTX_data_pointer_get_type(C, "particle_system", &RNA_ParticleSystem);
  ✅ 找到匹配行: 503
  📝 更新行号: 502 -> 503

处理断点 86/208
  文件: source/blender/editors/physics/particle_object.cc
  源行号: 548
  时间戳: 112
    📍 源文件 particle_object.cc -> 第548行:   PointerRNA ptr = CTX_data_pointer_get_type(C, "particle_system", &RNA_ParticleSystem);
    🔍 找到10个精确匹配: [155, 218, 265, 315, 355, 394, 424, 463, 503, 549], 选择最接近的: 549
    ✅ 目标文件 particle_object.cc -> 第549行:   PointerRNA ptr = CTX_data_pointer_get_type(C, "particle_system", &RNA_ParticleSystem);
  ✅ 找到匹配行: 549
  📝 更新行号: 548 -> 549

处理断点 87/208
  文件: source/blender/editors/physics/particle_object.cc
  源行号: 587
  时间戳: 113
    📍 源文件 particle_object.cc -> 第587行:   Object *object_eval = DEG_get_evaluated_object(depsgraph, ob);
    🔍 找到1个上下文匹配, 选择最佳: 行588 (匹配度: 0.91)
    ✅ 目标文件 particle_object.cc -> 第588行:   Object *object_eval = DEG_get_evaluated(depsgraph, ob);
  ✅ 找到匹配行: 588
  📝 更新行号: 587 -> 588

处理断点 88/208
  文件: source/blender/editors/physics/particle_object.cc
  源行号: 641
  时间戳: 114
    📍 源文件 particle_object.cc -> 第641行:   Depsgraph *depsgraph = CTX_data_ensure_evaluated_depsgraph(C);
    🔍 找到3个精确匹配: [642, 938, 1088], 选择最接近的: 642
    ✅ 目标文件 particle_object.cc -> 第642行:   Depsgraph *depsgraph = CTX_data_ensure_evaluated_depsgraph(C);
  ✅ 找到匹配行: 642
  📝 更新行号: 641 -> 642

处理断点 89/208
  文件: source/blender/editors/physics/particle_object.cc
  源行号: 698
  时间戳: 115
    📍 源文件 particle_object.cc -> 第698行:   Object *object_eval = DEG_get_evaluated_object(depsgraph, ob);
    🔍 找到1个上下文匹配, 选择最佳: 行699 (匹配度: 0.91)
    ✅ 目标文件 particle_object.cc -> 第699行:   Object *object_eval = DEG_get_evaluated(depsgraph, ob);
  ✅ 找到匹配行: 699
  📝 更新行号: 698 -> 699

处理断点 90/208
  文件: source/blender/editors/physics/particle_object.cc
  源行号: 724
  时间戳: 116
    📍 源文件 particle_object.cc -> 第723行:   edit_point = target_edit ? target_edit->points : nullptr; | 第724行:  [目标行] | 第725行:   invert_m4_m4(from_ob_imat, ob->object_to_world().ptr());
  ❌ 未找到匹配的行

处理断点 91/208
  文件: source/blender/editors/physics/particle_object.cc
  源行号: 917
  时间戳: 117
    📍 源文件 particle_object.cc -> 第915行:     return false; | 第917行:  [目标行] | 第918行:   ok = remap_hair_emitter(depsgraph,
  ❌ 未找到匹配的行

处理断点 92/208
  文件: source/blender/editors/physics/particle_object.cc
  源行号: 938
  时间戳: 118
    📍 源文件 particle_object.cc -> 第938行:   Depsgraph *depsgraph = CTX_data_ensure_evaluated_depsgraph(C);
    🔍 找到3个精确匹配: [642, 938, 1088], 选择最接近的: 938
    ✅ 目标文件 particle_object.cc -> 第938行:   Depsgraph *depsgraph = CTX_data_ensure_evaluated_depsgraph(C);
  ✅ 找到匹配行: 938
  ⏭️  行号未变化，跳过

处理断点 93/208
  文件: source/blender/editors/physics/particle_object.cc
  源行号: 1000
  时间戳: 119
    📍 源文件 particle_object.cc -> 第999行:                                ParticleSystem *psys_from) | 第1000行: { [目标行] | 第1001行:   PTCacheEdit *edit_from = psys_from->edit, *edit;
    🔍 找到47个精确匹配: [70, 88, 103, 137, 154, 160, 199, 216, 249, 263, 298, 314, 340, 354, 379, 393, 409, 423, 448, 462, 488, 502, 532, 548, 573, 587, 641, 669, 698, 776, 839, 861, 911, 937, 973, 1000, 1049, 1069, 1086, 1137, 1174, 1220, 1235, 1292, 1328, 1344, 1361], 选择最接近的: 1000
    ✅ 目标文件 particle_object.cc -> 第999行:                                ParticleSystem *psys_from) | 第1000行: { [目标行] | 第1001行:   PTCacheEdit *edit_from = psys_from->edit, *edit;
  ✅ 找到匹配行: 1000
  ⏭️  行号未变化，跳过

处理断点 94/208
  文件: source/blender/editors/physics/particle_object.cc
  源行号: 1086
  时间戳: 120
    📍 源文件 particle_object.cc -> 第1086行:   Main *bmain = CTX_data_main(C);
    🔍 找到6个精确匹配: [71, 104, 161, 217, 264, 1087], 选择最接近的: 1087
    ✅ 目标文件 particle_object.cc -> 第1087行:   Main *bmain = CTX_data_main(C);
  ✅ 找到匹配行: 1087
  📝 更新行号: 1086 -> 1087

处理断点 95/208
  文件: source/blender/editors/physics/particle_object.cc
  源行号: 1236
  时间戳: 121
    📍 源文件 particle_object.cc -> 第1236行:   const int space = RNA_enum_get(op->ptr, "space");
    ✅ 目标文件 particle_object.cc -> 第1236行:   const int space = RNA_enum_get(op->ptr, "space");
  ✅ 找到匹配行: 1236
  ⏭️  行号未变化，跳过

处理断点 96/208
  文件: source/blender/editors/physics/particle_object.cc
  源行号: 1342
  时间戳: 122
    📍 源文件 particle_object.cc -> 第1342行:   Scene *scene = CTX_data_scene(C);
    🔍 找到6个精确匹配: [73, 106, 643, 939, 1239, 1346], 选择最接近的: 1346
    ✅ 目标文件 particle_object.cc -> 第1346行:   Scene *scene = CTX_data_scene(C);
  ✅ 找到匹配行: 1346
  📝 更新行号: 1342 -> 1346

处理断点 97/208
  文件: source/blender/draw/engines/eevee_next/eevee_velocity.cc
  源行号: 420
  时间戳: 124
  ❌ 目标文件不存在: source\blender\draw\engines\eevee_next\eevee_velocity.cc

处理断点 98/208
  文件: source/blender/draw/engines/overlay/overlay_extra.cc
  源行号: 433
  时间戳: 125
  ❌ 目标文件不存在: source\blender\draw\engines\overlay\overlay_extra.cc

处理断点 99/208
  文件: source/blender/draw/engines/overlay/overlay_extra.cc
  源行号: 453
  时间戳: 127
  ❌ 目标文件不存在: source\blender\draw\engines\overlay\overlay_extra.cc

处理断点 100/208
  文件: source/blender/draw/engines/overlay/overlay_next_relation.hh
  源行号: 85
  时间戳: 130
  ❌ 目标文件不存在: source\blender\draw\engines\overlay\overlay_next_relation.hh

处理断点 101/208
  文件: source/blender/draw/engines/overlay/overlay_extra.cc
  源行号: 1350
  时间戳: 131
  ❌ 目标文件不存在: source\blender\draw\engines\overlay\overlay_extra.cc

处理断点 102/208
  文件: source/blender/draw/engines/overlay/overlay_extra.cc
  源行号: 1673
  时间戳: 132
  ❌ 目标文件不存在: source\blender\draw\engines\overlay\overlay_extra.cc

处理断点 103/208
  文件: source/blender/draw/engines/eevee_next/eevee_velocity.cc
  源行号: 415
  时间戳: 134
  ❌ 目标文件不存在: source\blender\draw\engines\eevee_next\eevee_velocity.cc

处理断点 104/208
  文件: source/blender/blenkernel/intern/pointcache.cc
  源行号: 3163
  时间戳: 135
    📍 源文件 pointcache.cc -> 第3163行:   Scene *scene = baker->scene;
    ✅ 目标文件 pointcache.cc -> 第3164行:   Scene *scene = baker->scene;
  ✅ 找到匹配行: 3164
  📝 更新行号: 3163 -> 3164

处理断点 105/208
  文件: source/blender/windowmanager/intern/wm_jobs.cc
  源行号: 487
  时间戳: 136
    📍 源文件 wm_jobs.cc -> 第486行:         // printf("job started: %s\n", wm_job->name); | 第487行:  [目标行] | 第488行:         BLI_threadpool_init(&wm_job->threads, do_job_thread, 1);
  ❌ 未找到匹配的行

处理断点 106/208
  文件: source/blender/blenkernel/intern/pointcache.cc
  源行号: 3283
  时间戳: 137
    📍 源文件 pointcache.cc -> 第3283行:  [目标行] | 第3284行:   scene->r.cfra = startframe;
  ❌ 未找到匹配的行

处理断点 107/208
  文件: source/blender/blenkernel/intern/pointcache.cc
  源行号: 3318
  时间戳: 138
    📍 源文件 pointcache.cc -> 第3317行:         ptcache_dt_to_str(etd, sizeof(etd), fetd); | 第3318行:  [目标行] | 第3319行:         printf("Baked for %s, current frame: %i/%i (%.3fs), ETC: %s\r",
  ❌ 未找到匹配的行

处理断点 108/208
  文件: source/blender/blenkernel/intern/pointcache.cc
  源行号: 3296
  时间戳: 139
    📍 源文件 pointcache.cc -> 第3296行:   for (int fr = scene->r.cfra; fr <= endframe; fr += baker->quick_step, scene->r.cfra = fr) {
    ✅ 目标文件 pointcache.cc -> 第3297行:   for (int fr = scene->r.cfra; fr <= endframe; fr += baker->quick_step, scene->r.cfra = fr) {
  ✅ 找到匹配行: 3297
  📝 更新行号: 3296 -> 3297

处理断点 109/208
  文件: source/blender/blenkernel/intern/pointcache.cc
  源行号: 3337
  时间戳: 140
    📍 源文件 pointcache.cc -> 第3335行:     scene->r.cfra += 1; | 第3337行:  [目标行] | 第3338行:   if (use_timer) {
  ❌ 未找到匹配的行

处理断点 110/208
  文件: source/blender/blenkernel/intern/scene.cc
  源行号: 2582
  时间戳: 141
    📍 源文件 scene.cc -> 第2582行:      */
    🔍 找到8个精确匹配: [7, 472, 2266, 2494, 2552, 2646, 3284, 3409], 选择最接近的: 2552
    ✅ 目标文件 scene.cc -> 第2552行:  */
  ✅ 找到匹配行: 2552
  📝 更新行号: 2582 -> 2552

处理断点 111/208
  文件: source/blender/blenkernel/intern/scene.cc
  源行号: 2592
  时间戳: 143
    📍 源文件 scene.cc -> 第2592行:       const float frame = BKE_scene_frame_get(scene);
    ✅ 目标文件 scene.cc -> 第2656行:       const float frame = BKE_scene_frame_get(scene);
  ✅ 找到匹配行: 2656
  📝 更新行号: 2592 -> 2656

处理断点 112/208
  文件: source/blender/blenkernel/intern/scene.cc
  源行号: 2595
  时间戳: 144
    📍 源文件 scene.cc -> 第2595行:     else {
    🔍 找到29个精确匹配: [366, 977, 1389, 1397, 1406, 1414, 1428, 1434, 1442, 1450, 1495, 1984, 2096, 2111, 2127, 2133, 2155, 2659, 2799, 2921, 2979, 3147, 3227, 3570, 3587, 3606, 3620, 3639, 3653], 选择最接近的: 2659
    ✅ 目标文件 scene.cc -> 第2659行:     else {
  ✅ 找到匹配行: 2659
  📝 更新行号: 2595 -> 2659

处理断点 113/208
  文件: source/blender/blenkernel/intern/scene.cc
  源行号: 2606
  时间戳: 145
    📍 源文件 scene.cc -> 第2606行:        * DEG_editors_update() doesn't access freed memory of possibly removed ID. */
    ✅ 目标文件 scene.cc -> 第2670行:        * DEG_editors_update() doesn't access freed memory of possibly removed ID. */
  ✅ 找到匹配行: 2670
  📝 更新行号: 2606 -> 2670

处理断点 114/208
  文件: source/blender/depsgraph/intern/depsgraph_eval.cc
  源行号: 101
  时间戳: 146
    📍 源文件 depsgraph_eval.cc -> 第101行:   deg_graph->ctime = BKE_scene_frame_to_ctime(scene, frame);
    ✅ 目标文件 depsgraph_eval.cc -> 第86行:   deg_graph->ctime = BKE_scene_frame_to_ctime(scene, frame);
  ✅ 找到匹配行: 86
  📝 更新行号: 101 -> 86

处理断点 115/208
  文件: source/blender/depsgraph/intern/eval/deg_eval_flush.cc
  源行号: 344
  时间戳: 149
    📍 源文件 deg_eval_flush.cc -> 第344行:   update_ctx.scene = graph->scene;
    ✅ 目标文件 deg_eval_flush.cc -> 第326行:   update_ctx.scene = graph->scene;
  ✅ 找到匹配行: 326
  📝 更新行号: 344 -> 326

处理断点 116/208
  文件: source/blender/depsgraph/intern/eval/deg_eval_flush.cc
  源行号: 362
  时间戳: 151
    📍 源文件 deg_eval_flush.cc -> 第362行:   /* Inform editors about all changes. */
    ✅ 目标文件 deg_eval_flush.cc -> 第344行:   /* Inform editors about all changes. */
  ✅ 找到匹配行: 344
  📝 更新行号: 362 -> 344

处理断点 117/208
  文件: source/blender/windowmanager/intern/wm_operators.cc
  源行号: 3674
  时间戳: 152
    📍 源文件 wm_operators.cc -> 第3674行:     scene->r.cfra += (cfra == scene->r.cfra) ? 1 : -1;
    ✅ 目标文件 wm_operators.cc -> 第3669行:     scene->r.cfra += (cfra == scene->r.cfra) ? 1 : -1;
  ✅ 找到匹配行: 3669
  📝 更新行号: 3674 -> 3669

处理断点 118/208
  文件: source/blender/draw/intern/draw_manager_c.cc
  源行号: 1496
  时间戳: 155
  ❌ 目标文件不存在: source\blender\draw\intern\draw_manager_c.cc

处理断点 119/208
  文件: source/blender/draw/intern/draw_manager_c.cc
  源行号: 2779
  时间戳: 156
  ❌ 目标文件不存在: source\blender\draw\intern\draw_manager_c.cc

处理断点 120/208
  文件: source/blender/draw/intern/draw_manager_c.cc
  源行号: 1733
  时间戳: 157
  ❌ 目标文件不存在: source\blender\draw\intern\draw_manager_c.cc

处理断点 121/208
  文件: source/blender/draw/intern/draw_manager_c.cc
  源行号: 1803
  时间戳: 158
  ❌ 目标文件不存在: source\blender\draw\intern\draw_manager_c.cc

处理断点 122/208
  文件: source/blender/draw/intern/draw_manager_c.cc
  源行号: 1515
  时间戳: 161
  ❌ 目标文件不存在: source\blender\draw\intern\draw_manager_c.cc

处理断点 123/208
  文件: source/blender/editors/screen/screen_edit.cc
  源行号: 1881
  时间戳: 162
    📍 源文件 screen_edit.cc -> 第1881行:   /* this function applies the changes too */
    ✅ 目标文件 screen_edit.cc -> 第1941行:   /* this function applies the changes too */
  ✅ 找到匹配行: 1941
  📝 更新行号: 1881 -> 1941

处理断点 124/208
  文件: source/blender/blenkernel/intern/scene.cc
  源行号: 2498
  时间戳: 163
    📍 源文件 scene.cc -> 第2497行:   bool used_multiple_passes = false; | 第2498行:  [目标行] | 第2499行:   bool run_callbacks = DEG_id_type_any_updated(depsgraph);
  ❌ 未找到匹配的行

处理断点 125/208
  文件: source/blender/blenkernel/intern/scene.cc
  源行号: 2505
  时间戳: 164
    📍 源文件 scene.cc -> 第2505行:     /* (Re-)build dependency graph if needed. */
    ✅ 目标文件 scene.cc -> 第2569行:     /* (Re-)build dependency graph if needed. */
  ✅ 找到匹配行: 2569
  📝 更新行号: 2505 -> 2569

处理断点 126/208
  文件: source/blender/depsgraph/intern/depsgraph_eval.cc
  源行号: 46
  时间戳: 167
    📍 源文件 depsgraph_eval.cc -> 第46行:   deg::deg_graph_flush_updates(deg_graph);
    ✅ 目标文件 depsgraph_eval.cc -> 第39行:   deg::deg_graph_flush_updates(deg_graph);
  ✅ 找到匹配行: 39
  📝 更新行号: 46 -> 39

处理断点 127/208
  文件: source/blender/depsgraph/intern/eval/deg_eval.cc
  源行号: 448
  时间戳: 168
    📍 源文件 deg_eval.cc -> 第448行: //    printf("entry_tags.is_empty------------------------");
  ❌ 未找到匹配的行

处理断点 128/208
  文件: source/blender/depsgraph/intern/eval/deg_eval.cc
  源行号: 247
  时间戳: 171
    📍 源文件 deg_eval.cc -> 第245行:     calculate_pending_parents_for_node(state, node); | 第247行:  [目标行] | 第248行:   state->need_update_pending_parents = false;
  ❌ 未找到匹配的行

处理断点 129/208
  文件: source/blender/depsgraph/intern/eval/deg_eval.cc
  源行号: 381
  时间戳: 172
    📍 源文件 deg_eval.cc -> 第380行:   calculate_pending_parents_if_needed(state); | 第381行:  [目标行] | 第382行:   schedule_graph(state, [&](OperationNode *node) {
  ❌ 未找到匹配的行

处理断点 130/208
  文件: source/blender/blenkernel/intern/object_update.cc
  源行号: 309
  时间戳: 173
    📍 源文件 object_update.cc -> 第309行:   BLI_assert(ob->type != OB_ARMATURE);
    ✅ 目标文件 object_update.cc -> 第305行:   BLI_assert(ob->type != OB_ARMATURE);
  ✅ 找到匹配行: 305
  📝 更新行号: 309 -> 305

处理断点 131/208
  文件: source/blender/blenkernel/intern/object_update.cc
  源行号: 161
  时间戳: 175
    📍 源文件 object_update.cc -> 第161行:     case OB_ARMATURE:
    ✅ 目标文件 object_update.cc -> 第160行:     case OB_ARMATURE:
  ✅ 找到匹配行: 160
  📝 更新行号: 161 -> 160

处理断点 132/208
  文件: source/blender/blenkernel/intern/object_update.cc
  源行号: 157
  时间戳: 176
    📍 源文件 object_update.cc -> 第156行:         cddata_masks.vmask |= CD_MASK_ORCO; | 第157行:       } [目标行] | 第158行:       blender::bke::mesh_data_update(*depsgraph, *scene, *ob, cddata_masks);
    🔍 找到49个精确匹配: [50, 58, 86, 89, 90, 110, 121, 124, 127, 156, 159, 174, 191, 207, 211, 217, 220, 221, 222, 227, 228, 234, 255, 256, 257, 281, 283, 299, 300, 310, 316, 324, 327, 330, 347, 348, 357, 360, 361, 391, 394, 395, 402, 412, 413, 421, 422, 428, 435], 选择最接近的: 156
    ✅ 目标文件 object_update.cc -> 第155行:         cddata_masks.vmask |= CD_MASK_ORCO; | 第156行:       } [目标行] | 第157行:       blender::bke::mesh_data_update(*depsgraph, *scene, *ob, cddata_masks);
  ✅ 找到匹配行: 156
  📝 更新行号: 157 -> 156

处理断点 133/208
  文件: source/blender/depsgraph/intern/eval/deg_eval.cc
  源行号: 521
  时间戳: 177
    📍 源文件 deg_eval.cc -> 第519行:     state.need_update_pending_parents = true; | 第521行:  [目标行] | 第522行:   evaluate_graph_threaded_stage(&state, task_pool, EvaluationStage::THREADED_EVALUATION);
  ❌ 未找到匹配的行

处理断点 134/208
  文件: source/blender/blenlib/intern/task_range.cc
  源行号: 107
  时间戳: 178
    📍 源文件 task_range.cc -> 第107行:   if (settings->use_threading && BLI_task_scheduler_num_threads() > 1) {
    ✅ 目标文件 task_range.cc -> 第107行:   if (settings->use_threading && BLI_task_scheduler_num_threads() > 1) {
  ✅ 找到匹配行: 107
  ⏭️  行号未变化，跳过

处理断点 135/208
  文件: source/blender/blenkernel/intern/rigidbody.cc
  源行号: 2520
  时间戳: 179
    📍 源文件 rigidbody.cc -> 第2520行:   /* rebuild sim data (i.e. after resetting to start of timeline) */
    ✅ 目标文件 rigidbody.cc -> 第2604行:   /* rebuild sim data (i.e. after resetting to start of timeline) */
  ✅ 找到匹配行: 2604
  📝 更新行号: 2520 -> 2604

处理断点 136/208
  文件: source/blender/blenkernel/intern/rigidbody.cc
  源行号: 2267
  时间戳: 180
    📍 源文件 rigidbody.cc -> 第2266行:   int startframe, endframe; | 第2267行:  [目标行] | 第2268行:   BKE_ptcache_id_from_rigidbody(&pid, nullptr, rbw);
  ❌ 未找到匹配的行

处理断点 137/208
  文件: source/blender/blenkernel/intern/collection.cc
  源行号: 874
  时间戳: 181
    📍 源文件 collection.cc -> 第874行:     if (!(collection->flag & COLLECTION_HAS_OBJECT_CACHE)) {
    🔍 找到2个精确匹配: [872, 876], 选择最接近的: 872
    ✅ 目标文件 collection.cc -> 第872行:   if (!(collection->flag & COLLECTION_HAS_OBJECT_CACHE)) {
  ✅ 找到匹配行: 872
  📝 更新行号: 874 -> 872

处理断点 138/208
  文件: source/blender/blenkernel/intern/anim_sys.cc
  源行号: 4210
  时间戳: 182
    📍 源文件 anim_sys.cc -> 第4209行:   std::cout << milliseconds.count() << " ms\n"; | 第4210行:  [目标行] | 第4211行:   BKE_animsys_evaluate_animdata(id, adt, &anim_eval_context, ADT_RECALC_ANIM, flush_to_original);
  ❌ 未找到匹配的行

处理断点 139/208
  文件: source/blender/blenkernel/intern/anim_sys.cc
  源行号: 903
  时间戳: 185
    📍 源文件 anim_sys.cc -> 第903行:     Vector<FCurve *> fcurves = animrig::legacy::fcurves_all(act);
    ✅ 目标文件 anim_sys.cc -> 第897行:     Vector<FCurve *> fcurves = animrig::legacy::fcurves_all(act);
  ✅ 找到匹配行: 897
  📝 更新行号: 903 -> 897

处理断点 140/208
  文件: source/blender/blenkernel/intern/armature_update.cc
  源行号: 833
  时间戳: 187
    📍 源文件 armature_update.cc -> 第833行:   /* world_to_object is needed for solvers. */
    ✅ 目标文件 armature_update.cc -> 第827行:   /* world_to_object is needed for solvers. */
  ✅ 找到匹配行: 827
  📝 更新行号: 833 -> 827

处理断点 141/208
  文件: source/blender/blenkernel/intern/armature_update.cc
  源行号: 908
  时间戳: 188
    📍 源文件 armature_update.cc -> 第908行:           float ctime = BKE_scene_ctime_get(scene); /* not accurate... */
    🔍 找到2个精确匹配: [890, 918], 选择最接近的: 918
    ✅ 目标文件 armature_update.cc -> 第918行:       float ctime = BKE_scene_ctime_get(scene); /* not accurate... */
  ✅ 找到匹配行: 918
  📝 更新行号: 908 -> 918

处理断点 142/208
  文件: source/blender/depsgraph/intern/builder/deg_builder_nodes_rig.cc
  源行号: 228
  时间戳: 189
    📍 源文件 deg_builder_nodes_rig.cc -> 第227行:     add_operation_node(&object->id, NodeType::BONE, pchan->name, OperationCode::BONE_READY); | 第228行:  [目标行] | 第229行:     op_node = add_operation_node(&object->id,
  ❌ 未找到匹配的行

处理断点 143/208
  文件: source/blender/depsgraph/intern/builder/deg_builder_nodes_rig.cc
  源行号: 222
  时间戳: 190
    📍 源文件 deg_builder_nodes_rig.cc -> 第222行:                        [scene_cow, object_cow, pchan_index](::Depsgraph *depsgraph) {
    🔍 找到2个精确匹配: [53, 212], 选择最接近的: 212
    ✅ 目标文件 deg_builder_nodes_rig.cc -> 第212行:                        [scene_cow, object_cow, pchan_index](::Depsgraph *depsgraph) {
  ✅ 找到匹配行: 212
  📝 更新行号: 222 -> 212

处理断点 144/208
  文件: source/blender/blenkernel/intern/armature_update.cc
  源行号: 1022
  时间戳: 191
    📍 源文件 armature_update.cc -> 第1021行:     return; | 第1022行:   } [目标行] | 第1023行:   BIK_execute_tree(depsgraph, scene, object, rootchan, ctime);
    🔍 找到124个精确匹配: [78, 82, 86, 87, 90, 103, 107, 118, 135, 141, 142, 146, 176, 180, 190, 191, 192, 215, 221, 242, 245, 255, 262, 271, 304, 323, 330, 333, 335, 348, 351, 362, 367, 381, 384, 393, 396, 400, 421, 443, 458, 462, 474, 478, 489, 497, 498, 508, 511, 512, 562, 589, 614, 631, 634, 640, 660, 661, 672, 673, 677, 681, 687, 688, 689, 697, 709, 717, 720, 721, 738, 752, 765, 766, 771, 775, 776, 781, 787, 801, 802, 812, 839, 840, 843, 853, 860, 867, 878, 879, 886, 892, 893, 894, 895, 896, 906, 912, 915, 920, 921, 922, 930, 934, 943, 950, 960, 961, 967, 968, 969, 976, 984, 985, 986, 996, 1004, 1006, 1017, 1025, 1027, 1035, 1044, 1057], 选择最接近的: 1025
    ✅ 目标文件 armature_update.cc -> 第1024行:     return; | 第1025行:   } [目标行] | 第1026行:   BKE_splineik_execute_tree(depsgraph, scene, object, rootchan, ctime);
  ✅ 找到匹配行: 1025
  📝 更新行号: 1022 -> 1025

处理断点 145/208
  文件: source/blender/blenkernel/intern/object_update.cc
  源行号: 77
  时间戳: 192
    📍 源文件 object_update.cc -> 第77行:   /* get parent effect matrix */
    ✅ 目标文件 object_update.cc -> 第76行:   /* get parent effect matrix */
  ✅ 找到匹配行: 76
  📝 更新行号: 77 -> 76

处理断点 146/208
  文件: source/blender/blenkernel/intern/object_update.cc
  源行号: 108
  时间戳: 193
    📍 源文件 object_update.cc -> 第108行:   cob = BKE_constraints_make_evalob(depsgraph, scene, ob, nullptr, CONSTRAINT_OBTYPE_OBJECT);
    ✅ 目标文件 object_update.cc -> 第107行:   cob = BKE_constraints_make_evalob(depsgraph, scene, ob, nullptr, CONSTRAINT_OBTYPE_OBJECT);
  ✅ 找到匹配行: 107
  📝 更新行号: 108 -> 107

处理断点 147/208
  文件: source/blender/blenkernel/intern/armature_update.cc
  源行号: 936
  时间戳: 194
    📍 源文件 armature_update.cc -> 第936行:       float ctime = BKE_scene_ctime_get(scene); /* not accurate... */
    🔍 找到2个精确匹配: [890, 918], 选择最接近的: 918
    ✅ 目标文件 armature_update.cc -> 第918行:       float ctime = BKE_scene_ctime_get(scene); /* not accurate... */
  ✅ 找到匹配行: 918
  📝 更新行号: 936 -> 918

处理断点 148/208
  文件: source/blender/blenkernel/intern/armature_update.cc
  源行号: 979
  时间戳: 195
    📍 源文件 armature_update.cc -> 第977行:       mat4_to_dquat(&pchan->runtime.deform_dual_quat, pchan->bone->arm_mat, pchan->chan_mat); | 第979行:   } [目标行] | 第980行:   pose_channel_flush_to_orig_if_needed(depsgraph, object, pchan);
    🔍 找到124个精确匹配: [78, 82, 86, 87, 90, 103, 107, 118, 135, 141, 142, 146, 176, 180, 190, 191, 192, 215, 221, 242, 245, 255, 262, 271, 304, 323, 330, 333, 335, 348, 351, 362, 367, 381, 384, 393, 396, 400, 421, 443, 458, 462, 474, 478, 489, 497, 498, 508, 511, 512, 562, 589, 614, 631, 634, 640, 660, 661, 672, 673, 677, 681, 687, 688, 689, 697, 709, 717, 720, 721, 738, 752, 765, 766, 771, 775, 776, 781, 787, 801, 802, 812, 839, 840, 843, 853, 860, 867, 878, 879, 886, 892, 893, 894, 895, 896, 906, 912, 915, 920, 921, 922, 930, 934, 943, 950, 960, 961, 967, 968, 969, 976, 984, 985, 986, 996, 1004, 1006, 1017, 1025, 1027, 1035, 1044, 1057], 选择最接近的: 976
    ✅ 目标文件 armature_update.cc -> 第975行:     return; | 第976行:   } [目标行] | 第977行:   bPoseChannel *pchan = pose_pchan_get_indexed(object, pchan_index);
  ✅ 找到匹配行: 976
  📝 更新行号: 979 -> 976

处理断点 149/208
  文件: source/blender/blenkernel/intern/rigidbody.cc
  源行号: 2543
  时间戳: 196
    📍 源文件 rigidbody.cc -> 第2543行:   /* read values pushed into RBO from sim/cache... */
    ✅ 目标文件 rigidbody.cc -> 第2627行:   /* read values pushed into RBO from sim/cache... */
  ✅ 找到匹配行: 2627
  📝 更新行号: 2543 -> 2627

处理断点 150/208
  文件: source/blender/blenkernel/intern/object_update.cc
  源行号: 57
  时间戳: 197
    📍 源文件 object_update.cc -> 第57行:   /* calculate local matrix */
    ✅ 目标文件 object_update.cc -> 第56行:   /* calculate local matrix */
  ✅ 找到匹配行: 56
  📝 更新行号: 57 -> 56

处理断点 151/208
  文件: source/blender/blenkernel/intern/object_update.cc
  源行号: 126
  时间戳: 198
    📍 源文件 object_update.cc -> 第124行:     ob->transflag &= ~OB_NEG_SCALE; | 第126行:  [目标行] | 第127行:   ob->runtime->last_update_transform = DEG_get_update_count(depsgraph);
  ❌ 未找到匹配的行

处理断点 152/208
  文件: source/blender/blenkernel/intern/object_update.cc
  源行号: 243
  时间戳: 199
    📍 源文件 object_update.cc -> 第243行:   object_orig->transflag = object->transflag;
    ✅ 目标文件 object_update.cc -> 第242行:   object_orig->transflag = object->transflag;
  ✅ 找到匹配行: 242
  📝 更新行号: 243 -> 242

处理断点 153/208
  文件: source/blender/blenkernel/intern/rigidbody.cc
  源行号: 231
  时间戳: 200
    📍 源文件 rigidbody.cc -> 第229行:     return false; | 第231行:  [目标行] | 第232行:   return true;
  ❌ 未找到匹配的行

处理断点 154/208
  文件: source/blender/blenkernel/intern/rigidbody.cc
  源行号: 228
  时间戳: 201
    📍 源文件 rigidbody.cc -> 第227行:       obCompoundParent) | 第228行:   { [目标行] | 第229行:     return false;
    🔍 找到84个精确匹配: [89, 97, 104, 109, 165, 189, 216, 236, 246, 260, 285, 316, 392, 503, 520, 542, 630, 698, 732, 785, 846, 914, 1105, 1128, 1166, 1197, 1204, 1266, 1336, 1351, 1365, 1368, 1376, 1390, 1400, 1429, 1458, 1463, 1473, 1482, 1508, 1573, 1603, 1616, 1633, 1642, 1663, 1731, 1870, 1914, 1949, 1961, 2016, 2027, 2038, 2052, 2057, 2074, 2095, 2164, 2173, 2194, 2233, 2311, 2532, 2538, 2542, 2546, 2551, 2555, 2559, 2566, 2576, 2579, 2588, 2601, 2611, 2623, 2632, 2653, 2676, 2695, 2727, 2745], 选择最接近的: 236
    ✅ 目标文件 rigidbody.cc -> 第235行: bool BKE_rigidbody_is_affected_by_simulation(Object *ob) | 第236行: { [目标行] | 第237行:   /* Check if the object will have its transform changed by the rigidbody simulation. */
  ✅ 找到匹配行: 236
  📝 更新行号: 228 -> 236

处理断点 155/208
  文件: source/blender/blenkernel/intern/scene.cc
  源行号: 2568
  时间戳: 202
    📍 源文件 scene.cc -> 第2567行:   scene_graph_update_tagged(depsgraph, bmain, true); | 第2568行: } [目标行] | 第2570行: void BKE_scene_graph_update_for_newframe_ex(Depsgraph *depsgraph, const bool clear_recalc)
    🔍 找到529个精确匹配: [122, 123, 138, 147, 206, 208, 247, 255, 282, 287, 295, 317, 322, 341, 361, 365, 368, 373, 381, 383, 384, 400, 409, 434, 446, 451, 457, 467, 500, 507, 509, 527, 528, 531, 536, 537, 611, 686, 694, 714, 716, 724, 732, 740, 748, 756, 766, 780, 785, 786, 823, 828, 832, 838, 855, 860, 869, 892, 899, 904, 905, 913, 919, 926, 931, 937, 941, 942, 943, 944, 958, 967, 975, 976, 981, 982, 984, 991, 992, 1005, 1006, 1016, 1036, 1040, 1044, 1049, 1053, 1058, 1061, 1064, 1067, 1071, 1075, 1079, 1083, 1087, 1091, 1095, 1099, 1103, 1106, 1117, 1121, 1122, 1130, 1135, 1144, 1160, 1167, 1175, 1189, 1198, 1199, 1210, 1213, 1214, 1215, 1255, 1261, 1267, 1273, 1294, 1304, 1310, 1313, 1319, 1324, 1329, 1335, 1339, 1388, 1396, 1399, 1400, 1405, 1413, 1416, 1417, 1427, 1433, 1436, 1437, 1441, 1449, 1452, 1453, 1454, 1455, 1456, 1472, 1493, 1494, 1502, 1503, 1513, 1514, 1525, 1531, 1547, 1549, 1550, 1554, 1559, 1566, 1567, 1582, 1583, 1594, 1595, 1596, 1634, 1650, 1656, 1664, 1665, 1666, 1672, 1677, 1681, 1690, 1696, 1697, 1701, 1705, 1709, 1713, 1717, 1721, 1746, 1752, 1756, 1760, 1764, 1767, 1771, 1774, 1778, 1782, 1786, 1790, 1794, 1800, 1803, 1806, 1811, 1815, 1818, 1822, 1826, 1829, 1835, 1859, 1862, 1892, 1912, 1918, 1919, 1925, 1926, 1953, 1961, 1962, 1974, 1982, 1983, 1989, 1992, 1998, 1999, 2006, 2011, 2012, 2014, 2023, 2031, 2032, 2034, 2043, 2044, 2045, 2047, 2057, 2065, 2066, 2069, 2078, 2082, 2095, 2110, 2123, 2124, 2125, 2126, 2132, 2145, 2146, 2147, 2148, 2149, 2150, 2154, 2169, 2171, 2172, 2173, 2184, 2188, 2192, 2201, 2206, 2207, 2208, 2209, 2212, 2217, 2225, 2226, 2227, 2230, 2236, 2252, 2253, 2258, 2259, 2260, 2268, 2271, 2280, 2282, 2297, 2301, 2305, 2306, 2309, 2318, 2323, 2324, 2327, 2337, 2344, 2348, 2349, 2358, 2367, 2368, 2371, 2376, 2385, 2390, 2397, 2407, 2409, 2418, 2421, 2424, 2426, 2433, 2440, 2446, 2452, 2466, 2472, 2475, 2476, 2477, 2479, 2509, 2510, 2511, 2512, 2521, 2524, 2527, 2532, 2535, 2537, 2545, 2557, 2566, 2597, 2604, 2611, 2616, 2622, 2627, 2632, 2658, 2661, 2672, 2679, 2685, 2690, 2700, 2701, 2706, 2713, 2719, 2732, 2740, 2744, 2752, 2761, 2764, 2767, 2774, 2777, 2780, 2787, 2795, 2798, 2808, 2809, 2810, 2813, 2819, 2825, 2831, 2836, 2841, 2846, 2863, 2866, 2873, 2874, 2880, 2897, 2898, 2904, 2915, 2920, 2923, 2926, 2931, 2945, 2946, 2952, 2954, 2964, 2971, 2977, 2978, 2983, 2984, 2985, 2987, 2995, 3004, 3010, 3014, 3018, 3022, 3027, 3030, 3036, 3040, 3045, 3046, 3049, 3055, 3059, 3064, 3065, 3068, 3077, 3083, 3084, 3085, 3087, 3095, 3098, 3107, 3111, 3117, 3120, 3121, 3124, 3132, 3146, 3149, 3153, 3161, 3167, 3170, 3176, 3180, 3196, 3208, 3209, 3210, 3211, 3226, 3230, 3231, 3237, 3241, 3245, 3249, 3258, 3265, 3268, 3275, 3293, 3301, 3307, 3313, 3319, 3325, 3326, 3332, 3335, 3342, 3343, 3359, 3362, 3371, 3378, 3386, 3396, 3400, 3418, 3426, 3431, 3437, 3443, 3449, 3454, 3466, 3478, 3479, 3480, 3483, 3496, 3512, 3513, 3516, 3532, 3536, 3537, 3540, 3545, 3551, 3566, 3569, 3574, 3576, 3583, 3586, 3589, 3591, 3605, 3608, 3610, 3614, 3619, 3622, 3624, 3625, 3626, 3638, 3641, 3643, 3647, 3652, 3655, 3657, 3658, 3659, 3666, 3672], 选择最接近的: 2566
    ✅ 目标文件 scene.cc -> 第2565行:     BKE_callback_exec_id(bmain, &scene->id, BKE_CB_EVT_DEPSGRAPH_UPDATE_PRE); | 第2566行:   } [目标行] | 第2568行:   for (int pass = 0; pass < 2; pass++) {
  ✅ 找到匹配行: 2566
  📝 更新行号: 2568 -> 2566

处理断点 156/208
  文件: source/blender/depsgraph/intern/eval/deg_eval.cc
  源行号: 509
  时间戳: 203
    📍 源文件 deg_eval.cc -> 第508行:     state.need_update_pending_parents = true; | 第509行:  [目标行] | 第510行:     evaluate_graph_threaded_stage(&state, task_pool, EvaluationStage::DYNAMIC_VISIBILITY);
  ❌ 未找到匹配的行

处理断点 157/208
  文件: source/gameengine/Ketsji/KX_Scene.cpp
  源行号: 760
  时间戳: 204
    📍 源文件 KX_Scene.cpp -> 第759行: //  BKE_scene_graph_update_for_newframe_ex(depsgraph, true); | 第760行:  [目标行] | 第761行: //  BKE_scene_graph_newframe_tag(depsgraph, true);
  ❌ 未找到匹配的行

处理断点 158/208
  文件: source/blender/draw/engines/eevee_next/eevee_view.cc
  源行号: 47
  时间戳: 205
  ❌ 目标文件不存在: source\blender\draw\engines\eevee_next\eevee_view.cc

处理断点 159/208
  文件: source/blender/depsgraph/intern/eval/deg_eval.cc
  源行号: 500
  时间戳: 206
    📍 源文件 deg_eval.cc -> 第499行:   std::cout << milliseconds.count() << " ms\n"; | 第500行:  [目标行] | 第501行:   TaskPool *task_pool = deg_evaluate_task_pool_create(&state);
  ❌ 未找到匹配的行

处理断点 160/208
  文件: source/blender/depsgraph/intern/eval/deg_eval.cc
  源行号: 137
  时间戳: 207
    📍 源文件 deg_eval.cc -> 第136行: //    printf("-b-%lld\n", milliseconds.count() % 1000); | 第137行:  [目标行] | 第138行:     operation_node->evaluate(depsgraph);
  ❌ 未找到匹配的行

处理断点 161/208
  文件: source/blender/depsgraph/intern/eval/deg_eval.cc
  源行号: 127
  时间戳: 215
    📍 源文件 deg_eval.cc -> 第127行:     const double start_time = BLI_time_now_seconds();
    ✅ 目标文件 deg_eval.cc -> 第95行:     const double start_time = BLI_time_now_seconds();
  ✅ 找到匹配行: 95
  📝 更新行号: 127 -> 95

处理断点 162/208
  文件: source/blender/depsgraph/intern/builder/deg_builder_nodes.cc
  源行号: 1543
  时间戳: 216
    📍 源文件 deg_builder_nodes.cc -> 第1543行:       Object *object_cow = get_cow_datablock(object);
    🔍 找到8个精确匹配: [781, 899, 1136, 1151, 1165, 1216, 1565, 1712], 选择最接近的: 1565
    ✅ 目标文件 deg_builder_nodes.cc -> 第1565行:       Object *object_cow = get_cow_datablock(object);
  ✅ 找到匹配行: 1565
  📝 更新行号: 1543 -> 1565

处理断点 163/208
  文件: source/blender/depsgraph/intern/eval/deg_eval_flush.cc
  源行号: 334
  时间戳: 222
    📍 源文件 deg_eval_flush.cc -> 第334行:   /* Reset all flags, get ready for the flush. */
    ✅ 目标文件 deg_eval_flush.cc -> 第317行:   /* Reset all flags, get ready for the flush. */
  ✅ 找到匹配行: 317
  📝 更新行号: 334 -> 317

处理断点 164/208
  文件: source/blender/editors/screen/screen_ops.cc
  源行号: 5548
  时间戳: 223
    📍 源文件 screen_ops.cc -> 第5547行:     std::cout << milliseconds.count() << " ms\n"; | 第5548行:  [目标行] | 第5549行:     ED_update_for_newframe(bmain, depsgraph);
  ❌ 未找到匹配的行

处理断点 165/208
  文件: source/blender/editors/screen/screen_ops.cc
  源行号: 5551
  时间戳: 225
    📍 源文件 screen_ops.cc -> 第5549行:     ED_update_for_newframe(bmain, depsgraph); | 第5551行:  [目标行] | 第5552行:   LISTBASE_FOREACH (wmWindow *, window, &wm->windows) {
  ❌ 未找到匹配的行

处理断点 166/208
  文件: source/blender/editors/screen/screen_ops.cc
  源行号: 5546
  时间戳: 226
    📍 源文件 screen_ops.cc -> 第5546行:     // ӡ벿
  ❌ 未找到匹配的行

处理断点 167/208
  文件: source/blender/depsgraph/intern/eval/deg_eval.cc
  源行号: 109
  时间戳: 227
    📍 源文件 deg_eval.cc -> 第109行:   if(id_node_name == "SCScene" && (ocode == "26" || ocode == "27")){
  ❌ 未找到匹配的行

处理断点 168/208
  文件: source/blender/blenkernel/intern/rigidbody.cc
  源行号: 2365
  时间戳: 231
    📍 源文件 rigidbody.cc -> 第2365行:   if (rbw->shared->physics_world == nullptr && !(cache->flag & PTCACHE_BAKED)) {
  ❌ 未找到匹配的行

处理断点 169/208
  文件: source/blender/blenkernel/intern/rigidbody.cc
  源行号: 2364
  时间戳: 232
    📍 源文件 rigidbody.cc -> 第2364行:   /* don't try to run the simulation if we don't have a world yet but allow reading baked cache */
    ✅ 目标文件 rigidbody.cc -> 第2380行:   /* don't try to run the simulation if we don't have a world yet but allow reading baked cache */
  ✅ 找到匹配行: 2380
  📝 更新行号: 2364 -> 2380

处理断点 170/208
  文件: source/blender/blenkernel/intern/rigidbody.cc
  源行号: 1192
  时间戳: 233
    📍 源文件 rigidbody.cc -> 第1191行:       RB_dworld_delete(static_cast<rbDynamicsWorld *>(rbw->shared->physics_world)); | 第1192行:     } [目标行] | 第1193行:     rbw->shared->physics_world = RB_dworld_new(scene->physics_settings.gravity);
    🔍 找到362个精确匹配: [92, 93, 100, 101, 106, 117, 129, 130, 131, 133, 138, 140, 141, 144, 153, 158, 162, 172, 183, 194, 195, 196, 200, 205, 208, 213, 222, 228, 233, 248, 251, 274, 279, 296, 299, 304, 307, 310, 323, 335, 359, 360, 375, 378, 379, 380, 383, 386, 406, 417, 424, 428, 457, 463, 481, 482, 483, 487, 491, 494, 497, 510, 515, 522, 530, 535, 536, 537, 564, 568, 596, 612, 613, 618, 620, 621, 626, 627, 670, 683, 684, 686, 687, 688, 708, 715, 722, 728, 734, 756, 770, 776, 777, 842, 852, 855, 862, 865, 872, 875, 882, 885, 892, 895, 902, 905, 906, 929, 937, 939, 944, 955, 973, 977, 985, 989, 996, 1000, 1004, 1008, 1028, 1041, 1062, 1063, 1066, 1071, 1079, 1083, 1088, 1092, 1093, 1099, 1100, 1109, 1115, 1117, 1122, 1138, 1163, 1174, 1175, 1179, 1187, 1194, 1201, 1215, 1218, 1247, 1250, 1263, 1277, 1333, 1341, 1344, 1347, 1348, 1356, 1359, 1361, 1362, 1373, 1378, 1382, 1383, 1384, 1394, 1397, 1407, 1411, 1416, 1426, 1436, 1440, 1445, 1455, 1466, 1467, 1468, 1476, 1477, 1478, 1479, 1486, 1492, 1497, 1505, 1521, 1522, 1523, 1533, 1537, 1538, 1539, 1541, 1550, 1554, 1562, 1570, 1582, 1588, 1589, 1596, 1608, 1618, 1619, 1626, 1636, 1637, 1639, 1650, 1653, 1660, 1667, 1689, 1690, 1706, 1707, 1708, 1715, 1720, 1739, 1757, 1758, 1760, 1782, 1793, 1796, 1808, 1809, 1810, 1815, 1816, 1822, 1842, 1848, 1851, 1852, 1854, 1856, 1879, 1906, 1907, 1911, 1930, 1942, 1943, 1944, 1955, 1993, 1997, 2001, 2002, 2005, 2009, 2011, 2013, 2020, 2023, 2045, 2046, 2047, 2049, 2054, 2061, 2086, 2090, 2091, 2105, 2116, 2119, 2122, 2132, 2135, 2139, 2143, 2146, 2149, 2155, 2158, 2161, 2167, 2168, 2196, 2197, 2202, 2217, 2218, 2226, 2227, 2236, 2243, 2248, 2259, 2260, 2267, 2295, 2308, 2328, 2353, 2366, 2376, 2383, 2386, 2398, 2399, 2405, 2411, 2419, 2429, 2434, 2435, 2442, 2453, 2457, 2461, 2462, 2467, 2468, 2478, 2487, 2504, 2514, 2515, 2535, 2536, 2540, 2544, 2548, 2553, 2557, 2561, 2569, 2577, 2581, 2589, 2607, 2608, 2618, 2620, 2629, 2641, 2642, 2643, 2665, 2669, 2673, 2688, 2692, 2701, 2716, 2717, 2722, 2723, 2733, 2736, 2738, 2739, 2740, 2741, 2748, 2753, 2758], 选择最接近的: 1194
    ✅ 目标文件 rigidbody.cc -> 第1193行:   return rbw_copy; | 第1194行: } [目标行] | 第1196行: void BKE_rigidbody_world_groups_relink(RigidBodyWorld *rbw)
  ✅ 找到匹配行: 1194
  📝 更新行号: 1192 -> 1194

处理断点 171/208
  文件: source/blender/blenkernel/intern/rigidbody.cc
  源行号: 2292
  时间戳: 234
    📍 源文件 rigidbody.cc -> 第2292行:       BKE_ptcache_id_reset(scene, &pid, PTCACHE_RESET_OUTDATED);
    🔍 找到3个精确匹配: [1561, 2212, 2221], 选择最接近的: 2221
    ✅ 目标文件 rigidbody.cc -> 第2221行:     BKE_ptcache_id_reset(scene, &pid, PTCACHE_RESET_OUTDATED);
  ✅ 找到匹配行: 2221
  📝 更新行号: 2292 -> 2221

处理断点 172/208
  文件: source/blender/blenkernel/intern/rigidbody.cc
  源行号: 2289
  时间戳: 235
    📍 源文件 rigidbody.cc -> 第2287行:     cache->flag |= PTCACHE_OUTDATED; | 第2289行:  [目标行] | 第2290行:   if (ctime == startframe + 1 && rbw->ltime == startframe) {
  ❌ 未找到匹配的行

处理断点 173/208
  文件: source/blender/blenkernel/intern/rigidbody.cc
  源行号: 2290
  时间戳: 236
    📍 源文件 rigidbody.cc -> 第2290行:   if (ctime == startframe + 1 && rbw->ltime == startframe) {
    ✅ 目标文件 rigidbody.cc -> 第2210行:   if (ctime == startframe + 1 && rbw->ltime == startframe) {
  ✅ 找到匹配行: 2210
  📝 更新行号: 2290 -> 2210

处理断点 174/208
  文件: source/blender/blenkernel/intern/scene.cc
  源行号: 1459
  时间戳: 237
    📍 源文件 scene.cc -> 第1459行:        */
    🔍 找到8个精确匹配: [7, 472, 2266, 2494, 2552, 2646, 3284, 3409], 选择最接近的: 2266
    ✅ 目标文件 scene.cc -> 第2266行:      */
  ✅ 找到匹配行: 2266
  📝 更新行号: 1459 -> 2266

处理断点 175/208
  文件: source/blender/depsgraph/intern/depsgraph_eval.cc
  源行号: 63
  时间戳: 238
    📍 源文件 depsgraph_eval.cc -> 第63行:   const float frame = BKE_scene_frame_get(scene);
    ✅ 目标文件 depsgraph_eval.cc -> 第57行:   const float frame = BKE_scene_frame_get(scene);
  ✅ 找到匹配行: 57
  📝 更新行号: 63 -> 57

处理断点 176/208
  文件: source/blender/blenkernel/intern/rigidbody.cc
  源行号: 2388
  时间戳: 239
    📍 源文件 rigidbody.cc -> 第2388行:   /* advance simulation, we can only step one frame forward */
  ❌ 未找到匹配的行

处理断点 177/208
  文件: source/blender/blenkernel/intern/rigidbody.cc
  源行号: 2355
  时间戳: 241
    📍 源文件 rigidbody.cc -> 第2355行:   if (ctime <= startframe) {
    ✅ 目标文件 rigidbody.cc -> 第2373行:   if (ctime <= startframe) {
  ✅ 找到匹配行: 2373
  📝 更新行号: 2355 -> 2373

处理断点 178/208
  文件: source/blender/blenkernel/intern/rigidbody.cc
  源行号: 2356
  时间戳: 242
    📍 源文件 rigidbody.cc -> 第2356行:     rbw->ltime = startframe;
    ✅ 目标文件 rigidbody.cc -> 第2374行:     rbw->ltime = startframe;
  ✅ 找到匹配行: 2374
  📝 更新行号: 2356 -> 2374

处理断点 179/208
  文件: source/gameengine/GameLogic/SCA_ActionActuator.cpp
  源行号: 278
  时间戳: 244
    📍 源文件 SCA_ActionActuator.cpp -> 第278行:                                                             BL_Action::ACT_BLEND_BLEND;
    ✅ 目标文件 SCA_ActionActuator.cpp -> 第274行:                                                             BL_Action::ACT_BLEND_BLEND;
  ✅ 找到匹配行: 274
  📝 更新行号: 278 -> 274

处理断点 180/208
  文件: source/gameengine/Ketsji/KX_Scene.cpp
  源行号: 2457
  时间戳: 245
    📍 源文件 KX_Scene.cpp -> 第2456行:    m_animationPoolData.curtime = curtime; | 第2457行:  [目标行] | 第2458行:   for (KX_GameObject *gameobj : m_animatedlist) {
  ❌ 未找到匹配的行

处理断点 181/208
  文件: source/gameengine/Ketsji/BL_Action.cpp
  源行号: 186
  时间戳: 246
    📍 源文件 BL_Action.cpp -> 第186行:   // Now that we have an action, we have something we can play
    ✅ 目标文件 BL_Action.cpp -> 第188行:   // Now that we have an action, we have something we can play
  ✅ 找到匹配行: 188
  📝 更新行号: 186 -> 188

处理断点 182/208
  文件: source/blender/editors/screen/screen_ops.cc
  源行号: 5374
  时间戳: 247
    📍 源文件 screen_ops.cc -> 第5373行: static int screen_animation_step_invoke(bContext *C, wmOperator * /*op*/, const wmEvent *event) | 第5374行: { [目标行] | 第5375行:   bScreen *screen = CTX_wm_screen(C);
    🔍 找到283个精确匹配: [99, 113, 127, 138, 147, 161, 176, 185, 194, 214, 224, 235, 244, 249, 259, 272, 285, 290, 304, 317, 322, 330, 338, 343, 348, 353, 364, 375, 380, 385, 390, 395, 400, 405, 410, 415, 421, 427, 433, 453, 459, 464, 470, 477, 484, 490, 499, 504, 514, 530, 542, 549, 565, 578, 594, 605, 612, 619, 635, 644, 654, 663, 674, 683, 692, 701, 710, 722, 771, 793, 798, 805, 844, 869, 890, 949, 1033, 1045, 1057, 1062, 1067, 1079, 1105, 1144, 1187, 1273, 1278, 1305, 1371, 1387, 1397, 1402, 1415, 1459, 1472, 1500, 1508, 1528, 1556, 1581, 1609, 1633, 1701, 1774, 1783, 1821, 1844, 1877, 1947, 1974, 2023, 2031, 2048, 2061, 2084, 2093, 2150, 2226, 2234, 2243, 2257, 2269, 2300, 2328, 2350, 2378, 2408, 2422, 2561, 2573, 2592, 2736, 2777, 2820, 2829, 2840, 2848, 2855, 2866, 2887, 2946, 2966, 2982, 2991, 3026, 3072, 3149, 3154, 3178, 3185, 3201, 3232, 3256, 3279, 3318, 3341, 3352, 3376, 3400, 3411, 3492, 3497, 3520, 3561, 3584, 3596, 3616, 3653, 3668, 3735, 3751, 3762, 3770, 3784, 3819, 3845, 3871, 3896, 3941, 3980, 3984, 3999, 4004, 4054, 4095, 4136, 4148, 4217, 4267, 4307, 4329, 4337, 4401, 4406, 4409, 4499, 4547, 4624, 4646, 4668, 4686, 4695, 4716, 4737, 4753, 4767, 4783, 4806, 4817, 4835, 4846, 4868, 4988, 5007, 5027, 5040, 5067, 5095, 5108, 5128, 5140, 5159, 5193, 5195, 5225, 5228, 5240, 5255, 5272, 5316, 5338, 5447, 5452, 5509, 5554, 5680, 5714, 5735, 5770, 5783, 5796, 5842, 5854, 5881, 5906, 5950, 5967, 5995, 6017, 6035, 6071, 6089, 6103, 6132, 6167, 6204, 6222, 6250, 6258, 6276, 6290, 6308, 6319, 6350, 6354, 6377, 6404, 6441, 6467, 6489, 6495, 6535, 6563, 6577, 6601, 6617, 6646, 6683, 6710, 6767, 6783, 6794, 6800], 选择最接近的: 5338
    ✅ 目标文件 screen_ops.cc -> 第5337行:                                       bool from_anim_edit) | 第5338行: { [目标行] | 第5339行:   const eSpace_Type spacetype = eSpace_Type(area->spacetype);
  ✅ 找到匹配行: 5338
  📝 更新行号: 5374 -> 5338

处理断点 183/208
  文件: source/blender/depsgraph/intern/depsgraph_eval.cc
  源行号: 100
  时间戳: 250
    📍 源文件 depsgraph_eval.cc -> 第100行:   deg_graph->frame = frame;
    🔍 找到2个精确匹配: [62, 85], 选择最接近的: 85
    ✅ 目标文件 depsgraph_eval.cc -> 第85行:   deg_graph->frame = frame;
  ✅ 找到匹配行: 85
  📝 更新行号: 100 -> 85

处理断点 184/208
  文件: source/blender/render/intern/engine.cc
  源行号: 693
  时间戳: 251
    📍 源文件 engine.cc -> 第691行:       DRW_render_context_enable(engine->re); | 第693行:  [目标行] | 第694行:     DEG_evaluate_on_framechange(depsgraph, BKE_scene_frame_get(scene));
  ❌ 未找到匹配的行

处理断点 185/208
  文件: source/blender/render/intern/pipeline.cc
  源行号: 1956
  时间戳: 252
    📍 源文件 pipeline.cc -> 第1956行:   Scene *scene = re->scene;
    🔍 找到2个精确匹配: [2012, 2019], 选择最接近的: 2012
    ✅ 目标文件 pipeline.cc -> 第2012行:   Scene *scene = re->scene;
  ✅ 找到匹配行: 2012
  📝 更新行号: 1956 -> 2012

处理断点 186/208
  文件: source/gameengine/Ketsji/BL_Action.cpp
  源行号: 584
  时间戳: 253
    📍 源文件 BL_Action.cpp -> 第584行:             LISTBASE_FOREACH (NlaStrip *, strip, &track->strips) {
    🔍 找到2个精确匹配: [573, 604], 选择最接近的: 573
    ✅ 目标文件 BL_Action.cpp -> 第573行:             LISTBASE_FOREACH (NlaStrip *, strip, &track->strips) {
  ✅ 找到匹配行: 573
  📝 更新行号: 584 -> 573

处理断点 187/208
  文件: source/gameengine/Ketsji/BL_Action.cpp
  源行号: 615
  时间戳: 254
    📍 源文件 BL_Action.cpp -> 第615行:             LISTBASE_FOREACH (NlaStrip *, strip, &track->strips) {
    🔍 找到2个精确匹配: [573, 604], 选择最接近的: 604
    ✅ 目标文件 BL_Action.cpp -> 第604行:             LISTBASE_FOREACH (NlaStrip *, strip, &track->strips) {
  ✅ 找到匹配行: 604
  📝 更新行号: 615 -> 604

处理断点 188/208
  文件: source/gameengine/Ketsji/KX_KetsjiEngine.cpp
  源行号: 538
  时间戳: 255
    📍 源文件 KX_KetsjiEngine.cpp -> 第538行:       m_logger.StartLog(tc_logic);
    🔍 找到6个精确匹配: [310, 355, 518, 530, 541, 572], 选择最接近的: 541
    ✅ 目标文件 KX_KetsjiEngine.cpp -> 第541行:       m_logger.StartLog(tc_logic);
  ✅ 找到匹配行: 541
  📝 更新行号: 538 -> 541

处理断点 189/208
  文件: source/gameengine/GameLogic/SCA_ActionActuator.cpp
  源行号: 168
  时间戳: 257
    📍 源文件 SCA_ActionActuator.cpp -> 第168行:       case ACT_ACTION_LOOP_END:
    🔍 找到3个精确匹配: [107, 164, 239], 选择最接近的: 164
    ✅ 目标文件 SCA_ActionActuator.cpp -> 第164行:       case ACT_ACTION_LOOP_END:
  ✅ 找到匹配行: 164
  📝 更新行号: 168 -> 164

处理断点 190/208
  文件: source/gameengine/GameLogic/SCA_ActionActuator.cpp
  源行号: 126
  时间戳: 260
    📍 源文件 SCA_ActionActuator.cpp -> 第126行:     // "Active" actions need to keep updating their current frame
    ✅ 目标文件 SCA_ActionActuator.cpp -> 第126行:     // "Active" actions need to keep updating their current frame
  ✅ 找到匹配行: 126
  ⏭️  行号未变化，跳过

处理断点 191/208
  文件: source/gameengine/Ketsji/BL_Action.cpp
  源行号: 251
  时间戳: 261
    📍 源文件 BL_Action.cpp -> 第250行:     frame = std::max(m_startframe, m_endframe); | 第251行:  [目标行] | 第252行:   m_localframe = frame;
  ❌ 未找到匹配的行

处理断点 192/208
  文件: source/gameengine/Ketsji/BL_Action.cpp
  源行号: 275
  时间戳: 262
    📍 源文件 BL_Action.cpp -> 第273行:     scene->m_blenderScene->r.cfra = cfra + 1; | 第275行:  [目标行] | 第276行: //  m_localframe = m_startframe + dt;
  ❌ 未找到匹配的行

处理断点 193/208
  文件: source/gameengine/Ketsji/KX_Scene.cpp
  源行号: 712
  时间戳: 263
    📍 源文件 KX_Scene.cpp -> 第712行:   Main *bmain = CTX_data_main(C);
    🔍 找到10个精确匹配: [248, 327, 528, 558, 689, 937, 959, 1061, 1193, 1399], 选择最接近的: 689
    ✅ 目标文件 KX_Scene.cpp -> 第689行:   Main *bmain = CTX_data_main(C);
  ✅ 找到匹配行: 689
  📝 更新行号: 712 -> 689

处理断点 194/208
  文件: source/gameengine/GameLogic/SCA_LogicManager.cpp
  源行号: 161
  时间戳: 264
    📍 源文件 SCA_LogicManager.cpp -> 第160行:     (*ie)->UpdateFrame(); | 第161行:  [目标行] | 第162行:   SG_DList::iterator<SG_QList> io(m_activeActuators);
  ❌ 未找到匹配的行

处理断点 195/208
  文件: source/gameengine/Ketsji/BL_Action.cpp
  源行号: 262
  时间戳: 268
    📍 源文件 BL_Action.cpp -> 第261行: void BL_Action::SetLocalTime(float curtime) | 第262行: { [目标行] | 第263行:   float dt = (curtime - m_starttime) * (float)KX_GetActiveEngine()->GetAnimFrameRate() * m_speed;
    🔍 找到18个精确匹配: [68, 82, 96, 117, 210, 215, 227, 232, 237, 247, 259, 264, 287, 296, 310, 322, 376, 659], 选择最接近的: 264
    ✅ 目标文件 BL_Action.cpp -> 第263行: void BL_Action::SetLocalTime(float curtime) | 第264行: { [目标行] | 第265行:   float dt = (curtime - m_starttime) * (float)KX_GetActiveEngine()->GetAnimFrameRate() * m_speed;
  ✅ 找到匹配行: 264
  📝 更新行号: 262 -> 264

处理断点 196/208
  文件: source/gameengine/Ketsji/BL_Action.cpp
  源行号: 388
  时间戳: 269
    📍 源文件 BL_Action.cpp -> 第388行:   // Handle wrap around
    ✅ 目标文件 BL_Action.cpp -> 第399行:   // Handle wrap around
  ✅ 找到匹配行: 399
  📝 更新行号: 388 -> 399

处理断点 197/208
  文件: source/gameengine/Ketsji/KX_Scene.cpp
  源行号: 1404
  时间戳: 272
    📍 源文件 KX_Scene.cpp -> 第1404行:   } [目标行] | 第1405行:   bool use_interactive_rb = scene->gm.flag & GAME_USE_INTERACTIVE_RIGIDBODY;
    🔍 找到455个精确匹配: [114, 126, 131, 212, 222, 301, 302, 305, 310, 334, 341, 346, 353, 354, 355, 359, 379, 386, 396, 409, 413, 417, 430, 433, 445, 452, 454, 487, 488, 489, 490, 491, 492, 497, 506, 512, 519, 536, 537, 538, 549, 561, 562, 563, 567, 569, 570, 578, 586, 590, 597, 600, 605, 606, 612, 615, 620, 625, 631, 633, 638, 643, 648, 653, 659, 665, 668, 672, 674, 700, 703, 704, 713, 722, 729, 737, 750, 758, 774, 788, 799, 800, 814, 817, 818, 836, 849, 853, 887, 892, 901, 917, 922, 927, 949, 973, 974, 1007, 1008, 1043, 1047, 1050, 1051, 1075, 1077, 1110, 1112, 1139, 1142, 1143, 1151, 1152, 1153, 1158, 1166, 1167, 1175, 1176, 1181, 1191, 1197, 1202, 1207, 1212, 1218, 1219, 1227, 1229, 1261, 1263, 1264, 1265, 1282, 1288, 1294, 1297, 1298, 1299, 1300, 1304, 1305, 1314, 1315, 1317, 1327, 1328, 1335, 1336, 1337, 1346, 1354, 1356, 1358, 1373, 1374, 1375, 1376, 1384, 1385, 1386, 1397, 1433, 1453, 1454, 1459, 1460, 1462, 1469, 1475, 1480, 1485, 1490, 1495, 1500, 1505, 1510, 1515, 1520, 1525, 1530, 1546, 1551, 1558, 1566, 1570, 1579, 1582, 1602, 1606, 1621, 1629, 1634, 1644, 1648, 1652, 1656, 1657, 1673, 1674, 1691, 1694, 1711, 1738, 1751, 1752, 1755, 1756, 1768, 1781, 1782, 1786, 1787, 1788, 1791, 1804, 1805, 1806, 1808, 1809, 1839, 1845, 1846, 1856, 1857, 1861, 1872, 1885, 1912, 1918, 1926, 1931, 1940, 1956, 1960, 1961, 1965, 1966, 1995, 2009, 2024, 2031, 2040, 2044, 2045, 2050, 2058, 2059, 2062, 2068, 2080, 2081, 2088, 2089, 2090, 2103, 2126, 2133, 2139, 2144, 2154, 2155, 2162, 2163, 2172, 2181, 2184, 2187, 2190, 2193, 2196, 2207, 2211, 2216, 2226, 2230, 2243, 2245, 2246, 2251, 2255, 2256, 2262, 2267, 2272, 2277, 2284, 2292, 2293, 2310, 2326, 2329, 2330, 2333, 2341, 2342, 2343, 2344, 2358, 2362, 2363, 2367, 2368, 2370, 2375, 2435, 2436, 2439, 2446, 2460, 2468, 2469, 2481, 2488, 2489, 2494, 2503, 2504, 2509, 2514, 2519, 2524, 2530, 2537, 2538, 2543, 2550, 2558, 2560, 2561, 2566, 2571, 2576, 2585, 2593, 2594, 2599, 2615, 2620, 2621, 2628, 2633, 2638, 2644, 2656, 2657, 2658, 2673, 2674, 2688, 2699, 2700, 2704, 2715, 2716, 2723, 2724, 2768, 2777, 2778, 2780, 2785, 2793, 2802, 2807, 2810, 2811, 2818, 2822, 2828, 2901, 2912, 2920, 2921, 2933, 2951, 2954, 2955, 2970, 2973, 2982, 2991, 3016, 3023, 3030, 3036, 3045, 3051, 3058, 3069, 3083, 3091, 3102, 3106, 3121, 3126, 3137, 3147, 3156, 3161, 3172, 3180, 3188, 3202, 3270, 3278, 3284, 3286, 3297, 3307, 3325, 3336, 3351, 3355, 3367, 3373, 3382, 3384, 3397, 3409, 3413, 3417, 3430, 3436, 3441, 3453, 3459, 3464, 3476, 3482, 3491, 3493, 3506, 3512, 3518, 3523, 3535, 3541, 3546, 3558, 3565, 3572, 3575, 3578, 3589, 3596, 3602, 3603, 3611, 3618, 3619, 3628, 3631, 3637, 3640, 3643], 选择最接近的: 1397
    ✅ 目标文件 KX_Scene.cpp -> 第1396行:       return nullptr; | 第1397行:     } [目标行] | 第1398行:     bContext *C = KX_GetActiveEngine()->GetContext();
  ✅ 找到匹配行: 1397
  📝 更新行号: 1404 -> 1397

处理断点 198/208
  文件: source/gameengine/GameLogic/SCA_ActionActuator.cpp
  源行号: 133
  时间戳: 274
    📍 源文件 SCA_ActionActuator.cpp -> 第133行:     // Handle a frame property if it's defined
    ✅ 目标文件 SCA_ActionActuator.cpp -> 第129行:     // Handle a frame property if it's defined
  ✅ 找到匹配行: 129
  📝 更新行号: 133 -> 129

处理断点 199/208
  文件: source/blender/depsgraph/intern/depsgraph_eval.cc
  源行号: 72
  时间戳: 275
    📍 源文件 depsgraph_eval.cc -> 第72行:     if(deg_graph->frame == 1.0f) {
  ❌ 未找到匹配的行

处理断点 200/208
  文件: intern/rigidbody/rb_bullet_api.cpp
  源行号: 198
  时间戳: 276
    📍 源文件 rb_bullet_api.cpp -> 第197行:                                float timeSubStep) | 第198行: { [目标行] | 第199行:   world->dynamicsWorld->stepSimulation(timeStep, maxSubSteps, timeSubStep);
    🔍 找到101个精确匹配: [92, 106, 112, 125, 152, 168, 173, 180, 189, 201, 208, 231, 239, 255, 313, 335, 362, 375, 391, 410, 416, 422, 428, 434, 440, 445, 451, 456, 462, 468, 473, 479, 484, 492, 499, 506, 513, 520, 526, 534, 547, 557, 562, 576, 587, 601, 620, 627, 634, 647, 659, 669, 679, 689, 699, 712, 738, 749, 757, 766, 773, 783, 804, 828, 844, 859, 879, 905, 910, 920, 927, 941, 952, 970, 987, 1004, 1021, 1039, 1057, 1075, 1093, 1117, 1132, 1139, 1151, 1160, 1170, 1177, 1185, 1193, 1204, 1212, 1220, 1228, 1236, 1244, 1252, 1259, 1266, 1276, 1286], 选择最接近的: 201
    ✅ 目标文件 rb_bullet_api.cpp -> 第200行:                                float timeSubStep) | 第201行: { [目标行] | 第202行:   world->dynamicsWorld->stepSimulation(timeStep, maxSubSteps, timeSubStep);
  ✅ 找到匹配行: 201
  📝 更新行号: 198 -> 201

处理断点 201/208
  文件: extern/bullet2/src/BulletDynamics/Dynamics/btDiscreteDynamicsWorld.cpp
  源行号: 385
  时间戳: 277
    📍 源文件 btDiscreteDynamicsWorld.cpp -> 第384行: int btDiscreteDynamicsWorld::stepSimulation(btScalar timeStep, int maxSubSteps, btScalar fixedTimeStep) | 第385行: { [目标行] | 第386行: 	startProfiling(timeStep);
    🔍 找到191个精确匹配: [57, 67, 70, 79, 101, 105, 112, 124, 126, 131, 139, 141, 148, 150, 156, 160, 168, 172, 179, 204, 206, 212, 216, 223, 230, 233, 238, 243, 250, 255, 259, 261, 270, 277, 280, 285, 287, 294, 298, 300, 310, 313, 323, 326, 329, 336, 340, 345, 357, 360, 363, 371, 374, 383, 389, 394, 400, 406, 411, 419, 424, 433, 439, 453, 457, 494, 500, 503, 506, 513, 518, 523, 532, 538, 540, 545, 547, 551, 564, 566, 571, 573, 577, 585, 589, 595, 599, 602, 606, 608, 612, 616, 623, 632, 638, 645, 652, 657, 662, 667, 672, 677, 682, 688, 710, 715, 718, 726, 732, 736, 739, 745, 757, 770, 774, 795, 807, 819, 825, 827, 831, 850, 853, 858, 864, 867, 871, 874, 878, 901, 930, 934, 942, 946, 952, 955, 960, 966, 969, 973, 976, 980, 1003, 1016, 1048, 1051, 1057, 1060, 1066, 1072, 1092, 1095, 1098, 1109, 1118, 1123, 1128, 1130, 1146, 1155, 1160, 1166, 1175, 1182, 1190, 1205, 1209, 1221, 1228, 1254, 1258, 1270, 1271, 1278, 1286, 1307, 1311, 1323, 1330, 1351, 1353, 1362, 1367, 1371, 1375, 1380, 1384, 1387, 1396, 1406, 1457], 选择最接近的: 383
    ✅ 目标文件 btDiscreteDynamicsWorld.cpp -> 第382行: int btDiscreteDynamicsWorld::stepSimulation(btScalar timeStep, int maxSubSteps, btScalar fixedTimeStep) | 第383行: { [目标行] | 第384行: 	startProfiling(timeStep);
  ✅ 找到匹配行: 383
  📝 更新行号: 385 -> 383

处理断点 202/208
  文件: source/gameengine/Physics/Bullet/CcdPhysicsEnvironment.cpp
  源行号: 737
  时间戳: 278
    📍 源文件 CcdPhysicsEnvironment.cpp -> 第737行:   float subStep = timeStep / float(m_numTimeSubSteps);
    ✅ 目标文件 CcdPhysicsEnvironment.cpp -> 第737行:   float subStep = timeStep / float(m_numTimeSubSteps);
  ✅ 找到匹配行: 737
  ⏭️  行号未变化，跳过

处理断点 203/208
  文件: source/blender/blenkernel/intern/rigidbody.cc
  源行号: 2298
  时间戳: 281
    📍 源文件 rigidbody.cc -> 第2296行:       cache->flag &= ~PTCACHE_REDO_NEEDED; | 第2298行:   } [目标行] | 第2301行: void BKE_rigidbody_do_simulation(Depsgraph *depsgraph, Scene *scene, float ctime)
    🔍 找到362个精确匹配: [92, 93, 100, 101, 106, 117, 129, 130, 131, 133, 138, 140, 141, 144, 153, 158, 162, 172, 183, 194, 195, 196, 200, 205, 208, 213, 222, 228, 233, 248, 251, 274, 279, 296, 299, 304, 307, 310, 323, 335, 359, 360, 375, 378, 379, 380, 383, 386, 406, 417, 424, 428, 457, 463, 481, 482, 483, 487, 491, 494, 497, 510, 515, 522, 530, 535, 536, 537, 564, 568, 596, 612, 613, 618, 620, 621, 626, 627, 670, 683, 684, 686, 687, 688, 708, 715, 722, 728, 734, 756, 770, 776, 777, 842, 852, 855, 862, 865, 872, 875, 882, 885, 892, 895, 902, 905, 906, 929, 937, 939, 944, 955, 973, 977, 985, 989, 996, 1000, 1004, 1008, 1028, 1041, 1062, 1063, 1066, 1071, 1079, 1083, 1088, 1092, 1093, 1099, 1100, 1109, 1115, 1117, 1122, 1138, 1163, 1174, 1175, 1179, 1187, 1194, 1201, 1215, 1218, 1247, 1250, 1263, 1277, 1333, 1341, 1344, 1347, 1348, 1356, 1359, 1361, 1362, 1373, 1378, 1382, 1383, 1384, 1394, 1397, 1407, 1411, 1416, 1426, 1436, 1440, 1445, 1455, 1466, 1467, 1468, 1476, 1477, 1478, 1479, 1486, 1492, 1497, 1505, 1521, 1522, 1523, 1533, 1537, 1538, 1539, 1541, 1550, 1554, 1562, 1570, 1582, 1588, 1589, 1596, 1608, 1618, 1619, 1626, 1636, 1637, 1639, 1650, 1653, 1660, 1667, 1689, 1690, 1706, 1707, 1708, 1715, 1720, 1739, 1757, 1758, 1760, 1782, 1793, 1796, 1808, 1809, 1810, 1815, 1816, 1822, 1842, 1848, 1851, 1852, 1854, 1856, 1879, 1906, 1907, 1911, 1930, 1942, 1943, 1944, 1955, 1993, 1997, 2001, 2002, 2005, 2009, 2011, 2013, 2020, 2023, 2045, 2046, 2047, 2049, 2054, 2061, 2086, 2090, 2091, 2105, 2116, 2119, 2122, 2132, 2135, 2139, 2143, 2146, 2149, 2155, 2158, 2161, 2167, 2168, 2196, 2197, 2202, 2217, 2218, 2226, 2227, 2236, 2243, 2248, 2259, 2260, 2267, 2295, 2308, 2328, 2353, 2366, 2376, 2383, 2386, 2398, 2399, 2405, 2411, 2419, 2429, 2434, 2435, 2442, 2453, 2457, 2461, 2462, 2467, 2468, 2478, 2487, 2504, 2514, 2515, 2535, 2536, 2540, 2544, 2548, 2553, 2557, 2561, 2569, 2577, 2581, 2589, 2607, 2608, 2618, 2620, 2629, 2641, 2642, 2643, 2665, 2669, 2673, 2688, 2692, 2701, 2716, 2717, 2722, 2723, 2733, 2736, 2738, 2739, 2740, 2741, 2748, 2753, 2758], 选择最接近的: 2295
    ✅ 目标文件 rigidbody.cc -> 第2294行:     cur_interp_val += interp_step; | 第2295行:   } [目标行] | 第2297行:   /* Cleanup and post-processing */
  ✅ 找到匹配行: 2295
  📝 更新行号: 2298 -> 2295

处理断点 204/208
  文件: source/blender/blenkernel/intern/rigidbody.cc
  源行号: 2517
  时间戳: 284
    📍 源文件 rigidbody.cc -> 第2516行: void BKE_rigidbody_rebuild_sim(Depsgraph *depsgraph, Scene *scene) | 第2517行: { [目标行] | 第2518行:   float ctime = DEG_get_ctime(depsgraph);
    🔍 找到84个精确匹配: [89, 97, 104, 109, 165, 189, 216, 236, 246, 260, 285, 316, 392, 503, 520, 542, 630, 698, 732, 785, 846, 914, 1105, 1128, 1166, 1197, 1204, 1266, 1336, 1351, 1365, 1368, 1376, 1390, 1400, 1429, 1458, 1463, 1473, 1482, 1508, 1573, 1603, 1616, 1633, 1642, 1663, 1731, 1870, 1914, 1949, 1961, 2016, 2027, 2038, 2052, 2057, 2074, 2095, 2164, 2173, 2194, 2233, 2311, 2532, 2538, 2542, 2546, 2551, 2555, 2559, 2566, 2576, 2579, 2588, 2601, 2611, 2623, 2632, 2653, 2676, 2695, 2727, 2745], 选择最接近的: 2532
    ✅ 目标文件 rigidbody.cc -> 第2531行: void BKE_rigidbody_calc_volume(Object *ob, float *r_vol) | 第2532行: { [目标行] | 第2533行:   if (r_vol) {
  ✅ 找到匹配行: 2532
  📝 更新行号: 2517 -> 2532

处理断点 205/208
  文件: source/blender/windowmanager/intern/wm_event_system.cc
  源行号: 598
  时间戳: 285
    📍 源文件 wm_event_system.cc -> 第598行:           else if (note->data == ND_WORKSPACE_DELETE) {
    ✅ 目标文件 wm_event_system.cc -> 第631行:           else if (note->data == ND_WORKSPACE_DELETE) {
  ✅ 找到匹配行: 631
  📝 更新行号: 598 -> 631

处理断点 206/208
  文件: source/blender/windowmanager/intern/wm_event_system.cc
  源行号: 585
  时间戳: 286
    📍 源文件 wm_event_system.cc -> 第583行:           ED_preview_restart_queue_work(C); | 第585行:       } [目标行] | 第586行:       if (note->window == win) {
    🔍 找到1036个精确匹配: [169, 170, 172, 188, 193, 200, 210, 221, 223, 229, 235, 238, 239, 246, 256, 260, 266, 279, 284, 295, 302, 303, 309, 310, 315, 331, 345, 369, 374, 379, 391, 393, 400, 408, 430, 434, 435, 436, 442, 444, 445, 455, 456, 457, 467, 470, 472, 473, 476, 483, 488, 497, 506, 524, 527, 530, 544, 545, 546, 551, 558, 566, 580, 603, 611, 614, 617, 618, 629, 630, 638, 639, 650, 651, 659, 660, 661, 662, 670, 671, 672, 675, 683, 684, 691, 702, 703, 704, 715, 732, 735, 758, 766, 767, 782, 783, 784, 785, 788, 796, 798, 810, 819, 825, 845, 848, 873, 880, 883, 884, 890, 895, 898, 902, 912, 918, 922, 927, 930, 939, 951, 952, 953, 960, 976, 977, 989, 996, 1002, 1010, 1014, 1020, 1033, 1049, 1064, 1066, 1072, 1074, 1084, 1085, 1090, 1093, 1096, 1103, 1113, 1114, 1116, 1121, 1123, 1133, 1134, 1137, 1146, 1147, 1148, 1167, 1174, 1175, 1186, 1192, 1193, 1194, 1201, 1204, 1215, 1241, 1251, 1252, 1257, 1258, 1267, 1268, 1269, 1270, 1276, 1288, 1289, 1292, 1293, 1300, 1301, 1304, 1307, 1308, 1309, 1326, 1330, 1338, 1345, 1346, 1353, 1361, 1366, 1367, 1370, 1381, 1387, 1392, 1397, 1402, 1411, 1419, 1424, 1431, 1432, 1434, 1437, 1446, 1449, 1451, 1470, 1473, 1479, 1483, 1494, 1504, 1518, 1519, 1521, 1529, 1530, 1534, 1535, 1540, 1553, 1559, 1560, 1561, 1562, 1563, 1572, 1577, 1578, 1597, 1601, 1604, 1618, 1623, 1632, 1641, 1648, 1649, 1653, 1660, 1661, 1665, 1672, 1676, 1682, 1699, 1702, 1705, 1706, 1715, 1721, 1724, 1725, 1728, 1735, 1738, 1739, 1742, 1777, 1779, 1782, 1787, 1788, 1801, 1802, 1833, 1841, 1842, 1850, 1861, 1875, 1879, 1880, 1883, 1893, 1903, 1906, 1913, 1916, 1928, 1938, 1953, 1959, 1962, 1986, 1988, 1992, 1995, 1998, 2010, 2012, 2019, 2021, 2025, 2027, 2031, 2033, 2036, 2037, 2051, 2062, 2065, 2080, 2081, 2082, 2087, 2102, 2105, 2106, 2123, 2124, 2128, 2136, 2150, 2173, 2177, 2185, 2186, 2195, 2196, 2206, 2207, 2210, 2215, 2216, 2221, 2222, 2223, 2224, 2233, 2254, 2256, 2257, 2267, 2273, 2277, 2283, 2286, 2287, 2288, 2299, 2302, 2306, 2314, 2319, 2320, 2323, 2324, 2330, 2335, 2336, 2345, 2346, 2347, 2356, 2359, 2362, 2363, 2367, 2368, 2369, 2374, 2375, 2381, 2382, 2383, 2390, 2391, 2396, 2397, 2402, 2403, 2408, 2409, 2414, 2415, 2423, 2424, 2427, 2439, 2440, 2441, 2443, 2472, 2485, 2491, 2492, 2509, 2510, 2511, 2518, 2519, 2520, 2537, 2541, 2542, 2558, 2559, 2560, 2561, 2566, 2567, 2570, 2595, 2613, 2621, 2636, 2637, 2643, 2644, 2653, 2657, 2663, 2668, 2682, 2683, 2684, 2687, 2688, 2699, 2705, 2706, 2716, 2722, 2742, 2743, 2744, 2745, 2746, 2747, 2749, 2750, 2755, 2760, 2764, 2767, 2777, 2778, 2779, 2780, 2783, 2830, 2834, 2838, 2855, 2856, 2867, 2874, 2897, 2899, 2902, 2905, 2909, 2915, 2916, 2929, 2936, 2944, 2951, 2954, 2955, 2956, 2969, 2979, 2986, 2990, 2991, 2996, 3002, 3003, 3005, 3013, 3014, 3017, 3028, 3031, 3034, 3039, 3045, 3048, 3050, 3058, 3068, 3073, 3074, 3076, 3097, 3124, 3126, 3127, 3128, 3129, 3132, 3133, 3136, 3173, 3175, 3179, 3180, 3183, 3184, 3185, 3186, 3189, 3193, 3196, 3225, 3245, 3256, 3257, 3259, 3260, 3274, 3289, 3290, 3293, 3300, 3301, 3306, 3307, 3317, 3318, 3324, 3325, 3326, 3329, 3362, 3363, 3364, 3365, 3366, 3367, 3369, 3385, 3386, 3387, 3388, 3389, 3390, 3398, 3399, 3400, 3405, 3406, 3413, 3437, 3455, 3465, 3481, 3482, 3491, 3492, 3493, 3499, 3500, 3527, 3542, 3546, 3547, 3550, 3551, 3552, 3553, 3558, 3565, 3566, 3569, 3570, 3574, 3579, 3582, 3583, 3584, 3590, 3600, 3601, 3602, 3606, 3611, 3624, 3670, 3671, 3672, 3673, 3678, 3679, 3680, 3697, 3698, 3705, 3710, 3711, 3712, 3725, 3726, 3741, 3742, 3743, 3744, 3745, 3754, 3755, 3756, 3766, 3767, 3768, 3774, 3778, 3779, 3780, 3784, 3798, 3801, 3803, 3814, 3815, 3816, 3818, 3829, 3830, 3831, 3833, 3842, 3843, 3844, 3845, 3866, 3880, 3881, 3882, 3892, 3896, 3904, 3921, 3924, 3935, 3937, 3939, 3955, 3956, 3957, 3958, 3983, 3990, 4002, 4017, 4024, 4025, 4026, 4027, 4031, 4042, 4044, 4057, 4058, 4061, 4080, 4084, 4090, 4093, 4117, 4134, 4135, 4143, 4148, 4149, 4156, 4157, 4165, 4171, 4175, 4186, 4196, 4197, 4198, 4218, 4225, 4226, 4227, 4241, 4245, 4255, 4261, 4273, 4280, 4285, 4286, 4302, 4303, 4304, 4311, 4318, 4322, 4330, 4343, 4346, 4354, 4374, 4375, 4406, 4410, 4411, 4418, 4457, 4458, 4466, 4467, 4476, 4477, 4480, 4486, 4495, 4505, 4510, 4511, 4512, 4520, 4537, 4543, 4544, 4564, 4566, 4572, 4580, 4587, 4591, 4594, 4607, 4624, 4627, 4638, 4641, 4649, 4660, 4664, 4666, 4667, 4668, 4669, 4677, 4678, 4689, 4690, 4691, 4692, 4706, 4707, 4708, 4709, 4720, 4721, 4722, 4723, 4730, 4738, 4739, 4740, 4748, 4787, 4800, 4811, 4812, 4813, 4821, 4823, 4824, 4825, 4829, 4830, 4835, 4836, 4848, 4851, 4852, 4860, 4868, 4876, 4886, 4887, 4888, 4897, 4912, 4918, 4921, 4923, 4933, 4945, 4947, 4952, 4954, 4959, 4961, 4966, 4968, 4971, 4977, 4984, 4993, 4999, 5001, 5010, 5014, 5019, 5030, 5031, 5032, 5033, 5043, 5061, 5066, 5074, 5091, 5095, 5097, 5098, 5099, 5100, 5114, 5115, 5116, 5117, 5127, 5128, 5129, 5139, 5151, 5152, 5153, 5154, 5161, 5165, 5166, 5168, 5175, 5181, 5278, 5285, 5296, 5299, 5302, 5305, 5442, 5446, 5448, 5452, 5474, 5475, 5481, 5510, 5511, 5512, 5518, 5522, 5523, 5524, 5525, 5571, 5572, 5573, 5574, 5585, 5590, 5601, 5605, 5606, 5625, 5634, 5645, 5649, 5665, 5666, 5672, 5673, 5675, 5686, 5690, 5691, 5692, 5695, 5704, 5716, 5728, 5733, 5737, 5754, 5757, 5768, 5780, 5788, 5819, 5830, 5834, 5835, 5836, 5852, 5860, 5864, 5867, 5884, 5894, 5899, 5909, 5946, 5959, 5966, 5987, 6010, 6011, 6014, 6036, 6046, 6056, 6060, 6061, 6062, 6067, 6110, 6113, 6116, 6124, 6130, 6141, 6143, 6149, 6150, 6167, 6175, 6183, 6184, 6186, 6187, 6213, 6218, 6223, 6227, 6231, 6237, 6238, 6243, 6244, 6250, 6257, 6259, 6260, 6278, 6282, 6285, 6296, 6300, 6301, 6306, 6310, 6311, 6324, 6327, 6338, 6354, 6368, 6380, 6399, 6411, 6418, 6420, 6425, 6441, 6461, 6462, 6465, 6486, 6502, 6509, 6510, 6511, 6520, 6521, 6522, 6524, 6533, 6547, 6548, 6549, 6550, 6551, 6552, 6554, 6559, 6601, 6612, 6613, 6615, 6621, 6627, 6628, 6630, 6638, 6639, 6648, 6653, 6659, 6670, 6677, 6678, 6681, 6686, 6696, 6700, 6715, 6719, 6720, 6745, 6763, 6775, 6776, 6785, 6790, 6792, 6795, 6798, 6799, 6803, 6806, 6830, 6831, 6832, 6833, 6836, 6843, 6848, 6854, 6860, 6861, 6863], 选择最接近的: 580
    ✅ 目标文件 wm_event_system.cc -> 第579行:     return; | 第580行:   } [目标行] | 第582行:   /* Disable? - Keep for now since its used for window level notifiers. */
  ✅ 找到匹配行: 580
  📝 更新行号: 585 -> 580

处理断点 207/208
  文件: source/blender/windowmanager/intern/wm_event_system.cc
  源行号: 586
  时间戳: 289
    📍 源文件 wm_event_system.cc -> 第586行:       if (note->window == win) {
    ✅ 目标文件 wm_event_system.cc -> 第619行:       if (note->window == win) {
  ✅ 找到匹配行: 619
  📝 更新行号: 586 -> 619

处理断点 208/208
  文件: cmake-build-release/bin/4.3/scripts/addons_core/cycles/properties.py
  源行号: 1622
  时间戳: 27
  ❌ 目标文件不存在: cmake-build-release\bin\4.3\scripts\addons_core\cycles\properties.py

==================================================
同步结果统计
==================================================
总断点数: 208
成功更新: 135
行号未变: 6
未找到匹配: 47
文件不存在: 20
==================================================

📝 详细日志已保存到: breakpoint_sync_log_20250615_003810.txt
