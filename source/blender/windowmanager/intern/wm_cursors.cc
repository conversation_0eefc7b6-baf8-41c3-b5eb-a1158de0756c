/* SPDX-FileCopyrightText: 2005-2007 Blender Authors
 *
 * SPDX-License-Identifier: GPL-2.0-or-later */

/** \file
 * \ingroup wm
 *
 * Cursor pixmap and cursor utility functions to change the cursor.
 */

#include <cstring>

#include "GHOST_C-api.h"

#include "BLI_utildefines.h"

#include "DNA_listBase.h"
#include "DNA_userdef_types.h"
#include "DNA_workspace_types.h"

#include "BKE_global.hh"
#include "BKE_main.hh"

#include "WM_api.hh"
#include "WM_types.hh"
#include "wm_cursors.hh"
#include "wm_window.hh"

enum CursorSize {
  /** Size: 16x16. */
  CURSOR_SIZE_16 = 0,
  /** Size: 24x24. */
  CURSOR_SIZE_24,
  /** Size: 32x32. */
  CURSOR_SIZE_32,
};
#define CURSOR_SIZE_NUM (CURSOR_SIZE_32 + 1)

static const uchar cursor_size_px[CURSOR_SIZE_NUM] = {
    16,
    /** Not a mistake 24x24 is stored as 32x32. */
    32,
    32,
};

/* Blender custom cursor. */
struct BCursor {
  char *bitmap[CURSOR_SIZE_NUM];
  char *mask[CURSOR_SIZE_NUM];
  char hotspot[CURSOR_SIZE_NUM][2];
  bool can_invert_color;
};

static BCursor *BlenderCursor[WM_CURSOR_NUM] = {nullptr};

/* Blender cursor to GHOST standard cursor conversion. */
static GHOST_TStandardCursor convert_to_ghost_standard_cursor(WMCursorType curs)
{
  switch (curs) {
    case WM_CURSOR_DEFAULT:
      return GHOST_kStandardCursorDefault;
    case WM_CURSOR_WAIT:
      return GHOST_kStandardCursorWait;
    case WM_CURSOR_EDIT:
    case WM_CURSOR_CROSS:
      return GHOST_kStandardCursorCrosshair;
    case WM_CURSOR_MOVE:
      return GHOST_kStandardCursorMove;
    case WM_CURSOR_X_MOVE:
      return GHOST_kStandardCursorLeftRight;
    case WM_CURSOR_Y_MOVE:
      return GHOST_kStandardCursorUpDown;
    case WM_CURSOR_COPY:
      return GHOST_kStandardCursorCopy;
    case WM_CURSOR_HAND:
      return GHOST_kStandardCursorHandOpen;
    case WM_CURSOR_HAND_CLOSED:
      return GHOST_kStandardCursorHandClosed;
    case WM_CURSOR_HAND_POINT:
      return GHOST_kStandardCursorHandPoint;
    case WM_CURSOR_H_SPLIT:
      return GHOST_kStandardCursorHorizontalSplit;
    case WM_CURSOR_V_SPLIT:
      return GHOST_kStandardCursorVerticalSplit;
    case WM_CURSOR_STOP:
      return GHOST_kStandardCursorStop;
    case WM_CURSOR_KNIFE:
      return GHOST_kStandardCursorKnife;
    case WM_CURSOR_NSEW_SCROLL:
      return GHOST_kStandardCursorNSEWScroll;
    case WM_CURSOR_NS_SCROLL:
      return GHOST_kStandardCursorNSScroll;
    case WM_CURSOR_EW_SCROLL:
      return GHOST_kStandardCursorEWScroll;
    case WM_CURSOR_EYEDROPPER:
      return GHOST_kStandardCursorEyedropper;
    case WM_CURSOR_N_ARROW:
      return GHOST_kStandardCursorUpArrow;
    case WM_CURSOR_S_ARROW:
      return GHOST_kStandardCursorDownArrow;
    case WM_CURSOR_PAINT:
      return GHOST_kStandardCursorCrosshairA;
    case WM_CURSOR_DOT:
      return GHOST_kStandardCursorCrosshairB;
    case WM_CURSOR_CROSSC:
      return GHOST_kStandardCursorCrosshairC;
    case WM_CURSOR_ERASER:
      return GHOST_kStandardCursorEraser;
    case WM_CURSOR_ZOOM_IN:
      return GHOST_kStandardCursorZoomIn;
    case WM_CURSOR_ZOOM_OUT:
      return GHOST_kStandardCursorZoomOut;
    case WM_CURSOR_TEXT_EDIT:
      return GHOST_kStandardCursorText;
    case WM_CURSOR_PAINT_BRUSH:
      return GHOST_kStandardCursorPencil;
    case WM_CURSOR_E_ARROW:
      return GHOST_kStandardCursorRightArrow;
    case WM_CURSOR_W_ARROW:
      return GHOST_kStandardCursorLeftArrow;
    case WM_CURSOR_LEFT_HANDLE:
      return GHOST_kStandardCursorLeftHandle;
    case WM_CURSOR_RIGHT_HANDLE:
      return GHOST_kStandardCursorRightHandle;
    case WM_CURSOR_BOTH_HANDLES:
      return GHOST_kStandardCursorBothHandles;
    case WM_CURSOR_BLADE:
      return GHOST_kStandardCursorBlade;
    default:
      return GHOST_kStandardCursorCustom;
  }
}

static CursorSize window_size_calc()
{
#if (OS_MAC)
  /* MacOS always scales up this type of cursor for high-dpi displays.
   * The mid-sized 24x24 versions are a nice compromise size. */
  return CURSOR_SIZE_24;
#endif

  /* Use `U.dpi` without the `U.ui_scale` because the UI scale does not impact the
   * windowing-systems cursor size (only the size which is used for drawing the UI).
   * The DPI however is used for scaling defined by the windowing-system.
   * Ideally this would also be able to check the cursor size via GHOST. */
  const int dpi_system = int(U.dpi / U.ui_scale);

  if (dpi_system <= 72) {
    return CURSOR_SIZE_16;
  }
  if (dpi_system <= int(72 * 1.2)) {
    return CURSOR_SIZE_24;
  }
  return CURSOR_SIZE_32;
}

static void window_set_custom_cursor(wmWindow *win, BCursor *cursor)
{
  const CursorSize size = window_size_calc();

  GHOST_SetCustomCursorShape(static_cast<GHOST_WindowHandle>(win->ghostwin),
                             (uint8_t *)cursor->bitmap[size],
                             (uint8_t *)cursor->mask[size],
                             cursor_size_px[size],
                             cursor_size_px[size],
                             cursor->hotspot[size][0],
                             cursor->hotspot[size][1],
                             cursor->can_invert_color);
}

void WM_cursor_set(wmWindow *win, int curs)
{
  if (win == nullptr || G.background) {
    return; /* Can't set custom cursor before Window init. */
  }

  if (curs == WM_CURSOR_DEFAULT && win->modalcursor) {
    curs = win->modalcursor;
  }

  if (curs == WM_CURSOR_NONE) {
    GHOST_SetCursorVisibility(static_cast<GHOST_WindowHandle>(win->ghostwin), false);
    return;
  }

  GHOST_SetCursorVisibility(static_cast<GHOST_WindowHandle>(win->ghostwin), true);

  if (win->cursor == curs) {
    return; /* Cursor is already set. */
  }

  win->cursor = curs;

  if (curs < 0 || curs >= WM_CURSOR_NUM) {
    BLI_assert_msg(0, "Invalid cursor number");
    return;
  }

  GHOST_TStandardCursor ghost_cursor = convert_to_ghost_standard_cursor(WMCursorType(curs));

  if (ghost_cursor != GHOST_kStandardCursorCustom &&
      GHOST_HasCursorShape(static_cast<GHOST_WindowHandle>(win->ghostwin), ghost_cursor))
  {
    /* Use native GHOST cursor when available. */
    GHOST_SetCursorShape(static_cast<GHOST_WindowHandle>(win->ghostwin), ghost_cursor);
  }
  else {
    BCursor *bcursor = BlenderCursor[curs];
    if (bcursor) {
      /* Use custom bitmap cursor. */
      window_set_custom_cursor(win, bcursor);
    }
    else {
      /* Fall back to default cursor if no bitmap found. */
      GHOST_SetCursorShape(static_cast<GHOST_WindowHandle>(win->ghostwin),
                           GHOST_kStandardCursorDefault);
    }
  }
}

bool WM_cursor_set_from_tool(wmWindow *win, const ScrArea *area, const ARegion *region)
{
  if (region && !ELEM(region->regiontype, RGN_TYPE_WINDOW, RGN_TYPE_PREVIEW)) {
    return false;
  }

  bToolRef_Runtime *tref_rt = (area && area->runtime.tool) ? area->runtime.tool->runtime : nullptr;
  if (tref_rt && tref_rt->cursor != WM_CURSOR_DEFAULT) {
    if (win->modalcursor == 0) {
      WM_cursor_set(win, tref_rt->cursor);
      win->cursor = tref_rt->cursor;
      return true;
    }
  }
  return false;
}

void WM_cursor_modal_set(wmWindow *win, int val)
{
  if (win->lastcursor == 0) {
    win->lastcursor = win->cursor;
  }
  win->modalcursor = val;
  WM_cursor_set(win, val);
}

void WM_cursor_modal_restore(wmWindow *win)
{
  win->modalcursor = 0;
  if (win->lastcursor) {
    WM_cursor_set(win, win->lastcursor);
  }
  win->lastcursor = 0;
}

void WM_cursor_wait(bool val)
{
  if (!G.background) {
    wmWindowManager *wm = static_cast<wmWindowManager *>(G_MAIN->wm.first);
    wmWindow *win = static_cast<wmWindow *>(wm ? wm->windows.first : nullptr);

    for (; win; win = win->next) {
      if (val) {
        WM_cursor_modal_set(win, WM_CURSOR_WAIT);
      }
      else {
        WM_cursor_modal_restore(win);
      }
    }
  }
}

void WM_cursor_grab_enable(wmWindow *win,
                           const eWM_CursorWrapAxis wrap,
                           const rcti *wrap_region,
                           const bool hide)
{
  int _wrap_region_buf[4];
  int *wrap_region_screen = nullptr;

  /* Only grab cursor when not running debug.
   * It helps not to get a stuck WM when hitting a break-point. */
  GHOST_TGrabCursorMode mode = GHOST_kGrabNormal;
  GHOST_TAxisFlag mode_axis = GHOST_TAxisFlag(GHOST_kAxisX | GHOST_kAxisY);

  if (wrap_region) {
    wrap_region_screen = _wrap_region_buf;
    wrap_region_screen[0] = wrap_region->xmin;
    wrap_region_screen[1] = wrap_region->ymax;
    wrap_region_screen[2] = wrap_region->xmax;
    wrap_region_screen[3] = wrap_region->ymin;
    wm_cursor_position_to_ghost_screen_coords(win, &wrap_region_screen[0], &wrap_region_screen[1]);
    wm_cursor_position_to_ghost_screen_coords(win, &wrap_region_screen[2], &wrap_region_screen[3]);
  }

  if (hide) {
    mode = GHOST_kGrabHide;
  }
  else if (wrap != WM_CURSOR_WRAP_NONE) {
    mode = GHOST_kGrabWrap;

    if (wrap == WM_CURSOR_WRAP_X) {
      mode_axis = GHOST_kAxisX;
    }
    else if (wrap == WM_CURSOR_WRAP_Y) {
      mode_axis = GHOST_kAxisY;
    }
  }

  if ((G.debug & G_DEBUG) == 0) {
    if (win->ghostwin) {
      if (win->eventstate->tablet.is_motion_absolute == false) {
        GHOST_SetCursorGrab(static_cast<GHOST_WindowHandle>(win->ghostwin),
                            mode,
                            mode_axis,
                            wrap_region_screen,
                            nullptr);
      }

      win->grabcursor = mode;
    }
  }
}

void WM_cursor_grab_disable(wmWindow *win, const int mouse_ungrab_xy[2])
{
  if ((G.debug & G_DEBUG) == 0) {
    if (win && win->ghostwin) {
      if (mouse_ungrab_xy) {
        int mouse_xy[2] = {mouse_ungrab_xy[0], mouse_ungrab_xy[1]};
        wm_cursor_position_to_ghost_screen_coords(win, &mouse_xy[0], &mouse_xy[1]);
        GHOST_SetCursorGrab(static_cast<GHOST_WindowHandle>(win->ghostwin),
                            GHOST_kGrabDisable,
                            GHOST_kAxisNone,
                            nullptr,
                            mouse_xy);
      }
      else {
        GHOST_SetCursorGrab(static_cast<GHOST_WindowHandle>(win->ghostwin),
                            GHOST_kGrabDisable,
                            GHOST_kAxisNone,
                            nullptr,
                            nullptr);
      }

      win->grabcursor = GHOST_kGrabDisable;
    }
  }
}

static void wm_cursor_warp_relative(wmWindow *win, int x, int y)
{
  /* NOTE: don't use wmEvent coords because of continuous grab #36409. */
  int cx, cy;
  if (wm_cursor_position_get(win, &cx, &cy)) {
    WM_cursor_warp(win, cx + x, cy + y);
  }
}

bool wm_cursor_arrow_move(wmWindow *win, const wmEvent *event)
{
  /* TODO: give it a modal keymap? Hard coded for now. */

  if (win && event->val == KM_PRESS) {
    /* Must move at least this much to avoid rounding in WM_cursor_warp. */
    float fac = GHOST_GetNativePixelSize(static_cast<GHOST_WindowHandle>(win->ghostwin));

    if (event->type == EVT_UPARROWKEY) {
      wm_cursor_warp_relative(win, 0, fac);
      return true;
    }
    if (event->type == EVT_DOWNARROWKEY) {
      wm_cursor_warp_relative(win, 0, -fac);
      return true;
    }
    if (event->type == EVT_LEFTARROWKEY) {
      wm_cursor_warp_relative(win, -fac, 0);
      return true;
    }
    if (event->type == EVT_RIGHTARROWKEY) {
      wm_cursor_warp_relative(win, fac, 0);
      return true;
    }
  }
  return false;
}

static bool wm_cursor_time_large(wmWindow *win, int nr)
{
  /* 10 16x16 digits. */
  const uchar number_bitmaps[][32] = {
      {0x00, 0x00, 0xf0, 0x0f, 0xf8, 0x1f, 0x1c, 0x38, 0x0c, 0x30, 0x0c,
       0x30, 0x0c, 0x30, 0x0c, 0x30, 0x0c, 0x30, 0x0c, 0x30, 0x0c, 0x30,
       0x0c, 0x30, 0x1c, 0x38, 0xf8, 0x1f, 0xf0, 0x0f, 0x00, 0x00},
      {0x00, 0x00, 0x80, 0x01, 0xc0, 0x01, 0xf0, 0x01, 0xbc, 0x01, 0x8c,
       0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01,
       0x80, 0x01, 0x80, 0x01, 0xfc, 0x3f, 0xfc, 0x3f, 0x00, 0x00},
      {0x00, 0x00, 0xf0, 0x1f, 0xf8, 0x3f, 0x1c, 0x30, 0x0c, 0x30, 0x00,
       0x30, 0x00, 0x30, 0xe0, 0x3f, 0xf0, 0x1f, 0x38, 0x00, 0x1c, 0x00,
       0x0c, 0x00, 0x0c, 0x00, 0xfc, 0x3f, 0xfc, 0x3f, 0x00, 0x00},
      {0x00, 0x00, 0xf0, 0x0f, 0xf8, 0x1f, 0x1c, 0x38, 0x00, 0x30, 0x00,
       0x30, 0x00, 0x38, 0xf0, 0x1f, 0xf0, 0x1f, 0x00, 0x38, 0x00, 0x30,
       0x00, 0x30, 0x1c, 0x38, 0xf8, 0x1f, 0xf0, 0x0f, 0x00, 0x00},
      {0x00, 0x00, 0x00, 0x0f, 0x80, 0x0f, 0xc0, 0x0d, 0xe0, 0x0c, 0x70,
       0x0c, 0x38, 0x0c, 0x1c, 0x0c, 0x0c, 0x0c, 0x0c, 0x0c, 0x0c, 0x0c,
       0xfc, 0x3f, 0xfc, 0x3f, 0x00, 0x0c, 0x00, 0x0c, 0x00, 0x00},
      {0x00, 0x00, 0xfc, 0x3f, 0xfc, 0x3f, 0x0c, 0x00, 0x0c, 0x00, 0x0c,
       0x00, 0xfc, 0x0f, 0xfc, 0x1f, 0x00, 0x38, 0x00, 0x30, 0x00, 0x30,
       0x00, 0x30, 0x0c, 0x38, 0xfc, 0x1f, 0xf8, 0x0f, 0x00, 0x00},
      {0x00, 0x00, 0xc0, 0x3f, 0xe0, 0x3f, 0x70, 0x00, 0x38, 0x00, 0x1c,
       0x00, 0xfc, 0x0f, 0xfc, 0x1f, 0x0c, 0x38, 0x0c, 0x30, 0x0c, 0x30,
       0x0c, 0x30, 0x1c, 0x38, 0xf8, 0x1f, 0xf0, 0x0f, 0x00, 0x00},
      {0x00, 0x00, 0xfc, 0x3f, 0xfc, 0x3f, 0x0c, 0x30, 0x0c, 0x38, 0x00,
       0x18, 0x00, 0x1c, 0x00, 0x0c, 0x00, 0x0e, 0x00, 0x06, 0x00, 0x07,
       0x00, 0x03, 0x80, 0x03, 0x80, 0x01, 0x80, 0x01, 0x00, 0x00},
      {0x00, 0x00, 0xf0, 0x0f, 0xf8, 0x1f, 0x1c, 0x38, 0x0c, 0x30, 0x0c,
       0x30, 0x1c, 0x38, 0xf8, 0x1f, 0xf8, 0x1f, 0x1c, 0x38, 0x0c, 0x30,
       0x0c, 0x30, 0x1c, 0x38, 0xf8, 0x1f, 0xf0, 0x0f, 0x00, 0x00},
      {0x00, 0x00, 0xf0, 0x0f, 0xf8, 0x1f, 0x1c, 0x38, 0x0c, 0x30, 0x0c,
       0x30, 0x0c, 0x30, 0x1c, 0x30, 0xf8, 0x3f, 0xf0, 0x3f, 0x00, 0x38,
       0x00, 0x1c, 0x00, 0x0e, 0xfc, 0x07, 0xfc, 0x03, 0x00, 0x00},
  };
  uchar mask[32][4] = {{0}};
  uchar bitmap[32][4] = {{0}};

  /* Print number bottom right justified. */
  for (int idx = 3; nr && idx >= 0; idx--) {
    const uchar *digit = number_bitmaps[nr % 10];
    int x = idx % 2;
    int y = idx / 2;

    for (int i = 0; i < 16; i++) {
      bitmap[i + y * 16][x * 2] = digit[i * 2];
      bitmap[i + y * 16][(x * 2) + 1] = digit[(i * 2) + 1];
    }
    for (int i = 0; i < 16; i++) {
      mask[i + y * 16][x * 2] = 0xFF;
      mask[i + y * 16][(x * 2) + 1] = 0xFF;
    }

    nr /= 10;
  }

  return GHOST_SetCustomCursorShape(static_cast<GHOST_WindowHandle>(win->ghostwin),
                                    (uint8_t *)bitmap,
                                    (uint8_t *)mask,
                                    32,
                                    32,
                                    15,
                                    15,
                                    false) == GHOST_kSuccess;
}

static void wm_cursor_time_small(wmWindow *win, int nr)
{
  /* 10 8x8 digits. */
  const char number_bitmaps[10][8] = {
      {0, 56, 68, 68, 68, 68, 68, 56},
      {0, 24, 16, 16, 16, 16, 16, 56},
      {0, 60, 66, 32, 16, 8, 4, 126},
      {0, 124, 32, 16, 56, 64, 66, 60},
      {0, 32, 48, 40, 36, 126, 32, 32},
      {0, 124, 4, 60, 64, 64, 68, 56},
      {0, 56, 4, 4, 60, 68, 68, 56},
      {0, 124, 64, 32, 16, 8, 8, 8},
      {0, 60, 66, 66, 60, 66, 66, 60},
      {0, 56, 68, 68, 120, 64, 68, 56},
  };
  uchar mask[16][2] = {{0}};
  uchar bitmap[16][2] = {{0}};

  /* Print number bottom right justified. */
  for (int idx = 3; nr && idx >= 0; idx--) {
    const char *digit = number_bitmaps[nr % 10];
    int x = idx % 2;
    int y = idx / 2;

    for (int i = 0; i < 8; i++) {
      bitmap[i + y * 8][x] = digit[i];
    }
    for (int i = 0; i < 8; i++) {
      mask[i + y * 8][x] = 0xFF;
    }
    nr /= 10;
  }

  GHOST_SetCustomCursorShape(static_cast<GHOST_WindowHandle>(win->ghostwin),
                             (uint8_t *)bitmap,
                             (uint8_t *)mask,
                             16,
                             16,
                             7,
                             7,
                             false);
}

void WM_cursor_time(wmWindow *win, int nr)
{
  if (win->lastcursor == 0) {
    win->lastcursor = win->cursor;
  }

  /* Use `U.ui_scale` instead of `UI_SCALE_FAC` here to ignore HiDPI/Retina scaling. */
  if (U.ui_scale < 1.45f || !wm_cursor_time_large(win, nr)) {
    wm_cursor_time_small(win, nr);
  }

  /* Unset current cursor value so it's properly reset to wmWindow.lastcursor. */
  win->cursor = 0;
}

/**
 * Custom Cursor Description
 * =========================
 *
 * Each bit represents a pixel, so 1 byte = 8 pixels,
 * the bytes go Left to Right. Top to bottom
 * the bits in a byte go right to left
 * (ie;  0x01, 0x80  represents a line of 16 pix with the first and last pix set.)
 *
 * - A 0 in the bitmap = white, a 1 black
 * - a 0 in the mask   = transparent pix.
 *
 * This type of cursor is 16x16 pixels only.
 *
 * ----
 *
 * There is a nice Python GUI utility that can be used for drawing cursors in
 * this format in the Blender source distribution, in
 * `./tools/utils/make_cursor_gui.py` .
 *
 * Start it with the command `python3 make_cursor_gui.py`
 * It will copy its output to the console when you press 'Do it'.
 */

/**
 * Because defining a cursor mixes declarations and executable code
 * each cursor needs its own scoping block or it would be split up
 * over several hundred lines of code. To enforce/document this better
 * I define 2 pretty brain-dead macros so it's obvious what the extra "[]" are for.
 */

#define BEGIN_CURSOR_BLOCK \
  { \
    ((void)0)
#define END_CURSOR_BLOCK \
  } \
  ((void)0)

void wm_init_cursor_data()
{
  /********************** NW_ARROW Cursor **************************/
  BEGIN_CURSOR_BLOCK;

  static char nw_bitmap16[] = {
      0x00, 0x00, 0x02, 0x00, 0x06, 0x00, 0x0e, 0x00, 0x1e, 0x00, 0x3e,
      0x00, 0x7e, 0x00, 0xfe, 0x00, 0xfe, 0x01, 0xfe, 0x03, 0xfe, 0x07,
      0x7e, 0x00, 0x6e, 0x00, 0xc6, 0x00, 0xc2, 0x00, 0x00, 0x00,
  };

  static char nw_mask16[] = {
      0x03, 0x00, 0x07, 0x00, 0x0f, 0x00, 0x1f, 0x00, 0x3f, 0x00, 0x7f,
      0x00, 0xff, 0x00, 0xff, 0x01, 0xff, 0x03, 0xff, 0x07, 0xff, 0x0f,
      0xff, 0x0f, 0xff, 0x00, 0xef, 0x01, 0xe7, 0x01, 0xc3, 0x00,
  };

  static char nw_bitmap24[] = {
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00,
      0x00, 0x0e, 0x00, 0x00, 0x00, 0x1e, 0x00, 0x00, 0x00, 0x3e, 0x00, 0x00, 0x00, 0x7e, 0x00,
      0x00, 0x00, 0xfe, 0x00, 0x00, 0x00, 0xfe, 0x01, 0x00, 0x00, 0xfe, 0x03, 0x00, 0x00, 0xfe,
      0x07, 0x00, 0x00, 0xfe, 0x0f, 0x00, 0x00, 0xfe, 0x1f, 0x00, 0x00, 0xfe, 0x3f, 0x00, 0x00,
      0xfe, 0x00, 0x00, 0x00, 0xfe, 0x00, 0x00, 0x00, 0xde, 0x01, 0x00, 0x00, 0x8e, 0x01, 0x00,
      0x00, 0x86, 0x03, 0x00, 0x00, 0x02, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static char nw_mask24[] = {
      0x01, 0x00, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x07, 0x00, 0x00, 0x00, 0x0f, 0x00, 0x00,
      0x00, 0x1f, 0x00, 0x00, 0x00, 0x3f, 0x00, 0x00, 0x00, 0x7f, 0x00, 0x00, 0x00, 0xff, 0x00,
      0x00, 0x00, 0xff, 0x01, 0x00, 0x00, 0xff, 0x03, 0x00, 0x00, 0xff, 0x07, 0x00, 0x00, 0xff,
      0x0f, 0x00, 0x00, 0xff, 0x1f, 0x00, 0x00, 0xff, 0x3f, 0x00, 0x00, 0xff, 0x7f, 0x00, 0x00,
      0xff, 0x7f, 0x00, 0x00, 0xff, 0x01, 0x00, 0x00, 0xff, 0x03, 0x00, 0x00, 0xdf, 0x03, 0x00,
      0x00, 0xcf, 0x07, 0x00, 0x00, 0x87, 0x07, 0x00, 0x00, 0x83, 0x07, 0x00, 0x00, 0x01, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static char nw_bitmap32[] = {
      0x00, 0x00, 0x00, 0x00, 0x02, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x0e, 0x00, 0x00,
      0x00, 0x1e, 0x00, 0x00, 0x00, 0x3e, 0x00, 0x00, 0x00, 0x7e, 0x00, 0x00, 0x00, 0xfe, 0x00,
      0x00, 0x00, 0xfe, 0x01, 0x00, 0x00, 0xfe, 0x03, 0x00, 0x00, 0xfe, 0x07, 0x00, 0x00, 0xfe,
      0x0f, 0x00, 0x00, 0xfe, 0x1f, 0x00, 0x00, 0xfe, 0x3f, 0x00, 0x00, 0xfe, 0x7f, 0x00, 0x00,
      0xfe, 0xff, 0x00, 0x00, 0xfe, 0xff, 0x01, 0x00, 0xfe, 0xff, 0x03, 0x00, 0xfe, 0xff, 0x07,
      0x00, 0xfe, 0xff, 0x0f, 0x00, 0xfe, 0xff, 0x1f, 0x00, 0xfe, 0x0f, 0x00, 0x00, 0xfe, 0x1f,
      0x00, 0x00, 0xfe, 0x1f, 0x00, 0x00, 0xfe, 0x3e, 0x00, 0x00, 0x7e, 0x3e, 0x00, 0x00, 0x3e,
      0x7c, 0x00, 0x00, 0x1e, 0x7c, 0x00, 0x00, 0x0e, 0xf8, 0x00, 0x00, 0x06, 0x78, 0x00, 0x00,
      0x02, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static char nw_mask32[] = {
      0x03, 0x00, 0x00, 0x00, 0x07, 0x00, 0x00, 0x00, 0x0f, 0x00, 0x00, 0x00, 0x1f, 0x00, 0x00,
      0x00, 0x3f, 0x00, 0x00, 0x00, 0x7f, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x01,
      0x00, 0x00, 0xff, 0x03, 0x00, 0x00, 0xff, 0x07, 0x00, 0x00, 0xff, 0x0f, 0x00, 0x00, 0xff,
      0x1f, 0x00, 0x00, 0xff, 0x3f, 0x00, 0x00, 0xff, 0x7f, 0x00, 0x00, 0xff, 0xff, 0x00, 0x00,
      0xff, 0xff, 0x01, 0x00, 0xff, 0xff, 0x03, 0x00, 0xff, 0xff, 0x07, 0x00, 0xff, 0xff, 0x0f,
      0x00, 0xff, 0xff, 0x1f, 0x00, 0xff, 0xff, 0x3f, 0x00, 0xff, 0xff, 0x7f, 0x00, 0xff, 0x3f,
      0x00, 0x00, 0xff, 0x3f, 0x00, 0x00, 0xff, 0x7f, 0x00, 0x00, 0xff, 0x7f, 0x00, 0x00, 0x7f,
      0xfe, 0x00, 0x00, 0x3f, 0xfe, 0x00, 0x00, 0x1f, 0xfc, 0x00, 0x00, 0x0f, 0xfc, 0x00, 0x00,
      0x07, 0x78, 0x00, 0x00, 0x03, 0x18, 0x00, 0x00,
  };

  static BCursor NWArrowCursor = {
      {nw_bitmap16, nw_bitmap24, nw_bitmap32},
      {nw_mask16, nw_mask24, nw_mask32},
      {{0, 0}, {0, 0}, {0, 0}},
      true,
  };

  BlenderCursor[WM_CURSOR_DEFAULT] = &NWArrowCursor;
  BlenderCursor[WM_CURSOR_MOVE] = &NWArrowCursor;
  BlenderCursor[WM_CURSOR_COPY] = &NWArrowCursor;
  BlenderCursor[WM_CURSOR_NW_ARROW] = &NWArrowCursor;
  END_CURSOR_BLOCK;

  /************************ NS_ARROW Cursor *************************/
  BEGIN_CURSOR_BLOCK;

  static char ns_bitmap16[] = {
      0x00, 0x00, 0x80, 0x00, 0xc0, 0x01, 0xe0, 0x03, 0xf0, 0x07, 0x80,
      0x00, 0x80, 0x00, 0x80, 0x00, 0x80, 0x00, 0x80, 0x00, 0x80, 0x00,
      0xf0, 0x07, 0xe0, 0x03, 0xc0, 0x01, 0x80, 0x00, 0x00, 0x00,
  };

  static char ns_mask16[] = {
      0x80, 0x00, 0xc0, 0x01, 0xe0, 0x03, 0xf0, 0x07, 0xf8, 0x0f, 0xfc,
      0x1f, 0xc0, 0x01, 0xc0, 0x01, 0xc0, 0x01, 0xc0, 0x01, 0xfc, 0x1f,
      0xf8, 0x0f, 0xf0, 0x07, 0xe0, 0x03, 0xc0, 0x01, 0x80, 0x00,
  };

  static char ns_bitmap24[] = {
      0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x00, 0x00, 0x00, 0xe0, 0x00, 0x00, 0x00, 0xf0, 0x01,
      0x00, 0x00, 0xf8, 0x03, 0x00, 0x00, 0xfc, 0x07, 0x00, 0x00, 0x40, 0x00, 0x00, 0x00, 0x40,
      0x00, 0x00, 0x00, 0x40, 0x00, 0x00, 0x00, 0x40, 0x00, 0x00, 0x00, 0x40, 0x00, 0x00, 0x00,
      0x40, 0x00, 0x00, 0x00, 0x40, 0x00, 0x00, 0x00, 0x40, 0x00, 0x00, 0x00, 0x40, 0x00, 0x00,
      0x00, 0x40, 0x00, 0x00, 0x00, 0x40, 0x00, 0x00, 0x00, 0x40, 0x00, 0x00, 0x00, 0xfc, 0x07,
      0x00, 0x00, 0xf8, 0x03, 0x00, 0x00, 0xf0, 0x01, 0x00, 0x00, 0xe0, 0x00, 0x00, 0x00, 0x40,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static char ns_mask24[] = {
      0x00, 0x40, 0x00, 0x00, 0x00, 0xe0, 0x00, 0x00, 0x00, 0xf0, 0x01, 0x00, 0x00, 0xf8, 0x03,
      0x00, 0x00, 0xfc, 0x07, 0x00, 0x00, 0xfe, 0x0f, 0x00, 0x00, 0xff, 0x1f, 0x00, 0x00, 0xe0,
      0x00, 0x00, 0x00, 0xe0, 0x00, 0x00, 0x00, 0xe0, 0x00, 0x00, 0x00, 0xe0, 0x00, 0x00, 0x00,
      0xe0, 0x00, 0x00, 0x00, 0xe0, 0x00, 0x00, 0x00, 0xe0, 0x00, 0x00, 0x00, 0xe0, 0x00, 0x00,
      0x00, 0xe0, 0x00, 0x00, 0x00, 0xe0, 0x00, 0x00, 0x00, 0xff, 0x1f, 0x00, 0x00, 0xfe, 0x0f,
      0x00, 0x00, 0xfc, 0x07, 0x00, 0x00, 0xf8, 0x03, 0x00, 0x00, 0xf0, 0x01, 0x00, 0x00, 0xe0,
      0x00, 0x00, 0x00, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static char ns_bitmap32[] = {
      0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 0xe0, 0x01, 0x00, 0x00, 0xf0, 0x03,
      0x00, 0x00, 0xf8, 0x07, 0x00, 0x00, 0xfc, 0x0f, 0x00, 0x00, 0xfe, 0x1f, 0x00, 0x00, 0xff,
      0x3f, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x00,
      0xc0, 0x00, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 0xc0, 0x00, 0x00,
      0x00, 0xc0, 0x00, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 0xc0, 0x00,
      0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 0xc0,
      0x00, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 0xff, 0x3f, 0x00, 0x00, 0xfe, 0x1f, 0x00, 0x00,
      0xfc, 0x0f, 0x00, 0x00, 0xf8, 0x07, 0x00, 0x00, 0xf0, 0x03, 0x00, 0x00, 0xe0, 0x01, 0x00,
      0x00, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static char ns_mask32[] = {
      0x00, 0xc0, 0x00, 0x00, 0x00, 0xe0, 0x01, 0x00, 0x00, 0xf0, 0x03, 0x00, 0x00, 0xf8, 0x07,
      0x00, 0x00, 0xfc, 0x0f, 0x00, 0x00, 0xfe, 0x1f, 0x00, 0x00, 0xff, 0x3f, 0x00, 0x80, 0xff,
      0x7f, 0x00, 0xc0, 0xff, 0xff, 0x00, 0x00, 0xe0, 0x01, 0x00, 0x00, 0xe0, 0x01, 0x00, 0x00,
      0xe0, 0x01, 0x00, 0x00, 0xe0, 0x01, 0x00, 0x00, 0xe0, 0x01, 0x00, 0x00, 0xe0, 0x01, 0x00,
      0x00, 0xe0, 0x01, 0x00, 0x00, 0xe0, 0x01, 0x00, 0x00, 0xe0, 0x01, 0x00, 0x00, 0xe0, 0x01,
      0x00, 0x00, 0xe0, 0x01, 0x00, 0x00, 0xe0, 0x01, 0x00, 0x00, 0xe0, 0x01, 0x00, 0x00, 0xe0,
      0x01, 0x00, 0xc0, 0xff, 0xff, 0x00, 0x80, 0xff, 0x7f, 0x00, 0x00, 0xff, 0x3f, 0x00, 0x00,
      0xfe, 0x1f, 0x00, 0x00, 0xfc, 0x0f, 0x00, 0x00, 0xf8, 0x07, 0x00, 0x00, 0xf0, 0x03, 0x00,
      0x00, 0xe0, 0x01, 0x00, 0x00, 0xc0, 0x00, 0x00,
  };

  static BCursor NSArrowCursor = {
      {ns_bitmap16, ns_bitmap24, ns_bitmap32},
      {ns_mask16, ns_mask24, ns_mask32},
      {{7, 7}, {15, 11}, {15, 15}},
      true,
  };

  BlenderCursor[WM_CURSOR_Y_MOVE] = &NSArrowCursor;
  BlenderCursor[WM_CURSOR_NS_ARROW] = &NSArrowCursor;
  END_CURSOR_BLOCK;

  /********************** EW_ARROW Cursor *************************/
  BEGIN_CURSOR_BLOCK;

  static char ew_bitmap16[] = {
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 0x08, 0x18,
      0x18, 0x1c, 0x38, 0xfe, 0x7f, 0x1c, 0x38, 0x18, 0x18, 0x10, 0x08,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static char ew_mask16[] = {
      0x00, 0x00, 0x00, 0x00, 0x20, 0x04, 0x30, 0x0c, 0x38, 0x1c, 0x3c,
      0x3c, 0xfe, 0x7f, 0xff, 0xff, 0xfe, 0x7f, 0x3c, 0x3c, 0x38, 0x1c,
      0x30, 0x0c, 0x20, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static char ew_bitmap24[] = {
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0x00, 0x04, 0x00, 0x30,
      0x00, 0x0c, 0x00, 0x38, 0x00, 0x1c, 0x00, 0x3c, 0x00, 0x3c, 0x00, 0xfe, 0xff, 0x7f, 0x00,
      0x3c, 0x00, 0x3c, 0x00, 0x38, 0x00, 0x1c, 0x00, 0x30, 0x00, 0x0c, 0x00, 0x20, 0x00, 0x04,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static char ew_mask24[] = {
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x40, 0x00, 0x02, 0x00, 0x60, 0x00, 0x06, 0x00, 0x70, 0x00, 0x0e, 0x00, 0x78,
      0x00, 0x1e, 0x00, 0x7c, 0x00, 0x3e, 0x00, 0xfe, 0xff, 0x7f, 0x00, 0xff, 0xff, 0xff, 0x00,
      0xfe, 0xff, 0x7f, 0x00, 0x7c, 0x00, 0x3e, 0x00, 0x78, 0x00, 0x1e, 0x00, 0x70, 0x00, 0x0e,
      0x00, 0x60, 0x00, 0x06, 0x00, 0x40, 0x00, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static char ew_bitmap32[] = {
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x80, 0x00, 0x00, 0x01, 0xc0, 0x00, 0x00, 0x03, 0xe0, 0x00, 0x00, 0x07, 0xf0,
      0x00, 0x00, 0x0f, 0xf8, 0x00, 0x00, 0x1f, 0xfc, 0x00, 0x00, 0x3f, 0xfe, 0xff, 0xff, 0x7f,
      0xfe, 0xff, 0xff, 0x7f, 0xfc, 0x00, 0x00, 0x3f, 0xf8, 0x00, 0x00, 0x1f, 0xf0, 0x00, 0x00,
      0x0f, 0xe0, 0x00, 0x00, 0x07, 0xc0, 0x00, 0x00, 0x03, 0x80, 0x00, 0x00, 0x01, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static char ew_mask32[] = {
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x80, 0x00, 0x80, 0x01,
      0x80, 0x01, 0xc0, 0x01, 0x80, 0x03, 0xe0, 0x01, 0x80, 0x07, 0xf0, 0x01, 0x80, 0x0f, 0xf8,
      0x01, 0x80, 0x1f, 0xfc, 0x01, 0x80, 0x3f, 0xfe, 0xff, 0xff, 0x7f, 0xff, 0xff, 0xff, 0xff,
      0xff, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff, 0x7f, 0xfc, 0x01, 0x80, 0x3f, 0xf8, 0x01, 0x80,
      0x1f, 0xf0, 0x01, 0x80, 0x0f, 0xe0, 0x01, 0x80, 0x07, 0xc0, 0x01, 0x80, 0x03, 0x80, 0x01,
      0x80, 0x01, 0x00, 0x01, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static BCursor EWArrowCursor = {
      {ew_bitmap16, ew_bitmap24, ew_bitmap32},
      {ew_mask16, ew_mask24, ew_mask32},
      {{7, 7}, {11, 14}, {15, 14}},
      true,
  };

  BlenderCursor[WM_CURSOR_X_MOVE] = &EWArrowCursor;
  BlenderCursor[WM_CURSOR_EW_ARROW] = &EWArrowCursor;
  END_CURSOR_BLOCK;

  /********************** Wait Cursor *****************************/
  BEGIN_CURSOR_BLOCK;

  static char wait_bitmap16[] = {
      0x00, 0x00, 0x00, 0x00, 0xf0, 0x07, 0xf0, 0x07, 0xb0, 0x06, 0x60,
      0x03, 0xc0, 0x01, 0x80, 0x00, 0x80, 0x00, 0xc0, 0x01, 0x60, 0x03,
      0x30, 0x06, 0x10, 0x04, 0xf0, 0x07, 0x00, 0x00, 0x00, 0x00,
  };

  static char wait_mask16[] = {
      0xfc, 0x1f, 0xfc, 0x1f, 0xf8, 0x0f, 0xf8, 0x0f, 0xf8, 0x0f, 0xf0,
      0x07, 0xe0, 0x03, 0xc0, 0x01, 0xc0, 0x01, 0xe0, 0x03, 0xf0, 0x07,
      0xf8, 0x0f, 0xf8, 0x0f, 0xf8, 0x0f, 0xfc, 0x1f, 0xfc, 0x1f,
  };

  static char wait_bitmap24[] = {
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfc, 0x7f, 0x00, 0x00, 0xfc, 0x7f, 0x00,
      0x00, 0xfc, 0x7f, 0x00, 0x00, 0xfc, 0x7f, 0x00, 0x00, 0xf8, 0x3f, 0x00, 0x00, 0x30, 0x18,
      0x00, 0x00, 0x60, 0x0c, 0x00, 0x00, 0xc0, 0x06, 0x00, 0x00, 0x80, 0x03, 0x00, 0x00, 0x00,
      0x01, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x80, 0x03, 0x00, 0x00, 0xc0, 0x07, 0x00, 0x00,
      0xe0, 0x0e, 0x00, 0x00, 0xf0, 0x1f, 0x00, 0x00, 0x78, 0x3c, 0x00, 0x00, 0x3c, 0x78, 0x00,
      0x00, 0x1c, 0x70, 0x00, 0x00, 0x1c, 0x70, 0x00, 0x00, 0xfc, 0x7f, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static char wait_mask24[] = {
      0xff, 0xff, 0x01, 0x00, 0xff, 0xff, 0x01, 0x00, 0xfe, 0xff, 0x00, 0x00, 0xfe, 0xff, 0x00,
      0x00, 0xfe, 0xff, 0x00, 0x00, 0xfe, 0xff, 0x00, 0x00, 0xfc, 0x7f, 0x00, 0x00, 0xf8, 0x3f,
      0x00, 0x00, 0xf0, 0x1f, 0x00, 0x00, 0xe0, 0x0f, 0x00, 0x00, 0xc0, 0x07, 0x00, 0x00, 0x80,
      0x03, 0x00, 0x00, 0x80, 0x03, 0x00, 0x00, 0xc0, 0x07, 0x00, 0x00, 0xe0, 0x0f, 0x00, 0x00,
      0xf0, 0x1f, 0x00, 0x00, 0xf8, 0x3f, 0x00, 0x00, 0xfc, 0x7f, 0x00, 0x00, 0xfe, 0xff, 0x00,
      0x00, 0xfe, 0xff, 0x00, 0x00, 0xfe, 0xff, 0x00, 0x00, 0xfe, 0xff, 0x00, 0x00, 0xff, 0xff,
      0x01, 0x00, 0xff, 0xff, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static char wait_bitmap32[] = {
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0xff, 0x7f, 0x00, 0x80, 0xff, 0x7f,
      0x00, 0x80, 0xff, 0x7f, 0x00, 0x80, 0xff, 0x7f, 0x00, 0x80, 0xff, 0x7f, 0x00, 0x80, 0xff,
      0x7f, 0x00, 0x80, 0x07, 0x78, 0x00, 0x00, 0x0f, 0x3c, 0x00, 0x00, 0x1e, 0x1e, 0x00, 0x00,
      0x3c, 0x0f, 0x00, 0x00, 0x38, 0x07, 0x00, 0x00, 0xf0, 0x03, 0x00, 0x00, 0xe0, 0x01, 0x00,
      0x00, 0xc0, 0x00, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 0xe0, 0x01, 0x00, 0x00, 0x30, 0x03,
      0x00, 0x00, 0x38, 0x07, 0x00, 0x00, 0xfc, 0x0f, 0x00, 0x00, 0x3e, 0x1f, 0x00, 0x00, 0x1f,
      0x3e, 0x00, 0x80, 0x0f, 0x7c, 0x00, 0x80, 0x07, 0x78, 0x00, 0x80, 0x03, 0x70, 0x00, 0x80,
      0x01, 0x60, 0x00, 0x80, 0x01, 0x60, 0x00, 0x80, 0xff, 0x7f, 0x00, 0x80, 0xff, 0x7f, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static char wait_mask32[] = {
      0xf0, 0xff, 0xff, 0x03, 0xf0, 0xff, 0xff, 0x03, 0xf0, 0xff, 0xff, 0x03, 0xc0, 0xff, 0xff,
      0x00, 0xc0, 0xff, 0xff, 0x00, 0xc0, 0xff, 0xff, 0x00, 0xc0, 0xff, 0xff, 0x00, 0xc0, 0xff,
      0xff, 0x00, 0xc0, 0xff, 0xff, 0x00, 0x80, 0xff, 0x7f, 0x00, 0x00, 0xff, 0x3f, 0x00, 0x00,
      0xfe, 0x1f, 0x00, 0x00, 0xfc, 0x0f, 0x00, 0x00, 0xf8, 0x07, 0x00, 0x00, 0xf0, 0x03, 0x00,
      0x00, 0xe0, 0x01, 0x00, 0x00, 0xe0, 0x01, 0x00, 0x00, 0xf0, 0x03, 0x00, 0x00, 0xf8, 0x07,
      0x00, 0x00, 0xfc, 0x0f, 0x00, 0x00, 0xfe, 0x1f, 0x00, 0x00, 0xff, 0x3f, 0x00, 0x80, 0xff,
      0x7f, 0x00, 0xc0, 0xff, 0xff, 0x00, 0xc0, 0xff, 0xff, 0x00, 0xc0, 0xff, 0xff, 0x00, 0xc0,
      0xff, 0xff, 0x00, 0xc0, 0xff, 0xff, 0x00, 0xc0, 0xff, 0xff, 0x00, 0xf0, 0xff, 0xff, 0x03,
      0xf0, 0xff, 0xff, 0x03, 0xf0, 0xff, 0xff, 0x03,
  };

  static BCursor WaitCursor = {
      {wait_bitmap16, wait_bitmap24, wait_bitmap32},
      {wait_mask16, wait_mask24, wait_mask32},
      {{7, 7}, {8, 11}, {15, 15}},
      false,
  };

  BlenderCursor[WM_CURSOR_WAIT] = &WaitCursor;
  END_CURSOR_BLOCK;

  /********************** Mute Cursor ***********************/
  BEGIN_CURSOR_BLOCK;

  static char mute_bitmap16[] = {
      0x00, 0x00, 0x22, 0x00, 0x14, 0x00, 0x08, 0x03, 0x14, 0x03, 0x22,
      0x03, 0x00, 0x03, 0x00, 0x03, 0xf8, 0x7c, 0xf8, 0x7c, 0x00, 0x03,
      0x00, 0x03, 0x00, 0x03, 0x00, 0x03, 0x00, 0x03, 0x00, 0x00,
  };

  static char mute_mask16[] = {
      0x63, 0x00, 0x77, 0x00, 0x3e, 0x03, 0x1c, 0x03, 0x3e, 0x03, 0x77,
      0x03, 0x63, 0x03, 0x80, 0x07, 0xfc, 0xfc, 0xfc, 0xfc, 0x80, 0x07,
      0x00, 0x03, 0x00, 0x03, 0x00, 0x03, 0x00, 0x03, 0x00, 0x03,
  };

  static char mute_bitmap24[] = {
      0x00, 0x00, 0x00, 0x00, 0x02, 0x02, 0x00, 0x00, 0x04, 0x01, 0x00, 0x00, 0x88, 0x00, 0x00,
      0x00, 0x50, 0x00, 0x00, 0x00, 0x20, 0x60, 0x00, 0x00, 0x50, 0x60, 0x00, 0x00, 0x88, 0x60,
      0x00, 0x00, 0x04, 0x61, 0x00, 0x00, 0x02, 0x62, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00,
      0x60, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0xe0, 0x9f, 0x7f, 0x00, 0xe0, 0x9f, 0x7f, 0x00,
      0x00, 0x60, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0x60, 0x00,
      0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0x60,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static char mute_mask24[] = {
      0x03, 0x06, 0x00, 0x00, 0x07, 0x07, 0x00, 0x00, 0x8e, 0x03, 0x00, 0x00, 0xdc, 0x01, 0x00,
      0x00, 0xf8, 0xf0, 0x00, 0x00, 0x70, 0xf0, 0x00, 0x00, 0xf8, 0xf0, 0x00, 0x00, 0xdc, 0xf1,
      0x00, 0x00, 0x8e, 0xf3, 0x00, 0x00, 0x07, 0xf7, 0x00, 0x00, 0x03, 0xf6, 0x00, 0x00, 0x00,
      0xf0, 0x00, 0x00, 0xf0, 0xff, 0xff, 0x00, 0xf0, 0x9f, 0xff, 0x00, 0xf0, 0x9f, 0xff, 0x00,
      0xf0, 0xff, 0xff, 0x00, 0x00, 0xf0, 0x00, 0x00, 0x00, 0xf0, 0x00, 0x00, 0x00, 0xf0, 0x00,
      0x00, 0x00, 0xf0, 0x00, 0x00, 0x00, 0xf0, 0x00, 0x00, 0x00, 0xf0, 0x00, 0x00, 0x00, 0xf0,
      0x00, 0x00, 0x00, 0xf0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static char mute_bitmap32[] = {
      0x00, 0x00, 0x00, 0x00, 0x02, 0x08, 0x00, 0x00, 0x06, 0x0c, 0x00, 0x00, 0x0c, 0x06, 0x00,
      0x00, 0x18, 0x03, 0x00, 0x00, 0xb0, 0x01, 0x0f, 0x00, 0xe0, 0x00, 0x0f, 0x00, 0xe0, 0x00,
      0x0f, 0x00, 0xb0, 0x01, 0x0f, 0x00, 0x18, 0x03, 0x0f, 0x00, 0x0c, 0x06, 0x0f, 0x00, 0x06,
      0x0c, 0x0f, 0x00, 0x02, 0x08, 0x0f, 0x00, 0x00, 0x00, 0x0f, 0x00, 0x00, 0x00, 0x0f, 0x00,
      0x00, 0x00, 0x0f, 0x00, 0xe0, 0xff, 0xf0, 0x7f, 0xe0, 0xff, 0xf0, 0x7f, 0xe0, 0xff, 0xf0,
      0x7f, 0xe0, 0xff, 0xf0, 0x7f, 0x00, 0x00, 0x0f, 0x00, 0x00, 0x00, 0x0f, 0x00, 0x00, 0x00,
      0x0f, 0x00, 0x00, 0x00, 0x0f, 0x00, 0x00, 0x00, 0x0f, 0x00, 0x00, 0x00, 0x0f, 0x00, 0x00,
      0x00, 0x0f, 0x00, 0x00, 0x00, 0x0f, 0x00, 0x00, 0x00, 0x0f, 0x00, 0x00, 0x00, 0x0f, 0x00,
      0x00, 0x00, 0x0f, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static char mute_mask32[] = {
      0x03, 0x18, 0x00, 0x00, 0x07, 0x1c, 0x00, 0x00, 0x0f, 0x1e, 0x00, 0x00, 0x1e, 0x0f, 0x00,
      0x00, 0xbc, 0x87, 0x1f, 0x00, 0xf8, 0x83, 0x1f, 0x00, 0xf0, 0x81, 0x1f, 0x00, 0xf0, 0x81,
      0x1f, 0x00, 0xf8, 0x83, 0x1f, 0x00, 0xbc, 0x87, 0x1f, 0x00, 0x1e, 0x8f, 0x1f, 0x00, 0x0f,
      0x9e, 0x1f, 0x00, 0x07, 0x9c, 0x1f, 0x00, 0x03, 0x98, 0x1f, 0x00, 0x00, 0x80, 0x1f, 0x00,
      0xf0, 0xff, 0xff, 0xff, 0xf0, 0xff, 0xf0, 0xff, 0xf0, 0xff, 0xf0, 0xff, 0xf0, 0xff, 0xf0,
      0xff, 0xf0, 0xff, 0xf0, 0xff, 0xf0, 0xff, 0xff, 0xff, 0x00, 0x80, 0x1f, 0x00, 0x00, 0x80,
      0x1f, 0x00, 0x00, 0x80, 0x1f, 0x00, 0x00, 0x80, 0x1f, 0x00, 0x00, 0x80, 0x1f, 0x00, 0x00,
      0x80, 0x1f, 0x00, 0x00, 0x80, 0x1f, 0x00, 0x00, 0x80, 0x1f, 0x00, 0x00, 0x80, 0x1f, 0x00,
      0x00, 0x80, 0x1f, 0x00, 0x00, 0x80, 0x1f, 0x00,
  };

  static BCursor MuteCursor = {
      {mute_bitmap16, mute_bitmap24, mute_bitmap32},
      {mute_mask16, mute_mask24, mute_mask32},
      {{8, 8}, {13, 13}, {17, 17}},
      true,
  };

  BlenderCursor[WM_CURSOR_MUTE] = &MuteCursor;
  END_CURSOR_BLOCK;

  /****************** Normal Cross Cursor ************************/
  BEGIN_CURSOR_BLOCK;

  static char cross_bitmap16[] = {
      0x00, 0x00, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80,
      0x01, 0x00, 0x00, 0x3e, 0x7c, 0x3e, 0x7c, 0x00, 0x00, 0x80, 0x01,
      0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x00, 0x00,
  };

  static char cross_mask16[] = {
      0xc0, 0x03, 0xc0, 0x03, 0xc0, 0x03, 0xc0, 0x03, 0xc0, 0x03, 0xc0,
      0x03, 0xff, 0xff, 0x7f, 0xfe, 0x7f, 0xfe, 0xff, 0xff, 0xc0, 0x03,
      0xc0, 0x03, 0xc0, 0x03, 0xc0, 0x03, 0xc0, 0x03, 0xc0, 0x03,
  };

  static char cross_bitmap24[] = {
      0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x18, 0x00,
      0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x18,
      0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfe,
      0xc3, 0x7f, 0x00, 0xfe, 0xc3, 0x7f, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00,
      0x00, 0x18, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x18, 0x00,
      0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x18,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static char cross_mask24[] = {
      0x00, 0x3c, 0x00, 0x00, 0x00, 0x3c, 0x00, 0x00, 0x00, 0x3c, 0x00, 0x00, 0x00, 0x3c, 0x00,
      0x00, 0x00, 0x3c, 0x00, 0x00, 0x00, 0x3c, 0x00, 0x00, 0x00, 0x3c, 0x00, 0x00, 0x00, 0x3c,
      0x00, 0x00, 0x00, 0x3c, 0x00, 0x00, 0x00, 0x3c, 0x00, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff,
      0xe7, 0xff, 0x00, 0xff, 0xe7, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0x00, 0x3c, 0x00, 0x00,
      0x00, 0x3c, 0x00, 0x00, 0x00, 0x3c, 0x00, 0x00, 0x00, 0x3c, 0x00, 0x00, 0x00, 0x3c, 0x00,
      0x00, 0x00, 0x3c, 0x00, 0x00, 0x00, 0x3c, 0x00, 0x00, 0x00, 0x3c, 0x00, 0x00, 0x00, 0x3c,
      0x00, 0x00, 0x00, 0x3c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static char cross_bitmap32[] = {
      0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x03, 0x00, 0x00, 0xc0, 0x03, 0x00, 0x00, 0xc0, 0x03,
      0x00, 0x00, 0xc0, 0x03, 0x00, 0x00, 0xc0, 0x03, 0x00, 0x00, 0xc0, 0x03, 0x00, 0x00, 0xc0,
      0x03, 0x00, 0x00, 0xc0, 0x03, 0x00, 0x00, 0xc0, 0x03, 0x00, 0x00, 0xc0, 0x03, 0x00, 0x00,
      0xc0, 0x03, 0x00, 0x00, 0xc0, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfe, 0x1f, 0xf8, 0x7f,
      0xfe, 0x1f, 0xf8, 0x7f, 0xfe, 0x1f, 0xf8, 0x7f, 0xfe, 0x1f, 0xf8, 0x7f, 0x00, 0x00, 0x00,
      0x00, 0x00, 0xc0, 0x03, 0x00, 0x00, 0xc0, 0x03, 0x00, 0x00, 0xc0, 0x03, 0x00, 0x00, 0xc0,
      0x03, 0x00, 0x00, 0xc0, 0x03, 0x00, 0x00, 0xc0, 0x03, 0x00, 0x00, 0xc0, 0x03, 0x00, 0x00,
      0xc0, 0x03, 0x00, 0x00, 0xc0, 0x03, 0x00, 0x00, 0xc0, 0x03, 0x00, 0x00, 0xc0, 0x03, 0x00,
      0x00, 0xc0, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static char cross_mask32[] = {
      0x00, 0xe0, 0x07, 0x00, 0x00, 0xe0, 0x07, 0x00, 0x00, 0xe0, 0x07, 0x00, 0x00, 0xe0, 0x07,
      0x00, 0x00, 0xe0, 0x07, 0x00, 0x00, 0xe0, 0x07, 0x00, 0x00, 0xe0, 0x07, 0x00, 0x00, 0xe0,
      0x07, 0x00, 0x00, 0xe0, 0x07, 0x00, 0x00, 0xe0, 0x07, 0x00, 0x00, 0xe0, 0x07, 0x00, 0x00,
      0xe0, 0x07, 0x00, 0x00, 0xe0, 0x07, 0x00, 0xff, 0xff, 0xff, 0xff, 0xff, 0x3f, 0xfc, 0xff,
      0xff, 0x3f, 0xfc, 0xff, 0xff, 0x3f, 0xfc, 0xff, 0xff, 0x3f, 0xfc, 0xff, 0xff, 0xff, 0xff,
      0xff, 0x00, 0xe0, 0x07, 0x00, 0x00, 0xe0, 0x07, 0x00, 0x00, 0xe0, 0x07, 0x00, 0x00, 0xe0,
      0x07, 0x00, 0x00, 0xe0, 0x07, 0x00, 0x00, 0xe0, 0x07, 0x00, 0x00, 0xe0, 0x07, 0x00, 0x00,
      0xe0, 0x07, 0x00, 0x00, 0xe0, 0x07, 0x00, 0x00, 0xe0, 0x07, 0x00, 0x00, 0xe0, 0x07, 0x00,
      0x00, 0xe0, 0x07, 0x00, 0x00, 0xe0, 0x07, 0x00,
  };

  static BCursor CrossCursor = {
      {cross_bitmap16, cross_bitmap24, cross_bitmap32},
      {cross_mask16, cross_mask24, cross_mask32},
      {{7, 7}, {11, 11}, {15, 15}},
      false,
  };

  BlenderCursor[WM_CURSOR_EDIT] = &CrossCursor;
  BlenderCursor[WM_CURSOR_CROSS] = &CrossCursor;
  END_CURSOR_BLOCK;

  /****************** Painting Cursor ************************/
  BEGIN_CURSOR_BLOCK;

  static char paint_bitmap16[] = {
      0x80, 0x00, 0x80, 0x00, 0x80, 0x00, 0x80, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x8f, 0x78, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x80, 0x00, 0x80, 0x00, 0x80, 0x00, 0x80, 0x00, 0x00, 0x00,
  };

  static char paint_mask16[] = {
      0xc0, 0x01, 0xc0, 0x01, 0xc0, 0x01, 0xc0, 0x01, 0x00, 0x00, 0x00,
      0x00, 0x8f, 0x78, 0xcf, 0x79, 0x8f, 0x78, 0x00, 0x00, 0x00, 0x00,
      0xc0, 0x01, 0xc0, 0x01, 0xc0, 0x01, 0xc0, 0x01, 0x00, 0x00,
  };

  static char paint_bitmap24[] = {
      0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x08, 0x00,
      0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7e,
      0x08, 0x3f, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x08, 0x00,
      0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static char paint_mask24[] = {
      0x00, 0x08, 0x00, 0x00, 0x00, 0x1c, 0x00, 0x00, 0x00, 0x1c, 0x00, 0x00, 0x00, 0x1c, 0x00,
      0x00, 0x00, 0x1c, 0x00, 0x00, 0x00, 0x1c, 0x00, 0x00, 0x00, 0x1c, 0x00, 0x00, 0x00, 0x08,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7e, 0x08, 0x3f, 0x00, 0xff,
      0x9c, 0x7f, 0x00, 0x7e, 0x08, 0x3f, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x08, 0x00, 0x00, 0x00, 0x1c, 0x00, 0x00, 0x00, 0x1c, 0x00, 0x00, 0x00, 0x1c, 0x00,
      0x00, 0x00, 0x1c, 0x00, 0x00, 0x00, 0x1c, 0x00, 0x00, 0x00, 0x1c, 0x00, 0x00, 0x00, 0x08,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static char paint_bitmap32[] = {
      0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x01, 0x00, 0x00, 0x80, 0x01, 0x00, 0x00, 0x80, 0x01,
      0x00, 0x00, 0x80, 0x01, 0x00, 0x00, 0x80, 0x01, 0x00, 0x00, 0x80, 0x01, 0x00, 0x00, 0x80,
      0x01, 0x00, 0x00, 0x80, 0x01, 0x00, 0x00, 0x80, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0xfe, 0x83, 0xc1, 0x7f, 0xfe, 0x83, 0xc1, 0x7f, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80,
      0x01, 0x00, 0x00, 0x80, 0x01, 0x00, 0x00, 0x80, 0x01, 0x00, 0x00, 0x80, 0x01, 0x00, 0x00,
      0x80, 0x01, 0x00, 0x00, 0x80, 0x01, 0x00, 0x00, 0x80, 0x01, 0x00, 0x00, 0x80, 0x01, 0x00,
      0x00, 0x80, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static char paint_mask32[] = {
      0x00, 0x80, 0x01, 0x00, 0x00, 0xc0, 0x03, 0x00, 0x00, 0xc0, 0x03, 0x00, 0x00, 0xc0, 0x03,
      0x00, 0x00, 0xc0, 0x03, 0x00, 0x00, 0xc0, 0x03, 0x00, 0x00, 0xc0, 0x03, 0x00, 0x00, 0xc0,
      0x03, 0x00, 0x00, 0xc0, 0x03, 0x00, 0x00, 0xc0, 0x03, 0x00, 0x00, 0x80, 0x01, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfe, 0x83, 0xc1, 0x7f,
      0xff, 0xc7, 0xe3, 0xff, 0xff, 0xc7, 0xe3, 0xff, 0xfe, 0x83, 0xc1, 0x7f, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x01, 0x00, 0x00, 0xc0,
      0x03, 0x00, 0x00, 0xc0, 0x03, 0x00, 0x00, 0xc0, 0x03, 0x00, 0x00, 0xc0, 0x03, 0x00, 0x00,
      0xc0, 0x03, 0x00, 0x00, 0xc0, 0x03, 0x00, 0x00, 0xc0, 0x03, 0x00, 0x00, 0xc0, 0x03, 0x00,
      0x00, 0xc0, 0x03, 0x00, 0x00, 0x80, 0x01, 0x00,
  };

  static BCursor PaintCursor = {
      {paint_bitmap16, paint_bitmap24, paint_bitmap32},
      {paint_mask16, paint_mask24, paint_mask32},
      {{7, 7}, {11, 11}, {15, 15}},
      false,
  };

  BlenderCursor[WM_CURSOR_PAINT] = &PaintCursor;
  END_CURSOR_BLOCK;

  /********************** Dot Cursor ***********************/
  BEGIN_CURSOR_BLOCK;

  static char dot_bitmap16[] = {
      0x80, 0x00, 0x80, 0x00, 0x80, 0x00, 0x80, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x8f, 0x78, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x80, 0x00, 0x80, 0x00, 0x80, 0x00, 0x80, 0x00, 0x00, 0x00,
  };

  static char dot_mask16[] = {
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x80, 0x00, 0xc0, 0x01, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static char dot_bitmap24[] = {
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static char dot_mask24[] = {
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x00, 0x00, 0x00, 0xe0, 0x00, 0x00,
      0x00, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static char dot_bitmap32[] = {
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x00, 0x00,
      0x00, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static char dot_mask32[] = {
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 0xe0, 0x01, 0x00,
      0x00, 0xe0, 0x01, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static BCursor DotCursor = {
      {dot_bitmap16, dot_bitmap24, dot_bitmap32},
      {dot_mask16, dot_mask24, dot_mask32},
      {{7, 7}, {14, 14}, {14, 14}},
      false,
  };

  BlenderCursor[WM_CURSOR_DOT] = &DotCursor;
  END_CURSOR_BLOCK;

  /************* Minimal Crosshair Cursor ***************/
  BEGIN_CURSOR_BLOCK;

  static char crossc_bitmap16[] = {
      0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00,
      0x00, 0x80, 0x00, 0x55, 0x55, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00,
      0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00,
  };

  static char crossc_mask16[] = {
      0x80, 0x00, 0x80, 0x00, 0x80, 0x00, 0x80, 0x00, 0x80, 0x00, 0x80,
      0x00, 0x80, 0x00, 0x7f, 0x7f, 0x80, 0x00, 0x80, 0x00, 0x80, 0x00,
      0x80, 0x00, 0x80, 0x00, 0x80, 0x00, 0x80, 0x00, 0x00, 0x00,
  };

  static char crossc_bitmap24[] = {
      0x00, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x00, 0x00, 0x55,
      0x55, 0x55, 0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static char crossc_mask24[] = {
      0x00, 0x08, 0x00, 0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x08, 0x00,
      0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x08,
      0x00, 0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x08, 0x00, 0x00, 0xff,
      0xf7, 0x7f, 0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x08, 0x00, 0x00,
      0x00, 0x08, 0x00, 0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x08, 0x00,
      0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x08,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static char crossc_bitmap32[] = {
      0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x01, 0x00, 0x00, 0x80, 0x01, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x01, 0x00, 0x00, 0x80, 0x01, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x01, 0x00, 0x00, 0x80, 0x01, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x01, 0x00, 0x00, 0x80, 0x01, 0x00,
      0x66, 0x66, 0x66, 0x66, 0x66, 0x66, 0x66, 0x66, 0x00, 0x80, 0x01, 0x00, 0x00, 0x80, 0x01,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x01, 0x00, 0x00, 0x80,
      0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x01, 0x00, 0x00,
      0x80, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x01, 0x00,
      0x00, 0x80, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static char crossc_mask32[] = {
      0x00, 0x80, 0x01, 0x00, 0x00, 0x80, 0x01, 0x00, 0x00, 0x80, 0x01, 0x00, 0x00, 0x80, 0x01,
      0x00, 0x00, 0x80, 0x01, 0x00, 0x00, 0x80, 0x01, 0x00, 0x00, 0x80, 0x01, 0x00, 0x00, 0x80,
      0x01, 0x00, 0x00, 0x80, 0x01, 0x00, 0x00, 0x80, 0x01, 0x00, 0x00, 0x80, 0x01, 0x00, 0x00,
      0x80, 0x01, 0x00, 0x00, 0x80, 0x01, 0x00, 0x00, 0x80, 0x01, 0x00, 0x00, 0x80, 0x01, 0x00,
      0xff, 0x7f, 0xfe, 0xff, 0xff, 0x7f, 0xfe, 0xff, 0x00, 0x80, 0x01, 0x00, 0x00, 0x80, 0x01,
      0x00, 0x00, 0x80, 0x01, 0x00, 0x00, 0x80, 0x01, 0x00, 0x00, 0x80, 0x01, 0x00, 0x00, 0x80,
      0x01, 0x00, 0x00, 0x80, 0x01, 0x00, 0x00, 0x80, 0x01, 0x00, 0x00, 0x80, 0x01, 0x00, 0x00,
      0x80, 0x01, 0x00, 0x00, 0x80, 0x01, 0x00, 0x00, 0x80, 0x01, 0x00, 0x00, 0x80, 0x01, 0x00,
      0x00, 0x80, 0x01, 0x00, 0x00, 0x80, 0x01, 0x00,
  };

  static BCursor CrossCursorC = {
      {crossc_bitmap16, crossc_bitmap24, crossc_bitmap32},
      {crossc_mask16, crossc_mask24, crossc_mask32},
      {{7, 7}, {11, 11}, {15, 15}},
      false,
  };

  BlenderCursor[WM_CURSOR_CROSSC] = &CrossCursorC;
  END_CURSOR_BLOCK;

  /********************** Knife Cursor ***********************/
  BEGIN_CURSOR_BLOCK;

  static char knife_bitmap16[] = {
      0x00, 0x00, 0x00, 0x40, 0x00, 0x60, 0x00, 0x30, 0x00, 0x18, 0x00,
      0x0c, 0x00, 0x06, 0x00, 0x0f, 0x80, 0x07, 0xc0, 0x03, 0xe0, 0x01,
      0xf0, 0x00, 0x78, 0x00, 0x3c, 0x00, 0x0e, 0x00, 0x00, 0x00,
  };

  static char knife_mask16[] = {
      0x00, 0x40, 0x00, 0xe0, 0x00, 0xf0, 0x00, 0x78, 0x00, 0x3c, 0x00,
      0x1e, 0x00, 0x0f, 0x80, 0x1f, 0xc0, 0x0f, 0xe0, 0x07, 0xf0, 0x03,
      0xf8, 0x01, 0xfc, 0x00, 0x7e, 0x00, 0x3f, 0x00, 0x0f, 0x00,
  };

  static char knife_bitmap24[] = {
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0x00, 0x00, 0x00, 0x30,
      0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x0c, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00,
      0x03, 0x00, 0x00, 0x80, 0x01, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 0xe0, 0x01, 0x00, 0x00,
      0xf0, 0x03, 0x00, 0x00, 0xf8, 0x01, 0x00, 0x00, 0xfc, 0x00, 0x00, 0x00, 0x7e, 0x00, 0x00,
      0x00, 0x3f, 0x00, 0x00, 0x80, 0x1f, 0x00, 0x00, 0xc0, 0x0f, 0x00, 0x00, 0xe0, 0x07, 0x00,
      0x00, 0xf0, 0x03, 0x00, 0x00, 0xf8, 0x01, 0x00, 0x00, 0x7c, 0x00, 0x00, 0x00, 0x1e, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static char knife_mask24[] = {
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0x00, 0x00, 0x00, 0x70, 0x00, 0x00, 0x00, 0x78,
      0x00, 0x00, 0x00, 0x3c, 0x00, 0x00, 0x00, 0x1e, 0x00, 0x00, 0x00, 0x0f, 0x00, 0x00, 0x80,
      0x07, 0x00, 0x00, 0xc0, 0x03, 0x00, 0x00, 0xe0, 0x01, 0x00, 0x00, 0xf0, 0x03, 0x00, 0x00,
      0xf8, 0x07, 0x00, 0x00, 0xfc, 0x03, 0x00, 0x00, 0xfe, 0x01, 0x00, 0x00, 0xff, 0x00, 0x00,
      0x80, 0x7f, 0x00, 0x00, 0xc0, 0x3f, 0x00, 0x00, 0xe0, 0x1f, 0x00, 0x00, 0xf0, 0x0f, 0x00,
      0x00, 0xf8, 0x07, 0x00, 0x00, 0xfc, 0x03, 0x00, 0x00, 0xfe, 0x01, 0x00, 0x00, 0x7f, 0x00,
      0x00, 0x00, 0x1f, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static char knife_bitmap32[] = {
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x10, 0x00, 0x00, 0x00, 0x38, 0x00, 0x00, 0x00, 0x1c, 0x00, 0x00, 0x00, 0x0e, 0x00, 0x00,
      0x00, 0x07, 0x00, 0x00, 0x80, 0x03, 0x00, 0x00, 0xc0, 0x01, 0x00, 0x00, 0xe0, 0x00, 0x00,
      0x00, 0x70, 0x00, 0x00, 0x00, 0x38, 0x00, 0x00, 0x00, 0x7c, 0x00, 0x00, 0x00, 0xfe, 0x00,
      0x00, 0x00, 0xff, 0x00, 0x00, 0x80, 0x7f, 0x00, 0x00, 0xc0, 0x3f, 0x00, 0x00, 0xe0, 0x1f,
      0x00, 0x00, 0xf0, 0x0f, 0x00, 0x00, 0xf8, 0x07, 0x00, 0x00, 0xfc, 0x03, 0x00, 0x00, 0xfe,
      0x01, 0x00, 0x00, 0xff, 0x00, 0x00, 0x80, 0x7f, 0x00, 0x00, 0xc0, 0x3f, 0x00, 0x00, 0xe0,
      0x1f, 0x00, 0x00, 0xf0, 0x0f, 0x00, 0x00, 0xf8, 0x07, 0x00, 0x00, 0xfc, 0x01, 0x00, 0x00,
      0x7e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static char knife_mask32[] = {
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 0x38, 0x00, 0x00, 0x00,
      0x7c, 0x00, 0x00, 0x00, 0xfe, 0x00, 0x00, 0x00, 0x7f, 0x00, 0x00, 0x80, 0x3f, 0x00, 0x00,
      0xc0, 0x1f, 0x00, 0x00, 0xe0, 0x0f, 0x00, 0x00, 0xf0, 0x07, 0x00, 0x00, 0xf8, 0x03, 0x00,
      0x00, 0xfc, 0x01, 0x00, 0x00, 0xfe, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x80, 0xff, 0x01,
      0x00, 0xc0, 0xff, 0x03, 0x00, 0xe0, 0xff, 0x01, 0x00, 0xf0, 0xff, 0x00, 0x00, 0xf8, 0x7f,
      0x00, 0x00, 0xfc, 0x3f, 0x00, 0x00, 0xfe, 0x1f, 0x00, 0x00, 0xff, 0x0f, 0x00, 0x80, 0xff,
      0x07, 0x00, 0xc0, 0xff, 0x03, 0x00, 0xe0, 0xff, 0x01, 0x00, 0xf0, 0xff, 0x00, 0x00, 0xf8,
      0x7f, 0x00, 0x00, 0xfc, 0x3f, 0x00, 0x00, 0xfe, 0x1f, 0x00, 0x00, 0xff, 0x07, 0x00, 0x00,
      0xff, 0x03, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00,
  };

  static BCursor KnifeCursor = {
      {knife_bitmap16, knife_bitmap24, knife_bitmap32},
      {knife_mask16, knife_mask24, knife_mask32},
      {{0, 15}, {0, 23}, {0, 31}},
      false,
  };

  BlenderCursor[WM_CURSOR_KNIFE] = &KnifeCursor;
  END_CURSOR_BLOCK;

  /********************** Loop Select Cursor ***********************/
  BEGIN_CURSOR_BLOCK;

  static char vloop_bitmap16[] = {
      0x00, 0x00, 0x7e, 0x00, 0x3e, 0x00, 0x1e, 0x00, 0xfe, 0xf0, 0x96,
      0x9f, 0x92, 0x90, 0xf0, 0xf0, 0x20, 0x40, 0x20, 0x40, 0x20, 0x40,
      0x20, 0x40, 0xf0, 0xf0, 0x90, 0x90, 0x90, 0x9f, 0xf0, 0xf0,
  };

  static char vloop_mask16[] = {
      0xff, 0x01, 0xff, 0x00, 0x7f, 0x00, 0x3f, 0x00, 0xff, 0xf0, 0xff,
      0xff, 0xf7, 0xff, 0xf3, 0xf0, 0x61, 0x60, 0x60, 0x60, 0x60, 0x60,
      0x60, 0x60, 0xf0, 0xf0, 0xf0, 0xff, 0xf0, 0xff, 0xf0, 0xf0,
  };

  static char vloop_bitmap24[] = {
      0x00, 0x00, 0x00, 0x00, 0xfe, 0x03, 0x00, 0x00, 0xfe, 0x01, 0x00, 0x00, 0xfe, 0x00, 0x00,
      0x00, 0x7e, 0x00, 0x00, 0x00, 0x3e, 0x00, 0x00, 0x00, 0x9e, 0x0f, 0xf8, 0x00, 0x8e, 0x08,
      0x88, 0x00, 0x86, 0xf8, 0x8f, 0x00, 0x82, 0x08, 0x88, 0x00, 0x80, 0x0f, 0xf8, 0x00, 0x00,
      0x02, 0x20, 0x00, 0x00, 0x02, 0x20, 0x00, 0x00, 0x02, 0x20, 0x00, 0x00, 0x02, 0x20, 0x00,
      0x00, 0x02, 0x20, 0x00, 0x00, 0x02, 0x20, 0x00, 0x00, 0x02, 0x20, 0x00, 0x00, 0x02, 0x20,
      0x00, 0x80, 0x0f, 0xf8, 0x00, 0x80, 0x08, 0x88, 0x00, 0x80, 0xf8, 0x8f, 0x00, 0x80, 0x08,
      0x88, 0x00, 0x80, 0x0f, 0xf8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static char vloop_mask24[] = {
      0xff, 0x0f, 0x00, 0x00, 0xff, 0x07, 0x00, 0x00, 0xff, 0x03, 0x00, 0x00, 0xff, 0x01, 0x00,
      0x00, 0xff, 0x00, 0x00, 0x00, 0x7f, 0x00, 0x00, 0x00, 0xbf, 0x0f, 0xf8, 0x00, 0x9f, 0xff,
      0xff, 0x00, 0x8f, 0xff, 0xff, 0x00, 0x87, 0xff, 0xff, 0x00, 0x83, 0x0f, 0xf8, 0x00, 0x01,
      0x07, 0x70, 0x00, 0x00, 0x07, 0x70, 0x00, 0x00, 0x07, 0x70, 0x00, 0x00, 0x07, 0x70, 0x00,
      0x00, 0x07, 0x70, 0x00, 0x00, 0x07, 0x70, 0x00, 0x00, 0x07, 0x70, 0x00, 0x00, 0x07, 0x70,
      0x00, 0x80, 0x0f, 0xf8, 0x00, 0x80, 0xff, 0xff, 0x00, 0x80, 0xff, 0xff, 0x00, 0x80, 0xff,
      0xff, 0x00, 0x80, 0x0f, 0xf8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static char vloop_bitmap32[] = {
      0x00, 0x00, 0x00, 0x00, 0xfe, 0x7f, 0x00, 0x00, 0xfe, 0x3f, 0x00, 0x00, 0xfe, 0x1f, 0x00,
      0x00, 0xfe, 0x0f, 0x00, 0x00, 0xfe, 0x07, 0x00, 0x00, 0xfe, 0x03, 0x00, 0x00, 0xfe, 0x01,
      0x00, 0x00, 0xfe, 0x7e, 0x00, 0x7e, 0x7e, 0xff, 0x00, 0xff, 0x3e, 0xc3, 0x00, 0xc3, 0x1e,
      0xc3, 0xff, 0xc3, 0x0e, 0xc3, 0xff, 0xc3, 0x06, 0xc3, 0x00, 0xc3, 0x02, 0xff, 0x00, 0xff,
      0x00, 0x7e, 0x00, 0x7e, 0x00, 0x18, 0x00, 0x18, 0x00, 0x18, 0x00, 0x18, 0x00, 0x18, 0x00,
      0x18, 0x00, 0x18, 0x00, 0x18, 0x00, 0x18, 0x00, 0x18, 0x00, 0x18, 0x00, 0x18, 0x00, 0x18,
      0x00, 0x18, 0x00, 0x18, 0x00, 0x18, 0x00, 0x7e, 0x00, 0x7e, 0x00, 0xff, 0x00, 0xff, 0x00,
      0xc3, 0x00, 0xc3, 0x00, 0xc3, 0xff, 0xc3, 0x00, 0xc3, 0xff, 0xc3, 0x00, 0xc3, 0x00, 0xc3,
      0x00, 0xff, 0x00, 0xff, 0x00, 0x7e, 0x00, 0x7e,
  };

  static char vloop_mask32[] = {
      0xff, 0xff, 0x01, 0x00, 0xff, 0xff, 0x00, 0x00, 0xff, 0x7f, 0x00, 0x00, 0xff, 0x3f, 0x00,
      0x00, 0xff, 0x1f, 0x00, 0x00, 0xff, 0x0f, 0x00, 0x00, 0xff, 0x07, 0x00, 0x00, 0xff, 0x03,
      0x00, 0x00, 0xff, 0x7f, 0x00, 0x7e, 0xff, 0xff, 0x00, 0xff, 0x7f, 0xff, 0xff, 0xff, 0x3f,
      0xe7, 0xff, 0xe7, 0x1f, 0xe7, 0xff, 0xe7, 0x0f, 0xff, 0xff, 0xff, 0x07, 0xff, 0x00, 0xff,
      0x03, 0x7e, 0x00, 0x7e, 0x01, 0x3c, 0x00, 0x3c, 0x00, 0x3c, 0x00, 0x3c, 0x00, 0x3c, 0x00,
      0x3c, 0x00, 0x3c, 0x00, 0x3c, 0x00, 0x3c, 0x00, 0x3c, 0x00, 0x3c, 0x00, 0x3c, 0x00, 0x3c,
      0x00, 0x3c, 0x00, 0x3c, 0x00, 0x3c, 0x00, 0x7e, 0x00, 0x7e, 0x00, 0xff, 0x00, 0xff, 0x00,
      0xff, 0xff, 0xff, 0x00, 0xe7, 0xff, 0xe7, 0x00, 0xe7, 0xff, 0xe7, 0x00, 0xff, 0xff, 0xff,
      0x00, 0xff, 0x00, 0xff, 0x00, 0x7e, 0x00, 0x7e,
  };

  static BCursor VLoopCursor = {
      {vloop_bitmap16, vloop_bitmap24, vloop_bitmap32},
      {vloop_mask16, vloop_mask24, vloop_mask32},
      {{0, 0}, {0, 0}, {0, 0}},
      false,
  };

  BlenderCursor[WM_CURSOR_VERTEX_LOOP] = &VLoopCursor;
  END_CURSOR_BLOCK;

  /********************** TextEdit Cursor ***********************/
  BEGIN_CURSOR_BLOCK;

  static char textedit_bitmap16[] = {
      0x00, 0x00, 0x70, 0x07, 0x80, 0x00, 0x80, 0x00, 0x80, 0x00, 0x80,
      0x00, 0x80, 0x00, 0x80, 0x00, 0x80, 0x00, 0x80, 0x00, 0x80, 0x00,
      0x80, 0x00, 0x80, 0x00, 0x80, 0x00, 0x70, 0x07, 0x00, 0x00,
  };

  static char textedit_mask16[] = {
      0x70, 0x07, 0xf8, 0x0f, 0xf0, 0x07, 0xc0, 0x01, 0xc0, 0x01, 0xc0,
      0x01, 0xc0, 0x01, 0xc0, 0x01, 0xc0, 0x01, 0xc0, 0x01, 0xc0, 0x01,
      0xc0, 0x01, 0xc0, 0x01, 0xf0, 0x07, 0xf8, 0x0f, 0x70, 0x07,
  };

  static char textedit_bitmap24[] = {
      0x00, 0x00, 0x00, 0x00, 0x80, 0xf7, 0x00, 0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x08, 0x00,
      0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x08,
      0x00, 0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x08, 0x00, 0x00, 0x00,
      0x08, 0x00, 0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x08, 0x00, 0x00,
      0x00, 0x08, 0x00, 0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x08, 0x00,
      0x00, 0x80, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static char textedit_mask24[] = {
      0x80, 0xf7, 0x00, 0x00, 0xc0, 0xff, 0x01, 0x00, 0x80, 0xff, 0x00, 0x00, 0x00, 0x1c, 0x00,
      0x00, 0x00, 0x1c, 0x00, 0x00, 0x00, 0x1c, 0x00, 0x00, 0x00, 0x1c, 0x00, 0x00, 0x00, 0x1c,
      0x00, 0x00, 0x00, 0x1c, 0x00, 0x00, 0x00, 0x1c, 0x00, 0x00, 0x00, 0x1c, 0x00, 0x00, 0x00,
      0x1c, 0x00, 0x00, 0x00, 0x1c, 0x00, 0x00, 0x00, 0x1c, 0x00, 0x00, 0x00, 0x1c, 0x00, 0x00,
      0x00, 0x1c, 0x00, 0x00, 0x00, 0x1c, 0x00, 0x00, 0x00, 0x1c, 0x00, 0x00, 0x80, 0xff, 0x00,
      0x00, 0xc0, 0xff, 0x01, 0x00, 0x80, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static char textedit_bitmap32[] = {
      0x00, 0x00, 0x00, 0x00, 0x00, 0x3f, 0x3f, 0x00, 0x00, 0xff, 0x3f, 0x00, 0x00, 0xe0, 0x01,
      0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 0xc0,
      0x00, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x00,
      0xc0, 0x00, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 0xc0, 0x00, 0x00,
      0x00, 0xc0, 0x00, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 0xc0, 0x00,
      0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 0xc0,
      0x00, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x00,
      0xc0, 0x00, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 0xe0, 0x01, 0x00, 0x00, 0xff, 0x3f, 0x00,
      0x00, 0x3f, 0x3f, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static char textedit_mask32[] = {
      0x00, 0x3f, 0x3f, 0x00, 0x80, 0xff, 0x7f, 0x00, 0x80, 0xff, 0x7f, 0x00, 0x00, 0xff, 0x3f,
      0x00, 0x00, 0xf0, 0x03, 0x00, 0x00, 0xe0, 0x01, 0x00, 0x00, 0xe0, 0x01, 0x00, 0x00, 0xe0,
      0x01, 0x00, 0x00, 0xe0, 0x01, 0x00, 0x00, 0xe0, 0x01, 0x00, 0x00, 0xe0, 0x01, 0x00, 0x00,
      0xe0, 0x01, 0x00, 0x00, 0xe0, 0x01, 0x00, 0x00, 0xe0, 0x01, 0x00, 0x00, 0xe0, 0x01, 0x00,
      0x00, 0xe0, 0x01, 0x00, 0x00, 0xe0, 0x01, 0x00, 0x00, 0xe0, 0x01, 0x00, 0x00, 0xe0, 0x01,
      0x00, 0x00, 0xe0, 0x01, 0x00, 0x00, 0xe0, 0x01, 0x00, 0x00, 0xe0, 0x01, 0x00, 0x00, 0xe0,
      0x01, 0x00, 0x00, 0xe0, 0x01, 0x00, 0x00, 0xe0, 0x01, 0x00, 0x00, 0xe0, 0x01, 0x00, 0x00,
      0xe0, 0x01, 0x00, 0x00, 0xf0, 0x03, 0x00, 0x00, 0xff, 0x3f, 0x00, 0x80, 0xff, 0x7f, 0x00,
      0x80, 0xff, 0x7f, 0x00, 0x00, 0x3f, 0x3f, 0x00,
  };

  static BCursor TextEditCursor = {
      {textedit_bitmap16, textedit_bitmap24, textedit_bitmap32},
      {textedit_mask16, textedit_mask24, textedit_mask32},
      {{7, 7}, {11, 10}, {14, 15}},
      false,
  };

  BlenderCursor[WM_CURSOR_TEXT_EDIT] = &TextEditCursor;
  END_CURSOR_BLOCK;

  /********************** Paintbrush Cursor ***********************/
  BEGIN_CURSOR_BLOCK;

  static char paintbrush_bitmap16[] = {
      0x00, 0x00, 0x00, 0x30, 0x00, 0x78, 0x00, 0x74, 0x00, 0x2e, 0x00,
      0x1f, 0x80, 0x0f, 0xc0, 0x07, 0xe0, 0x03, 0xf0, 0x01, 0xf8, 0x00,
      0x7c, 0x00, 0x3e, 0x00, 0x1e, 0x00, 0x0e, 0x00, 0x00, 0x00,
  };

  static char paintbrush_mask16[] = {
      0x00, 0x30, 0x00, 0x78, 0x00, 0xfc, 0x00, 0xfe, 0x00, 0x7f, 0x80,
      0x3f, 0xc0, 0x1f, 0xe0, 0x0f, 0xf0, 0x07, 0xf8, 0x03, 0xfc, 0x01,
      0xfe, 0x00, 0x7f, 0x00, 0x3f, 0x00, 0x1f, 0x00, 0x0f, 0x00,
  };

  static char paintbrush_bitmap24[] = {
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x1c, 0x00, 0x00, 0x00, 0x3e,
      0x00, 0x00, 0x00, 0x7d, 0x00, 0x00, 0x80, 0x3b, 0x00, 0x00, 0xc0, 0x17, 0x00, 0x00, 0xe0,
      0x0f, 0x00, 0x00, 0xf0, 0x07, 0x00, 0x00, 0xf8, 0x03, 0x00, 0x00, 0xfc, 0x01, 0x00, 0x00,
      0xfe, 0x00, 0x00, 0x00, 0x7f, 0x00, 0x00, 0x80, 0x3f, 0x00, 0x00, 0xc0, 0x1f, 0x00, 0x00,
      0xe0, 0x0f, 0x00, 0x00, 0xf0, 0x07, 0x00, 0x00, 0xf8, 0x03, 0x00, 0x00, 0xfc, 0x01, 0x00,
      0x00, 0xfe, 0x00, 0x00, 0x00, 0x7e, 0x00, 0x00, 0x00, 0x3e, 0x00, 0x00, 0x00, 0x1e, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static char paintbrush_mask24[] = {
      0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x1c, 0x00, 0x00, 0x00, 0x3e, 0x00, 0x00, 0x00, 0x7f,
      0x00, 0x00, 0x80, 0xff, 0x00, 0x00, 0xc0, 0x7f, 0x00, 0x00, 0xe0, 0x3f, 0x00, 0x00, 0xf0,
      0x1f, 0x00, 0x00, 0xf8, 0x0f, 0x00, 0x00, 0xfc, 0x07, 0x00, 0x00, 0xfe, 0x03, 0x00, 0x00,
      0xff, 0x01, 0x00, 0x80, 0xff, 0x00, 0x00, 0xc0, 0x7f, 0x00, 0x00, 0xe0, 0x3f, 0x00, 0x00,
      0xf0, 0x1f, 0x00, 0x00, 0xf8, 0x0f, 0x00, 0x00, 0xfc, 0x07, 0x00, 0x00, 0xfe, 0x03, 0x00,
      0x00, 0xff, 0x01, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0x7f, 0x00, 0x00, 0x00, 0x3f, 0x00,
      0x00, 0x00, 0x1f, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static char paintbrush_bitmap32[] = {
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00,
      0x0f, 0x00, 0x00, 0x80, 0x1f, 0x00, 0x00, 0xc0, 0x3f, 0x00, 0x00, 0x80, 0x3f, 0x00, 0x00,
      0x10, 0x1f, 0x00, 0x00, 0x38, 0x0e, 0x00, 0x00, 0x7c, 0x04, 0x00, 0x00, 0xfe, 0x00, 0x00,
      0x00, 0xff, 0x01, 0x00, 0x80, 0xff, 0x00, 0x00, 0xc0, 0x7f, 0x00, 0x00, 0xe0, 0x3f, 0x00,
      0x00, 0xf0, 0x1f, 0x00, 0x00, 0xf8, 0x0f, 0x00, 0x00, 0xfc, 0x07, 0x00, 0x00, 0xfe, 0x03,
      0x00, 0x00, 0xff, 0x01, 0x00, 0x80, 0xff, 0x00, 0x00, 0xc0, 0x7f, 0x00, 0x00, 0xe0, 0x3f,
      0x00, 0x00, 0xf0, 0x1f, 0x00, 0x00, 0xf8, 0x0f, 0x00, 0x00, 0xfc, 0x07, 0x00, 0x00, 0xfe,
      0x03, 0x00, 0x00, 0xfe, 0x01, 0x00, 0x00, 0xfe, 0x00, 0x00, 0x00, 0x7e, 0x00, 0x00, 0x00,
      0x3e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static char paintbrush_mask32[] = {
      0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x0f, 0x00, 0x00, 0x80, 0x1f, 0x00, 0x00, 0xc0,
      0x3f, 0x00, 0x00, 0xe0, 0x7f, 0x00, 0x00, 0xf0, 0xff, 0x00, 0x00, 0xf8, 0xff, 0x00, 0x00,
      0xfc, 0x7f, 0x00, 0x00, 0xfe, 0x3f, 0x00, 0x00, 0xff, 0x1f, 0x00, 0x80, 0xff, 0x0f, 0x00,
      0xc0, 0xff, 0x07, 0x00, 0xe0, 0xff, 0x03, 0x00, 0xf0, 0xff, 0x01, 0x00, 0xf8, 0xff, 0x00,
      0x00, 0xfc, 0x7f, 0x00, 0x00, 0xfe, 0x3f, 0x00, 0x00, 0xff, 0x1f, 0x00, 0x80, 0xff, 0x0f,
      0x00, 0xc0, 0xff, 0x07, 0x00, 0xe0, 0xff, 0x03, 0x00, 0xf0, 0xff, 0x01, 0x00, 0xf8, 0xff,
      0x00, 0x00, 0xfc, 0x7f, 0x00, 0x00, 0xfe, 0x3f, 0x00, 0x00, 0xff, 0x1f, 0x00, 0x00, 0xff,
      0x0f, 0x00, 0x00, 0xff, 0x07, 0x00, 0x00, 0xff, 0x03, 0x00, 0x00, 0xff, 0x01, 0x00, 0x00,
      0xff, 0x00, 0x00, 0x00, 0x7f, 0x00, 0x00, 0x00,
  };

  static BCursor PaintBrushCursor = {
      {paintbrush_bitmap16, paintbrush_bitmap24, paintbrush_bitmap32},
      {paintbrush_mask16, paintbrush_mask24, paintbrush_mask32},
      {{0, 15}, {0, 23}, {0, 31}},
      false,
  };

  BlenderCursor[WM_CURSOR_PAINT_BRUSH] = &PaintBrushCursor;
  END_CURSOR_BLOCK;

  /********************** Eraser Cursor ***********************/
  BEGIN_CURSOR_BLOCK;

  static char eraser_bitmap16[] = {
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80,
      0x00, 0xc0, 0x01, 0xe0, 0x03, 0xf0, 0x07, 0xf8, 0x0f, 0xfc, 0x07,
      0xfe, 0x03, 0xfe, 0x01, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static char eraser_mask16[] = {
      0x00, 0x00, 0x00, 0x04, 0x00, 0x0e, 0x00, 0x1f, 0x80, 0x3f, 0xc0,
      0x7f, 0xe0, 0xff, 0xf0, 0x7f, 0xf8, 0x3f, 0xfc, 0x1f, 0xfe, 0x0f,
      0xff, 0x07, 0xff, 0x03, 0xff, 0x01, 0xff, 0x00, 0x00, 0x00,
  };

  static char eraser_bitmap24[] = {
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x01,
      0x00, 0x00, 0x40, 0x02, 0x00, 0x00, 0x20, 0x04, 0x00, 0x00, 0x10, 0x08, 0x00, 0x00, 0x08,
      0x10, 0x00, 0x00, 0x04, 0x20, 0x00, 0x00, 0x0a, 0x40, 0x00, 0x00, 0x1d, 0x80, 0x00, 0x80,
      0x3e, 0x40, 0x00, 0x40, 0x7f, 0x20, 0x00, 0xa0, 0xff, 0x10, 0x00, 0xd0, 0xff, 0x09, 0x00,
      0xe8, 0xff, 0x05, 0x00, 0xf4, 0xff, 0x02, 0x00, 0xfa, 0x7f, 0x01, 0x00, 0xfd, 0xbf, 0x00,
      0x00, 0xfd, 0x5f, 0x00, 0x00, 0xfd, 0x2f, 0x00, 0x00, 0xfd, 0x17, 0x00, 0x00, 0x01, 0x08,
      0x00, 0x00, 0xff, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static char eraser_mask24[] = {
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x01,
      0x00, 0x00, 0xc0, 0x03, 0x00, 0x00, 0xe0, 0x07, 0x00, 0x00, 0xf0, 0x0f, 0x00, 0x00, 0xf8,
      0x1f, 0x00, 0x00, 0xfc, 0x3f, 0x00, 0x00, 0xfe, 0x7f, 0x00, 0x00, 0xff, 0xff, 0x00, 0x80,
      0xff, 0x7f, 0x00, 0xc0, 0xff, 0x3f, 0x00, 0xe0, 0xff, 0x1f, 0x00, 0xf0, 0xff, 0x0f, 0x00,
      0xf8, 0xff, 0x07, 0x00, 0xfc, 0xff, 0x03, 0x00, 0xfe, 0xff, 0x01, 0x00, 0xff, 0xff, 0x00,
      0x00, 0xff, 0x7f, 0x00, 0x00, 0xff, 0x3f, 0x00, 0x00, 0xff, 0x1f, 0x00, 0x00, 0xff, 0x0f,
      0x00, 0x00, 0xff, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static char eraser_bitmap32[] = {
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x78,
      0x00, 0x00, 0x00, 0xcc, 0x00, 0x00, 0x00, 0x86, 0x01, 0x00, 0x00, 0x03, 0x03, 0x00, 0x80,
      0x01, 0x06, 0x00, 0xc0, 0x00, 0x0c, 0x00, 0x60, 0x00, 0x18, 0x00, 0x30, 0x00, 0x30, 0x00,
      0x18, 0x00, 0x60, 0x00, 0x4c, 0x00, 0xc0, 0x00, 0xe6, 0x00, 0xc0, 0x00, 0xf3, 0x01, 0x60,
      0x80, 0xf9, 0x03, 0x30, 0xc0, 0xfc, 0x07, 0x18, 0x60, 0xfe, 0x0f, 0x0c, 0x30, 0xff, 0x1f,
      0x06, 0x98, 0xff, 0x3f, 0x03, 0xcc, 0xff, 0x9f, 0x01, 0xe6, 0xff, 0xcf, 0x00, 0xf3, 0xff,
      0x67, 0x00, 0xf9, 0xff, 0x33, 0x00, 0xfd, 0xff, 0x19, 0x00, 0xfd, 0xff, 0x0c, 0x00, 0xfd,
      0x7f, 0x06, 0x00, 0xfd, 0x3f, 0x03, 0x00, 0x01, 0x80, 0x01, 0x00, 0xff, 0xff, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static char eraser_mask32[] = {
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x78,
      0x00, 0x00, 0x00, 0xfc, 0x00, 0x00, 0x00, 0xfe, 0x01, 0x00, 0x00, 0xff, 0x03, 0x00, 0x80,
      0xff, 0x07, 0x00, 0xc0, 0xff, 0x0f, 0x00, 0xe0, 0xff, 0x1f, 0x00, 0xf0, 0xff, 0x3f, 0x00,
      0xf8, 0xff, 0x7f, 0x00, 0xfc, 0xff, 0xff, 0x00, 0xfe, 0xff, 0xff, 0x00, 0xff, 0xff, 0x7f,
      0x80, 0xff, 0xff, 0x3f, 0xc0, 0xff, 0xff, 0x1f, 0xe0, 0xff, 0xff, 0x0f, 0xf0, 0xff, 0xff,
      0x07, 0xf8, 0xff, 0xff, 0x03, 0xfc, 0xff, 0xff, 0x01, 0xfe, 0xff, 0xff, 0x00, 0xff, 0xff,
      0x7f, 0x00, 0xff, 0xff, 0x3f, 0x00, 0xff, 0xff, 0x1f, 0x00, 0xff, 0xff, 0x0f, 0x00, 0xff,
      0xff, 0x07, 0x00, 0xff, 0xff, 0x03, 0x00, 0xff, 0xff, 0x01, 0x00, 0xff, 0xff, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static BCursor EraserCursor = {
      {eraser_bitmap16, eraser_bitmap24, eraser_bitmap32},
      {eraser_mask16, eraser_mask24, eraser_mask32},
      {{0, 14}, {0, 23}, {0, 28}},
      false,
  };

  BlenderCursor[WM_CURSOR_ERASER] = &EraserCursor;
  END_CURSOR_BLOCK;

  /********************** Hand Cursor ***********************/
  BEGIN_CURSOR_BLOCK;

  static char hand_bitmap16[] = {
      0x00, 0x00, 0x80, 0x01, 0x80, 0x0d, 0x98, 0x6d, 0xb8, 0x6d, 0xb0,
      0x6d, 0xb0, 0x6d, 0xe0, 0x6f, 0xe6, 0x7f, 0xee, 0x7f, 0x7c, 0x35,
      0x78, 0x35, 0x70, 0x15, 0x60, 0x15, 0xc0, 0x1f, 0xc0, 0x1f,
  };

  static char hand_mask16[] = {
      0x80, 0x01, 0xc0, 0x0f, 0xd8, 0x7f, 0xfc, 0xff, 0xfc, 0xff, 0xf8,
      0xff, 0xf8, 0xff, 0xf6, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x7f,
      0xfc, 0x7f, 0xf8, 0x3f, 0xf0, 0x3f, 0xe0, 0x3f, 0xe0, 0x3f,
  };

  static char hand_bitmap24[] = {
      0x00, 0x00, 0x00, 0x00, 0x00, 0x38, 0x00, 0x00, 0x00, 0x38, 0x00, 0x00, 0x80, 0x3b, 0x00,
      0x00, 0x80, 0xbb, 0x03, 0x00, 0x80, 0xbb, 0x03, 0x00, 0x80, 0xbb, 0x3b, 0x00, 0x80, 0xbb,
      0x3b, 0x00, 0x80, 0xbb, 0x3b, 0x00, 0x80, 0xbb, 0x3b, 0x00, 0x80, 0xbb, 0x3b, 0x00, 0x80,
      0xbb, 0x3b, 0x00, 0x8e, 0xbb, 0x3b, 0x00, 0x8e, 0xbb, 0x3b, 0x00, 0x9e, 0xff, 0x3b, 0x00,
      0x9c, 0xff, 0x3f, 0x00, 0xfc, 0xff, 0x3f, 0x00, 0xfc, 0xdb, 0x3e, 0x00, 0xf8, 0xdb, 0x1e,
      0x00, 0xf0, 0xdb, 0x1e, 0x00, 0xe0, 0xdb, 0x0e, 0x00, 0xc0, 0xff, 0x07, 0x00, 0x80, 0xff,
      0x07, 0x00, 0x00, 0xff, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static char hand_mask24[] = {
      0x00, 0x38, 0x00, 0x00, 0x00, 0x7c, 0x00, 0x00, 0x80, 0x7f, 0x00, 0x00, 0xc0, 0xff, 0x03,
      0x00, 0xc0, 0xff, 0x07, 0x00, 0xc0, 0xff, 0x3f, 0x00, 0xc0, 0xff, 0x7f, 0x00, 0xc0, 0xff,
      0x7f, 0x00, 0xc0, 0xff, 0x7f, 0x00, 0xc0, 0xff, 0x7f, 0x00, 0xc0, 0xff, 0x7f, 0x00, 0xce,
      0xff, 0x7f, 0x00, 0xcf, 0xff, 0x7f, 0x00, 0xdf, 0xff, 0x7f, 0x00, 0xff, 0xff, 0x7f, 0x00,
      0xfe, 0xff, 0x7f, 0x00, 0xfe, 0xff, 0x7f, 0x00, 0xfe, 0xff, 0x7f, 0x00, 0xfc, 0xff, 0x3f,
      0x00, 0xf8, 0xff, 0x3f, 0x00, 0xf0, 0xff, 0x1f, 0x00, 0xe0, 0xff, 0x0f, 0x00, 0xc0, 0xff,
      0x0f, 0x00, 0x80, 0xff, 0x0f, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static char hand_bitmap32[] = {
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x0f, 0x00, 0x00, 0x30, 0x0f,
      0x00, 0x00, 0x78, 0xcf, 0x00, 0x00, 0x78, 0xef, 0x01, 0x00, 0x78, 0xef, 0x01, 0x00, 0x78,
      0xef, 0x01, 0x00, 0x78, 0xef, 0x1d, 0x00, 0x78, 0xef, 0x3d, 0x00, 0x78, 0xef, 0x3d, 0x00,
      0x78, 0xef, 0x3d, 0x00, 0x78, 0xef, 0x3d, 0x00, 0x78, 0xef, 0x3d, 0x70, 0x78, 0xef, 0x3d,
      0xf0, 0x78, 0xef, 0x3d, 0xf0, 0xf8, 0xff, 0x3d, 0xf0, 0xf8, 0xff, 0x3d, 0xf0, 0xf8, 0xff,
      0x3f, 0xf0, 0xb9, 0xf7, 0x3f, 0xe0, 0xbb, 0xf7, 0x3f, 0xe0, 0xbf, 0xf7, 0x3e, 0xe0, 0xbf,
      0xf7, 0x3e, 0xc0, 0xbf, 0xf7, 0x3e, 0x80, 0xbf, 0xf7, 0x3e, 0x80, 0xbf, 0xf7, 0x3e, 0x00,
      0xbf, 0xf7, 0x1e, 0x00, 0xbe, 0xf7, 0x1e, 0x00, 0xfc, 0xff, 0x0f, 0x00, 0xfc, 0xff, 0x0f,
      0x00, 0xf8, 0xff, 0x07, 0x00, 0xf8, 0xff, 0x07,
  };

  static char hand_mask32[] = {
      0x00, 0x00, 0x0f, 0x00, 0x00, 0x80, 0x1f, 0x00, 0x00, 0xf8, 0x3f, 0x00, 0x00, 0xfc, 0xff,
      0x01, 0x00, 0xfc, 0xff, 0x03, 0x00, 0xfc, 0xff, 0x03, 0x00, 0xfc, 0xff, 0x03, 0x00, 0xfc,
      0xff, 0x3f, 0x00, 0xfc, 0xff, 0x7f, 0x00, 0xfc, 0xff, 0x7f, 0x00, 0xfc, 0xff, 0x7f, 0x00,
      0xfc, 0xff, 0x7f, 0x60, 0xfc, 0xff, 0x7f, 0xf8, 0xfc, 0xff, 0x7f, 0xf8, 0xfd, 0xff, 0x7f,
      0xfc, 0xfd, 0xff, 0x7f, 0xfc, 0xfd, 0xff, 0x7f, 0xfc, 0xff, 0xff, 0x7f, 0xf8, 0xff, 0xff,
      0x7f, 0xf8, 0xff, 0xff, 0x7f, 0xf8, 0xff, 0xff, 0x7f, 0xf8, 0xff, 0xff, 0x7f, 0xf0, 0xff,
      0xff, 0x7f, 0xe0, 0xff, 0xff, 0x7f, 0xe0, 0xff, 0xff, 0x7f, 0xc0, 0xff, 0xff, 0x7f, 0x80,
      0xff, 0xff, 0x7f, 0x80, 0xff, 0xff, 0x3f, 0x00, 0xff, 0xff, 0x3f, 0x00, 0xfe, 0xff, 0x1f,
      0x00, 0xfe, 0xff, 0x1f, 0x00, 0xfc, 0xff, 0x1f,
  };

  static BCursor HandCursor = {
      {hand_bitmap16, hand_bitmap24, hand_bitmap32},
      {hand_mask16, hand_mask24, hand_mask32},
      {{8, 8}, {11, 11}, {17, 17}},
      false,
  };

  BlenderCursor[WM_CURSOR_HAND] = &HandCursor;
  BlenderCursor[WM_CURSOR_HAND_CLOSED] = &HandCursor;
  BlenderCursor[WM_CURSOR_HAND_POINT] = &HandCursor;
  END_CURSOR_BLOCK;

  /********************** NSEW Scroll Cursor ***********************/
  BEGIN_CURSOR_BLOCK;

  static char nsewscroll_bitmap16[] = {
      0x00, 0x00, 0x80, 0x01, 0xc0, 0x03, 0x40, 0x02, 0x00, 0x00, 0x00,
      0x00, 0x0c, 0x30, 0x06, 0x60, 0x06, 0x60, 0x0c, 0x30, 0x00, 0x00,
      0x00, 0x00, 0x40, 0x02, 0xc0, 0x03, 0x80, 0x01, 0x00, 0x00,
  };

  static char nsewscroll_mask16[] = {
      0x80, 0x01, 0xc0, 0x03, 0xe0, 0x07, 0xe0, 0x07, 0x40, 0x02, 0x0c,
      0x30, 0x1e, 0x78, 0x0f, 0xf0, 0x0f, 0xf8, 0x1e, 0x78, 0x0c, 0x30,
      0x40, 0x02, 0xe0, 0x07, 0xe0, 0x07, 0xc0, 0x03, 0x80, 0x01,
  };

  static char nsewscroll_bitmap24[] = {
      0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x1c, 0x00, 0x00, 0x00, 0x3e, 0x00,
      0x00, 0x00, 0x77, 0x00, 0x00, 0x00, 0x22, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x10, 0x00, 0x04, 0x00, 0x38, 0x00, 0x0e, 0x00, 0x1c, 0x00, 0x1c, 0x00, 0x0e,
      0x00, 0x38, 0x00, 0x1c, 0x00, 0x1c, 0x00, 0x38, 0x00, 0x0e, 0x00, 0x10, 0x00, 0x04, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x22, 0x00, 0x00, 0x00, 0x77, 0x00,
      0x00, 0x00, 0x3e, 0x00, 0x00, 0x00, 0x1c, 0x00, 0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static char nsewscroll_mask24[] = {
      0x00, 0x08, 0x00, 0x00, 0x00, 0x1c, 0x00, 0x00, 0x00, 0x3e, 0x00, 0x00, 0x00, 0x7f, 0x00,
      0x00, 0x80, 0xff, 0x00, 0x00, 0x00, 0x77, 0x00, 0x00, 0x00, 0x22, 0x00, 0x00, 0x10, 0x00,
      0x04, 0x00, 0x38, 0x00, 0x0e, 0x00, 0x7c, 0x00, 0x1f, 0x00, 0x3e, 0x00, 0x3e, 0x00, 0x1f,
      0x00, 0x7c, 0x00, 0x3e, 0x00, 0x3e, 0x00, 0x7c, 0x00, 0x1f, 0x00, 0x38, 0x00, 0x0e, 0x00,
      0x10, 0x00, 0x04, 0x00, 0x00, 0x22, 0x00, 0x00, 0x00, 0x77, 0x00, 0x00, 0x80, 0xff, 0x00,
      0x00, 0x00, 0x7f, 0x00, 0x00, 0x00, 0x3e, 0x00, 0x00, 0x00, 0x1c, 0x00, 0x00, 0x00, 0x08,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static char nsewscroll_bitmap32[] = {
      0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x01, 0x00, 0x00, 0xc0, 0x03, 0x00, 0x00, 0xe0, 0x07,
      0x00, 0x00, 0xf0, 0x0f, 0x00, 0x00, 0xf8, 0x1f, 0x00, 0x00, 0x7c, 0x3e, 0x00, 0x00, 0x38,
      0x1c, 0x00, 0x00, 0x10, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x00, 0x00, 0x02, 0xe0,
      0x00, 0x00, 0x07, 0xf0, 0x01, 0x80, 0x0f, 0xf8, 0x00, 0x00, 0x1f, 0x7c, 0x00, 0x00, 0x3e,
      0x3e, 0x00, 0x00, 0x7c, 0x3e, 0x00, 0x00, 0x7c, 0x7c, 0x00, 0x00, 0x3e, 0xf8, 0x00, 0x00,
      0x1f, 0xf0, 0x01, 0x80, 0x0f, 0xe0, 0x00, 0x00, 0x07, 0x40, 0x00, 0x00, 0x02, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x10, 0x08, 0x00, 0x00, 0x38, 0x1c, 0x00, 0x00, 0x7c, 0x3e, 0x00, 0x00,
      0xf8, 0x1f, 0x00, 0x00, 0xf0, 0x0f, 0x00, 0x00, 0xe0, 0x07, 0x00, 0x00, 0xc0, 0x03, 0x00,
      0x00, 0x80, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static char nsewscroll_mask32[] = {
      0x00, 0x80, 0x01, 0x00, 0x00, 0xc0, 0x03, 0x00, 0x00, 0xe0, 0x07, 0x00, 0x00, 0xf0, 0x0f,
      0x00, 0x00, 0xf8, 0x1f, 0x00, 0x00, 0xfc, 0x3f, 0x00, 0x00, 0xfe, 0x7f, 0x00, 0x00, 0x7c,
      0x3e, 0x00, 0x00, 0x38, 0x1c, 0x00, 0x40, 0x10, 0x08, 0x02, 0xe0, 0x00, 0x00, 0x07, 0xf0,
      0x01, 0x80, 0x0f, 0xf8, 0x03, 0xc0, 0x1f, 0xfc, 0x01, 0x80, 0x3f, 0xfe, 0x00, 0x00, 0x7f,
      0x7f, 0x00, 0x00, 0xfe, 0x7f, 0x00, 0x00, 0xfe, 0xfe, 0x00, 0x00, 0x7f, 0xfc, 0x01, 0x80,
      0x3f, 0xf8, 0x03, 0xc0, 0x1f, 0xf0, 0x01, 0x80, 0x0f, 0xe0, 0x00, 0x00, 0x07, 0x40, 0x10,
      0x08, 0x02, 0x00, 0x38, 0x1c, 0x00, 0x00, 0x7c, 0x3e, 0x00, 0x00, 0xfe, 0x7f, 0x00, 0x00,
      0xfc, 0x3f, 0x00, 0x00, 0xf8, 0x1f, 0x00, 0x00, 0xf0, 0x0f, 0x00, 0x00, 0xe0, 0x07, 0x00,
      0x00, 0xc0, 0x03, 0x00, 0x00, 0x80, 0x01, 0x00,
  };

  static BCursor NSEWScrollCursor = {
      {nsewscroll_bitmap16, nsewscroll_bitmap24, nsewscroll_bitmap32},
      {nsewscroll_mask16, nsewscroll_mask24, nsewscroll_mask32},
      {{7, 7}, {11, 11}, {15, 15}},
      true,
  };

  BlenderCursor[WM_CURSOR_NSEW_SCROLL] = &NSEWScrollCursor;
  END_CURSOR_BLOCK;

  /********************** NS Scroll Cursor ***********************/
  BEGIN_CURSOR_BLOCK;

  static char nsscroll_bitmap16[] = {
      0x00, 0x00, 0x80, 0x00, 0xc0, 0x01, 0xe0, 0x03, 0x70, 0x07, 0x20,
      0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0x02,
      0x70, 0x07, 0xe0, 0x03, 0xc0, 0x01, 0x80, 0x00, 0x00, 0x00,
  };

  static char nsscroll_mask16[] = {
      0x80, 0x00, 0xc0, 0x01, 0xe0, 0x03, 0xf0, 0x07, 0xf8, 0x0f, 0x70,
      0x07, 0x20, 0x02, 0x00, 0x00, 0x00, 0x00, 0x20, 0x02, 0x70, 0x07,
      0xf8, 0x0f, 0xf0, 0x07, 0xe0, 0x03, 0xc0, 0x01, 0x80, 0x00,
  };

  static char nsscroll_bitmap24[] = {
      0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x1c, 0x00, 0x00, 0x00, 0x3e, 0x00,
      0x00, 0x00, 0x7f, 0x00, 0x00, 0x80, 0xf7, 0x00, 0x00, 0xc0, 0xe3, 0x01, 0x00, 0x80, 0xc1,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x80, 0xc1, 0x00, 0x00, 0xc0, 0xe3, 0x01, 0x00, 0x80, 0xf7, 0x00, 0x00, 0x00, 0x7f, 0x00,
      0x00, 0x00, 0x3e, 0x00, 0x00, 0x00, 0x1c, 0x00, 0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static char nsscroll_mask24[] = {
      0x00, 0x08, 0x00, 0x00, 0x00, 0x1c, 0x00, 0x00, 0x00, 0x3e, 0x00, 0x00, 0x00, 0x7f, 0x00,
      0x00, 0x80, 0xff, 0x00, 0x00, 0xc0, 0xff, 0x01, 0x00, 0xe0, 0xf7, 0x03, 0x00, 0xc0, 0xe3,
      0x01, 0x00, 0x80, 0xc1, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0xc1, 0x00, 0x00,
      0xc0, 0xe3, 0x01, 0x00, 0xe0, 0xf7, 0x03, 0x00, 0xc0, 0xff, 0x01, 0x00, 0x80, 0xff, 0x00,
      0x00, 0x00, 0x7f, 0x00, 0x00, 0x00, 0x3e, 0x00, 0x00, 0x00, 0x1c, 0x00, 0x00, 0x00, 0x08,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static char nsscroll_bitmap32[] = {
      0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x00, 0x00, 0x00, 0xe0, 0x00, 0x00, 0x00, 0xf0, 0x01,
      0x00, 0x00, 0xf8, 0x03, 0x00, 0x00, 0xfc, 0x07, 0x00, 0x00, 0xfe, 0x0f, 0x00, 0x00, 0xbf,
      0x1f, 0x00, 0x80, 0x1f, 0x3f, 0x00, 0x00, 0x0f, 0x1e, 0x00, 0x00, 0x06, 0x0c, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x0c, 0x00, 0x00, 0x0f,
      0x1e, 0x00, 0x80, 0x1f, 0x3f, 0x00, 0x00, 0xbf, 0x1f, 0x00, 0x00, 0xfe, 0x0f, 0x00, 0x00,
      0xfc, 0x07, 0x00, 0x00, 0xf8, 0x03, 0x00, 0x00, 0xf0, 0x01, 0x00, 0x00, 0xe0, 0x00, 0x00,
      0x00, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static char nsscroll_mask32[] = {
      0x00, 0x40, 0x00, 0x00, 0x00, 0xe0, 0x00, 0x00, 0x00, 0xf0, 0x01, 0x00, 0x00, 0xf8, 0x03,
      0x00, 0x00, 0xfc, 0x07, 0x00, 0x00, 0xfe, 0x0f, 0x00, 0x00, 0xff, 0x1f, 0x00, 0x80, 0xff,
      0x3f, 0x00, 0xc0, 0xbf, 0x7f, 0x00, 0x80, 0x1f, 0x3f, 0x00, 0x00, 0x0f, 0x1e, 0x00, 0x00,
      0x06, 0x0c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x0c, 0x00, 0x00, 0x0f, 0x1e, 0x00, 0x80, 0x1f,
      0x3f, 0x00, 0xc0, 0xbf, 0x7f, 0x00, 0x80, 0xff, 0x3f, 0x00, 0x00, 0xff, 0x1f, 0x00, 0x00,
      0xfe, 0x0f, 0x00, 0x00, 0xfc, 0x07, 0x00, 0x00, 0xf8, 0x03, 0x00, 0x00, 0xf0, 0x01, 0x00,
      0x00, 0xe0, 0x00, 0x00, 0x00, 0x40, 0x00, 0x00,
  };

  static BCursor NSScrollCursor = {
      {nsscroll_bitmap16, nsscroll_bitmap24, nsscroll_bitmap32},
      {nsscroll_mask16, nsscroll_mask24, nsscroll_mask32},
      {{7, 7}, {11, 11}, {14, 15}},
      true,
  };

  BlenderCursor[WM_CURSOR_NS_SCROLL] = &NSScrollCursor;
  END_CURSOR_BLOCK;

  /********************** EW Scroll Cursor ***********************/
  BEGIN_CURSOR_BLOCK;

  static char ewscroll_bitmap16[] = {
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 0x08, 0x38,
      0x1c, 0x1c, 0x38, 0x0e, 0x70, 0x1c, 0x38, 0x38, 0x1c, 0x10, 0x08,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static char ewscroll_mask16[] = {
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 0x08, 0x38, 0x1c, 0x7c,
      0x3e, 0x3e, 0x7c, 0x1f, 0xf8, 0x3e, 0x7c, 0x7c, 0x3e, 0x38, 0x1c,
      0x10, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static char ewscroll_bitmap24[] = {
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x00, 0x02, 0x00, 0xe0, 0x00, 0x07, 0x00, 0xf0,
      0x00, 0x0f, 0x00, 0x78, 0x00, 0x1e, 0x00, 0x3c, 0x00, 0x3c, 0x00, 0x1e, 0x00, 0x78, 0x00,
      0x3c, 0x00, 0x3c, 0x00, 0x78, 0x00, 0x1e, 0x00, 0xf0, 0x00, 0x0f, 0x00, 0xe0, 0x00, 0x07,
      0x00, 0x40, 0x00, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static char ewscroll_mask24[] = {
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x40, 0x00, 0x02, 0x00, 0xe0, 0x00, 0x07, 0x00, 0xf0, 0x81, 0x0f, 0x00, 0xf8,
      0x81, 0x1f, 0x00, 0xfc, 0x00, 0x3f, 0x00, 0x7e, 0x00, 0x7e, 0x00, 0x3f, 0x00, 0xfc, 0x00,
      0x7e, 0x00, 0x7e, 0x00, 0xfc, 0x00, 0x3f, 0x00, 0xf8, 0x81, 0x1f, 0x00, 0xf0, 0x81, 0x0f,
      0x00, 0xe0, 0x00, 0x07, 0x00, 0x40, 0x00, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static char ewscroll_bitmap32[] = {
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01,
      0x80, 0x00, 0x80, 0x03, 0xc0, 0x01, 0xc0, 0x07, 0xe0, 0x03, 0xe0, 0x07, 0xe0, 0x07, 0xf0,
      0x03, 0xc0, 0x0f, 0xf8, 0x01, 0x80, 0x1f, 0xfc, 0x00, 0x00, 0x3f, 0x7e, 0x00, 0x00, 0x7e,
      0xfc, 0x00, 0x00, 0x3f, 0xf8, 0x01, 0x80, 0x1f, 0xf0, 0x03, 0xc0, 0x0f, 0xe0, 0x07, 0xe0,
      0x07, 0xc0, 0x07, 0xe0, 0x03, 0x80, 0x03, 0xc0, 0x01, 0x00, 0x01, 0x80, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static char ewscroll_mask32[] = {
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x80, 0x00, 0x80, 0x03,
      0xc0, 0x01, 0xc0, 0x07, 0xe0, 0x03, 0xe0, 0x0f, 0xf0, 0x07, 0xf0, 0x0f, 0xf0, 0x0f, 0xf8,
      0x07, 0xe0, 0x1f, 0xfc, 0x03, 0xc0, 0x3f, 0xfe, 0x01, 0x80, 0x7f, 0xff, 0x00, 0x00, 0xff,
      0xfe, 0x01, 0x80, 0x7f, 0xfc, 0x03, 0xc0, 0x3f, 0xf8, 0x07, 0xe0, 0x1f, 0xf0, 0x0f, 0xf0,
      0x0f, 0xe0, 0x0f, 0xf0, 0x07, 0xc0, 0x07, 0xe0, 0x03, 0x80, 0x03, 0xc0, 0x01, 0x00, 0x01,
      0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static BCursor EWScrollCursor = {
      {ewscroll_bitmap16, ewscroll_bitmap24, ewscroll_bitmap32},
      {ewscroll_mask16, ewscroll_mask24, ewscroll_mask32},
      {{7, 7}, {11, 14}, {15, 15}},
      true,
  };

  BlenderCursor[WM_CURSOR_EW_SCROLL] = &EWScrollCursor;
  END_CURSOR_BLOCK;

  /********************** Eyedropper Cursor ***********************/
  BEGIN_CURSOR_BLOCK;

  static char eyedropper_bitmap16[] = {
      0x00, 0x00, 0x00, 0x60, 0x00, 0x70, 0x00, 0x3a, 0x00, 0x17, 0x00,
      0x0e, 0x00, 0x1d, 0x80, 0x0b, 0xc0, 0x01, 0xe0, 0x00, 0x70, 0x00,
      0x38, 0x00, 0x1c, 0x00, 0x0c, 0x00, 0x02, 0x00, 0x00, 0x00,
  };

  static char eyedropper_mask16[] = {
      0x00, 0x60, 0x00, 0xf0, 0x00, 0xfa, 0x00, 0x7f, 0x80, 0x3f, 0x00,
      0x1f, 0x80, 0x3f, 0xc0, 0x1f, 0xe0, 0x0b, 0xf0, 0x01, 0xf8, 0x00,
      0x7c, 0x00, 0x3e, 0x00, 0x1e, 0x00, 0x0f, 0x00, 0x03, 0x00,
  };

  static char eyedropper_bitmap24[] = {
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x3c, 0x00, 0x00, 0x00, 0x7e,
      0x00, 0x00, 0x00, 0x7f, 0x00, 0x00, 0x60, 0x3f, 0x00, 0x00, 0xf0, 0x1e, 0x00, 0x00, 0xe0,
      0x0d, 0x00, 0x00, 0xc0, 0x03, 0x00, 0x00, 0xb0, 0x07, 0x00, 0x00, 0x78, 0x07, 0x00, 0x00,
      0x7c, 0x02, 0x00, 0x00, 0x3e, 0x00, 0x00, 0x00, 0x1f, 0x00, 0x00, 0x80, 0x0d, 0x00, 0x00,
      0xc0, 0x06, 0x00, 0x00, 0x60, 0x03, 0x00, 0x00, 0xb0, 0x01, 0x00, 0x00, 0xd8, 0x00, 0x00,
      0x00, 0x7c, 0x00, 0x00, 0x00, 0x3c, 0x00, 0x00, 0x00, 0x1c, 0x00, 0x00, 0x00, 0x02, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static char eyedropper_mask24[] = {
      0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x3c, 0x00, 0x00, 0x00, 0x7e, 0x00, 0x00, 0x00, 0xff,
      0x00, 0x00, 0xe0, 0xff, 0x00, 0x00, 0xf0, 0x7f, 0x00, 0x00, 0xf8, 0x3f, 0x00, 0x00, 0xf0,
      0x1f, 0x00, 0x00, 0xf0, 0x0f, 0x00, 0x00, 0xf8, 0x0f, 0x00, 0x00, 0xfc, 0x0f, 0x00, 0x00,
      0xfe, 0x07, 0x00, 0x00, 0x7f, 0x02, 0x00, 0x80, 0x3f, 0x00, 0x00, 0xc0, 0x1f, 0x00, 0x00,
      0xe0, 0x0f, 0x00, 0x00, 0xf0, 0x07, 0x00, 0x00, 0xf8, 0x03, 0x00, 0x00, 0xfc, 0x01, 0x00,
      0x00, 0xfe, 0x00, 0x00, 0x00, 0x7e, 0x00, 0x00, 0x00, 0x3f, 0x00, 0x00, 0x00, 0x1f, 0x00,
      0x00, 0x00, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static char eyedropper_bitmap32[] = {
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1c, 0x00, 0x00, 0x00, 0x3e, 0x00, 0x00, 0x00,
      0x7f, 0x00, 0x00, 0x80, 0x7f, 0x00, 0x00, 0xc0, 0x7f, 0x00, 0x00, 0xe4, 0x3f, 0x00, 0x00,
      0xee, 0x1f, 0x00, 0x00, 0xdf, 0x0f, 0x00, 0x00, 0xbf, 0x07, 0x00, 0x00, 0x7e, 0x03, 0x00,
      0x00, 0xfc, 0x00, 0x00, 0x00, 0xfb, 0x01, 0x00, 0x80, 0xf7, 0x03, 0x00, 0xc0, 0xef, 0x01,
      0x00, 0xe0, 0xcf, 0x00, 0x00, 0xf0, 0x07, 0x00, 0x00, 0xb8, 0x03, 0x00, 0x00, 0xdc, 0x01,
      0x00, 0x00, 0xee, 0x00, 0x00, 0x00, 0x77, 0x00, 0x00, 0x80, 0x3b, 0x00, 0x00, 0xc0, 0x1d,
      0x00, 0x00, 0xe0, 0x0e, 0x00, 0x00, 0x70, 0x07, 0x00, 0x00, 0xf8, 0x03, 0x00, 0x00, 0xf8,
      0x01, 0x00, 0x00, 0xf8, 0x00, 0x00, 0x00, 0x78, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00,
      0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static char eyedropper_mask32[] = {
      0x00, 0x00, 0x00, 0x1c, 0x00, 0x00, 0x00, 0x3e, 0x00, 0x00, 0x00, 0x7f, 0x00, 0x00, 0x80,
      0xff, 0x00, 0x00, 0xc0, 0xff, 0x00, 0x00, 0xe4, 0xff, 0x00, 0x00, 0xfe, 0x7f, 0x00, 0x00,
      0xff, 0x3f, 0x00, 0x80, 0xff, 0x1f, 0x00, 0x80, 0xff, 0x0f, 0x00, 0x00, 0xff, 0x07, 0x00,
      0x00, 0xff, 0x03, 0x00, 0x80, 0xff, 0x03, 0x00, 0xc0, 0xff, 0x07, 0x00, 0xe0, 0xff, 0x03,
      0x00, 0xf0, 0xff, 0x01, 0x00, 0xf8, 0xcf, 0x00, 0x00, 0xfc, 0x07, 0x00, 0x00, 0xfe, 0x03,
      0x00, 0x00, 0xff, 0x01, 0x00, 0x80, 0xff, 0x00, 0x00, 0xc0, 0x7f, 0x00, 0x00, 0xe0, 0x3f,
      0x00, 0x00, 0xf0, 0x1f, 0x00, 0x00, 0xf8, 0x0f, 0x00, 0x00, 0xfc, 0x07, 0x00, 0x00, 0xfc,
      0x03, 0x00, 0x00, 0xfc, 0x01, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0x7f, 0x00, 0x00, 0x00,
      0x0f, 0x00, 0x00, 0x00, 0x0f, 0x00, 0x00, 0x00,
  };

  static BCursor EyedropperCursor = {
      {eyedropper_bitmap16, eyedropper_bitmap24, eyedropper_bitmap32},
      {eyedropper_mask16, eyedropper_mask24, eyedropper_mask32},
      {{0, 15}, {1, 22}, {1, 30}},
      false,
  };

  BlenderCursor[WM_CURSOR_EYEDROPPER] = &EyedropperCursor;
  END_CURSOR_BLOCK;

  /********************** Swap Area Cursor ***********************/
  BEGIN_CURSOR_BLOCK;

  static char swap_bitmap16[] = {
      0xc0, 0xff, 0x40, 0x80, 0x40, 0xbc, 0x40, 0xb8, 0x40, 0xb8, 0x40,
      0xa4, 0x00, 0x82, 0xfe, 0x81, 0x7e, 0x81, 0xbe, 0xfd, 0xda, 0x01,
      0xe2, 0x01, 0xe2, 0x01, 0xc2, 0x01, 0xfe, 0x01, 0x00, 0x00,
  };

  static char swap_mask16[] = {
      0xc0, 0xff, 0xc0, 0xff, 0xc0, 0xff, 0xc0, 0xff, 0xc0, 0xff, 0xc0,
      0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x03,
      0xff, 0x03, 0xff, 0x03, 0xff, 0x03, 0xff, 0x03, 0xff, 0x03,
  };

  static char swap_bitmap24[] = {
      0x00, 0xfe, 0xff, 0x00, 0x00, 0x02, 0x80, 0x00, 0x00, 0x82, 0xbf, 0x00, 0x00, 0x02, 0xbf,
      0x00, 0x00, 0x02, 0xbe, 0x00, 0x00, 0x02, 0xbe, 0x00, 0x00, 0x02, 0xbf, 0x00, 0x00, 0x82,
      0xb3, 0x00, 0x00, 0xc2, 0xa1, 0x00, 0x00, 0xe0, 0x80, 0x00, 0xfe, 0x7f, 0x80, 0x00, 0xfe,
      0x37, 0x80, 0x00, 0xfe, 0x23, 0x80, 0x00, 0xfe, 0x31, 0x80, 0x00, 0xfe, 0xb8, 0xff, 0x00,
      0x7a, 0x3c, 0x00, 0x00, 0x32, 0x3e, 0x00, 0x00, 0x02, 0x3f, 0x00, 0x00, 0x82, 0x3f, 0x00,
      0x00, 0x82, 0x3f, 0x00, 0x00, 0x02, 0x3f, 0x00, 0x00, 0x02, 0x3e, 0x00, 0x00, 0xfe, 0x3f,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static char swap_mask24[] = {
      0x00, 0xfe, 0xff, 0x00, 0x00, 0xfe, 0xff, 0x00, 0x00, 0xfe, 0xff, 0x00, 0x00, 0xfe, 0xff,
      0x00, 0x00, 0xfe, 0xff, 0x00, 0x00, 0xfe, 0xff, 0x00, 0x00, 0xfe, 0xff, 0x00, 0x00, 0xfe,
      0xff, 0x00, 0x00, 0xfe, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff,
      0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00,
      0xff, 0x7f, 0x00, 0x00, 0xff, 0x7f, 0x00, 0x00, 0xff, 0x7f, 0x00, 0x00, 0xff, 0x7f, 0x00,
      0x00, 0xff, 0x7f, 0x00, 0x00, 0xff, 0x7f, 0x00, 0x00, 0xff, 0x7f, 0x00, 0x00, 0xff, 0x7f,
      0x00, 0x00, 0xff, 0x7f, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static char swap_bitmap32[] = {
      0x00, 0xe0, 0xff, 0xff, 0x00, 0x20, 0x00, 0x80, 0x00, 0x20, 0x00, 0x80, 0x00, 0x20, 0xf8,
      0x9f, 0x00, 0x20, 0xf0, 0x9f, 0x00, 0x20, 0xe0, 0x9f, 0x00, 0x20, 0xc0, 0x9f, 0x00, 0x20,
      0x80, 0x9f, 0x00, 0x20, 0xc0, 0x9f, 0x00, 0x20, 0xe0, 0x9e, 0x00, 0x20, 0x70, 0x9c, 0x00,
      0x20, 0x38, 0x98, 0x00, 0x20, 0x1c, 0x90, 0x00, 0x00, 0x0e, 0x80, 0xfe, 0xff, 0x07, 0x80,
      0xfe, 0x7f, 0x03, 0x80, 0xfe, 0x3f, 0x02, 0x80, 0xfe, 0x1f, 0x03, 0x80, 0xfe, 0x8f, 0xfb,
      0xff, 0xf6, 0xc7, 0x03, 0x00, 0xe6, 0xe3, 0x03, 0x00, 0xc6, 0xf1, 0x03, 0x00, 0x86, 0xf8,
      0x03, 0x00, 0x06, 0xfc, 0x03, 0x00, 0x06, 0xfe, 0x03, 0x00, 0x06, 0xfc, 0x03, 0x00, 0x06,
      0xf8, 0x03, 0x00, 0x06, 0xf0, 0x03, 0x00, 0x06, 0xe0, 0x03, 0x00, 0xfe, 0xff, 0x03, 0x00,
      0xfe, 0xff, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static char swap_mask32[] = {
      0x00, 0xe0, 0xff, 0xff, 0x00, 0xe0, 0xff, 0xff, 0x00, 0xe0, 0xff, 0xff, 0x00, 0xe0, 0xff,
      0xff, 0x00, 0xe0, 0xff, 0xff, 0x00, 0xe0, 0xff, 0xff, 0x00, 0xe0, 0xff, 0xff, 0x00, 0xe0,
      0xff, 0xff, 0x00, 0xe0, 0xff, 0xff, 0x00, 0xe0, 0xff, 0xff, 0x00, 0xe0, 0xff, 0xff, 0x00,
      0xe0, 0xff, 0xff, 0x00, 0xe0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
      0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
      0xff, 0xff, 0xff, 0x07, 0x00, 0xff, 0xff, 0x07, 0x00, 0xff, 0xff, 0x07, 0x00, 0xff, 0xff,
      0x07, 0x00, 0xff, 0xff, 0x07, 0x00, 0xff, 0xff, 0x07, 0x00, 0xff, 0xff, 0x07, 0x00, 0xff,
      0xff, 0x07, 0x00, 0xff, 0xff, 0x07, 0x00, 0xff, 0xff, 0x07, 0x00, 0xff, 0xff, 0x07, 0x00,
      0xff, 0xff, 0x07, 0x00, 0xff, 0xff, 0x07, 0x00,
  };

  static BCursor SwapCursor = {
      {swap_bitmap16, swap_bitmap24, swap_bitmap32},
      {swap_mask16, swap_mask24, swap_mask32},
      {{7, 7}, {11, 11}, {15, 15}},
      false,
  };

  BlenderCursor[WM_CURSOR_SWAP_AREA] = &SwapCursor;
  END_CURSOR_BLOCK;

  /********************** Vertical Split Cursor ***********************/
  BEGIN_CURSOR_BLOCK;

  static char vsplit_bitmap16[] = {
      0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x88,
      0x11, 0x8c, 0x31, 0x86, 0x61, 0x86, 0x61, 0x8c, 0x31, 0x88, 0x11,
      0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01,
  };

  static char vsplit_mask16[] = {
      0xe0, 0x07, 0xc0, 0x03, 0xc0, 0x03, 0xc0, 0x03, 0xc8, 0x13, 0xdc,
      0x3b, 0xde, 0x7b, 0xcf, 0xf3, 0xcf, 0xf3, 0xde, 0x7b, 0xdc, 0x3b,
      0xc8, 0x13, 0xc0, 0x03, 0xc0, 0x03, 0xc0, 0x03, 0xe0, 0x07,
  };

  static char vsplit_bitmap24[] = {
      0x00, 0x00, 0x00, 0x00, 0x00, 0x81, 0x00, 0x00, 0x00, 0xc3, 0x00, 0x00, 0x00, 0xc3, 0x00,
      0x00, 0x00, 0xc3, 0x00, 0x00, 0x00, 0xc3, 0x00, 0x00, 0x00, 0xc3, 0x00, 0x00, 0x20, 0xc3,
      0x04, 0x00, 0x30, 0xc3, 0x0c, 0x00, 0x38, 0xc3, 0x1c, 0x00, 0x1c, 0xc3, 0x38, 0x00, 0x0e,
      0xc3, 0x70, 0x00, 0x1c, 0xc3, 0x38, 0x00, 0x38, 0xc3, 0x1c, 0x00, 0x30, 0xc3, 0x0c, 0x00,
      0x20, 0xc3, 0x04, 0x00, 0x00, 0xc3, 0x00, 0x00, 0x00, 0xc3, 0x00, 0x00, 0x00, 0xc3, 0x00,
      0x00, 0x00, 0xc3, 0x00, 0x00, 0x00, 0xc3, 0x00, 0x00, 0x00, 0x81, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static char vsplit_mask24[] = {
      0x00, 0x81, 0x00, 0x00, 0x00, 0xc3, 0x00, 0x00, 0x00, 0xe7, 0x00, 0x00, 0x00, 0xe7, 0x00,
      0x00, 0x00, 0xe7, 0x00, 0x00, 0x00, 0xe7, 0x00, 0x00, 0x20, 0xe7, 0x04, 0x00, 0x70, 0xe7,
      0x0e, 0x00, 0x78, 0xe7, 0x1e, 0x00, 0x7c, 0xe7, 0x3e, 0x00, 0x3e, 0xe7, 0x7c, 0x00, 0x1f,
      0xe7, 0xf8, 0x00, 0x3e, 0xe7, 0x7c, 0x00, 0x7c, 0xe7, 0x3e, 0x00, 0x78, 0xe7, 0x1e, 0x00,
      0x70, 0xe7, 0x0e, 0x00, 0x20, 0xe7, 0x04, 0x00, 0x00, 0xe7, 0x00, 0x00, 0x00, 0xe7, 0x00,
      0x00, 0x00, 0xe7, 0x00, 0x00, 0x00, 0xe7, 0x00, 0x00, 0x00, 0xc3, 0x00, 0x00, 0x00, 0x81,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static char vsplit_bitmap32[] = {
      0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0x18, 0x00, 0x00, 0x38, 0x1c, 0x00, 0x00, 0x38, 0x1c,
      0x00, 0x00, 0x38, 0x1c, 0x00, 0x00, 0x38, 0x1c, 0x00, 0x00, 0x38, 0x1c, 0x00, 0x00, 0x38,
      0x1c, 0x00, 0x00, 0x38, 0x1c, 0x00, 0x80, 0x38, 0x1c, 0x01, 0xc0, 0x39, 0x9c, 0x03, 0xe0,
      0x3b, 0xdc, 0x07, 0xf0, 0x39, 0x9c, 0x0f, 0xf8, 0x38, 0x1c, 0x1f, 0x7c, 0x38, 0x1c, 0x3e,
      0x3e, 0x38, 0x1c, 0x7c, 0x3e, 0x38, 0x1c, 0x7c, 0x7c, 0x38, 0x1c, 0x3e, 0xf8, 0x38, 0x1c,
      0x1f, 0xf0, 0x39, 0x9c, 0x0f, 0xe0, 0x3b, 0xdc, 0x07, 0xc0, 0x39, 0x9c, 0x03, 0x80, 0x38,
      0x1c, 0x01, 0x00, 0x38, 0x1c, 0x00, 0x00, 0x38, 0x1c, 0x00, 0x00, 0x38, 0x1c, 0x00, 0x00,
      0x38, 0x1c, 0x00, 0x00, 0x38, 0x1c, 0x00, 0x00, 0x38, 0x1c, 0x00, 0x00, 0x38, 0x1c, 0x00,
      0x00, 0x18, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static char vsplit_mask32[] = {
      0x00, 0x18, 0x18, 0x00, 0x00, 0x38, 0x1c, 0x00, 0x00, 0x78, 0x1e, 0x00, 0x00, 0x78, 0x1e,
      0x00, 0x00, 0x78, 0x1e, 0x00, 0x00, 0x78, 0x1e, 0x00, 0x00, 0x78, 0x1e, 0x00, 0x00, 0x78,
      0x1e, 0x00, 0x80, 0x78, 0x1e, 0x01, 0xc0, 0x79, 0x9e, 0x03, 0xe0, 0x7b, 0xde, 0x07, 0xf0,
      0x7f, 0xfe, 0x0f, 0xf8, 0x7b, 0xde, 0x1f, 0xfc, 0x79, 0x9e, 0x3f, 0xfe, 0x78, 0x1e, 0x7f,
      0x7f, 0x78, 0x1e, 0xfe, 0x7f, 0x78, 0x1e, 0xfe, 0xfe, 0x78, 0x1e, 0x7f, 0xfc, 0x79, 0x9e,
      0x3f, 0xf8, 0x7b, 0xde, 0x1f, 0xf0, 0x7f, 0xfe, 0x0f, 0xe0, 0x7b, 0xde, 0x07, 0xc0, 0x79,
      0x9e, 0x03, 0x80, 0x78, 0x1e, 0x01, 0x00, 0x78, 0x1e, 0x00, 0x00, 0x78, 0x1e, 0x00, 0x00,
      0x78, 0x1e, 0x00, 0x00, 0x78, 0x1e, 0x00, 0x00, 0x78, 0x1e, 0x00, 0x00, 0x78, 0x1e, 0x00,
      0x00, 0x38, 0x1c, 0x00, 0x00, 0x18, 0x18, 0x00,
  };

  static BCursor VSplitCursor = {
      {vsplit_bitmap16, vsplit_bitmap24, vsplit_bitmap32},
      {vsplit_mask16, vsplit_mask24, vsplit_mask32},
      {{7, 7}, {11, 11}, {15, 15}},
      true,
  };

  BlenderCursor[WM_CURSOR_V_SPLIT] = &VSplitCursor;
  END_CURSOR_BLOCK;

  /********************** Horizontal Split Cursor ***********************/
  BEGIN_CURSOR_BLOCK;

  static char hsplit_bitmap16[] = {
      0x00, 0x00, 0x80, 0x01, 0xc0, 0x03, 0x60, 0x06, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0xff, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x60, 0x06, 0xc0, 0x03, 0x80, 0x01, 0x00, 0x00,
  };

  static char hsplit_mask16[] = {
      0x80, 0x01, 0xc0, 0x03, 0xe0, 0x07, 0xf0, 0x0f, 0x60, 0x06, 0x01,
      0x80, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x01, 0x80,
      0x60, 0x06, 0xf0, 0x0f, 0xe0, 0x07, 0xc0, 0x03, 0x80, 0x01,
  };

  static char hsplit_bitmap24[] = {
      0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x1c, 0x00, 0x00, 0x00, 0x3e, 0x00,
      0x00, 0x00, 0x77, 0x00, 0x00, 0x80, 0xe3, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0xfe, 0xff, 0x3f, 0x00, 0xfc, 0xff, 0x1f, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfc, 0xff, 0x1f, 0x00,
      0xfe, 0xff, 0x3f, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0xe3, 0x00,
      0x00, 0x00, 0x77, 0x00, 0x00, 0x00, 0x3e, 0x00, 0x00, 0x00, 0x1c, 0x00, 0x00, 0x00, 0x08,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static char hsplit_mask24[] = {
      0x00, 0x08, 0x00, 0x00, 0x00, 0x1c, 0x00, 0x00, 0x00, 0x3e, 0x00, 0x00, 0x00, 0x7f, 0x00,
      0x00, 0x80, 0xff, 0x00, 0x00, 0xc0, 0xf7, 0x01, 0x00, 0x80, 0xe3, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0xff, 0xff, 0x7f, 0x00, 0xfe, 0xff, 0x3f, 0x00, 0xfc, 0xff, 0x1f, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfc, 0xff, 0x1f, 0x00, 0xfe, 0xff, 0x3f, 0x00,
      0xff, 0xff, 0x7f, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0xe3, 0x00, 0x00, 0xc0, 0xf7, 0x01,
      0x00, 0x80, 0xff, 0x00, 0x00, 0x00, 0x7f, 0x00, 0x00, 0x00, 0x3e, 0x00, 0x00, 0x00, 0x1c,
      0x00, 0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static char hsplit_bitmap32[] = {
      0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x01, 0x00, 0x00, 0xc0, 0x03, 0x00, 0x00, 0xe0, 0x07,
      0x00, 0x00, 0xf0, 0x0f, 0x00, 0x00, 0xf8, 0x1f, 0x00, 0x00, 0x7c, 0x3e, 0x00, 0x00, 0x3e,
      0x7c, 0x00, 0x00, 0x1c, 0x38, 0x00, 0x00, 0x08, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfe,
      0xff, 0xff, 0x7f, 0xfe, 0xff, 0xff, 0x7f, 0xfc, 0xff, 0xff, 0x3f, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfc, 0xff, 0xff,
      0x3f, 0xfe, 0xff, 0xff, 0x7f, 0xfe, 0xff, 0xff, 0x7f, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08,
      0x10, 0x00, 0x00, 0x1c, 0x38, 0x00, 0x00, 0x3e, 0x7c, 0x00, 0x00, 0x7c, 0x3e, 0x00, 0x00,
      0xf8, 0x1f, 0x00, 0x00, 0xf0, 0x0f, 0x00, 0x00, 0xe0, 0x07, 0x00, 0x00, 0xc0, 0x03, 0x00,
      0x00, 0x80, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static char hsplit_mask32[] = {
      0x00, 0x80, 0x01, 0x00, 0x00, 0xc0, 0x03, 0x00, 0x00, 0xe0, 0x07, 0x00, 0x00, 0xf0, 0x0f,
      0x00, 0x00, 0xf8, 0x1f, 0x00, 0x00, 0xfc, 0x3f, 0x00, 0x00, 0xfe, 0x7f, 0x00, 0x00, 0x7f,
      0xfe, 0x00, 0x00, 0x3e, 0x7c, 0x00, 0x00, 0x1c, 0x38, 0x00, 0x00, 0x08, 0x10, 0x00, 0xff,
      0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff, 0x7f, 0xfc, 0xff, 0xff, 0x3f,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfc, 0xff, 0xff, 0x3f, 0xfe, 0xff, 0xff,
      0x7f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x00, 0x08, 0x10, 0x00, 0x00, 0x1c,
      0x38, 0x00, 0x00, 0x3e, 0x7c, 0x00, 0x00, 0x7f, 0xfe, 0x00, 0x00, 0xfe, 0x7f, 0x00, 0x00,
      0xfc, 0x3f, 0x00, 0x00, 0xf8, 0x1f, 0x00, 0x00, 0xf0, 0x0f, 0x00, 0x00, 0xe0, 0x07, 0x00,
      0x00, 0xc0, 0x03, 0x00, 0x00, 0x80, 0x01, 0x00,
  };

  static BCursor HSplitCursor = {
      {hsplit_bitmap16, hsplit_bitmap24, hsplit_bitmap32},
      {hsplit_mask16, hsplit_mask24, hsplit_mask32},
      {{7, 7}, {11, 11}, {15, 15}},
      true,
  };

  BlenderCursor[WM_CURSOR_H_SPLIT] = &HSplitCursor;
  END_CURSOR_BLOCK;

  /********************** North Arrow Cursor ***********************/
  BEGIN_CURSOR_BLOCK;

  static char narrow_bitmap16[] = {
      0x00, 0x00, 0x80, 0x00, 0xc0, 0x01, 0xe0, 0x03, 0xf0, 0x07, 0xf8,
      0x0f, 0x7c, 0x1f, 0x3e, 0x3e, 0x1c, 0x1c, 0x08, 0x08, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static char narrow_mask16[] = {
      0x80, 0x00, 0xc0, 0x01, 0xe0, 0x03, 0xf0, 0x07, 0xf8, 0x0f, 0xfc,
      0x1f, 0xfe, 0x3f, 0x7f, 0x7f, 0x3e, 0x3e, 0x1c, 0x1c, 0x08, 0x08,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static char narrow_bitmap24[] = {
      0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x1c, 0x00, 0x00, 0x00, 0x3e, 0x00,
      0x00, 0x00, 0x7f, 0x00, 0x00, 0x80, 0xff, 0x00, 0x00, 0xc0, 0xff, 0x01, 0x00, 0xe0, 0xff,
      0x03, 0x00, 0xf0, 0xf7, 0x07, 0x00, 0xf8, 0xe3, 0x0f, 0x00, 0xfc, 0xc1, 0x1f, 0x00, 0xfe,
      0x80, 0x3f, 0x00, 0x7c, 0x00, 0x1f, 0x00, 0x38, 0x00, 0x0e, 0x00, 0x10, 0x00, 0x04, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static char narrow_mask24[] = {
      0x00, 0x08, 0x00, 0x00, 0x00, 0x1c, 0x00, 0x00, 0x00, 0x3e, 0x00, 0x00, 0x00, 0x7f, 0x00,
      0x00, 0x80, 0xff, 0x00, 0x00, 0xc0, 0xff, 0x01, 0x00, 0xe0, 0xff, 0x03, 0x00, 0xf0, 0xff,
      0x07, 0x00, 0xf8, 0xff, 0x0f, 0x00, 0xfc, 0xf7, 0x1f, 0x00, 0xfe, 0xe3, 0x3f, 0x00, 0xff,
      0xc1, 0x7f, 0x00, 0xfe, 0x80, 0x3f, 0x00, 0x7c, 0x00, 0x1f, 0x00, 0x38, 0x00, 0x0e, 0x00,
      0x10, 0x00, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static char narrow_bitmap32[] = {
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x80, 0x03, 0x00, 0x00, 0xc0, 0x07,
      0x00, 0x00, 0xe0, 0x0f, 0x00, 0x00, 0xf0, 0x1f, 0x00, 0x00, 0xf8, 0x3f, 0x00, 0x00, 0xfc,
      0x7f, 0x00, 0x00, 0xfe, 0xff, 0x00, 0x00, 0xff, 0xff, 0x01, 0x80, 0xff, 0xfe, 0x03, 0xc0,
      0x7f, 0xfc, 0x07, 0xe0, 0x3f, 0xf8, 0x0f, 0xf0, 0x1f, 0xf0, 0x1f, 0xf8, 0x0f, 0xe0, 0x3f,
      0xfc, 0x07, 0xc0, 0x7f, 0xf8, 0x03, 0x80, 0x3f, 0xf0, 0x01, 0x00, 0x1f, 0xe0, 0x00, 0x00,
      0x0e, 0x40, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static char narrow_mask32[] = {
      0x00, 0x00, 0x01, 0x00, 0x00, 0x80, 0x03, 0x00, 0x00, 0xc0, 0x07, 0x00, 0x00, 0xe0, 0x0f,
      0x00, 0x00, 0xf0, 0x1f, 0x00, 0x00, 0xf8, 0x3f, 0x00, 0x00, 0xfc, 0x7f, 0x00, 0x00, 0xfe,
      0xff, 0x00, 0x00, 0xff, 0xff, 0x01, 0x80, 0xff, 0xff, 0x03, 0xc0, 0xff, 0xff, 0x07, 0xe0,
      0xff, 0xfe, 0x0f, 0xf0, 0x7f, 0xfc, 0x1f, 0xf8, 0x3f, 0xf8, 0x3f, 0xfc, 0x1f, 0xf0, 0x7f,
      0xfe, 0x0f, 0xe0, 0xff, 0xfc, 0x07, 0xc0, 0x7f, 0xf8, 0x03, 0x80, 0x3f, 0xf0, 0x01, 0x00,
      0x1f, 0xe0, 0x00, 0x00, 0x0e, 0x40, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static BCursor NArrowCursor = {
      {narrow_bitmap16, narrow_bitmap24, narrow_bitmap32},
      {narrow_mask16, narrow_mask24, narrow_mask32},
      {{7, 5}, {11, 7}, {16, 9}},
      true,
  };

  BlenderCursor[WM_CURSOR_N_ARROW] = &NArrowCursor;
  END_CURSOR_BLOCK;

  /********************** South Arrow Cursor ***********************/
  BEGIN_CURSOR_BLOCK;

  static char sarrow_bitmap16[] = {
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x08, 0x08, 0x1c, 0x1c, 0x3e, 0x3e, 0x7c, 0x1f, 0xf8, 0x0f,
      0xf0, 0x07, 0xe0, 0x03, 0xc0, 0x01, 0x80, 0x00, 0x00, 0x00,
  };

  static char sarrow_mask16[] = {
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08,
      0x08, 0x1c, 0x1c, 0x3e, 0x3e, 0x7f, 0x7f, 0xfe, 0x3f, 0xfc, 0x1f,
      0xf8, 0x0f, 0xf0, 0x07, 0xe0, 0x03, 0xc0, 0x01, 0x80, 0x00,
  };

  static char sarrow_bitmap24[] = {
      0x00, 0x00, 0x00, 0x00, 0x10, 0x00, 0x04, 0x00, 0x38, 0x00, 0x0e, 0x00, 0x7c, 0x00, 0x1f,
      0x00, 0xfe, 0x80, 0x3f, 0x00, 0xfc, 0xc1, 0x1f, 0x00, 0xf8, 0xe3, 0x0f, 0x00, 0xf0, 0xf7,
      0x07, 0x00, 0xe0, 0xff, 0x03, 0x00, 0xc0, 0xff, 0x01, 0x00, 0x80, 0xff, 0x00, 0x00, 0x00,
      0x7f, 0x00, 0x00, 0x00, 0x3e, 0x00, 0x00, 0x00, 0x1c, 0x00, 0x00, 0x00, 0x08, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static char sarrow_mask24[] = {
      0x10, 0x00, 0x04, 0x00, 0x38, 0x00, 0x0e, 0x00, 0x7c, 0x00, 0x1f, 0x00, 0xfe, 0x80, 0x3f,
      0x00, 0xff, 0xc1, 0x7f, 0x00, 0xfe, 0xe3, 0x3f, 0x00, 0xfc, 0xf7, 0x1f, 0x00, 0xf8, 0xff,
      0x0f, 0x00, 0xf0, 0xff, 0x07, 0x00, 0xe0, 0xff, 0x03, 0x00, 0xc0, 0xff, 0x01, 0x00, 0x80,
      0xff, 0x00, 0x00, 0x00, 0x7f, 0x00, 0x00, 0x00, 0x3e, 0x00, 0x00, 0x00, 0x1c, 0x00, 0x00,
      0x00, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static char sarrow_bitmap32[] = {
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x20, 0x00, 0x00, 0x02, 0x70, 0x00, 0x00, 0x07, 0xf8, 0x00, 0x80, 0x0f,
      0xfc, 0x01, 0xc0, 0x1f, 0xfe, 0x03, 0xe0, 0x3f, 0xfc, 0x07, 0xf0, 0x1f, 0xf8, 0x0f, 0xf8,
      0x0f, 0xf0, 0x1f, 0xfc, 0x07, 0xe0, 0x3f, 0xfe, 0x03, 0xc0, 0x7f, 0xff, 0x01, 0x80, 0xff,
      0xff, 0x00, 0x00, 0xff, 0x7f, 0x00, 0x00, 0xfe, 0x3f, 0x00, 0x00, 0xfc, 0x1f, 0x00, 0x00,
      0xf8, 0x0f, 0x00, 0x00, 0xf0, 0x07, 0x00, 0x00, 0xe0, 0x03, 0x00, 0x00, 0xc0, 0x01, 0x00,
      0x00, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static char sarrow_mask32[] = {
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20,
      0x00, 0x00, 0x02, 0x70, 0x00, 0x00, 0x07, 0xf8, 0x00, 0x80, 0x0f, 0xfc, 0x01, 0xc0, 0x1f,
      0xfe, 0x03, 0xe0, 0x3f, 0xff, 0x07, 0xf0, 0x7f, 0xfe, 0x0f, 0xf8, 0x3f, 0xfc, 0x1f, 0xfc,
      0x1f, 0xf8, 0x3f, 0xfe, 0x0f, 0xf0, 0x7f, 0xff, 0x07, 0xe0, 0xff, 0xff, 0x03, 0xc0, 0xff,
      0xff, 0x01, 0x80, 0xff, 0xff, 0x00, 0x00, 0xff, 0x7f, 0x00, 0x00, 0xfe, 0x3f, 0x00, 0x00,
      0xfc, 0x1f, 0x00, 0x00, 0xf8, 0x0f, 0x00, 0x00, 0xf0, 0x07, 0x00, 0x00, 0xe0, 0x03, 0x00,
      0x00, 0xc0, 0x01, 0x00, 0x00, 0x80, 0x00, 0x00,
  };

  static BCursor SArrowCursor = {
      {sarrow_bitmap16, sarrow_bitmap24, sarrow_bitmap32},
      {sarrow_mask16, sarrow_mask24, sarrow_mask32},
      {{7, 10}, {11, 8}, {15, 22}},
      true,
  };

  BlenderCursor[WM_CURSOR_S_ARROW] = &SArrowCursor;
  END_CURSOR_BLOCK;

  /********************** East Arrow Cursor ***********************/
  BEGIN_CURSOR_BLOCK;

  static char earrow_bitmap16[] = {
      0x00, 0x00, 0x00, 0x01, 0x80, 0x03, 0xc0, 0x07, 0x80, 0x0f, 0x00,
      0x1f, 0x00, 0x3e, 0x00, 0x7c, 0x00, 0x3e, 0x00, 0x1f, 0x80, 0x0f,
      0xc0, 0x07, 0x80, 0x03, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00,
  };

  static char earrow_mask16[] = {
      0x00, 0x01, 0x80, 0x03, 0xc0, 0x07, 0xe0, 0x0f, 0xc0, 0x1f, 0x80,
      0x3f, 0x00, 0x7f, 0x00, 0xfe, 0x00, 0x7f, 0x80, 0x3f, 0xc0, 0x1f,
      0xe0, 0x0f, 0xc0, 0x07, 0x80, 0x03, 0x00, 0x01, 0x00, 0x00,
  };

  static char earrow_bitmap24[] = {
      0x00, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 0x38, 0x00, 0x00, 0x00, 0x7c, 0x00, 0x00,
      0x00, 0xfe, 0x00, 0x00, 0x00, 0xfc, 0x01, 0x00, 0x00, 0xf8, 0x03, 0x00, 0x00, 0xf0, 0x07,
      0x00, 0x00, 0xe0, 0x0f, 0x00, 0x00, 0xc0, 0x1f, 0x00, 0x00, 0x80, 0x3f, 0x00, 0x00, 0x00,
      0x7f, 0x00, 0x00, 0x80, 0x3f, 0x00, 0x00, 0xc0, 0x1f, 0x00, 0x00, 0xe0, 0x0f, 0x00, 0x00,
      0xf0, 0x07, 0x00, 0x00, 0xf8, 0x03, 0x00, 0x00, 0xfc, 0x01, 0x00, 0x00, 0xfe, 0x00, 0x00,
      0x00, 0x7c, 0x00, 0x00, 0x00, 0x38, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static char earrow_mask24[] = {
      0x10, 0x00, 0x00, 0x00, 0x38, 0x00, 0x00, 0x00, 0x7c, 0x00, 0x00, 0x00, 0xfe, 0x00, 0x00,
      0x00, 0xff, 0x01, 0x00, 0x00, 0xfe, 0x03, 0x00, 0x00, 0xfc, 0x07, 0x00, 0x00, 0xf8, 0x0f,
      0x00, 0x00, 0xf0, 0x1f, 0x00, 0x00, 0xe0, 0x3f, 0x00, 0x00, 0xc0, 0x7f, 0x00, 0x00, 0x80,
      0xff, 0x00, 0x00, 0xc0, 0x7f, 0x00, 0x00, 0xe0, 0x3f, 0x00, 0x00, 0xf0, 0x1f, 0x00, 0x00,
      0xf8, 0x0f, 0x00, 0x00, 0xfc, 0x07, 0x00, 0x00, 0xfe, 0x03, 0x00, 0x00, 0xff, 0x01, 0x00,
      0x00, 0xfe, 0x00, 0x00, 0x00, 0x7c, 0x00, 0x00, 0x00, 0x38, 0x00, 0x00, 0x00, 0x10, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static char earrow_bitmap32[] = {
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x80, 0x03, 0x00, 0x00, 0xc0, 0x07,
      0x00, 0x00, 0xe0, 0x0f, 0x00, 0x00, 0xf0, 0x1f, 0x00, 0x00, 0xe0, 0x3f, 0x00, 0x00, 0xc0,
      0x7f, 0x00, 0x00, 0x80, 0xff, 0x00, 0x00, 0x00, 0xff, 0x01, 0x00, 0x00, 0xfe, 0x03, 0x00,
      0x00, 0xfc, 0x07, 0x00, 0x00, 0xf8, 0x0f, 0x00, 0x00, 0xf0, 0x1f, 0x00, 0x00, 0xe0, 0x3f,
      0x00, 0x00, 0xc0, 0x7f, 0x00, 0x00, 0xe0, 0x3f, 0x00, 0x00, 0xf0, 0x1f, 0x00, 0x00, 0xf8,
      0x0f, 0x00, 0x00, 0xfc, 0x07, 0x00, 0x00, 0xfe, 0x03, 0x00, 0x00, 0xff, 0x01, 0x00, 0x80,
      0xff, 0x00, 0x00, 0xc0, 0x7f, 0x00, 0x00, 0xe0, 0x3f, 0x00, 0x00, 0xf0, 0x1f, 0x00, 0x00,
      0xe0, 0x0f, 0x00, 0x00, 0xc0, 0x07, 0x00, 0x00, 0x80, 0x03, 0x00, 0x00, 0x00, 0x01, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static char earrow_mask32[] = {
      0x00, 0x00, 0x01, 0x00, 0x00, 0x80, 0x03, 0x00, 0x00, 0xc0, 0x07, 0x00, 0x00, 0xe0, 0x0f,
      0x00, 0x00, 0xf0, 0x1f, 0x00, 0x00, 0xf8, 0x3f, 0x00, 0x00, 0xf0, 0x7f, 0x00, 0x00, 0xe0,
      0xff, 0x00, 0x00, 0xc0, 0xff, 0x01, 0x00, 0x80, 0xff, 0x03, 0x00, 0x00, 0xff, 0x07, 0x00,
      0x00, 0xfe, 0x0f, 0x00, 0x00, 0xfc, 0x1f, 0x00, 0x00, 0xf8, 0x3f, 0x00, 0x00, 0xf0, 0x7f,
      0x00, 0x00, 0xe0, 0xff, 0x00, 0x00, 0xf0, 0x7f, 0x00, 0x00, 0xf8, 0x3f, 0x00, 0x00, 0xfc,
      0x1f, 0x00, 0x00, 0xfe, 0x0f, 0x00, 0x00, 0xff, 0x07, 0x00, 0x80, 0xff, 0x03, 0x00, 0xc0,
      0xff, 0x01, 0x00, 0xe0, 0xff, 0x00, 0x00, 0xf0, 0x7f, 0x00, 0x00, 0xf8, 0x3f, 0x00, 0x00,
      0xf0, 0x1f, 0x00, 0x00, 0xe0, 0x0f, 0x00, 0x00, 0xc0, 0x07, 0x00, 0x00, 0x80, 0x03, 0x00,
      0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static BCursor EArrowCursor = {
      {earrow_bitmap16, earrow_bitmap24, earrow_bitmap32},
      {earrow_mask16, earrow_mask24, earrow_mask32},
      {{10, 7}, {8, 11}, {22, 15}},
      true,
  };

  BlenderCursor[WM_CURSOR_E_ARROW] = &EArrowCursor;
  END_CURSOR_BLOCK;

  /********************** West Arrow Cursor ***********************/
  BEGIN_CURSOR_BLOCK;

  static char warrow_bitmap16[] = {
      0x00, 0x00, 0x80, 0x00, 0xc0, 0x01, 0xe0, 0x03, 0xf0, 0x01, 0xf8,
      0x00, 0x7c, 0x00, 0x3e, 0x00, 0x7c, 0x00, 0xf8, 0x00, 0xf0, 0x01,
      0xe0, 0x03, 0xc0, 0x01, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static char warrow_mask16[] = {
      0x80, 0x00, 0xc0, 0x01, 0xe0, 0x03, 0xf0, 0x07, 0xf8, 0x03, 0xfc,
      0x01, 0xfe, 0x00, 0x7f, 0x00, 0xfe, 0x00, 0xfc, 0x01, 0xf8, 0x03,
      0xf0, 0x07, 0xe0, 0x03, 0xc0, 0x01, 0x80, 0x00, 0x00, 0x00,
  };

  static char warrow_bitmap24[] = {
      0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x1c, 0x00, 0x00, 0x00, 0x3e, 0x00,
      0x00, 0x00, 0x7f, 0x00, 0x00, 0x80, 0x3f, 0x00, 0x00, 0xc0, 0x1f, 0x00, 0x00, 0xe0, 0x0f,
      0x00, 0x00, 0xf0, 0x07, 0x00, 0x00, 0xf8, 0x03, 0x00, 0x00, 0xfc, 0x01, 0x00, 0x00, 0xfe,
      0x00, 0x00, 0x00, 0xfc, 0x01, 0x00, 0x00, 0xf8, 0x03, 0x00, 0x00, 0xf0, 0x07, 0x00, 0x00,
      0xe0, 0x0f, 0x00, 0x00, 0xc0, 0x1f, 0x00, 0x00, 0x80, 0x3f, 0x00, 0x00, 0x00, 0x7f, 0x00,
      0x00, 0x00, 0x3e, 0x00, 0x00, 0x00, 0x1c, 0x00, 0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static char warrow_mask24[] = {
      0x00, 0x08, 0x00, 0x00, 0x00, 0x1c, 0x00, 0x00, 0x00, 0x3e, 0x00, 0x00, 0x00, 0x7f, 0x00,
      0x00, 0x80, 0xff, 0x00, 0x00, 0xc0, 0x7f, 0x00, 0x00, 0xe0, 0x3f, 0x00, 0x00, 0xf0, 0x1f,
      0x00, 0x00, 0xf8, 0x0f, 0x00, 0x00, 0xfc, 0x07, 0x00, 0x00, 0xfe, 0x03, 0x00, 0x00, 0xff,
      0x01, 0x00, 0x00, 0xfe, 0x03, 0x00, 0x00, 0xfc, 0x07, 0x00, 0x00, 0xf8, 0x0f, 0x00, 0x00,
      0xf0, 0x1f, 0x00, 0x00, 0xe0, 0x3f, 0x00, 0x00, 0xc0, 0x7f, 0x00, 0x00, 0x80, 0xff, 0x00,
      0x00, 0x00, 0x7f, 0x00, 0x00, 0x00, 0x3e, 0x00, 0x00, 0x00, 0x1c, 0x00, 0x00, 0x00, 0x08,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static char warrow_bitmap32[] = {
      0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0xc0, 0x01, 0x00, 0x00, 0xe0, 0x03,
      0x00, 0x00, 0xf0, 0x07, 0x00, 0x00, 0xf8, 0x0f, 0x00, 0x00, 0xfc, 0x07, 0x00, 0x00, 0xfe,
      0x03, 0x00, 0x00, 0xff, 0x01, 0x00, 0x80, 0xff, 0x00, 0x00, 0xc0, 0x7f, 0x00, 0x00, 0xe0,
      0x3f, 0x00, 0x00, 0xf0, 0x1f, 0x00, 0x00, 0xf8, 0x0f, 0x00, 0x00, 0xfc, 0x07, 0x00, 0x00,
      0xfe, 0x03, 0x00, 0x00, 0xfc, 0x07, 0x00, 0x00, 0xf8, 0x0f, 0x00, 0x00, 0xf0, 0x1f, 0x00,
      0x00, 0xe0, 0x3f, 0x00, 0x00, 0xc0, 0x7f, 0x00, 0x00, 0x80, 0xff, 0x00, 0x00, 0x00, 0xff,
      0x01, 0x00, 0x00, 0xfe, 0x03, 0x00, 0x00, 0xfc, 0x07, 0x00, 0x00, 0xf8, 0x0f, 0x00, 0x00,
      0xf0, 0x07, 0x00, 0x00, 0xe0, 0x03, 0x00, 0x00, 0xc0, 0x01, 0x00, 0x00, 0x80, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static char warrow_mask32[] = {
      0x00, 0x80, 0x00, 0x00, 0x00, 0xc0, 0x01, 0x00, 0x00, 0xe0, 0x03, 0x00, 0x00, 0xf0, 0x07,
      0x00, 0x00, 0xf8, 0x0f, 0x00, 0x00, 0xfc, 0x1f, 0x00, 0x00, 0xfe, 0x0f, 0x00, 0x00, 0xff,
      0x07, 0x00, 0x80, 0xff, 0x03, 0x00, 0xc0, 0xff, 0x01, 0x00, 0xe0, 0xff, 0x00, 0x00, 0xf0,
      0x7f, 0x00, 0x00, 0xf8, 0x3f, 0x00, 0x00, 0xfc, 0x1f, 0x00, 0x00, 0xfe, 0x0f, 0x00, 0x00,
      0xff, 0x07, 0x00, 0x00, 0xfe, 0x0f, 0x00, 0x00, 0xfc, 0x1f, 0x00, 0x00, 0xf8, 0x3f, 0x00,
      0x00, 0xf0, 0x7f, 0x00, 0x00, 0xe0, 0xff, 0x00, 0x00, 0xc0, 0xff, 0x01, 0x00, 0x80, 0xff,
      0x03, 0x00, 0x00, 0xff, 0x07, 0x00, 0x00, 0xfe, 0x0f, 0x00, 0x00, 0xfc, 0x1f, 0x00, 0x00,
      0xf8, 0x0f, 0x00, 0x00, 0xf0, 0x07, 0x00, 0x00, 0xe0, 0x03, 0x00, 0x00, 0xc0, 0x01, 0x00,
      0x00, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static BCursor WArrowCursor = {
      {warrow_bitmap16, warrow_bitmap24, warrow_bitmap32},
      {warrow_mask16, warrow_mask24, warrow_mask32},
      {{5, 7}, {7, 11}, {9, 15}},
      true,
  };

  BlenderCursor[WM_CURSOR_W_ARROW] = &WArrowCursor;
  END_CURSOR_BLOCK;

  /********************** Stop Sign Cursor ***********************/
  BEGIN_CURSOR_BLOCK;

  static char stop_bitmap16[] = {
      0x00, 0x00, 0xe0, 0x07, 0xf8, 0x1f, 0x1c, 0x3c, 0x3c, 0x30, 0x76,
      0x70, 0xe6, 0x60, 0xc6, 0x61, 0x86, 0x63, 0x06, 0x67, 0x0e, 0x6e,
      0x0c, 0x3c, 0x3c, 0x38, 0xf8, 0x1f, 0xe0, 0x07, 0x00, 0x00,
  };

  static char stop_mask16[] = {
      0xe0, 0x07, 0xf8, 0x1f, 0xfc, 0x3f, 0xfe, 0x7f, 0x7e, 0x7c, 0xff,
      0xf8, 0xff, 0xf1, 0xef, 0xf3, 0xcf, 0xf7, 0x8f, 0xff, 0x1f, 0xff,
      0x3e, 0x7e, 0xfe, 0x7f, 0xfc, 0x3f, 0xf8, 0x1f, 0xe0, 0x07,
  };

  static char stop_bitmap24[] = {
      0x00, 0x00, 0x00, 0x00, 0x00, 0x7e, 0x00, 0x00, 0x80, 0xff, 0x01, 0x00, 0xe0, 0xc3, 0x07,
      0x00, 0xf0, 0x00, 0x0f, 0x00, 0x78, 0x00, 0x1c, 0x00, 0xf8, 0x00, 0x18, 0x00, 0xdc, 0x01,
      0x38, 0x00, 0x8c, 0x03, 0x30, 0x00, 0x0e, 0x07, 0x70, 0x00, 0x06, 0x0e, 0x60, 0x00, 0x06,
      0x1c, 0x60, 0x00, 0x06, 0x38, 0x60, 0x00, 0x06, 0x70, 0x60, 0x00, 0x0e, 0xe0, 0x70, 0x00,
      0x0c, 0xc0, 0x31, 0x00, 0x1c, 0x80, 0x3b, 0x00, 0x18, 0x00, 0x1f, 0x00, 0x38, 0x00, 0x1e,
      0x00, 0xf0, 0x00, 0x0f, 0x00, 0xe0, 0xc3, 0x07, 0x00, 0x80, 0xff, 0x01, 0x00, 0x00, 0x7e,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static char stop_mask24[] = {
      0x00, 0x7e, 0x00, 0x00, 0x80, 0xff, 0x01, 0x00, 0xe0, 0xff, 0x07, 0x00, 0xf0, 0xff, 0x0f,
      0x00, 0xf8, 0xc3, 0x1f, 0x00, 0xfc, 0x00, 0x3f, 0x00, 0xfc, 0x01, 0x3c, 0x00, 0xfe, 0x03,
      0x7c, 0x00, 0xde, 0x07, 0x78, 0x00, 0x9f, 0x0f, 0xf8, 0x00, 0x0f, 0x1f, 0xf0, 0x00, 0x0f,
      0x3e, 0xf0, 0x00, 0x0f, 0x7c, 0xf0, 0x00, 0x0f, 0xf8, 0xf0, 0x00, 0x1f, 0xf0, 0xf9, 0x00,
      0x1e, 0xe0, 0x7b, 0x00, 0x3e, 0xc0, 0x7f, 0x00, 0x3c, 0x80, 0x3f, 0x00, 0xfc, 0x00, 0x3f,
      0x00, 0xf8, 0xc3, 0x1f, 0x00, 0xf0, 0xff, 0x0f, 0x00, 0xe0, 0xff, 0x07, 0x00, 0x80, 0xff,
      0x01, 0x00, 0x00, 0x7e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static char stop_bitmap32[] = {
      0x00, 0x00, 0x00, 0x00, 0x00, 0xf0, 0x0f, 0x00, 0x00, 0xfe, 0x7f, 0x00, 0x00, 0xff, 0xff,
      0x00, 0xc0, 0x1f, 0xf8, 0x03, 0xe0, 0x03, 0xc0, 0x07, 0xf0, 0x01, 0x80, 0x0f, 0xf0, 0x03,
      0x00, 0x0f, 0xf8, 0x07, 0x00, 0x1e, 0xbc, 0x0f, 0x00, 0x3c, 0x1c, 0x1f, 0x00, 0x38, 0x1c,
      0x3e, 0x00, 0x38, 0x1e, 0x7c, 0x00, 0x78, 0x0e, 0xf8, 0x00, 0x70, 0x0e, 0xf0, 0x01, 0x70,
      0x0e, 0xe0, 0x03, 0x70, 0x0e, 0xc0, 0x07, 0x70, 0x0e, 0x80, 0x0f, 0x70, 0x0e, 0x00, 0x1f,
      0x70, 0x1e, 0x00, 0x3e, 0x78, 0x1c, 0x00, 0x7c, 0x38, 0x1c, 0x00, 0xf8, 0x38, 0x3c, 0x00,
      0xf0, 0x3d, 0x78, 0x00, 0xe0, 0x1f, 0xf0, 0x00, 0xc0, 0x0f, 0xf0, 0x01, 0x80, 0x0f, 0xe0,
      0x03, 0xc0, 0x07, 0xc0, 0x1f, 0xf8, 0x03, 0x00, 0xff, 0xff, 0x00, 0x00, 0xfe, 0x7f, 0x00,
      0x00, 0xf0, 0x0f, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static char stop_mask32[] = {
      0x00, 0xf8, 0x1f, 0x00, 0x00, 0xff, 0xff, 0x00, 0x80, 0xff, 0xff, 0x01, 0xe0, 0xff, 0xff,
      0x07, 0xf0, 0xff, 0xff, 0x0f, 0xf8, 0x3f, 0xfc, 0x1f, 0xf8, 0x07, 0xe0, 0x1f, 0xfc, 0x0f,
      0xc0, 0x3f, 0xfe, 0x1f, 0x80, 0x7f, 0xfe, 0x3f, 0x00, 0x7f, 0xfe, 0x7f, 0x00, 0x7e, 0xbf,
      0xff, 0x00, 0xfc, 0x3f, 0xff, 0x01, 0xfc, 0x3f, 0xfe, 0x03, 0xfc, 0x1f, 0xfc, 0x07, 0xf8,
      0x1f, 0xf8, 0x0f, 0xf8, 0x1f, 0xf0, 0x1f, 0xf8, 0x1f, 0xe0, 0x3f, 0xf8, 0x3f, 0xc0, 0x7f,
      0xfc, 0x3f, 0x80, 0xff, 0xfc, 0x3f, 0x00, 0xff, 0xfd, 0x7e, 0x00, 0xfe, 0x7f, 0xfe, 0x00,
      0xfc, 0x7f, 0xfe, 0x01, 0xf8, 0x7f, 0xfc, 0x03, 0xf0, 0x3f, 0xf8, 0x07, 0xe0, 0x1f, 0xf8,
      0x3f, 0xfc, 0x1f, 0xf0, 0xff, 0xff, 0x0f, 0xe0, 0xff, 0xff, 0x07, 0x80, 0xff, 0xff, 0x01,
      0x00, 0xff, 0xff, 0x00, 0x00, 0xf8, 0x1f, 0x00,
  };

  static BCursor StopCursor = {
      {stop_bitmap16, stop_bitmap24, stop_bitmap32},
      {stop_mask16, stop_mask24, stop_mask32},
      {{7, 7}, {11, 11}, {15, 15}},
      false,
  };

  BlenderCursor[WM_CURSOR_STOP] = &StopCursor;
  END_CURSOR_BLOCK;

  /********************** Zoom In Cursor ***********************/
  BEGIN_CURSOR_BLOCK;

  static char zoomin_bitmap16[] = {
      0x00, 0x00, 0x00, 0x00, 0xe0, 0x00, 0xf8, 0x03, 0xb8, 0x03, 0xbc,
      0x07, 0x0c, 0x06, 0xbc, 0x07, 0xb8, 0x03, 0xf8, 0x0b, 0xe0, 0x14,
      0x00, 0x22, 0x00, 0x44, 0x00, 0x88, 0x00, 0x90, 0x00, 0x60,
  };

  static char zoomin_mask16[] = {
      0x00, 0x00, 0xe0, 0x00, 0xf8, 0x03, 0xfc, 0x07, 0xfc, 0x07, 0xfe,
      0x0f, 0xfe, 0x0f, 0xfe, 0x0f, 0xfc, 0x07, 0xfc, 0x0f, 0xf8, 0x1f,
      0xe0, 0x3e, 0x00, 0x7c, 0x00, 0xf8, 0x00, 0xf0, 0x00, 0x60,
  };

  static char zoomin_bitmap24[] = {
      0x00, 0x00, 0x00, 0x00, 0xc0, 0x07, 0x00, 0x00, 0xf0, 0x1f, 0x00, 0x00, 0xf8, 0x3f, 0x00,
      0x00, 0xfc, 0x7f, 0x00, 0x00, 0xfc, 0x7e, 0x00, 0x00, 0xfe, 0xfe, 0x00, 0x00, 0xfe, 0xfe,
      0x00, 0x00, 0x1e, 0xf0, 0x00, 0x00, 0xfe, 0xfe, 0x00, 0x00, 0xfe, 0xfe, 0x00, 0x00, 0xfc,
      0x7e, 0x00, 0x00, 0xfc, 0x7f, 0x00, 0x00, 0xf8, 0xbf, 0x01, 0x00, 0xf0, 0x5f, 0x02, 0x00,
      0xc0, 0x27, 0x04, 0x00, 0x00, 0x20, 0x08, 0x00, 0x00, 0x40, 0x10, 0x00, 0x00, 0x80, 0x20,
      0x00, 0x00, 0x00, 0x41, 0x00, 0x00, 0x00, 0x82, 0x00, 0x00, 0x00, 0x44, 0x00, 0x00, 0x00,
      0x28, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static char zoomin_mask24[] = {
      0xc0, 0x07, 0x00, 0x00, 0xf0, 0x1f, 0x00, 0x00, 0xf8, 0x3f, 0x00, 0x00, 0xfc, 0x7f, 0x00,
      0x00, 0xfe, 0xff, 0x00, 0x00, 0xfe, 0xff, 0x00, 0x00, 0xff, 0xff, 0x01, 0x00, 0xff, 0xff,
      0x01, 0x00, 0xff, 0xff, 0x01, 0x00, 0xff, 0xff, 0x01, 0x00, 0xff, 0xff, 0x01, 0x00, 0xfe,
      0xff, 0x00, 0x00, 0xfe, 0xff, 0x00, 0x00, 0xfc, 0xff, 0x01, 0x00, 0xf8, 0xff, 0x03, 0x00,
      0xf0, 0xff, 0x07, 0x00, 0xc0, 0xe7, 0x0f, 0x00, 0x00, 0xc0, 0x1f, 0x00, 0x00, 0x80, 0x3f,
      0x00, 0x00, 0x00, 0x7f, 0x00, 0x00, 0x00, 0xfe, 0x00, 0x00, 0x00, 0x7c, 0x00, 0x00, 0x00,
      0x38, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static char zoomin_bitmap32[] = {
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7e, 0x00, 0x00, 0x80, 0xff, 0x01,
      0x00, 0xe0, 0xff, 0x07, 0x00, 0xf0, 0xff, 0x0f, 0x00, 0xf0, 0xff, 0x0f, 0x00, 0xf8, 0xe7,
      0x1f, 0x00, 0xf8, 0xe7, 0x1f, 0x00, 0xfc, 0xe7, 0x3f, 0x00, 0xfc, 0xe7, 0x3f, 0x00, 0x7c,
      0x00, 0x3e, 0x00, 0x7c, 0x00, 0x3e, 0x00, 0xfc, 0xe7, 0x3f, 0x00, 0xfc, 0xe7, 0x3f, 0x00,
      0xf8, 0xe7, 0x1f, 0x00, 0xf8, 0xe7, 0x1f, 0x00, 0xf0, 0xff, 0x0f, 0x00, 0xf0, 0xff, 0x4f,
      0x00, 0xe0, 0xff, 0xe7, 0x00, 0x80, 0xff, 0xb1, 0x01, 0x00, 0x7e, 0x18, 0x03, 0x00, 0x00,
      0x0c, 0x06, 0x00, 0x00, 0x18, 0x0c, 0x00, 0x00, 0x30, 0x18, 0x00, 0x00, 0x60, 0x30, 0x00,
      0x00, 0xc0, 0x60, 0x00, 0x00, 0x80, 0xc1, 0x00, 0x00, 0x00, 0xc3, 0x00, 0x00, 0x00, 0xc6,
      0x00, 0x00, 0x00, 0x7c, 0x00, 0x00, 0x00, 0x38,
  };

  static char zoomin_mask32[] = {
      0x00, 0x7e, 0x00, 0x00, 0x80, 0xff, 0x01, 0x00, 0xe0, 0xff, 0x07, 0x00, 0xf0, 0xff, 0x0f,
      0x00, 0xf8, 0xff, 0x1f, 0x00, 0xfc, 0xff, 0x3f, 0x00, 0xfc, 0xff, 0x3f, 0x00, 0xfe, 0xff,
      0x7f, 0x00, 0xfe, 0xff, 0x7f, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff,
      0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00,
      0xfe, 0xff, 0x7f, 0x00, 0xfe, 0xff, 0x7f, 0x00, 0xfc, 0xff, 0x7f, 0x00, 0xfc, 0xff, 0x7f,
      0x00, 0xf8, 0xff, 0xff, 0x00, 0xf0, 0xff, 0xff, 0x01, 0xe0, 0xff, 0xff, 0x03, 0x80, 0xff,
      0xff, 0x07, 0x00, 0x7e, 0xf8, 0x0f, 0x00, 0x00, 0xf0, 0x1f, 0x00, 0x00, 0xe0, 0x3f, 0x00,
      0x00, 0xc0, 0x7f, 0x00, 0x00, 0x80, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xfe,
      0x00, 0x00, 0x00, 0x7c, 0x00, 0x00, 0x00, 0x38,
  };

  static BCursor ZoomInCursor = {
      {zoomin_bitmap16, zoomin_bitmap24, zoomin_bitmap32},
      {zoomin_mask16, zoomin_mask24, zoomin_mask32},
      {{6, 6}, {8, 8}, {11, 11}},
      false,
  };

  BlenderCursor[WM_CURSOR_ZOOM_IN] = &ZoomInCursor;
  END_CURSOR_BLOCK;

  /********************** Zoom Out Cursor ***********************/
  BEGIN_CURSOR_BLOCK;

  static char zoomout_bitmap16[] = {
      0x00, 0x00, 0x00, 0x00, 0xe0, 0x00, 0xf8, 0x03, 0xf8, 0x03, 0xfc,
      0x07, 0x0c, 0x06, 0xfc, 0x07, 0xf8, 0x03, 0xf8, 0x0b, 0xe0, 0x14,
      0x00, 0x22, 0x00, 0x44, 0x00, 0x88, 0x00, 0x90, 0x00, 0x60,
  };

  static char zoomout_mask16[] = {
      0x00, 0x00, 0xe0, 0x00, 0xf8, 0x03, 0xfc, 0x07, 0xfc, 0x07, 0xfe,
      0x0f, 0xfe, 0x0f, 0xfe, 0x0f, 0xfc, 0x07, 0xfc, 0x0f, 0xf8, 0x1f,
      0xe0, 0x3e, 0x00, 0x7c, 0x00, 0xf8, 0x00, 0xf0, 0x00, 0x60,
  };

  static char zoomout_bitmap24[] = {
      0x00, 0x00, 0x00, 0x00, 0xc0, 0x07, 0x00, 0x00, 0xf0, 0x1f, 0x00, 0x00, 0xf8, 0x3f, 0x00,
      0x00, 0xfc, 0x7f, 0x00, 0x00, 0xfc, 0x7f, 0x00, 0x00, 0xfe, 0xff, 0x00, 0x00, 0xfe, 0xff,
      0x00, 0x00, 0x1e, 0xf0, 0x00, 0x00, 0xfe, 0xff, 0x00, 0x00, 0xfe, 0xff, 0x00, 0x00, 0xfc,
      0x7f, 0x00, 0x00, 0xfc, 0x7f, 0x00, 0x00, 0xf8, 0xbf, 0x01, 0x00, 0xf0, 0x5f, 0x02, 0x00,
      0xc0, 0x27, 0x04, 0x00, 0x00, 0x20, 0x08, 0x00, 0x00, 0x40, 0x10, 0x00, 0x00, 0x80, 0x20,
      0x00, 0x00, 0x00, 0x41, 0x00, 0x00, 0x00, 0x82, 0x00, 0x00, 0x00, 0x44, 0x00, 0x00, 0x00,
      0x28, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static char zoomout_mask24[] = {
      0xc0, 0x07, 0x00, 0x00, 0xf0, 0x1f, 0x00, 0x00, 0xf8, 0x3f, 0x00, 0x00, 0xfc, 0x7f, 0x00,
      0x00, 0xfe, 0xff, 0x00, 0x00, 0xfe, 0xff, 0x00, 0x00, 0xff, 0xff, 0x01, 0x00, 0xff, 0xff,
      0x01, 0x00, 0xff, 0xff, 0x01, 0x00, 0xff, 0xff, 0x01, 0x00, 0xff, 0xff, 0x01, 0x00, 0xfe,
      0xff, 0x00, 0x00, 0xfe, 0xff, 0x00, 0x00, 0xfc, 0xff, 0x01, 0x00, 0xf8, 0xff, 0x03, 0x00,
      0xf0, 0xff, 0x07, 0x00, 0xc0, 0xe7, 0x0f, 0x00, 0x00, 0xc0, 0x1f, 0x00, 0x00, 0x80, 0x3f,
      0x00, 0x00, 0x00, 0x7f, 0x00, 0x00, 0x00, 0xfe, 0x00, 0x00, 0x00, 0x7c, 0x00, 0x00, 0x00,
      0x38, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static char zoomout_bitmap32[] = {
      0x00, 0x00, 0x00, 0x00, 0x00, 0x7e, 0x00, 0x00, 0xc0, 0xff, 0x03, 0x00, 0xf0, 0xff, 0x0f,
      0x00, 0xf8, 0xff, 0x1f, 0x00, 0xf8, 0xff, 0x1f, 0x00, 0xfc, 0xff, 0x3f, 0x00, 0xfc, 0xff,
      0x3f, 0x00, 0xfc, 0xff, 0x3f, 0x00, 0xfe, 0xff, 0x7f, 0x00, 0xfe, 0xff, 0x7f, 0x00, 0x7e,
      0x00, 0x7e, 0x00, 0x7e, 0x00, 0x7e, 0x00, 0xfe, 0xff, 0x7f, 0x00, 0xfe, 0xff, 0x7f, 0x00,
      0xfc, 0xff, 0x3f, 0x00, 0xfc, 0xff, 0x3f, 0x00, 0xfc, 0xff, 0x3f, 0x00, 0xf8, 0xff, 0x5f,
      0x00, 0xf8, 0xff, 0x9f, 0x00, 0xf0, 0xff, 0x0f, 0x01, 0xc0, 0xff, 0x03, 0x02, 0x00, 0x7e,
      0x04, 0x04, 0x00, 0x00, 0x08, 0x08, 0x00, 0x00, 0x10, 0x10, 0x00, 0x00, 0x20, 0x20, 0x00,
      0x00, 0x40, 0x40, 0x00, 0x00, 0x80, 0x80, 0x00, 0x00, 0x00, 0x81, 0x00, 0x00, 0x00, 0x82,
      0x00, 0x00, 0x00, 0x44, 0x00, 0x00, 0x00, 0x38,
  };

  static char zoomout_mask32[] = {
      0x00, 0x7e, 0x00, 0x00, 0xc0, 0xff, 0x03, 0x00, 0xf0, 0xff, 0x0f, 0x00, 0xf8, 0xff, 0x1f,
      0x00, 0xfc, 0xff, 0x3f, 0x00, 0xfc, 0xff, 0x3f, 0x00, 0xfe, 0xff, 0x7f, 0x00, 0xfe, 0xff,
      0x7f, 0x00, 0xfe, 0xff, 0x7f, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff,
      0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00,
      0xfe, 0xff, 0x7f, 0x00, 0xfe, 0xff, 0x7f, 0x00, 0xfe, 0xff, 0x7f, 0x00, 0xfc, 0xff, 0x7f,
      0x00, 0xfc, 0xff, 0xff, 0x00, 0xf8, 0xff, 0xff, 0x01, 0xf0, 0xff, 0xff, 0x03, 0xc0, 0xff,
      0xff, 0x07, 0x00, 0x7e, 0xf8, 0x0f, 0x00, 0x00, 0xf0, 0x1f, 0x00, 0x00, 0xe0, 0x3f, 0x00,
      0x00, 0xc0, 0x7f, 0x00, 0x00, 0x80, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xfe,
      0x00, 0x00, 0x00, 0x7c, 0x00, 0x00, 0x00, 0x38,
  };

  static BCursor ZoomOutCursor = {
      {zoomout_bitmap16, zoomout_bitmap24, zoomout_bitmap32},
      {zoomout_mask16, zoomout_mask24, zoomout_mask32},
      {{6, 6}, {8, 8}, {11, 11}},
      false,
  };

  BlenderCursor[WM_CURSOR_ZOOM_OUT] = &ZoomOutCursor;
  END_CURSOR_BLOCK;

  /********************** Area Pick Cursor ***********************/
  BEGIN_CURSOR_BLOCK;

  static char pick_area_bitmap16[] = {
      0x00, 0x00, 0x10, 0x00, 0x10, 0x00, 0x10, 0x00, 0xfe, 0x00, 0x10,
      0x00, 0x10, 0x00, 0x10, 0x00, 0x00, 0xbf, 0x00, 0x81, 0x00, 0x81,
      0x00, 0x81, 0x00, 0x81, 0x00, 0x81, 0x00, 0x80, 0x00, 0xff,
  };

  static char pick_area_mask16[] = {
      0x38, 0x00, 0x38, 0x00, 0x38, 0x00, 0xff, 0x01, 0xff, 0x01, 0xff,
      0x01, 0x38, 0x00, 0xb8, 0x7f, 0xb8, 0xff, 0x80, 0xc1, 0x80, 0xc1,
      0x80, 0xc1, 0x80, 0xc1, 0x80, 0xc1, 0x80, 0xff, 0x00, 0xff,
  };

  static char pick_area_bitmap24[] = {
      0x00, 0x00, 0x00, 0x00, 0x20, 0x00, 0x00, 0x00, 0x20, 0x00, 0x00, 0x00, 0x20, 0x00, 0x00,
      0x00, 0x20, 0x00, 0x00, 0x00, 0xfe, 0x03, 0x00, 0x00, 0x20, 0x00, 0x00, 0x00, 0x20, 0x00,
      0x00, 0x00, 0x20, 0x00, 0x00, 0x00, 0x20, 0x00, 0x00, 0x00, 0x00, 0xf8, 0xbf, 0x00, 0x00,
      0x08, 0x80, 0x00, 0x00, 0x08, 0x80, 0x00, 0x00, 0x08, 0x80, 0x00, 0x00, 0x08, 0x80, 0x00,
      0x00, 0x08, 0x80, 0x00, 0x00, 0x08, 0x80, 0x00, 0x00, 0x08, 0x80, 0x00, 0x00, 0x08, 0x80,
      0x00, 0x00, 0x08, 0x80, 0x00, 0x00, 0x08, 0x80, 0x00, 0x00, 0x08, 0x80, 0x00, 0x00, 0x00,
      0x80, 0x00, 0x00, 0xf8, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static char pick_area_mask24[] = {
      0x70, 0x00, 0x00, 0x00, 0x70, 0x00, 0x00, 0x00, 0x70, 0x00, 0x00, 0x00, 0x70, 0x00, 0x00,
      0x00, 0xff, 0x07, 0x00, 0x00, 0xff, 0x07, 0x00, 0x00, 0xff, 0x07, 0x00, 0x00, 0x70, 0x00,
      0x00, 0x00, 0x70, 0x00, 0x00, 0x00, 0x70, 0xfc, 0x7f, 0x00, 0x70, 0xfc, 0xff, 0x00, 0x00,
      0x0c, 0xc0, 0x00, 0x00, 0x0c, 0xc0, 0x00, 0x00, 0x0c, 0xc0, 0x00, 0x00, 0x0c, 0xc0, 0x00,
      0x00, 0x0c, 0xc0, 0x00, 0x00, 0x0c, 0xc0, 0x00, 0x00, 0x0c, 0xc0, 0x00, 0x00, 0x0c, 0xc0,
      0x00, 0x00, 0x0c, 0xc0, 0x00, 0x00, 0x0c, 0xc0, 0x00, 0x00, 0x0c, 0xc0, 0x00, 0x00, 0xfc,
      0xff, 0x00, 0x00, 0xf8, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static char pick_area_bitmap32[] = {
      0x00, 0x00, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 0xc0, 0x00, 0x00,
      0x00, 0xc0, 0x00, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 0xfe, 0x1f, 0x00, 0x00, 0xfe, 0x1f,
      0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 0xc0,
      0x00, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x30, 0x8e, 0x63, 0x00, 0x30, 0x8e, 0x63,
      0x00, 0x30, 0x00, 0x60, 0x00, 0x30, 0x00, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x00, 0x60, 0x00, 0x30, 0x00, 0x60, 0x00, 0x30,
      0x00, 0x60, 0x00, 0x30, 0x00, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x30, 0x00, 0x60, 0x00, 0x30, 0x00, 0x60, 0x00, 0x30, 0x8e, 0x63,
      0x00, 0x30, 0x8e, 0x63, 0x00, 0x00, 0x00, 0x00,
  };

  static char pick_area_mask32[] = {
      0xe0, 0x01, 0x00, 0x00, 0xe0, 0x01, 0x00, 0x00, 0xe0, 0x01, 0x00, 0x00, 0xe0, 0x01, 0x00,
      0x00, 0xe0, 0x01, 0x00, 0x00, 0xff, 0x3f, 0x00, 0x00, 0xff, 0x3f, 0x00, 0x00, 0xff, 0x3f,
      0x00, 0x00, 0xff, 0x3f, 0x00, 0x00, 0xe0, 0x01, 0x00, 0x00, 0xe0, 0x01, 0x00, 0x00, 0xe0,
      0x01, 0x00, 0x00, 0xe0, 0x79, 0xdf, 0xf7, 0xe0, 0x79, 0xdf, 0xf7, 0x00, 0x78, 0xdf, 0xf7,
      0x00, 0x78, 0xdf, 0xf7, 0x00, 0x78, 0x00, 0xf0, 0x00, 0x78, 0x00, 0xf0, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x78, 0x00, 0xf0, 0x00, 0x78, 0x00, 0xf0, 0x00, 0x78, 0x00, 0xf0, 0x00, 0x78,
      0x00, 0xf0, 0x00, 0x78, 0x00, 0xf0, 0x00, 0x78, 0x00, 0xf0, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x78, 0x00, 0xf0, 0x00, 0x78, 0x00, 0xf0, 0x00, 0x78, 0xdf, 0xf7, 0x00, 0x78, 0xdf, 0xf7,
      0x00, 0x78, 0xdf, 0xf7, 0x00, 0x78, 0xdf, 0xf7,
  };

  static BCursor PickAreaCursor = {
      {pick_area_bitmap16, pick_area_bitmap24, pick_area_bitmap32},
      {pick_area_mask16, pick_area_mask24, pick_area_mask32},
      {{7, 7}, {11, 11}, {15, 15}},
      false,
  };

  BlenderCursor[WM_CURSOR_PICK_AREA] = &PickAreaCursor;
  END_CURSOR_BLOCK;

  /********************** Right Handle Cursor ***********************/
  BEGIN_CURSOR_BLOCK;

  static char right_handle_bitmap16[] = {
      0x00, 0x00, 0x7e, 0x00, 0x7e, 0x00, 0x70, 0x00, 0x70, 0x08, 0x70,
      0x18, 0x70, 0x38, 0x70, 0x78, 0x70, 0x78, 0x70, 0x38, 0x70, 0x18,
      0x70, 0x08, 0x70, 0x00, 0x7e, 0x00, 0x7e, 0x00, 0x00, 0x00,
  };

  static char right_handle_mask16[] = {
      0xff, 0x00, 0xff, 0x00, 0xff, 0x04, 0xff, 0x0c, 0xf8, 0x1c, 0xf8,
      0x3c, 0xf8, 0x7c, 0xf8, 0xfc, 0xf8, 0xfc, 0xf8, 0x7c, 0xf8, 0x3c,
      0xf8, 0x1c, 0xff, 0x0c, 0xff, 0x04, 0xff, 0x00, 0xff, 0x00,
  };

  static char right_handle_bitmap24[] = {
      0x00, 0x00, 0x00, 0x00, 0x7e, 0x00, 0x00, 0x00, 0xfe, 0x01, 0x00, 0x00, 0xfe, 0x03, 0x00,
      0x00, 0xc0, 0x03, 0x00, 0x00, 0x80, 0x07, 0x00, 0x00, 0x00, 0x07, 0x00, 0x00, 0x00, 0x87,
      0x00, 0x00, 0x0e, 0x87, 0x01, 0x00, 0x0e, 0x87, 0x03, 0x00, 0x0e, 0x87, 0x07, 0x00, 0x0e,
      0x87, 0x0f, 0x00, 0x0e, 0x87, 0x0f, 0x00, 0x0e, 0x87, 0x07, 0x00, 0x0e, 0x87, 0x03, 0x00,
      0x0e, 0x87, 0x01, 0x00, 0x00, 0x87, 0x00, 0x00, 0x00, 0x07, 0x00, 0x00, 0x80, 0x07, 0x00,
      0x00, 0xc0, 0x03, 0x00, 0x00, 0xfe, 0x03, 0x00, 0x00, 0xfe, 0x01, 0x00, 0x00, 0x7e, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static char right_handle_mask24[] = {
      0x7e, 0x00, 0x00, 0x00, 0xff, 0x01, 0x00, 0x00, 0xff, 0x03, 0x00, 0x00, 0xff, 0x07, 0x00,
      0x00, 0xfe, 0x07, 0x00, 0x00, 0xc0, 0x4f, 0x00, 0x00, 0x80, 0xcf, 0x00, 0x00, 0x8e, 0xcf,
      0x01, 0x00, 0x9f, 0xcf, 0x03, 0x00, 0x9f, 0xcf, 0x07, 0x00, 0x9f, 0xcf, 0x0f, 0x00, 0x9f,
      0xcf, 0x1f, 0x00, 0x9f, 0xcf, 0x1f, 0x00, 0x9f, 0xcf, 0x0f, 0x00, 0x9f, 0xcf, 0x07, 0x00,
      0x9f, 0xcf, 0x03, 0x00, 0x8e, 0xcf, 0x01, 0x00, 0x80, 0xcf, 0x00, 0x00, 0xc0, 0x4f, 0x00,
      0x00, 0xfe, 0x07, 0x00, 0x00, 0xff, 0x07, 0x00, 0x00, 0xff, 0x03, 0x00, 0x00, 0xff, 0x01,
      0x00, 0x00, 0x7e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static char right_handle_bitmap32[] = {
      0x00, 0x00, 0x00, 0x00, 0x00, 0xfc, 0x01, 0x00, 0x00, 0xfc, 0x07, 0x00, 0x00, 0xfc, 0x0f,
      0x00, 0x00, 0xfc, 0x1f, 0x00, 0x00, 0x80, 0x1f, 0x00, 0x00, 0x00, 0x3e, 0x00, 0x00, 0x00,
      0x3c, 0x00, 0x00, 0x00, 0x3c, 0x00, 0x00, 0x3c, 0x3c, 0x00, 0x00, 0x3c, 0x3c, 0x00, 0x00,
      0x3c, 0x3c, 0x04, 0x00, 0x3c, 0x3c, 0x0c, 0x00, 0x3c, 0x3c, 0x1c, 0x00, 0x3c, 0x3c, 0x3c,
      0x00, 0x3c, 0x3c, 0x7c, 0x00, 0x3c, 0x3c, 0x3c, 0x00, 0x3c, 0x3c, 0x1c, 0x00, 0x3c, 0x3c,
      0x0c, 0x00, 0x3c, 0x3c, 0x04, 0x00, 0x3c, 0x3c, 0x00, 0x00, 0x3c, 0x3c, 0x00, 0x00, 0x3c,
      0x3c, 0x00, 0x00, 0x00, 0x3c, 0x00, 0x00, 0x00, 0x3c, 0x00, 0x00, 0x00, 0x3e, 0x00, 0x00,
      0x80, 0x1f, 0x00, 0x00, 0xfc, 0x1f, 0x00, 0x00, 0xfc, 0x0f, 0x00, 0x00, 0xfc, 0x07, 0x00,
      0x00, 0xfc, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static char right_handle_mask32[] = {
      0x00, 0xfc, 0x01, 0x00, 0x00, 0xfe, 0x07, 0x00, 0x00, 0xfe, 0x0f, 0x00, 0x00, 0xfe, 0x1f,
      0x00, 0x00, 0xfe, 0x3f, 0x00, 0x00, 0xfc, 0x3f, 0x00, 0x00, 0x80, 0x7f, 0x00, 0x00, 0x00,
      0x7e, 0x00, 0x00, 0x3c, 0x7e, 0x00, 0x00, 0x7e, 0x7e, 0x02, 0x00, 0x7e, 0x7e, 0x06, 0x00,
      0x7e, 0x7e, 0x0e, 0x00, 0x7e, 0x7e, 0x1e, 0x00, 0x7e, 0x7e, 0x3e, 0x00, 0x7e, 0x7e, 0x7e,
      0x00, 0x7e, 0x7e, 0xfe, 0x00, 0x7e, 0x7e, 0x7e, 0x00, 0x7e, 0x7e, 0x3e, 0x00, 0x7e, 0x7e,
      0x1e, 0x00, 0x7e, 0x7e, 0x0e, 0x00, 0x7e, 0x7e, 0x06, 0x00, 0x7e, 0x7e, 0x02, 0x00, 0x7e,
      0x7e, 0x00, 0x00, 0x3c, 0x7e, 0x00, 0x00, 0x00, 0x7e, 0x00, 0x00, 0x80, 0x7f, 0x00, 0x00,
      0xfc, 0x3f, 0x00, 0x00, 0xfe, 0x3f, 0x00, 0x00, 0xfe, 0x1f, 0x00, 0x00, 0xfe, 0x0f, 0x00,
      0x00, 0xfe, 0x07, 0x00, 0x00, 0xfc, 0x01, 0x00,
  };

  static BCursor RightHandleCursor = {
      {right_handle_bitmap16, right_handle_bitmap24, right_handle_bitmap32},
      {right_handle_mask16, right_handle_mask24, right_handle_mask32},
      {{6, 7}, {10, 11}, {19, 15}},
      false,
  };

  BlenderCursor[WM_CURSOR_RIGHT_HANDLE] = &RightHandleCursor;
  END_CURSOR_BLOCK;

  /********************** Left Handle Cursor ***********************/
  BEGIN_CURSOR_BLOCK;

  static char left_handle_bitmap16[] = {
      0x00, 0x00, 0x00, 0x7e, 0x00, 0x7e, 0x00, 0x0e, 0x10, 0x0e, 0x18,
      0x0e, 0x1c, 0x0e, 0x1e, 0x0e, 0x1e, 0x0e, 0x1c, 0x0e, 0x18, 0x0e,
      0x10, 0x0e, 0x00, 0x0e, 0x00, 0x7e, 0x00, 0x7e, 0x00, 0x00,
  };

  static char left_handle_mask16[] = {
      0x00, 0xff, 0x00, 0xff, 0x20, 0xff, 0x30, 0xff, 0x38, 0x1f, 0x3c,
      0x1f, 0x3e, 0x1f, 0x3f, 0x1f, 0x3f, 0x1f, 0x3e, 0x1f, 0x3c, 0x1f,
      0x38, 0x1f, 0x30, 0xff, 0x20, 0xff, 0x00, 0xff, 0x00, 0xff,
  };

  static char left_handle_bitmap24[] = {
      0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x0f, 0x00, 0x00, 0xf0, 0x0f, 0x00, 0x00, 0xf8, 0x0f,
      0x00, 0x00, 0x78, 0x00, 0x00, 0x00, 0x3c, 0x00, 0x00, 0x00, 0x1c, 0x00, 0x00, 0x20, 0x1c,
      0x00, 0x00, 0x30, 0x1c, 0x0e, 0x00, 0x38, 0x1c, 0x0e, 0x00, 0x3c, 0x1c, 0x0e, 0x00, 0x3e,
      0x1c, 0x0e, 0x00, 0x3e, 0x1c, 0x0e, 0x00, 0x3c, 0x1c, 0x0e, 0x00, 0x38, 0x1c, 0x0e, 0x00,
      0x30, 0x1c, 0x0e, 0x00, 0x20, 0x1c, 0x00, 0x00, 0x00, 0x1c, 0x00, 0x00, 0x00, 0x3c, 0x00,
      0x00, 0x00, 0x78, 0x00, 0x00, 0x00, 0xf8, 0x0f, 0x00, 0x00, 0xf0, 0x0f, 0x00, 0x00, 0xc0,
      0x0f, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static char left_handle_mask24[] = {
      0x00, 0xc0, 0x0f, 0x00, 0x00, 0xf0, 0x1f, 0x00, 0x00, 0xf8, 0x1f, 0x00, 0x00, 0xfc, 0x1f,
      0x00, 0x00, 0xfc, 0x0f, 0x00, 0x40, 0x7e, 0x00, 0x00, 0x60, 0x3e, 0x00, 0x00, 0x70, 0x3e,
      0x0e, 0x00, 0x78, 0x3e, 0x1f, 0x00, 0x7c, 0x3e, 0x1f, 0x00, 0x7e, 0x3e, 0x1f, 0x00, 0x7f,
      0x3e, 0x1f, 0x00, 0x7f, 0x3e, 0x1f, 0x00, 0x7e, 0x3e, 0x1f, 0x00, 0x7c, 0x3e, 0x1f, 0x00,
      0x78, 0x3e, 0x1f, 0x00, 0x70, 0x3e, 0x0e, 0x00, 0x60, 0x3e, 0x00, 0x00, 0x40, 0x7e, 0x00,
      0x00, 0x00, 0xfc, 0x0f, 0x00, 0x00, 0xfc, 0x1f, 0x00, 0x00, 0xf8, 0x1f, 0x00, 0x00, 0xf0,
      0x1f, 0x00, 0x00, 0xc0, 0x0f, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static char left_handle_bitmap32[] = {
      0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x3f, 0x00, 0x00, 0xe0, 0x3f, 0x00, 0x00, 0xf0, 0x3f,
      0x00, 0x00, 0xf8, 0x3f, 0x00, 0x00, 0xf8, 0x01, 0x00, 0x00, 0x7c, 0x00, 0x00, 0x00, 0x3c,
      0x00, 0x00, 0x00, 0x3c, 0x00, 0x00, 0x00, 0x3c, 0x3c, 0x00, 0x00, 0x3c, 0x3c, 0x00, 0x20,
      0x3c, 0x3c, 0x00, 0x30, 0x3c, 0x3c, 0x00, 0x38, 0x3c, 0x3c, 0x00, 0x3c, 0x3c, 0x3c, 0x00,
      0x3e, 0x3c, 0x3c, 0x00, 0x3c, 0x3c, 0x3c, 0x00, 0x38, 0x3c, 0x3c, 0x00, 0x30, 0x3c, 0x3c,
      0x00, 0x20, 0x3c, 0x3c, 0x00, 0x00, 0x3c, 0x3c, 0x00, 0x00, 0x3c, 0x3c, 0x00, 0x00, 0x3c,
      0x3c, 0x00, 0x00, 0x3c, 0x00, 0x00, 0x00, 0x3c, 0x00, 0x00, 0x00, 0x7c, 0x00, 0x00, 0x00,
      0xf8, 0x01, 0x00, 0x00, 0xf8, 0x3f, 0x00, 0x00, 0xf0, 0x3f, 0x00, 0x00, 0xe0, 0x3f, 0x00,
      0x00, 0x80, 0x3f, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static char left_handle_mask32[] = {
      0x00, 0x80, 0x3f, 0x00, 0x00, 0xe0, 0x7f, 0x00, 0x00, 0xf0, 0x7f, 0x00, 0x00, 0xf8, 0x7f,
      0x00, 0x00, 0xfc, 0x7f, 0x00, 0x00, 0xfc, 0x3f, 0x00, 0x00, 0xfe, 0x01, 0x00, 0x00, 0x7e,
      0x00, 0x00, 0x00, 0x7e, 0x3c, 0x00, 0x40, 0x7e, 0x7e, 0x00, 0x60, 0x7e, 0x7e, 0x00, 0x70,
      0x7e, 0x7e, 0x00, 0x78, 0x7e, 0x7e, 0x00, 0x7c, 0x7e, 0x7e, 0x00, 0x7e, 0x7e, 0x7e, 0x00,
      0x7f, 0x7e, 0x7e, 0x00, 0x7e, 0x7e, 0x7e, 0x00, 0x7c, 0x7e, 0x7e, 0x00, 0x78, 0x7e, 0x7e,
      0x00, 0x70, 0x7e, 0x7e, 0x00, 0x60, 0x7e, 0x7e, 0x00, 0x40, 0x7e, 0x7e, 0x00, 0x00, 0x7e,
      0x7e, 0x00, 0x00, 0x7e, 0x3c, 0x00, 0x00, 0x7e, 0x00, 0x00, 0x00, 0xfe, 0x01, 0x00, 0x00,
      0xfc, 0x3f, 0x00, 0x00, 0xfc, 0x7f, 0x00, 0x00, 0xf8, 0x7f, 0x00, 0x00, 0xf0, 0x7f, 0x00,
      0x00, 0xe0, 0x7f, 0x00, 0x00, 0x80, 0x3f, 0x00,
  };

  static BCursor LeftHandleCursor = {
      {left_handle_bitmap16, left_handle_bitmap24, left_handle_bitmap32},
      {left_handle_mask16, left_handle_mask24, left_handle_mask32},
      {{9, 7}, {12, 11}, {12, 15}},
      false,
  };

  BlenderCursor[WM_CURSOR_LEFT_HANDLE] = &LeftHandleCursor;
  END_CURSOR_BLOCK;

  /********************** Both Handles Cursor ***********************/
  BEGIN_CURSOR_BLOCK;

  static char both_handles_bitmap16[] = {
      0x00, 0x00, 0x7e, 0x7e, 0x7e, 0x7e, 0x60, 0x06, 0x60, 0x06, 0x64,
      0x26, 0x66, 0x66, 0x67, 0xe6, 0x67, 0xe6, 0x66, 0x66, 0x64, 0x26,
      0x60, 0x06, 0x60, 0x06, 0x7e, 0x7e, 0x7e, 0x7e, 0x00, 0x00,
  };

  static char both_handles_mask16[] = {
      0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x3f, 0xfe,
      0x7f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x7f,
      0xfc, 0x3f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
  };

  static char both_handles_bitmap24[] = {
      0x00, 0x00, 0x00, 0x00, 0x3e, 0x00, 0x3e, 0x00, 0xfe, 0x80, 0x3f, 0x00, 0xfe, 0xc1, 0x3f,
      0x00, 0xe0, 0xc1, 0x03, 0x00, 0xc0, 0xe3, 0x01, 0x00, 0x80, 0xe3, 0x00, 0x00, 0x80, 0xe3,
      0x00, 0x00, 0x80, 0xe3, 0x00, 0x00, 0x88, 0xe3, 0x08, 0x00, 0x8c, 0xe3, 0x18, 0x00, 0x8e,
      0xe3, 0x38, 0x00, 0x8e, 0xe3, 0x38, 0x00, 0x8c, 0xe3, 0x18, 0x00, 0x88, 0xe3, 0x08, 0x00,
      0x80, 0xe3, 0x00, 0x00, 0x80, 0xe3, 0x00, 0x00, 0x80, 0xe3, 0x00, 0x00, 0xc0, 0xe3, 0x01,
      0x00, 0xe0, 0xc1, 0x01, 0x00, 0xfe, 0xc1, 0x3f, 0x00, 0xfe, 0x80, 0x3f, 0x00, 0x3e, 0x00,
      0x3e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static char both_handles_mask24[] = {
      0x3e, 0x00, 0x3e, 0x00, 0xff, 0x80, 0x7f, 0x00, 0xff, 0xc1, 0x7f, 0x00, 0xff, 0xe3, 0x7f,
      0x00, 0xfe, 0xe3, 0x3f, 0x00, 0xe0, 0xf7, 0x03, 0x00, 0xc0, 0xf7, 0x01, 0x00, 0xd0, 0xf7,
      0x05, 0x00, 0xd8, 0xf7, 0x0d, 0x00, 0xdc, 0xf7, 0x1d, 0x00, 0xde, 0xf7, 0x3d, 0x00, 0xdf,
      0xf7, 0x7d, 0x00, 0xdf, 0xf7, 0x7d, 0x00, 0xde, 0xf7, 0x3d, 0x00, 0xdc, 0xf7, 0x1d, 0x00,
      0xd8, 0xf7, 0x0d, 0x00, 0xd0, 0xf7, 0x05, 0x00, 0xc0, 0xf7, 0x01, 0x00, 0xe0, 0xf7, 0x03,
      0x00, 0xfe, 0xe3, 0x3f, 0x00, 0xff, 0xe3, 0x7f, 0x00, 0xff, 0xc1, 0x7f, 0x00, 0xff, 0x80,
      0x7f, 0x00, 0x3e, 0x00, 0x3e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static char both_handles_bitmap32[] = {
      0x00, 0x00, 0x00, 0x00, 0xfc, 0x01, 0x80, 0x3f, 0xfc, 0x07, 0xe0, 0x3f, 0xfc, 0x0f, 0xf0,
      0x3f, 0xfc, 0x1f, 0xf8, 0x3f, 0x80, 0x1f, 0xf8, 0x01, 0x00, 0x3e, 0x7c, 0x00, 0x00, 0x3c,
      0x3c, 0x00, 0x00, 0x3c, 0x3c, 0x00, 0x00, 0x3c, 0x3c, 0x00, 0x00, 0x3c, 0x3c, 0x00, 0x20,
      0x3c, 0x3c, 0x04, 0x30, 0x3c, 0x3c, 0x0c, 0x38, 0x3c, 0x3c, 0x1c, 0x3c, 0x3c, 0x3c, 0x3c,
      0x3e, 0x3c, 0x3c, 0x7c, 0x3c, 0x3c, 0x3c, 0x3c, 0x38, 0x3c, 0x3c, 0x1c, 0x30, 0x3c, 0x3c,
      0x0c, 0x20, 0x3c, 0x3c, 0x04, 0x00, 0x3c, 0x3c, 0x00, 0x00, 0x3c, 0x3c, 0x00, 0x00, 0x3c,
      0x3c, 0x00, 0x00, 0x3c, 0x3c, 0x00, 0x00, 0x3c, 0x3c, 0x00, 0x00, 0x3e, 0x7c, 0x00, 0x80,
      0x1f, 0xf8, 0x01, 0xfc, 0x1f, 0xf8, 0x3f, 0xfc, 0x0f, 0xf0, 0x3f, 0xfc, 0x07, 0xe0, 0x3f,
      0xfc, 0x01, 0x80, 0x3f, 0x00, 0x00, 0x00, 0x00,
  };

  static char both_handles_mask32[] = {
      0xfc, 0x01, 0x80, 0x3f, 0xfe, 0x07, 0xe0, 0x7f, 0xfe, 0x0f, 0xf0, 0x7f, 0xfe, 0x1f, 0xf8,
      0x7f, 0xfe, 0x3f, 0xfc, 0x7f, 0xfc, 0x3f, 0xfc, 0x3f, 0x80, 0x7f, 0xfe, 0x01, 0x00, 0x7e,
      0x7e, 0x00, 0x00, 0x7e, 0x7e, 0x00, 0x40, 0x7e, 0x7e, 0x02, 0x60, 0x7e, 0x7e, 0x06, 0x70,
      0x7e, 0x7e, 0x0e, 0x78, 0x7e, 0x7e, 0x1e, 0x7c, 0x7e, 0x7e, 0x3e, 0x7e, 0x7e, 0x7e, 0x7e,
      0x7f, 0x7e, 0x7e, 0xfe, 0x7e, 0x7e, 0x7e, 0x7e, 0x7c, 0x7e, 0x7e, 0x3e, 0x78, 0x7e, 0x7e,
      0x1e, 0x70, 0x7e, 0x7e, 0x0e, 0x60, 0x7e, 0x7e, 0x06, 0x40, 0x7e, 0x7e, 0x02, 0x00, 0x7e,
      0x7e, 0x00, 0x00, 0x7e, 0x7e, 0x00, 0x00, 0x7e, 0x7e, 0x00, 0x80, 0x7f, 0xfe, 0x01, 0xfc,
      0x3f, 0xfc, 0x3f, 0xfe, 0x3f, 0xfc, 0x7f, 0xfe, 0x1f, 0xf8, 0x7f, 0xfe, 0x0f, 0xf0, 0x7f,
      0xfe, 0x07, 0xe0, 0x7f, 0xfc, 0x01, 0x80, 0x3f,
  };

  static BCursor BothHandlesCursor = {
      {both_handles_bitmap16, both_handles_bitmap24, both_handles_bitmap32},
      {both_handles_mask16, both_handles_mask24, both_handles_mask32},
      {{7, 7}, {11, 11}, {15, 15}},
      false,
  };

  BlenderCursor[WM_CURSOR_BOTH_HANDLES] = &BothHandlesCursor;
  END_CURSOR_BLOCK;

  /********************** Blade Cursor ***********************/
  BEGIN_CURSOR_BLOCK;

  static char blade_bitmap16[] = {
      0x00, 0x00, 0x90, 0x00, 0xf8, 0x01, 0xdc, 0x03, 0xce, 0x07, 0xc4,
      0x0f, 0xbc, 0x1f, 0x7e, 0x3e, 0x7c, 0x7e, 0xf8, 0x3d, 0xf0, 0x23,
      0xe0, 0x73, 0xc0, 0x3b, 0x80, 0x1f, 0x00, 0x09, 0x00, 0x00,
  };

  static char blade_mask16[] = {
      0x90, 0x00, 0xf8, 0x01, 0xfc, 0x03, 0xfe, 0x07, 0xff, 0x0f, 0xfe,
      0x1f, 0xfe, 0x3f, 0xff, 0x7f, 0xfe, 0xff, 0xfc, 0x7f, 0xf8, 0x7f,
      0xf0, 0xff, 0xe0, 0x7f, 0xc0, 0x3f, 0x80, 0x1f, 0x00, 0x09,
  };

  static char blade_bitmap24[] = {
      0x00, 0x00, 0x00, 0x00, 0x30, 0x03, 0x00, 0x00, 0xf8, 0x07, 0x00, 0x00, 0xfc, 0x0f, 0x00,
      0x00, 0x3e, 0x1f, 0x00, 0x00, 0x1e, 0x3f, 0x00, 0x00, 0x0c, 0x7f, 0x00, 0x00, 0x0c, 0xfc,
      0x00, 0x00, 0x7e, 0xfc, 0x01, 0x00, 0x7e, 0xf8, 0x03, 0x00, 0xfc, 0xc1, 0x07, 0x00, 0xf8,
      0xc3, 0x0f, 0x00, 0xf0, 0xc3, 0x1f, 0x00, 0xe0, 0x83, 0x3f, 0x00, 0xc0, 0x1f, 0x7e, 0x00,
      0x80, 0x3f, 0x7e, 0x00, 0x00, 0x3f, 0x30, 0x00, 0x00, 0xfe, 0x30, 0x00, 0x00, 0xfc, 0x78,
      0x00, 0x00, 0xf8, 0x7c, 0x00, 0x00, 0xf0, 0x3f, 0x00, 0x00, 0xe0, 0x1f, 0x00, 0x00, 0xc0,
      0x0c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static char blade_mask24[] = {
      0x30, 0x03, 0x00, 0x00, 0xf8, 0x07, 0x00, 0x00, 0xfc, 0x0f, 0x00, 0x00, 0xfe, 0x1f, 0x00,
      0x00, 0xff, 0x3f, 0x00, 0x00, 0xff, 0x7f, 0x00, 0x00, 0xfe, 0xff, 0x00, 0x00, 0xfe, 0xff,
      0x01, 0x00, 0xff, 0xff, 0x03, 0x00, 0xff, 0xff, 0x07, 0x00, 0xfe, 0xff, 0x0f, 0x00, 0xfc,
      0xff, 0x1f, 0x00, 0xf8, 0xff, 0x3f, 0x00, 0xf0, 0xff, 0x7f, 0x00, 0xe0, 0xff, 0xff, 0x00,
      0xc0, 0xff, 0xff, 0x00, 0x80, 0xff, 0x7f, 0x00, 0x00, 0xff, 0x7f, 0x00, 0x00, 0xfe, 0xff,
      0x00, 0x00, 0xfc, 0xff, 0x00, 0x00, 0xf8, 0x7f, 0x00, 0x00, 0xf0, 0x3f, 0x00, 0x00, 0xe0,
      0x1f, 0x00, 0x00, 0xc0, 0x0c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  };

  static char blade_bitmap32[] = {
      0x00, 0x00, 0x00, 0x00, 0x80, 0x30, 0x00, 0x00, 0xc0, 0x79, 0x00, 0x00, 0xe0, 0xff, 0x00,
      0x00, 0xf0, 0xff, 0x01, 0x00, 0xf8, 0xfc, 0x03, 0x00, 0x7c, 0xfc, 0x07, 0x00, 0x3e, 0xfe,
      0x0f, 0x00, 0x1c, 0xf8, 0x1f, 0x00, 0x98, 0xc8, 0x3f, 0x00, 0xf8, 0xc0, 0x7f, 0x00, 0xfc,
      0xe3, 0xff, 0x00, 0xfe, 0xc1, 0xff, 0x01, 0xfe, 0x89, 0xff, 0x03, 0xfc, 0x1f, 0xfc, 0x07,
      0xf8, 0x3f, 0xfc, 0x0f, 0xf0, 0x3f, 0xfc, 0x1f, 0xe0, 0x3f, 0xf8, 0x3f, 0xc0, 0xff, 0x91,
      0x7f, 0x80, 0xff, 0x83, 0x7f, 0x00, 0xff, 0xc7, 0x3f, 0x00, 0xfe, 0x03, 0x1f, 0x00, 0xfc,
      0x13, 0x19, 0x00, 0xf8, 0x1f, 0x38, 0x00, 0xf0, 0x7f, 0x7c, 0x00, 0xe0, 0x3f, 0x3e, 0x00,
      0xc0, 0x3f, 0x1f, 0x00, 0x80, 0xff, 0x0f, 0x00, 0x00, 0xff, 0x07, 0x00, 0x00, 0x9e, 0x03,
      0x00, 0x00, 0x0c, 0x01, 0x00, 0x00, 0x00, 0x00,
  };

  static char blade_mask32[] = {
      0x80, 0x30, 0x00, 0x00, 0xc0, 0x79, 0x00, 0x00, 0xe0, 0xff, 0x00, 0x00, 0xf0, 0xff, 0x01,
      0x00, 0xf8, 0xff, 0x03, 0x00, 0xfc, 0xff, 0x07, 0x00, 0xfe, 0xff, 0x0f, 0x00, 0xff, 0xff,
      0x1f, 0x00, 0xfe, 0xff, 0x3f, 0x00, 0xfc, 0xff, 0x7f, 0x00, 0xfc, 0xff, 0xff, 0x00, 0xfe,
      0xff, 0xff, 0x01, 0xff, 0xff, 0xff, 0x03, 0xff, 0xff, 0xff, 0x07, 0xfe, 0xff, 0xff, 0x0f,
      0xfc, 0xff, 0xff, 0x1f, 0xf8, 0xff, 0xff, 0x3f, 0xf0, 0xff, 0xff, 0x7f, 0xe0, 0xff, 0xff,
      0xff, 0xc0, 0xff, 0xff, 0xff, 0x80, 0xff, 0xff, 0x7f, 0x00, 0xff, 0xff, 0x3f, 0x00, 0xfe,
      0xff, 0x3f, 0x00, 0xfc, 0xff, 0x7f, 0x00, 0xf8, 0xff, 0xff, 0x00, 0xf0, 0xff, 0x7f, 0x00,
      0xe0, 0xff, 0x3f, 0x00, 0xc0, 0xff, 0x1f, 0x00, 0x80, 0xff, 0x0f, 0x00, 0x00, 0xff, 0x07,
      0x00, 0x00, 0x9e, 0x03, 0x00, 0x00, 0x0c, 0x01,
  };

  static BCursor BladeCursor = {
      {blade_bitmap16, blade_bitmap24, blade_bitmap32},
      {blade_mask16, blade_mask24, blade_mask32},
      {{1, 4}, {1, 5}, {1, 7}},
      false,
  };

  BlenderCursor[WM_CURSOR_BLADE] = &BladeCursor;
  END_CURSOR_BLOCK;

  /********************** Put the cursors in the array ***********************/

#ifndef NDEBUG
  /* Assert if any cursors are empty. */
  for (int i = 1; i < WM_CURSOR_NUM; i++) {
    if (i == WM_CURSOR_NONE) {
      continue;
    }
    BLI_assert(BlenderCursor[i] != nullptr);
  }
#endif
}
