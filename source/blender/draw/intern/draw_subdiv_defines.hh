/* SPDX-FileCopyrightText: 2025 Blender Authors
 *
 * SPDX-License-Identifier: GPL-2.0-or-later */

/** \file
 * \ingroup draw
 */

#ifndef GPU_SHADER
#  pragma once
#endif

#define SUBDIV_GROUP_SIZE 64

/* Uniform buffer bindings */
#define SHADER_DATA_BUF_SLOT 0

/* Storage buffer bindings */
#define SUBDIV_FACE_OFFSET_BUF_SLOT 0

#define LINES_INPUT_EDGE_DRAW_FLAG_BUF_SLOT 1
#define LINES_EXTRA_COARSE_FACE_DATA_BUF_SLOT 2
#define LINES_OUTPUT_LINES_BUF_SLOT 3
#define LINES_LINES_LOOSE_FLAGS 4

#define TRIS_EXTRA_COARSE_FACE_DATA_BUF_SLOT 1
#define TRIS_OUTPUT_TRIS_BUF_SLOT 2
#define TRIS_FACE_MAT_OFFSET 3

#define EDGE_FAC_POS_BUF_SLOT 0
#define EDGE_FAC_EDGE_DRAW_FLAG_BUF_SLOT 1
#define EDGE_FAC_POLY_OTHER_MAP_BUF_SLOT 2
#define EDGE_FAC_EDGE_FAC_BUF_SLOT 3

#define PATCH_EVALUATION_SOURCE_VERTEX_BUFFER_BUF_SLOT 0
#define PATCH_EVALUATION_INPUT_PATCH_HANDLES_BUF_SLOT 1
#define PATCH_EVALUATION_QUAD_NODES_BUF_SLOT 2
#define PATCH_EVALUATION_PATCH_COORDS_BUF_SLOT 3
#define PATCH_EVALUATION_INPUT_VERTEX_ORIG_INDEX_BUF_SLOT 4
#define PATCH_EVALUATION_PATCH_ARRAY_BUFFER_BUF_SLOT 5
#define PATCH_EVALUATION_PATCH_INDEX_BUFFER_BUF_SLOT 6
#define PATCH_EVALUATION_PATCH_PARAM_BUFFER_BUF_SLOT 7
#define PATCH_EVALUATION_OUTPUT_FVAR_BUF_SLOT 8
#define PATCH_EVALUATION_OUTPUT_FDOTS_VERTEX_BUFFER_BUF_SLOT 8
#define PATCH_EVALUATION_OUTPUT_NORMALS_BUF_SLOT 9
#define PATCH_EVALUATION_OUTPUT_INDICES_BUF_SLOT 10
#define PATCH_EVALUATION_EXTRA_COARSE_FACE_DATA_BUF_SLOT 11
#define PATCH_EVALUATION_OUTPUT_POS_BUF_SLOT 9
#define PATCH_EVALUATION_SOURCE_EXTRA_VERTEX_BUFFER_BUF_SLOT 10
#define PATCH_EVALUATION_OUTPUT_ORCOS_BUF_SLOT 11

#define CUSTOM_DATA_SOURCE_DATA_BUF_SLOT 1
#define CUSTOM_DATA_FACE_PTEX_OFFSET_BUF_SLOT 2
#define CUSTOM_DATA_PATCH_COORDS_BUF_SLOT 3
#define CUSTOM_DATA_EXTRA_COARSE_FACE_DATA_BUF_SLOT 4
#define CUSTOM_DATA_DESTINATION_DATA_BUF_SLOT 5

#define SCULPT_DATA_SCULPT_MASK_BUF_SLOT 0
#define SCULPT_DATA_SCULPT_FACE_SET_COLOR_BUF_SLOT 1
#define SCULPT_DATA_SCULPT_DATA_BUF_SLOT 2

#define STRETCH_ANGLE_POS_BUF_SLOT 0
#define STRETCH_ANGLE_UVS_BUF_SLOT 1
#define STRETCH_ANGLE_UV_STRETCHES_BUF_SLOT 2

#define STRETCH_AREA_COARSE_STRETCH_AREA_BUF_SLOT 1
#define STRETCH_AREA_SUBDIV_STRETCH_AREA_BUF_SLOT 2

#define NORMALS_ACCUMULATE_POS_BUF_SLOT 0
#define NORMALS_ACCUMULATE_FACE_ADJACENCY_OFFSETS_BUF_SLOT 1
#define NORMALS_ACCUMULATE_FACE_ADJACENCY_LISTS_BUF_SLOT 2
#define NORMALS_ACCUMULATE_VERTEX_LOOP_MAP_BUF_SLOT 3
#define NORMALS_ACCUMULATE_NORMALS_BUF_SLOT 4

#define NORMALS_FINALIZE_CUSTOM_NORMALS_BUF_SLOT 1
#define NORMALS_FINALIZE_INPUT_VERT_ORIG_INDEX_BUF_SLOT 2
#define NORMALS_FINALIZE_EXTRA_COARSE_FACE_DATA_BUF_SLOT 3
#define NORMALS_FINALIZE_OUTPUT_LNOR_BUF_SLOT 4

#define LOOP_NORMALS_POS_SLOT 1
#define LOOP_NORMALS_EXTRA_COARSE_FACE_DATA_BUF_SLOT 2
#define LOOP_NORMALS_INPUT_VERT_ORIG_INDEX_BUF_SLOT 3
#define LOOP_NORMALS_VERT_NORMALS_BUF_SLOT 4
#define LOOP_NORMALS_VERTEX_LOOP_MAP_BUF_SLOT 5
#define LOOP_NORMALS_OUTPUT_LNOR_BUF_SLOT 6
