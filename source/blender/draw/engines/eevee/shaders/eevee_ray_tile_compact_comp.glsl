/* SPDX-FileCopyrightText: 2023 Blender Authors
 *
 * SPDX-License-Identifier: GPL-2.0-or-later */

/**
 * This pass scans the tile mask generated by the classify step and output indirect dispatch args.
 *
 * Dispatched as one thread for each trace resolution tile.
 */

#include "infos/eevee_tracing_info.hh"

COMPUTE_SHADER_CREATE_INFO(eevee_ray_tile_compact)

#include "gpu_shader_codegen_lib.glsl"
#include "gpu_shader_math_vector_lib.glsl"
#include "gpu_shader_utildefines_lib.glsl"

void main()
{
  int2 tile = int2(gl_GlobalInvocationID.xy);

  if (all(equal(tile, int2(0)))) {

    /* Workaround: Using atomics to initialize num groups on Windows/Intel GPUs. On Windows/Intel
     * Arc assignments are ignored when shader later on accesses the variables using atomic
     * operations.
     *
     * Ref #124060.
     */
#if defined(GPU_INTEL) && defined(OS_WIN)
    atomicExchange(raytrace_tracing_dispatch_buf.num_groups_y, 1u);
    atomicExchange(raytrace_denoise_dispatch_buf.num_groups_y, 1u);

    atomicExchange(raytrace_tracing_dispatch_buf.num_groups_z, 1u);
    atomicExchange(raytrace_denoise_dispatch_buf.num_groups_z, 1u);
#else
    raytrace_tracing_dispatch_buf.num_groups_y = 1u;
    raytrace_denoise_dispatch_buf.num_groups_y = 1u;

    raytrace_tracing_dispatch_buf.num_groups_z = 1u;
    raytrace_denoise_dispatch_buf.num_groups_z = 1u;
#endif
  }

  if (!in_image_range(tile, tile_raytrace_tracing_img)) {
    return;
  }

  /* True if this tile is shooting and tracing rays. */
  bool is_ray_tracing = imageLoad(tile_raytrace_tracing_img, int3(tile, closure_index)).r != 0;

  /* True if an adjacent tile is ray tracing and will need this tile data for denoising. */
  bool tile_is_ray_sampled = false;
  /* Could be optimized if that becomes an issue (3x3 cross gather + 3x3 "X" shape scatter). */
  for (int x_tile = -1; x_tile <= 1; x_tile++) {
    for (int y_tile = -1; y_tile <= 1; y_tile++) {
      int2 tile_adj = tile + int2(x_tile, y_tile);
      bool is_center_tile = (x_tile == 0 && y_tile == 0);
      if (in_image_range(tile_adj, tile_raytrace_tracing_img) && !is_center_tile) {
        if (imageLoad(tile_raytrace_tracing_img, int3(tile_adj, closure_index)).r != 0) {
          /* This tile will sample the target tracing tile. Make sure it is cleared. */
          tile_is_ray_sampled = true;
          break;
        }
      }
    }
  }

  /* TODO(fclem): we might want to dispatch another type of shader only for clearing. */
  if (is_ray_tracing || tile_is_ray_sampled) {
    /* Dispatch trace resolution tracing tile. */
    uint tile_index = atomicAdd(raytrace_tracing_dispatch_buf.num_groups_x, 1u);
    raytrace_tracing_tiles_buf[tile_index] = packUvec2x16(uint2(tile));
  }

  /* Dispatch denoise tiles. */
  if (is_ray_tracing) {
    for (int x_tile = 0; x_tile < resolution_scale; x_tile++) {
      for (int y_tile = 0; y_tile < resolution_scale; y_tile++) {
        int2 tile_adj = tile * resolution_scale + int2(x_tile, y_tile);
        if (in_image_range(tile_adj, tile_raytrace_denoise_img)) {
          if (imageLoad(tile_raytrace_denoise_img, int3(tile_adj, closure_index)).r != 0) {
            uint tile_index = atomicAdd(raytrace_denoise_dispatch_buf.num_groups_x, 1u);
            raytrace_denoise_tiles_buf[tile_index] = packUvec2x16(uint2(tile_adj));
          }
        }
      }
    }
  }
}
