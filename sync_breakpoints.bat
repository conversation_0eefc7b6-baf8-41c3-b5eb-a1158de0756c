@echo off
chcp 65001 >nul
echo 断点同步工具
echo ===============

echo.
echo 默认配置:
echo   源项目: G:\upbge\upbge-20240921-chun\upbge-master\.idea\workspace.xml
echo   目标项目: .idea\workspace.xml
echo   目标根目录: .
echo.

echo 使用方法:
echo   1. 试运行（推荐）: sync_breakpoints.bat --dry-run
echo   2. 实际执行: sync_breakpoints.bat
echo   3. 自定义参数: sync_breakpoints.bat --source "路径" --target "路径" --target-root "路径"
echo.

python sync_breakpoints.py %*

if %ERRORLEVEL% neq 0 (
    echo.
    echo 执行失败，请检查错误信息
    pause
    exit /b %ERRORLEVEL%
)

echo.
echo 执行完成！
pause 