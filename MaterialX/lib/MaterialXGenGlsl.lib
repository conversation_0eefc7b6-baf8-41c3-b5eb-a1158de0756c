!<arch>
/               -1                      0       47119     `
  � qF s� t� 洫 洫 'H 'H .V .V 8� 8� �$ �$ 厹 厹 匋 匋 �8 �8   俷 俷 佄 佄 砽 砽 财 财 y@ y@ 擢 擢 %� %� -v -v 0 0 鵂 鵂 � � � � 
� 
� !� !� 燌 燌 x� x� x x 鍉 鍉    �  � ( ( +� +� $� $�   �2 �2 墯 墯 稸 稸 陡 陡 ぜ ぜ 噒 噒 喴 喴 � � 礿 礿 � � 鄜 鄜 爯 爯 zp zp y� y� �4 �4 瓥 瓥 起 爬 �. � 仑 � 莿 胠 { { 刏 刏 懵 懵 3 3 鵫 鵫 � � ` `       兇 兇 � � 淳 淳 � � 芓 �: �: � � " " 
 
 1 1 F F   �6 �6 髭 髭 \ \ 
� 
� 	t 	t � � "� "� 7� 7� � � � � � � 9� 9� &j &j |� |� � � |2 |2 {� {� 痶 痶   5� 5� ~� ~� 〈 〈 ~ ~ }d }d 熬 熬 � � �� ��   � � @ @ � � 眃 眃 ﹏ ﹏ 栻 栻 朧 朧 檅 檅 � � 樎 樎 �  �  � � 緋 緋 � � 彫 彫 � �   嵷 嵷 �6 �6 轨 轨 笵 笵 ˊ ˊ 揚 揚 挵 挵 紗 紗 秽 秽 寽 寽 モ モ 孃 孃 媀 媀 笢 笢 夫 夫 Ж Ж 憍 憍 愔 愔 �: �: 簰 簰 ㄘ ㄘ �$ �$ 攩 攩 轿 轿 �* �* wr wr 焗 焗 v� v� v@ v@   琗 琗 � 陟 螟 膴 �  � 坍 稍 �> 束 圈 蔮 藢 翵 娙 娙 :n :n 6� 6� 顳 顳 ,� ,� 笑 褆 债 晰 弦 襌 謫 锥 劓 揶 揶 � � 酅 酅 餪 餪 鼝 鼝 � � �2 �2 齢 齢 怃 怃 � � 﨑 﨑 鍬 鍬 蜩 蜩 D D �* �* (( (( 軚 軚 軱 軱 巟 巟 � � )
 )
 3� 3� 罄 罄       ( ( 怐 怐 珀 珀 魱 魱  �  � � � 瓒 瓒 � � 擃 擃 閵 閵 )� )� 4x 4x 鮠 鮠 � � 暲 暲 闬 闬 �8 �8 � � 棇 棇 �, �, �
 �
 � � � � 膪 膪 h h 汥 汥 煳 煳 5@ 5@   � � � � 獨 獨 殸 殸 欪 欪 纉 纉 烤 烤 虳 � � 聿 聿 *� *� /6 /6 2 2   
8 
8 D D #� #� �6 �6 渶 渶 涒 涒 顒 顒 � �   濰 濰 澆 澆 涟 涟 � � 在 勐 娃 蝡 炣 炣__IMPORT_DESCRIPTOR_MaterialXGenGlsl __NULL_IMPORT_DESCRIPTOR MaterialXGenGlsl_NULL_THUNK_DATA ?create@GlslShaderGenerator@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderGenerator@MaterialX_v1_39_2@@@std@@XZ __imp_?create@GlslShaderGenerator@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderGenerator@MaterialX_v1_39_2@@@std@@XZ ?getTarget@GlslShaderGenerator@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ __imp_?getTarget@GlslShaderGenerator@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ ?getVersion@GlslShaderGenerator@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ __imp_?getVersion@GlslShaderGenerator@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ ?toVec4@GlslShaderGenerator@MaterialX_v1_39_2@@KAXPEBVTypeDesc@2@AEAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z __imp_?toVec4@GlslShaderGenerator@MaterialX_v1_39_2@@KAXPEBVTypeDesc@2@AEAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z ??1GlslShaderGenerator@MaterialX_v1_39_2@@UEAA@XZ __imp_??1GlslShaderGenerator@MaterialX_v1_39_2@@UEAA@XZ ??0GlslShaderGenerator@MaterialX_v1_39_2@@QEAA@AEBV01@@Z __imp_??0GlslShaderGenerator@MaterialX_v1_39_2@@QEAA@AEBV01@@Z ??0GlslShaderGenerator@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z __imp_??0GlslShaderGenerator@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z ??0GlslImplementation@MaterialX_v1_39_2@@IEAA@XZ __imp_??0GlslImplementation@MaterialX_v1_39_2@@IEAA@XZ ??1GlslImplementation@MaterialX_v1_39_2@@UEAA@XZ __imp_??1GlslImplementation@MaterialX_v1_39_2@@UEAA@XZ ??0GlslImplementation@MaterialX_v1_39_2@@QEAA@AEBV01@@Z __imp_??0GlslImplementation@MaterialX_v1_39_2@@QEAA@AEBV01@@Z ??0GlslImplementation@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z __imp_??0GlslImplementation@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z ??4GlslImplementation@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z __imp_??4GlslImplementation@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z ??4GlslImplementation@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z __imp_??4GlslImplementation@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z ??0EsslShaderGenerator@MaterialX_v1_39_2@@QEAA@XZ __imp_??0EsslShaderGenerator@MaterialX_v1_39_2@@QEAA@XZ ?create@EsslShaderGenerator@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderGenerator@MaterialX_v1_39_2@@@std@@XZ __imp_?create@EsslShaderGenerator@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderGenerator@MaterialX_v1_39_2@@@std@@XZ ?getTarget@EsslShaderGenerator@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ __imp_?getTarget@EsslShaderGenerator@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ ?getVersion@EsslShaderGenerator@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ __imp_?getVersion@EsslShaderGenerator@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ ?getVertexDataPrefix@EsslShaderGenerator@MaterialX_v1_39_2@@UEBA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEBVVariableBlock@2@@Z __imp_?getVertexDataPrefix@EsslShaderGenerator@MaterialX_v1_39_2@@UEBA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEBVVariableBlock@2@@Z ?emitDirectives@EsslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitDirectives@EsslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z ?emitUniforms@EsslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitUniforms@EsslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z ?emitInputs@EsslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitInputs@EsslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z ?emitOutputs@EsslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitOutputs@EsslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z ?getResourceBindingContext@EsslShaderGenerator@MaterialX_v1_39_2@@MEBA?AV?$shared_ptr@VHwResourceBindingContext@MaterialX_v1_39_2@@@std@@AEAVGenContext@2@@Z __imp_?getResourceBindingContext@EsslShaderGenerator@MaterialX_v1_39_2@@MEBA?AV?$shared_ptr@VHwResourceBindingContext@MaterialX_v1_39_2@@@std@@AEAVGenContext@2@@Z ??1EsslShaderGenerator@MaterialX_v1_39_2@@UEAA@XZ __imp_??1EsslShaderGenerator@MaterialX_v1_39_2@@UEAA@XZ ??0EsslShaderGenerator@MaterialX_v1_39_2@@QEAA@AEBV01@@Z __imp_??0EsslShaderGenerator@MaterialX_v1_39_2@@QEAA@AEBV01@@Z ??0EsslShaderGenerator@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z __imp_??0EsslShaderGenerator@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z ?create@GlslSyntax@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VSyntax@MaterialX_v1_39_2@@@std@@XZ __imp_?create@GlslSyntax@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VSyntax@MaterialX_v1_39_2@@@std@@XZ ?getInputQualifier@GlslSyntax@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ __imp_?getInputQualifier@GlslSyntax@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ ?getOutputQualifier@GlslSyntax@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ __imp_?getOutputQualifier@GlslSyntax@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ ?getConstantQualifier@GlslSyntax@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ __imp_?getConstantQualifier@GlslSyntax@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ ?getUniformQualifier@GlslSyntax@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ __imp_?getUniformQualifier@GlslSyntax@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ ?getSourceFileExtension@GlslSyntax@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ __imp_?getSourceFileExtension@GlslSyntax@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ ??1GlslSyntax@MaterialX_v1_39_2@@UEAA@XZ __imp_??1GlslSyntax@MaterialX_v1_39_2@@UEAA@XZ ??0GlslSyntax@MaterialX_v1_39_2@@QEAA@AEBV01@@Z __imp_??0GlslSyntax@MaterialX_v1_39_2@@QEAA@AEBV01@@Z ??0GlslSyntax@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z __imp_??0GlslSyntax@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z ??4GlslSyntax@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z __imp_??4GlslSyntax@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z ??4GlslSyntax@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z __imp_??4GlslSyntax@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z ??1GlslStructTypeSyntax@MaterialX_v1_39_2@@UEAA@XZ __imp_??1GlslStructTypeSyntax@MaterialX_v1_39_2@@UEAA@XZ ??0GlslStructTypeSyntax@MaterialX_v1_39_2@@QEAA@AEBV01@@Z __imp_??0GlslStructTypeSyntax@MaterialX_v1_39_2@@QEAA@AEBV01@@Z ??0GlslStructTypeSyntax@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z __imp_??0GlslStructTypeSyntax@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z ??4GlslStructTypeSyntax@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z __imp_??4GlslStructTypeSyntax@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z ??4GlslStructTypeSyntax@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z __imp_??4GlslStructTypeSyntax@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z ??0GlslStructTypeSyntax@MaterialX_v1_39_2@@QEAA@PEBVSyntax@1@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@1111AEBV?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@4@@Z __imp_??0GlslStructTypeSyntax@MaterialX_v1_39_2@@QEAA@PEBVSyntax@1@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@1111AEBV?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@4@@Z ?create@EsslSyntax@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VSyntax@MaterialX_v1_39_2@@@std@@XZ __imp_?create@EsslSyntax@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VSyntax@MaterialX_v1_39_2@@@std@@XZ ??1EsslSyntax@MaterialX_v1_39_2@@UEAA@XZ __imp_??1EsslSyntax@MaterialX_v1_39_2@@UEAA@XZ ??0EsslSyntax@MaterialX_v1_39_2@@QEAA@AEBV01@@Z __imp_??0EsslSyntax@MaterialX_v1_39_2@@QEAA@AEBV01@@Z ??0EsslSyntax@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z __imp_??0EsslSyntax@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z ??4EsslSyntax@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z __imp_??4EsslSyntax@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z ??4EsslSyntax@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z __imp_??4EsslSyntax@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z __imp_??_7GlslShaderGenerator@MaterialX_v1_39_2@@6B@ __imp_??_7GlslImplementation@MaterialX_v1_39_2@@6B@ __imp_?TARGET@EsslShaderGenerator@MaterialX_v1_39_2@@2V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@B __imp_?VERSION@EsslShaderGenerator@MaterialX_v1_39_2@@2V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@B __imp_??_7EsslShaderGenerator@MaterialX_v1_39_2@@6B@ __imp_??_7GlslSyntax@MaterialX_v1_39_2@@6B@ __imp_??_7GlslStructTypeSyntax@MaterialX_v1_39_2@@6B@ __imp_??_7EsslSyntax@MaterialX_v1_39_2@@6B@ ??0EsslSyntax@MaterialX_v1_39_2@@QEAA@XZ __imp_??0EsslSyntax@MaterialX_v1_39_2@@QEAA@XZ ??0GlslResourceBindingContext@MaterialX_v1_39_2@@QEAA@_K0@Z __imp_??0GlslResourceBindingContext@MaterialX_v1_39_2@@QEAA@_K0@Z ?create@GlslResourceBindingContext@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VGlslResourceBindingContext@MaterialX_v1_39_2@@@std@@_K0@Z __imp_?create@GlslResourceBindingContext@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VGlslResourceBindingContext@MaterialX_v1_39_2@@@std@@_K0@Z ?initialize@GlslResourceBindingContext@MaterialX_v1_39_2@@UEAAXXZ __imp_?initialize@GlslResourceBindingContext@MaterialX_v1_39_2@@UEAAXXZ ?emitDirectives@GlslResourceBindingContext@MaterialX_v1_39_2@@UEAAXAEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitDirectives@GlslResourceBindingContext@MaterialX_v1_39_2@@UEAAXAEAVGenContext@2@AEAVShaderStage@2@@Z ?emitResourceBindings@GlslResourceBindingContext@MaterialX_v1_39_2@@UEAAXAEAVGenContext@2@AEBVVariableBlock@2@AEAVShaderStage@2@@Z __imp_?emitResourceBindings@GlslResourceBindingContext@MaterialX_v1_39_2@@UEAAXAEAVGenContext@2@AEBVVariableBlock@2@AEAVShaderStage@2@@Z ?emitStructuredResourceBindings@GlslResourceBindingContext@MaterialX_v1_39_2@@UEAAXAEAVGenContext@2@AEBVVariableBlock@2@AEAVShaderStage@2@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@3@Z __imp_?emitStructuredResourceBindings@GlslResourceBindingContext@MaterialX_v1_39_2@@UEAAXAEAVGenContext@2@AEBVVariableBlock@2@AEAVShaderStage@2@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@3@Z ?enableSeparateBindingLocations@GlslResourceBindingContext@MaterialX_v1_39_2@@QEAAX_N@Z __imp_?enableSeparateBindingLocations@GlslResourceBindingContext@MaterialX_v1_39_2@@QEAAX_N@Z ??1GlslResourceBindingContext@MaterialX_v1_39_2@@UEAA@XZ __imp_??1GlslResourceBindingContext@MaterialX_v1_39_2@@UEAA@XZ ??0GlslResourceBindingContext@MaterialX_v1_39_2@@QEAA@AEBV01@@Z __imp_??0GlslResourceBindingContext@MaterialX_v1_39_2@@QEAA@AEBV01@@Z ??0GlslResourceBindingContext@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z __imp_??0GlslResourceBindingContext@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z ??4GlslResourceBindingContext@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z __imp_??4GlslResourceBindingContext@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z ??4GlslResourceBindingContext@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z __imp_??4GlslResourceBindingContext@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z __imp_??_7GlslResourceBindingContext@MaterialX_v1_39_2@@6B@ ??0GlslShaderGenerator@MaterialX_v1_39_2@@QEAA@XZ __imp_??0GlslShaderGenerator@MaterialX_v1_39_2@@QEAA@XZ ?generate@GlslShaderGenerator@MaterialX_v1_39_2@@UEBA?AV?$shared_ptr@VShader@MaterialX_v1_39_2@@@std@@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@4@V?$shared_ptr@VElement@MaterialX_v1_39_2@@@4@AEAVGenContext@2@@Z __imp_?generate@GlslShaderGenerator@MaterialX_v1_39_2@@UEBA?AV?$shared_ptr@VShader@MaterialX_v1_39_2@@@std@@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@4@V?$shared_ptr@VElement@MaterialX_v1_39_2@@@4@AEAVGenContext@2@@Z ?emitVariableDeclaration@GlslShaderGenerator@MaterialX_v1_39_2@@UEBAXPEBVShaderPort@2@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAVGenContext@2@AEAVShaderStage@2@_N@Z __imp_?emitVariableDeclaration@GlslShaderGenerator@MaterialX_v1_39_2@@UEBAXPEBVShaderPort@2@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAVGenContext@2@AEAVShaderStage@2@_N@Z ?getImplementation@GlslShaderGenerator@MaterialX_v1_39_2@@UEBA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@AEBVNodeDef@2@AEAVGenContext@2@@Z __imp_?getImplementation@GlslShaderGenerator@MaterialX_v1_39_2@@UEBA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@AEBVNodeDef@2@AEAVGenContext@2@@Z ?getVertexDataPrefix@GlslShaderGenerator@MaterialX_v1_39_2@@UEBA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEBVVariableBlock@2@@Z __imp_?getVertexDataPrefix@GlslShaderGenerator@MaterialX_v1_39_2@@UEBA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEBVVariableBlock@2@@Z ?emitVertexStage@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEBVShaderGraph@2@AEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitVertexStage@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEBVShaderGraph@2@AEAVGenContext@2@AEAVShaderStage@2@@Z ?emitPixelStage@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEBVShaderGraph@2@AEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitPixelStage@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEBVShaderGraph@2@AEAVGenContext@2@AEAVShaderStage@2@@Z ?emitDirectives@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitDirectives@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z ?emitConstants@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitConstants@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z ?emitUniforms@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitUniforms@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z ?emitLightData@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitLightData@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z ?emitInputs@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitInputs@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z ?emitOutputs@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitOutputs@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z ?getResourceBindingContext@GlslShaderGenerator@MaterialX_v1_39_2@@MEBA?AV?$shared_ptr@VHwResourceBindingContext@MaterialX_v1_39_2@@@std@@AEAVGenContext@2@@Z __imp_?getResourceBindingContext@GlslShaderGenerator@MaterialX_v1_39_2@@MEBA?AV?$shared_ptr@VHwResourceBindingContext@MaterialX_v1_39_2@@@std@@AEAVGenContext@2@@Z ?requiresLighting@GlslShaderGenerator@MaterialX_v1_39_2@@MEBA_NAEBVShaderGraph@2@@Z __imp_?requiresLighting@GlslShaderGenerator@MaterialX_v1_39_2@@MEBA_NAEBVShaderGraph@2@@Z ?emitSpecularEnvironment@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitSpecularEnvironment@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z ?emitTransmissionRender@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitTransmissionRender@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z ?emitLightFunctionDefinitions@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEBVShaderGraph@2@AEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitLightFunctionDefinitions@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEBVShaderGraph@2@AEAVGenContext@2@AEAVShaderStage@2@@Z ?toVec4@GlslShaderGenerator@MaterialX_v1_39_2@@KAXVTypeDesc@2@AEAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z __imp_?toVec4@GlslShaderGenerator@MaterialX_v1_39_2@@KAXVTypeDesc@2@AEAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z ?getTarget@GlslImplementation@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ __imp_?getTarget@GlslImplementation@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ ??0GeomColorNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ __imp_??0GeomColorNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ ??1GeomColorNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ __imp_??1GeomColorNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ ??0GeomColorNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z __imp_??0GeomColorNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z ??0GeomColorNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z __imp_??0GeomColorNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z ??4GeomColorNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z __imp_??4GeomColorNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z ??4GeomColorNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z __imp_??4GeomColorNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z ?isEditable@GeomPropValueNodeGlsl@MaterialX_v1_39_2@@UEBA_NAEBVShaderInput@2@@Z __imp_?isEditable@GeomPropValueNodeGlsl@MaterialX_v1_39_2@@UEBA_NAEBVShaderInput@2@@Z ??0GeomPropValueNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ __imp_??0GeomPropValueNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ ??1GeomPropValueNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ __imp_??1GeomPropValueNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ ??0GeomPropValueNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z __imp_??0GeomPropValueNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z ??0GeomPropValueNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z __imp_??0GeomPropValueNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z ??4GeomPropValueNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z __imp_??4GeomPropValueNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z ??4GeomPropValueNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z __imp_??4GeomPropValueNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z ??0GeomPropValueNodeGlslAsUniform@MaterialX_v1_39_2@@QEAA@XZ __imp_??0GeomPropValueNodeGlslAsUniform@MaterialX_v1_39_2@@QEAA@XZ ??1GeomPropValueNodeGlslAsUniform@MaterialX_v1_39_2@@UEAA@XZ __imp_??1GeomPropValueNodeGlslAsUniform@MaterialX_v1_39_2@@UEAA@XZ ??0GeomPropValueNodeGlslAsUniform@MaterialX_v1_39_2@@QEAA@AEBV01@@Z __imp_??0GeomPropValueNodeGlslAsUniform@MaterialX_v1_39_2@@QEAA@AEBV01@@Z ??0GeomPropValueNodeGlslAsUniform@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z __imp_??0GeomPropValueNodeGlslAsUniform@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z ??4GeomPropValueNodeGlslAsUniform@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z __imp_??4GeomPropValueNodeGlslAsUniform@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z ??4GeomPropValueNodeGlslAsUniform@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z __imp_??4GeomPropValueNodeGlslAsUniform@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z ??1SurfaceNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ __imp_??1SurfaceNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ ??0SurfaceNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z __imp_??0SurfaceNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z ??0SurfaceNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z __imp_??0SurfaceNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z ??0UnlitSurfaceNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ __imp_??0UnlitSurfaceNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ ??1UnlitSurfaceNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ __imp_??1UnlitSurfaceNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ ??0UnlitSurfaceNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z __imp_??0UnlitSurfaceNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z ??0UnlitSurfaceNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z __imp_??0UnlitSurfaceNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z ??4UnlitSurfaceNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z __imp_??4UnlitSurfaceNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z ??4UnlitSurfaceNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z __imp_??4UnlitSurfaceNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z ??1LightNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ __imp_??1LightNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ ??0LightNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z __imp_??0LightNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z ??0LightNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z __imp_??0LightNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z ??1LightCompoundNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ __imp_??1LightCompoundNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ ??0LightCompoundNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z __imp_??0LightCompoundNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z ??0LightCompoundNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z __imp_??0LightCompoundNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z ??4LightCompoundNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z __imp_??4LightCompoundNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z ??4LightCompoundNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z __imp_??4LightCompoundNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z ??1LightShaderNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ __imp_??1LightShaderNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ ??0LightShaderNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z __imp_??0LightShaderNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z ??0LightShaderNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z __imp_??0LightShaderNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z ??4LightShaderNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z __imp_??4LightShaderNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z ??4LightShaderNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z __imp_??4LightShaderNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z ??0HeightToNormalNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ __imp_??0HeightToNormalNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ ??1HeightToNormalNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ __imp_??1HeightToNormalNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ ??0HeightToNormalNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z __imp_??0HeightToNormalNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z ??0HeightToNormalNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z __imp_??0HeightToNormalNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z ??4HeightToNormalNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z __imp_??4HeightToNormalNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z ??4HeightToNormalNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z __imp_??4HeightToNormalNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z ??1LightSamplerNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ __imp_??1LightSamplerNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ ??0LightSamplerNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z __imp_??0LightSamplerNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z ??0LightSamplerNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z __imp_??0LightSamplerNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z ??4LightSamplerNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z __imp_??4LightSamplerNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z ??4LightSamplerNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z __imp_??4LightSamplerNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z ??1NumLightsNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ __imp_??1NumLightsNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ ??0NumLightsNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z __imp_??0NumLightsNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z ??0NumLightsNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z __imp_??0NumLightsNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z ??4NumLightsNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z __imp_??4NumLightsNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z ??4NumLightsNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z __imp_??4NumLightsNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z ??0BlurNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ __imp_??0BlurNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ ??1BlurNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ __imp_??1BlurNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ ??0BlurNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z __imp_??0BlurNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z ??0BlurNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z __imp_??0BlurNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z ??4BlurNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z __imp_??4BlurNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z ??4BlurNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z __imp_??4BlurNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z __imp_?TARGET@GlslShaderGenerator@MaterialX_v1_39_2@@2V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@B __imp_?VERSION@GlslShaderGenerator@MaterialX_v1_39_2@@2V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@B __imp_??_7GeomColorNodeGlsl@MaterialX_v1_39_2@@6B@ __imp_??_7GeomPropValueNodeGlsl@MaterialX_v1_39_2@@6B@ __imp_??_7GeomPropValueNodeGlslAsUniform@MaterialX_v1_39_2@@6B@ __imp_??_7SurfaceNodeGlsl@MaterialX_v1_39_2@@6B@ __imp_??_7UnlitSurfaceNodeGlsl@MaterialX_v1_39_2@@6B@ __imp_??_7LightNodeGlsl@MaterialX_v1_39_2@@6B@ __imp_??_7LightCompoundNodeGlsl@MaterialX_v1_39_2@@6B@ __imp_??_7LightShaderNodeGlsl@MaterialX_v1_39_2@@6B@ __imp_??_7HeightToNormalNodeGlsl@MaterialX_v1_39_2@@6B@ __imp_??_7LightSamplerNodeGlsl@MaterialX_v1_39_2@@6B@ __imp_??_7NumLightsNodeGlsl@MaterialX_v1_39_2@@6B@ __imp_??_7BlurNodeGlsl@MaterialX_v1_39_2@@6B@ ??0GlslSyntax@MaterialX_v1_39_2@@QEAA@XZ __imp_??0GlslSyntax@MaterialX_v1_39_2@@QEAA@XZ ?typeSupported@GlslSyntax@MaterialX_v1_39_2@@UEBA_NPEBVTypeDesc@2@@Z __imp_?typeSupported@GlslSyntax@MaterialX_v1_39_2@@UEBA_NPEBVTypeDesc@2@@Z ?remapEnumeration@GlslSyntax@MaterialX_v1_39_2@@UEBA_NAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@VTypeDesc@2@0AEAU?$pair@VTypeDesc@MaterialX_v1_39_2@@V?$shared_ptr@VValue@MaterialX_v1_39_2@@@std@@@4@@Z __imp_?remapEnumeration@GlslSyntax@MaterialX_v1_39_2@@UEBA_NAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@VTypeDesc@2@0AEAU?$pair@VTypeDesc@MaterialX_v1_39_2@@V?$shared_ptr@VValue@MaterialX_v1_39_2@@@std@@@4@@Z ?createStructSyntax@GlslSyntax@MaterialX_v1_39_2@@MEBA?AV?$shared_ptr@VStructTypeSyntax@MaterialX_v1_39_2@@@std@@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@4@0000@Z __imp_?createStructSyntax@GlslSyntax@MaterialX_v1_39_2@@MEBA?AV?$shared_ptr@VStructTypeSyntax@MaterialX_v1_39_2@@@std@@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@4@0000@Z ?getValue@GlslStructTypeSyntax@MaterialX_v1_39_2@@UEBA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEBVValue@2@_N@Z __imp_?getValue@GlslStructTypeSyntax@MaterialX_v1_39_2@@UEBA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEBVValue@2@_N@Z __imp_?INPUT_QUALIFIER@GlslSyntax@MaterialX_v1_39_2@@2V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@B __imp_?OUTPUT_QUALIFIER@GlslSyntax@MaterialX_v1_39_2@@2V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@B __imp_?UNIFORM_QUALIFIER@GlslSyntax@MaterialX_v1_39_2@@2V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@B __imp_?CONSTANT_QUALIFIER@GlslSyntax@MaterialX_v1_39_2@@2V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@B __imp_?FLAT_QUALIFIER@GlslSyntax@MaterialX_v1_39_2@@2V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@B __imp_?SOURCE_FILE_EXTENSION@GlslSyntax@MaterialX_v1_39_2@@2V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@B __imp_?VEC2_MEMBERS@GlslSyntax@MaterialX_v1_39_2@@2V?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@B __imp_?VEC3_MEMBERS@GlslSyntax@MaterialX_v1_39_2@@2V?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@B __imp_?VEC4_MEMBERS@GlslSyntax@MaterialX_v1_39_2@@2V?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@B ?create@BlurNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ __imp_?create@BlurNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ ?emitSamplingFunctionDefinition@BlurNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitSamplingFunctionDefinition@BlurNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z ?create@GeomColorNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ __imp_?create@GeomColorNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ ?createVariables@GeomColorNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShader@2@@Z __imp_?createVariables@GeomColorNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShader@2@@Z ?emitFunctionCall@GeomColorNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitFunctionCall@GeomColorNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z ?create@GeomPropValueNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ __imp_?create@GeomPropValueNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ ?createVariables@GeomPropValueNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShader@2@@Z __imp_?createVariables@GeomPropValueNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShader@2@@Z ?emitFunctionCall@GeomPropValueNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitFunctionCall@GeomPropValueNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z ?create@GeomPropValueNodeGlslAsUniform@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ __imp_?create@GeomPropValueNodeGlslAsUniform@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ ?createVariables@GeomPropValueNodeGlslAsUniform@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShader@2@@Z __imp_?createVariables@GeomPropValueNodeGlslAsUniform@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShader@2@@Z ?emitFunctionCall@GeomPropValueNodeGlslAsUniform@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitFunctionCall@GeomPropValueNodeGlslAsUniform@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z ?create@HeightToNormalNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ __imp_?create@HeightToNormalNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ ?createVariables@HeightToNormalNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShader@2@@Z __imp_?createVariables@HeightToNormalNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShader@2@@Z ?emitFunctionDefinition@HeightToNormalNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitFunctionDefinition@HeightToNormalNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z ?emitFunctionCall@HeightToNormalNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitFunctionCall@HeightToNormalNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z ?getTarget@HeightToNormalNodeGlsl@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ __imp_?getTarget@HeightToNormalNodeGlsl@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ ?acceptsInputType@HeightToNormalNodeGlsl@MaterialX_v1_39_2@@MEBA_NVTypeDesc@2@@Z __imp_?acceptsInputType@HeightToNormalNodeGlsl@MaterialX_v1_39_2@@MEBA_NVTypeDesc@2@@Z ?computeSampleOffsetStrings@HeightToNormalNodeGlsl@MaterialX_v1_39_2@@MEBAXAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@0IAEAV?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@4@@Z __imp_?computeSampleOffsetStrings@HeightToNormalNodeGlsl@MaterialX_v1_39_2@@MEBAXAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@0IAEAV?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@4@@Z ??0LightCompoundNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ __imp_??0LightCompoundNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ ?create@LightCompoundNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ __imp_?create@LightCompoundNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ ?getTarget@LightCompoundNodeGlsl@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ __imp_?getTarget@LightCompoundNodeGlsl@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ ?initialize@LightCompoundNodeGlsl@MaterialX_v1_39_2@@UEAAXAEBVInterfaceElement@2@AEAVGenContext@2@@Z __imp_?initialize@LightCompoundNodeGlsl@MaterialX_v1_39_2@@UEAAXAEBVInterfaceElement@2@AEAVGenContext@2@@Z ?createVariables@LightCompoundNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShader@2@@Z __imp_?createVariables@LightCompoundNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShader@2@@Z ?emitFunctionDefinition@LightCompoundNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitFunctionDefinition@LightCompoundNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z ?emitFunctionCall@LightCompoundNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitFunctionCall@LightCompoundNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z ?emitFunctionDefinition@LightCompoundNodeGlsl@MaterialX_v1_39_2@@IEBAXPEAVClosureContext@2@AEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitFunctionDefinition@LightCompoundNodeGlsl@MaterialX_v1_39_2@@IEBAXPEAVClosureContext@2@AEAVGenContext@2@AEAVShaderStage@2@@Z ??0LightNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ __imp_??0LightNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ ?create@LightNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ __imp_?create@LightNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ ?createVariables@LightNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShader@2@@Z __imp_?createVariables@LightNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShader@2@@Z ?emitFunctionCall@LightNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitFunctionCall@LightNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z ??0LightSamplerNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ __imp_??0LightSamplerNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ ?create@LightSamplerNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ __imp_?create@LightSamplerNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ ?emitFunctionDefinition@LightSamplerNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitFunctionDefinition@LightSamplerNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z ??0LightShaderNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ __imp_??0LightShaderNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ ?create@LightShaderNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ __imp_?create@LightShaderNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ ?getTarget@LightShaderNodeGlsl@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ __imp_?getTarget@LightShaderNodeGlsl@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ ?initialize@LightShaderNodeGlsl@MaterialX_v1_39_2@@UEAAXAEBVInterfaceElement@2@AEAVGenContext@2@@Z __imp_?initialize@LightShaderNodeGlsl@MaterialX_v1_39_2@@UEAAXAEBVInterfaceElement@2@AEAVGenContext@2@@Z ?createVariables@LightShaderNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShader@2@@Z __imp_?createVariables@LightShaderNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShader@2@@Z ?emitFunctionCall@LightShaderNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitFunctionCall@LightShaderNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z ??0NumLightsNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ __imp_??0NumLightsNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ ?create@NumLightsNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ __imp_?create@NumLightsNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ ?createVariables@NumLightsNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShader@2@@Z __imp_?createVariables@NumLightsNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShader@2@@Z ?emitFunctionDefinition@NumLightsNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitFunctionDefinition@NumLightsNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z ??0SurfaceNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ __imp_??0SurfaceNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ ?create@SurfaceNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ __imp_?create@SurfaceNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ ?createVariables@SurfaceNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShader@2@@Z __imp_?createVariables@SurfaceNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShader@2@@Z ?emitFunctionCall@SurfaceNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitFunctionCall@SurfaceNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z ?emitLightLoop@SurfaceNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z __imp_?emitLightLoop@SurfaceNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z ?create@UnlitSurfaceNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ __imp_?create@UnlitSurfaceNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ ?emitFunctionCall@UnlitSurfaceNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitFunctionCall@UnlitSurfaceNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z ??0VkResourceBindingContext@MaterialX_v1_39_2@@QEAA@_K@Z __imp_??0VkResourceBindingContext@MaterialX_v1_39_2@@QEAA@_K@Z ?create@VkResourceBindingContext@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VVkResourceBindingContext@MaterialX_v1_39_2@@@std@@_K@Z __imp_?create@VkResourceBindingContext@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VVkResourceBindingContext@MaterialX_v1_39_2@@@std@@_K@Z ?initialize@VkResourceBindingContext@MaterialX_v1_39_2@@UEAAXXZ __imp_?initialize@VkResourceBindingContext@MaterialX_v1_39_2@@UEAAXXZ ?emitDirectives@VkResourceBindingContext@MaterialX_v1_39_2@@UEAAXAEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitDirectives@VkResourceBindingContext@MaterialX_v1_39_2@@UEAAXAEAVGenContext@2@AEAVShaderStage@2@@Z ?emitResourceBindings@VkResourceBindingContext@MaterialX_v1_39_2@@UEAAXAEAVGenContext@2@AEBVVariableBlock@2@AEAVShaderStage@2@@Z __imp_?emitResourceBindings@VkResourceBindingContext@MaterialX_v1_39_2@@UEAAXAEAVGenContext@2@AEBVVariableBlock@2@AEAVShaderStage@2@@Z ?emitStructuredResourceBindings@VkResourceBindingContext@MaterialX_v1_39_2@@UEAAXAEAVGenContext@2@AEBVVariableBlock@2@AEAVShaderStage@2@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@3@Z __imp_?emitStructuredResourceBindings@VkResourceBindingContext@MaterialX_v1_39_2@@UEAAXAEAVGenContext@2@AEBVVariableBlock@2@AEAVShaderStage@2@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@3@Z ??1VkResourceBindingContext@MaterialX_v1_39_2@@UEAA@XZ __imp_??1VkResourceBindingContext@MaterialX_v1_39_2@@UEAA@XZ ??0VkResourceBindingContext@MaterialX_v1_39_2@@QEAA@AEBV01@@Z __imp_??0VkResourceBindingContext@MaterialX_v1_39_2@@QEAA@AEBV01@@Z ??0VkResourceBindingContext@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z __imp_??0VkResourceBindingContext@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z ??4VkResourceBindingContext@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z __imp_??4VkResourceBindingContext@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z ??4VkResourceBindingContext@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z __imp_??4VkResourceBindingContext@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z __imp_??_7VkResourceBindingContext@MaterialX_v1_39_2@@6B@ ??0VkShaderGenerator@MaterialX_v1_39_2@@QEAA@XZ __imp_??0VkShaderGenerator@MaterialX_v1_39_2@@QEAA@XZ ?create@VkShaderGenerator@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderGenerator@MaterialX_v1_39_2@@@std@@XZ __imp_?create@VkShaderGenerator@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderGenerator@MaterialX_v1_39_2@@@std@@XZ ?getTarget@VkShaderGenerator@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ __imp_?getTarget@VkShaderGenerator@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ ?getVersion@VkShaderGenerator@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ __imp_?getVersion@VkShaderGenerator@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ ?getVertexDataPrefix@VkShaderGenerator@MaterialX_v1_39_2@@UEBA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEBVVariableBlock@2@@Z __imp_?getVertexDataPrefix@VkShaderGenerator@MaterialX_v1_39_2@@UEBA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEBVVariableBlock@2@@Z ?emitDirectives@VkShaderGenerator@MaterialX_v1_39_2@@UEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitDirectives@VkShaderGenerator@MaterialX_v1_39_2@@UEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z ?emitInputs@VkShaderGenerator@MaterialX_v1_39_2@@UEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitInputs@VkShaderGenerator@MaterialX_v1_39_2@@UEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z ?emitOutputs@VkShaderGenerator@MaterialX_v1_39_2@@UEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitOutputs@VkShaderGenerator@MaterialX_v1_39_2@@UEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z ?getResourceBindingContext@VkShaderGenerator@MaterialX_v1_39_2@@MEBA?AV?$shared_ptr@VHwResourceBindingContext@MaterialX_v1_39_2@@@std@@AEAVGenContext@2@@Z __imp_?getResourceBindingContext@VkShaderGenerator@MaterialX_v1_39_2@@MEBA?AV?$shared_ptr@VHwResourceBindingContext@MaterialX_v1_39_2@@@std@@AEAVGenContext@2@@Z ??1VkShaderGenerator@MaterialX_v1_39_2@@UEAA@XZ __imp_??1VkShaderGenerator@MaterialX_v1_39_2@@UEAA@XZ ??0VkShaderGenerator@MaterialX_v1_39_2@@QEAA@AEBV01@@Z __imp_??0VkShaderGenerator@MaterialX_v1_39_2@@QEAA@AEBV01@@Z ??0VkShaderGenerator@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z __imp_??0VkShaderGenerator@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z ?create@VkSyntax@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VSyntax@MaterialX_v1_39_2@@@std@@XZ __imp_?create@VkSyntax@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VSyntax@MaterialX_v1_39_2@@@std@@XZ ?getInputQualifier@VkSyntax@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ __imp_?getInputQualifier@VkSyntax@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ ??1VkSyntax@MaterialX_v1_39_2@@UEAA@XZ __imp_??1VkSyntax@MaterialX_v1_39_2@@UEAA@XZ ??0VkSyntax@MaterialX_v1_39_2@@QEAA@AEBV01@@Z __imp_??0VkSyntax@MaterialX_v1_39_2@@QEAA@AEBV01@@Z ??0VkSyntax@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z __imp_??0VkSyntax@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z ??4VkSyntax@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z __imp_??4VkSyntax@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z ??4VkSyntax@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z __imp_??4VkSyntax@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z __imp_?TARGET@VkShaderGenerator@MaterialX_v1_39_2@@2V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@B __imp_?VERSION@VkShaderGenerator@MaterialX_v1_39_2@@2V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@B __imp_??_7VkShaderGenerator@MaterialX_v1_39_2@@6B@ __imp_??_7VkSyntax@MaterialX_v1_39_2@@6B@ ??0VkSyntax@MaterialX_v1_39_2@@QEAA@XZ __imp_??0VkSyntax@MaterialX_v1_39_2@@QEAA@XZ 
/               -1                      0       47203     `
  Fq 攕 詔  H' V. �8 $� 渽 鼊 8� 稷 n� 蝸 l� 撇 @y  �% v- 0 狓 � � �
 �! 鵁  x ~�  �  ( �+ �$ T� 2� 殙 V� 付 激 t� 覇 � j� � ~� 悹 pz 貀 4� 柇 鹌 琅 .� � 芈 � 勄 l� { Z� 裸 3 h� � `   啠 磧 � 敬 � T� :� � " 
 1 F  6� 邝 \ �
 t	 � �" �7 � � � �9 j& 蝲 � 2| 攞 t� 挟 �5  础 ~ d} 景 � 杸 N� � @ � d� n� 驏 V� b� � 聵  � � p� � 瑥 � |� 貚 6� 旃 D� @� P� 皰 喖 嗷 湆 猊 鷭 V� 湼 蚍 ě x� 謵 :� 捄 亘 $� 啍 谓 *� rw h� 趘 @v  X� � 熠  娔  � �  陨 >�  θ b� 屗 J� 葕 n: �6 D� �, π |�   蚁 R� 喼 蹲 尕 揶 � @� `� 慄 � 2� h� 溻 � D� @� 栩 D *� (( 栜 L� x� � 
) �3 荔    ( D� 赙 桇 �  � 惰 � 顡 婇 �) x4 d� � 罆 \� 8� � 寳 ,� 
� � �  h D� 戊 @5  � � 毆 牃 鷻 j� 究 D� � 岔 �* 6/ 2 墅 8
 D �# 6� �� 鉀 勵 � 太 H� 矟 傲 � 谠 论 尥 p� 転 �  � � �    2 1 = b a _ i h f o n l   
 G F > 
 	 K + * . & % � � � � �  � } | � � � � � � � � � � t s � x w u � � � � �  0 ` g m  E  ) $ � ~ { � � � r v � � � 4 3 d c k j q p   I H - , ( ' � � � � � � � � � � z y � � 
	� � �  / � � � ?   � � � � � � � � � � � � � � � � � � � � S  A R � � � � � � � � � � � � � � � �  V � U \ �  W � Q B � � Z C � [  T M P D L ! N     X  #  ^  � � � � " �   �  O � @ � � � e � Y  ] �   � � �    2 1 = b a _ i h f o n l   
 G F > 
 	 K + * . & % � � � � �  � } | � � � � � � � � � � t s � x w u � � � � �  0 ` g m  E  ) $ � ~ { � � � r v � � � 4 3 d c k j q p   I H - , ( ' � � � � � � � � � � z y � � 
	� 9 < � � � 6 J 5 ; : � � � � � � � � � 
� � � � � 7 � � � � � 8 � � � �  / � � � ?   � � � � � � � � � � � � � � � � � � � � S  A R � � � � � � � � � � � � � � � �  V � U \ �  W � Q B � � Z C � [  T M P D L ! N     X  #  ^  � � � � " �   �  O � @ � � � e � Y  ] �  ??0BlurNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z ??0BlurNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z ??0BlurNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ ??0EsslShaderGenerator@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z ??0EsslShaderGenerator@MaterialX_v1_39_2@@QEAA@AEBV01@@Z ??0EsslShaderGenerator@MaterialX_v1_39_2@@QEAA@XZ ??0EsslSyntax@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z ??0EsslSyntax@MaterialX_v1_39_2@@QEAA@AEBV01@@Z ??0EsslSyntax@MaterialX_v1_39_2@@QEAA@XZ ??0GeomColorNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z ??0GeomColorNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z ??0GeomColorNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ ??0GeomPropValueNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z ??0GeomPropValueNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z ??0GeomPropValueNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ ??0GeomPropValueNodeGlslAsUniform@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z ??0GeomPropValueNodeGlslAsUniform@MaterialX_v1_39_2@@QEAA@AEBV01@@Z ??0GeomPropValueNodeGlslAsUniform@MaterialX_v1_39_2@@QEAA@XZ ??0GlslImplementation@MaterialX_v1_39_2@@IEAA@XZ ??0GlslImplementation@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z ??0GlslImplementation@MaterialX_v1_39_2@@QEAA@AEBV01@@Z ??0GlslResourceBindingContext@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z ??0GlslResourceBindingContext@MaterialX_v1_39_2@@QEAA@AEBV01@@Z ??0GlslResourceBindingContext@MaterialX_v1_39_2@@QEAA@_K0@Z ??0GlslShaderGenerator@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z ??0GlslShaderGenerator@MaterialX_v1_39_2@@QEAA@AEBV01@@Z ??0GlslShaderGenerator@MaterialX_v1_39_2@@QEAA@XZ ??0GlslStructTypeSyntax@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z ??0GlslStructTypeSyntax@MaterialX_v1_39_2@@QEAA@AEBV01@@Z ??0GlslStructTypeSyntax@MaterialX_v1_39_2@@QEAA@PEBVSyntax@1@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@1111AEBV?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@4@@Z ??0GlslSyntax@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z ??0GlslSyntax@MaterialX_v1_39_2@@QEAA@AEBV01@@Z ??0GlslSyntax@MaterialX_v1_39_2@@QEAA@XZ ??0HeightToNormalNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z ??0HeightToNormalNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z ??0HeightToNormalNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ ??0LightCompoundNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z ??0LightCompoundNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z ??0LightCompoundNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ ??0LightNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z ??0LightNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z ??0LightNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ ??0LightSamplerNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z ??0LightSamplerNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z ??0LightSamplerNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ ??0LightShaderNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z ??0LightShaderNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z ??0LightShaderNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ ??0NumLightsNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z ??0NumLightsNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z ??0NumLightsNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ ??0SurfaceNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z ??0SurfaceNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z ??0SurfaceNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ ??0UnlitSurfaceNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z ??0UnlitSurfaceNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z ??0UnlitSurfaceNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ ??0VkResourceBindingContext@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z ??0VkResourceBindingContext@MaterialX_v1_39_2@@QEAA@AEBV01@@Z ??0VkResourceBindingContext@MaterialX_v1_39_2@@QEAA@_K@Z ??0VkShaderGenerator@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z ??0VkShaderGenerator@MaterialX_v1_39_2@@QEAA@AEBV01@@Z ??0VkShaderGenerator@MaterialX_v1_39_2@@QEAA@XZ ??0VkSyntax@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z ??0VkSyntax@MaterialX_v1_39_2@@QEAA@AEBV01@@Z ??0VkSyntax@MaterialX_v1_39_2@@QEAA@XZ ??1BlurNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ ??1EsslShaderGenerator@MaterialX_v1_39_2@@UEAA@XZ ??1EsslSyntax@MaterialX_v1_39_2@@UEAA@XZ ??1GeomColorNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ ??1GeomPropValueNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ ??1GeomPropValueNodeGlslAsUniform@MaterialX_v1_39_2@@UEAA@XZ ??1GlslImplementation@MaterialX_v1_39_2@@UEAA@XZ ??1GlslResourceBindingContext@MaterialX_v1_39_2@@UEAA@XZ ??1GlslShaderGenerator@MaterialX_v1_39_2@@UEAA@XZ ??1GlslStructTypeSyntax@MaterialX_v1_39_2@@UEAA@XZ ??1GlslSyntax@MaterialX_v1_39_2@@UEAA@XZ ??1HeightToNormalNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ ??1LightCompoundNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ ??1LightNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ ??1LightSamplerNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ ??1LightShaderNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ ??1NumLightsNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ ??1SurfaceNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ ??1UnlitSurfaceNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ ??1VkResourceBindingContext@MaterialX_v1_39_2@@UEAA@XZ ??1VkShaderGenerator@MaterialX_v1_39_2@@UEAA@XZ ??1VkSyntax@MaterialX_v1_39_2@@UEAA@XZ ??4BlurNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z ??4BlurNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z ??4EsslSyntax@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z ??4EsslSyntax@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z ??4GeomColorNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z ??4GeomColorNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z ??4GeomPropValueNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z ??4GeomPropValueNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z ??4GeomPropValueNodeGlslAsUniform@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z ??4GeomPropValueNodeGlslAsUniform@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z ??4GlslImplementation@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z ??4GlslImplementation@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z ??4GlslResourceBindingContext@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z ??4GlslResourceBindingContext@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z ??4GlslStructTypeSyntax@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z ??4GlslStructTypeSyntax@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z ??4GlslSyntax@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z ??4GlslSyntax@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z ??4HeightToNormalNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z ??4HeightToNormalNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z ??4LightCompoundNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z ??4LightCompoundNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z ??4LightSamplerNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z ??4LightSamplerNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z ??4LightShaderNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z ??4LightShaderNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z ??4NumLightsNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z ??4NumLightsNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z ??4UnlitSurfaceNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z ??4UnlitSurfaceNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z ??4VkResourceBindingContext@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z ??4VkResourceBindingContext@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z ??4VkSyntax@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z ??4VkSyntax@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z ?acceptsInputType@HeightToNormalNodeGlsl@MaterialX_v1_39_2@@MEBA_NVTypeDesc@2@@Z ?computeSampleOffsetStrings@HeightToNormalNodeGlsl@MaterialX_v1_39_2@@MEBAXAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@0IAEAV?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@4@@Z ?create@BlurNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ ?create@EsslShaderGenerator@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderGenerator@MaterialX_v1_39_2@@@std@@XZ ?create@EsslSyntax@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VSyntax@MaterialX_v1_39_2@@@std@@XZ ?create@GeomColorNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ ?create@GeomPropValueNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ ?create@GeomPropValueNodeGlslAsUniform@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ ?create@GlslResourceBindingContext@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VGlslResourceBindingContext@MaterialX_v1_39_2@@@std@@_K0@Z ?create@GlslShaderGenerator@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderGenerator@MaterialX_v1_39_2@@@std@@XZ ?create@GlslSyntax@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VSyntax@MaterialX_v1_39_2@@@std@@XZ ?create@HeightToNormalNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ ?create@LightCompoundNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ ?create@LightNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ ?create@LightSamplerNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ ?create@LightShaderNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ ?create@NumLightsNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ ?create@SurfaceNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ ?create@UnlitSurfaceNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ ?create@VkResourceBindingContext@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VVkResourceBindingContext@MaterialX_v1_39_2@@@std@@_K@Z ?create@VkShaderGenerator@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderGenerator@MaterialX_v1_39_2@@@std@@XZ ?create@VkSyntax@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VSyntax@MaterialX_v1_39_2@@@std@@XZ ?createStructSyntax@GlslSyntax@MaterialX_v1_39_2@@MEBA?AV?$shared_ptr@VStructTypeSyntax@MaterialX_v1_39_2@@@std@@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@4@0000@Z ?createVariables@GeomColorNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShader@2@@Z ?createVariables@GeomPropValueNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShader@2@@Z ?createVariables@GeomPropValueNodeGlslAsUniform@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShader@2@@Z ?createVariables@HeightToNormalNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShader@2@@Z ?createVariables@LightCompoundNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShader@2@@Z ?createVariables@LightNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShader@2@@Z ?createVariables@LightShaderNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShader@2@@Z ?createVariables@NumLightsNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShader@2@@Z ?createVariables@SurfaceNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShader@2@@Z ?emitConstants@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z ?emitDirectives@EsslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z ?emitDirectives@GlslResourceBindingContext@MaterialX_v1_39_2@@UEAAXAEAVGenContext@2@AEAVShaderStage@2@@Z ?emitDirectives@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z ?emitDirectives@VkResourceBindingContext@MaterialX_v1_39_2@@UEAAXAEAVGenContext@2@AEAVShaderStage@2@@Z ?emitDirectives@VkShaderGenerator@MaterialX_v1_39_2@@UEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z ?emitFunctionCall@GeomColorNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z ?emitFunctionCall@GeomPropValueNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z ?emitFunctionCall@GeomPropValueNodeGlslAsUniform@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z ?emitFunctionCall@HeightToNormalNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z ?emitFunctionCall@LightCompoundNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z ?emitFunctionCall@LightNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z ?emitFunctionCall@LightShaderNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z ?emitFunctionCall@SurfaceNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z ?emitFunctionCall@UnlitSurfaceNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z ?emitFunctionDefinition@HeightToNormalNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z ?emitFunctionDefinition@LightCompoundNodeGlsl@MaterialX_v1_39_2@@IEBAXPEAVClosureContext@2@AEAVGenContext@2@AEAVShaderStage@2@@Z ?emitFunctionDefinition@LightCompoundNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z ?emitFunctionDefinition@LightSamplerNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z ?emitFunctionDefinition@NumLightsNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z ?emitInputs@EsslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z ?emitInputs@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z ?emitInputs@VkShaderGenerator@MaterialX_v1_39_2@@UEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z ?emitLightData@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z ?emitLightFunctionDefinitions@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEBVShaderGraph@2@AEAVGenContext@2@AEAVShaderStage@2@@Z ?emitLightLoop@SurfaceNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z ?emitOutputs@EsslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z ?emitOutputs@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z ?emitOutputs@VkShaderGenerator@MaterialX_v1_39_2@@UEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z ?emitPixelStage@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEBVShaderGraph@2@AEAVGenContext@2@AEAVShaderStage@2@@Z ?emitResourceBindings@GlslResourceBindingContext@MaterialX_v1_39_2@@UEAAXAEAVGenContext@2@AEBVVariableBlock@2@AEAVShaderStage@2@@Z ?emitResourceBindings@VkResourceBindingContext@MaterialX_v1_39_2@@UEAAXAEAVGenContext@2@AEBVVariableBlock@2@AEAVShaderStage@2@@Z ?emitSamplingFunctionDefinition@BlurNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z ?emitSpecularEnvironment@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z ?emitStructuredResourceBindings@GlslResourceBindingContext@MaterialX_v1_39_2@@UEAAXAEAVGenContext@2@AEBVVariableBlock@2@AEAVShaderStage@2@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@3@Z ?emitStructuredResourceBindings@VkResourceBindingContext@MaterialX_v1_39_2@@UEAAXAEAVGenContext@2@AEBVVariableBlock@2@AEAVShaderStage@2@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@3@Z ?emitTransmissionRender@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z ?emitUniforms@EsslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z ?emitUniforms@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z ?emitVariableDeclaration@GlslShaderGenerator@MaterialX_v1_39_2@@UEBAXPEBVShaderPort@2@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAVGenContext@2@AEAVShaderStage@2@_N@Z ?emitVertexStage@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEBVShaderGraph@2@AEAVGenContext@2@AEAVShaderStage@2@@Z ?enableSeparateBindingLocations@GlslResourceBindingContext@MaterialX_v1_39_2@@QEAAX_N@Z ?generate@GlslShaderGenerator@MaterialX_v1_39_2@@UEBA?AV?$shared_ptr@VShader@MaterialX_v1_39_2@@@std@@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@4@V?$shared_ptr@VElement@MaterialX_v1_39_2@@@4@AEAVGenContext@2@@Z ?getConstantQualifier@GlslSyntax@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ ?getImplementation@GlslShaderGenerator@MaterialX_v1_39_2@@UEBA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@AEBVNodeDef@2@AEAVGenContext@2@@Z ?getInputQualifier@GlslSyntax@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ ?getInputQualifier@VkSyntax@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ ?getOutputQualifier@GlslSyntax@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ ?getResourceBindingContext@EsslShaderGenerator@MaterialX_v1_39_2@@MEBA?AV?$shared_ptr@VHwResourceBindingContext@MaterialX_v1_39_2@@@std@@AEAVGenContext@2@@Z ?getResourceBindingContext@GlslShaderGenerator@MaterialX_v1_39_2@@MEBA?AV?$shared_ptr@VHwResourceBindingContext@MaterialX_v1_39_2@@@std@@AEAVGenContext@2@@Z ?getResourceBindingContext@VkShaderGenerator@MaterialX_v1_39_2@@MEBA?AV?$shared_ptr@VHwResourceBindingContext@MaterialX_v1_39_2@@@std@@AEAVGenContext@2@@Z ?getSourceFileExtension@GlslSyntax@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ ?getTarget@EsslShaderGenerator@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ ?getTarget@GlslImplementation@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ ?getTarget@GlslShaderGenerator@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ ?getTarget@HeightToNormalNodeGlsl@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ ?getTarget@LightCompoundNodeGlsl@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ ?getTarget@LightShaderNodeGlsl@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ ?getTarget@VkShaderGenerator@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ ?getUniformQualifier@GlslSyntax@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ ?getValue@GlslStructTypeSyntax@MaterialX_v1_39_2@@UEBA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEBVValue@2@_N@Z ?getVersion@EsslShaderGenerator@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ ?getVersion@GlslShaderGenerator@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ ?getVersion@VkShaderGenerator@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ ?getVertexDataPrefix@EsslShaderGenerator@MaterialX_v1_39_2@@UEBA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEBVVariableBlock@2@@Z ?getVertexDataPrefix@GlslShaderGenerator@MaterialX_v1_39_2@@UEBA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEBVVariableBlock@2@@Z ?getVertexDataPrefix@VkShaderGenerator@MaterialX_v1_39_2@@UEBA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEBVVariableBlock@2@@Z ?initialize@GlslResourceBindingContext@MaterialX_v1_39_2@@UEAAXXZ ?initialize@LightCompoundNodeGlsl@MaterialX_v1_39_2@@UEAAXAEBVInterfaceElement@2@AEAVGenContext@2@@Z ?initialize@LightShaderNodeGlsl@MaterialX_v1_39_2@@UEAAXAEBVInterfaceElement@2@AEAVGenContext@2@@Z ?initialize@VkResourceBindingContext@MaterialX_v1_39_2@@UEAAXXZ ?isEditable@GeomPropValueNodeGlsl@MaterialX_v1_39_2@@UEBA_NAEBVShaderInput@2@@Z ?remapEnumeration@GlslSyntax@MaterialX_v1_39_2@@UEBA_NAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@VTypeDesc@2@0AEAU?$pair@VTypeDesc@MaterialX_v1_39_2@@V?$shared_ptr@VValue@MaterialX_v1_39_2@@@std@@@4@@Z ?requiresLighting@GlslShaderGenerator@MaterialX_v1_39_2@@MEBA_NAEBVShaderGraph@2@@Z ?toVec4@GlslShaderGenerator@MaterialX_v1_39_2@@KAXPEBVTypeDesc@2@AEAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z ?toVec4@GlslShaderGenerator@MaterialX_v1_39_2@@KAXVTypeDesc@2@AEAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z ?typeSupported@GlslSyntax@MaterialX_v1_39_2@@UEBA_NPEBVTypeDesc@2@@Z __IMPORT_DESCRIPTOR_MaterialXGenGlsl __NULL_IMPORT_DESCRIPTOR __imp_??0BlurNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z __imp_??0BlurNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z __imp_??0BlurNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ __imp_??0EsslShaderGenerator@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z __imp_??0EsslShaderGenerator@MaterialX_v1_39_2@@QEAA@AEBV01@@Z __imp_??0EsslShaderGenerator@MaterialX_v1_39_2@@QEAA@XZ __imp_??0EsslSyntax@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z __imp_??0EsslSyntax@MaterialX_v1_39_2@@QEAA@AEBV01@@Z __imp_??0EsslSyntax@MaterialX_v1_39_2@@QEAA@XZ __imp_??0GeomColorNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z __imp_??0GeomColorNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z __imp_??0GeomColorNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ __imp_??0GeomPropValueNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z __imp_??0GeomPropValueNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z __imp_??0GeomPropValueNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ __imp_??0GeomPropValueNodeGlslAsUniform@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z __imp_??0GeomPropValueNodeGlslAsUniform@MaterialX_v1_39_2@@QEAA@AEBV01@@Z __imp_??0GeomPropValueNodeGlslAsUniform@MaterialX_v1_39_2@@QEAA@XZ __imp_??0GlslImplementation@MaterialX_v1_39_2@@IEAA@XZ __imp_??0GlslImplementation@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z __imp_??0GlslImplementation@MaterialX_v1_39_2@@QEAA@AEBV01@@Z __imp_??0GlslResourceBindingContext@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z __imp_??0GlslResourceBindingContext@MaterialX_v1_39_2@@QEAA@AEBV01@@Z __imp_??0GlslResourceBindingContext@MaterialX_v1_39_2@@QEAA@_K0@Z __imp_??0GlslShaderGenerator@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z __imp_??0GlslShaderGenerator@MaterialX_v1_39_2@@QEAA@AEBV01@@Z __imp_??0GlslShaderGenerator@MaterialX_v1_39_2@@QEAA@XZ __imp_??0GlslStructTypeSyntax@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z __imp_??0GlslStructTypeSyntax@MaterialX_v1_39_2@@QEAA@AEBV01@@Z __imp_??0GlslStructTypeSyntax@MaterialX_v1_39_2@@QEAA@PEBVSyntax@1@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@1111AEBV?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@4@@Z __imp_??0GlslSyntax@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z __imp_??0GlslSyntax@MaterialX_v1_39_2@@QEAA@AEBV01@@Z __imp_??0GlslSyntax@MaterialX_v1_39_2@@QEAA@XZ __imp_??0HeightToNormalNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z __imp_??0HeightToNormalNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z __imp_??0HeightToNormalNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ __imp_??0LightCompoundNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z __imp_??0LightCompoundNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z __imp_??0LightCompoundNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ __imp_??0LightNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z __imp_??0LightNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z __imp_??0LightNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ __imp_??0LightSamplerNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z __imp_??0LightSamplerNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z __imp_??0LightSamplerNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ __imp_??0LightShaderNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z __imp_??0LightShaderNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z __imp_??0LightShaderNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ __imp_??0NumLightsNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z __imp_??0NumLightsNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z __imp_??0NumLightsNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ __imp_??0SurfaceNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z __imp_??0SurfaceNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z __imp_??0SurfaceNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ __imp_??0UnlitSurfaceNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z __imp_??0UnlitSurfaceNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z __imp_??0UnlitSurfaceNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ __imp_??0VkResourceBindingContext@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z __imp_??0VkResourceBindingContext@MaterialX_v1_39_2@@QEAA@AEBV01@@Z __imp_??0VkResourceBindingContext@MaterialX_v1_39_2@@QEAA@_K@Z __imp_??0VkShaderGenerator@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z __imp_??0VkShaderGenerator@MaterialX_v1_39_2@@QEAA@AEBV01@@Z __imp_??0VkShaderGenerator@MaterialX_v1_39_2@@QEAA@XZ __imp_??0VkSyntax@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z __imp_??0VkSyntax@MaterialX_v1_39_2@@QEAA@AEBV01@@Z __imp_??0VkSyntax@MaterialX_v1_39_2@@QEAA@XZ __imp_??1BlurNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ __imp_??1EsslShaderGenerator@MaterialX_v1_39_2@@UEAA@XZ __imp_??1EsslSyntax@MaterialX_v1_39_2@@UEAA@XZ __imp_??1GeomColorNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ __imp_??1GeomPropValueNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ __imp_??1GeomPropValueNodeGlslAsUniform@MaterialX_v1_39_2@@UEAA@XZ __imp_??1GlslImplementation@MaterialX_v1_39_2@@UEAA@XZ __imp_??1GlslResourceBindingContext@MaterialX_v1_39_2@@UEAA@XZ __imp_??1GlslShaderGenerator@MaterialX_v1_39_2@@UEAA@XZ __imp_??1GlslStructTypeSyntax@MaterialX_v1_39_2@@UEAA@XZ __imp_??1GlslSyntax@MaterialX_v1_39_2@@UEAA@XZ __imp_??1HeightToNormalNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ __imp_??1LightCompoundNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ __imp_??1LightNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ __imp_??1LightSamplerNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ __imp_??1LightShaderNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ __imp_??1NumLightsNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ __imp_??1SurfaceNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ __imp_??1UnlitSurfaceNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ __imp_??1VkResourceBindingContext@MaterialX_v1_39_2@@UEAA@XZ __imp_??1VkShaderGenerator@MaterialX_v1_39_2@@UEAA@XZ __imp_??1VkSyntax@MaterialX_v1_39_2@@UEAA@XZ __imp_??4BlurNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z __imp_??4BlurNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z __imp_??4EsslSyntax@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z __imp_??4EsslSyntax@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z __imp_??4GeomColorNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z __imp_??4GeomColorNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z __imp_??4GeomPropValueNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z __imp_??4GeomPropValueNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z __imp_??4GeomPropValueNodeGlslAsUniform@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z __imp_??4GeomPropValueNodeGlslAsUniform@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z __imp_??4GlslImplementation@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z __imp_??4GlslImplementation@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z __imp_??4GlslResourceBindingContext@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z __imp_??4GlslResourceBindingContext@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z __imp_??4GlslStructTypeSyntax@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z __imp_??4GlslStructTypeSyntax@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z __imp_??4GlslSyntax@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z __imp_??4GlslSyntax@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z __imp_??4HeightToNormalNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z __imp_??4HeightToNormalNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z __imp_??4LightCompoundNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z __imp_??4LightCompoundNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z __imp_??4LightSamplerNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z __imp_??4LightSamplerNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z __imp_??4LightShaderNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z __imp_??4LightShaderNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z __imp_??4NumLightsNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z __imp_??4NumLightsNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z __imp_??4UnlitSurfaceNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z __imp_??4UnlitSurfaceNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z __imp_??4VkResourceBindingContext@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z __imp_??4VkResourceBindingContext@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z __imp_??4VkSyntax@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z __imp_??4VkSyntax@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z __imp_??_7BlurNodeGlsl@MaterialX_v1_39_2@@6B@ __imp_??_7EsslShaderGenerator@MaterialX_v1_39_2@@6B@ __imp_??_7EsslSyntax@MaterialX_v1_39_2@@6B@ __imp_??_7GeomColorNodeGlsl@MaterialX_v1_39_2@@6B@ __imp_??_7GeomPropValueNodeGlsl@MaterialX_v1_39_2@@6B@ __imp_??_7GeomPropValueNodeGlslAsUniform@MaterialX_v1_39_2@@6B@ __imp_??_7GlslImplementation@MaterialX_v1_39_2@@6B@ __imp_??_7GlslResourceBindingContext@MaterialX_v1_39_2@@6B@ __imp_??_7GlslShaderGenerator@MaterialX_v1_39_2@@6B@ __imp_??_7GlslStructTypeSyntax@MaterialX_v1_39_2@@6B@ __imp_??_7GlslSyntax@MaterialX_v1_39_2@@6B@ __imp_??_7HeightToNormalNodeGlsl@MaterialX_v1_39_2@@6B@ __imp_??_7LightCompoundNodeGlsl@MaterialX_v1_39_2@@6B@ __imp_??_7LightNodeGlsl@MaterialX_v1_39_2@@6B@ __imp_??_7LightSamplerNodeGlsl@MaterialX_v1_39_2@@6B@ __imp_??_7LightShaderNodeGlsl@MaterialX_v1_39_2@@6B@ __imp_??_7NumLightsNodeGlsl@MaterialX_v1_39_2@@6B@ __imp_??_7SurfaceNodeGlsl@MaterialX_v1_39_2@@6B@ __imp_??_7UnlitSurfaceNodeGlsl@MaterialX_v1_39_2@@6B@ __imp_??_7VkResourceBindingContext@MaterialX_v1_39_2@@6B@ __imp_??_7VkShaderGenerator@MaterialX_v1_39_2@@6B@ __imp_??_7VkSyntax@MaterialX_v1_39_2@@6B@ __imp_?CONSTANT_QUALIFIER@GlslSyntax@MaterialX_v1_39_2@@2V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@B __imp_?FLAT_QUALIFIER@GlslSyntax@MaterialX_v1_39_2@@2V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@B __imp_?INPUT_QUALIFIER@GlslSyntax@MaterialX_v1_39_2@@2V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@B __imp_?OUTPUT_QUALIFIER@GlslSyntax@MaterialX_v1_39_2@@2V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@B __imp_?SOURCE_FILE_EXTENSION@GlslSyntax@MaterialX_v1_39_2@@2V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@B __imp_?TARGET@EsslShaderGenerator@MaterialX_v1_39_2@@2V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@B __imp_?TARGET@GlslShaderGenerator@MaterialX_v1_39_2@@2V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@B __imp_?TARGET@VkShaderGenerator@MaterialX_v1_39_2@@2V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@B __imp_?UNIFORM_QUALIFIER@GlslSyntax@MaterialX_v1_39_2@@2V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@B __imp_?VEC2_MEMBERS@GlslSyntax@MaterialX_v1_39_2@@2V?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@B __imp_?VEC3_MEMBERS@GlslSyntax@MaterialX_v1_39_2@@2V?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@B __imp_?VEC4_MEMBERS@GlslSyntax@MaterialX_v1_39_2@@2V?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@B __imp_?VERSION@EsslShaderGenerator@MaterialX_v1_39_2@@2V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@B __imp_?VERSION@GlslShaderGenerator@MaterialX_v1_39_2@@2V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@B __imp_?VERSION@VkShaderGenerator@MaterialX_v1_39_2@@2V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@B __imp_?acceptsInputType@HeightToNormalNodeGlsl@MaterialX_v1_39_2@@MEBA_NVTypeDesc@2@@Z __imp_?computeSampleOffsetStrings@HeightToNormalNodeGlsl@MaterialX_v1_39_2@@MEBAXAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@0IAEAV?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@4@@Z __imp_?create@BlurNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ __imp_?create@EsslShaderGenerator@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderGenerator@MaterialX_v1_39_2@@@std@@XZ __imp_?create@EsslSyntax@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VSyntax@MaterialX_v1_39_2@@@std@@XZ __imp_?create@GeomColorNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ __imp_?create@GeomPropValueNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ __imp_?create@GeomPropValueNodeGlslAsUniform@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ __imp_?create@GlslResourceBindingContext@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VGlslResourceBindingContext@MaterialX_v1_39_2@@@std@@_K0@Z __imp_?create@GlslShaderGenerator@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderGenerator@MaterialX_v1_39_2@@@std@@XZ __imp_?create@GlslSyntax@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VSyntax@MaterialX_v1_39_2@@@std@@XZ __imp_?create@HeightToNormalNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ __imp_?create@LightCompoundNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ __imp_?create@LightNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ __imp_?create@LightSamplerNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ __imp_?create@LightShaderNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ __imp_?create@NumLightsNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ __imp_?create@SurfaceNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ __imp_?create@UnlitSurfaceNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ __imp_?create@VkResourceBindingContext@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VVkResourceBindingContext@MaterialX_v1_39_2@@@std@@_K@Z __imp_?create@VkShaderGenerator@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderGenerator@MaterialX_v1_39_2@@@std@@XZ __imp_?create@VkSyntax@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VSyntax@MaterialX_v1_39_2@@@std@@XZ __imp_?createStructSyntax@GlslSyntax@MaterialX_v1_39_2@@MEBA?AV?$shared_ptr@VStructTypeSyntax@MaterialX_v1_39_2@@@std@@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@4@0000@Z __imp_?createVariables@GeomColorNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShader@2@@Z __imp_?createVariables@GeomPropValueNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShader@2@@Z __imp_?createVariables@GeomPropValueNodeGlslAsUniform@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShader@2@@Z __imp_?createVariables@HeightToNormalNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShader@2@@Z __imp_?createVariables@LightCompoundNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShader@2@@Z __imp_?createVariables@LightNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShader@2@@Z __imp_?createVariables@LightShaderNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShader@2@@Z __imp_?createVariables@NumLightsNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShader@2@@Z __imp_?createVariables@SurfaceNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShader@2@@Z __imp_?emitConstants@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitDirectives@EsslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitDirectives@GlslResourceBindingContext@MaterialX_v1_39_2@@UEAAXAEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitDirectives@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitDirectives@VkResourceBindingContext@MaterialX_v1_39_2@@UEAAXAEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitDirectives@VkShaderGenerator@MaterialX_v1_39_2@@UEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitFunctionCall@GeomColorNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitFunctionCall@GeomPropValueNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitFunctionCall@GeomPropValueNodeGlslAsUniform@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitFunctionCall@HeightToNormalNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitFunctionCall@LightCompoundNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitFunctionCall@LightNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitFunctionCall@LightShaderNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitFunctionCall@SurfaceNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitFunctionCall@UnlitSurfaceNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitFunctionDefinition@HeightToNormalNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitFunctionDefinition@LightCompoundNodeGlsl@MaterialX_v1_39_2@@IEBAXPEAVClosureContext@2@AEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitFunctionDefinition@LightCompoundNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitFunctionDefinition@LightSamplerNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitFunctionDefinition@NumLightsNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitInputs@EsslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitInputs@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitInputs@VkShaderGenerator@MaterialX_v1_39_2@@UEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitLightData@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitLightFunctionDefinitions@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEBVShaderGraph@2@AEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitLightLoop@SurfaceNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z __imp_?emitOutputs@EsslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitOutputs@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitOutputs@VkShaderGenerator@MaterialX_v1_39_2@@UEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitPixelStage@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEBVShaderGraph@2@AEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitResourceBindings@GlslResourceBindingContext@MaterialX_v1_39_2@@UEAAXAEAVGenContext@2@AEBVVariableBlock@2@AEAVShaderStage@2@@Z __imp_?emitResourceBindings@VkResourceBindingContext@MaterialX_v1_39_2@@UEAAXAEAVGenContext@2@AEBVVariableBlock@2@AEAVShaderStage@2@@Z __imp_?emitSamplingFunctionDefinition@BlurNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitSpecularEnvironment@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitStructuredResourceBindings@GlslResourceBindingContext@MaterialX_v1_39_2@@UEAAXAEAVGenContext@2@AEBVVariableBlock@2@AEAVShaderStage@2@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@3@Z __imp_?emitStructuredResourceBindings@VkResourceBindingContext@MaterialX_v1_39_2@@UEAAXAEAVGenContext@2@AEBVVariableBlock@2@AEAVShaderStage@2@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@3@Z __imp_?emitTransmissionRender@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitUniforms@EsslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitUniforms@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitVariableDeclaration@GlslShaderGenerator@MaterialX_v1_39_2@@UEBAXPEBVShaderPort@2@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAVGenContext@2@AEAVShaderStage@2@_N@Z __imp_?emitVertexStage@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEBVShaderGraph@2@AEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?enableSeparateBindingLocations@GlslResourceBindingContext@MaterialX_v1_39_2@@QEAAX_N@Z __imp_?generate@GlslShaderGenerator@MaterialX_v1_39_2@@UEBA?AV?$shared_ptr@VShader@MaterialX_v1_39_2@@@std@@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@4@V?$shared_ptr@VElement@MaterialX_v1_39_2@@@4@AEAVGenContext@2@@Z __imp_?getConstantQualifier@GlslSyntax@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ __imp_?getImplementation@GlslShaderGenerator@MaterialX_v1_39_2@@UEBA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@AEBVNodeDef@2@AEAVGenContext@2@@Z __imp_?getInputQualifier@GlslSyntax@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ __imp_?getInputQualifier@VkSyntax@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ __imp_?getOutputQualifier@GlslSyntax@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ __imp_?getResourceBindingContext@EsslShaderGenerator@MaterialX_v1_39_2@@MEBA?AV?$shared_ptr@VHwResourceBindingContext@MaterialX_v1_39_2@@@std@@AEAVGenContext@2@@Z __imp_?getResourceBindingContext@GlslShaderGenerator@MaterialX_v1_39_2@@MEBA?AV?$shared_ptr@VHwResourceBindingContext@MaterialX_v1_39_2@@@std@@AEAVGenContext@2@@Z __imp_?getResourceBindingContext@VkShaderGenerator@MaterialX_v1_39_2@@MEBA?AV?$shared_ptr@VHwResourceBindingContext@MaterialX_v1_39_2@@@std@@AEAVGenContext@2@@Z __imp_?getSourceFileExtension@GlslSyntax@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ __imp_?getTarget@EsslShaderGenerator@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ __imp_?getTarget@GlslImplementation@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ __imp_?getTarget@GlslShaderGenerator@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ __imp_?getTarget@HeightToNormalNodeGlsl@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ __imp_?getTarget@LightCompoundNodeGlsl@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ __imp_?getTarget@LightShaderNodeGlsl@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ __imp_?getTarget@VkShaderGenerator@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ __imp_?getUniformQualifier@GlslSyntax@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ __imp_?getValue@GlslStructTypeSyntax@MaterialX_v1_39_2@@UEBA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEBVValue@2@_N@Z __imp_?getVersion@EsslShaderGenerator@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ __imp_?getVersion@GlslShaderGenerator@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ __imp_?getVersion@VkShaderGenerator@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ __imp_?getVertexDataPrefix@EsslShaderGenerator@MaterialX_v1_39_2@@UEBA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEBVVariableBlock@2@@Z __imp_?getVertexDataPrefix@GlslShaderGenerator@MaterialX_v1_39_2@@UEBA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEBVVariableBlock@2@@Z __imp_?getVertexDataPrefix@VkShaderGenerator@MaterialX_v1_39_2@@UEBA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEBVVariableBlock@2@@Z __imp_?initialize@GlslResourceBindingContext@MaterialX_v1_39_2@@UEAAXXZ __imp_?initialize@LightCompoundNodeGlsl@MaterialX_v1_39_2@@UEAAXAEBVInterfaceElement@2@AEAVGenContext@2@@Z __imp_?initialize@LightShaderNodeGlsl@MaterialX_v1_39_2@@UEAAXAEBVInterfaceElement@2@AEAVGenContext@2@@Z __imp_?initialize@VkResourceBindingContext@MaterialX_v1_39_2@@UEAAXXZ __imp_?isEditable@GeomPropValueNodeGlsl@MaterialX_v1_39_2@@UEBA_NAEBVShaderInput@2@@Z __imp_?remapEnumeration@GlslSyntax@MaterialX_v1_39_2@@UEBA_NAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@VTypeDesc@2@0AEAU?$pair@VTypeDesc@MaterialX_v1_39_2@@V?$shared_ptr@VValue@MaterialX_v1_39_2@@@std@@@4@@Z __imp_?requiresLighting@GlslShaderGenerator@MaterialX_v1_39_2@@MEBA_NAEBVShaderGraph@2@@Z __imp_?toVec4@GlslShaderGenerator@MaterialX_v1_39_2@@KAXPEBVTypeDesc@2@AEAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z __imp_?toVec4@GlslShaderGenerator@MaterialX_v1_39_2@@KAXVTypeDesc@2@AEAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z __imp_?typeSupported@GlslSyntax@MaterialX_v1_39_2@@UEBA_NPEBVTypeDesc@2@@Z MaterialXGenGlsl_NULL_THUNK_DATA 
//              -1                      0       21        `
MaterialXGenGlsl.dll 
/0              -1                      0       530       `
d� 圬容         .debug$S        J   �               @ B.idata$2           �   �          @ 0�.idata$6             �           @  �    	     MaterialXGenGlsl.dll'    �         膗Microsoft (R) LINK                                          MaterialXGenGlsl.dll  @comp.id膗��                  .idata$2@  �   h .idata$6        .idata$4@  �    h .idata$5@  �    h     )                B            d   __IMPORT_DESCRIPTOR_MaterialXGenGlsl __NULL_IMPORT_DESCRIPTOR MaterialXGenGlsl_NULL_THUNK_DATA /0              -1                      0       259       `
d� h �          .debug$S        J   d               @ B.idata$3           �               @ 0�    	     MaterialXGenGlsl.dll'    �         膗Microsoft (R) LINK                    @comp.id膗��                     __NULL_IMPORT_DESCRIPTOR 
/0              -1                      0       304       `
d� 嵮�          .debug$S        J   �               @ B.idata$5           �               @ @�.idata$4           �               @ @�    	     MaterialXGenGlsl.dll'    �         膗Microsoft (R) LINK                @comp.id膗��                  &   MaterialXGenGlsl_NULL_THUNK_DATA /0              -1                      0       93        `
  ��  d�:a霫      ??0BlurNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z MaterialXGenGlsl.dll 
/0              -1                      0       91        `
  ��  d唀c~鯣     ??0BlurNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z MaterialXGenGlsl.dll 
/0              -1                      0       84        `
  ��  d嘂儣銨     ??0BlurNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ MaterialXGenGlsl.dll /0              -1                      0       100       `
  ��  d�?珁騊     ??0EsslShaderGenerator@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z MaterialXGenGlsl.dll /0              -1                      0       98        `
  ��  d嗎w橃N     ??0EsslShaderGenerator@MaterialX_v1_39_2@@QEAA@AEBV01@@Z MaterialXGenGlsl.dll /0              -1                      0       91        `
  ��  d嗻�蔊     ??0EsslShaderGenerator@MaterialX_v1_39_2@@QEAA@XZ MaterialXGenGlsl.dll 
/0              -1                      0       91        `
  ��  d咥G     ??0EsslSyntax@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z MaterialXGenGlsl.dll 
/0              -1                      0       89        `
  ��  d啈脲臙     ??0EsslSyntax@MaterialX_v1_39_2@@QEAA@AEBV01@@Z MaterialXGenGlsl.dll 
/0              -1                      0       82        `
  ��  d�$.�>     ??0EsslSyntax@MaterialX_v1_39_2@@QEAA@XZ MaterialXGenGlsl.dll /0              -1                      0       98        `
  ��  d唚c/餘   	  ??0GeomColorNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z MaterialXGenGlsl.dll /0              -1                      0       96        `
  ��  d�	縄芁   
  ??0GeomColorNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z MaterialXGenGlsl.dll /0              -1                      0       89        `
  ��  d�'&&譋     ??0GeomColorNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ MaterialXGenGlsl.dll 
/0              -1                      0       102       `
  ��  d員{蘎     ??0GeomPropValueNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z MaterialXGenGlsl.dll /0              -1                      0       100       `
  ��  d哫疗跴   
  ??0GeomPropValueNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z MaterialXGenGlsl.dll /0              -1                      0       93        `
  ��  d啙蠌�I     ??0GeomPropValueNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ MaterialXGenGlsl.dll 
/0              -1                      0       111       `
  ��  d�/翟鉡     ??0GeomPropValueNodeGlslAsUniform@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z MaterialXGenGlsl.dll 
/0              -1                      0       109       `
  ��  d喚uW頨     ??0GeomPropValueNodeGlslAsUniform@MaterialX_v1_39_2@@QEAA@AEBV01@@Z MaterialXGenGlsl.dll 
/0              -1                      0       102       `
  ��  d嘃�R     ??0GeomPropValueNodeGlslAsUniform@MaterialX_v1_39_2@@QEAA@XZ MaterialXGenGlsl.dll /0              -1                      0       90        `
  ��  d喪嚗虵     ??0GlslImplementation@MaterialX_v1_39_2@@IEAA@XZ MaterialXGenGlsl.dll /0              -1                      0       99        `
  ��  d喢a闛     ??0GlslImplementation@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z MaterialXGenGlsl.dll 
/0              -1                      0       97        `
  ��  d哤�!腗     ??0GlslImplementation@MaterialX_v1_39_2@@QEAA@AEBV01@@Z MaterialXGenGlsl.dll 
/0              -1                      0       107       `
  ��  d哃LU軼     ??0GlslResourceBindingContext@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z MaterialXGenGlsl.dll 
/0              -1                      0       105       `
  ��  d唎�$訳     ??0GlslResourceBindingContext@MaterialX_v1_39_2@@QEAA@AEBV01@@Z MaterialXGenGlsl.dll 
/0              -1                      0       101       `
  ��  d嗁嫰霶     ??0GlslResourceBindingContext@MaterialX_v1_39_2@@QEAA@_K0@Z MaterialXGenGlsl.dll 
/0              -1                      0       100       `
  ��  d啨敕齈     ??0GlslShaderGenerator@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z MaterialXGenGlsl.dll /0              -1                      0       98        `
  ��  d哫K傊N     ??0GlslShaderGenerator@MaterialX_v1_39_2@@QEAA@AEBV01@@Z MaterialXGenGlsl.dll /0              -1                      0       91        `
  ��  d喤"｜G     ??0GlslShaderGenerator@MaterialX_v1_39_2@@QEAA@XZ MaterialXGenGlsl.dll 
/0              -1                      0       101       `
  ��  d嗘)读Q     ??0GlslStructTypeSyntax@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z MaterialXGenGlsl.dll 
/0              -1                      0       99        `
  ��  d�奔譕     ??0GlslStructTypeSyntax@MaterialX_v1_39_2@@QEAA@AEBV01@@Z MaterialXGenGlsl.dll 
/0              -1                      0       330       `
  ��  d唋j9�6    ??0GlslStructTypeSyntax@MaterialX_v1_39_2@@QEAA@PEBVSyntax@1@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@1111AEBV?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@4@@Z MaterialXGenGlsl.dll /0              -1                      0       91        `
  ��  d啳�繥     ??0GlslSyntax@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z MaterialXGenGlsl.dll 
/0              -1                      0       89        `
  ��  d啎鷟�E     ??0GlslSyntax@MaterialX_v1_39_2@@QEAA@AEBV01@@Z MaterialXGenGlsl.dll 
/0              -1                      0       82        `
  ��  d哾NQ�>      ??0GlslSyntax@MaterialX_v1_39_2@@QEAA@XZ MaterialXGenGlsl.dll /0              -1                      0       103       `
  ��  d嗶�蚐   !  ??0HeightToNormalNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z MaterialXGenGlsl.dll 
/0              -1                      0       101       `
  ��  d喨孰鞶   "  ??0HeightToNormalNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z MaterialXGenGlsl.dll 
/0              -1                      0       94        `
  ��  d啒G蠮   #  ??0HeightToNormalNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ MaterialXGenGlsl.dll /0              -1                      0       102       `
  ��  d啑�+覴   $  ??0LightCompoundNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z MaterialXGenGlsl.dll /0              -1                      0       100       `
  ��  d唌|稣P   %  ??0LightCompoundNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z MaterialXGenGlsl.dll /0              -1                      0       93        `
  ��  d啴%肿I   &  ??0LightCompoundNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ MaterialXGenGlsl.dll 
/0              -1                      0       94        `
  ��  d唒@贘   '  ??0LightNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z MaterialXGenGlsl.dll /0              -1                      0       92        `
  ��  d�E鲴H   (  ??0LightNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z MaterialXGenGlsl.dll /0              -1                      0       85        `
  ��  d問腽鵄   )  ??0LightNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ MaterialXGenGlsl.dll 
/0              -1                      0       101       `
  ��  d嗱8   *  ??0LightSamplerNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z MaterialXGenGlsl.dll 
/0              -1                      0       99        `
  ��  d嗈骏設   +  ??0LightSamplerNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z MaterialXGenGlsl.dll 
/0              -1                      0       92        `
  ��  d嗱�轍   ,  ??0LightSamplerNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ MaterialXGenGlsl.dll /0              -1                      0       100       `
  ��  d唈�齈   -  ??0LightShaderNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z MaterialXGenGlsl.dll /0              -1                      0       98        `
  ��  d喆&鹌N   .  ??0LightShaderNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z MaterialXGenGlsl.dll /0              -1                      0       91        `
  ��  d嗋�鱃   /  ??0LightShaderNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ MaterialXGenGlsl.dll 
/0              -1                      0       98        `
  ��  d嗙纍袾   0  ??0NumLightsNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z MaterialXGenGlsl.dll /0              -1                      0       96        `
  ��  d啨孹躄   1  ??0NumLightsNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z MaterialXGenGlsl.dll /0              -1                      0       89        `
  ��  d�.+貳   2  ??0NumLightsNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ MaterialXGenGlsl.dll 
/0              -1                      0       96        `
  ��  d嗞焾馤   3  ??0SurfaceNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z MaterialXGenGlsl.dll /0              -1                      0       94        `
  ��  d嗏�'跩   4  ??0SurfaceNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z MaterialXGenGlsl.dll /0              -1                      0       87        `
  ��  d�=2糸C   5  ??0SurfaceNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ MaterialXGenGlsl.dll 
/0              -1                      0       101       `
  ��  d哃齁鞶   6  ??0UnlitSurfaceNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z MaterialXGenGlsl.dll 
/0              -1                      0       99        `
  ��  d唲z佭O   7  ??0UnlitSurfaceNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z MaterialXGenGlsl.dll 
/0              -1                      0       92        `
  ��  d喲虅薍   8  ??0UnlitSurfaceNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ MaterialXGenGlsl.dll /0              -1                      0       105       `
  ��  d唄r諹   9  ??0VkResourceBindingContext@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z MaterialXGenGlsl.dll 
/0              -1                      0       103       `
  ��  d喖蹁薙   :  ??0VkResourceBindingContext@MaterialX_v1_39_2@@QEAA@AEBV01@@Z MaterialXGenGlsl.dll 
/0              -1                      0       98        `
  ��  d嗀覰   ;  ??0VkResourceBindingContext@MaterialX_v1_39_2@@QEAA@_K@Z MaterialXGenGlsl.dll /0              -1                      0       98        `
  ��  d喡�N   <  ??0VkShaderGenerator@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z MaterialXGenGlsl.dll /0              -1                      0       96        `
  ��  d唕z袜L   =  ??0VkShaderGenerator@MaterialX_v1_39_2@@QEAA@AEBV01@@Z MaterialXGenGlsl.dll /0              -1                      0       89        `
  ��  d�&Y徬E   >  ??0VkShaderGenerator@MaterialX_v1_39_2@@QEAA@XZ MaterialXGenGlsl.dll 
/0              -1                      0       89        `
  ��  d�,`O鳨   ?  ??0VkSyntax@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z MaterialXGenGlsl.dll 
/0              -1                      0       87        `
  ��  d嗗�&藽   @  ??0VkSyntax@MaterialX_v1_39_2@@QEAA@AEBV01@@Z MaterialXGenGlsl.dll 
/0              -1                      0       80        `
  ��  d啽卂�<   A  ??0VkSyntax@MaterialX_v1_39_2@@QEAA@XZ MaterialXGenGlsl.dll /0              -1                      0       84        `
  ��  d唗葻茾   B  ??1BlurNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ MaterialXGenGlsl.dll /0              -1                      0       91        `
  ��  d喺捡郍   C  ??1EsslShaderGenerator@MaterialX_v1_39_2@@UEAA@XZ MaterialXGenGlsl.dll 
/0              -1                      0       82        `
  ��  d�4列�>   D  ??1EsslSyntax@MaterialX_v1_39_2@@UEAA@XZ MaterialXGenGlsl.dll /0              -1                      0       89        `
  ��  d唭	愬E   E  ??1GeomColorNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ MaterialXGenGlsl.dll 
/0              -1                      0       93        `
  ��  d咲茇繧   F  ??1GeomPropValueNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ MaterialXGenGlsl.dll 
/0              -1                      0       102       `
  ��  d唂y�R   G  ??1GeomPropValueNodeGlslAsUniform@MaterialX_v1_39_2@@UEAA@XZ MaterialXGenGlsl.dll /0              -1                      0       90        `
  ��  d嗺兑F   H  ??1GlslImplementation@MaterialX_v1_39_2@@UEAA@XZ MaterialXGenGlsl.dll /0              -1                      0       98        `
  ��  d喐g}翹   I  ??1GlslResourceBindingContext@MaterialX_v1_39_2@@UEAA@XZ MaterialXGenGlsl.dll /0              -1                      0       91        `
  ��  d咷j栃G   J  ??1GlslShaderGenerator@MaterialX_v1_39_2@@UEAA@XZ MaterialXGenGlsl.dll 
/0              -1                      0       92        `
  ��  d喴�酘   K  ??1GlslStructTypeSyntax@MaterialX_v1_39_2@@UEAA@XZ MaterialXGenGlsl.dll /0              -1                      0       82        `
  ��  d�S忊>   L  ??1GlslSyntax@MaterialX_v1_39_2@@UEAA@XZ MaterialXGenGlsl.dll /0              -1                      0       94        `
  ��  d啘o薐   M  ??1HeightToNormalNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ MaterialXGenGlsl.dll /0              -1                      0       93        `
  ��  d啒郔   N  ??1LightCompoundNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ MaterialXGenGlsl.dll 
/0              -1                      0       85        `
  ��  d�.�-鳤   O  ??1LightNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ MaterialXGenGlsl.dll 
/0              -1                      0       92        `
  ��  d唵*%豀   P  ??1LightSamplerNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ MaterialXGenGlsl.dll /0              -1                      0       91        `
  ��  d喼0尚G   Q  ??1LightShaderNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ MaterialXGenGlsl.dll 
/0              -1                      0       89        `
  ��  d��8鼸   R  ??1NumLightsNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ MaterialXGenGlsl.dll 
/0              -1                      0       87        `
  ��  d啍げ虲   S  ??1SurfaceNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ MaterialXGenGlsl.dll 
/0              -1                      0       92        `
  ��  d��?驢   T  ??1UnlitSurfaceNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ MaterialXGenGlsl.dll /0              -1                      0       96        `
  ��  d喦酟   U  ??1VkResourceBindingContext@MaterialX_v1_39_2@@UEAA@XZ MaterialXGenGlsl.dll /0              -1                      0       89        `
  ��  d啋uL螮   V  ??1VkShaderGenerator@MaterialX_v1_39_2@@UEAA@XZ MaterialXGenGlsl.dll 
/0              -1                      0       80        `
  ��  d喣貨�<   W  ??1VkSyntax@MaterialX_v1_39_2@@UEAA@XZ MaterialXGenGlsl.dll /0              -1                      0       99        `
  ��  d嗈�麿   X  ??4BlurNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z MaterialXGenGlsl.dll 
/0              -1                      0       97        `
  ��  d唓�譓   Y  ??4BlurNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z MaterialXGenGlsl.dll 
/0              -1                      0       97        `
  ��  d�9t颩   Z  ??4EsslSyntax@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z MaterialXGenGlsl.dll 
/0              -1                      0       95        `
  ��  d哷陳餕   [  ??4EsslSyntax@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z MaterialXGenGlsl.dll 
/0              -1                      0       104       `
  ��  d哴#`蔜   \  ??4GeomColorNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z MaterialXGenGlsl.dll /0              -1                      0       102       `
  ��  d嗐u 罵   ]  ??4GeomColorNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z MaterialXGenGlsl.dll /0              -1                      0       108       `
  ��  d喰噉譞   ^  ??4GeomPropValueNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z MaterialXGenGlsl.dll /0              -1                      0       106       `
  ��  d啑屎馰   _  ??4GeomPropValueNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z MaterialXGenGlsl.dll /0              -1                      0       117       `
  ��  d�#悖臿   `  ??4GeomPropValueNodeGlslAsUniform@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z MaterialXGenGlsl.dll 
/0              -1                      0       115       `
  ��  d哃穁蔩   a  ??4GeomPropValueNodeGlslAsUniform@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z MaterialXGenGlsl.dll 
/0              -1                      0       105       `
  ��  d唀"骥U   b  ??4GlslImplementation@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z MaterialXGenGlsl.dll 
/0              -1                      0       103       `
  ��  d哸ㄐ餝   c  ??4GlslImplementation@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z MaterialXGenGlsl.dll 
/0              -1                      0       113       `
  ��  d哷�襗   d  ??4GlslResourceBindingContext@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z MaterialXGenGlsl.dll 
/0              -1                      0       111       `
  ��  d哾貲蝃   e  ??4GlslResourceBindingContext@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z MaterialXGenGlsl.dll 
/0              -1                      0       107       `
  ��  d唓箨騑   f  ??4GlslStructTypeSyntax@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z MaterialXGenGlsl.dll 
/0              -1                      0       105       `
  ��  d唓嗜荱   g  ??4GlslStructTypeSyntax@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z MaterialXGenGlsl.dll 
/0              -1                      0       97        `
  ��  d�k国M   h  ??4GlslSyntax@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z MaterialXGenGlsl.dll 
/0              -1                      0       95        `
  ��  d��袺   i  ??4GlslSyntax@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z MaterialXGenGlsl.dll 
/0              -1                      0       109       `
  ��  d嘄耏   j  ??4HeightToNormalNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z MaterialXGenGlsl.dll 
/0              -1                      0       107       `
  ��  d唴B�W   k  ??4HeightToNormalNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z MaterialXGenGlsl.dll 
/0              -1                      0       108       `
  ��  d�0|裍   l  ??4LightCompoundNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z MaterialXGenGlsl.dll /0              -1                      0       106       `
  ��  d嗠A吲V   m  ??4LightCompoundNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z MaterialXGenGlsl.dll /0              -1                      0       107       `
  ��  d� 蕝颳   n  ??4LightSamplerNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z MaterialXGenGlsl.dll 
/0              -1                      0       105       `
  ��  d�JQ襏   o  ??4LightSamplerNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z MaterialXGenGlsl.dll 
/0              -1                      0       106       `
  ��  d啇^麇V   p  ??4LightShaderNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z MaterialXGenGlsl.dll /0              -1                      0       104       `
  ��  d啺?萒   q  ??4LightShaderNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z MaterialXGenGlsl.dll /0              -1                      0       104       `
  ��  d啨�覶   r  ??4NumLightsNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z MaterialXGenGlsl.dll /0              -1                      0       102       `
  ��  d咺+鮎   s  ??4NumLightsNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z MaterialXGenGlsl.dll /0              -1                      0       107       `
  ��  d�
茁鸚   t  ??4UnlitSurfaceNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z MaterialXGenGlsl.dll 
/0              -1                      0       105       `
  ��  d唜劖餟   u  ??4UnlitSurfaceNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z MaterialXGenGlsl.dll 
/0              -1                      0       111       `
  ��  d喦骩   v  ??4VkResourceBindingContext@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z MaterialXGenGlsl.dll 
/0              -1                      0       109       `
  ��  d�
v撛Y   w  ??4VkResourceBindingContext@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z MaterialXGenGlsl.dll 
/0              -1                      0       95        `
  ��  d咢
r�K   x  ??4VkSyntax@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z MaterialXGenGlsl.dll 
/0              -1                      0       93        `
  ��  d唝�'鉏   y  ??4VkSyntax@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z MaterialXGenGlsl.dll 
/0              -1                      0       81        `
  ��  d嗶'袤=   z  ??_7BlurNodeGlsl@MaterialX_v1_39_2@@6B@ MaterialXGenGlsl.dll 
/0              -1                      0       88        `
  ��  d喿鸢霥   {  ??_7EsslShaderGenerator@MaterialX_v1_39_2@@6B@ MaterialXGenGlsl.dll /0              -1                      0       79        `
  ��  d啇5�;   |  ??_7EsslSyntax@MaterialX_v1_39_2@@6B@ MaterialXGenGlsl.dll 
/0              -1                      0       86        `
  ��  d唒搧藼   }  ??_7GeomColorNodeGlsl@MaterialX_v1_39_2@@6B@ MaterialXGenGlsl.dll /0              -1                      0       90        `
  ��  d喸餭誇   ~  ??_7GeomPropValueNodeGlsl@MaterialX_v1_39_2@@6B@ MaterialXGenGlsl.dll /0              -1                      0       99        `
  ��  d哻�錙     ??_7GeomPropValueNodeGlslAsUniform@MaterialX_v1_39_2@@6B@ MaterialXGenGlsl.dll 
/0              -1                      0       87        `
  ��  d啠潰霤   �  ??_7GlslImplementation@MaterialX_v1_39_2@@6B@ MaterialXGenGlsl.dll 
/0              -1                      0       95        `
  ��  d咹�跭   �  ??_7GlslResourceBindingContext@MaterialX_v1_39_2@@6B@ MaterialXGenGlsl.dll 
/0              -1                      0       88        `
  ��  d�狝餌   �  ??_7GlslShaderGenerator@MaterialX_v1_39_2@@6B@ MaterialXGenGlsl.dll /0              -1                      0       89        `
  ��  d�&醛鱁   �  ??_7GlslStructTypeSyntax@MaterialX_v1_39_2@@6B@ MaterialXGenGlsl.dll 
/0              -1                      0       79        `
  ��  d喤:W�;   �  ??_7GlslSyntax@MaterialX_v1_39_2@@6B@ MaterialXGenGlsl.dll 
/0              -1                      0       91        `
  ��  d唫颀褿   �  ??_7HeightToNormalNodeGlsl@MaterialX_v1_39_2@@6B@ MaterialXGenGlsl.dll 
/0              -1                      0       90        `
  ��  d喢xt鯢   �  ??_7LightCompoundNodeGlsl@MaterialX_v1_39_2@@6B@ MaterialXGenGlsl.dll /0              -1                      0       82        `
  ��  d� 
u�>   �  ??_7LightNodeGlsl@MaterialX_v1_39_2@@6B@ MaterialXGenGlsl.dll /0              -1                      0       89        `
  ��  d唫P 驟   �  ??_7LightSamplerNodeGlsl@MaterialX_v1_39_2@@6B@ MaterialXGenGlsl.dll 
/0              -1                      0       88        `
  ��  d喸爎鵇   �  ??_7LightShaderNodeGlsl@MaterialX_v1_39_2@@6B@ MaterialXGenGlsl.dll /0              -1                      0       86        `
  ��  d�'ё贐   �  ??_7NumLightsNodeGlsl@MaterialX_v1_39_2@@6B@ MaterialXGenGlsl.dll /0              -1                      0       84        `
  ��  d啀�蔃   �  ??_7SurfaceNodeGlsl@MaterialX_v1_39_2@@6B@ MaterialXGenGlsl.dll /0              -1                      0       89        `
  ��  d唒釫   �  ??_7UnlitSurfaceNodeGlsl@MaterialX_v1_39_2@@6B@ MaterialXGenGlsl.dll 
/0              -1                      0       93        `
  ��  d�絔螴   �  ??_7VkResourceBindingContext@MaterialX_v1_39_2@@6B@ MaterialXGenGlsl.dll 
/0              -1                      0       86        `
  ��  d�0z艬   �  ??_7VkShaderGenerator@MaterialX_v1_39_2@@6B@ MaterialXGenGlsl.dll /0              -1                      0       77        `
  ��  d嗎�>�9   �  ??_7VkSyntax@MaterialX_v1_39_2@@6B@ MaterialXGenGlsl.dll 
/0              -1                      0       156       `
  ��  d唖腱讏   �  ?CONSTANT_QUALIFIER@GlslSyntax@MaterialX_v1_39_2@@2V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@B MaterialXGenGlsl.dll /0              -1                      0       152       `
  ��  d哵嚊讋   �  ?FLAT_QUALIFIER@GlslSyntax@MaterialX_v1_39_2@@2V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@B MaterialXGenGlsl.dll /0              -1                      0       153       `
  ��  d嗼牭閰   �  ?INPUT_QUALIFIER@GlslSyntax@MaterialX_v1_39_2@@2V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@B MaterialXGenGlsl.dll 
/0              -1                      0       154       `
  ��  d�kX绬   �  ?OUTPUT_QUALIFIER@GlslSyntax@MaterialX_v1_39_2@@2V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@B MaterialXGenGlsl.dll /0              -1                      0       159       `
  ��  d啒拧詪   �  ?SOURCE_FILE_EXTENSION@GlslSyntax@MaterialX_v1_39_2@@2V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@B MaterialXGenGlsl.dll 
/0              -1                      0       153       `
  ��  d唘�=鲄   �  ?TARGET@EsslShaderGenerator@MaterialX_v1_39_2@@2V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@B MaterialXGenGlsl.dll 
/0              -1                      0       153       `
  ��  d嗧A邀�   �  ?TARGET@GlslShaderGenerator@MaterialX_v1_39_2@@2V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@B MaterialXGenGlsl.dll 
/0              -1                      0       151       `
  ��  d喆鴠韮   �  ?TARGET@VkShaderGenerator@MaterialX_v1_39_2@@2V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@B MaterialXGenGlsl.dll 
/0              -1                      0       155       `
  ��  d啅�羾   �  ?UNIFORM_QUALIFIER@GlslSyntax@MaterialX_v1_39_2@@2V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@B MaterialXGenGlsl.dll 
/0              -1                      0       244       `
  ��  d唂*栩�   �  ?VEC2_MEMBERS@GlslSyntax@MaterialX_v1_39_2@@2V?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@B MaterialXGenGlsl.dll /0              -1                      0       244       `
  ��  d�'狼�   �  ?VEC3_MEMBERS@GlslSyntax@MaterialX_v1_39_2@@2V?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@B MaterialXGenGlsl.dll /0              -1                      0       244       `
  ��  d�8!�袜   �  ?VEC4_MEMBERS@GlslSyntax@MaterialX_v1_39_2@@2V?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@B MaterialXGenGlsl.dll /0              -1                      0       154       `
  ��  d唄郻艈   �  ?VERSION@EsslShaderGenerator@MaterialX_v1_39_2@@2V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@B MaterialXGenGlsl.dll /0              -1                      0       154       `
  ��  d唺陠   �  ?VERSION@GlslShaderGenerator@MaterialX_v1_39_2@@2V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@B MaterialXGenGlsl.dll /0              -1                      0       152       `
  ��  d咼�)謩   �  ?VERSION@VkShaderGenerator@MaterialX_v1_39_2@@2V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@B MaterialXGenGlsl.dll /0              -1                      0       122       `
  ��  d�鱀鄁   �  ?acceptsInputType@HeightToNormalNodeGlsl@MaterialX_v1_39_2@@MEBA_NVTypeDesc@2@@Z MaterialXGenGlsl.dll /0              -1                      0       342       `
  ��  d哠擴霣  �  ?computeSampleOffsetStrings@HeightToNormalNodeGlsl@MaterialX_v1_39_2@@MEBAXAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@0IAEAV?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@4@@Z MaterialXGenGlsl.dll /0              -1                      0       143       `
  ��  d啹刪讃   �  ?create@BlurNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ MaterialXGenGlsl.dll 
/0              -1                      0       151       `
  ��  d喯�   �  ?create@EsslShaderGenerator@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderGenerator@MaterialX_v1_39_2@@@std@@XZ MaterialXGenGlsl.dll 
/0              -1                      0       133       `
  ��  d唄{q輖   �  ?create@EsslSyntax@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VSyntax@MaterialX_v1_39_2@@@std@@XZ MaterialXGenGlsl.dll 
/0              -1                      0       148       `
  ��  d�$埘�   �  ?create@GeomColorNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ MaterialXGenGlsl.dll /0              -1                      0       152       `
  ��  d哴=覄   �  ?create@GeomPropValueNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ MaterialXGenGlsl.dll /0              -1                      0       161       `
  ��  d��鼚   �  ?create@GeomPropValueNodeGlslAsUniform@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ MaterialXGenGlsl.dll 
/0              -1                      0       172       `
  ��  d啓�
   �  ?create@GlslResourceBindingContext@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VGlslResourceBindingContext@MaterialX_v1_39_2@@@std@@_K0@Z MaterialXGenGlsl.dll /0              -1                      0       151       `
  ��  d咰軏   �  ?create@GlslShaderGenerator@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderGenerator@MaterialX_v1_39_2@@@std@@XZ MaterialXGenGlsl.dll 
/0              -1                      0       133       `
  ��  d唨�9鄎   �  ?create@GlslSyntax@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VSyntax@MaterialX_v1_39_2@@@std@@XZ MaterialXGenGlsl.dll 
/0              -1                      0       153       `
  ��  d嗸;c鲄   �  ?create@HeightToNormalNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ MaterialXGenGlsl.dll 
/0              -1                      0       152       `
  ��  d唊�駝   �  ?create@LightCompoundNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ MaterialXGenGlsl.dll /0              -1                      0       144       `
  ��  d咷襸雦   �  ?create@LightNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ MaterialXGenGlsl.dll /0              -1                      0       151       `
  ��  d唈柤韮   �  ?create@LightSamplerNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ MaterialXGenGlsl.dll 
/0              -1                      0       150       `
  ��  d�t疬�   �  ?create@LightShaderNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ MaterialXGenGlsl.dll /0              -1                      0       148       `
  ��  d啰�3莯   �  ?create@NumLightsNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ MaterialXGenGlsl.dll /0              -1                      0       146       `
  ��  d啓@1鐍   �  ?create@SurfaceNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ MaterialXGenGlsl.dll /0              -1                      0       151       `
  ��  d哵饺脙   �  ?create@UnlitSurfaceNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ MaterialXGenGlsl.dll 
/0              -1                      0       167       `
  ��  d哋5B艙   �  ?create@VkResourceBindingContext@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VVkResourceBindingContext@MaterialX_v1_39_2@@@std@@_K@Z MaterialXGenGlsl.dll 
/0              -1                      0       149       `
  ��  d啺隩鈦   �  ?create@VkShaderGenerator@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderGenerator@MaterialX_v1_39_2@@@std@@XZ MaterialXGenGlsl.dll 
/0              -1                      0       131       `
  ��  d唕i   �  ?create@VkSyntax@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VSyntax@MaterialX_v1_39_2@@@std@@XZ MaterialXGenGlsl.dll 
/0              -1                      0       223       `
  ��  d�钸跛   �  ?createStructSyntax@GlslSyntax@MaterialX_v1_39_2@@MEBA?AV?$shared_ptr@VStructTypeSyntax@MaterialX_v1_39_2@@@std@@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@4@0000@Z MaterialXGenGlsl.dll 
/0              -1                      0       150       `
  ��  d�&S袀   �  ?createVariables@GeomColorNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShader@2@@Z MaterialXGenGlsl.dll /0              -1                      0       154       `
  ��  d嘄踓詥   �  ?createVariables@GeomPropValueNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShader@2@@Z MaterialXGenGlsl.dll /0              -1                      0       163       `
  ��  d咲�   �  ?createVariables@GeomPropValueNodeGlslAsUniform@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShader@2@@Z MaterialXGenGlsl.dll 
/0              -1                      0       155       `
  ��  d喥Ch膰   �  ?createVariables@HeightToNormalNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShader@2@@Z MaterialXGenGlsl.dll 
/0              -1                      0       154       `
  ��  d啞�陠   �  ?createVariables@LightCompoundNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShader@2@@Z MaterialXGenGlsl.dll /0              -1                      0       146       `
  ��  d喆ю鑯   �  ?createVariables@LightNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShader@2@@Z MaterialXGenGlsl.dll /0              -1                      0       152       `
  ��  d哅組蟿   �  ?createVariables@LightShaderNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShader@2@@Z MaterialXGenGlsl.dll /0              -1                      0       150       `
  ��  d啑骱蕚   �  ?createVariables@NumLightsNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShader@2@@Z MaterialXGenGlsl.dll /0              -1                      0       148       `
  ��  d�:芰�   �  ?createVariables@SurfaceNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShader@2@@Z MaterialXGenGlsl.dll /0              -1                      0       138       `
  ��  d喩扤藇   �  ?emitConstants@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z MaterialXGenGlsl.dll /0              -1                      0       139       `
  ��  d嗐�5鵺   �  ?emitDirectives@EsslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z MaterialXGenGlsl.dll 
/0              -1                      0       146       `
  ��  d唘D沐~   �  ?emitDirectives@GlslResourceBindingContext@MaterialX_v1_39_2@@UEAAXAEAVGenContext@2@AEAVShaderStage@2@@Z MaterialXGenGlsl.dll /0              -1                      0       139       `
  ��  d啿餔蘷   �  ?emitDirectives@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z MaterialXGenGlsl.dll 
/0              -1                      0       144       `
  ��  d喞捦|   �  ?emitDirectives@VkResourceBindingContext@MaterialX_v1_39_2@@UEAAXAEAVGenContext@2@AEAVShaderStage@2@@Z MaterialXGenGlsl.dll /0              -1                      0       137       `
  ��  d哃�鉼   �  ?emitDirectives@VkShaderGenerator@MaterialX_v1_39_2@@UEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z MaterialXGenGlsl.dll 
/0              -1                      0       156       `
  ��  d喴�
鴪   �  ?emitFunctionCall@GeomColorNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z MaterialXGenGlsl.dll /0              -1                      0       160       `
  ��  d�!h髮   �  ?emitFunctionCall@GeomPropValueNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z MaterialXGenGlsl.dll /0              -1                      0       169       `
  ��  d嘂层鶗   �  ?emitFunctionCall@GeomPropValueNodeGlslAsUniform@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z MaterialXGenGlsl.dll 
/0              -1                      0       161       `
  ��  d唓   �  ?emitFunctionCall@HeightToNormalNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z MaterialXGenGlsl.dll 
/0              -1                      0       160       `
  ��  d喆0`魧   �  ?emitFunctionCall@LightCompoundNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z MaterialXGenGlsl.dll /0              -1                      0       152       `
  ��  d啍Q答�   �  ?emitFunctionCall@LightNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z MaterialXGenGlsl.dll /0              -1                      0       158       `
  ��  d啢鹿鶌   �  ?emitFunctionCall@LightShaderNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z MaterialXGenGlsl.dll /0              -1                      0       154       `
  ��  d喯X擂�   �  ?emitFunctionCall@SurfaceNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z MaterialXGenGlsl.dll /0              -1                      0       159       `
  ��  d�(�鄫   �  ?emitFunctionCall@UnlitSurfaceNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z MaterialXGenGlsl.dll 
/0              -1                      0       167       `
  ��  d�陧霌   �  ?emitFunctionDefinition@HeightToNormalNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z MaterialXGenGlsl.dll 
/0              -1                      0       170       `
  ��  d啩�   �  ?emitFunctionDefinition@LightCompoundNodeGlsl@MaterialX_v1_39_2@@IEBAXPEAVClosureContext@2@AEAVGenContext@2@AEAVShaderStage@2@@Z MaterialXGenGlsl.dll /0              -1                      0       166       `
  ��  d唵麙   �  ?emitFunctionDefinition@LightCompoundNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z MaterialXGenGlsl.dll /0              -1                      0       165       `
  ��  d�
J�   �  ?emitFunctionDefinition@LightSamplerNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z MaterialXGenGlsl.dll 
/0              -1                      0       162       `
  ��  d啢j'鎺   �  ?emitFunctionDefinition@NumLightsNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z MaterialXGenGlsl.dll /0              -1                      0       135       `
  ��  d啲齯萻   �  ?emitInputs@EsslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z MaterialXGenGlsl.dll 
/0              -1                      0       135       `
  ��  d�g稼s   �  ?emitInputs@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z MaterialXGenGlsl.dll 
/0              -1                      0       133       `
  ��  d唚Z~莙   �  ?emitInputs@VkShaderGenerator@MaterialX_v1_39_2@@UEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z MaterialXGenGlsl.dll 
/0              -1                      0       138       `
  ��  d�吢蝪   �  ?emitLightData@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z MaterialXGenGlsl.dll /0              -1                      0       171       `
  ��  d嗛〕鈼   �  ?emitLightFunctionDefinitions@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEBVShaderGraph@2@AEAVGenContext@2@AEAVShaderStage@2@@Z MaterialXGenGlsl.dll 
/0              -1                      0       216       `
  ��  d啀秀赡   �  ?emitLightLoop@SurfaceNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z MaterialXGenGlsl.dll /0              -1                      0       136       `
  ��  d喛J櫺t   �  ?emitOutputs@EsslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z MaterialXGenGlsl.dll /0              -1                      0       136       `
  ��  d喿$`顃   �  ?emitOutputs@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z MaterialXGenGlsl.dll /0              -1                      0       134       `
  ��  d��輗   �  ?emitOutputs@VkShaderGenerator@MaterialX_v1_39_2@@UEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z MaterialXGenGlsl.dll /0              -1                      0       157       `
  ��  d哅~鶋   �  ?emitPixelStage@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEBVShaderGraph@2@AEAVGenContext@2@AEAVShaderStage@2@@Z MaterialXGenGlsl.dll 
/0              -1                      0       172       `
  ��  d喡\X蓸   �  ?emitResourceBindings@GlslResourceBindingContext@MaterialX_v1_39_2@@UEAAXAEAVGenContext@2@AEBVVariableBlock@2@AEAVShaderStage@2@@Z MaterialXGenGlsl.dll /0              -1                      0       170       `
  ��  d�.'t臇   �  ?emitResourceBindings@VkResourceBindingContext@MaterialX_v1_39_2@@UEAAXAEAVGenContext@2@AEBVVariableBlock@2@AEAVShaderStage@2@@Z MaterialXGenGlsl.dll /0              -1                      0       165       `
  ��  d喣捪驊   �  ?emitSamplingFunctionDefinition@BlurNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z MaterialXGenGlsl.dll 
/0              -1                      0       148       `
  ��  d�1R   �  ?emitSpecularEnvironment@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z MaterialXGenGlsl.dll /0              -1                      0       248       `
  ��  d唓�
怃   �  ?emitStructuredResourceBindings@GlslResourceBindingContext@MaterialX_v1_39_2@@UEAAXAEAVGenContext@2@AEBVVariableBlock@2@AEAVShaderStage@2@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@3@Z MaterialXGenGlsl.dll /0              -1                      0       246       `
  ��  d嗊|耷�   �  ?emitStructuredResourceBindings@VkResourceBindingContext@MaterialX_v1_39_2@@UEAAXAEAVGenContext@2@AEBVVariableBlock@2@AEAVShaderStage@2@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@3@Z MaterialXGenGlsl.dll /0              -1                      0       147       `
  ��  d唫��   �  ?emitTransmissionRender@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z MaterialXGenGlsl.dll 
/0              -1                      0       137       `
  ��  d哸J犂u   �  ?emitUniforms@EsslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z MaterialXGenGlsl.dll 
/0              -1                      0       137       `
  ��  d�
評   �  ?emitUniforms@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z MaterialXGenGlsl.dll 
/0              -1                      0       232       `
  ��  d�Kp碓   �  ?emitVariableDeclaration@GlslShaderGenerator@MaterialX_v1_39_2@@UEBAXPEBVShaderPort@2@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAVGenContext@2@AEAVShaderStage@2@_N@Z MaterialXGenGlsl.dll /0              -1                      0       158       `
  ��  d�(缝釆   �  ?emitVertexStage@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEBVShaderGraph@2@AEAVGenContext@2@AEAVShaderStage@2@@Z MaterialXGenGlsl.dll /0              -1                      0       129       `
  ��  d唎餭飉   �  ?enableSeparateBindingLocations@GlslResourceBindingContext@MaterialX_v1_39_2@@QEAAX_N@Z MaterialXGenGlsl.dll 
/0              -1                      0       270       `
  ��  d喼蠮潸   �  ?generate@GlslShaderGenerator@MaterialX_v1_39_2@@UEBA?AV?$shared_ptr@VShader@MaterialX_v1_39_2@@@std@@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@4@V?$shared_ptr@VElement@MaterialX_v1_39_2@@@4@AEAVGenContext@2@@Z MaterialXGenGlsl.dll /0              -1                      0       165       `
  ��  d�7誜螒   �  ?getConstantQualifier@GlslSyntax@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ MaterialXGenGlsl.dll 
/0              -1                      0       194       `
  ��  d�3$H��   �  ?getImplementation@GlslShaderGenerator@MaterialX_v1_39_2@@UEBA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@AEBVNodeDef@2@AEAVGenContext@2@@Z MaterialXGenGlsl.dll /0              -1                      0       162       `
  ��  d哹�   �  ?getInputQualifier@GlslSyntax@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ MaterialXGenGlsl.dll /0              -1                      0       160       `
  ��  d�"VA詫   �  ?getInputQualifier@VkSyntax@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ MaterialXGenGlsl.dll /0              -1                      0       163       `
  ��  d啴痐葟   �  ?getOutputQualifier@GlslSyntax@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ MaterialXGenGlsl.dll 
/0              -1                      0       198       `
  ��  d啺�貌   �  ?getResourceBindingContext@EsslShaderGenerator@MaterialX_v1_39_2@@MEBA?AV?$shared_ptr@VHwResourceBindingContext@MaterialX_v1_39_2@@@std@@AEAVGenContext@2@@Z MaterialXGenGlsl.dll /0              -1                      0       198       `
  ��  d嗋a猃�   �  ?getResourceBindingContext@GlslShaderGenerator@MaterialX_v1_39_2@@MEBA?AV?$shared_ptr@VHwResourceBindingContext@MaterialX_v1_39_2@@@std@@AEAVGenContext@2@@Z MaterialXGenGlsl.dll /0              -1                      0       196       `
  ��  d�"辭砂   �  ?getResourceBindingContext@VkShaderGenerator@MaterialX_v1_39_2@@MEBA?AV?$shared_ptr@VHwResourceBindingContext@MaterialX_v1_39_2@@@std@@AEAVGenContext@2@@Z MaterialXGenGlsl.dll /0              -1                      0       167       `
  ��  d�^[膿   �  ?getSourceFileExtension@GlslSyntax@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ MaterialXGenGlsl.dll 
/0              -1                      0       163       `
  ��  d啿}x鈴   �  ?getTarget@EsslShaderGenerator@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ MaterialXGenGlsl.dll 
/0              -1                      0       162       `
  ��  d唭�&韼   �  ?getTarget@GlslImplementation@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ MaterialXGenGlsl.dll /0              -1                      0       163       `
  ��  d嗚��   �  ?getTarget@GlslShaderGenerator@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ MaterialXGenGlsl.dll 
/0              -1                      0       166       `
  ��  d喭M螔   �  ?getTarget@HeightToNormalNodeGlsl@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ MaterialXGenGlsl.dll /0              -1                      0       165       `
  ��  d�3罤魬   �  ?getTarget@LightCompoundNodeGlsl@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ MaterialXGenGlsl.dll 
/0              -1                      0       163       `
  ��  d嘃^黜�   �  ?getTarget@LightShaderNodeGlsl@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ MaterialXGenGlsl.dll 
/0              -1                      0       161       `
  ��  d啞L梭�   �  ?getTarget@VkShaderGenerator@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ MaterialXGenGlsl.dll 
/0              -1                      0       164       `
  ��  d�膼   �  ?getUniformQualifier@GlslSyntax@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ MaterialXGenGlsl.dll /0              -1                      0       176       `
  ��  d嘄块脺   �  ?getValue@GlslStructTypeSyntax@MaterialX_v1_39_2@@UEBA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEBVValue@2@_N@Z MaterialXGenGlsl.dll /0              -1                      0       164       `
  ��  d�笪鶒   �  ?getVersion@EsslShaderGenerator@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ MaterialXGenGlsl.dll /0              -1                      0       164       `
  ��  d喿X^蹛   �  ?getVersion@GlslShaderGenerator@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ MaterialXGenGlsl.dll /0              -1                      0       162       `
  ��  d哊�艓   �  ?getVersion@VkShaderGenerator@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ MaterialXGenGlsl.dll /0              -1                      0       192       `
  ��  d啚6f默   �  ?getVertexDataPrefix@EsslShaderGenerator@MaterialX_v1_39_2@@UEBA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEBVVariableBlock@2@@Z MaterialXGenGlsl.dll /0              -1                      0       192       `
  ��  d喐`嵰�     ?getVertexDataPrefix@GlslShaderGenerator@MaterialX_v1_39_2@@UEBA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEBVVariableBlock@2@@Z MaterialXGenGlsl.dll /0              -1                      0       190       `
  ��  d嗰7i戟    ?getVertexDataPrefix@VkShaderGenerator@MaterialX_v1_39_2@@UEBA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEBVVariableBlock@2@@Z MaterialXGenGlsl.dll /0              -1                      0       107       `
  ��  d嗚丽颳    ?initialize@GlslResourceBindingContext@MaterialX_v1_39_2@@UEAAXXZ MaterialXGenGlsl.dll 
/0              -1                      0       142       `
  ��  d��飠    ?initialize@LightCompoundNodeGlsl@MaterialX_v1_39_2@@UEAAXAEBVInterfaceElement@2@AEAVGenContext@2@@Z MaterialXGenGlsl.dll /0              -1                      0       140       `
  ��  d�齺魓    ?initialize@LightShaderNodeGlsl@MaterialX_v1_39_2@@UEAAXAEBVInterfaceElement@2@AEAVGenContext@2@@Z MaterialXGenGlsl.dll /0              -1                      0       105       `
  ��  d問餟    ?initialize@VkResourceBindingContext@MaterialX_v1_39_2@@UEAAXXZ MaterialXGenGlsl.dll 
/0              -1                      0       121       `
  ��  d唅奉鰁    ?isEditable@GeomPropValueNodeGlsl@MaterialX_v1_39_2@@UEBA_NAEBVShaderInput@2@@Z MaterialXGenGlsl.dll 
/0              -1                      0       265       `
  ��  d唺髐王    ?remapEnumeration@GlslSyntax@MaterialX_v1_39_2@@UEBA_NAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@VTypeDesc@2@0AEAU?$pair@VTypeDesc@MaterialX_v1_39_2@@V?$shared_ptr@VValue@MaterialX_v1_39_2@@@std@@@4@@Z MaterialXGenGlsl.dll 
/0              -1                      0       125       `
  ��  d喪蒳    ?requiresLighting@GlslShaderGenerator@MaterialX_v1_39_2@@MEBA_NAEBVShaderGraph@2@@Z MaterialXGenGlsl.dll 
/0              -1                      0       174       `
  ��  d�>涏魵   	 ?toVec4@GlslShaderGenerator@MaterialX_v1_39_2@@KAXPEBVTypeDesc@2@AEAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z MaterialXGenGlsl.dll /0              -1                      0       171       `
  ��  d嗆h`蓷   
 ?toVec4@GlslShaderGenerator@MaterialX_v1_39_2@@KAXVTypeDesc@2@AEAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z MaterialXGenGlsl.dll 
/0              -1                      0       110       `
  ��  d�/⊙鈀    ?typeSupported@GlslSyntax@MaterialX_v1_39_2@@UEBA_NPEBVTypeDesc@2@@Z MaterialXGenGlsl.dll 