!<arch>
/               -1                      0       47123     `
  � qP s� t� � � )J )J 0h 0h :� :� ば ば 呮 呮 匘 匘 乼 乼   偖 偖 � � 碕 碕 尝 尝 yb yb � � '� '� /� /� 2* 2* �8 �8 v v 
n 
n � � #� #� 爾 爾 x� x� x  x  骅 骅  �  � "� "�   -� -� &� &� � � 妶 妶 夘 夘 窣 窣 窢 窢   嚹 嚹 �  �  而 而 禢 禢 坒 坒 彳 彳 �0 �0 z� z� y� y� � � 産 産 � 曝 詐 踗 面 �: 娶 膢 {. {. 劆 劆 �( �( 5" 5" � � � � : :   �0 �0 凐 凐 僋 僋 禒 禒 答 答 莕 唵 唵 � �   � � 3( 3( , , � �   鵳 鵳 > > � � 4 4 L L $� $� :
 :
 h h � � 
� 
� ;� ;� (j (j |� |� ± ± |^ |^ {� {� 癋 癋 癄 癄 8
 8
 ~� ~�   ~8 ~8 }� }� 睌 睌 瓣 瓣 �� �� Ⅳ Ⅳ �$ �$ v v 拆 拆 �< �< �, �, 梤 梤 栐 栐 欔 欔   橦 橦 槫 槫 � � 縫 縫   � � 弜 弜 �0 �0 �: �: 崠 崠 恨 恨 �4 �4   撃 撃 �" �" 絸 絸 钾 钾 岤 岤   孷 孷 嫲 嫲 箠 箠 皋 皋 ╜ ╜ 戞 戞 態 態 �0 �0 粏 粏   暈 暈 旫 旫 咎 咎 �& �& w� w� � � v� v� vX vX   �  �  誋 �> �
 艦 �6 蚅 娃 竖 蔮 �" 扇 藠 谈 肰 �  �  <� <� 8� 8� 鹌 鹌 .� .� 砚 液 嘱 �2 � 訏 仔 � �4 �8 �8 � � 鉅 鉅 皲 皲 �2 �2 鉹 鉹 蚋 蚋 � � 銱 銱 髳 髳 �� �� 绗 绗 魊 魊 � �  �  � *, *, 蒽 蒽 蓼 蓼 庈 庈 鑴 鑴 + + 5� 5� 鮈 鮈 � � � � � � 惍 惍 閆 閆 �$ �$ � � 拡 拡 �( �( � � 攄 攄 掰 掰 +� +� 6� 6� 鲷 鲷 f f �< �< 胍 胍 魇 魇 	� 	� � � 欷 欷 鵀 鵀 B B p p 韙 韙   浺 浺 頙 頙 7b 7b 鼫 鼫 � � p p 玕 玕 �, �, 殑 殑 羛 羛 缆 缆 蝪 澃 澃 �0 �0 ,� ,� 1J 1J 4& 4& 齤 齤 � �   %� %�   � � 渞 渞 � � !� !� 瑨 瑨 炧 炧 濰 濰 潞 潞 � � �  � � 夕 焩 焩__IMPORT_DESCRIPTOR_MaterialXGenGlsl_d __NULL_IMPORT_DESCRIPTOR MaterialXGenGlsl_d_NULL_THUNK_DATA ?create@GlslShaderGenerator@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderGenerator@MaterialX_v1_39_2@@@std@@XZ __imp_?create@GlslShaderGenerator@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderGenerator@MaterialX_v1_39_2@@@std@@XZ ?getTarget@GlslShaderGenerator@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ __imp_?getTarget@GlslShaderGenerator@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ ?getVersion@GlslShaderGenerator@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ __imp_?getVersion@GlslShaderGenerator@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ ?toVec4@GlslShaderGenerator@MaterialX_v1_39_2@@KAXPEBVTypeDesc@2@AEAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z __imp_?toVec4@GlslShaderGenerator@MaterialX_v1_39_2@@KAXPEBVTypeDesc@2@AEAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z ??1GlslShaderGenerator@MaterialX_v1_39_2@@UEAA@XZ __imp_??1GlslShaderGenerator@MaterialX_v1_39_2@@UEAA@XZ ??0GlslShaderGenerator@MaterialX_v1_39_2@@QEAA@AEBV01@@Z __imp_??0GlslShaderGenerator@MaterialX_v1_39_2@@QEAA@AEBV01@@Z ??0GlslShaderGenerator@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z __imp_??0GlslShaderGenerator@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z ??0GlslImplementation@MaterialX_v1_39_2@@IEAA@XZ __imp_??0GlslImplementation@MaterialX_v1_39_2@@IEAA@XZ ??1GlslImplementation@MaterialX_v1_39_2@@UEAA@XZ __imp_??1GlslImplementation@MaterialX_v1_39_2@@UEAA@XZ ??0GlslImplementation@MaterialX_v1_39_2@@QEAA@AEBV01@@Z __imp_??0GlslImplementation@MaterialX_v1_39_2@@QEAA@AEBV01@@Z ??0GlslImplementation@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z __imp_??0GlslImplementation@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z ??4GlslImplementation@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z __imp_??4GlslImplementation@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z ??4GlslImplementation@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z __imp_??4GlslImplementation@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z ??0EsslShaderGenerator@MaterialX_v1_39_2@@QEAA@XZ __imp_??0EsslShaderGenerator@MaterialX_v1_39_2@@QEAA@XZ ?create@EsslShaderGenerator@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderGenerator@MaterialX_v1_39_2@@@std@@XZ __imp_?create@EsslShaderGenerator@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderGenerator@MaterialX_v1_39_2@@@std@@XZ ?getTarget@EsslShaderGenerator@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ __imp_?getTarget@EsslShaderGenerator@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ ?getVersion@EsslShaderGenerator@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ __imp_?getVersion@EsslShaderGenerator@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ ?getVertexDataPrefix@EsslShaderGenerator@MaterialX_v1_39_2@@UEBA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEBVVariableBlock@2@@Z __imp_?getVertexDataPrefix@EsslShaderGenerator@MaterialX_v1_39_2@@UEBA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEBVVariableBlock@2@@Z ?emitDirectives@EsslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitDirectives@EsslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z ?emitUniforms@EsslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitUniforms@EsslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z ?emitInputs@EsslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitInputs@EsslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z ?emitOutputs@EsslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitOutputs@EsslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z ?getResourceBindingContext@EsslShaderGenerator@MaterialX_v1_39_2@@MEBA?AV?$shared_ptr@VHwResourceBindingContext@MaterialX_v1_39_2@@@std@@AEAVGenContext@2@@Z __imp_?getResourceBindingContext@EsslShaderGenerator@MaterialX_v1_39_2@@MEBA?AV?$shared_ptr@VHwResourceBindingContext@MaterialX_v1_39_2@@@std@@AEAVGenContext@2@@Z ??1EsslShaderGenerator@MaterialX_v1_39_2@@UEAA@XZ __imp_??1EsslShaderGenerator@MaterialX_v1_39_2@@UEAA@XZ ??0EsslShaderGenerator@MaterialX_v1_39_2@@QEAA@AEBV01@@Z __imp_??0EsslShaderGenerator@MaterialX_v1_39_2@@QEAA@AEBV01@@Z ??0EsslShaderGenerator@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z __imp_??0EsslShaderGenerator@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z ?create@GlslSyntax@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VSyntax@MaterialX_v1_39_2@@@std@@XZ __imp_?create@GlslSyntax@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VSyntax@MaterialX_v1_39_2@@@std@@XZ ?getInputQualifier@GlslSyntax@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ __imp_?getInputQualifier@GlslSyntax@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ ?getOutputQualifier@GlslSyntax@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ __imp_?getOutputQualifier@GlslSyntax@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ ?getConstantQualifier@GlslSyntax@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ __imp_?getConstantQualifier@GlslSyntax@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ ?getUniformQualifier@GlslSyntax@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ __imp_?getUniformQualifier@GlslSyntax@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ ?getSourceFileExtension@GlslSyntax@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ __imp_?getSourceFileExtension@GlslSyntax@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ ??1GlslSyntax@MaterialX_v1_39_2@@UEAA@XZ __imp_??1GlslSyntax@MaterialX_v1_39_2@@UEAA@XZ ??0GlslSyntax@MaterialX_v1_39_2@@QEAA@AEBV01@@Z __imp_??0GlslSyntax@MaterialX_v1_39_2@@QEAA@AEBV01@@Z ??0GlslSyntax@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z __imp_??0GlslSyntax@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z ??4GlslSyntax@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z __imp_??4GlslSyntax@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z ??4GlslSyntax@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z __imp_??4GlslSyntax@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z ??1GlslStructTypeSyntax@MaterialX_v1_39_2@@UEAA@XZ __imp_??1GlslStructTypeSyntax@MaterialX_v1_39_2@@UEAA@XZ ??0GlslStructTypeSyntax@MaterialX_v1_39_2@@QEAA@AEBV01@@Z __imp_??0GlslStructTypeSyntax@MaterialX_v1_39_2@@QEAA@AEBV01@@Z ??0GlslStructTypeSyntax@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z __imp_??0GlslStructTypeSyntax@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z ??4GlslStructTypeSyntax@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z __imp_??4GlslStructTypeSyntax@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z ??4GlslStructTypeSyntax@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z __imp_??4GlslStructTypeSyntax@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z ??0GlslStructTypeSyntax@MaterialX_v1_39_2@@QEAA@PEBVSyntax@1@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@1111AEBV?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@4@@Z __imp_??0GlslStructTypeSyntax@MaterialX_v1_39_2@@QEAA@PEBVSyntax@1@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@1111AEBV?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@4@@Z ?create@EsslSyntax@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VSyntax@MaterialX_v1_39_2@@@std@@XZ __imp_?create@EsslSyntax@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VSyntax@MaterialX_v1_39_2@@@std@@XZ ??1EsslSyntax@MaterialX_v1_39_2@@UEAA@XZ __imp_??1EsslSyntax@MaterialX_v1_39_2@@UEAA@XZ ??0EsslSyntax@MaterialX_v1_39_2@@QEAA@AEBV01@@Z __imp_??0EsslSyntax@MaterialX_v1_39_2@@QEAA@AEBV01@@Z ??0EsslSyntax@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z __imp_??0EsslSyntax@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z ??4EsslSyntax@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z __imp_??4EsslSyntax@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z ??4EsslSyntax@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z __imp_??4EsslSyntax@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z __imp_??_7GlslShaderGenerator@MaterialX_v1_39_2@@6B@ __imp_??_7GlslImplementation@MaterialX_v1_39_2@@6B@ __imp_?TARGET@EsslShaderGenerator@MaterialX_v1_39_2@@2V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@B __imp_?VERSION@EsslShaderGenerator@MaterialX_v1_39_2@@2V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@B __imp_??_7EsslShaderGenerator@MaterialX_v1_39_2@@6B@ __imp_??_7GlslSyntax@MaterialX_v1_39_2@@6B@ __imp_??_7GlslStructTypeSyntax@MaterialX_v1_39_2@@6B@ __imp_??_7EsslSyntax@MaterialX_v1_39_2@@6B@ ??0EsslSyntax@MaterialX_v1_39_2@@QEAA@XZ __imp_??0EsslSyntax@MaterialX_v1_39_2@@QEAA@XZ ??0GlslResourceBindingContext@MaterialX_v1_39_2@@QEAA@_K0@Z __imp_??0GlslResourceBindingContext@MaterialX_v1_39_2@@QEAA@_K0@Z ?create@GlslResourceBindingContext@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VGlslResourceBindingContext@MaterialX_v1_39_2@@@std@@_K0@Z __imp_?create@GlslResourceBindingContext@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VGlslResourceBindingContext@MaterialX_v1_39_2@@@std@@_K0@Z ?initialize@GlslResourceBindingContext@MaterialX_v1_39_2@@UEAAXXZ __imp_?initialize@GlslResourceBindingContext@MaterialX_v1_39_2@@UEAAXXZ ?emitDirectives@GlslResourceBindingContext@MaterialX_v1_39_2@@UEAAXAEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitDirectives@GlslResourceBindingContext@MaterialX_v1_39_2@@UEAAXAEAVGenContext@2@AEAVShaderStage@2@@Z ?emitResourceBindings@GlslResourceBindingContext@MaterialX_v1_39_2@@UEAAXAEAVGenContext@2@AEBVVariableBlock@2@AEAVShaderStage@2@@Z __imp_?emitResourceBindings@GlslResourceBindingContext@MaterialX_v1_39_2@@UEAAXAEAVGenContext@2@AEBVVariableBlock@2@AEAVShaderStage@2@@Z ?emitStructuredResourceBindings@GlslResourceBindingContext@MaterialX_v1_39_2@@UEAAXAEAVGenContext@2@AEBVVariableBlock@2@AEAVShaderStage@2@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@3@Z __imp_?emitStructuredResourceBindings@GlslResourceBindingContext@MaterialX_v1_39_2@@UEAAXAEAVGenContext@2@AEBVVariableBlock@2@AEAVShaderStage@2@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@3@Z ?enableSeparateBindingLocations@GlslResourceBindingContext@MaterialX_v1_39_2@@QEAAX_N@Z __imp_?enableSeparateBindingLocations@GlslResourceBindingContext@MaterialX_v1_39_2@@QEAAX_N@Z ??1GlslResourceBindingContext@MaterialX_v1_39_2@@UEAA@XZ __imp_??1GlslResourceBindingContext@MaterialX_v1_39_2@@UEAA@XZ ??0GlslResourceBindingContext@MaterialX_v1_39_2@@QEAA@AEBV01@@Z __imp_??0GlslResourceBindingContext@MaterialX_v1_39_2@@QEAA@AEBV01@@Z ??0GlslResourceBindingContext@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z __imp_??0GlslResourceBindingContext@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z ??4GlslResourceBindingContext@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z __imp_??4GlslResourceBindingContext@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z ??4GlslResourceBindingContext@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z __imp_??4GlslResourceBindingContext@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z __imp_??_7GlslResourceBindingContext@MaterialX_v1_39_2@@6B@ ??0GlslShaderGenerator@MaterialX_v1_39_2@@QEAA@XZ __imp_??0GlslShaderGenerator@MaterialX_v1_39_2@@QEAA@XZ ?generate@GlslShaderGenerator@MaterialX_v1_39_2@@UEBA?AV?$shared_ptr@VShader@MaterialX_v1_39_2@@@std@@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@4@V?$shared_ptr@VElement@MaterialX_v1_39_2@@@4@AEAVGenContext@2@@Z __imp_?generate@GlslShaderGenerator@MaterialX_v1_39_2@@UEBA?AV?$shared_ptr@VShader@MaterialX_v1_39_2@@@std@@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@4@V?$shared_ptr@VElement@MaterialX_v1_39_2@@@4@AEAVGenContext@2@@Z ?emitVariableDeclaration@GlslShaderGenerator@MaterialX_v1_39_2@@UEBAXPEBVShaderPort@2@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAVGenContext@2@AEAVShaderStage@2@_N@Z __imp_?emitVariableDeclaration@GlslShaderGenerator@MaterialX_v1_39_2@@UEBAXPEBVShaderPort@2@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAVGenContext@2@AEAVShaderStage@2@_N@Z ?getImplementation@GlslShaderGenerator@MaterialX_v1_39_2@@UEBA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@AEBVNodeDef@2@AEAVGenContext@2@@Z __imp_?getImplementation@GlslShaderGenerator@MaterialX_v1_39_2@@UEBA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@AEBVNodeDef@2@AEAVGenContext@2@@Z ?getVertexDataPrefix@GlslShaderGenerator@MaterialX_v1_39_2@@UEBA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEBVVariableBlock@2@@Z __imp_?getVertexDataPrefix@GlslShaderGenerator@MaterialX_v1_39_2@@UEBA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEBVVariableBlock@2@@Z ?emitVertexStage@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEBVShaderGraph@2@AEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitVertexStage@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEBVShaderGraph@2@AEAVGenContext@2@AEAVShaderStage@2@@Z ?emitPixelStage@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEBVShaderGraph@2@AEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitPixelStage@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEBVShaderGraph@2@AEAVGenContext@2@AEAVShaderStage@2@@Z ?emitDirectives@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitDirectives@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z ?emitConstants@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitConstants@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z ?emitUniforms@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitUniforms@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z ?emitLightData@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitLightData@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z ?emitInputs@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitInputs@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z ?emitOutputs@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitOutputs@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z ?getResourceBindingContext@GlslShaderGenerator@MaterialX_v1_39_2@@MEBA?AV?$shared_ptr@VHwResourceBindingContext@MaterialX_v1_39_2@@@std@@AEAVGenContext@2@@Z __imp_?getResourceBindingContext@GlslShaderGenerator@MaterialX_v1_39_2@@MEBA?AV?$shared_ptr@VHwResourceBindingContext@MaterialX_v1_39_2@@@std@@AEAVGenContext@2@@Z ?requiresLighting@GlslShaderGenerator@MaterialX_v1_39_2@@MEBA_NAEBVShaderGraph@2@@Z __imp_?requiresLighting@GlslShaderGenerator@MaterialX_v1_39_2@@MEBA_NAEBVShaderGraph@2@@Z ?emitSpecularEnvironment@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitSpecularEnvironment@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z ?emitTransmissionRender@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitTransmissionRender@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z ?emitLightFunctionDefinitions@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEBVShaderGraph@2@AEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitLightFunctionDefinitions@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEBVShaderGraph@2@AEAVGenContext@2@AEAVShaderStage@2@@Z ?toVec4@GlslShaderGenerator@MaterialX_v1_39_2@@KAXVTypeDesc@2@AEAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z __imp_?toVec4@GlslShaderGenerator@MaterialX_v1_39_2@@KAXVTypeDesc@2@AEAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z ?getTarget@GlslImplementation@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ __imp_?getTarget@GlslImplementation@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ ??0GeomColorNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ __imp_??0GeomColorNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ ??1GeomColorNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ __imp_??1GeomColorNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ ??0GeomColorNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z __imp_??0GeomColorNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z ??0GeomColorNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z __imp_??0GeomColorNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z ??4GeomColorNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z __imp_??4GeomColorNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z ??4GeomColorNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z __imp_??4GeomColorNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z ?isEditable@GeomPropValueNodeGlsl@MaterialX_v1_39_2@@UEBA_NAEBVShaderInput@2@@Z __imp_?isEditable@GeomPropValueNodeGlsl@MaterialX_v1_39_2@@UEBA_NAEBVShaderInput@2@@Z ??0GeomPropValueNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ __imp_??0GeomPropValueNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ ??1GeomPropValueNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ __imp_??1GeomPropValueNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ ??0GeomPropValueNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z __imp_??0GeomPropValueNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z ??0GeomPropValueNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z __imp_??0GeomPropValueNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z ??4GeomPropValueNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z __imp_??4GeomPropValueNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z ??4GeomPropValueNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z __imp_??4GeomPropValueNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z ??0GeomPropValueNodeGlslAsUniform@MaterialX_v1_39_2@@QEAA@XZ __imp_??0GeomPropValueNodeGlslAsUniform@MaterialX_v1_39_2@@QEAA@XZ ??1GeomPropValueNodeGlslAsUniform@MaterialX_v1_39_2@@UEAA@XZ __imp_??1GeomPropValueNodeGlslAsUniform@MaterialX_v1_39_2@@UEAA@XZ ??0GeomPropValueNodeGlslAsUniform@MaterialX_v1_39_2@@QEAA@AEBV01@@Z __imp_??0GeomPropValueNodeGlslAsUniform@MaterialX_v1_39_2@@QEAA@AEBV01@@Z ??0GeomPropValueNodeGlslAsUniform@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z __imp_??0GeomPropValueNodeGlslAsUniform@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z ??4GeomPropValueNodeGlslAsUniform@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z __imp_??4GeomPropValueNodeGlslAsUniform@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z ??4GeomPropValueNodeGlslAsUniform@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z __imp_??4GeomPropValueNodeGlslAsUniform@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z ??1SurfaceNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ __imp_??1SurfaceNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ ??0SurfaceNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z __imp_??0SurfaceNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z ??0SurfaceNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z __imp_??0SurfaceNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z ??0UnlitSurfaceNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ __imp_??0UnlitSurfaceNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ ??1UnlitSurfaceNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ __imp_??1UnlitSurfaceNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ ??0UnlitSurfaceNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z __imp_??0UnlitSurfaceNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z ??0UnlitSurfaceNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z __imp_??0UnlitSurfaceNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z ??4UnlitSurfaceNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z __imp_??4UnlitSurfaceNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z ??4UnlitSurfaceNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z __imp_??4UnlitSurfaceNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z ??1LightNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ __imp_??1LightNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ ??0LightNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z __imp_??0LightNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z ??0LightNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z __imp_??0LightNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z ??1LightCompoundNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ __imp_??1LightCompoundNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ ??0LightCompoundNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z __imp_??0LightCompoundNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z ??0LightCompoundNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z __imp_??0LightCompoundNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z ??4LightCompoundNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z __imp_??4LightCompoundNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z ??4LightCompoundNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z __imp_??4LightCompoundNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z ??1LightShaderNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ __imp_??1LightShaderNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ ??0LightShaderNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z __imp_??0LightShaderNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z ??0LightShaderNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z __imp_??0LightShaderNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z ??4LightShaderNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z __imp_??4LightShaderNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z ??4LightShaderNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z __imp_??4LightShaderNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z ??0HeightToNormalNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ __imp_??0HeightToNormalNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ ??1HeightToNormalNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ __imp_??1HeightToNormalNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ ??0HeightToNormalNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z __imp_??0HeightToNormalNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z ??0HeightToNormalNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z __imp_??0HeightToNormalNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z ??4HeightToNormalNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z __imp_??4HeightToNormalNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z ??4HeightToNormalNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z __imp_??4HeightToNormalNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z ??1LightSamplerNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ __imp_??1LightSamplerNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ ??0LightSamplerNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z __imp_??0LightSamplerNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z ??0LightSamplerNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z __imp_??0LightSamplerNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z ??4LightSamplerNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z __imp_??4LightSamplerNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z ??4LightSamplerNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z __imp_??4LightSamplerNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z ??1NumLightsNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ __imp_??1NumLightsNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ ??0NumLightsNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z __imp_??0NumLightsNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z ??0NumLightsNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z __imp_??0NumLightsNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z ??4NumLightsNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z __imp_??4NumLightsNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z ??4NumLightsNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z __imp_??4NumLightsNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z ??0BlurNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ __imp_??0BlurNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ ??1BlurNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ __imp_??1BlurNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ ??0BlurNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z __imp_??0BlurNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z ??0BlurNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z __imp_??0BlurNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z ??4BlurNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z __imp_??4BlurNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z ??4BlurNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z __imp_??4BlurNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z __imp_?TARGET@GlslShaderGenerator@MaterialX_v1_39_2@@2V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@B __imp_?VERSION@GlslShaderGenerator@MaterialX_v1_39_2@@2V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@B __imp_??_7GeomColorNodeGlsl@MaterialX_v1_39_2@@6B@ __imp_??_7GeomPropValueNodeGlsl@MaterialX_v1_39_2@@6B@ __imp_??_7GeomPropValueNodeGlslAsUniform@MaterialX_v1_39_2@@6B@ __imp_??_7SurfaceNodeGlsl@MaterialX_v1_39_2@@6B@ __imp_??_7UnlitSurfaceNodeGlsl@MaterialX_v1_39_2@@6B@ __imp_??_7LightNodeGlsl@MaterialX_v1_39_2@@6B@ __imp_??_7LightCompoundNodeGlsl@MaterialX_v1_39_2@@6B@ __imp_??_7LightShaderNodeGlsl@MaterialX_v1_39_2@@6B@ __imp_??_7HeightToNormalNodeGlsl@MaterialX_v1_39_2@@6B@ __imp_??_7LightSamplerNodeGlsl@MaterialX_v1_39_2@@6B@ __imp_??_7NumLightsNodeGlsl@MaterialX_v1_39_2@@6B@ __imp_??_7BlurNodeGlsl@MaterialX_v1_39_2@@6B@ ??0GlslSyntax@MaterialX_v1_39_2@@QEAA@XZ __imp_??0GlslSyntax@MaterialX_v1_39_2@@QEAA@XZ ?typeSupported@GlslSyntax@MaterialX_v1_39_2@@UEBA_NPEBVTypeDesc@2@@Z __imp_?typeSupported@GlslSyntax@MaterialX_v1_39_2@@UEBA_NPEBVTypeDesc@2@@Z ?remapEnumeration@GlslSyntax@MaterialX_v1_39_2@@UEBA_NAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@VTypeDesc@2@0AEAU?$pair@VTypeDesc@MaterialX_v1_39_2@@V?$shared_ptr@VValue@MaterialX_v1_39_2@@@std@@@4@@Z __imp_?remapEnumeration@GlslSyntax@MaterialX_v1_39_2@@UEBA_NAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@VTypeDesc@2@0AEAU?$pair@VTypeDesc@MaterialX_v1_39_2@@V?$shared_ptr@VValue@MaterialX_v1_39_2@@@std@@@4@@Z ?createStructSyntax@GlslSyntax@MaterialX_v1_39_2@@MEBA?AV?$shared_ptr@VStructTypeSyntax@MaterialX_v1_39_2@@@std@@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@4@0000@Z __imp_?createStructSyntax@GlslSyntax@MaterialX_v1_39_2@@MEBA?AV?$shared_ptr@VStructTypeSyntax@MaterialX_v1_39_2@@@std@@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@4@0000@Z ?getValue@GlslStructTypeSyntax@MaterialX_v1_39_2@@UEBA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEBVValue@2@_N@Z __imp_?getValue@GlslStructTypeSyntax@MaterialX_v1_39_2@@UEBA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEBVValue@2@_N@Z __imp_?INPUT_QUALIFIER@GlslSyntax@MaterialX_v1_39_2@@2V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@B __imp_?OUTPUT_QUALIFIER@GlslSyntax@MaterialX_v1_39_2@@2V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@B __imp_?UNIFORM_QUALIFIER@GlslSyntax@MaterialX_v1_39_2@@2V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@B __imp_?CONSTANT_QUALIFIER@GlslSyntax@MaterialX_v1_39_2@@2V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@B __imp_?FLAT_QUALIFIER@GlslSyntax@MaterialX_v1_39_2@@2V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@B __imp_?SOURCE_FILE_EXTENSION@GlslSyntax@MaterialX_v1_39_2@@2V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@B __imp_?VEC2_MEMBERS@GlslSyntax@MaterialX_v1_39_2@@2V?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@B __imp_?VEC3_MEMBERS@GlslSyntax@MaterialX_v1_39_2@@2V?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@B __imp_?VEC4_MEMBERS@GlslSyntax@MaterialX_v1_39_2@@2V?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@B ?create@BlurNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ __imp_?create@BlurNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ ?emitSamplingFunctionDefinition@BlurNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitSamplingFunctionDefinition@BlurNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z ?create@GeomColorNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ __imp_?create@GeomColorNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ ?createVariables@GeomColorNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShader@2@@Z __imp_?createVariables@GeomColorNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShader@2@@Z ?emitFunctionCall@GeomColorNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitFunctionCall@GeomColorNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z ?create@GeomPropValueNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ __imp_?create@GeomPropValueNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ ?createVariables@GeomPropValueNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShader@2@@Z __imp_?createVariables@GeomPropValueNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShader@2@@Z ?emitFunctionCall@GeomPropValueNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitFunctionCall@GeomPropValueNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z ?create@GeomPropValueNodeGlslAsUniform@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ __imp_?create@GeomPropValueNodeGlslAsUniform@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ ?createVariables@GeomPropValueNodeGlslAsUniform@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShader@2@@Z __imp_?createVariables@GeomPropValueNodeGlslAsUniform@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShader@2@@Z ?emitFunctionCall@GeomPropValueNodeGlslAsUniform@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitFunctionCall@GeomPropValueNodeGlslAsUniform@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z ?create@HeightToNormalNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ __imp_?create@HeightToNormalNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ ?createVariables@HeightToNormalNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShader@2@@Z __imp_?createVariables@HeightToNormalNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShader@2@@Z ?emitFunctionDefinition@HeightToNormalNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitFunctionDefinition@HeightToNormalNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z ?emitFunctionCall@HeightToNormalNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitFunctionCall@HeightToNormalNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z ?getTarget@HeightToNormalNodeGlsl@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ __imp_?getTarget@HeightToNormalNodeGlsl@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ ?acceptsInputType@HeightToNormalNodeGlsl@MaterialX_v1_39_2@@MEBA_NVTypeDesc@2@@Z __imp_?acceptsInputType@HeightToNormalNodeGlsl@MaterialX_v1_39_2@@MEBA_NVTypeDesc@2@@Z ?computeSampleOffsetStrings@HeightToNormalNodeGlsl@MaterialX_v1_39_2@@MEBAXAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@0IAEAV?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@4@@Z __imp_?computeSampleOffsetStrings@HeightToNormalNodeGlsl@MaterialX_v1_39_2@@MEBAXAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@0IAEAV?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@4@@Z ??0LightCompoundNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ __imp_??0LightCompoundNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ ?create@LightCompoundNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ __imp_?create@LightCompoundNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ ?getTarget@LightCompoundNodeGlsl@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ __imp_?getTarget@LightCompoundNodeGlsl@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ ?initialize@LightCompoundNodeGlsl@MaterialX_v1_39_2@@UEAAXAEBVInterfaceElement@2@AEAVGenContext@2@@Z __imp_?initialize@LightCompoundNodeGlsl@MaterialX_v1_39_2@@UEAAXAEBVInterfaceElement@2@AEAVGenContext@2@@Z ?createVariables@LightCompoundNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShader@2@@Z __imp_?createVariables@LightCompoundNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShader@2@@Z ?emitFunctionDefinition@LightCompoundNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitFunctionDefinition@LightCompoundNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z ?emitFunctionCall@LightCompoundNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitFunctionCall@LightCompoundNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z ?emitFunctionDefinition@LightCompoundNodeGlsl@MaterialX_v1_39_2@@IEBAXPEAVClosureContext@2@AEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitFunctionDefinition@LightCompoundNodeGlsl@MaterialX_v1_39_2@@IEBAXPEAVClosureContext@2@AEAVGenContext@2@AEAVShaderStage@2@@Z ??0LightNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ __imp_??0LightNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ ?create@LightNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ __imp_?create@LightNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ ?createVariables@LightNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShader@2@@Z __imp_?createVariables@LightNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShader@2@@Z ?emitFunctionCall@LightNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitFunctionCall@LightNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z ??0LightSamplerNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ __imp_??0LightSamplerNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ ?create@LightSamplerNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ __imp_?create@LightSamplerNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ ?emitFunctionDefinition@LightSamplerNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitFunctionDefinition@LightSamplerNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z ??0LightShaderNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ __imp_??0LightShaderNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ ?create@LightShaderNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ __imp_?create@LightShaderNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ ?getTarget@LightShaderNodeGlsl@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ __imp_?getTarget@LightShaderNodeGlsl@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ ?initialize@LightShaderNodeGlsl@MaterialX_v1_39_2@@UEAAXAEBVInterfaceElement@2@AEAVGenContext@2@@Z __imp_?initialize@LightShaderNodeGlsl@MaterialX_v1_39_2@@UEAAXAEBVInterfaceElement@2@AEAVGenContext@2@@Z ?createVariables@LightShaderNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShader@2@@Z __imp_?createVariables@LightShaderNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShader@2@@Z ?emitFunctionCall@LightShaderNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitFunctionCall@LightShaderNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z ??0NumLightsNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ __imp_??0NumLightsNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ ?create@NumLightsNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ __imp_?create@NumLightsNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ ?createVariables@NumLightsNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShader@2@@Z __imp_?createVariables@NumLightsNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShader@2@@Z ?emitFunctionDefinition@NumLightsNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitFunctionDefinition@NumLightsNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z ??0SurfaceNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ __imp_??0SurfaceNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ ?create@SurfaceNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ __imp_?create@SurfaceNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ ?createVariables@SurfaceNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShader@2@@Z __imp_?createVariables@SurfaceNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShader@2@@Z ?emitFunctionCall@SurfaceNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitFunctionCall@SurfaceNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z ?emitLightLoop@SurfaceNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z __imp_?emitLightLoop@SurfaceNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z ?create@UnlitSurfaceNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ __imp_?create@UnlitSurfaceNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ ?emitFunctionCall@UnlitSurfaceNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitFunctionCall@UnlitSurfaceNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z ??0VkResourceBindingContext@MaterialX_v1_39_2@@QEAA@_K@Z __imp_??0VkResourceBindingContext@MaterialX_v1_39_2@@QEAA@_K@Z ?create@VkResourceBindingContext@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VVkResourceBindingContext@MaterialX_v1_39_2@@@std@@_K@Z __imp_?create@VkResourceBindingContext@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VVkResourceBindingContext@MaterialX_v1_39_2@@@std@@_K@Z ?initialize@VkResourceBindingContext@MaterialX_v1_39_2@@UEAAXXZ __imp_?initialize@VkResourceBindingContext@MaterialX_v1_39_2@@UEAAXXZ ?emitDirectives@VkResourceBindingContext@MaterialX_v1_39_2@@UEAAXAEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitDirectives@VkResourceBindingContext@MaterialX_v1_39_2@@UEAAXAEAVGenContext@2@AEAVShaderStage@2@@Z ?emitResourceBindings@VkResourceBindingContext@MaterialX_v1_39_2@@UEAAXAEAVGenContext@2@AEBVVariableBlock@2@AEAVShaderStage@2@@Z __imp_?emitResourceBindings@VkResourceBindingContext@MaterialX_v1_39_2@@UEAAXAEAVGenContext@2@AEBVVariableBlock@2@AEAVShaderStage@2@@Z ?emitStructuredResourceBindings@VkResourceBindingContext@MaterialX_v1_39_2@@UEAAXAEAVGenContext@2@AEBVVariableBlock@2@AEAVShaderStage@2@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@3@Z __imp_?emitStructuredResourceBindings@VkResourceBindingContext@MaterialX_v1_39_2@@UEAAXAEAVGenContext@2@AEBVVariableBlock@2@AEAVShaderStage@2@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@3@Z ??1VkResourceBindingContext@MaterialX_v1_39_2@@UEAA@XZ __imp_??1VkResourceBindingContext@MaterialX_v1_39_2@@UEAA@XZ ??0VkResourceBindingContext@MaterialX_v1_39_2@@QEAA@AEBV01@@Z __imp_??0VkResourceBindingContext@MaterialX_v1_39_2@@QEAA@AEBV01@@Z ??0VkResourceBindingContext@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z __imp_??0VkResourceBindingContext@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z ??4VkResourceBindingContext@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z __imp_??4VkResourceBindingContext@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z ??4VkResourceBindingContext@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z __imp_??4VkResourceBindingContext@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z __imp_??_7VkResourceBindingContext@MaterialX_v1_39_2@@6B@ ??0VkShaderGenerator@MaterialX_v1_39_2@@QEAA@XZ __imp_??0VkShaderGenerator@MaterialX_v1_39_2@@QEAA@XZ ?create@VkShaderGenerator@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderGenerator@MaterialX_v1_39_2@@@std@@XZ __imp_?create@VkShaderGenerator@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderGenerator@MaterialX_v1_39_2@@@std@@XZ ?getTarget@VkShaderGenerator@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ __imp_?getTarget@VkShaderGenerator@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ ?getVersion@VkShaderGenerator@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ __imp_?getVersion@VkShaderGenerator@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ ?getVertexDataPrefix@VkShaderGenerator@MaterialX_v1_39_2@@UEBA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEBVVariableBlock@2@@Z __imp_?getVertexDataPrefix@VkShaderGenerator@MaterialX_v1_39_2@@UEBA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEBVVariableBlock@2@@Z ?emitDirectives@VkShaderGenerator@MaterialX_v1_39_2@@UEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitDirectives@VkShaderGenerator@MaterialX_v1_39_2@@UEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z ?emitInputs@VkShaderGenerator@MaterialX_v1_39_2@@UEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitInputs@VkShaderGenerator@MaterialX_v1_39_2@@UEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z ?emitOutputs@VkShaderGenerator@MaterialX_v1_39_2@@UEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitOutputs@VkShaderGenerator@MaterialX_v1_39_2@@UEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z ?getResourceBindingContext@VkShaderGenerator@MaterialX_v1_39_2@@MEBA?AV?$shared_ptr@VHwResourceBindingContext@MaterialX_v1_39_2@@@std@@AEAVGenContext@2@@Z __imp_?getResourceBindingContext@VkShaderGenerator@MaterialX_v1_39_2@@MEBA?AV?$shared_ptr@VHwResourceBindingContext@MaterialX_v1_39_2@@@std@@AEAVGenContext@2@@Z ??1VkShaderGenerator@MaterialX_v1_39_2@@UEAA@XZ __imp_??1VkShaderGenerator@MaterialX_v1_39_2@@UEAA@XZ ??0VkShaderGenerator@MaterialX_v1_39_2@@QEAA@AEBV01@@Z __imp_??0VkShaderGenerator@MaterialX_v1_39_2@@QEAA@AEBV01@@Z ??0VkShaderGenerator@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z __imp_??0VkShaderGenerator@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z ?create@VkSyntax@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VSyntax@MaterialX_v1_39_2@@@std@@XZ __imp_?create@VkSyntax@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VSyntax@MaterialX_v1_39_2@@@std@@XZ ?getInputQualifier@VkSyntax@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ __imp_?getInputQualifier@VkSyntax@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ ??1VkSyntax@MaterialX_v1_39_2@@UEAA@XZ __imp_??1VkSyntax@MaterialX_v1_39_2@@UEAA@XZ ??0VkSyntax@MaterialX_v1_39_2@@QEAA@AEBV01@@Z __imp_??0VkSyntax@MaterialX_v1_39_2@@QEAA@AEBV01@@Z ??0VkSyntax@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z __imp_??0VkSyntax@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z ??4VkSyntax@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z __imp_??4VkSyntax@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z ??4VkSyntax@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z __imp_??4VkSyntax@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z __imp_?TARGET@VkShaderGenerator@MaterialX_v1_39_2@@2V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@B __imp_?VERSION@VkShaderGenerator@MaterialX_v1_39_2@@2V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@B __imp_??_7VkShaderGenerator@MaterialX_v1_39_2@@6B@ __imp_??_7VkSyntax@MaterialX_v1_39_2@@6B@ ??0VkSyntax@MaterialX_v1_39_2@@QEAA@XZ __imp_??0VkSyntax@MaterialX_v1_39_2@@QEAA@XZ 
/               -1                      0       47207     `
  Pq  鑤 � J) h0 �: 肖 鎱 D� t� 槪 畟 � J� ⒊ by � �' �/ *2 8� v n
 � �# 枲 聏  x 桄 �  �"  �- �& � 垔 顗 @� 牱 j� 膰  �  N� f� 茚 0� 杬 鼀 � b� � 仄 p� f� 婷 :� ⑷ |� .{ 爠 (� "5 � � :  0� 鴥 N� 牭 鸫 n� 唵 �  � (3 , � 饮 p� > � 4 L �$ 
: h � �
 �; j( 鼃 馈 ^| 緖 F� 牤 
8 趡 X� 8~ 攠 敱 臧 衻 簪 $� v 鸩 <� ,� r� 詵 隀 陋 H�  � p� 抬 � x� 0� :� 枍 藓 4�  膿 "� �� 丶 鷮 敠 V� 皨 姽 薷 `� 鎽 B� 0� 喕 敥 灂  叹 &� 巜 � 魐 Xv 颅  � H� >� 
� 炁 6� L� 尥  b� "� 壬 娝 柑 V�  � �< �8 起 �. 庋 阂 鲋 2� � 捰 凶 � 4� 8� � 犫 漶 2� r� 蛤 � H� 愺 �  r� � �  ,* 燧 ま 軒 勮 + �5 L� � � � 異 Z� $� � 垝 (� � d�  �+ �6 赧 f <� 译 树 �	 � れ 烒 B p t�  覜 J� b7 滭 � p \� ,� 剼 p� 吕 v� 皾 0� �, J1 &4 j� �  �%  � r� � �! 挰 酁 H� 郝 �  � � � ο v� �  � � �    2 1 = b a _ i h f o n l   
 G F > 
 	 K + * . & % � � � � �  � } | � � � � � � � � � � t s � x w u � � � � �  0 ` g m  E  ) $ � ~ { � � � r v � � � 4 3 d c k j q p   I H - , ( ' � � � � � � � � � � z y � � 
	� � �  / � � � ?   � � � � � � � � � � � � � � � � � � � � S  A R � � � � � � � � � � � � � � � �  V � U \ �  W � Q B � � Z C � [  T M P D L ! N     X  #  ^  � � � � " �   �  O � @ � � � e � Y  ] �   � � �    2 1 = b a _ i h f o n l   
 G F > 
 	 K + * . & % � � � � �  � } | � � � � � � � � � � t s � x w u � � � � �  0 ` g m  E  ) $ � ~ { � � � r v � � � 4 3 d c k j q p   I H - , ( ' � � � � � � � � � � z y � � 
	� 9 < � � � 6 J 5 ; : � � � � � � � � � 
� � � � � 7 � � � � � 8 � � � �  / � � � ?   � � � � � � � � � � � � � � � � � � � � S  A R � � � � � � � � � � � � � � � �  V � U \ �  W � Q B � � Z C � [  T M P D L ! N     X  #  ^  � � � � " �   �  O � @ � � � e � Y  ] �  ??0BlurNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z ??0BlurNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z ??0BlurNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ ??0EsslShaderGenerator@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z ??0EsslShaderGenerator@MaterialX_v1_39_2@@QEAA@AEBV01@@Z ??0EsslShaderGenerator@MaterialX_v1_39_2@@QEAA@XZ ??0EsslSyntax@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z ??0EsslSyntax@MaterialX_v1_39_2@@QEAA@AEBV01@@Z ??0EsslSyntax@MaterialX_v1_39_2@@QEAA@XZ ??0GeomColorNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z ??0GeomColorNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z ??0GeomColorNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ ??0GeomPropValueNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z ??0GeomPropValueNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z ??0GeomPropValueNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ ??0GeomPropValueNodeGlslAsUniform@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z ??0GeomPropValueNodeGlslAsUniform@MaterialX_v1_39_2@@QEAA@AEBV01@@Z ??0GeomPropValueNodeGlslAsUniform@MaterialX_v1_39_2@@QEAA@XZ ??0GlslImplementation@MaterialX_v1_39_2@@IEAA@XZ ??0GlslImplementation@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z ??0GlslImplementation@MaterialX_v1_39_2@@QEAA@AEBV01@@Z ??0GlslResourceBindingContext@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z ??0GlslResourceBindingContext@MaterialX_v1_39_2@@QEAA@AEBV01@@Z ??0GlslResourceBindingContext@MaterialX_v1_39_2@@QEAA@_K0@Z ??0GlslShaderGenerator@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z ??0GlslShaderGenerator@MaterialX_v1_39_2@@QEAA@AEBV01@@Z ??0GlslShaderGenerator@MaterialX_v1_39_2@@QEAA@XZ ??0GlslStructTypeSyntax@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z ??0GlslStructTypeSyntax@MaterialX_v1_39_2@@QEAA@AEBV01@@Z ??0GlslStructTypeSyntax@MaterialX_v1_39_2@@QEAA@PEBVSyntax@1@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@1111AEBV?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@4@@Z ??0GlslSyntax@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z ??0GlslSyntax@MaterialX_v1_39_2@@QEAA@AEBV01@@Z ??0GlslSyntax@MaterialX_v1_39_2@@QEAA@XZ ??0HeightToNormalNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z ??0HeightToNormalNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z ??0HeightToNormalNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ ??0LightCompoundNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z ??0LightCompoundNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z ??0LightCompoundNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ ??0LightNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z ??0LightNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z ??0LightNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ ??0LightSamplerNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z ??0LightSamplerNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z ??0LightSamplerNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ ??0LightShaderNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z ??0LightShaderNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z ??0LightShaderNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ ??0NumLightsNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z ??0NumLightsNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z ??0NumLightsNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ ??0SurfaceNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z ??0SurfaceNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z ??0SurfaceNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ ??0UnlitSurfaceNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z ??0UnlitSurfaceNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z ??0UnlitSurfaceNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ ??0VkResourceBindingContext@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z ??0VkResourceBindingContext@MaterialX_v1_39_2@@QEAA@AEBV01@@Z ??0VkResourceBindingContext@MaterialX_v1_39_2@@QEAA@_K@Z ??0VkShaderGenerator@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z ??0VkShaderGenerator@MaterialX_v1_39_2@@QEAA@AEBV01@@Z ??0VkShaderGenerator@MaterialX_v1_39_2@@QEAA@XZ ??0VkSyntax@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z ??0VkSyntax@MaterialX_v1_39_2@@QEAA@AEBV01@@Z ??0VkSyntax@MaterialX_v1_39_2@@QEAA@XZ ??1BlurNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ ??1EsslShaderGenerator@MaterialX_v1_39_2@@UEAA@XZ ??1EsslSyntax@MaterialX_v1_39_2@@UEAA@XZ ??1GeomColorNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ ??1GeomPropValueNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ ??1GeomPropValueNodeGlslAsUniform@MaterialX_v1_39_2@@UEAA@XZ ??1GlslImplementation@MaterialX_v1_39_2@@UEAA@XZ ??1GlslResourceBindingContext@MaterialX_v1_39_2@@UEAA@XZ ??1GlslShaderGenerator@MaterialX_v1_39_2@@UEAA@XZ ??1GlslStructTypeSyntax@MaterialX_v1_39_2@@UEAA@XZ ??1GlslSyntax@MaterialX_v1_39_2@@UEAA@XZ ??1HeightToNormalNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ ??1LightCompoundNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ ??1LightNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ ??1LightSamplerNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ ??1LightShaderNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ ??1NumLightsNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ ??1SurfaceNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ ??1UnlitSurfaceNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ ??1VkResourceBindingContext@MaterialX_v1_39_2@@UEAA@XZ ??1VkShaderGenerator@MaterialX_v1_39_2@@UEAA@XZ ??1VkSyntax@MaterialX_v1_39_2@@UEAA@XZ ??4BlurNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z ??4BlurNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z ??4EsslSyntax@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z ??4EsslSyntax@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z ??4GeomColorNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z ??4GeomColorNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z ??4GeomPropValueNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z ??4GeomPropValueNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z ??4GeomPropValueNodeGlslAsUniform@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z ??4GeomPropValueNodeGlslAsUniform@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z ??4GlslImplementation@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z ??4GlslImplementation@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z ??4GlslResourceBindingContext@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z ??4GlslResourceBindingContext@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z ??4GlslStructTypeSyntax@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z ??4GlslStructTypeSyntax@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z ??4GlslSyntax@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z ??4GlslSyntax@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z ??4HeightToNormalNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z ??4HeightToNormalNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z ??4LightCompoundNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z ??4LightCompoundNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z ??4LightSamplerNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z ??4LightSamplerNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z ??4LightShaderNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z ??4LightShaderNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z ??4NumLightsNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z ??4NumLightsNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z ??4UnlitSurfaceNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z ??4UnlitSurfaceNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z ??4VkResourceBindingContext@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z ??4VkResourceBindingContext@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z ??4VkSyntax@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z ??4VkSyntax@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z ?acceptsInputType@HeightToNormalNodeGlsl@MaterialX_v1_39_2@@MEBA_NVTypeDesc@2@@Z ?computeSampleOffsetStrings@HeightToNormalNodeGlsl@MaterialX_v1_39_2@@MEBAXAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@0IAEAV?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@4@@Z ?create@BlurNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ ?create@EsslShaderGenerator@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderGenerator@MaterialX_v1_39_2@@@std@@XZ ?create@EsslSyntax@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VSyntax@MaterialX_v1_39_2@@@std@@XZ ?create@GeomColorNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ ?create@GeomPropValueNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ ?create@GeomPropValueNodeGlslAsUniform@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ ?create@GlslResourceBindingContext@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VGlslResourceBindingContext@MaterialX_v1_39_2@@@std@@_K0@Z ?create@GlslShaderGenerator@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderGenerator@MaterialX_v1_39_2@@@std@@XZ ?create@GlslSyntax@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VSyntax@MaterialX_v1_39_2@@@std@@XZ ?create@HeightToNormalNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ ?create@LightCompoundNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ ?create@LightNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ ?create@LightSamplerNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ ?create@LightShaderNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ ?create@NumLightsNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ ?create@SurfaceNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ ?create@UnlitSurfaceNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ ?create@VkResourceBindingContext@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VVkResourceBindingContext@MaterialX_v1_39_2@@@std@@_K@Z ?create@VkShaderGenerator@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderGenerator@MaterialX_v1_39_2@@@std@@XZ ?create@VkSyntax@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VSyntax@MaterialX_v1_39_2@@@std@@XZ ?createStructSyntax@GlslSyntax@MaterialX_v1_39_2@@MEBA?AV?$shared_ptr@VStructTypeSyntax@MaterialX_v1_39_2@@@std@@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@4@0000@Z ?createVariables@GeomColorNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShader@2@@Z ?createVariables@GeomPropValueNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShader@2@@Z ?createVariables@GeomPropValueNodeGlslAsUniform@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShader@2@@Z ?createVariables@HeightToNormalNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShader@2@@Z ?createVariables@LightCompoundNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShader@2@@Z ?createVariables@LightNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShader@2@@Z ?createVariables@LightShaderNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShader@2@@Z ?createVariables@NumLightsNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShader@2@@Z ?createVariables@SurfaceNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShader@2@@Z ?emitConstants@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z ?emitDirectives@EsslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z ?emitDirectives@GlslResourceBindingContext@MaterialX_v1_39_2@@UEAAXAEAVGenContext@2@AEAVShaderStage@2@@Z ?emitDirectives@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z ?emitDirectives@VkResourceBindingContext@MaterialX_v1_39_2@@UEAAXAEAVGenContext@2@AEAVShaderStage@2@@Z ?emitDirectives@VkShaderGenerator@MaterialX_v1_39_2@@UEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z ?emitFunctionCall@GeomColorNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z ?emitFunctionCall@GeomPropValueNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z ?emitFunctionCall@GeomPropValueNodeGlslAsUniform@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z ?emitFunctionCall@HeightToNormalNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z ?emitFunctionCall@LightCompoundNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z ?emitFunctionCall@LightNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z ?emitFunctionCall@LightShaderNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z ?emitFunctionCall@SurfaceNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z ?emitFunctionCall@UnlitSurfaceNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z ?emitFunctionDefinition@HeightToNormalNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z ?emitFunctionDefinition@LightCompoundNodeGlsl@MaterialX_v1_39_2@@IEBAXPEAVClosureContext@2@AEAVGenContext@2@AEAVShaderStage@2@@Z ?emitFunctionDefinition@LightCompoundNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z ?emitFunctionDefinition@LightSamplerNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z ?emitFunctionDefinition@NumLightsNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z ?emitInputs@EsslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z ?emitInputs@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z ?emitInputs@VkShaderGenerator@MaterialX_v1_39_2@@UEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z ?emitLightData@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z ?emitLightFunctionDefinitions@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEBVShaderGraph@2@AEAVGenContext@2@AEAVShaderStage@2@@Z ?emitLightLoop@SurfaceNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z ?emitOutputs@EsslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z ?emitOutputs@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z ?emitOutputs@VkShaderGenerator@MaterialX_v1_39_2@@UEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z ?emitPixelStage@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEBVShaderGraph@2@AEAVGenContext@2@AEAVShaderStage@2@@Z ?emitResourceBindings@GlslResourceBindingContext@MaterialX_v1_39_2@@UEAAXAEAVGenContext@2@AEBVVariableBlock@2@AEAVShaderStage@2@@Z ?emitResourceBindings@VkResourceBindingContext@MaterialX_v1_39_2@@UEAAXAEAVGenContext@2@AEBVVariableBlock@2@AEAVShaderStage@2@@Z ?emitSamplingFunctionDefinition@BlurNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z ?emitSpecularEnvironment@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z ?emitStructuredResourceBindings@GlslResourceBindingContext@MaterialX_v1_39_2@@UEAAXAEAVGenContext@2@AEBVVariableBlock@2@AEAVShaderStage@2@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@3@Z ?emitStructuredResourceBindings@VkResourceBindingContext@MaterialX_v1_39_2@@UEAAXAEAVGenContext@2@AEBVVariableBlock@2@AEAVShaderStage@2@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@3@Z ?emitTransmissionRender@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z ?emitUniforms@EsslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z ?emitUniforms@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z ?emitVariableDeclaration@GlslShaderGenerator@MaterialX_v1_39_2@@UEBAXPEBVShaderPort@2@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAVGenContext@2@AEAVShaderStage@2@_N@Z ?emitVertexStage@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEBVShaderGraph@2@AEAVGenContext@2@AEAVShaderStage@2@@Z ?enableSeparateBindingLocations@GlslResourceBindingContext@MaterialX_v1_39_2@@QEAAX_N@Z ?generate@GlslShaderGenerator@MaterialX_v1_39_2@@UEBA?AV?$shared_ptr@VShader@MaterialX_v1_39_2@@@std@@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@4@V?$shared_ptr@VElement@MaterialX_v1_39_2@@@4@AEAVGenContext@2@@Z ?getConstantQualifier@GlslSyntax@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ ?getImplementation@GlslShaderGenerator@MaterialX_v1_39_2@@UEBA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@AEBVNodeDef@2@AEAVGenContext@2@@Z ?getInputQualifier@GlslSyntax@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ ?getInputQualifier@VkSyntax@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ ?getOutputQualifier@GlslSyntax@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ ?getResourceBindingContext@EsslShaderGenerator@MaterialX_v1_39_2@@MEBA?AV?$shared_ptr@VHwResourceBindingContext@MaterialX_v1_39_2@@@std@@AEAVGenContext@2@@Z ?getResourceBindingContext@GlslShaderGenerator@MaterialX_v1_39_2@@MEBA?AV?$shared_ptr@VHwResourceBindingContext@MaterialX_v1_39_2@@@std@@AEAVGenContext@2@@Z ?getResourceBindingContext@VkShaderGenerator@MaterialX_v1_39_2@@MEBA?AV?$shared_ptr@VHwResourceBindingContext@MaterialX_v1_39_2@@@std@@AEAVGenContext@2@@Z ?getSourceFileExtension@GlslSyntax@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ ?getTarget@EsslShaderGenerator@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ ?getTarget@GlslImplementation@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ ?getTarget@GlslShaderGenerator@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ ?getTarget@HeightToNormalNodeGlsl@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ ?getTarget@LightCompoundNodeGlsl@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ ?getTarget@LightShaderNodeGlsl@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ ?getTarget@VkShaderGenerator@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ ?getUniformQualifier@GlslSyntax@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ ?getValue@GlslStructTypeSyntax@MaterialX_v1_39_2@@UEBA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEBVValue@2@_N@Z ?getVersion@EsslShaderGenerator@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ ?getVersion@GlslShaderGenerator@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ ?getVersion@VkShaderGenerator@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ ?getVertexDataPrefix@EsslShaderGenerator@MaterialX_v1_39_2@@UEBA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEBVVariableBlock@2@@Z ?getVertexDataPrefix@GlslShaderGenerator@MaterialX_v1_39_2@@UEBA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEBVVariableBlock@2@@Z ?getVertexDataPrefix@VkShaderGenerator@MaterialX_v1_39_2@@UEBA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEBVVariableBlock@2@@Z ?initialize@GlslResourceBindingContext@MaterialX_v1_39_2@@UEAAXXZ ?initialize@LightCompoundNodeGlsl@MaterialX_v1_39_2@@UEAAXAEBVInterfaceElement@2@AEAVGenContext@2@@Z ?initialize@LightShaderNodeGlsl@MaterialX_v1_39_2@@UEAAXAEBVInterfaceElement@2@AEAVGenContext@2@@Z ?initialize@VkResourceBindingContext@MaterialX_v1_39_2@@UEAAXXZ ?isEditable@GeomPropValueNodeGlsl@MaterialX_v1_39_2@@UEBA_NAEBVShaderInput@2@@Z ?remapEnumeration@GlslSyntax@MaterialX_v1_39_2@@UEBA_NAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@VTypeDesc@2@0AEAU?$pair@VTypeDesc@MaterialX_v1_39_2@@V?$shared_ptr@VValue@MaterialX_v1_39_2@@@std@@@4@@Z ?requiresLighting@GlslShaderGenerator@MaterialX_v1_39_2@@MEBA_NAEBVShaderGraph@2@@Z ?toVec4@GlslShaderGenerator@MaterialX_v1_39_2@@KAXPEBVTypeDesc@2@AEAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z ?toVec4@GlslShaderGenerator@MaterialX_v1_39_2@@KAXVTypeDesc@2@AEAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z ?typeSupported@GlslSyntax@MaterialX_v1_39_2@@UEBA_NPEBVTypeDesc@2@@Z __IMPORT_DESCRIPTOR_MaterialXGenGlsl_d __NULL_IMPORT_DESCRIPTOR __imp_??0BlurNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z __imp_??0BlurNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z __imp_??0BlurNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ __imp_??0EsslShaderGenerator@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z __imp_??0EsslShaderGenerator@MaterialX_v1_39_2@@QEAA@AEBV01@@Z __imp_??0EsslShaderGenerator@MaterialX_v1_39_2@@QEAA@XZ __imp_??0EsslSyntax@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z __imp_??0EsslSyntax@MaterialX_v1_39_2@@QEAA@AEBV01@@Z __imp_??0EsslSyntax@MaterialX_v1_39_2@@QEAA@XZ __imp_??0GeomColorNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z __imp_??0GeomColorNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z __imp_??0GeomColorNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ __imp_??0GeomPropValueNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z __imp_??0GeomPropValueNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z __imp_??0GeomPropValueNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ __imp_??0GeomPropValueNodeGlslAsUniform@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z __imp_??0GeomPropValueNodeGlslAsUniform@MaterialX_v1_39_2@@QEAA@AEBV01@@Z __imp_??0GeomPropValueNodeGlslAsUniform@MaterialX_v1_39_2@@QEAA@XZ __imp_??0GlslImplementation@MaterialX_v1_39_2@@IEAA@XZ __imp_??0GlslImplementation@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z __imp_??0GlslImplementation@MaterialX_v1_39_2@@QEAA@AEBV01@@Z __imp_??0GlslResourceBindingContext@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z __imp_??0GlslResourceBindingContext@MaterialX_v1_39_2@@QEAA@AEBV01@@Z __imp_??0GlslResourceBindingContext@MaterialX_v1_39_2@@QEAA@_K0@Z __imp_??0GlslShaderGenerator@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z __imp_??0GlslShaderGenerator@MaterialX_v1_39_2@@QEAA@AEBV01@@Z __imp_??0GlslShaderGenerator@MaterialX_v1_39_2@@QEAA@XZ __imp_??0GlslStructTypeSyntax@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z __imp_??0GlslStructTypeSyntax@MaterialX_v1_39_2@@QEAA@AEBV01@@Z __imp_??0GlslStructTypeSyntax@MaterialX_v1_39_2@@QEAA@PEBVSyntax@1@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@1111AEBV?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@4@@Z __imp_??0GlslSyntax@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z __imp_??0GlslSyntax@MaterialX_v1_39_2@@QEAA@AEBV01@@Z __imp_??0GlslSyntax@MaterialX_v1_39_2@@QEAA@XZ __imp_??0HeightToNormalNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z __imp_??0HeightToNormalNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z __imp_??0HeightToNormalNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ __imp_??0LightCompoundNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z __imp_??0LightCompoundNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z __imp_??0LightCompoundNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ __imp_??0LightNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z __imp_??0LightNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z __imp_??0LightNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ __imp_??0LightSamplerNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z __imp_??0LightSamplerNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z __imp_??0LightSamplerNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ __imp_??0LightShaderNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z __imp_??0LightShaderNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z __imp_??0LightShaderNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ __imp_??0NumLightsNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z __imp_??0NumLightsNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z __imp_??0NumLightsNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ __imp_??0SurfaceNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z __imp_??0SurfaceNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z __imp_??0SurfaceNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ __imp_??0UnlitSurfaceNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z __imp_??0UnlitSurfaceNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z __imp_??0UnlitSurfaceNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ __imp_??0VkResourceBindingContext@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z __imp_??0VkResourceBindingContext@MaterialX_v1_39_2@@QEAA@AEBV01@@Z __imp_??0VkResourceBindingContext@MaterialX_v1_39_2@@QEAA@_K@Z __imp_??0VkShaderGenerator@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z __imp_??0VkShaderGenerator@MaterialX_v1_39_2@@QEAA@AEBV01@@Z __imp_??0VkShaderGenerator@MaterialX_v1_39_2@@QEAA@XZ __imp_??0VkSyntax@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z __imp_??0VkSyntax@MaterialX_v1_39_2@@QEAA@AEBV01@@Z __imp_??0VkSyntax@MaterialX_v1_39_2@@QEAA@XZ __imp_??1BlurNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ __imp_??1EsslShaderGenerator@MaterialX_v1_39_2@@UEAA@XZ __imp_??1EsslSyntax@MaterialX_v1_39_2@@UEAA@XZ __imp_??1GeomColorNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ __imp_??1GeomPropValueNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ __imp_??1GeomPropValueNodeGlslAsUniform@MaterialX_v1_39_2@@UEAA@XZ __imp_??1GlslImplementation@MaterialX_v1_39_2@@UEAA@XZ __imp_??1GlslResourceBindingContext@MaterialX_v1_39_2@@UEAA@XZ __imp_??1GlslShaderGenerator@MaterialX_v1_39_2@@UEAA@XZ __imp_??1GlslStructTypeSyntax@MaterialX_v1_39_2@@UEAA@XZ __imp_??1GlslSyntax@MaterialX_v1_39_2@@UEAA@XZ __imp_??1HeightToNormalNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ __imp_??1LightCompoundNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ __imp_??1LightNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ __imp_??1LightSamplerNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ __imp_??1LightShaderNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ __imp_??1NumLightsNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ __imp_??1SurfaceNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ __imp_??1UnlitSurfaceNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ __imp_??1VkResourceBindingContext@MaterialX_v1_39_2@@UEAA@XZ __imp_??1VkShaderGenerator@MaterialX_v1_39_2@@UEAA@XZ __imp_??1VkSyntax@MaterialX_v1_39_2@@UEAA@XZ __imp_??4BlurNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z __imp_??4BlurNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z __imp_??4EsslSyntax@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z __imp_??4EsslSyntax@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z __imp_??4GeomColorNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z __imp_??4GeomColorNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z __imp_??4GeomPropValueNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z __imp_??4GeomPropValueNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z __imp_??4GeomPropValueNodeGlslAsUniform@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z __imp_??4GeomPropValueNodeGlslAsUniform@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z __imp_??4GlslImplementation@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z __imp_??4GlslImplementation@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z __imp_??4GlslResourceBindingContext@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z __imp_??4GlslResourceBindingContext@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z __imp_??4GlslStructTypeSyntax@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z __imp_??4GlslStructTypeSyntax@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z __imp_??4GlslSyntax@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z __imp_??4GlslSyntax@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z __imp_??4HeightToNormalNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z __imp_??4HeightToNormalNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z __imp_??4LightCompoundNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z __imp_??4LightCompoundNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z __imp_??4LightSamplerNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z __imp_??4LightSamplerNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z __imp_??4LightShaderNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z __imp_??4LightShaderNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z __imp_??4NumLightsNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z __imp_??4NumLightsNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z __imp_??4UnlitSurfaceNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z __imp_??4UnlitSurfaceNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z __imp_??4VkResourceBindingContext@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z __imp_??4VkResourceBindingContext@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z __imp_??4VkSyntax@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z __imp_??4VkSyntax@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z __imp_??_7BlurNodeGlsl@MaterialX_v1_39_2@@6B@ __imp_??_7EsslShaderGenerator@MaterialX_v1_39_2@@6B@ __imp_??_7EsslSyntax@MaterialX_v1_39_2@@6B@ __imp_??_7GeomColorNodeGlsl@MaterialX_v1_39_2@@6B@ __imp_??_7GeomPropValueNodeGlsl@MaterialX_v1_39_2@@6B@ __imp_??_7GeomPropValueNodeGlslAsUniform@MaterialX_v1_39_2@@6B@ __imp_??_7GlslImplementation@MaterialX_v1_39_2@@6B@ __imp_??_7GlslResourceBindingContext@MaterialX_v1_39_2@@6B@ __imp_??_7GlslShaderGenerator@MaterialX_v1_39_2@@6B@ __imp_??_7GlslStructTypeSyntax@MaterialX_v1_39_2@@6B@ __imp_??_7GlslSyntax@MaterialX_v1_39_2@@6B@ __imp_??_7HeightToNormalNodeGlsl@MaterialX_v1_39_2@@6B@ __imp_??_7LightCompoundNodeGlsl@MaterialX_v1_39_2@@6B@ __imp_??_7LightNodeGlsl@MaterialX_v1_39_2@@6B@ __imp_??_7LightSamplerNodeGlsl@MaterialX_v1_39_2@@6B@ __imp_??_7LightShaderNodeGlsl@MaterialX_v1_39_2@@6B@ __imp_??_7NumLightsNodeGlsl@MaterialX_v1_39_2@@6B@ __imp_??_7SurfaceNodeGlsl@MaterialX_v1_39_2@@6B@ __imp_??_7UnlitSurfaceNodeGlsl@MaterialX_v1_39_2@@6B@ __imp_??_7VkResourceBindingContext@MaterialX_v1_39_2@@6B@ __imp_??_7VkShaderGenerator@MaterialX_v1_39_2@@6B@ __imp_??_7VkSyntax@MaterialX_v1_39_2@@6B@ __imp_?CONSTANT_QUALIFIER@GlslSyntax@MaterialX_v1_39_2@@2V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@B __imp_?FLAT_QUALIFIER@GlslSyntax@MaterialX_v1_39_2@@2V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@B __imp_?INPUT_QUALIFIER@GlslSyntax@MaterialX_v1_39_2@@2V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@B __imp_?OUTPUT_QUALIFIER@GlslSyntax@MaterialX_v1_39_2@@2V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@B __imp_?SOURCE_FILE_EXTENSION@GlslSyntax@MaterialX_v1_39_2@@2V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@B __imp_?TARGET@EsslShaderGenerator@MaterialX_v1_39_2@@2V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@B __imp_?TARGET@GlslShaderGenerator@MaterialX_v1_39_2@@2V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@B __imp_?TARGET@VkShaderGenerator@MaterialX_v1_39_2@@2V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@B __imp_?UNIFORM_QUALIFIER@GlslSyntax@MaterialX_v1_39_2@@2V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@B __imp_?VEC2_MEMBERS@GlslSyntax@MaterialX_v1_39_2@@2V?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@B __imp_?VEC3_MEMBERS@GlslSyntax@MaterialX_v1_39_2@@2V?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@B __imp_?VEC4_MEMBERS@GlslSyntax@MaterialX_v1_39_2@@2V?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@B __imp_?VERSION@EsslShaderGenerator@MaterialX_v1_39_2@@2V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@B __imp_?VERSION@GlslShaderGenerator@MaterialX_v1_39_2@@2V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@B __imp_?VERSION@VkShaderGenerator@MaterialX_v1_39_2@@2V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@B __imp_?acceptsInputType@HeightToNormalNodeGlsl@MaterialX_v1_39_2@@MEBA_NVTypeDesc@2@@Z __imp_?computeSampleOffsetStrings@HeightToNormalNodeGlsl@MaterialX_v1_39_2@@MEBAXAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@0IAEAV?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@4@@Z __imp_?create@BlurNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ __imp_?create@EsslShaderGenerator@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderGenerator@MaterialX_v1_39_2@@@std@@XZ __imp_?create@EsslSyntax@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VSyntax@MaterialX_v1_39_2@@@std@@XZ __imp_?create@GeomColorNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ __imp_?create@GeomPropValueNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ __imp_?create@GeomPropValueNodeGlslAsUniform@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ __imp_?create@GlslResourceBindingContext@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VGlslResourceBindingContext@MaterialX_v1_39_2@@@std@@_K0@Z __imp_?create@GlslShaderGenerator@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderGenerator@MaterialX_v1_39_2@@@std@@XZ __imp_?create@GlslSyntax@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VSyntax@MaterialX_v1_39_2@@@std@@XZ __imp_?create@HeightToNormalNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ __imp_?create@LightCompoundNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ __imp_?create@LightNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ __imp_?create@LightSamplerNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ __imp_?create@LightShaderNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ __imp_?create@NumLightsNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ __imp_?create@SurfaceNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ __imp_?create@UnlitSurfaceNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ __imp_?create@VkResourceBindingContext@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VVkResourceBindingContext@MaterialX_v1_39_2@@@std@@_K@Z __imp_?create@VkShaderGenerator@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderGenerator@MaterialX_v1_39_2@@@std@@XZ __imp_?create@VkSyntax@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VSyntax@MaterialX_v1_39_2@@@std@@XZ __imp_?createStructSyntax@GlslSyntax@MaterialX_v1_39_2@@MEBA?AV?$shared_ptr@VStructTypeSyntax@MaterialX_v1_39_2@@@std@@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@4@0000@Z __imp_?createVariables@GeomColorNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShader@2@@Z __imp_?createVariables@GeomPropValueNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShader@2@@Z __imp_?createVariables@GeomPropValueNodeGlslAsUniform@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShader@2@@Z __imp_?createVariables@HeightToNormalNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShader@2@@Z __imp_?createVariables@LightCompoundNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShader@2@@Z __imp_?createVariables@LightNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShader@2@@Z __imp_?createVariables@LightShaderNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShader@2@@Z __imp_?createVariables@NumLightsNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShader@2@@Z __imp_?createVariables@SurfaceNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShader@2@@Z __imp_?emitConstants@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitDirectives@EsslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitDirectives@GlslResourceBindingContext@MaterialX_v1_39_2@@UEAAXAEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitDirectives@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitDirectives@VkResourceBindingContext@MaterialX_v1_39_2@@UEAAXAEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitDirectives@VkShaderGenerator@MaterialX_v1_39_2@@UEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitFunctionCall@GeomColorNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitFunctionCall@GeomPropValueNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitFunctionCall@GeomPropValueNodeGlslAsUniform@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitFunctionCall@HeightToNormalNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitFunctionCall@LightCompoundNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitFunctionCall@LightNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitFunctionCall@LightShaderNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitFunctionCall@SurfaceNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitFunctionCall@UnlitSurfaceNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitFunctionDefinition@HeightToNormalNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitFunctionDefinition@LightCompoundNodeGlsl@MaterialX_v1_39_2@@IEBAXPEAVClosureContext@2@AEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitFunctionDefinition@LightCompoundNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitFunctionDefinition@LightSamplerNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitFunctionDefinition@NumLightsNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitInputs@EsslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitInputs@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitInputs@VkShaderGenerator@MaterialX_v1_39_2@@UEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitLightData@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitLightFunctionDefinitions@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEBVShaderGraph@2@AEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitLightLoop@SurfaceNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z __imp_?emitOutputs@EsslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitOutputs@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitOutputs@VkShaderGenerator@MaterialX_v1_39_2@@UEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitPixelStage@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEBVShaderGraph@2@AEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitResourceBindings@GlslResourceBindingContext@MaterialX_v1_39_2@@UEAAXAEAVGenContext@2@AEBVVariableBlock@2@AEAVShaderStage@2@@Z __imp_?emitResourceBindings@VkResourceBindingContext@MaterialX_v1_39_2@@UEAAXAEAVGenContext@2@AEBVVariableBlock@2@AEAVShaderStage@2@@Z __imp_?emitSamplingFunctionDefinition@BlurNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitSpecularEnvironment@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitStructuredResourceBindings@GlslResourceBindingContext@MaterialX_v1_39_2@@UEAAXAEAVGenContext@2@AEBVVariableBlock@2@AEAVShaderStage@2@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@3@Z __imp_?emitStructuredResourceBindings@VkResourceBindingContext@MaterialX_v1_39_2@@UEAAXAEAVGenContext@2@AEBVVariableBlock@2@AEAVShaderStage@2@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@3@Z __imp_?emitTransmissionRender@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitUniforms@EsslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitUniforms@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?emitVariableDeclaration@GlslShaderGenerator@MaterialX_v1_39_2@@UEBAXPEBVShaderPort@2@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAVGenContext@2@AEAVShaderStage@2@_N@Z __imp_?emitVertexStage@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEBVShaderGraph@2@AEAVGenContext@2@AEAVShaderStage@2@@Z __imp_?enableSeparateBindingLocations@GlslResourceBindingContext@MaterialX_v1_39_2@@QEAAX_N@Z __imp_?generate@GlslShaderGenerator@MaterialX_v1_39_2@@UEBA?AV?$shared_ptr@VShader@MaterialX_v1_39_2@@@std@@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@4@V?$shared_ptr@VElement@MaterialX_v1_39_2@@@4@AEAVGenContext@2@@Z __imp_?getConstantQualifier@GlslSyntax@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ __imp_?getImplementation@GlslShaderGenerator@MaterialX_v1_39_2@@UEBA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@AEBVNodeDef@2@AEAVGenContext@2@@Z __imp_?getInputQualifier@GlslSyntax@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ __imp_?getInputQualifier@VkSyntax@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ __imp_?getOutputQualifier@GlslSyntax@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ __imp_?getResourceBindingContext@EsslShaderGenerator@MaterialX_v1_39_2@@MEBA?AV?$shared_ptr@VHwResourceBindingContext@MaterialX_v1_39_2@@@std@@AEAVGenContext@2@@Z __imp_?getResourceBindingContext@GlslShaderGenerator@MaterialX_v1_39_2@@MEBA?AV?$shared_ptr@VHwResourceBindingContext@MaterialX_v1_39_2@@@std@@AEAVGenContext@2@@Z __imp_?getResourceBindingContext@VkShaderGenerator@MaterialX_v1_39_2@@MEBA?AV?$shared_ptr@VHwResourceBindingContext@MaterialX_v1_39_2@@@std@@AEAVGenContext@2@@Z __imp_?getSourceFileExtension@GlslSyntax@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ __imp_?getTarget@EsslShaderGenerator@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ __imp_?getTarget@GlslImplementation@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ __imp_?getTarget@GlslShaderGenerator@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ __imp_?getTarget@HeightToNormalNodeGlsl@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ __imp_?getTarget@LightCompoundNodeGlsl@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ __imp_?getTarget@LightShaderNodeGlsl@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ __imp_?getTarget@VkShaderGenerator@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ __imp_?getUniformQualifier@GlslSyntax@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ __imp_?getValue@GlslStructTypeSyntax@MaterialX_v1_39_2@@UEBA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEBVValue@2@_N@Z __imp_?getVersion@EsslShaderGenerator@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ __imp_?getVersion@GlslShaderGenerator@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ __imp_?getVersion@VkShaderGenerator@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ __imp_?getVertexDataPrefix@EsslShaderGenerator@MaterialX_v1_39_2@@UEBA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEBVVariableBlock@2@@Z __imp_?getVertexDataPrefix@GlslShaderGenerator@MaterialX_v1_39_2@@UEBA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEBVVariableBlock@2@@Z __imp_?getVertexDataPrefix@VkShaderGenerator@MaterialX_v1_39_2@@UEBA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEBVVariableBlock@2@@Z __imp_?initialize@GlslResourceBindingContext@MaterialX_v1_39_2@@UEAAXXZ __imp_?initialize@LightCompoundNodeGlsl@MaterialX_v1_39_2@@UEAAXAEBVInterfaceElement@2@AEAVGenContext@2@@Z __imp_?initialize@LightShaderNodeGlsl@MaterialX_v1_39_2@@UEAAXAEBVInterfaceElement@2@AEAVGenContext@2@@Z __imp_?initialize@VkResourceBindingContext@MaterialX_v1_39_2@@UEAAXXZ __imp_?isEditable@GeomPropValueNodeGlsl@MaterialX_v1_39_2@@UEBA_NAEBVShaderInput@2@@Z __imp_?remapEnumeration@GlslSyntax@MaterialX_v1_39_2@@UEBA_NAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@VTypeDesc@2@0AEAU?$pair@VTypeDesc@MaterialX_v1_39_2@@V?$shared_ptr@VValue@MaterialX_v1_39_2@@@std@@@4@@Z __imp_?requiresLighting@GlslShaderGenerator@MaterialX_v1_39_2@@MEBA_NAEBVShaderGraph@2@@Z __imp_?toVec4@GlslShaderGenerator@MaterialX_v1_39_2@@KAXPEBVTypeDesc@2@AEAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z __imp_?toVec4@GlslShaderGenerator@MaterialX_v1_39_2@@KAXVTypeDesc@2@AEAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z __imp_?typeSupported@GlslSyntax@MaterialX_v1_39_2@@UEBA_NPEBVTypeDesc@2@@Z MaterialXGenGlsl_d_NULL_THUNK_DATA 
//              -1                      0       23        `
MaterialXGenGlsl_d.dll 
/0              -1                      0       538       `
d� �e�"         .debug$S        L   �               @ B.idata$2           �   �          @ 0�.idata$6           
  �           @  �    	     MaterialXGenGlsl_d.dll'    �         膗Microsoft (R) LINK                                          MaterialXGenGlsl_d.dll  @comp.id膗��                  .idata$2@  �   h .idata$6        .idata$4@  �    h .idata$5@  �    h     +                D            h   __IMPORT_DESCRIPTOR_MaterialXGenGlsl_d __NULL_IMPORT_DESCRIPTOR MaterialXGenGlsl_d_NULL_THUNK_DATA /0              -1                      0       261       `
d� 唟揀�          .debug$S        L   d               @ B.idata$3           �               @ 0�    	     MaterialXGenGlsl_d.dll'    �         膗Microsoft (R) LINK                    @comp.id膗��                     __NULL_IMPORT_DESCRIPTOR 
/0              -1                      0       308       `
d� � y阻          .debug$S        L   �               @ B.idata$5           �               @ @�.idata$4           �               @ @�    	     MaterialXGenGlsl_d.dll'    �         膗Microsoft (R) LINK                @comp.id膗��                  (   MaterialXGenGlsl_d_NULL_THUNK_DATA /0              -1                      0       95        `
  ��  d��>隟      ??0BlurNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z MaterialXGenGlsl_d.dll 
/0              -1                      0       93        `
  ��  d唸W溒I     ??0BlurNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z MaterialXGenGlsl_d.dll 
/0              -1                      0       86        `
  ��  d嘄s畸B     ??0BlurNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ MaterialXGenGlsl_d.dll /0              -1                      0       102       `
  ��  d唜�	鸕     ??0EsslShaderGenerator@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z MaterialXGenGlsl_d.dll /0              -1                      0       100       `
  ��  d唋�:齈     ??0EsslShaderGenerator@MaterialX_v1_39_2@@QEAA@AEBV01@@Z MaterialXGenGlsl_d.dll /0              -1                      0       93        `
  ��  d唘鷊鍵     ??0EsslShaderGenerator@MaterialX_v1_39_2@@QEAA@XZ MaterialXGenGlsl_d.dll 
/0              -1                      0       93        `
  ��  d�+祺I     ??0EsslSyntax@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z MaterialXGenGlsl_d.dll 
/0              -1                      0       91        `
  ��  d�蔊     ??0EsslSyntax@MaterialX_v1_39_2@@QEAA@AEBV01@@Z MaterialXGenGlsl_d.dll 
/0              -1                      0       84        `
  ��  d哣崋蹳     ??0EsslSyntax@MaterialX_v1_39_2@@QEAA@XZ MaterialXGenGlsl_d.dll /0              -1                      0       100       `
  ��  d啔�罰   	  ??0GeomColorNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z MaterialXGenGlsl_d.dll /0              -1                      0       98        `
  ��  d嗈J蹶N   
  ??0GeomColorNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z MaterialXGenGlsl_d.dll /0              -1                      0       91        `
  ��  d啍g 蜧     ??0GeomColorNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ MaterialXGenGlsl_d.dll 
/0              -1                      0       104       `
  ��  d唙B[譚     ??0GeomPropValueNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z MaterialXGenGlsl_d.dll /0              -1                      0       102       `
  ��  d啞杗齊   
  ??0GeomPropValueNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z MaterialXGenGlsl_d.dll /0              -1                      0       95        `
  ��  d哘嗀罧     ??0GeomPropValueNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ MaterialXGenGlsl_d.dll 
/0              -1                      0       113       `
  ��  d��	臸     ??0GeomPropValueNodeGlslAsUniform@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z MaterialXGenGlsl_d.dll 
/0              -1                      0       111       `
  ��  d�v繹     ??0GeomPropValueNodeGlslAsUniform@MaterialX_v1_39_2@@QEAA@AEBV01@@Z MaterialXGenGlsl_d.dll 
/0              -1                      0       104       `
  ��  d啈祚鬞     ??0GeomPropValueNodeGlslAsUniform@MaterialX_v1_39_2@@QEAA@XZ MaterialXGenGlsl_d.dll /0              -1                      0       92        `
  ��  d啇节臜     ??0GlslImplementation@MaterialX_v1_39_2@@IEAA@XZ MaterialXGenGlsl_d.dll /0              -1                      0       101       `
  ��  d�5码Q     ??0GlslImplementation@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z MaterialXGenGlsl_d.dll 
/0              -1                      0       99        `
  ��  d嘄瞂芆     ??0GlslImplementation@MaterialX_v1_39_2@@QEAA@AEBV01@@Z MaterialXGenGlsl_d.dll 
/0              -1                      0       109       `
  ��  d��(鬥     ??0GlslResourceBindingContext@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z MaterialXGenGlsl_d.dll 
/0              -1                      0       107       `
  ��  d�1責黈     ??0GlslResourceBindingContext@MaterialX_v1_39_2@@QEAA@AEBV01@@Z MaterialXGenGlsl_d.dll 
/0              -1                      0       103       `
  ��  d喛阳S     ??0GlslResourceBindingContext@MaterialX_v1_39_2@@QEAA@_K0@Z MaterialXGenGlsl_d.dll 
/0              -1                      0       102       `
  ��  d啳�#蠷     ??0GlslShaderGenerator@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z MaterialXGenGlsl_d.dll /0              -1                      0       100       `
  ��  d唘�=     ??0GlslShaderGenerator@MaterialX_v1_39_2@@QEAA@AEBV01@@Z MaterialXGenGlsl_d.dll /0              -1                      0       93        `
  ��  d嗎ud釯     ??0GlslShaderGenerator@MaterialX_v1_39_2@@QEAA@XZ MaterialXGenGlsl_d.dll 
/0              -1                      0       103       `
  ��  d�铌S     ??0GlslStructTypeSyntax@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z MaterialXGenGlsl_d.dll 
/0              -1                      0       101       `
  ��  d�5篭镼     ??0GlslStructTypeSyntax@MaterialX_v1_39_2@@QEAA@AEBV01@@Z MaterialXGenGlsl_d.dll 
/0              -1                      0       332       `
  ��  d啇
I�8    ??0GlslStructTypeSyntax@MaterialX_v1_39_2@@QEAA@PEBVSyntax@1@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@1111AEBV?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@4@@Z MaterialXGenGlsl_d.dll /0              -1                      0       93        `
  ��  d喲�9�I     ??0GlslSyntax@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z MaterialXGenGlsl_d.dll 
/0              -1                      0       91        `
  ��  d喨E梯G     ??0GlslSyntax@MaterialX_v1_39_2@@QEAA@AEBV01@@Z MaterialXGenGlsl_d.dll 
/0              -1                      0       84        `
  ��  d� 愁@      ??0GlslSyntax@MaterialX_v1_39_2@@QEAA@XZ MaterialXGenGlsl_d.dll /0              -1                      0       105       `
  ��  d�遀   !  ??0HeightToNormalNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z MaterialXGenGlsl_d.dll 
/0              -1                      0       103       `
  ��  d�S[赟   "  ??0HeightToNormalNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z MaterialXGenGlsl_d.dll 
/0              -1                      0       96        `
  ��  d�傆驦   #  ??0HeightToNormalNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ MaterialXGenGlsl_d.dll /0              -1                      0       104       `
  ��  d唂畘薚   $  ??0LightCompoundNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z MaterialXGenGlsl_d.dll /0              -1                      0       102       `
  ��  d嗻K滧R   %  ??0LightCompoundNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z MaterialXGenGlsl_d.dll /0              -1                      0       95        `
  ��  d�9|@螷   &  ??0LightCompoundNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ MaterialXGenGlsl_d.dll 
/0              -1                      0       96        `
  ��  d�3仇腖   '  ??0LightNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z MaterialXGenGlsl_d.dll /0              -1                      0       94        `
  ��  d�6ov蘆   (  ??0LightNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z MaterialXGenGlsl_d.dll /0              -1                      0       87        `
  ��  d啓qZ薈   )  ??0LightNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ MaterialXGenGlsl_d.dll 
/0              -1                      0       103       `
  ��  d哹溛S   *  ??0LightSamplerNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z MaterialXGenGlsl_d.dll 
/0              -1                      0       101       `
  ��  d唞绾颭   +  ??0LightSamplerNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z MaterialXGenGlsl_d.dll 
/0              -1                      0       94        `
  ��  d喼悚螶   ,  ??0LightSamplerNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ MaterialXGenGlsl_d.dll /0              -1                      0       102       `
  ��  d唹眫鳵   -  ??0LightShaderNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z MaterialXGenGlsl_d.dll /0              -1                      0       100       `
  ��  d啿Q涋P   .  ??0LightShaderNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z MaterialXGenGlsl_d.dll /0              -1                      0       93        `
  ��  d咮謾镮   /  ??0LightShaderNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ MaterialXGenGlsl_d.dll 
/0              -1                      0       100       `
  ��  d啹�#鍼   0  ??0NumLightsNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z MaterialXGenGlsl_d.dll /0              -1                      0       98        `
  ��  d哶狖N   1  ??0NumLightsNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z MaterialXGenGlsl_d.dll /0              -1                      0       91        `
  ��  d嗩鮡腉   2  ??0NumLightsNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ MaterialXGenGlsl_d.dll 
/0              -1                      0       98        `
  ��  d�!J借N   3  ??0SurfaceNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z MaterialXGenGlsl_d.dll /0              -1                      0       96        `
  ��  d咷帱L   4  ??0SurfaceNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z MaterialXGenGlsl_d.dll /0              -1                      0       89        `
  ��  d啅F5軪   5  ??0SurfaceNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ MaterialXGenGlsl_d.dll 
/0              -1                      0       103       `
  ��  d啂奙萐   6  ??0UnlitSurfaceNodeGlsl@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z MaterialXGenGlsl_d.dll 
/0              -1                      0       101       `
  ��  d�T迈Q   7  ??0UnlitSurfaceNodeGlsl@MaterialX_v1_39_2@@QEAA@AEBV01@@Z MaterialXGenGlsl_d.dll 
/0              -1                      0       94        `
  ��  d唵IQ艼   8  ??0UnlitSurfaceNodeGlsl@MaterialX_v1_39_2@@QEAA@XZ MaterialXGenGlsl_d.dll /0              -1                      0       107       `
  ��  d單zH鉝   9  ??0VkResourceBindingContext@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z MaterialXGenGlsl_d.dll 
/0              -1                      0       105       `
  ��  d��肬   :  ??0VkResourceBindingContext@MaterialX_v1_39_2@@QEAA@AEBV01@@Z MaterialXGenGlsl_d.dll 
/0              -1                      0       100       `
  ��  d喬l{轕   ;  ??0VkResourceBindingContext@MaterialX_v1_39_2@@QEAA@_K@Z MaterialXGenGlsl_d.dll /0              -1                      0       100       `
  ��  d咢#關   <  ??0VkShaderGenerator@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z MaterialXGenGlsl_d.dll /0              -1                      0       98        `
  ��  d啎3s袾   =  ??0VkShaderGenerator@MaterialX_v1_39_2@@QEAA@AEBV01@@Z MaterialXGenGlsl_d.dll /0              -1                      0       91        `
  ��  d喕5 蔊   >  ??0VkShaderGenerator@MaterialX_v1_39_2@@QEAA@XZ MaterialXGenGlsl_d.dll 
/0              -1                      0       91        `
  ��  d�*篽�G   ?  ??0VkSyntax@MaterialX_v1_39_2@@QEAA@$$QEAV01@@Z MaterialXGenGlsl_d.dll 
/0              -1                      0       89        `
  ��  d唂�诵E   @  ??0VkSyntax@MaterialX_v1_39_2@@QEAA@AEBV01@@Z MaterialXGenGlsl_d.dll 
/0              -1                      0       82        `
  ��  d唊6Y�>   A  ??0VkSyntax@MaterialX_v1_39_2@@QEAA@XZ MaterialXGenGlsl_d.dll /0              -1                      0       86        `
  ��  d嗻翨   B  ??1BlurNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ MaterialXGenGlsl_d.dll /0              -1                      0       93        `
  ��  d唕9]襂   C  ??1EsslShaderGenerator@MaterialX_v1_39_2@@UEAA@XZ MaterialXGenGlsl_d.dll 
/0              -1                      0       84        `
  ��  d嗀�2贎   D  ??1EsslSyntax@MaterialX_v1_39_2@@UEAA@XZ MaterialXGenGlsl_d.dll /0              -1                      0       91        `
  ��  d啌餦銰   E  ??1GeomColorNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ MaterialXGenGlsl_d.dll 
/0              -1                      0       95        `
  ��  d嗁�(襅   F  ??1GeomPropValueNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ MaterialXGenGlsl_d.dll 
/0              -1                      0       104       `
  ��  d啞�轙   G  ??1GeomPropValueNodeGlslAsUniform@MaterialX_v1_39_2@@UEAA@XZ MaterialXGenGlsl_d.dll /0              -1                      0       92        `
  ��  d�煒蔋   H  ??1GlslImplementation@MaterialX_v1_39_2@@UEAA@XZ MaterialXGenGlsl_d.dll /0              -1                      0       100       `
  ��  d嗴綱鵓   I  ??1GlslResourceBindingContext@MaterialX_v1_39_2@@UEAA@XZ MaterialXGenGlsl_d.dll /0              -1                      0       93        `
  ��  d�?wJ贗   J  ??1GlslShaderGenerator@MaterialX_v1_39_2@@UEAA@XZ MaterialXGenGlsl_d.dll 
/0              -1                      0       94        `
  ��  d営~M翵   K  ??1GlslStructTypeSyntax@MaterialX_v1_39_2@@UEAA@XZ MaterialXGenGlsl_d.dll /0              -1                      0       84        `
  ��  d啢(6兀   L  ??1GlslSyntax@MaterialX_v1_39_2@@UEAA@XZ MaterialXGenGlsl_d.dll /0              -1                      0       96        `
  ��  d嗢z险L   M  ??1HeightToNormalNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ MaterialXGenGlsl_d.dll /0              -1                      0       95        `
  ��  d唵1佔K   N  ??1LightCompoundNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ MaterialXGenGlsl_d.dll 
/0              -1                      0       87        `
  ��  d�O陶C   O  ??1LightNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ MaterialXGenGlsl_d.dll 
/0              -1                      0       94        `
  ��  d唦]啬J   P  ??1LightSamplerNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ MaterialXGenGlsl_d.dll /0              -1                      0       93        `
  ��  d哅�蒊   Q  ??1LightShaderNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ MaterialXGenGlsl_d.dll 
/0              -1                      0       91        `
  ��  d嗚烟G   R  ??1NumLightsNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ MaterialXGenGlsl_d.dll 
/0              -1                      0       89        `
  ��  d�浺E   S  ??1SurfaceNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ MaterialXGenGlsl_d.dll 
/0              -1                      0       94        `
  ��  d喺�袹   T  ??1UnlitSurfaceNodeGlsl@MaterialX_v1_39_2@@UEAA@XZ MaterialXGenGlsl_d.dll /0              -1                      0       98        `
  ��  d哘キ餘   U  ??1VkResourceBindingContext@MaterialX_v1_39_2@@UEAA@XZ MaterialXGenGlsl_d.dll /0              -1                      0       91        `
  ��  d喨gB諫   V  ??1VkShaderGenerator@MaterialX_v1_39_2@@UEAA@XZ MaterialXGenGlsl_d.dll 
/0              -1                      0       82        `
  ��  d嗼� �>   W  ??1VkSyntax@MaterialX_v1_39_2@@UEAA@XZ MaterialXGenGlsl_d.dll /0              -1                      0       101       `
  ��  d嗗=}薗   X  ??4BlurNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z MaterialXGenGlsl_d.dll 
/0              -1                      0       99        `
  ��  d唀 缤O   Y  ??4BlurNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z MaterialXGenGlsl_d.dll 
/0              -1                      0       99        `
  ��  d�=欽鏞   Z  ??4EsslSyntax@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z MaterialXGenGlsl_d.dll 
/0              -1                      0       97        `
  ��  d�?8鱉   [  ??4EsslSyntax@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z MaterialXGenGlsl_d.dll 
/0              -1                      0       106       `
  ��  d唈Q蝮V   \  ??4GeomColorNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z MaterialXGenGlsl_d.dll /0              -1                      0       104       `
  ��  d咲肊   ]  ??4GeomColorNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z MaterialXGenGlsl_d.dll /0              -1                      0       110       `
  ��  d�7轟   ^  ??4GeomPropValueNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z MaterialXGenGlsl_d.dll /0              -1                      0       108       `
  ��  d喍靀   _  ??4GeomPropValueNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z MaterialXGenGlsl_d.dll /0              -1                      0       119       `
  ��  d唎玠錭   `  ??4GeomPropValueNodeGlslAsUniform@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z MaterialXGenGlsl_d.dll 
/0              -1                      0       117       `
  ��  d哯Za   a  ??4GeomPropValueNodeGlslAsUniform@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z MaterialXGenGlsl_d.dll 
/0              -1                      0       107       `
  ��  d�闣   b  ??4GlslImplementation@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z MaterialXGenGlsl_d.dll 
/0              -1                      0       105       `
  ��  d啈�)蒛   c  ??4GlslImplementation@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z MaterialXGenGlsl_d.dll 
/0              -1                      0       115       `
  ��  d喺A溤_   d  ??4GlslResourceBindingContext@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z MaterialXGenGlsl_d.dll 
/0              -1                      0       113       `
  ��  d�磈黓   e  ??4GlslResourceBindingContext@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z MaterialXGenGlsl_d.dll 
/0              -1                      0       109       `
  ��  d啚 器Y   f  ??4GlslStructTypeSyntax@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z MaterialXGenGlsl_d.dll 
/0              -1                      0       107       `
  ��  d唌�蚖   g  ??4GlslStructTypeSyntax@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z MaterialXGenGlsl_d.dll 
/0              -1                      0       99        `
  ��  d哊WO   h  ??4GlslSyntax@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z MaterialXGenGlsl_d.dll 
/0              -1                      0       97        `
  ��  d啌xG釳   i  ??4GlslSyntax@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z MaterialXGenGlsl_d.dll 
/0              -1                      0       111       `
  ��  d�=bU羀   j  ??4HeightToNormalNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z MaterialXGenGlsl_d.dll 
/0              -1                      0       109       `
  ��  d喛8砣Y   k  ??4HeightToNormalNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z MaterialXGenGlsl_d.dll 
/0              -1                      0       110       `
  ��  d� 蔧耑   l  ??4LightCompoundNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z MaterialXGenGlsl_d.dll /0              -1                      0       108       `
  ��  d唓+%鮔   m  ??4LightCompoundNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z MaterialXGenGlsl_d.dll /0              -1                      0       109       `
  ��  d�68楇Y   n  ??4LightSamplerNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z MaterialXGenGlsl_d.dll 
/0              -1                      0       107       `
  ��  d哶/苏W   o  ??4LightSamplerNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z MaterialXGenGlsl_d.dll 
/0              -1                      0       108       `
  ��  d喥桂蒟   p  ??4LightShaderNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z MaterialXGenGlsl_d.dll /0              -1                      0       106       `
  ��  d喚uV   q  ??4LightShaderNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z MaterialXGenGlsl_d.dll /0              -1                      0       106       `
  ��  d�/AV   r  ??4NumLightsNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z MaterialXGenGlsl_d.dll /0              -1                      0       104       `
  ��  d哖攕腡   s  ??4NumLightsNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z MaterialXGenGlsl_d.dll /0              -1                      0       109       `
  ��  d�&ym鉟   t  ??4UnlitSurfaceNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z MaterialXGenGlsl_d.dll 
/0              -1                      0       107       `
  ��  d喅�鏦   u  ??4UnlitSurfaceNodeGlsl@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z MaterialXGenGlsl_d.dll 
/0              -1                      0       113       `
  ��  d喡�]   v  ??4VkResourceBindingContext@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z MaterialXGenGlsl_d.dll 
/0              -1                      0       111       `
  ��  d唎酸繹   w  ??4VkResourceBindingContext@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z MaterialXGenGlsl_d.dll 
/0              -1                      0       97        `
  ��  d啓k撟M   x  ??4VkSyntax@MaterialX_v1_39_2@@QEAAAEAV01@$$QEAV01@@Z MaterialXGenGlsl_d.dll 
/0              -1                      0       95        `
  ��  d�'i\轐   y  ??4VkSyntax@MaterialX_v1_39_2@@QEAAAEAV01@AEBV01@@Z MaterialXGenGlsl_d.dll 
/0              -1                      0       83        `
  ��  d哃�&�?   z  ??_7BlurNodeGlsl@MaterialX_v1_39_2@@6B@ MaterialXGenGlsl_d.dll 
/0              -1                      0       90        `
  ��  d��錐   {  ??_7EsslShaderGenerator@MaterialX_v1_39_2@@6B@ MaterialXGenGlsl_d.dll /0              -1                      0       81        `
  ��  d嗊a=   |  ??_7EsslSyntax@MaterialX_v1_39_2@@6B@ MaterialXGenGlsl_d.dll 
/0              -1                      0       88        `
  ��  d唗椣D   }  ??_7GeomColorNodeGlsl@MaterialX_v1_39_2@@6B@ MaterialXGenGlsl_d.dll /0              -1                      0       92        `
  ��  d啯5H   ~  ??_7GeomPropValueNodeGlsl@MaterialX_v1_39_2@@6B@ MaterialXGenGlsl_d.dll /0              -1                      0       101       `
  ��  d唅\I鵔     ??_7GeomPropValueNodeGlslAsUniform@MaterialX_v1_39_2@@6B@ MaterialXGenGlsl_d.dll 
/0              -1                      0       89        `
  ��  d�
跲褽   �  ??_7GlslImplementation@MaterialX_v1_39_2@@6B@ MaterialXGenGlsl_d.dll 
/0              -1                      0       97        `
  ��  d嗿ぴM   �  ??_7GlslResourceBindingContext@MaterialX_v1_39_2@@6B@ MaterialXGenGlsl_d.dll 
/0              -1                      0       90        `
  ��  d唌0麱   �  ??_7GlslShaderGenerator@MaterialX_v1_39_2@@6B@ MaterialXGenGlsl_d.dll /0              -1                      0       91        `
  ��  d�9�+軬   �  ??_7GlslStructTypeSyntax@MaterialX_v1_39_2@@6B@ MaterialXGenGlsl_d.dll 
/0              -1                      0       81        `
  ��  d哱紜�=   �  ??_7GlslSyntax@MaterialX_v1_39_2@@6B@ MaterialXGenGlsl_d.dll 
/0              -1                      0       93        `
  ��  d咼傲銲   �  ??_7HeightToNormalNodeGlsl@MaterialX_v1_39_2@@6B@ MaterialXGenGlsl_d.dll 
/0              -1                      0       92        `
  ��  d喅犰鍴   �  ??_7LightCompoundNodeGlsl@MaterialX_v1_39_2@@6B@ MaterialXGenGlsl_d.dll /0              -1                      0       84        `
  ��  d�祉@   �  ??_7LightNodeGlsl@MaterialX_v1_39_2@@6B@ MaterialXGenGlsl_d.dll /0              -1                      0       91        `
  ��  d唲I嘿G   �  ??_7LightSamplerNodeGlsl@MaterialX_v1_39_2@@6B@ MaterialXGenGlsl_d.dll 
/0              -1                      0       90        `
  ��  d嗿硰颋   �  ??_7LightShaderNodeGlsl@MaterialX_v1_39_2@@6B@ MaterialXGenGlsl_d.dll /0              -1                      0       88        `
  ��  d啳�9蜠   �  ??_7NumLightsNodeGlsl@MaterialX_v1_39_2@@6B@ MaterialXGenGlsl_d.dll /0              -1                      0       86        `
  ��  d�!习魾   �  ??_7SurfaceNodeGlsl@MaterialX_v1_39_2@@6B@ MaterialXGenGlsl_d.dll /0              -1                      0       91        `
  ��  d嗗迸螱   �  ??_7UnlitSurfaceNodeGlsl@MaterialX_v1_39_2@@6B@ MaterialXGenGlsl_d.dll 
/0              -1                      0       95        `
  ��  d�	z忥K   �  ??_7VkResourceBindingContext@MaterialX_v1_39_2@@6B@ MaterialXGenGlsl_d.dll 
/0              -1                      0       88        `
  ��  d唹鞁藾   �  ??_7VkShaderGenerator@MaterialX_v1_39_2@@6B@ MaterialXGenGlsl_d.dll /0              -1                      0       79        `
  ��  d�i肛;   �  ??_7VkSyntax@MaterialX_v1_39_2@@6B@ MaterialXGenGlsl_d.dll 
/0              -1                      0       158       `
  ��  d哾��   �  ?CONSTANT_QUALIFIER@GlslSyntax@MaterialX_v1_39_2@@2V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@B MaterialXGenGlsl_d.dll /0              -1                      0       154       `
  ��  d�竔茊   �  ?FLAT_QUALIFIER@GlslSyntax@MaterialX_v1_39_2@@2V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@B MaterialXGenGlsl_d.dll /0              -1                      0       155       `
  ��  d唡鮎藝   �  ?INPUT_QUALIFIER@GlslSyntax@MaterialX_v1_39_2@@2V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@B MaterialXGenGlsl_d.dll 
/0              -1                      0       156       `
  ��  d� 覙貓   �  ?OUTPUT_QUALIFIER@GlslSyntax@MaterialX_v1_39_2@@2V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@B MaterialXGenGlsl_d.dll /0              -1                      0       161       `
  ��  d唅q>   �  ?SOURCE_FILE_EXTENSION@GlslSyntax@MaterialX_v1_39_2@@2V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@B MaterialXGenGlsl_d.dll 
/0              -1                      0       155       `
  ��  d喦Q鰢   �  ?TARGET@EsslShaderGenerator@MaterialX_v1_39_2@@2V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@B MaterialXGenGlsl_d.dll 
/0              -1                      0       155       `
  ��  d�6圢車   �  ?TARGET@GlslShaderGenerator@MaterialX_v1_39_2@@2V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@B MaterialXGenGlsl_d.dll 
/0              -1                      0       153       `
  ��  d�$"顓   �  ?TARGET@VkShaderGenerator@MaterialX_v1_39_2@@2V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@B MaterialXGenGlsl_d.dll 
/0              -1                      0       157       `
  ��  d哘x雎�   �  ?UNIFORM_QUALIFIER@GlslSyntax@MaterialX_v1_39_2@@2V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@B MaterialXGenGlsl_d.dll 
/0              -1                      0       246       `
  ��  d啀钾筲   �  ?VEC2_MEMBERS@GlslSyntax@MaterialX_v1_39_2@@2V?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@B MaterialXGenGlsl_d.dll /0              -1                      0       246       `
  ��  d嗧SQ钼   �  ?VEC3_MEMBERS@GlslSyntax@MaterialX_v1_39_2@@2V?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@B MaterialXGenGlsl_d.dll /0              -1                      0       246       `
  ��  d�(鷥逾   �  ?VEC4_MEMBERS@GlslSyntax@MaterialX_v1_39_2@@2V?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@B MaterialXGenGlsl_d.dll /0              -1                      0       156       `
  ��  d唃薴鯃   �  ?VERSION@EsslShaderGenerator@MaterialX_v1_39_2@@2V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@B MaterialXGenGlsl_d.dll /0              -1                      0       156       `
  ��  d啌埽鯃   �  ?VERSION@GlslShaderGenerator@MaterialX_v1_39_2@@2V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@B MaterialXGenGlsl_d.dll /0              -1                      0       154       `
  ��  d唹(缲�   �  ?VERSION@VkShaderGenerator@MaterialX_v1_39_2@@2V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@B MaterialXGenGlsl_d.dll /0              -1                      0       124       `
  ��  d喦2氉h   �  ?acceptsInputType@HeightToNormalNodeGlsl@MaterialX_v1_39_2@@MEBA_NVTypeDesc@2@@Z MaterialXGenGlsl_d.dll /0              -1                      0       344       `
  ��  d�,�顳  �  ?computeSampleOffsetStrings@HeightToNormalNodeGlsl@MaterialX_v1_39_2@@MEBAXAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@0IAEAV?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@4@@Z MaterialXGenGlsl_d.dll /0              -1                      0       145       `
  ��  d�;_掄}   �  ?create@BlurNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ MaterialXGenGlsl_d.dll 
/0              -1                      0       153       `
  ��  d嘃�&蛥   �  ?create@EsslShaderGenerator@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderGenerator@MaterialX_v1_39_2@@@std@@XZ MaterialXGenGlsl_d.dll 
/0              -1                      0       135       `
  ��  d咮L黼s   �  ?create@EsslSyntax@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VSyntax@MaterialX_v1_39_2@@@std@@XZ MaterialXGenGlsl_d.dll 
/0              -1                      0       150       `
  ��  d喅}i   �  ?create@GeomColorNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ MaterialXGenGlsl_d.dll /0              -1                      0       154       `
  ��  d啰�鄦   �  ?create@GeomPropValueNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ MaterialXGenGlsl_d.dll /0              -1                      0       163       `
  ��  d嗻w礓�   �  ?create@GeomPropValueNodeGlslAsUniform@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ MaterialXGenGlsl_d.dll 
/0              -1                      0       174       `
  ��  d�U疚�   �  ?create@GlslResourceBindingContext@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VGlslResourceBindingContext@MaterialX_v1_39_2@@@std@@_K0@Z MaterialXGenGlsl_d.dll /0              -1                      0       153       `
  ��  d唺痒鴧   �  ?create@GlslShaderGenerator@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderGenerator@MaterialX_v1_39_2@@@std@@XZ MaterialXGenGlsl_d.dll 
/0              -1                      0       135       `
  ��  d啠匞阺   �  ?create@GlslSyntax@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VSyntax@MaterialX_v1_39_2@@@std@@XZ MaterialXGenGlsl_d.dll 
/0              -1                      0       155       `
  ��  d唀黰讎   �  ?create@HeightToNormalNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ MaterialXGenGlsl_d.dll 
/0              -1                      0       154       `
  ��  d�9 鎲   �  ?create@LightCompoundNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ MaterialXGenGlsl_d.dll /0              -1                      0       146       `
  ��  d啨樥藒   �  ?create@LightNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ MaterialXGenGlsl_d.dll /0              -1                      0       153       `
  ��  d啂囤�   �  ?create@LightSamplerNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ MaterialXGenGlsl_d.dll 
/0              -1                      0       152       `
  ��  d唥鼩邉   �  ?create@LightShaderNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ MaterialXGenGlsl_d.dll /0              -1                      0       150       `
  ��  d啴*'饌   �  ?create@NumLightsNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ MaterialXGenGlsl_d.dll /0              -1                      0       148       `
  ��  d唌;堼�   �  ?create@SurfaceNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ MaterialXGenGlsl_d.dll /0              -1                      0       153       `
  ��  d唹*再�   �  ?create@UnlitSurfaceNodeGlsl@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@XZ MaterialXGenGlsl_d.dll 
/0              -1                      0       169       `
  ��  d喌绲褧   �  ?create@VkResourceBindingContext@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VVkResourceBindingContext@MaterialX_v1_39_2@@@std@@_K@Z MaterialXGenGlsl_d.dll 
/0              -1                      0       151       `
  ��  d喩月鎯   �  ?create@VkShaderGenerator@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VShaderGenerator@MaterialX_v1_39_2@@@std@@XZ MaterialXGenGlsl_d.dll 
/0              -1                      0       133       `
  ��  d啴讫q   �  ?create@VkSyntax@MaterialX_v1_39_2@@SA?AV?$shared_ptr@VSyntax@MaterialX_v1_39_2@@@std@@XZ MaterialXGenGlsl_d.dll 
/0              -1                      0       225       `
  ��  d�*鉦鐾   �  ?createStructSyntax@GlslSyntax@MaterialX_v1_39_2@@MEBA?AV?$shared_ptr@VStructTypeSyntax@MaterialX_v1_39_2@@@std@@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@4@0000@Z MaterialXGenGlsl_d.dll 
/0              -1                      0       152       `
  ��  d唨砪韯   �  ?createVariables@GeomColorNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShader@2@@Z MaterialXGenGlsl_d.dll /0              -1                      0       156       `
  ��  d喆}靾   �  ?createVariables@GeomPropValueNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShader@2@@Z MaterialXGenGlsl_d.dll /0              -1                      0       165       `
  ��  d咶~'軕   �  ?createVariables@GeomPropValueNodeGlslAsUniform@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShader@2@@Z MaterialXGenGlsl_d.dll 
/0              -1                      0       157       `
  ��  d咾堳顗   �  ?createVariables@HeightToNormalNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShader@2@@Z MaterialXGenGlsl_d.dll 
/0              -1                      0       156       `
  ��  d�萩銏   �  ?createVariables@LightCompoundNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShader@2@@Z MaterialXGenGlsl_d.dll /0              -1                      0       148       `
  ��  d啍泆詟   �  ?createVariables@LightNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShader@2@@Z MaterialXGenGlsl_d.dll /0              -1                      0       154       `
  ��  d嗘辊�   �  ?createVariables@LightShaderNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShader@2@@Z MaterialXGenGlsl_d.dll /0              -1                      0       152       `
  ��  d啚舊鋭   �  ?createVariables@NumLightsNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShader@2@@Z MaterialXGenGlsl_d.dll /0              -1                      0       150       `
  ��  d�粠韨   �  ?createVariables@SurfaceNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShader@2@@Z MaterialXGenGlsl_d.dll /0              -1                      0       140       `
  ��  d�櫞蝬   �  ?emitConstants@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z MaterialXGenGlsl_d.dll /0              -1                      0       141       `
  ��  d唨?衴   �  ?emitDirectives@EsslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z MaterialXGenGlsl_d.dll 
/0              -1                      0       148       `
  ��  d�,&櫭�   �  ?emitDirectives@GlslResourceBindingContext@MaterialX_v1_39_2@@UEAAXAEAVGenContext@2@AEAVShaderStage@2@@Z MaterialXGenGlsl_d.dll /0              -1                      0       141       `
  ��  d哻�*鋣   �  ?emitDirectives@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z MaterialXGenGlsl_d.dll 
/0              -1                      0       146       `
  ��  d啽鬚雫   �  ?emitDirectives@VkResourceBindingContext@MaterialX_v1_39_2@@UEAAXAEAVGenContext@2@AEAVShaderStage@2@@Z MaterialXGenGlsl_d.dll /0              -1                      0       139       `
  ��  d咵育饂   �  ?emitDirectives@VkShaderGenerator@MaterialX_v1_39_2@@UEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z MaterialXGenGlsl_d.dll 
/0              -1                      0       158       `
  ��  d�,譓詩   �  ?emitFunctionCall@GeomColorNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z MaterialXGenGlsl_d.dll /0              -1                      0       162       `
  ��  d�髢蹘   �  ?emitFunctionCall@GeomPropValueNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z MaterialXGenGlsl_d.dll /0              -1                      0       171       `
  ��  d啔輋闂   �  ?emitFunctionCall@GeomPropValueNodeGlslAsUniform@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z MaterialXGenGlsl_d.dll 
/0              -1                      0       163       `
  ��  d嗙b滏�   �  ?emitFunctionCall@HeightToNormalNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z MaterialXGenGlsl_d.dll 
/0              -1                      0       162       `
  ��  d哾嬋�   �  ?emitFunctionCall@LightCompoundNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z MaterialXGenGlsl_d.dll /0              -1                      0       154       `
  ��  d嗰蝑釂   �  ?emitFunctionCall@LightNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z MaterialXGenGlsl_d.dll /0              -1                      0       160       `
  ��  d�8*飳   �  ?emitFunctionCall@LightShaderNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z MaterialXGenGlsl_d.dll /0              -1                      0       156       `
  ��  d唽d鋈�   �  ?emitFunctionCall@SurfaceNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z MaterialXGenGlsl_d.dll /0              -1                      0       161       `
  ��  d嗙磘陯   �  ?emitFunctionCall@UnlitSurfaceNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z MaterialXGenGlsl_d.dll 
/0              -1                      0       169       `
  ��  d哠�贂   �  ?emitFunctionDefinition@HeightToNormalNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z MaterialXGenGlsl_d.dll 
/0              -1                      0       172       `
  ��  d喼'b詷   �  ?emitFunctionDefinition@LightCompoundNodeGlsl@MaterialX_v1_39_2@@IEBAXPEAVClosureContext@2@AEAVGenContext@2@AEAVShaderStage@2@@Z MaterialXGenGlsl_d.dll /0              -1                      0       168       `
  ��  d喭�*螖   �  ?emitFunctionDefinition@LightCompoundNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z MaterialXGenGlsl_d.dll /0              -1                      0       167       `
  ��  d�8-烑�   �  ?emitFunctionDefinition@LightSamplerNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z MaterialXGenGlsl_d.dll 
/0              -1                      0       164       `
  ��  d唝/葠   �  ?emitFunctionDefinition@NumLightsNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z MaterialXGenGlsl_d.dll /0              -1                      0       137       `
  ��  d咷E挈u   �  ?emitInputs@EsslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z MaterialXGenGlsl_d.dll 
/0              -1                      0       137       `
  ��  d��!鄒   �  ?emitInputs@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z MaterialXGenGlsl_d.dll 
/0              -1                      0       135       `
  ��  d唕�魋   �  ?emitInputs@VkShaderGenerator@MaterialX_v1_39_2@@UEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z MaterialXGenGlsl_d.dll 
/0              -1                      0       140       `
  ��  d�?輥鹸   �  ?emitLightData@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z MaterialXGenGlsl_d.dll /0              -1                      0       173       `
  ��  d嗘^貦   �  ?emitLightFunctionDefinitions@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEBVShaderGraph@2@AEAVGenContext@2@AEAVShaderStage@2@@Z MaterialXGenGlsl_d.dll 
/0              -1                      0       218       `
  ��  d啺R   �  ?emitLightLoop@SurfaceNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z MaterialXGenGlsl_d.dll /0              -1                      0       138       `
  ��  d啅&庇v   �  ?emitOutputs@EsslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z MaterialXGenGlsl_d.dll /0              -1                      0       138       `
  ��  d嗆Bk纕   �  ?emitOutputs@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z MaterialXGenGlsl_d.dll /0              -1                      0       136       `
  ��  d�坉鵷   �  ?emitOutputs@VkShaderGenerator@MaterialX_v1_39_2@@UEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z MaterialXGenGlsl_d.dll /0              -1                      0       159       `
  ��  d啫�鎷   �  ?emitPixelStage@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEBVShaderGraph@2@AEAVGenContext@2@AEAVShaderStage@2@@Z MaterialXGenGlsl_d.dll 
/0              -1                      0       174       `
  ��  d啚c圩�   �  ?emitResourceBindings@GlslResourceBindingContext@MaterialX_v1_39_2@@UEAAXAEAVGenContext@2@AEBVVariableBlock@2@AEAVShaderStage@2@@Z MaterialXGenGlsl_d.dll /0              -1                      0       172       `
  ��  d咥遈虡   �  ?emitResourceBindings@VkResourceBindingContext@MaterialX_v1_39_2@@UEAAXAEAVGenContext@2@AEBVVariableBlock@2@AEAVShaderStage@2@@Z MaterialXGenGlsl_d.dll /0              -1                      0       167       `
  ��  d喬m8蹞   �  ?emitSamplingFunctionDefinition@BlurNodeGlsl@MaterialX_v1_39_2@@UEBAXAEBVShaderNode@2@AEAVGenContext@2@AEAVShaderStage@2@@Z MaterialXGenGlsl_d.dll 
/0              -1                      0       150       `
  ��  d喅TF軅   �  ?emitSpecularEnvironment@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z MaterialXGenGlsl_d.dll /0              -1                      0       250       `
  ��  d�Dn翩   �  ?emitStructuredResourceBindings@GlslResourceBindingContext@MaterialX_v1_39_2@@UEAAXAEAVGenContext@2@AEBVVariableBlock@2@AEAVShaderStage@2@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@3@Z MaterialXGenGlsl_d.dll /0              -1                      0       248       `
  ��  d唫 畿�   �  ?emitStructuredResourceBindings@VkResourceBindingContext@MaterialX_v1_39_2@@UEAAXAEAVGenContext@2@AEBVVariableBlock@2@AEAVShaderStage@2@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@3@Z MaterialXGenGlsl_d.dll /0              -1                      0       149       `
  ��  d啫蒚飦   �  ?emitTransmissionRender@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z MaterialXGenGlsl_d.dll 
/0              -1                      0       139       `
  ��  d�!ey閣   �  ?emitUniforms@EsslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z MaterialXGenGlsl_d.dll 
/0              -1                      0       139       `
  ��  d哘lC鉾   �  ?emitUniforms@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEAVGenContext@2@AEAVShaderStage@2@@Z MaterialXGenGlsl_d.dll 
/0              -1                      0       234       `
  ��  d喍�   �  ?emitVariableDeclaration@GlslShaderGenerator@MaterialX_v1_39_2@@UEBAXPEBVShaderPort@2@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAVGenContext@2@AEAVShaderStage@2@_N@Z MaterialXGenGlsl_d.dll /0              -1                      0       160       `
  ��  d啫M 鷮   �  ?emitVertexStage@GlslShaderGenerator@MaterialX_v1_39_2@@MEBAXAEBVShaderGraph@2@AEAVGenContext@2@AEAVShaderStage@2@@Z MaterialXGenGlsl_d.dll /0              -1                      0       131       `
  ��  d嗭惺鵲   �  ?enableSeparateBindingLocations@GlslResourceBindingContext@MaterialX_v1_39_2@@QEAAX_N@Z MaterialXGenGlsl_d.dll 
/0              -1                      0       272       `
  ��  d�7恻   �  ?generate@GlslShaderGenerator@MaterialX_v1_39_2@@UEBA?AV?$shared_ptr@VShader@MaterialX_v1_39_2@@@std@@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@4@V?$shared_ptr@VElement@MaterialX_v1_39_2@@@4@AEAVGenContext@2@@Z MaterialXGenGlsl_d.dll /0              -1                      0       167       `
  ��  d唘溮蓳   �  ?getConstantQualifier@GlslSyntax@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ MaterialXGenGlsl_d.dll 
/0              -1                      0       196       `
  ��  d喸�"影   �  ?getImplementation@GlslShaderGenerator@MaterialX_v1_39_2@@UEBA?AV?$shared_ptr@VShaderNodeImpl@MaterialX_v1_39_2@@@std@@AEBVNodeDef@2@AEAVGenContext@2@@Z MaterialXGenGlsl_d.dll /0              -1                      0       164       `
  ��  d�>v"趷   �  ?getInputQualifier@GlslSyntax@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ MaterialXGenGlsl_d.dll /0              -1                      0       162       `
  ��  d哬�迬   �  ?getInputQualifier@VkSyntax@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ MaterialXGenGlsl_d.dll /0              -1                      0       165       `
  ��  d�(�<駪   �  ?getOutputQualifier@GlslSyntax@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ MaterialXGenGlsl_d.dll 
/0              -1                      0       200       `
  ��  d嗗c拇   �  ?getResourceBindingContext@EsslShaderGenerator@MaterialX_v1_39_2@@MEBA?AV?$shared_ptr@VHwResourceBindingContext@MaterialX_v1_39_2@@@std@@AEAVGenContext@2@@Z MaterialXGenGlsl_d.dll /0              -1                      0       200       `
  ��  d�=檛执   �  ?getResourceBindingContext@GlslShaderGenerator@MaterialX_v1_39_2@@MEBA?AV?$shared_ptr@VHwResourceBindingContext@MaterialX_v1_39_2@@@std@@AEAVGenContext@2@@Z MaterialXGenGlsl_d.dll /0              -1                      0       198       `
  ��  d唞瞬   �  ?getResourceBindingContext@VkShaderGenerator@MaterialX_v1_39_2@@MEBA?AV?$shared_ptr@VHwResourceBindingContext@MaterialX_v1_39_2@@@std@@AEAVGenContext@2@@Z MaterialXGenGlsl_d.dll /0              -1                      0       169       `
  ��  d咷q饡   �  ?getSourceFileExtension@GlslSyntax@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ MaterialXGenGlsl_d.dll 
/0              -1                      0       165       `
  ��  d哅r钟�   �  ?getTarget@EsslShaderGenerator@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ MaterialXGenGlsl_d.dll 
/0              -1                      0       164       `
  ��  d哠妯鐞   �  ?getTarget@GlslImplementation@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ MaterialXGenGlsl_d.dll /0              -1                      0       165       `
  ��  d啎�'膽   �  ?getTarget@GlslShaderGenerator@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ MaterialXGenGlsl_d.dll 
/0              -1                      0       168       `
  ��  d�%zl銛   �  ?getTarget@HeightToNormalNodeGlsl@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ MaterialXGenGlsl_d.dll /0              -1                      0       167       `
  ��  d喴p蛽   �  ?getTarget@LightCompoundNodeGlsl@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ MaterialXGenGlsl_d.dll 
/0              -1                      0       165       `
  ��  d�6縑邞   �  ?getTarget@LightShaderNodeGlsl@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ MaterialXGenGlsl_d.dll 
/0              -1                      0       163       `
  ��  d咰惥鼜   �  ?getTarget@VkShaderGenerator@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ MaterialXGenGlsl_d.dll 
/0              -1                      0       166       `
  ��  d啂Jb飹   �  ?getUniformQualifier@GlslSyntax@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ MaterialXGenGlsl_d.dll /0              -1                      0       178       `
  ��  d喖C岩�   �  ?getValue@GlslStructTypeSyntax@MaterialX_v1_39_2@@UEBA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEBVValue@2@_N@Z MaterialXGenGlsl_d.dll /0              -1                      0       166       `
  ��  d問H曍�   �  ?getVersion@EsslShaderGenerator@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ MaterialXGenGlsl_d.dll /0              -1                      0       166       `
  ��  d�圇顠   �  ?getVersion@GlslShaderGenerator@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ MaterialXGenGlsl_d.dll /0              -1                      0       164       `
  ��  d�'深鲪   �  ?getVersion@VkShaderGenerator@MaterialX_v1_39_2@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ MaterialXGenGlsl_d.dll /0              -1                      0       194       `
  ��  d�3蝦债   �  ?getVertexDataPrefix@EsslShaderGenerator@MaterialX_v1_39_2@@UEBA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEBVVariableBlock@2@@Z MaterialXGenGlsl_d.dll /0              -1                      0       194       `
  ��  d�!z砒�     ?getVertexDataPrefix@GlslShaderGenerator@MaterialX_v1_39_2@@UEBA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEBVVariableBlock@2@@Z MaterialXGenGlsl_d.dll /0              -1                      0       192       `
  ��  d咰v娟�    ?getVertexDataPrefix@VkShaderGenerator@MaterialX_v1_39_2@@UEBA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEBVVariableBlock@2@@Z MaterialXGenGlsl_d.dll /0              -1                      0       109       `
  ��  d�O⑴Y    ?initialize@GlslResourceBindingContext@MaterialX_v1_39_2@@UEAAXXZ MaterialXGenGlsl_d.dll 
/0              -1                      0       144       `
  ��  d�- 咧|    ?initialize@LightCompoundNodeGlsl@MaterialX_v1_39_2@@UEAAXAEBVInterfaceElement@2@AEAVGenContext@2@@Z MaterialXGenGlsl_d.dll /0              -1                      0       142       `
  ��  d�騴    ?initialize@LightShaderNodeGlsl@MaterialX_v1_39_2@@UEAAXAEBVInterfaceElement@2@AEAVGenContext@2@@Z MaterialXGenGlsl_d.dll /0              -1                      0       107       `
  ��  d啎:嵷W    ?initialize@VkResourceBindingContext@MaterialX_v1_39_2@@UEAAXXZ MaterialXGenGlsl_d.dll 
/0              -1                      0       123       `
  ��  d�$飢雊    ?isEditable@GeomPropValueNodeGlsl@MaterialX_v1_39_2@@UEBA_NAEBVShaderInput@2@@Z MaterialXGenGlsl_d.dll 
/0              -1                      0       267       `
  ��  d唡僟伧    ?remapEnumeration@GlslSyntax@MaterialX_v1_39_2@@UEBA_NAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@VTypeDesc@2@0AEAU?$pair@VTypeDesc@MaterialX_v1_39_2@@V?$shared_ptr@VValue@MaterialX_v1_39_2@@@std@@@4@@Z MaterialXGenGlsl_d.dll 
/0              -1                      0       127       `
  ��  d唃罰頺    ?requiresLighting@GlslShaderGenerator@MaterialX_v1_39_2@@MEBA_NAEBVShaderGraph@2@@Z MaterialXGenGlsl_d.dll 
/0              -1                      0       176       `
  ��  d嗧T年�   	 ?toVec4@GlslShaderGenerator@MaterialX_v1_39_2@@KAXPEBVTypeDesc@2@AEAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z MaterialXGenGlsl_d.dll /0              -1                      0       173       `
  ��  d�<
T迿   
 ?toVec4@GlslShaderGenerator@MaterialX_v1_39_2@@KAXVTypeDesc@2@AEAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z MaterialXGenGlsl_d.dll 
/0              -1                      0       112       `
  ��  d唸峉黒    ?typeSupported@GlslSyntax@MaterialX_v1_39_2@@UEBA_NPEBVTypeDesc@2@@Z MaterialXGenGlsl_d.dll 