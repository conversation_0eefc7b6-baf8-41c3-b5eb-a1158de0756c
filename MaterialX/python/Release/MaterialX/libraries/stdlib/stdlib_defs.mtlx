<?xml version="1.0"?>
<materialx version="1.39">
  <!--
    Copyright Contributors to the MaterialX Project
    SPDX-License-Identifier: Apache-2.0

    Declarations of standard data types and nodes included in the MaterialX specification.
  -->

  <!-- ======================================================================== -->
  <!-- Data types                                                               -->
  <!-- ======================================================================== -->

  <typedef name="boolean" />
  <typedef name="integer" />
  <typedef name="float" />
  <typedef name="color3" semantic="color" />
  <typedef name="color4" semantic="color" />
  <typedef name="vector2" />
  <typedef name="vector3" />
  <typedef name="vector4" />
  <typedef name="matrix33" />
  <typedef name="matrix44" />
  <typedef name="string" />
  <typedef name="filename" />
  <typedef name="geomname" />
  <typedef name="surfaceshader" semantic="shader" context="surface" />
  <typedef name="displacementshader" semantic="shader" context="displacement" />
  <typedef name="volumeshader" semantic="shader" context="volume" />
  <typedef name="lightshader" semantic="shader" context="light" />
  <typedef name="material" semantic="material" />
  <typedef name="none" />

  <typedef name="integerarray" />
  <typedef name="floatarray" />
  <typedef name="color3array" semantic="color" />
  <typedef name="color4array" semantic="color" />
  <typedef name="vector2array" />
  <typedef name="vector3array" />
  <typedef name="vector4array" />
  <typedef name="stringarray" />
  <typedef name="geomnamearray" />

  <!-- ======================================================================== -->
  <!-- Units and unit types                                                     -->
  <!-- ======================================================================== -->

  <unittypedef name="distance" />
  <unitdef name="UD_stdlib_distance" unittype="distance">
    <unit name="nanometer" scale="0.000000001" />
    <unit name="micron" scale="0.000001" />
    <unit name="millimeter" scale="0.001" />
    <unit name="centimeter" scale="0.01" />
    <unit name="inch" scale="0.0254" />
    <unit name="foot" scale="0.3048" />
    <unit name="yard" scale="0.9144" />
    <unit name="meter" scale="1.0" />
    <unit name="kilometer" scale="1000.0" />
    <unit name="mile" scale="1609.344" />
  </unitdef>

  <unittypedef name="angle" />
  <unitdef name="UD_stdlib_angle" unittype="angle">
    <unit name="degree" scale="1.0" />
    <unit name="radian" scale="57.295779513" />
  </unitdef>

  <!-- ======================================================================== -->
  <!-- Geometric Properties                                                     -->
  <!-- ======================================================================== -->

  <geompropdef name="Pobject" type="vector3" geomprop="position" space="object" />
  <geompropdef name="Nobject" type="vector3" geomprop="normal" space="object" />
  <geompropdef name="Tobject" type="vector3" geomprop="tangent" space="object" index="0" />
  <geompropdef name="Bobject" type="vector3" geomprop="bitangent" space="object" index="0" />
  <geompropdef name="Pworld" type="vector3" geomprop="position" space="world" />
  <geompropdef name="Nworld" type="vector3" geomprop="normal" space="world" />
  <geompropdef name="Tworld" type="vector3" geomprop="tangent" space="world" index="0" />
  <geompropdef name="Bworld" type="vector3" geomprop="bitangent" space="world" index="0" />
  <geompropdef name="UV0" type="vector2" geomprop="texcoord" index="0" />

  <!-- ======================================================================== -->
  <!-- Materials                                                                -->
  <!-- ======================================================================== -->

  <!-- Surface material -->
  <nodedef name="ND_surfacematerial" node="surfacematerial" nodegroup="material">
    <input name="surfaceshader" type="surfaceshader" value="" />
    <input name="backsurfaceshader" type="surfaceshader" value="" />
    <input name="displacementshader" type="displacementshader" value="" />
    <output name="out" type="material" />
  </nodedef>

  <!-- Volume material -->
  <nodedef name="ND_volumematerial" node="volumematerial" nodegroup="material">
    <input name="volumeshader" type="volumeshader" value="" />
    <output name="out" type="material" />
  </nodedef>

  <!-- ======================================================================== -->
  <!-- Shader nodes                                                             -->
  <!-- ======================================================================== -->

  <!--
    Node: <surface_unlit>
    An unlit surface shader node. Represents a surface that can emit and transmit light, 
    but does not receive illumination from light sources or other surfaces.
  -->
  <nodedef name="ND_surface_unlit" node="surface_unlit" nodegroup="shader" doc="Construct a surface shader from emission and transmission values.">
    <input name="emission" type="float" value="1.0" doc="Surface emission amount." />
    <input name="emission_color" type="color3" value="1,1,1" doc="Surface emission color." />
    <input name="transmission" type="float" value="0.0" doc="Surface transmission amount." />
    <input name="transmission_color" type="color3" value="1,1,1" doc="Surface transmission color." />
    <input name="opacity" type="float" value="1.0" doc="Surface cutout opacity." />
    <output name="out" type="surfaceshader" />
  </nodedef>

  <!-- ======================================================================== -->
  <!-- Texture nodes                                                            -->
  <!-- ======================================================================== -->

  <!--
    Node: <image>
    Samples data from a single image, or from a layer within a multi-layer image.
  -->
  <nodedef name="ND_image_float" node="image" nodegroup="texture2d">
    <input name="file" type="filename" value="" uiname="Filename" uniform="true" />
    <input name="layer" type="string" value="" uiname="Layer" uniform="true" />
    <input name="default" type="float" value="0.0" uiname="Default Color" />
    <input name="texcoord" type="vector2" defaultgeomprop="UV0" uiname="Texture Coordinates" />
    <input name="uaddressmode" type="string" value="periodic" enum="constant,clamp,periodic,mirror" uiname="Address Mode U" uniform="true" />
    <input name="vaddressmode" type="string" value="periodic" enum="constant,clamp,periodic,mirror" uiname="Address Mode V" uniform="true" />
    <input name="filtertype" type="string" value="linear" enum="closest,linear,cubic" uiname="Filter Type" uniform="true" />
    <input name="framerange" type="string" value="" uiname="Frame Range" uniform="true" />
    <input name="frameoffset" type="integer" value="0" uiname="Frame Offset" uniform="true" />
    <input name="frameendaction" type="string" value="constant" enum="constant,clamp,periodic,mirror" uiname="Frame End Action" uniform="true" />
    <output name="out" type="float" default="0.0" />
  </nodedef>
  <nodedef name="ND_image_color3" node="image" nodegroup="texture2d">
    <input name="file" type="filename" value="" uiname="Filename" uniform="true" />
    <input name="layer" type="string" value="" uiname="Layer" uniform="true" />
    <input name="default" type="color3" value="0.0, 0.0, 0.0" uiname="Default Color" />
    <input name="texcoord" type="vector2" defaultgeomprop="UV0" uiname="Texture Coordinates" />
    <input name="uaddressmode" type="string" value="periodic" enum="constant,clamp,periodic,mirror" uiname="Address Mode U" uniform="true" />
    <input name="vaddressmode" type="string" value="periodic" enum="constant,clamp,periodic,mirror" uiname="Address Mode V" uniform="true" />
    <input name="filtertype" type="string" value="linear" enum="closest,linear,cubic" uiname="Filter Type" uniform="true" />
    <input name="framerange" type="string" value="" uiname="Frame Range" uniform="true" />
    <input name="frameoffset" type="integer" value="0" uiname="Frame Offset" uniform="true" />
    <input name="frameendaction" type="string" value="constant" enum="constant,clamp,periodic,mirror" uiname="Frame End Action" uniform="true" />
    <output name="out" type="color3" default="0.0, 0.0, 0.0" />
  </nodedef>
  <nodedef name="ND_image_color4" node="image" nodegroup="texture2d">
    <input name="file" type="filename" value="" uiname="Filename" uniform="true" />
    <input name="layer" type="string" value="" uiname="Layer" uniform="true" />
    <input name="default" type="color4" value="0.0, 0.0, 0.0, 0.0" uiname="Default Color" />
    <input name="texcoord" type="vector2" defaultgeomprop="UV0" uiname="Texture Coordinates" />
    <input name="uaddressmode" type="string" value="periodic" enum="constant,clamp,periodic,mirror" uiname="Address Mode U" uniform="true" />
    <input name="vaddressmode" type="string" value="periodic" enum="constant,clamp,periodic,mirror" uiname="Address Mode V" uniform="true" />
    <input name="filtertype" type="string" value="linear" enum="closest,linear,cubic" uiname="Filter Type" uniform="true" />
    <input name="framerange" type="string" value="" uiname="Frame Range" uniform="true" />
    <input name="frameoffset" type="integer" value="0" uiname="Frame Offset" uniform="true" />
    <input name="frameendaction" type="string" value="constant" enum="constant,clamp,periodic,mirror" uiname="Frame End Action" uniform="true" />
    <output name="out" type="color4" default="0.0, 0.0, 0.0, 0.0" />
  </nodedef>
  <nodedef name="ND_image_vector2" node="image" nodegroup="texture2d">
    <input name="file" type="filename" value="" uiname="Filename" uniform="true" />
    <input name="layer" type="string" value="" uiname="Layer" uniform="true" />
    <input name="default" type="vector2" value="0.0, 0.0" uiname="Default Color" />
    <input name="texcoord" type="vector2" defaultgeomprop="UV0" uiname="Texture Coordinates" />
    <input name="uaddressmode" type="string" value="periodic" enum="constant,clamp,periodic,mirror" uiname="Address Mode U" uniform="true" />
    <input name="vaddressmode" type="string" value="periodic" enum="constant,clamp,periodic,mirror" uiname="Address Mode V" uniform="true" />
    <input name="filtertype" type="string" value="linear" enum="closest,linear,cubic" uiname="Filter Type" uniform="true" />
    <input name="framerange" type="string" value="" uiname="Frame Range" uniform="true" />
    <input name="frameoffset" type="integer" value="0" uiname="Frame Offset" uniform="true" />
    <input name="frameendaction" type="string" value="constant" enum="constant,clamp,periodic,mirror" uiname="Frame End Action" uniform="true" />
    <output name="out" type="vector2" default="0.0, 0.0" />
  </nodedef>
  <nodedef name="ND_image_vector3" node="image" nodegroup="texture2d">
    <input name="file" type="filename" value="" uiname="Filename" uniform="true" />
    <input name="layer" type="string" value="" uiname="Layer" uniform="true" />
    <input name="default" type="vector3" value="0.0, 0.0, 0.0" uiname="Default Color" />
    <input name="texcoord" type="vector2" defaultgeomprop="UV0" uiname="Texture Coordinates" />
    <input name="uaddressmode" type="string" value="periodic" enum="constant,clamp,periodic,mirror" uiname="Address Mode U" uniform="true" />
    <input name="vaddressmode" type="string" value="periodic" enum="constant,clamp,periodic,mirror" uiname="Address Mode V" uniform="true" />
    <input name="filtertype" type="string" value="linear" enum="closest,linear,cubic" uiname="Filter Type" uniform="true" />
    <input name="framerange" type="string" value="" uiname="Frame Range" uniform="true" />
    <input name="frameoffset" type="integer" value="0" uiname="Frame Offset" uniform="true" />
    <input name="frameendaction" type="string" value="constant" enum="constant,clamp,periodic,mirror" uiname="Frame End Action" uniform="true" />
    <output name="out" type="vector3" default="0.0, 0.0, 0.0" />
  </nodedef>
  <nodedef name="ND_image_vector4" node="image" nodegroup="texture2d">
    <input name="file" type="filename" value="" uiname="Filename" uniform="true" />
    <input name="layer" type="string" value="" uiname="Layer" uniform="true" />
    <input name="default" type="vector4" value="0.0, 0.0, 0.0, 0.0" uiname="Default Color" />
    <input name="texcoord" type="vector2" defaultgeomprop="UV0" uiname="Texture Coordinates" />
    <input name="uaddressmode" type="string" value="periodic" enum="constant,clamp,periodic,mirror" uiname="Address Mode U" uniform="true" />
    <input name="vaddressmode" type="string" value="periodic" enum="constant,clamp,periodic,mirror" uiname="Address Mode V" uniform="true" />
    <input name="filtertype" type="string" value="linear" enum="closest,linear,cubic" uiname="Filter Type" uniform="true" />
    <input name="framerange" type="string" value="" uiname="Frame Range" uniform="true" />
    <input name="frameoffset" type="integer" value="0" uiname="Frame Offset" uniform="true" />
    <input name="frameendaction" type="string" value="constant" enum="constant,clamp,periodic,mirror" uiname="Frame End Action" uniform="true" />
    <output name="out" type="vector4" default="0.0, 0.0, 0.0, 0.0" />
  </nodedef>

  <!--
    Node: <tiledimage> Supplemental Node
    Samples data from a single image, with provisions for tiling and offsetting the image
    across uv space.
  -->
  <nodedef name="ND_tiledimage_float" node="tiledimage" nodegroup="texture2d">
    <input name="file" type="filename" value="" uniform="true" />
    <input name="default" type="float" value="0.0" />
    <input name="texcoord" type="vector2" defaultgeomprop="UV0" />
    <input name="uvtiling" type="vector2" value="1.0, 1.0" />
    <input name="uvoffset" type="vector2" value="0.0, 0.0" />
    <input name="realworldimagesize" type="vector2" value="1.0, 1.0" unittype="distance" />
    <input name="realworldtilesize" type="vector2" value="1.0, 1.0" unittype="distance" />
    <input name="filtertype" type="string" value="linear" enum="closest,linear,cubic" uniform="true" />
    <input name="framerange" type="string" value="" uniform="true" />
    <input name="frameoffset" type="integer" value="0" uniform="true" />
    <input name="frameendaction" type="string" value="constant" enum="constant,clamp,periodic,mirror" uniform="true" />
    <output name="out" type="float" default="0.0" />
  </nodedef>
  <nodedef name="ND_tiledimage_color3" node="tiledimage" nodegroup="texture2d">
    <input name="file" type="filename" value="" uniform="true" />
    <input name="default" type="color3" value="0.0, 0.0, 0.0" />
    <input name="texcoord" type="vector2" defaultgeomprop="UV0" />
    <input name="uvtiling" type="vector2" value="1.0, 1.0" />
    <input name="uvoffset" type="vector2" value="0.0, 0.0" />
    <input name="realworldimagesize" type="vector2" value="1.0, 1.0" unittype="distance" />
    <input name="realworldtilesize" type="vector2" value="1.0, 1.0" unittype="distance" />
    <input name="filtertype" type="string" value="linear" enum="closest,linear,cubic" uniform="true" />
    <input name="framerange" type="string" value="" uniform="true" />
    <input name="frameoffset" type="integer" value="0" uniform="true" />
    <input name="frameendaction" type="string" value="constant" enum="constant,clamp,periodic,mirror" uniform="true" />
    <output name="out" type="color3" default="0.0, 0.0, 0.0" />
  </nodedef>
  <nodedef name="ND_tiledimage_color4" node="tiledimage" nodegroup="texture2d">
    <input name="file" type="filename" value="" uniform="true" />
    <input name="default" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="texcoord" type="vector2" defaultgeomprop="UV0" />
    <input name="uvtiling" type="vector2" value="1.0, 1.0" />
    <input name="uvoffset" type="vector2" value="0.0, 0.0" />
    <input name="realworldimagesize" type="vector2" value="1.0, 1.0" unittype="distance" />
    <input name="realworldtilesize" type="vector2" value="1.0, 1.0" unittype="distance" />
    <input name="filtertype" type="string" value="linear" enum="closest,linear,cubic" uniform="true" />
    <input name="framerange" type="string" value="" uniform="true" />
    <input name="frameoffset" type="integer" value="0" uniform="true" />
    <input name="frameendaction" type="string" value="constant" enum="constant,clamp,periodic,mirror" uniform="true" />
    <output name="out" type="color4" default="0.0, 0.0, 0.0, 0.0" />
  </nodedef>
  <nodedef name="ND_tiledimage_vector2" node="tiledimage" nodegroup="texture2d">
    <input name="file" type="filename" value="" uniform="true" />
    <input name="default" type="vector2" value="0.0, 0.0" />
    <input name="texcoord" type="vector2" defaultgeomprop="UV0" />
    <input name="uvtiling" type="vector2" value="1.0, 1.0" />
    <input name="uvoffset" type="vector2" value="0.0, 0.0" />
    <input name="realworldimagesize" type="vector2" value="1.0, 1.0" unittype="distance" />
    <input name="realworldtilesize" type="vector2" value="1.0, 1.0" unittype="distance" />
    <input name="filtertype" type="string" value="linear" enum="closest,linear,cubic" uniform="true" />
    <input name="framerange" type="string" value="" uniform="true" />
    <input name="frameoffset" type="integer" value="0" uniform="true" />
    <input name="frameendaction" type="string" value="constant" enum="constant,clamp,periodic,mirror" uniform="true" />
    <output name="out" type="vector2" default="0.0, 0.0" />
  </nodedef>
  <nodedef name="ND_tiledimage_vector3" node="tiledimage" nodegroup="texture2d">
    <input name="file" type="filename" value="" uniform="true" />
    <input name="default" type="vector3" value="0.0, 0.0, 0.0" />
    <input name="texcoord" type="vector2" defaultgeomprop="UV0" />
    <input name="uvtiling" type="vector2" value="1.0, 1.0" />
    <input name="uvoffset" type="vector2" value="0.0, 0.0" />
    <input name="realworldimagesize" type="vector2" value="1.0, 1.0" unittype="distance" />
    <input name="realworldtilesize" type="vector2" value="1.0, 1.0" unittype="distance" />
    <input name="filtertype" type="string" value="linear" enum="closest,linear,cubic" uniform="true" />
    <input name="framerange" type="string" value="" uniform="true" />
    <input name="frameoffset" type="integer" value="0" uniform="true" />
    <input name="frameendaction" type="string" value="constant" enum="constant,clamp,periodic,mirror" uniform="true" />
    <output name="out" type="vector3" default="0.0, 0.0, 0.0" />
  </nodedef>
  <nodedef name="ND_tiledimage_vector4" node="tiledimage" nodegroup="texture2d">
    <input name="file" type="filename" value="" uniform="true" />
    <input name="default" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="texcoord" type="vector2" defaultgeomprop="UV0" />
    <input name="uvtiling" type="vector2" value="1.0, 1.0" />
    <input name="uvoffset" type="vector2" value="0.0, 0.0" />
    <input name="realworldimagesize" type="vector2" value="1.0, 1.0" unittype="distance" />
    <input name="realworldtilesize" type="vector2" value="1.0, 1.0" unittype="distance" />
    <input name="filtertype" type="string" value="linear" enum="closest,linear,cubic" uniform="true" />
    <input name="framerange" type="string" value="" uniform="true" />
    <input name="frameoffset" type="integer" value="0" uniform="true" />
    <input name="frameendaction" type="string" value="constant" enum="constant,clamp,periodic,mirror" uniform="true" />
    <output name="out" type="vector4" default="0.0, 0.0, 0.0, 0.0" />
  </nodedef>

  <!--
    Node: <triplanarprojection> Supplemental Node
    Samples data from three images, or layers within multi-layer images, and projects a tiled
    representation of the images along each of the three respective coordinate axes, computing
    an adjustable weighted blend of the three samples using the geometric normal.
  -->
  <nodedef name="ND_triplanarprojection_float" node="triplanarprojection" nodegroup="texture3d">
    <input name="filex" type="filename" value="" uniform="true" />
    <input name="filey" type="filename" value="" uniform="true" />
    <input name="filez" type="filename" value="" uniform="true" />
    <input name="layerx" type="string" value="" uniform="true" />
    <input name="layery" type="string" value="" uniform="true" />
    <input name="layerz" type="string" value="" uniform="true" />
    <input name="default" type="float" value="0.0" />
    <input name="position" type="vector3" defaultgeomprop="Pobject" />
    <input name="normal" type="vector3" defaultgeomprop="Nobject" />
    <input name="upaxis" type="integer" value="2" enum="X,Y,Z" enumvalues="0,1,2" uniform="true" />
    <input name="blend" type="float" value="1.0" uimin="0.0" uimax="1.0" />
    <input name="filtertype" type="string" value="linear" enum="closest,linear,cubic" uniform="true" />
    <input name="framerange" type="string" value="" uniform="true" />
    <input name="frameoffset" type="integer" value="0" uniform="true" />
    <input name="frameendaction" type="string" value="constant" enum="constant,clamp,periodic,mirror" uniform="true" />
    <output name="out" type="float" default="0.0" />
  </nodedef>
  <nodedef name="ND_triplanarprojection_color3" node="triplanarprojection" nodegroup="texture3d">
    <input name="filex" type="filename" value="" uniform="true" />
    <input name="filey" type="filename" value="" uniform="true" />
    <input name="filez" type="filename" value="" uniform="true" />
    <input name="layerx" type="string" value="" uniform="true" />
    <input name="layery" type="string" value="" uniform="true" />
    <input name="layerz" type="string" value="" uniform="true" />
    <input name="default" type="color3" value="0.0, 0.0, 0.0" />
    <input name="position" type="vector3" defaultgeomprop="Pobject" />
    <input name="normal" type="vector3" defaultgeomprop="Nobject" />
    <input name="upaxis" type="integer" value="2" enum="X,Y,Z" enumvalues="0,1,2" uniform="true" />
    <input name="blend" type="float" value="1.0" uimin="0.0" uimax="1.0" />
    <input name="filtertype" type="string" value="linear" enum="closest,linear,cubic" uniform="true" />
    <input name="framerange" type="string" value="" uniform="true" />
    <input name="frameoffset" type="integer" value="0" uniform="true" />
    <input name="frameendaction" type="string" value="constant" enum="constant,clamp,periodic,mirror" uniform="true" />
    <output name="out" type="color3" default="0.0, 0.0, 0.0" />
  </nodedef>
  <nodedef name="ND_triplanarprojection_color4" node="triplanarprojection" nodegroup="texture3d">
    <input name="filex" type="filename" value="" uniform="true" />
    <input name="filey" type="filename" value="" uniform="true" />
    <input name="filez" type="filename" value="" uniform="true" />
    <input name="layerx" type="string" value="" uniform="true" />
    <input name="layery" type="string" value="" uniform="true" />
    <input name="layerz" type="string" value="" uniform="true" />
    <input name="default" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="position" type="vector3" defaultgeomprop="Pobject" />
    <input name="normal" type="vector3" defaultgeomprop="Nobject" />
    <input name="upaxis" type="integer" value="2" enum="X,Y,Z" enumvalues="0,1,2" uniform="true" />
    <input name="blend" type="float" value="1.0" uimin="0.0" uimax="1.0" />
    <input name="filtertype" type="string" value="linear" enum="closest,linear,cubic" uniform="true" />
    <input name="framerange" type="string" value="" uniform="true" />
    <input name="frameoffset" type="integer" value="0" uniform="true" />
    <input name="frameendaction" type="string" value="constant" enum="constant,clamp,periodic,mirror" uniform="true" />
    <output name="out" type="color4" default="0.0, 0.0, 0.0, 0.0" />
  </nodedef>
  <nodedef name="ND_triplanarprojection_vector2" node="triplanarprojection" nodegroup="texture3d">
    <input name="filex" type="filename" value="" uniform="true" />
    <input name="filey" type="filename" value="" uniform="true" />
    <input name="filez" type="filename" value="" uniform="true" />
    <input name="layerx" type="string" value="" uniform="true" />
    <input name="layery" type="string" value="" uniform="true" />
    <input name="layerz" type="string" value="" uniform="true" />
    <input name="default" type="vector2" value="0.0, 0.0" />
    <input name="position" type="vector3" defaultgeomprop="Pobject" />
    <input name="normal" type="vector3" defaultgeomprop="Nobject" />
    <input name="upaxis" type="integer" value="2" enum="X,Y,Z" enumvalues="0,1,2" uniform="true" />
    <input name="blend" type="float" value="1.0" uimin="0.0" uimax="1.0" />
    <input name="filtertype" type="string" value="linear" enum="closest,linear,cubic" uniform="true" />
    <input name="framerange" type="string" value="" uniform="true" />
    <input name="frameoffset" type="integer" value="0" uniform="true" />
    <input name="frameendaction" type="string" value="constant" enum="constant,clamp,periodic,mirror" uniform="true" />
    <output name="out" type="vector2" default="0.0, 0.0" />
  </nodedef>
  <nodedef name="ND_triplanarprojection_vector3" node="triplanarprojection" nodegroup="texture3d">
    <input name="filex" type="filename" value="" uniform="true" />
    <input name="filey" type="filename" value="" uniform="true" />
    <input name="filez" type="filename" value="" uniform="true" />
    <input name="layerx" type="string" value="" uniform="true" />
    <input name="layery" type="string" value="" uniform="true" />
    <input name="layerz" type="string" value="" uniform="true" />
    <input name="default" type="vector3" value="0.0, 0.0, 0.0" />
    <input name="position" type="vector3" defaultgeomprop="Pobject" />
    <input name="normal" type="vector3" defaultgeomprop="Nobject" />
    <input name="upaxis" type="integer" value="2" enum="X,Y,Z" enumvalues="0,1,2" uniform="true" />
    <input name="blend" type="float" value="1.0" uimin="0.0" uimax="1.0" />
    <input name="filtertype" type="string" value="linear" enum="closest,linear,cubic" uniform="true" />
    <input name="framerange" type="string" value="" uniform="true" />
    <input name="frameoffset" type="integer" value="0" uniform="true" />
    <input name="frameendaction" type="string" value="constant" enum="constant,clamp,periodic,mirror" uniform="true" />
    <output name="out" type="vector3" default="0.0, 0.0, 0.0" />
  </nodedef>
  <nodedef name="ND_triplanarprojection_vector4" node="triplanarprojection" nodegroup="texture3d">
    <input name="filex" type="filename" value="" uniform="true" />
    <input name="filey" type="filename" value="" uniform="true" />
    <input name="filez" type="filename" value="" uniform="true" />
    <input name="layerx" type="string" value="" uniform="true" />
    <input name="layery" type="string" value="" uniform="true" />
    <input name="layerz" type="string" value="" uniform="true" />
    <input name="default" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="position" type="vector3" defaultgeomprop="Pobject" />
    <input name="normal" type="vector3" defaultgeomprop="Nobject" />
    <input name="upaxis" type="integer" value="2" enum="X,Y,Z" enumvalues="0,1,2" uniform="true" />
    <input name="blend" type="float" value="1.0" uimin="0.0" uimax="1.0" />
    <input name="filtertype" type="string" value="linear" enum="closest,linear,cubic" uniform="true" />
    <input name="framerange" type="string" value="" uniform="true" />
    <input name="frameoffset" type="integer" value="0" uniform="true" />
    <input name="frameendaction" type="string" value="constant" enum="constant,clamp,periodic,mirror" uniform="true" />
    <output name="out" type="vector4" default="0.0, 0.0, 0.0, 0.0" />
  </nodedef>

  <!-- ======================================================================== -->
  <!-- Procedural nodes                                                         -->
  <!-- ======================================================================== -->

  <!--
    Node: <constant>
    A constant value. When exposed as a public parameter, this is a way to create a
    value that can be accessed in multiple places in the opgraph.
  -->
  <nodedef name="ND_constant_float" node="constant" nodegroup="procedural">
    <input name="value" type="float" value="0.0" />
    <output name="out" type="float" default="0.0" />
  </nodedef>
  <nodedef name="ND_constant_color3" node="constant" nodegroup="procedural">
    <input name="value" type="color3" value="0.0, 0.0, 0.0" />
    <output name="out" type="color3" default="0.0, 0.0, 0.0" />
  </nodedef>
  <nodedef name="ND_constant_color4" node="constant" nodegroup="procedural">
    <input name="value" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <output name="out" type="color4" default="0.0, 0.0, 0.0, 0.0" />
  </nodedef>
  <nodedef name="ND_constant_vector2" node="constant" nodegroup="procedural">
    <input name="value" type="vector2" value="0.0, 0.0" />
    <output name="out" type="vector2" default="0.0, 0.0" />
  </nodedef>
  <nodedef name="ND_constant_vector3" node="constant" nodegroup="procedural">
    <input name="value" type="vector3" value="0.0, 0.0, 0.0" />
    <output name="out" type="vector3" default="0.0, 0.0, 0.0" />
  </nodedef>
  <nodedef name="ND_constant_vector4" node="constant" nodegroup="procedural">
    <input name="value" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <output name="out" type="vector4" default="0.0, 0.0, 0.0, 0.0" />
  </nodedef>
  <nodedef name="ND_constant_boolean" node="constant" nodegroup="procedural">
    <input name="value" type="boolean" value="false" />
    <output name="out" type="boolean" default="false" />
  </nodedef>
  <nodedef name="ND_constant_integer" node="constant" nodegroup="procedural">
    <input name="value" type="integer" value="0" />
    <output name="out" type="integer" default="0" />
  </nodedef>
  <nodedef name="ND_constant_matrix33" node="constant" nodegroup="procedural">
    <input name="value" type="matrix33" value="1.0,0.0,0.0, 0.0,1.0,0.0, 0.0,0.0,1.0" />
    <output name="out" type="matrix33" default="1.0,0.0,0.0, 0.0,1.0,0.0, 0.0,0.0,1.0" />
  </nodedef>
  <nodedef name="ND_constant_matrix44" node="constant" nodegroup="procedural">
    <input name="value" type="matrix44" value="1.0,0.0,0.0,0.0, 0.0,1.0,0.0,0.0, 0.0,0.0,1.0,0.0, 0.0,0.0,0.0,1.0" />
    <output name="out" type="matrix44" default="1.0,0.0,0.0,0.0, 0.0,1.0,0.0,0.0, 0.0,0.0,1.0,0.0, 0.0,0.0,0.0,1.0" />
  </nodedef>
  <nodedef name="ND_constant_string" node="constant" nodegroup="procedural">
    <input name="value" type="string" value="" uniform="true" />
    <output name="out" type="string" default="" />
  </nodedef>
  <nodedef name="ND_constant_filename" node="constant" nodegroup="procedural">
    <input name="value" type="filename" value="" uniform="true" />
    <output name="out" type="filename" default="" />
  </nodedef>

  <!--
    Node: <ramp>
    A ramp that supports up to 10 control points.
  -->
  <nodedef name="ND_ramp" node="ramp" nodegroup="procedural2d">
    <input name="texcoord" type="vector2" defaultgeomprop="UV0" />
    <input name="type" type="integer" value="0" enum="standard,radial,circular,box" enumvalues="0,1,2,3" />
    <input name="interpolation" type="integer" value="1" enum="linear,smooth,step" enumvalues="0,1,2" />
    <input name="num_intervals" type="integer" value="2" uimin="2" uimax="10" />
    <input name="interval1" type="float" value="0" uimin="0" uimax="1" />
    <input name="color1" type="color4" value="0,0,0,1" uimin="0,0,0,0" uimax="1,1,1,1" />
    <input name="interval2" type="float" value="1" uimin="0" uimax="1" />
    <input name="color2" type="color4" value="1,1,1,1" uimin="0,0,0,0" uimax="1,1,1,1" />
    <input name="interval3" type="float" value="1" uimin="0" uimax="1" />
    <input name="color3" type="color4" value="1,1,1,1" uimin="0,0,0,0" uimax="1,1,1,1" />
    <input name="interval4" type="float" value="1" uimin="0" uimax="1" />
    <input name="color4" type="color4" value="1,1,1,1" uimin="0,0,0,0" uimax="1,1,1,1" />
    <input name="interval5" type="float" value="1" uimin="0" uimax="1" />
    <input name="color5" type="color4" value="1,1,1,1" uimin="0,0,0,0" uimax="1,1,1,1" />
    <input name="interval6" type="float" value="1" uimin="0" uimax="1" />
    <input name="color6" type="color4" value="1,1,1,1" uimin="0,0,0,0" uimax="1,1,1,1" />
    <input name="interval7" type="float" value="1" uimin="0" uimax="1" />
    <input name="color7" type="color4" value="1,1,1,1" uimin="0,0,0,0" uimax="1,1,1,1" />
    <input name="interval8" type="float" value="1" uimin="0" uimax="1" />
    <input name="color8" type="color4" value="1,1,1,1" uimin="0,0,0,0" uimax="1,1,1,1" />
    <input name="interval9" type="float" value="1" uimin="0" uimax="1" />
    <input name="color9" type="color4" value="1,1,1,1" uimin="0,0,0,0" uimax="1,1,1,1" />
    <input name="interval10" type="float" value="1" uimin="0" uimax="1" />
    <input name="color10" type="color4" value="1,1,1,1" uimin="0,0,0,0" uimax="1,1,1,1" />
    <output name="out" type="color4" />
  </nodedef>

  <!--
    Node <ramp_gradient>
    A helper node that handles a single control point within a <ramp>.
  -->
  <nodedef name="ND_ramp_gradient" node="ramp_gradient" nodegroup="procedural2d">
    <input name="x" type="float" value="0" uimin="0" uimax="1" />
    <input name="interval1" type="float" value="0" uimin="0" uimax="1" />
    <input name="interval2" type="float" value="1" uimin="0" uimax="1" />
    <input name="color1" type="color4" value="0,0,0,1" uimin="0,0,0,0" uimax="1,1,1,1" />
    <input name="color2" type="color4" value="1,1,1,1" uimin="0,0,0,0" uimax="1,1,1,1" />
    <input name="interpolation" type="integer" value="1" enum="linear,smooth,step" enumvalues="0,1,2" />
    <input name="prev_color" type="color4" value="0,0,0,1" uimin="0,0,0,0" uimax="1,1,1,1" />
    <input name="interval_num" type="integer" value="1" />
    <input name="num_intervals" type="integer" value="2" />
    <output name="out" type="color4" />
  </nodedef>

  <!--
    Node: <ramplr>
    A left-to-right bilinear value ramp.
  -->
  <nodedef name="ND_ramplr_float" node="ramplr" nodegroup="procedural2d">
    <input name="valuel" type="float" value="0.0" />
    <input name="valuer" type="float" value="0.0" />
    <input name="texcoord" type="vector2" defaultgeomprop="UV0" />
    <output name="out" type="float" default="0.0" />
  </nodedef>
  <nodedef name="ND_ramplr_color3" node="ramplr" nodegroup="procedural2d">
    <input name="valuel" type="color3" value="0.0, 0.0, 0.0" />
    <input name="valuer" type="color3" value="0.0, 0.0, 0.0" />
    <input name="texcoord" type="vector2" defaultgeomprop="UV0" />
    <output name="out" type="color3" default="0.0, 0.0, 0.0" />
  </nodedef>
  <nodedef name="ND_ramplr_color4" node="ramplr" nodegroup="procedural2d">
    <input name="valuel" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="valuer" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="texcoord" type="vector2" defaultgeomprop="UV0" />
    <output name="out" type="color4" default="0.0, 0.0, 0.0, 0.0" />
  </nodedef>
  <nodedef name="ND_ramplr_vector2" node="ramplr" nodegroup="procedural2d">
    <input name="valuel" type="vector2" value="0.0, 0.0" />
    <input name="valuer" type="vector2" value="0.0, 0.0" />
    <input name="texcoord" type="vector2" defaultgeomprop="UV0" />
    <output name="out" type="vector2" default="0.0, 0.0" />
  </nodedef>
  <nodedef name="ND_ramplr_vector3" node="ramplr" nodegroup="procedural2d">
    <input name="valuel" type="vector3" value="0.0, 0.0, 0.0" />
    <input name="valuer" type="vector3" value="0.0, 0.0, 0.0" />
    <input name="texcoord" type="vector2" defaultgeomprop="UV0" />
    <output name="out" type="vector3" default="0.0, 0.0, 0.0" />
  </nodedef>
  <nodedef name="ND_ramplr_vector4" node="ramplr" nodegroup="procedural2d">
    <input name="valuel" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="valuer" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="texcoord" type="vector2" defaultgeomprop="UV0" />
    <output name="out" type="vector4" default="0.0, 0.0, 0.0, 0.0" />
  </nodedef>

  <!--
    Node: <ramptb>
    A top-to-bottom bilinear value ramp.
  -->
  <nodedef name="ND_ramptb_float" node="ramptb" nodegroup="procedural2d">
    <input name="valuet" type="float" value="0.0" />
    <input name="valueb" type="float" value="0.0" />
    <input name="texcoord" type="vector2" defaultgeomprop="UV0" />
    <output name="out" type="float" default="0.0" />
  </nodedef>
  <nodedef name="ND_ramptb_color3" node="ramptb" nodegroup="procedural2d">
    <input name="valuet" type="color3" value="0.0, 0.0, 0.0" />
    <input name="valueb" type="color3" value="0.0, 0.0, 0.0" />
    <input name="texcoord" type="vector2" defaultgeomprop="UV0" />
    <output name="out" type="color3" default="0.0, 0.0, 0.0" />
  </nodedef>
  <nodedef name="ND_ramptb_color4" node="ramptb" nodegroup="procedural2d">
    <input name="valuet" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="valueb" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="texcoord" type="vector2" defaultgeomprop="UV0" />
    <output name="out" type="color4" default="0.0, 0.0, 0.0, 0.0" />
  </nodedef>
  <nodedef name="ND_ramptb_vector2" node="ramptb" nodegroup="procedural2d">
    <input name="valuet" type="vector2" value="0.0, 0.0" />
    <input name="valueb" type="vector2" value="0.0, 0.0" />
    <input name="texcoord" type="vector2" defaultgeomprop="UV0" />
    <output name="out" type="vector2" default="0.0, 0.0" />
  </nodedef>
  <nodedef name="ND_ramptb_vector3" node="ramptb" nodegroup="procedural2d">
    <input name="valuet" type="vector3" value="0.0, 0.0, 0.0" />
    <input name="valueb" type="vector3" value="0.0, 0.0, 0.0" />
    <input name="texcoord" type="vector2" defaultgeomprop="UV0" />
    <output name="out" type="vector3" default="0.0, 0.0, 0.0" />
  </nodedef>
  <nodedef name="ND_ramptb_vector4" node="ramptb" nodegroup="procedural2d">
    <input name="valuet" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="valueb" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="texcoord" type="vector2" defaultgeomprop="UV0" />
    <output name="out" type="vector4" default="0.0, 0.0, 0.0, 0.0" />
  </nodedef>

  <!--
    Node: <ramp4> Supplemental Node
    A 4-corner bilinear value ramp.
  -->
  <nodedef name="ND_ramp4_float" node="ramp4" nodegroup="procedural2d">
    <input name="valuetl" type="float" value="0.0" />
    <input name="valuetr" type="float" value="0.0" />
    <input name="valuebl" type="float" value="0.0" />
    <input name="valuebr" type="float" value="0.0" />
    <input name="texcoord" type="vector2" defaultgeomprop="UV0" />
    <output name="out" type="float" default="0.0" />
  </nodedef>
  <nodedef name="ND_ramp4_color3" node="ramp4" nodegroup="procedural2d">
    <input name="valuetl" type="color3" value="0.0, 0.0, 0.0" />
    <input name="valuetr" type="color3" value="0.0, 0.0, 0.0" />
    <input name="valuebl" type="color3" value="0.0, 0.0, 0.0" />
    <input name="valuebr" type="color3" value="0.0, 0.0, 0.0" />
    <input name="texcoord" type="vector2" defaultgeomprop="UV0" />
    <output name="out" type="color3" default="0.0, 0.0, 0.0" />
  </nodedef>
  <nodedef name="ND_ramp4_color4" node="ramp4" nodegroup="procedural2d">
    <input name="valuetl" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="valuetr" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="valuebl" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="valuebr" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="texcoord" type="vector2" defaultgeomprop="UV0" />
    <output name="out" type="color4" default="0.0, 0.0, 0.0, 0.0" />
  </nodedef>
  <nodedef name="ND_ramp4_vector2" node="ramp4" nodegroup="procedural2d">
    <input name="valuetl" type="vector2" value="0.0, 0.0" />
    <input name="valuetr" type="vector2" value="0.0, 0.0" />
    <input name="valuebl" type="vector2" value="0.0, 0.0" />
    <input name="valuebr" type="vector2" value="0.0, 0.0" />
    <input name="texcoord" type="vector2" defaultgeomprop="UV0" />
    <output name="out" type="vector2" default="0.0, 0.0" />
  </nodedef>
  <nodedef name="ND_ramp4_vector3" node="ramp4" nodegroup="procedural2d">
    <input name="valuetl" type="vector3" value="0.0, 0.0, 0.0" />
    <input name="valuetr" type="vector3" value="0.0, 0.0, 0.0" />
    <input name="valuebl" type="vector3" value="0.0, 0.0, 0.0" />
    <input name="valuebr" type="vector3" value="0.0, 0.0, 0.0" />
    <input name="texcoord" type="vector2" defaultgeomprop="UV0" />
    <output name="out" type="vector3" default="0.0, 0.0, 0.0" />
  </nodedef>
  <nodedef name="ND_ramp4_vector4" node="ramp4" nodegroup="procedural2d">
    <input name="valuetl" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="valuetr" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="valuebl" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="valuebr" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="texcoord" type="vector2" defaultgeomprop="UV0" />
    <output name="out" type="vector4" default="0.0, 0.0, 0.0, 0.0" />
  </nodedef>

  <!--
    Node: <splitlr>
    A left-right split matte, split at a specified u value.
  -->
  <nodedef name="ND_splitlr_float" node="splitlr" nodegroup="procedural2d">
    <input name="valuel" type="float" value="0.0" uiname="Left" />
    <input name="valuer" type="float" value="0.0" uiname="Right" />
    <input name="center" type="float" value="0.5" uiname="Center" uimin="0.0" uimax="1.0" />
    <input name="texcoord" type="vector2" defaultgeomprop="UV0" />
    <output name="out" type="float" default="0.0" />
  </nodedef>
  <nodedef name="ND_splitlr_color3" node="splitlr" nodegroup="procedural2d">
    <input name="valuel" type="color3" value="0.0, 0.0, 0.0" uiname="Left" />
    <input name="valuer" type="color3" value="0.0, 0.0, 0.0" uiname="Right" />
    <input name="center" type="float" value="0.5" uiname="Center" uimin="0.0" uimax="1.0" />
    <input name="texcoord" type="vector2" defaultgeomprop="UV0" />
    <output name="out" type="color3" default="0.0, 0.0, 0.0" />
  </nodedef>
  <nodedef name="ND_splitlr_color4" node="splitlr" nodegroup="procedural2d">
    <input name="valuel" type="color4" value="0.0, 0.0, 0.0, 0.0" uiname="Left" />
    <input name="valuer" type="color4" value="0.0, 0.0, 0.0, 0.0" uiname="Right" />
    <input name="center" type="float" value="0.5" uiname="Center" uimin="0.0" uimax="1.0" />
    <input name="texcoord" type="vector2" defaultgeomprop="UV0" />
    <output name="out" type="color4" default="0.0, 0.0, 0.0, 0.0" />
  </nodedef>
  <nodedef name="ND_splitlr_vector2" node="splitlr" nodegroup="procedural2d">
    <input name="valuel" type="vector2" value="0.0, 0.0" uiname="Left" />
    <input name="valuer" type="vector2" value="0.0, 0.0" uiname="Right" />
    <input name="center" type="float" value="0.5" uiname="Center" uimin="0.0" uimax="1.0" />
    <input name="texcoord" type="vector2" defaultgeomprop="UV0" />
    <output name="out" type="vector2" default="0.0, 0.0" />
  </nodedef>
  <nodedef name="ND_splitlr_vector3" node="splitlr" nodegroup="procedural2d">
    <input name="valuel" type="vector3" value="0.0, 0.0, 0.0" uiname="Left" />
    <input name="valuer" type="vector3" value="0.0, 0.0, 0.0" uiname="Right" />
    <input name="center" type="float" value="0.5" uiname="Center" uimin="0.0" uimax="1.0" />
    <input name="texcoord" type="vector2" defaultgeomprop="UV0" />
    <output name="out" type="vector3" default="0.0, 0.0, 0.0" />
  </nodedef>
  <nodedef name="ND_splitlr_vector4" node="splitlr" nodegroup="procedural2d">
    <input name="valuel" type="vector4" value="0.0, 0.0, 0.0, 0.0" uiname="Left" />
    <input name="valuer" type="vector4" value="0.0, 0.0, 0.0, 0.0" uiname="Right" />
    <input name="center" type="float" value="0.5" uiname="Center" uimin="0.0" uimax="1.0" />
    <input name="texcoord" type="vector2" defaultgeomprop="UV0" />
    <output name="out" type="vector4" default="0.0, 0.0, 0.0, 0.0" />
  </nodedef>
  <!--
    Node: <splittb>
    A top-bottom split matte, split at a specified v value.
  -->
  <nodedef name="ND_splittb_float" node="splittb" nodegroup="procedural2d">
    <input name="valuet" type="float" value="0.0" uiname="Top" />
    <input name="valueb" type="float" value="0.0" uiname="Bottom" />
    <input name="center" type="float" value="0.5" uiname="Center" uimin="0.0" uimax="1.0" />
    <input name="texcoord" type="vector2" defaultgeomprop="UV0" />
    <output name="out" type="float" default="0.0" />
  </nodedef>
  <nodedef name="ND_splittb_color3" node="splittb" nodegroup="procedural2d">
    <input name="valuet" type="color3" value="0.0, 0.0, 0.0" uiname="Top" />
    <input name="valueb" type="color3" value="0.0, 0.0, 0.0" uiname="Bottom" />
    <input name="center" type="float" value="0.5" uiname="Center" uimin="0.0" uimax="1.0" />
    <input name="texcoord" type="vector2" defaultgeomprop="UV0" />
    <output name="out" type="color3" default="0.0, 0.0, 0.0" />
  </nodedef>
  <nodedef name="ND_splittb_color4" node="splittb" nodegroup="procedural2d">
    <input name="valuet" type="color4" value="0.0, 0.0, 0.0, 0.0" uiname="Top" />
    <input name="valueb" type="color4" value="0.0, 0.0, 0.0, 0.0" uiname="Bottom" />
    <input name="center" type="float" value="0.5" uiname="Center" uimin="0.0" uimax="1.0" />
    <input name="texcoord" type="vector2" defaultgeomprop="UV0" />
    <output name="out" type="color4" default="0.0, 0.0, 0.0, 0.0" />
  </nodedef>
  <nodedef name="ND_splittb_vector2" node="splittb" nodegroup="procedural2d">
    <input name="valuet" type="vector2" value="0.0, 0.0" uiname="Top" />
    <input name="valueb" type="vector2" value="0.0, 0.0" uiname="Bottom" />
    <input name="center" type="float" value="0.5" uiname="Center" uimin="0.0" uimax="1.0" />
    <input name="texcoord" type="vector2" defaultgeomprop="UV0" />
    <output name="out" type="vector2" default="0.0, 0.0" />
  </nodedef>
  <nodedef name="ND_splittb_vector3" node="splittb" nodegroup="procedural2d">
    <input name="valuet" type="vector3" value="0.0, 0.0, 0.0" uiname="Top" />
    <input name="valueb" type="vector3" value="0.0, 0.0, 0.0" uiname="Bottom" />
    <input name="center" type="float" value="0.5" uiname="Center" uimin="0.0" uimax="1.0" />
    <input name="texcoord" type="vector2" defaultgeomprop="UV0" />
    <output name="out" type="vector3" default="0.0, 0.0, 0.0" />
  </nodedef>
  <nodedef name="ND_splittb_vector4" node="splittb" nodegroup="procedural2d">
    <input name="valuet" type="vector4" value="0.0, 0.0, 0.0, 0.0" uiname="Top" />
    <input name="valueb" type="vector4" value="0.0, 0.0, 0.0, 0.0" uiname="Bottom" />
    <input name="center" type="float" value="0.5" uiname="Center" uimin="0.0" uimax="1.0" />
    <input name="texcoord" type="vector2" defaultgeomprop="UV0" />
    <output name="out" type="vector4" default="0.0, 0.0, 0.0, 0.0" />
  </nodedef>

  <!--
    Node: <noise2d>
    2D Perlin noise in 1, 2, 3 or 4 channels.
  -->
  <nodedef name="ND_noise2d_float" node="noise2d" nodegroup="procedural2d">
    <input name="amplitude" type="float" value="1.0" />
    <input name="pivot" type="float" value="0.0" />
    <input name="texcoord" type="vector2" defaultgeomprop="UV0" />
    <output name="out" type="float" default="0.0" />
  </nodedef>
  <nodedef name="ND_noise2d_color3" node="noise2d" nodegroup="procedural2d">
    <input name="amplitude" type="vector3" value="1.0, 1.0, 1.0" />
    <input name="pivot" type="float" value="0.0" />
    <input name="texcoord" type="vector2" defaultgeomprop="UV0" />
    <output name="out" type="color3" default="0.0, 0.0, 0.0" />
  </nodedef>
  <nodedef name="ND_noise2d_color4" node="noise2d" nodegroup="procedural2d">
    <input name="amplitude" type="vector4" value="1.0, 1.0, 1.0, 1.0" />
    <input name="pivot" type="float" value="0.0" />
    <input name="texcoord" type="vector2" defaultgeomprop="UV0" />
    <output name="out" type="color4" default="0.0, 0.0, 0.0, 0.0" />
  </nodedef>
  <nodedef name="ND_noise2d_vector2" node="noise2d" nodegroup="procedural2d">
    <input name="amplitude" type="vector2" value="1.0, 1.0" />
    <input name="pivot" type="float" value="0.0" />
    <input name="texcoord" type="vector2" defaultgeomprop="UV0" />
    <output name="out" type="vector2" default="0.0, 0.0" />
  </nodedef>
  <nodedef name="ND_noise2d_vector3" node="noise2d" nodegroup="procedural2d">
    <input name="amplitude" type="vector3" value="1.0, 1.0, 1.0" />
    <input name="pivot" type="float" value="0.0" />
    <input name="texcoord" type="vector2" defaultgeomprop="UV0" />
    <output name="out" type="vector3" default="0.0, 0.0, 0.0" />
  </nodedef>
  <nodedef name="ND_noise2d_vector4" node="noise2d" nodegroup="procedural2d">
    <input name="amplitude" type="vector4" value="1.0, 1.0, 1.0, 1.0" />
    <input name="pivot" type="float" value="0.0" />
    <input name="texcoord" type="vector2" defaultgeomprop="UV0" />
    <output name="out" type="vector4" default="0.0, 0.0, 0.0, 0.0" />
  </nodedef>
  <nodedef name="ND_noise2d_color3FA" node="noise2d" nodegroup="procedural2d">
    <input name="amplitude" type="float" value="1.0" />
    <input name="pivot" type="float" value="0.0" />
    <input name="texcoord" type="vector2" defaultgeomprop="UV0" />
    <output name="out" type="color3" default="0.0, 0.0, 0.0" />
  </nodedef>
  <nodedef name="ND_noise2d_color4FA" node="noise2d" nodegroup="procedural2d">
    <input name="amplitude" type="float" value="1.0" />
    <input name="pivot" type="float" value="0.0" />
    <input name="texcoord" type="vector2" defaultgeomprop="UV0" />
    <output name="out" type="color4" default="0.0, 0.0, 0.0, 0.0" />
  </nodedef>
  <nodedef name="ND_noise2d_vector2FA" node="noise2d" nodegroup="procedural2d">
    <input name="amplitude" type="float" value="1.0" />
    <input name="pivot" type="float" value="0.0" />
    <input name="texcoord" type="vector2" defaultgeomprop="UV0" />
    <output name="out" type="vector2" default="0.0, 0.0" />
  </nodedef>
  <nodedef name="ND_noise2d_vector3FA" node="noise2d" nodegroup="procedural2d">
    <input name="amplitude" type="float" value="1.0" />
    <input name="pivot" type="float" value="0.0" />
    <input name="texcoord" type="vector2" defaultgeomprop="UV0" />
    <output name="out" type="vector3" default="0.0, 0.0, 0.0" />
  </nodedef>
  <nodedef name="ND_noise2d_vector4FA" node="noise2d" nodegroup="procedural2d">
    <input name="amplitude" type="float" value="1.0" />
    <input name="pivot" type="float" value="0.0" />
    <input name="texcoord" type="vector2" defaultgeomprop="UV0" />
    <output name="out" type="vector4" default="0.0, 0.0, 0.0, 0.0" />
  </nodedef>

  <!--
    Node: <noise3d>
    3D Perlin noise in 1, 2, 3 or 4 channels.
  -->
  <nodedef name="ND_noise3d_float" node="noise3d" nodegroup="procedural3d">
    <input name="amplitude" type="float" value="1.0" />
    <input name="pivot" type="float" value="0.0" />
    <input name="position" type="vector3" defaultgeomprop="Pobject" />
    <output name="out" type="float" default="0.0" />
  </nodedef>
  <nodedef name="ND_noise3d_color3" node="noise3d" nodegroup="procedural3d">
    <input name="amplitude" type="vector3" value="1.0, 1.0, 1.0" />
    <input name="pivot" type="float" value="0.0" />
    <input name="position" type="vector3" defaultgeomprop="Pobject" />
    <output name="out" type="color3" default="0.0, 0.0, 0.0" />
  </nodedef>
  <nodedef name="ND_noise3d_color4" node="noise3d" nodegroup="procedural3d">
    <input name="amplitude" type="vector4" value="1.0, 1.0, 1.0, 1.0" />
    <input name="pivot" type="float" value="0.0" />
    <input name="position" type="vector3" defaultgeomprop="Pobject" />
    <output name="out" type="color4" default="0.0, 0.0, 0.0, 0.0" />
  </nodedef>
  <nodedef name="ND_noise3d_vector2" node="noise3d" nodegroup="procedural3d">
    <input name="amplitude" type="vector2" value="1.0, 1.0" />
    <input name="pivot" type="float" value="0.0" />
    <input name="position" type="vector3" defaultgeomprop="Pobject" />
    <output name="out" type="vector2" default="0.0, 0.0" />
  </nodedef>
  <nodedef name="ND_noise3d_vector3" node="noise3d" nodegroup="procedural3d">
    <input name="amplitude" type="vector3" value="1.0, 1.0, 1.0" />
    <input name="pivot" type="float" value="0.0" />
    <input name="position" type="vector3" defaultgeomprop="Pobject" />
    <output name="out" type="vector3" default="0.0, 0.0, 0.0" />
  </nodedef>
  <nodedef name="ND_noise3d_vector4" node="noise3d" nodegroup="procedural3d">
    <input name="amplitude" type="vector4" value="1.0, 1.0, 1.0, 1.0" />
    <input name="pivot" type="float" value="0.0" />
    <input name="position" type="vector3" defaultgeomprop="Pobject" />
    <output name="out" type="vector4" default="0.0, 0.0, 0.0, 0.0" />
  </nodedef>
  <nodedef name="ND_noise3d_color3FA" node="noise3d" nodegroup="procedural3d">
    <input name="amplitude" type="float" value="1.0" />
    <input name="pivot" type="float" value="0.0" />
    <input name="position" type="vector3" defaultgeomprop="Pobject" />
    <output name="out" type="color3" default="0.0, 0.0, 0.0" />
  </nodedef>
  <nodedef name="ND_noise3d_color4FA" node="noise3d" nodegroup="procedural3d">
    <input name="amplitude" type="float" value="1.0" />
    <input name="pivot" type="float" value="0.0" />
    <input name="position" type="vector3" defaultgeomprop="Pobject" />
    <output name="out" type="color4" default="0.0, 0.0, 0.0, 0.0" />
  </nodedef>
  <nodedef name="ND_noise3d_vector2FA" node="noise3d" nodegroup="procedural3d">
    <input name="amplitude" type="float" value="1.0" />
    <input name="pivot" type="float" value="0.0" />
    <input name="position" type="vector3" defaultgeomprop="Pobject" />
    <output name="out" type="vector2" default="0.0, 0.0" />
  </nodedef>
  <nodedef name="ND_noise3d_vector3FA" node="noise3d" nodegroup="procedural3d">
    <input name="amplitude" type="float" value="1.0" />
    <input name="pivot" type="float" value="0.0" />
    <input name="position" type="vector3" defaultgeomprop="Pobject" />
    <output name="out" type="vector3" default="0.0, 0.0, 0.0" />
  </nodedef>
  <nodedef name="ND_noise3d_vector4FA" node="noise3d" nodegroup="procedural3d">
    <input name="amplitude" type="float" value="1.0" />
    <input name="pivot" type="float" value="0.0" />
    <input name="position" type="vector3" defaultgeomprop="Pobject" />
    <output name="out" type="vector4" default="0.0, 0.0, 0.0, 0.0" />
  </nodedef>

  <!--
    Node: <fractal3d>
    3D Fractal noise in 1, 2, 3 or 4 channels.
  -->
  <nodedef name="ND_fractal3d_float" node="fractal3d" nodegroup="procedural3d">
    <input name="amplitude" type="float" value="1.0" />
    <input name="octaves" type="integer" value="3" />
    <input name="lacunarity" type="float" value="2.0" />
    <input name="diminish" type="float" value="0.5" />
    <input name="position" type="vector3" defaultgeomprop="Pobject" />
    <output name="out" type="float" default="0.0" />
  </nodedef>
  <nodedef name="ND_fractal3d_color3" node="fractal3d" nodegroup="procedural3d">
    <input name="amplitude" type="vector3" value="1.0, 1.0, 1.0" />
    <input name="octaves" type="integer" value="3" />
    <input name="lacunarity" type="float" value="2.0" />
    <input name="diminish" type="float" value="0.5" />
    <input name="position" type="vector3" defaultgeomprop="Pobject" />
    <output name="out" type="color3" default="0.0, 0.0, 0.0" />
  </nodedef>
  <nodedef name="ND_fractal3d_color4" node="fractal3d" nodegroup="procedural3d">
    <input name="amplitude" type="vector4" value="1.0, 1.0, 1.0, 1.0" />
    <input name="octaves" type="integer" value="3" />
    <input name="lacunarity" type="float" value="2.0" />
    <input name="diminish" type="float" value="0.5" />
    <input name="position" type="vector3" defaultgeomprop="Pobject" />
    <output name="out" type="color4" default="0.0, 0.0, 0.0, 0.0" />
  </nodedef>
  <nodedef name="ND_fractal3d_vector2" node="fractal3d" nodegroup="procedural3d">
    <input name="amplitude" type="vector2" value="1.0, 1.0" />
    <input name="octaves" type="integer" value="3" />
    <input name="lacunarity" type="float" value="2.0" />
    <input name="diminish" type="float" value="0.5" />
    <input name="position" type="vector3" defaultgeomprop="Pobject" />
    <output name="out" type="vector2" default="0.0, 0.0" />
  </nodedef>
  <nodedef name="ND_fractal3d_vector3" node="fractal3d" nodegroup="procedural3d">
    <input name="amplitude" type="vector3" value="1.0, 1.0, 1.0" />
    <input name="octaves" type="integer" value="3" />
    <input name="lacunarity" type="float" value="2.0" />
    <input name="diminish" type="float" value="0.5" />
    <input name="position" type="vector3" defaultgeomprop="Pobject" />
    <output name="out" type="vector3" default="0.0, 0.0, 0.0" />
  </nodedef>
  <nodedef name="ND_fractal3d_vector4" node="fractal3d" nodegroup="procedural3d">
    <input name="amplitude" type="vector4" value="1.0, 1.0, 1.0, 1.0" />
    <input name="octaves" type="integer" value="3" />
    <input name="lacunarity" type="float" value="2.0" />
    <input name="diminish" type="float" value="0.5" />
    <input name="position" type="vector3" defaultgeomprop="Pobject" />
    <output name="out" type="vector4" default="0.0, 0.0, 0.0, 0.0" />
  </nodedef>
  <nodedef name="ND_fractal3d_color3FA" node="fractal3d" nodegroup="procedural3d">
    <input name="amplitude" type="float" value="1.0" />
    <input name="octaves" type="integer" value="3" />
    <input name="lacunarity" type="float" value="2.0" />
    <input name="diminish" type="float" value="0.5" />
    <input name="position" type="vector3" defaultgeomprop="Pobject" />
    <output name="out" type="color3" default="0.0, 0.0, 0.0" />
  </nodedef>
  <nodedef name="ND_fractal3d_color4FA" node="fractal3d" nodegroup="procedural3d">
    <input name="amplitude" type="float" value="1.0" />
    <input name="octaves" type="integer" value="3" />
    <input name="lacunarity" type="float" value="2.0" />
    <input name="diminish" type="float" value="0.5" />
    <input name="position" type="vector3" defaultgeomprop="Pobject" />
    <output name="out" type="color4" default="0.0, 0.0, 0.0, 0.0" />
  </nodedef>
  <nodedef name="ND_fractal3d_vector2FA" node="fractal3d" nodegroup="procedural3d">
    <input name="amplitude" type="float" value="1.0" />
    <input name="octaves" type="integer" value="3" />
    <input name="lacunarity" type="float" value="2.0" />
    <input name="diminish" type="float" value="0.5" />
    <input name="position" type="vector3" defaultgeomprop="Pobject" />
    <output name="out" type="vector2" default="0.0, 0.0" />
  </nodedef>
  <nodedef name="ND_fractal3d_vector3FA" node="fractal3d" nodegroup="procedural3d">
    <input name="amplitude" type="float" value="1.0" />
    <input name="octaves" type="integer" value="3" />
    <input name="lacunarity" type="float" value="2.0" />
    <input name="diminish" type="float" value="0.5" />
    <input name="position" type="vector3" defaultgeomprop="Pobject" />
    <output name="out" type="vector3" default="0.0, 0.0, 0.0" />
  </nodedef>
  <nodedef name="ND_fractal3d_vector4FA" node="fractal3d" nodegroup="procedural3d">
    <input name="amplitude" type="float" value="1.0" />
    <input name="octaves" type="integer" value="3" />
    <input name="lacunarity" type="float" value="2.0" />
    <input name="diminish" type="float" value="0.5" />
    <input name="position" type="vector3" defaultgeomprop="Pobject" />
    <output name="out" type="vector4" default="0.0, 0.0, 0.0, 0.0" />
  </nodedef>

  <!--
    Node: <cellnoise2d>
    2D cellular noise in 1 channel.
  -->
  <nodedef name="ND_cellnoise2d_float" node="cellnoise2d" nodegroup="procedural2d">
    <input name="texcoord" type="vector2" defaultgeomprop="UV0" />
    <output name="out" type="float" default="0.0" />
  </nodedef>

  <!--
    Node: <cellnoise3d>
    3D cellular noise in 1 channel.
  -->
  <nodedef name="ND_cellnoise3d_float" node="cellnoise3d" nodegroup="procedural3d">
    <input name="position" type="vector3" defaultgeomprop="Pobject" />
    <output name="out" type="float" default="0.0" />
  </nodedef>

  <!--
    Node: <worleynoise2d>
    2D Worley (voronoi) noise in 1, 2 or 3 channels.
  -->
  <nodedef name="ND_worleynoise2d_float" node="worleynoise2d" nodegroup="procedural2d">
    <input name="texcoord" type="vector2" defaultgeomprop="UV0" />
    <input name="jitter" type="float" value="1.0" />
    <input name="style" uiname="Cell Style" type="integer" value="0" enum="Distance,Solid" enumvalues="0,1" />
    <output name="out" type="float" default="0.0" />
  </nodedef>
  <nodedef name="ND_worleynoise2d_vector2" node="worleynoise2d" nodegroup="procedural2d">
    <input name="texcoord" type="vector2" defaultgeomprop="UV0" />
    <input name="jitter" type="float" value="1.0" />
    <input name="style" uiname="Cell Style" type="integer" value="0" enum="Distance,Solid" enumvalues="0,1" />
    <output name="out" type="vector2" default="0.0, 0.0" />
  </nodedef>
  <nodedef name="ND_worleynoise2d_vector3" node="worleynoise2d" nodegroup="procedural2d">
    <input name="texcoord" type="vector2" defaultgeomprop="UV0" />
    <input name="jitter" type="float" value="1.0" />
    <input name="style" uiname="Cell Style" type="integer" value="0" enum="Distance,Solid" enumvalues="0,1" />
    <output name="out" type="vector3" default="0.0, 0.0, 0.0" />
  </nodedef>

  <!--
    Node: <worleynoise3d>
    3D Worley (voronoi) noise in 1, 2 or 3 channels.
  -->
  <nodedef name="ND_worleynoise3d_float" node="worleynoise3d" nodegroup="procedural3d">
    <input name="position" type="vector3" defaultgeomprop="Pobject" />
    <input name="jitter" type="float" value="1.0" />
    <input name="style" uiname="Cell Style" type="integer" value="0" enum="Distance,Solid" enumvalues="0,1" />
    <output name="out" type="float" default="0.0" />
  </nodedef>
  <nodedef name="ND_worleynoise3d_vector2" node="worleynoise3d" nodegroup="procedural3d">
    <input name="position" type="vector3" defaultgeomprop="Pobject" />
    <input name="jitter" type="float" value="1.0" />
    <input name="style" uiname="Cell Style" type="integer" value="0" enum="Distance,Solid" enumvalues="0,1" />
    <output name="out" type="vector2" default="0.0, 0.0" />
  </nodedef>
  <nodedef name="ND_worleynoise3d_vector3" node="worleynoise3d" nodegroup="procedural3d">
    <input name="position" type="vector3" defaultgeomprop="Pobject" />
    <input name="jitter" type="float" value="1.0" />
    <input name="style" uiname="Cell Style" type="integer" value="0" enum="Distance,Solid" enumvalues="0,1" />
    <output name="out" type="vector3" default="0.0, 0.0, 0.0" />
  </nodedef>

  <!--
    Node: <unifiednoise2d>
    Combines the available noises into an artist-friendly node, adding controls
    to adjust frequency, offset, jitter, etc... Where possible controls have
    been unified between noises; any noise-specific controls should live in a
    dedicated folder. These folders should be hidden by artist tools, unless their
    corresponding noise type is selected.
    
    The noise types are:
    
       0 - Perlin (noise2d/noise3d)
       1 - Cell (cellnoise2d/cellnoise3d)
       2 - Worley (worley2d/worley3d)
       3 - Fractal (fractal3d)
    
    The output is 1 channel, with controls to adjust the output range.
  -->
  <nodedef name="ND_unifiednoise2d_float" node="unifiednoise2d" nodegroup="procedural2d">
    <input name="texcoord" type="vector2" uifolder="Common" defaultgeomprop="UV0" doc="The input 2d space. Default is the first texture coordinates." />
    <input name="freq" type="vector2" uiname="Frequency" uifolder="Common" value="1, 1" doc="Adjusts the noise frequency, with higher values producing smaller noise shapes. Default is (1,1)." />
    <input name="offset" type="vector2" uiname="Offset" uifolder="Common" value="0, 0" doc="Shift the noise in 2d space. Default is (0,0)." />
    <input name="jitter" type="float" uiname="Jitter" uifolder="Common" uisoftmin="0.0" uisoftmax="1.0" value="1" doc="Adjust uniformity of Worley noise; for other noise types jitters the results." />
    <input name="outmin" type="float" uiname="Output Min" uifolder="Post Process" value="0" doc="The lowest values fit to the noise. Default is 0.0." />
    <input name="outmax" type="float" uiname="Output Max" uifolder="Post Process" value="1" doc="The highest values fit to the noise. Default is 1.0." />
    <input name="clampoutput" type="boolean" uiname="Clamp Output" uifolder="Post Process" value="true" doc="Clamp the output to the min and max output values." />
    <input name="octaves" type="integer" uiname="Octaves" uifolder="Fractal" value="3" doc="The number of octaves of Fractal noise to be generated. Default is 3." />
    <input name="lacunarity" type="float" uiname="Lacunarity" uifolder="Fractal" value="2" doc="The exponential scale between successive octaves of Fractal noise. Default is 2.0." />
    <input name="diminish" type="float" uiname="Diminish" uifolder="Fractal" uisoftmin="0.0" uisoftmax="1.0" value="0.5" doc="The rate at which noise amplitude is diminished for each octave of Fractal noise. Default is 0.5." />
    <input name="type" type="integer" uiname="Noise Type" uifolder="Common" uisoftmin="0" uisoftmax="3" value="0" enum="Perlin,Cell,Worley,Fractal" enumvalues="0,1,2,3" doc="Menu to select the type of noise: Perlin, Cell, Worley, or Fractal. Default is Perlin." />
    <input name="style" uiname="Worley Cell Style" type="integer" value="0" enum="Distance,Solid" enumvalues="0,1" doc="Sets the style of cell used when Noise Type is set to Worley." />
    <output name="out" type="float" />
  </nodedef>

  <!--
    Node: <unifiednoise3d>
    The 3d flavor of <unifiednoise2d>.
  -->
  <nodedef name="ND_unifiednoise3d_float" node="unifiednoise3d" nodegroup="procedural3d">
    <input name="position" type="vector3" uifolder="Common" defaultgeomprop="Pobject" doc="The input 3d space. Default is position in object-space." />
    <input name="freq" type="vector3" uiname="Frequency" uifolder="Common" value="1, 1, 1" doc="Adjusts the noise frequency, with higher values producing smaller noise shapes. Default is (1,1,1)." />
    <input name="offset" type="vector3" uiname="Offset" uifolder="Common" value="0, 0, 0" doc="Shift the noise in 3d space. Default is (0,0,0)." />
    <input name="jitter" type="float" uiname="Jitter" uifolder="Common" uisoftmin="0.0" uisoftmax="1.0" value="1" doc="Adjust uniformity of Worley noise; for other noise types jitters the results." />
    <input name="outmin" type="float" uiname="Output Min" uifolder="Post Process" value="0" doc="The lowest values fit to the noise. Default is 0.0." />
    <input name="outmax" type="float" uiname="Output Max" uifolder="Post Process" value="1" doc="The highest values fit to the noise. Default is 1.0." />
    <input name="clampoutput" type="boolean" uiname="Clamp Output" uifolder="Post Process" value="true" doc="Clamp the output to the min and max output values." />
    <input name="octaves" type="integer" uiname="Octaves" uifolder="Fractal" value="3" doc="The number of octaves of Fractal noise to be generated. Default is 3." />
    <input name="lacunarity" type="float" uiname="Lacunarity" uifolder="Fractal" value="2" doc="The exponential scale between successive octaves of Fractal noise. Default is 2.0." />
    <input name="diminish" type="float" uiname="Diminish" uifolder="Fractal" uisoftmin="0.0" uisoftmax="1.0" value="0.5" doc="The rate at which noise amplitude is diminished for each octave of Fractal noise. Default is 0.5." />
    <input name="type" type="integer" uiname="Noise Type" uifolder="Common" uisoftmin="0" uisoftmax="3" value="0" enum="Perlin,Cell,Worley,Fractal" enumvalues="0,1,2,3" doc="Menu to select the type of noise: Perlin, Cell, Worley, or Fractal. Default is Perlin." />
    <input name="style" uiname="Worley Cell Style" type="integer" value="0" enum="Distance,Solid" enumvalues="0,1" doc="Sets the style of cell used when Noise Type is set to Worley." />
    <output name="out" type="float" />
  </nodedef>

  <!--
    Node: <randomfloat>
    Produces a randomized float, based on an 'input' signal and 'seed' value.
  -->
  <nodedef name="ND_randomfloat_float" node="randomfloat" nodegroup="procedural">
    <input name="in" type="float" uiname="Input" value="0.0" doc="Initial randomization seed." />
    <input name="min" type="float" uiname="Minimum" value="0.0" doc="The minimum output value." />
    <input name="max" type="float" uiname="Maximum" value="1.0" doc="The maximum output value." />
    <input name="seed" type="integer" uiname="Seed" value="0" doc="Additional seed." />
    <output name="out" type="float" />
  </nodedef>
  <nodedef name="ND_randomfloat_integer" node="randomfloat" nodegroup="procedural">
    <input name="in" type="integer" uiname="Input" value="0" />
    <input name="min" type="float" uiname="Minimum" value="0.0" />
    <input name="max" type="float" uiname="Maximum" value="1.0" />
    <input name="seed" type="integer" uiname="Seed" value="0" />
    <output name="out" type="float" />
  </nodedef>

  <!--
    Node: <randomcolor>
    Produces a randomized RGB color, based on an 'input' signal and 'seed' value.
  -->
  <nodedef name="ND_randomcolor_float" node="randomcolor" nodegroup="procedural3d">
    <input name="in" type="float" uiname="Input" uisoftmin="0.0" uisoftmax="10.0" value="0.0" />
    <input name="huelow" type="float" uiname="Hue Low" uisoftmin="0.0" uisoftmax="1.0" value="0" />
    <input name="huehigh" type="float" uiname="Hue High" uisoftmin="0.0" uisoftmax="1.0" value="1" />
    <input name="saturationlow" type="float" uiname="Saturation Low" uisoftmin="0.0" uisoftmax="1.0" value="0.825" />
    <input name="saturationhigh" type="float" uiname="Saturation High" uisoftmin="0.0" uisoftmax="1.0" value="1" />
    <input name="brightnesslow" type="float" uiname="Brightness Low" uisoftmin="0.0" uisoftmax="1.0" value="1" />
    <input name="brightnesshigh" type="float" uiname="Brightness High" uisoftmin="0.0" uisoftmax="1.0" value="1" />
    <input name="seed" type="integer" uiname="Seed" value="0" />
    <output name="out" type="color3" />
  </nodedef>
  <nodedef name="ND_randomcolor_integer" node="randomcolor" nodegroup="procedural3d">
    <input name="in" type="integer" uiname="Input" uisoftmin="0" uisoftmax="10" value="0" />
    <input name="huelow" type="float" uiname="Hue Low" uisoftmin="0.0" uisoftmax="1.0" value="0" />
    <input name="huehigh" type="float" uiname="Hue High" uisoftmin="0.0" uisoftmax="1.0" value="1" />
    <input name="saturationlow" type="float" uiname="Saturation Low" uisoftmin="0.0" uisoftmax="1.0" value="0.825" />
    <input name="saturationhigh" type="float" uiname="Saturation High" uisoftmin="0.0" uisoftmax="1.0" value="1" />
    <input name="brightnesslow" type="float" uiname="Brightness Low" uisoftmin="0.0" uisoftmax="1.0" value="1" />
    <input name="brightnesshigh" type="float" uiname="Brightness High" uisoftmin="0.0" uisoftmax="1.0" value="1" />
    <input name="seed" type="integer" uiname="Seed" value="0" />
    <output name="out" type="color3" />
  </nodedef>

  <!--
    Node: <checkerboard>
    A 2D checkerboard pattern.
  -->
  <nodedef name="ND_checkerboard_color3" node="checkerboard" nodegroup="procedural2d">
    <input name="color1" type="color3" uiname="Color 1" value="1.0, 1.0, 1.0" doc="The first color used in the checkerboard pattern." />
    <input name="color2" type="color3" uiname="Color 2" value="0.0, 0.0, 0.0" doc="The second color used in the checkerboard pattern." />
    <input name="uvtiling" type="vector2" uiname="UV Tiling" value="8, 8" doc="The tiling of the checkerboard pattern along each axis, with higher values producing smaller squares. Default is (8, 8)." />
    <input name="uvoffset" type="vector2" uiname="UV Offset" value="0, 0" doc="The offset of the checkerboard pattern along each axis. Default is (0, 0)." />
    <input name="texcoord" type="vector2" uiname="Texture Coordinates" defaultgeomprop="UV0" doc="The input 2d space. Default is the first texture coordinates." />
    <output name="out" type="color3" />
  </nodedef>

  <!--
    Node: <line>
    Returns 1 if texcoord is at less than radius distance from a line segment defined by point1 and point2; otherwise returns 0.
    Segment ends will be rounded.
    Uses formulas from Inigo Quilez SDF samples (iquilezles.org)
  -->
  <nodedef name="ND_line_float" node="line" nodegroup="procedural2d">
    <input name="texcoord" type="vector2" defaultgeomprop="UV0" />
    <input name="center" type="vector2" value="0, 0" />
    <input name="radius" type="float" value="0.1" />
    <input name="point1" type="vector2" value="0.25, 0.25" />
    <input name="point2" type="vector2" value="0.75, 0.75" />
    <output name="out" type="float" />
  </nodedef>

  <!--
    Node: <circle>
    Returns 1 if texcoord is inside a circle defined by center and radius; otherwise returns 0.
  -->
  <nodedef name="ND_circle_float" node="circle" nodegroup="procedural2d">
    <input name="texcoord" type="vector2" defaultgeomprop="UV0" />
    <input name="center" type="vector2" value="0, 0" />
    <input name="radius" type="float" value="0.5" />
    <output name="out" type="float" />
  </nodedef>

  <!--
    Node: <cloverleaf>
    Returns 1 if texcoord is inside a cloverleaf shape inscribed by a circle defined by center and radius; otherwise returns 0.
  -->
  <nodedef name="ND_cloverleaf_float" node="cloverleaf" nodegroup="procedural2d">
    <input name="texcoord" type="vector2" defaultgeomprop="UV0" />
    <input name="center" type="vector2" value="0, 0" />
    <input name="radius" type="float" value="0.5" />
    <output name="out" type="float" />
  </nodedef>

  <!--
    Node: <hexagon>
    Returns 1 if texcoord is inside a hexagon shape inscribed by a circle defined by center and radius; otherwise returns 0.
    Uses formulas from Inigo Quilez SDF samples (iquilezles.org)
  -->
  <nodedef name="ND_hexagon_float" node="hexagon" nodegroup="procedural2d">
    <input name="texcoord" type="vector2" defaultgeomprop="UV0" />
    <input name="center" type="vector2" value="0, 0" />
    <input name="radius" type="float" value="0.5" />
    <output name="out" type="float" />
  </nodedef>

  <!--
    Node: <grid>
    Creates a grid pattern with the given tiling, offset, and line thickness.
    Pattern can be regular or staggered.
  -->
  <nodedef name="ND_grid_color3" node="grid" nodegroup="procedural2d">
    <input name="texcoord" type="vector2" defaultgeomprop="UV0" />
    <input name="uvtiling" type="vector2" value="1.0, 1.0" />
    <input name="uvoffset" type="vector2" value="0.0, 0.0" />
    <input name="thickness" type="float" value="0.05" />
    <input name="staggered" type="boolean" value="false" />
    <output name="out" type="color3" />
  </nodedef>

  <!--
    Node: <crosshatch>
    Creates a crosshatch pattern with the given tiling, offset, and line thickness.
    Pattern can be regular or staggered.
  -->
  <nodedef name="ND_crosshatch_color3" node="crosshatch" nodegroup="procedural2d">
    <input name="texcoord" type="vector2" defaultgeomprop="UV0" />
    <input name="uvtiling" type="vector2" value="1.0, 1.0" />
    <input name="uvoffset" type="vector2" value="0.0, 0.0" />
    <input name="thickness" type="float" value="0.05" />
    <input name="staggered" type="boolean" value="false" />
    <output name="out" type="color3" />
  </nodedef>

  <!--
    Node: <tiledcircles>
    Creates a black and white pattern of circles with a defined tiling and size (diameter).
    Pattern can be regular or staggered.
  -->
  <nodedef name="ND_tiledcircles_color3" node="tiledcircles" nodegroup="procedural2d">
    <input name="texcoord" type="vector2" defaultgeomprop="UV0" />
    <input name="uvtiling" type="vector2" value="1.0, 1.0" />
    <input name="uvoffset" type="vector2" value="0.0, 0.0" />
    <input name="size" type="float" value="0.5" />
    <input name="staggered" type="boolean" value="false" />
    <output name="out" type="color3" />
  </nodedef>

  <!--
    Node: <tiledcloverleafs>
    Creates a black and white pattern of cloverleafs with a defined tiling and size (diameter of the circles circumscribing the shape).
    Pattern can be regular or staggered.
  -->
  <nodedef name="ND_tiledcloverleafs_color3" node="tiledcloverleafs" nodegroup="procedural2d">
    <input name="texcoord" type="vector2" defaultgeomprop="UV0" />
    <input name="uvtiling" type="vector2" value="1.0, 1.0" />
    <input name="uvoffset" type="vector2" value="0.0, 0.0" />
    <input name="size" type="float" value="0.5" />
    <input name="staggered" type="boolean" value="false" />
    <output name="out" type="color3" />
  </nodedef>

  <!--
    Node: <tiledhexagons>
    Creates a black and white pattern of hexagons with a defined tiling and size (diameter of the circles circumscribing the shape).
    Pattern can be regular or staggered.
  -->
  <nodedef name="ND_tiledhexagons_color3" node="tiledhexagons" nodegroup="procedural2d">
    <input name="texcoord" type="vector2" defaultgeomprop="UV0" />
    <input name="uvtiling" type="vector2" value="1.0, 1.0" />
    <input name="uvoffset" type="vector2" value="0.0, 0.0" />
    <input name="size" type="float" value="0.5" />
    <input name="staggered" type="boolean" value="false" />
    <output name="out" type="color3" />
  </nodedef>

  <!-- ======================================================================== -->
  <!-- Geometric nodes                                                          -->
  <!-- ======================================================================== -->

  <!--
    Node: <position>
    The geometric position associated with the currently processed data,
    as defined in a specific coordinate space.
  -->
  <nodedef name="ND_position_vector3" node="position" nodegroup="geometric">
    <input name="space" type="string" value="object" enum="model,object,world" uniform="true" />
    <output name="out" type="vector3" default="0.0, 0.0, 0.0" />
  </nodedef>

  <!--
    Node: <normal>
    The geometric normal associated with the currently processed data,
    as defined in a specific coordinate space.
  -->
  <nodedef name="ND_normal_vector3" node="normal" nodegroup="geometric">
    <input name="space" type="string" value="object" enum="model,object,world" uniform="true" />
    <output name="out" type="vector3" default="0.0, 1.0, 0.0" />
  </nodedef>

  <!--
    Node: <tangent>
    The geometric tangent vector associated with the currently processed data,
    as defined in a specific coordinate space.
  -->
  <nodedef name="ND_tangent_vector3" node="tangent" nodegroup="geometric">
    <input name="space" type="string" value="object" enum="model,object,world" uniform="true" />
    <input name="index" type="integer" value="0" uniform="true" />
    <output name="out" type="vector3" default="1.0, 0.0, 0.0" />
  </nodedef>

  <!--
    Node: <bitangent>
    The geometric bitangent vector associated with the currently processed data,
    as defined in a specific coordinate space.
  -->
  <nodedef name="ND_bitangent_vector3" node="bitangent" nodegroup="geometric">
    <input name="space" type="string" value="object" enum="model,object,world" uniform="true" />
    <input name="index" type="integer" value="0" uniform="true" />
    <output name="out" type="vector3" default="0.0, 0.0, 1.0" />
  </nodedef>

  <!--
    Node: <texcoord>
    The full 2D or 3D texture coordinates associated with the currently processed data.
  -->
  <nodedef name="ND_texcoord_vector2" node="texcoord" nodegroup="geometric">
    <input name="index" type="integer" value="0" uniform="true" />
    <output name="out" type="vector2" default="0.0, 0.0" />
  </nodedef>
  <nodedef name="ND_texcoord_vector3" node="texcoord" nodegroup="geometric">
    <input name="index" type="integer" value="0" uniform="true" />
    <output name="out" type="vector3" default="0.0, 0.0, 0.0" />
  </nodedef>

  <!--
    Node: <geomcolor>
    The color associated with the current geometry at the current position, generally
    bound via per-vertex color values.
  -->
  <nodedef name="ND_geomcolor_float" node="geomcolor" nodegroup="geometric">
    <input name="index" type="integer" value="0" uniform="true" />
    <output name="out" type="float" default="0.0" />
  </nodedef>
  <nodedef name="ND_geomcolor_color3" node="geomcolor" nodegroup="geometric">
    <input name="index" type="integer" value="0" uniform="true" />
    <output name="out" type="color3" default="0.0, 0.0, 0.0" />
  </nodedef>
  <nodedef name="ND_geomcolor_color4" node="geomcolor" nodegroup="geometric">
    <input name="index" type="integer" value="0" uniform="true" />
    <output name="out" type="color4" default="0.0, 0.0, 0.0, 0.0" />
  </nodedef>

  <!--
    Node: <geompropvalue>
    The value of the specified geometric property for the current geometry.
  -->
  <nodedef name="ND_geompropvalue_integer" node="geompropvalue" nodegroup="geometric">
    <input name="geomprop" type="string" value="" uniform="true" />
    <input name="default" type="integer" value="0" />
    <output name="out" type="integer" default="0" />
  </nodedef>
  <nodedef name="ND_geompropvalue_boolean" node="geompropvalue" nodegroup="geometric">
    <input name="geomprop" type="string" value="" uniform="true" />
    <input name="default" type="boolean" value="false" />
    <output name="out" type="boolean" default="false" />
  </nodedef>
  <nodedef name="ND_geompropvalue_float" node="geompropvalue" nodegroup="geometric">
    <input name="geomprop" type="string" value="" uniform="true" />
    <input name="default" type="float" value="0.0" />
    <output name="out" type="float" default="0.0" />
  </nodedef>
  <nodedef name="ND_geompropvalue_color3" node="geompropvalue" nodegroup="geometric">
    <input name="geomprop" type="string" value="" uniform="true" />
    <input name="default" type="color3" value="0.0, 0.0, 0.0" />
    <output name="out" type="color3" default="0.0, 0.0, 0.0" />
  </nodedef>
  <nodedef name="ND_geompropvalue_color4" node="geompropvalue" nodegroup="geometric">
    <input name="geomprop" type="string" value="" uniform="true" />
    <input name="default" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <output name="out" type="color4" default="0.0, 0.0, 0.0, 0.0" />
  </nodedef>
  <nodedef name="ND_geompropvalue_vector2" node="geompropvalue" nodegroup="geometric">
    <input name="geomprop" type="string" value="" uniform="true" />
    <input name="default" type="vector2" value="0.0, 0.0" />
    <output name="out" type="vector2" default="0.0, 0.0" />
  </nodedef>
  <nodedef name="ND_geompropvalue_vector3" node="geompropvalue" nodegroup="geometric">
    <input name="geomprop" type="string" value="" uniform="true" />
    <input name="default" type="vector3" value="0.0, 0.0, 0.0" />
    <output name="out" type="vector3" default="0.0, 0.0, 0.0" />
  </nodedef>
  <nodedef name="ND_geompropvalue_vector4" node="geompropvalue" nodegroup="geometric">
    <input name="geomprop" type="string" value="" uniform="true" />
    <input name="default" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <output name="out" type="vector4" default="0.0, 0.0, 0.0, 0.0" />
  </nodedef>

  <!--
    Node: <geompropvalueuniform>
    The uniform, non-varying value of the specified geometric property for the current geometry.
  -->
  <nodedef name="ND_geompropvalueuniform_string" node="geompropvalueuniform" nodegroup="geometric">
    <input name="geomprop" type="string" value="" uniform="true" />
    <input name="default" type="string" value="" uniform="true" />
    <output name="out" type="string" default="" uniform="true" />
  </nodedef>
  <nodedef name="ND_geompropvalueuniform_filename" node="geompropvalueuniform" nodegroup="geometric">
    <input name="geomprop" type="string" value="" uniform="true" />
    <input name="default" type="filename" value="" uniform="true" />
    <output name="out" type="filename" default="" uniform="true" />
  </nodedef>


  <!--
    Node: <bump>
    Offset the surface normal by a scalar value.
  -->
  <nodedef name="ND_bump_vector3" node="bump" nodegroup="geometric">
    <input name="height" type="float" uiname="Height" uisoftmin="0.0" uisoftmax="1.0" value="0" doc="Amount to offset the surface normal." />
    <input name="scale" type="float" uiname="Scale" uisoftmin="0.0" uisoftmax="1.0" value="1" doc="Scalar to adjust the height amount." />
    <input name="normal" type="vector3" uiname="Normal" defaultgeomprop="Nworld" doc="Surface normal; defaults to the current world-space normal." />
    <input name="tangent" type="vector3" uiname="Tangent" defaultgeomprop="Tworld" doc="Surface tangent vector, defaults to the current world-space tangent vector." />
    <output name="out" type="vector3" doc="Offset surface normal; connect this to a shader's 'normal' input." />
  </nodedef>

  <!-- ======================================================================== -->
  <!-- Application nodes                                                        -->
  <!-- ======================================================================== -->

  <!--
    Node: <frame>
    The current frame number as defined by the host environment.
  -->
  <nodedef name="ND_frame_float" node="frame" nodegroup="application">
    <output name="out" type="float" default="1.0" />
  </nodedef>

  <!--
    Node: <time>
    The current time in seconds as defined by the host environment.
    (Default values is 1/24.0)
  -->
  <nodedef name="ND_time_float" node="time" nodegroup="application">
    <input name="fps" type="float" value="24.0" />
    <output name="out" type="float" default="0.041666667" />
  </nodedef>

  <!-- ======================================================================== -->
  <!-- Math nodes                                                               -->
  <!-- ======================================================================== -->

  <!--
    Node: <add>
    Add "in2" value/stream to the incoming float/integer/color/vector/matrix.
  -->
  <nodedef name="ND_add_float" node="add" nodegroup="math">
    <input name="in1" type="float" value="0.0" />
    <input name="in2" type="float" value="0.0" />
    <output name="out" type="float" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_add_integer" node="add" nodegroup="math">
    <input name="in1" type="integer" value="0" />
    <input name="in2" type="integer" value="0" />
    <output name="out" type="integer" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_add_color3" node="add" nodegroup="math">
    <input name="in1" type="color3" value="0.0, 0.0, 0.0" />
    <input name="in2" type="color3" value="0.0, 0.0, 0.0" />
    <output name="out" type="color3" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_add_color4" node="add" nodegroup="math">
    <input name="in1" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="in2" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <output name="out" type="color4" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_add_vector2" node="add" nodegroup="math">
    <input name="in1" type="vector2" value="0.0, 0.0" />
    <input name="in2" type="vector2" value="0.0, 0.0" />
    <output name="out" type="vector2" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_add_vector3" node="add" nodegroup="math">
    <input name="in1" type="vector3" value="0.0, 0.0, 0.0" />
    <input name="in2" type="vector3" value="0.0, 0.0, 0.0" />
    <output name="out" type="vector3" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_add_vector4" node="add" nodegroup="math">
    <input name="in1" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="in2" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <output name="out" type="vector4" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_add_matrix33" node="add" nodegroup="math">
    <input name="in1" type="matrix33" value="1.0,0.0,0.0, 0.0,1.0,0.0, 0.0,0.0,1.0" />
    <input name="in2" type="matrix33" value="0.0,0.0,0.0, 0.0,0.0,0.0, 0.0,0.0,0.0" />
    <output name="out" type="matrix33" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_add_matrix44" node="add" nodegroup="math">
    <input name="in1" type="matrix44" value="1.0,0.0,0.0,0.0, 0.0,1.0,0.0,0.0, 0.0,0.0,1.0,0.0, 0.0,0.0,0.0,1.0" />
    <input name="in2" type="matrix44" value="0.0,0.0,0.0,0.0, 0.0,0.0,0.0,0.0, 0.0,0.0,0.0,0.0, 0.0,0.0,0.0,0.0" />
    <output name="out" type="matrix44" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_add_color3FA" node="add" nodegroup="math">
    <input name="in1" type="color3" value="0.0, 0.0, 0.0" />
    <input name="in2" type="float" value="0.0" />
    <output name="out" type="color3" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_add_color4FA" node="add" nodegroup="math">
    <input name="in1" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="in2" type="float" value="0.0" />
    <output name="out" type="color4" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_add_vector2FA" node="add" nodegroup="math">
    <input name="in1" type="vector2" value="0.0, 0.0" />
    <input name="in2" type="float" value="0.0" />
    <output name="out" type="vector2" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_add_vector3FA" node="add" nodegroup="math">
    <input name="in1" type="vector3" value="0.0, 0.0, 0.0" />
    <input name="in2" type="float" value="0.0" />
    <output name="out" type="vector3" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_add_vector4FA" node="add" nodegroup="math">
    <input name="in1" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="in2" type="float" value="0.0" />
    <output name="out" type="vector4" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_add_matrix33FA" node="add" nodegroup="math">
    <input name="in1" type="matrix33" value="1.0,0.0,0.0, 0.0,1.0,0.0, 0.0,0.0,1.0" />
    <input name="in2" type="float" value="0.0" />
    <output name="out" type="matrix33" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_add_matrix44FA" node="add" nodegroup="math">
    <input name="in1" type="matrix44" value="1.0,0.0,0.0,0.0, 0.0,1.0,0.0,0.0, 0.0,0.0,1.0,0.0, 0.0,0.0,0.0,1.0" />
    <input name="in2" type="float" value="0.0" />
    <output name="out" type="matrix44" defaultinput="in1" />
  </nodedef>

  <!--
    Node: <subtract>
    Subtract "in2" value/stream from the incoming float/integer/color/vector/matrix.
  -->
  <nodedef name="ND_subtract_float" node="subtract" nodegroup="math">
    <input name="in1" type="float" value="0.0" />
    <input name="in2" type="float" value="0.0" />
    <output name="out" type="float" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_subtract_integer" node="subtract" nodegroup="math">
    <input name="in1" type="integer" value="0" />
    <input name="in2" type="integer" value="0" />
    <output name="out" type="integer" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_subtract_color3" node="subtract" nodegroup="math">
    <input name="in1" type="color3" value="0.0, 0.0, 0.0" />
    <input name="in2" type="color3" value="0.0, 0.0, 0.0" />
    <output name="out" type="color3" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_subtract_color4" node="subtract" nodegroup="math">
    <input name="in1" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="in2" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <output name="out" type="color4" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_subtract_vector2" node="subtract" nodegroup="math">
    <input name="in1" type="vector2" value="0.0, 0.0" />
    <input name="in2" type="vector2" value="0.0, 0.0" />
    <output name="out" type="vector2" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_subtract_vector3" node="subtract" nodegroup="math">
    <input name="in1" type="vector3" value="0.0, 0.0, 0.0" />
    <input name="in2" type="vector3" value="0.0, 0.0, 0.0" />
    <output name="out" type="vector3" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_subtract_vector4" node="subtract" nodegroup="math">
    <input name="in1" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="in2" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <output name="out" type="vector4" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_subtract_matrix33" node="subtract" nodegroup="math">
    <input name="in1" type="matrix33" value="1.0,0.0,0.0, 0.0,1.0,0.0, 0.0,0.0,1.0" />
    <input name="in2" type="matrix33" value="0.0,0.0,0.0, 0.0,0.0,0.0, 0.0,0.0,0.0" />
    <output name="out" type="matrix33" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_subtract_matrix44" node="subtract" nodegroup="math">
    <input name="in1" type="matrix44" value="1.0,0.0,0.0,0.0, 0.0,1.0,0.0,0.0, 0.0,0.0,1.0,0.0, 0.0,0.0,0.0,1.0" />
    <input name="in2" type="matrix44" value="0.0,0.0,0.0,0.0, 0.0,0.0,0.0,0.0, 0.0,0.0,0.0,0.0, 0.0,0.0,0.0,0.0" />
    <output name="out" type="matrix44" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_subtract_color3FA" node="subtract" nodegroup="math">
    <input name="in1" type="color3" value="0.0, 0.0, 0.0" />
    <input name="in2" type="float" value="0.0" />
    <output name="out" type="color3" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_subtract_color4FA" node="subtract" nodegroup="math">
    <input name="in1" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="in2" type="float" value="0.0" />
    <output name="out" type="color4" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_subtract_vector2FA" node="subtract" nodegroup="math">
    <input name="in1" type="vector2" value="0.0, 0.0" />
    <input name="in2" type="float" value="0.0" />
    <output name="out" type="vector2" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_subtract_vector3FA" node="subtract" nodegroup="math">
    <input name="in1" type="vector3" value="0.0, 0.0, 0.0" />
    <input name="in2" type="float" value="0.0" />
    <output name="out" type="vector3" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_subtract_vector4FA" node="subtract" nodegroup="math">
    <input name="in1" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="in2" type="float" value="0.0" />
    <output name="out" type="vector4" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_subtract_matrix33FA" node="subtract" nodegroup="math">
    <input name="in1" type="matrix33" value="1.0,0.0,0.0, 0.0,1.0,0.0, 0.0,0.0,1.0" />
    <input name="in2" type="float" value="0.0" />
    <output name="out" type="matrix33" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_subtract_matrix44FA" node="subtract" nodegroup="math">
    <input name="in1" type="matrix44" value="1.0,0.0,0.0,0.0, 0.0,1.0,0.0,0.0, 0.0,0.0,1.0,0.0, 0.0,0.0,0.0,1.0" />
    <input name="in2" type="float" value="0.0" />
    <output name="out" type="matrix44" defaultinput="in1" />
  </nodedef>

  <!--
    Node: <multiply>
    Multiply the incoming float/color/vector by the "in2" value/stream, or multiply
    two matrices.
  -->
  <nodedef name="ND_multiply_float" node="multiply" nodegroup="math">
    <input name="in1" type="float" value="0.0" />
    <input name="in2" type="float" value="1.0" />
    <output name="out" type="float" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_multiply_color3" node="multiply" nodegroup="math">
    <input name="in1" type="color3" value="0.0, 0.0, 0.0" />
    <input name="in2" type="color3" value="1.0, 1.0, 1.0" />
    <output name="out" type="color3" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_multiply_color4" node="multiply" nodegroup="math">
    <input name="in1" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="in2" type="color4" value="1.0, 1.0, 1.0, 1.0" />
    <output name="out" type="color4" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_multiply_vector2" node="multiply" nodegroup="math">
    <input name="in1" type="vector2" value="0.0, 0.0" />
    <input name="in2" type="vector2" value="1.0, 1.0" />
    <output name="out" type="vector2" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_multiply_vector3" node="multiply" nodegroup="math">
    <input name="in1" type="vector3" value="0.0, 0.0, 0.0" />
    <input name="in2" type="vector3" value="1.0, 1.0, 1.0" />
    <output name="out" type="vector3" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_multiply_vector4" node="multiply" nodegroup="math">
    <input name="in1" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="in2" type="vector4" value="1.0, 1.0, 1.0, 1.0" />
    <output name="out" type="vector4" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_multiply_matrix33" node="multiply" nodegroup="math">
    <input name="in1" type="matrix33" value="1.0,0.0,0.0, 0.0,1.0,0.0, 0.0,0.0,1.0" />
    <input name="in2" type="matrix33" value="1.0,0.0,0.0, 0.0,1.0,0.0, 0.0,0.0,1.0" />
    <output name="out" type="matrix33" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_multiply_matrix44" node="multiply" nodegroup="math">
    <input name="in1" type="matrix44" value="1.0,0.0,0.0,0.0, 0.0,1.0,0.0,0.0, 0.0,0.0,1.0,0.0, 0.0,0.0,0.0,1.0" />
    <input name="in2" type="matrix44" value="1.0,0.0,0.0,0.0, 0.0,1.0,0.0,0.0, 0.0,0.0,1.0,0.0, 0.0,0.0,0.0,1.0" />
    <output name="out" type="matrix44" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_multiply_color3FA" node="multiply" nodegroup="math">
    <input name="in1" type="color3" value="0.0, 0.0, 0.0" />
    <input name="in2" type="float" value="1.0" />
    <output name="out" type="color3" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_multiply_color4FA" node="multiply" nodegroup="math">
    <input name="in1" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="in2" type="float" value="1.0" />
    <output name="out" type="color4" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_multiply_vector2FA" node="multiply" nodegroup="math">
    <input name="in1" type="vector2" value="0.0, 0.0" />
    <input name="in2" type="float" value="1.0" />
    <output name="out" type="vector2" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_multiply_vector3FA" node="multiply" nodegroup="math">
    <input name="in1" type="vector3" value="0.0, 0.0, 0.0" />
    <input name="in2" type="float" value="1.0" />
    <output name="out" type="vector3" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_multiply_vector4FA" node="multiply" nodegroup="math">
    <input name="in1" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="in2" type="float" value="1.0" />
    <output name="out" type="vector4" defaultinput="in1" />
  </nodedef>

  <!--
    Node: <divide>
    Divide an incoming float/color/vector by the "in2" value/stream; dividing a channel
    value by 0 results in floating-point "NaN".  Or, multiply one matrix by the
    inverse of a second matrix.
  -->
  <nodedef name="ND_divide_float" node="divide" nodegroup="math">
    <input name="in1" type="float" value="0.0" />
    <input name="in2" type="float" value="1.0" />
    <output name="out" type="float" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_divide_color3" node="divide" nodegroup="math">
    <input name="in1" type="color3" value="0.0, 0.0, 0.0" />
    <input name="in2" type="color3" value="1.0, 1.0, 1.0" />
    <output name="out" type="color3" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_divide_color4" node="divide" nodegroup="math">
    <input name="in1" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="in2" type="color4" value="1.0, 1.0, 1.0, 1.0" />
    <output name="out" type="color4" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_divide_vector2" node="divide" nodegroup="math">
    <input name="in1" type="vector2" value="0.0, 0.0" />
    <input name="in2" type="vector2" value="1.0, 1.0" />
    <output name="out" type="vector2" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_divide_vector3" node="divide" nodegroup="math">
    <input name="in1" type="vector3" value="0.0, 0.0, 0.0" />
    <input name="in2" type="vector3" value="1.0, 1.0, 1.0" />
    <output name="out" type="vector3" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_divide_vector4" node="divide" nodegroup="math">
    <input name="in1" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="in2" type="vector4" value="1.0, 1.0, 1.0, 1.0" />
    <output name="out" type="vector4" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_divide_matrix33" node="divide" nodegroup="math">
    <input name="in1" type="matrix33" value="1.0,0.0,0.0, 0.0,1.0,0.0, 0.0,0.0,1.0" />
    <input name="in2" type="matrix33" value="1.0,0.0,0.0, 0.0,1.0,0.0, 0.0,0.0,1.0" />
    <output name="out" type="matrix33" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_divide_matrix44" node="divide" nodegroup="math">
    <input name="in1" type="matrix44" value="1.0,0.0,0.0,0.0, 0.0,1.0,0.0,0.0, 0.0,0.0,1.0,0.0, 0.0,0.0,0.0,1.0" />
    <input name="in2" type="matrix44" value="1.0,0.0,0.0,0.0, 0.0,1.0,0.0,0.0, 0.0,0.0,1.0,0.0, 0.0,0.0,0.0,1.0" />
    <output name="out" type="matrix44" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_divide_color3FA" node="divide" nodegroup="math">
    <input name="in1" type="color3" value="0.0, 0.0, 0.0" />
    <input name="in2" type="float" value="1.0" />
    <output name="out" type="color3" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_divide_color4FA" node="divide" nodegroup="math">
    <input name="in1" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="in2" type="float" value="1.0" />
    <output name="out" type="color4" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_divide_vector2FA" node="divide" nodegroup="math">
    <input name="in1" type="vector2" value="0.0, 0.0" />
    <input name="in2" type="float" value="1.0" />
    <output name="out" type="vector2" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_divide_vector3FA" node="divide" nodegroup="math">
    <input name="in1" type="vector3" value="0.0, 0.0, 0.0" />
    <input name="in2" type="float" value="1.0" />
    <output name="out" type="vector3" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_divide_vector4FA" node="divide" nodegroup="math">
    <input name="in1" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="in2" type="float" value="1.0" />
    <output name="out" type="vector4" defaultinput="in1" />
  </nodedef>

  <!--
    Node: <modulo>
    The remaining fraction after dividing one float/color/vector by another and
    subtracting the integer portion. The modula "in2" value cannot be 0.
  -->
  <nodedef name="ND_modulo_float" node="modulo" nodegroup="math">
    <input name="in1" type="float" value="0.0" />
    <input name="in2" type="float" value="1.0" />
    <output name="out" type="float" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_modulo_color3" node="modulo" nodegroup="math">
    <input name="in1" type="color3" value="0.0, 0.0, 0.0" />
    <input name="in2" type="color3" value="1.0, 1.0, 1.0" />
    <output name="out" type="color3" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_modulo_color4" node="modulo" nodegroup="math">
    <input name="in1" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="in2" type="color4" value="1.0, 1.0, 1.0, 1.0" />
    <output name="out" type="color4" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_modulo_vector2" node="modulo" nodegroup="math">
    <input name="in1" type="vector2" value="0.0, 0.0" />
    <input name="in2" type="vector2" value="1.0, 1.0" />
    <output name="out" type="vector2" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_modulo_vector3" node="modulo" nodegroup="math">
    <input name="in1" type="vector3" value="0.0, 0.0, 0.0" />
    <input name="in2" type="vector3" value="1.0, 1.0, 1.0" />
    <output name="out" type="vector3" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_modulo_vector4" node="modulo" nodegroup="math">
    <input name="in1" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="in2" type="vector4" value="1.0, 1.0, 1.0, 1.0" />
    <output name="out" type="vector4" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_modulo_color3FA" node="modulo" nodegroup="math">
    <input name="in1" type="color3" value="0.0, 0.0, 0.0" />
    <input name="in2" type="float" value="1.0" />
    <output name="out" type="color3" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_modulo_color4FA" node="modulo" nodegroup="math">
    <input name="in1" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="in2" type="float" value="1.0" />
    <output name="out" type="color4" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_modulo_vector2FA" node="modulo" nodegroup="math">
    <input name="in1" type="vector2" value="0.0, 0.0" />
    <input name="in2" type="float" value="1.0" />
    <output name="out" type="vector2" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_modulo_vector3FA" node="modulo" nodegroup="math">
    <input name="in1" type="vector3" value="0.0, 0.0, 0.0" />
    <input name="in2" type="float" value="1.0" />
    <output name="out" type="vector3" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_modulo_vector4FA" node="modulo" nodegroup="math">
    <input name="in1" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="in2" type="float" value="1.0" />
    <output name="out" type="vector4" defaultinput="in1" />
  </nodedef>

  <!--
    Node: <invert>
    Subtract the incoming float/color/vector from "amount" in all channels,
    outputting: amount - in.
  -->
  <nodedef name="ND_invert_float" node="invert" nodegroup="math">
    <input name="in" type="float" value="0.0" />
    <input name="amount" type="float" value="1.0" />
    <output name="out" type="float" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_invert_color3" node="invert" nodegroup="math">
    <input name="in" type="color3" value="0.0, 0.0, 0.0" />
    <input name="amount" type="color3" value="1.0, 1.0, 1.0" />
    <output name="out" type="color3" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_invert_color4" node="invert" nodegroup="math">
    <input name="in" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="amount" type="color4" value="1.0, 1.0, 1.0, 1.0" />
    <output name="out" type="color4" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_invert_vector2" node="invert" nodegroup="math">
    <input name="in" type="vector2" value="0.0, 0.0" />
    <input name="amount" type="vector2" value="1.0, 1.0" />
    <output name="out" type="vector2" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_invert_vector3" node="invert" nodegroup="math">
    <input name="in" type="vector3" value="0.0, 0.0, 0.0" />
    <input name="amount" type="vector3" value="1.0, 1.0, 1.0" />
    <output name="out" type="vector3" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_invert_vector4" node="invert" nodegroup="math">
    <input name="in" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="amount" type="vector4" value="1.0, 1.0, 1.0, 1.0" />
    <output name="out" type="vector4" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_invert_color3FA" node="invert" nodegroup="math">
    <input name="in" type="color3" value="0.0, 0.0, 0.0" />
    <input name="amount" type="float" value="1.0" />
    <output name="out" type="color3" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_invert_color4FA" node="invert" nodegroup="math">
    <input name="in" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="amount" type="float" value="1.0" />
    <output name="out" type="color4" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_invert_vector2FA" node="invert" nodegroup="math">
    <input name="in" type="vector2" value="0.0, 0.0" />
    <input name="amount" type="float" value="1.0" />
    <output name="out" type="vector2" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_invert_vector3FA" node="invert" nodegroup="math">
    <input name="in" type="vector3" value="0.0, 0.0, 0.0" />
    <input name="amount" type="float" value="1.0" />
    <output name="out" type="vector3" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_invert_vector4FA" node="invert" nodegroup="math">
    <input name="in" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="amount" type="float" value="1.0" />
    <output name="out" type="vector4" defaultinput="in" />
  </nodedef>

  <!--
    Node: <absval>
    The per-channel absolute value of the incoming float/color/vector.
  -->
  <nodedef name="ND_absval_float" node="absval" nodegroup="math">
    <input name="in" type="float" value="0.0" />
    <output name="out" type="float" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_absval_color3" node="absval" nodegroup="math">
    <input name="in" type="color3" value="0.0, 0.0, 0.0" />
    <output name="out" type="color3" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_absval_color4" node="absval" nodegroup="math">
    <input name="in" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <output name="out" type="color4" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_absval_vector2" node="absval" nodegroup="math">
    <input name="in" type="vector2" value="0.0, 0.0" />
    <output name="out" type="vector2" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_absval_vector3" node="absval" nodegroup="math">
    <input name="in" type="vector3" value="0.0, 0.0, 0.0" />
    <output name="out" type="vector3" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_absval_vector4" node="absval" nodegroup="math">
    <input name="in" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <output name="out" type="vector4" defaultinput="in" />
  </nodedef>

  <!--
    Node: <floor>
    Find the nearest integer less than or equal to the parameter.
  -->
  <nodedef name="ND_floor_float" node="floor" nodegroup="math">
    <input name="in" type="float" value="0.0" />
    <output name="out" type="float" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_floor_color3" node="floor" nodegroup="math">
    <input name="in" type="color3" value="0.0, 0.0, 0.0" />
    <output name="out" type="color3" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_floor_color4" node="floor" nodegroup="math">
    <input name="in" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <output name="out" type="color4" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_floor_vector2" node="floor" nodegroup="math">
    <input name="in" type="vector2" value="0.0, 0.0" />
    <output name="out" type="vector2" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_floor_vector3" node="floor" nodegroup="math">
    <input name="in" type="vector3" value="0.0, 0.0, 0.0" />
    <output name="out" type="vector3" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_floor_vector4" node="floor" nodegroup="math">
    <input name="in" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <output name="out" type="vector4" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_floor_integer" node="floor" nodegroup="math">
    <input name="in" type="float" value="0.0" />
    <output name="out" type="integer" defaultinput="in" />
  </nodedef>

  <!--
    Node: <ceil>
    Find the nearest integer greater than or equal to the parameter.
  -->
  <nodedef name="ND_ceil_float" node="ceil" nodegroup="math">
    <input name="in" type="float" value="0.0" />
    <output name="out" type="float" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_ceil_color3" node="ceil" nodegroup="math">
    <input name="in" type="color3" value="0.0, 0.0, 0.0" />
    <output name="out" type="color3" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_ceil_color4" node="ceil" nodegroup="math">
    <input name="in" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <output name="out" type="color4" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_ceil_vector2" node="ceil" nodegroup="math">
    <input name="in" type="vector2" value="0.0, 0.0" />
    <output name="out" type="vector2" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_ceil_vector3" node="ceil" nodegroup="math">
    <input name="in" type="vector3" value="0.0, 0.0, 0.0" />
    <output name="out" type="vector3" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_ceil_vector4" node="ceil" nodegroup="math">
    <input name="in" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <output name="out" type="vector4" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_ceil_integer" node="ceil" nodegroup="math">
    <input name="in" type="float" value="0.0" />
    <output name="out" type="integer" defaultinput="in" />
  </nodedef>

  <!--
    Node: <round>
    Round incoming float/color/vector values.
  -->
  <nodedef name="ND_round_float" node="round" nodegroup="math">
    <input name="in" type="float" value="0.0" />
    <output name="out" type="float" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_round_color3" node="round" nodegroup="math">
    <input name="in" type="color3" value="0.0, 0.0, 0.0" />
    <output name="out" type="color3" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_round_color4" node="round" nodegroup="math">
    <input name="in" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <output name="out" type="color4" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_round_vector2" node="round" nodegroup="math">
    <input name="in" type="vector2" value="0.0, 0.0" />
    <output name="out" type="vector2" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_round_vector3" node="round" nodegroup="math">
    <input name="in" type="vector3" value="0.0, 0.0, 0.0" />
    <output name="out" type="vector3" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_round_vector4" node="round" nodegroup="math">
    <input name="in" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <output name="out" type="vector4" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_round_integer" node="round" nodegroup="math">
    <input name="in" type="float" value="0.0" />
    <output name="out" type="integer" defaultinput="in" />
  </nodedef>

  <!--
    Node: <power>
    Raise incoming float/color/vector values to the "in2" power.
  -->
  <nodedef name="ND_power_float" node="power" nodegroup="math">
    <input name="in1" type="float" value="0.0" />
    <input name="in2" type="float" value="1.0" />
    <output name="out" type="float" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_power_color3" node="power" nodegroup="math">
    <input name="in1" type="color3" value="0.0, 0.0, 0.0" />
    <input name="in2" type="color3" value="1.0, 1.0, 1.0" />
    <output name="out" type="color3" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_power_color4" node="power" nodegroup="math">
    <input name="in1" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="in2" type="color4" value="1.0, 1.0, 1.0, 1.0" />
    <output name="out" type="color4" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_power_vector2" node="power" nodegroup="math">
    <input name="in1" type="vector2" value="0.0, 0.0" />
    <input name="in2" type="vector2" value="1.0, 1.0" />
    <output name="out" type="vector2" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_power_vector3" node="power" nodegroup="math">
    <input name="in1" type="vector3" value="0.0, 0.0, 0.0" />
    <input name="in2" type="vector3" value="1.0, 1.0, 1.0" />
    <output name="out" type="vector3" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_power_vector4" node="power" nodegroup="math">
    <input name="in1" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="in2" type="vector4" value="1.0, 1.0, 1.0, 1.0" />
    <output name="out" type="vector4" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_power_color3FA" node="power" nodegroup="math">
    <input name="in1" type="color3" value="0.0, 0.0, 0.0" />
    <input name="in2" type="float" value="1.0" />
    <output name="out" type="color3" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_power_color4FA" node="power" nodegroup="math">
    <input name="in1" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="in2" type="float" value="1.0" />
    <output name="out" type="color4" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_power_vector2FA" node="power" nodegroup="math">
    <input name="in1" type="vector2" value="0.0, 0.0" />
    <input name="in2" type="float" value="1.0" />
    <output name="out" type="vector2" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_power_vector3FA" node="power" nodegroup="math">
    <input name="in1" type="vector3" value="0.0, 0.0, 0.0" />
    <input name="in2" type="float" value="1.0" />
    <output name="out" type="vector3" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_power_vector4FA" node="power" nodegroup="math">
    <input name="in1" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="in2" type="float" value="1.0" />
    <output name="out" type="vector4" defaultinput="in1" />
  </nodedef>

  <!--
    Node: <safepower>
    Raise incoming float/color/vector values to the "in2" power.
    Negative "in1" values will result in negative output values. ie. out = sign(in1)*pow(abs(in1),in2)
  -->
  <nodedef name="ND_safepower_float" node="safepower" nodegroup="math">
    <input name="in1" type="float" value="0.0" />
    <input name="in2" type="float" value="1.0" />
    <output name="out" type="float" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_safepower_color3" node="safepower" nodegroup="math">
    <input name="in1" type="color3" value="0.0, 0.0, 0.0" />
    <input name="in2" type="color3" value="1.0, 1.0, 1.0" />
    <output name="out" type="color3" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_safepower_color4" node="safepower" nodegroup="math">
    <input name="in1" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="in2" type="color4" value="1.0, 1.0, 1.0, 1.0" />
    <output name="out" type="color4" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_safepower_vector2" node="safepower" nodegroup="math">
    <input name="in1" type="vector2" value="0.0, 0.0" />
    <input name="in2" type="vector2" value="1.0, 1.0" />
    <output name="out" type="vector2" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_safepower_vector3" node="safepower" nodegroup="math">
    <input name="in1" type="vector3" value="0.0, 0.0, 0.0" />
    <input name="in2" type="vector3" value="1.0, 1.0, 1.0" />
    <output name="out" type="vector3" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_safepower_vector4" node="safepower" nodegroup="math">
    <input name="in1" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="in2" type="vector4" value="1.0, 1.0, 1.0, 1.0" />
    <output name="out" type="vector4" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_safepower_color3FA" node="safepower" nodegroup="math">
    <input name="in1" type="color3" value="0.0, 0.0, 0.0" />
    <input name="in2" type="float" value="1.0" />
    <output name="out" type="color3" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_safepower_color4FA" node="safepower" nodegroup="math">
    <input name="in1" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="in2" type="float" value="1.0" />
    <output name="out" type="color4" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_safepower_vector2FA" node="safepower" nodegroup="math">
    <input name="in1" type="vector2" value="0.0, 0.0" />
    <input name="in2" type="float" value="1.0" />
    <output name="out" type="vector2" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_safepower_vector3FA" node="safepower" nodegroup="math">
    <input name="in1" type="vector3" value="0.0, 0.0, 0.0" />
    <input name="in2" type="float" value="1.0" />
    <output name="out" type="vector3" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_safepower_vector4FA" node="safepower" nodegroup="math">
    <input name="in1" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="in2" type="float" value="1.0" />
    <output name="out" type="vector4" defaultinput="in1" />
  </nodedef>

  <!--
    Nodes: <sin>, <cos>, <tan>, <asin>, <acos>, <atan2>
    Standard trigonometric functions; angles are given in radians.
  -->
  <nodedef name="ND_sin_float" node="sin" nodegroup="math">
    <input name="in" type="float" value="0.0" />
    <output name="out" type="float" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_cos_float" node="cos" nodegroup="math">
    <input name="in" type="float" value="0.0" />
    <output name="out" type="float" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_tan_float" node="tan" nodegroup="math">
    <input name="in" type="float" value="0.0" />
    <output name="out" type="float" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_asin_float" node="asin" nodegroup="math">
    <input name="in" type="float" value="0.0" />
    <output name="out" type="float" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_acos_float" node="acos" nodegroup="math">
    <input name="in" type="float" value="0.0" />
    <output name="out" type="float" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_atan2_float" node="atan2" nodegroup="math">
    <input name="iny" type="float" value="0.0" />
    <input name="inx" type="float" value="1.0" />
    <output name="out" type="float" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_sin_vector2" node="sin" nodegroup="math">
    <input name="in" type="vector2" value="0.0, 0.0" />
    <output name="out" type="vector2" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_cos_vector2" node="cos" nodegroup="math">
    <input name="in" type="vector2" value="0.0, 0.0" />
    <output name="out" type="vector2" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_tan_vector2" node="tan" nodegroup="math">
    <input name="in" type="vector2" value="0.0, 0.0" />
    <output name="out" type="vector2" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_asin_vector2" node="asin" nodegroup="math">
    <input name="in" type="vector2" value="0.0, 0.0" />
    <output name="out" type="vector2" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_acos_vector2" node="acos" nodegroup="math">
    <input name="in" type="vector2" value="0.0, 0.0" />
    <output name="out" type="vector2" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_atan2_vector2" node="atan2" nodegroup="math">
    <input name="iny" type="vector2" value="1.0, 1.0" />
    <input name="inx" type="vector2" value="0.0, 0.0" />
    <output name="out" type="vector2" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_sin_vector3" node="sin" nodegroup="math">
    <input name="in" type="vector3" value="0.0, 0.0, 0.0" />
    <output name="out" type="vector3" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_cos_vector3" node="cos" nodegroup="math">
    <input name="in" type="vector3" value="0.0, 0.0, 0.0" />
    <output name="out" type="vector3" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_tan_vector3" node="tan" nodegroup="math">
    <input name="in" type="vector3" value="0.0, 0.0, 0.0" />
    <output name="out" type="vector3" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_asin_vector3" node="asin" nodegroup="math">
    <input name="in" type="vector3" value="0.0, 0.0, 0.0" />
    <output name="out" type="vector3" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_acos_vector3" node="acos" nodegroup="math">
    <input name="in" type="vector3" value="0.0, 0.0, 0.0" />
    <output name="out" type="vector3" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_atan2_vector3" node="atan2" nodegroup="math">
    <input name="iny" type="vector3" value="1.0, 1.0, 1.0" />
    <input name="inx" type="vector3" value="0.0, 0.0, 0.0" />
    <output name="out" type="vector3" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_sin_vector4" node="sin" nodegroup="math">
    <input name="in" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <output name="out" type="vector4" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_cos_vector4" node="cos" nodegroup="math">
    <input name="in" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <output name="out" type="vector4" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_tan_vector4" node="tan" nodegroup="math">
    <input name="in" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <output name="out" type="vector4" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_asin_vector4" node="asin" nodegroup="math">
    <input name="in" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <output name="out" type="vector4" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_acos_vector4" node="acos" nodegroup="math">
    <input name="in" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <output name="out" type="vector4" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_atan2_vector4" node="atan2" nodegroup="math">
    <input name="iny" type="vector4" value="1.0, 1.0, 1.0, 1.0" />
    <input name="inx" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <output name="out" type="vector4" defaultinput="in1" />
  </nodedef>

  <!--
    Nodes: <sqrt>, <ln>, <exp>
    Standard math functions.
  -->
  <nodedef name="ND_sqrt_float" node="sqrt" nodegroup="math">
    <input name="in" type="float" value="0.0" />
    <output name="out" type="float" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_ln_float" node="ln" nodegroup="math">
    <input name="in" type="float" value="1.0" />
    <output name="out" type="float" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_exp_float" node="exp" nodegroup="math">
    <input name="in" type="float" value="0.0" />
    <output name="out" type="float" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_sqrt_vector2" node="sqrt" nodegroup="math">
    <input name="in" type="vector2" value="0.0, 0.0" />
    <output name="out" type="vector2" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_ln_vector2" node="ln" nodegroup="math">
    <input name="in" type="vector2" value="1.0, 1.0" />
    <output name="out" type="vector2" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_exp_vector2" node="exp" nodegroup="math">
    <input name="in" type="vector2" value="0.0, 0.0" />
    <output name="out" type="vector2" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_sqrt_vector3" node="sqrt" nodegroup="math">
    <input name="in" type="vector3" value="0.0, 0.0, 0.0" />
    <output name="out" type="vector3" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_ln_vector3" node="ln" nodegroup="math">
    <input name="in" type="vector3" value="1.0, 1.0, 1.0" />
    <output name="out" type="vector3" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_exp_vector3" node="exp" nodegroup="math">
    <input name="in" type="vector3" value="0.0, 0.0, 0.0" />
    <output name="out" type="vector3" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_sqrt_vector4" node="sqrt" nodegroup="math">
    <input name="in" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <output name="out" type="vector4" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_ln_vector4" node="ln" nodegroup="math">
    <input name="in" type="vector4" value="1.0, 1.0, 1.0, 1.0" />
    <output name="out" type="vector4" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_exp_vector4" node="exp" nodegroup="math">
    <input name="in" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <output name="out" type="vector4" defaultinput="in" />
  </nodedef>

  <!--
    Node: <sign>
    Sign of eachinput channel: -1, 0 or +1
  -->
  <nodedef name="ND_sign_float" node="sign" nodegroup="math">
    <input name="in" type="float" value="0.0" />
    <output name="out" type="float" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_sign_color3" node="sign" nodegroup="math">
    <input name="in" type="color3" value="0.0, 0.0, 0.0" />
    <output name="out" type="color3" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_sign_color4" node="sign" nodegroup="math">
    <input name="in" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <output name="out" type="color4" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_sign_vector2" node="sign" nodegroup="math">
    <input name="in" type="vector2" value="0.0, 0.0" />
    <output name="out" type="vector2" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_sign_vector3" node="sign" nodegroup="math">
    <input name="in" type="vector3" value="0.0, 0.0, 0.0" />
    <output name="out" type="vector3" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_sign_vector4" node="sign" nodegroup="math">
    <input name="in" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <output name="out" type="vector4" defaultinput="in" />
  </nodedef>

  <!--
    Node: <clamp>
    Clamp incoming value to a specified range of values.
  -->
  <nodedef name="ND_clamp_float" node="clamp" nodegroup="math">
    <input name="in" type="float" value="0.0" />
    <input name="low" type="float" value="0.0" />
    <input name="high" type="float" value="1.0" />
    <output name="out" type="float" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_clamp_color3" node="clamp" nodegroup="math">
    <input name="in" type="color3" value="0.0, 0.0, 0.0" />
    <input name="low" type="color3" value="0.0, 0.0, 0.0" />
    <input name="high" type="color3" value="1.0, 1.0, 1.0" />
    <output name="out" type="color3" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_clamp_color4" node="clamp" nodegroup="math">
    <input name="in" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="low" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="high" type="color4" value="1.0, 1.0, 1.0, 1.0" />
    <output name="out" type="color4" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_clamp_vector2" node="clamp" nodegroup="math">
    <input name="in" type="vector2" value="0.0, 0.0" />
    <input name="low" type="vector2" value="0.0, 0.0" />
    <input name="high" type="vector2" value="1.0, 1.0" />
    <output name="out" type="vector2" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_clamp_vector3" node="clamp" nodegroup="math">
    <input name="in" type="vector3" value="0.0, 0.0, 0.0" />
    <input name="low" type="vector3" value="0.0, 0.0, 0.0" />
    <input name="high" type="vector3" value="1.0, 1.0, 1.0" />
    <output name="out" type="vector3" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_clamp_vector4" node="clamp" nodegroup="math">
    <input name="in" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="low" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="high" type="vector4" value="1.0, 1.0, 1.0, 1.0" />
    <output name="out" type="vector4" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_clamp_color3FA" node="clamp" nodegroup="math">
    <input name="in" type="color3" value="0.0, 0.0, 0.0" />
    <input name="low" type="float" value="0.0" />
    <input name="high" type="float" value="1.0" />
    <output name="out" type="color3" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_clamp_color4FA" node="clamp" nodegroup="math">
    <input name="in" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="low" type="float" value="0.0" />
    <input name="high" type="float" value="1.0" />
    <output name="out" type="color4" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_clamp_vector2FA" node="clamp" nodegroup="math">
    <input name="in" type="vector2" value="0.0, 0.0" />
    <input name="low" type="float" value="0.0" />
    <input name="high" type="float" value="1.0" />
    <output name="out" type="vector2" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_clamp_vector3FA" node="clamp" nodegroup="math">
    <input name="in" type="vector3" value="0.0, 0.0, 0.0" />
    <input name="low" type="float" value="0.0" />
    <input name="high" type="float" value="1.0" />
    <output name="out" type="vector3" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_clamp_vector4FA" node="clamp" nodegroup="math">
    <input name="in" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="low" type="float" value="0.0" />
    <input name="high" type="float" value="1.0" />
    <output name="out" type="vector4" defaultinput="in" />
  </nodedef>

  <!--
    Node: <min>
    Select the minimum among incoming values.
  -->
  <nodedef name="ND_min_float" node="min" nodegroup="math">
    <input name="in1" type="float" value="0.0" />
    <input name="in2" type="float" value="0.0" />
    <output name="out" type="float" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_min_color3" node="min" nodegroup="math">
    <input name="in1" type="color3" value="0.0, 0.0, 0.0" />
    <input name="in2" type="color3" value="0.0, 0.0, 0.0" />
    <output name="out" type="color3" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_min_color4" node="min" nodegroup="math">
    <input name="in1" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="in2" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <output name="out" type="color4" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_min_vector2" node="min" nodegroup="math">
    <input name="in1" type="vector2" value="0.0, 0.0" />
    <input name="in2" type="vector2" value="0.0, 0.0" />
    <output name="out" type="vector2" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_min_vector3" node="min" nodegroup="math">
    <input name="in1" type="vector3" value="0.0, 0.0, 0.0" />
    <input name="in2" type="vector3" value="0.0, 0.0, 0.0" />
    <output name="out" type="vector3" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_min_vector4" node="min" nodegroup="math">
    <input name="in1" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="in2" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <output name="out" type="vector4" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_min_color3FA" node="min" nodegroup="math">
    <input name="in1" type="color3" value="0.0, 0.0, 0.0" />
    <input name="in2" type="float" value="0.0" />
    <output name="out" type="color3" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_min_color4FA" node="min" nodegroup="math">
    <input name="in1" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="in2" type="float" value="0.0" />
    <output name="out" type="color4" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_min_vector2FA" node="min" nodegroup="math">
    <input name="in1" type="vector2" value="0.0, 0.0" />
    <input name="in2" type="float" value="0.0" />
    <output name="out" type="vector2" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_min_vector3FA" node="min" nodegroup="math">
    <input name="in1" type="vector3" value="0.0, 0.0, 0.0" />
    <input name="in2" type="float" value="0.0" />
    <output name="out" type="vector3" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_min_vector4FA" node="min" nodegroup="math">
    <input name="in1" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="in2" type="float" value="0.0" />
    <output name="out" type="vector4" defaultinput="in1" />
  </nodedef>

  <!--
    Node: <max>
    Select the maximum among incoming values.
  -->
  <nodedef name="ND_max_float" node="max" nodegroup="math">
    <input name="in1" type="float" value="0.0" />
    <input name="in2" type="float" value="0.0" />
    <output name="out" type="float" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_max_color3" node="max" nodegroup="math">
    <input name="in1" type="color3" value="0.0, 0.0, 0.0" />
    <input name="in2" type="color3" value="0.0, 0.0, 0.0" />
    <output name="out" type="color3" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_max_color4" node="max" nodegroup="math">
    <input name="in1" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="in2" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <output name="out" type="color4" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_max_vector2" node="max" nodegroup="math">
    <input name="in1" type="vector2" value="0.0, 0.0" />
    <input name="in2" type="vector2" value="0.0, 0.0" />
    <output name="out" type="vector2" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_max_vector3" node="max" nodegroup="math">
    <input name="in1" type="vector3" value="0.0, 0.0, 0.0" />
    <input name="in2" type="vector3" value="0.0, 0.0, 0.0" />
    <output name="out" type="vector3" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_max_vector4" node="max" nodegroup="math">
    <input name="in1" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="in2" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <output name="out" type="vector4" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_max_color3FA" node="max" nodegroup="math">
    <input name="in1" type="color3" value="0.0, 0.0, 0.0" />
    <input name="in2" type="float" value="0.0" />
    <output name="out" type="color3" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_max_color4FA" node="max" nodegroup="math">
    <input name="in1" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="in2" type="float" value="0.0" />
    <output name="out" type="color4" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_max_vector2FA" node="max" nodegroup="math">
    <input name="in1" type="vector2" value="0.0, 0.0" />
    <input name="in2" type="float" value="0.0" />
    <output name="out" type="vector2" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_max_vector3FA" node="max" nodegroup="math">
    <input name="in1" type="vector3" value="0.0, 0.0, 0.0" />
    <input name="in2" type="float" value="0.0" />
    <output name="out" type="vector3" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_max_vector4FA" node="max" nodegroup="math">
    <input name="in1" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="in2" type="float" value="0.0" />
    <output name="out" type="vector4" defaultinput="in1" />
  </nodedef>

  <!--
    Node: <normalize>
    Outputs the normalized vector from the incoming vector stream.
  -->
  <nodedef name="ND_normalize_vector2" node="normalize" nodegroup="math">
    <input name="in" type="vector2" value="0.0, 0.0" />
    <output name="out" type="vector2" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_normalize_vector3" node="normalize" nodegroup="math">
    <input name="in" type="vector3" value="0.0, 0.0, 0.0" />
    <output name="out" type="vector3" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_normalize_vector4" node="normalize" nodegroup="math">
    <input name="in" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <output name="out" type="vector4" defaultinput="in" />
  </nodedef>

  <!--
    Node: <magnitude>
    Outputs the float magnitude (vector length) of the incoming vector stream.
  -->
  <nodedef name="ND_magnitude_vector2" node="magnitude" nodegroup="math">
    <input name="in" type="vector2" value="0.0, 0.0" />
    <output name="out" type="float" default="0.0" />
  </nodedef>
  <nodedef name="ND_magnitude_vector3" node="magnitude" nodegroup="math">
    <input name="in" type="vector3" value="0.0, 0.0, 0.0" />
    <output name="out" type="float" default="0.0" />
  </nodedef>
  <nodedef name="ND_magnitude_vector4" node="magnitude" nodegroup="math">
    <input name="in" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <output name="out" type="float" default="0.0" />
  </nodedef>

  <!--
    Node: <distance>
    Measures the distance between two points in 2D, 3D, or 4D.
  -->
  <nodedef name="ND_distance_vector2" node="distance" nodegroup="math">
    <input name="in1" type="vector2" uiname="in1" value="0.0, 0.0" />
    <input name="in2" type="vector2" uiname="in2" value="0.0, 0.0" />
    <output name="out" type="float" />
  </nodedef>
  <nodedef name="ND_distance_vector3" node="distance" nodegroup="math">
    <input name="in1" type="vector3" uiname="in1" value="0.0, 0.0, 0.0" />
    <input name="in2" type="vector3" uiname="in2" value="0.0, 0.0, 0.0" />
    <output name="out" type="float" />
  </nodedef>
  <nodedef name="ND_distance_vector4" node="distance" nodegroup="math">
    <input name="in1" type="vector4" uiname="in1" value="0.0, 0.0, 0.0, 0.0" />
    <input name="in2" type="vector4" uiname="in2" value="0.0, 0.0, 0.0, 0.0" />
    <output name="out" type="float" />
  </nodedef>

  <!--
    Node: <dotproduct>
    Perform a dot product of two 2-4 channel vectors
  -->
  <nodedef name="ND_dotproduct_vector2" node="dotproduct" nodegroup="math">
    <input name="in1" type="vector2" value="0.0, 0.0" />
    <input name="in2" type="vector2" value="0.0, 0.0" />
    <output name="out" type="float" default="0.0" />
  </nodedef>
  <nodedef name="ND_dotproduct_vector3" node="dotproduct" nodegroup="math">
    <input name="in1" type="vector3" value="0.0, 0.0, 0.0" />
    <input name="in2" type="vector3" value="0.0, 0.0, 0.0" />
    <output name="out" type="float" default="0.0" />
  </nodedef>
  <nodedef name="ND_dotproduct_vector4" node="dotproduct" nodegroup="math">
    <input name="in1" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="in2" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <output name="out" type="float" default="0.0" />
  </nodedef>

  <!--
    Node: <crossproduct>
    Perform a cross product of two vectors
  -->
  <nodedef name="ND_crossproduct_vector3" node="crossproduct" nodegroup="math">
    <input name="in1" type="vector3" value="0.0, 0.0, 0.0" />
    <input name="in2" type="vector3" value="0.0, 0.0, 0.0" />
    <output name="out" type="vector3" defaultinput="in1" />
  </nodedef>

  <!--
    Node: <transformpoint>
    Transform a vector3 coordinate from one named space to another.
  -->
  <nodedef name="ND_transformpoint_vector3" node="transformpoint" nodegroup="math">
    <input name="in" type="vector3" value="0.0, 0.0, 0.0" />
    <input name="fromspace" type="string" value="" uniform="true" />
    <input name="tospace" type="string" value="" uniform="true" />
    <output name="out" type="vector3" defaultinput="in" />
  </nodedef>

  <!--
    Node: <transformvector>
    Transform a vector from one named space to another.
  -->
  <nodedef name="ND_transformvector_vector3" node="transformvector" nodegroup="math">
    <input name="in" type="vector3" value="0.0, 0.0, 0.0" />
    <input name="fromspace" type="string" value="" uniform="true" />
    <input name="tospace" type="string" value="" uniform="true" />
    <output name="out" type="vector3" defaultinput="in" />
  </nodedef>
  <!--
    Node: <transformnormal>
    Transform a normal vector from one named space to another.
  -->
  <nodedef name="ND_transformnormal_vector3" node="transformnormal" nodegroup="math">
    <input name="in" type="vector3" value="0.0, 0.0, 1.0" />
    <input name="fromspace" type="string" value="" uniform="true" />
    <input name="tospace" type="string" value="" uniform="true" />
    <output name="out" type="vector3" defaultinput="in" />
  </nodedef>

  <!--
    Node: <transformmatrix>
    Transform a vector by a matrix.
  -->
  <nodedef name="ND_transformmatrix_vector2M3" node="transformmatrix" nodegroup="math">
    <input name="in" type="vector2" value="0.0, 0.0" />
    <input name="mat" type="matrix33" value="1.0,0.0,0.0, 0.0,1.0,0.0, 0.0,0.0,1.0" />
    <output name="out" type="vector2" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_transformmatrix_vector3" node="transformmatrix" nodegroup="math">
    <input name="in" type="vector3" value="0.0, 0.0, 0.0" />
    <input name="mat" type="matrix33" value="1.0,0.0,0.0, 0.0,1.0,0.0, 0.0,0.0,1.0" />
    <output name="out" type="vector3" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_transformmatrix_vector3M4" node="transformmatrix" nodegroup="math">
    <input name="in" type="vector3" value="0.0, 0.0, 0.0" />
    <input name="mat" type="matrix44" value="1.0,0.0,0.0,0.0, 0.0,1.0,0.0,0.0, 0.0,0.0,1.0,0.0, 0.0,0.0,0.0,1.0" />
    <output name="out" type="vector3" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_transformmatrix_vector4" node="transformmatrix" nodegroup="math">
    <input name="in" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="mat" type="matrix44" value="1.0,0.0,0.0,0.0, 0.0,1.0,0.0,0.0, 0.0,0.0,1.0,0.0, 0.0,0.0,0.0,1.0" />
    <output name="out" type="vector4" defaultinput="in" />
  </nodedef>

  <!--
    Node: <normalmap>
    Transform a normal vector from object or tangent space into "world" space.
  -->
  <nodedef name="ND_normalmap_float" node="normalmap" nodegroup="math">
    <input name="in" type="vector3" value="0.5, 0.5, 1.0" />
    <input name="scale" type="float" value="1.0" />
    <input name="normal" type="vector3" defaultgeomprop="Nworld" />
    <input name="tangent" type="vector3" defaultgeomprop="Tworld" />
    <input name="bitangent" type="vector3" defaultgeomprop="Bworld" />
    <output name="out" type="vector3" defaultinput="normal" />
  </nodedef>
  <nodedef name="ND_normalmap_vector2" node="normalmap" nodegroup="math">
    <input name="in" type="vector3" value="0.5, 0.5, 1.0" />
    <input name="scale" type="vector2" value="1.0, 1.0" />
    <input name="normal" type="vector3" defaultgeomprop="Nworld" />
    <input name="tangent" type="vector3" defaultgeomprop="Tworld" />
    <input name="bitangent" type="vector3" defaultgeomprop="Bworld" />
    <output name="out" type="vector3" defaultinput="normal" />
  </nodedef>

  <!--
    Node: <transpose>
    Output the transpose of the incoming matrix.
  -->
  <nodedef name="ND_transpose_matrix33" node="transpose" nodegroup="math">
    <input name="in" type="matrix33" value="1.0,0.0,0.0, 0.0,1.0,0.0, 0.0,0.0,1.0" />
    <output name="out" type="matrix33" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_transpose_matrix44" node="transpose" nodegroup="math">
    <input name="in" type="matrix44" value="1.0,0.0,0.0,0.0, 0.0,1.0,0.0,0.0, 0.0,0.0,1.0,0.0, 0.0,0.0,0.0,1.0" />
    <output name="out" type="matrix44" defaultinput="in" />
  </nodedef>

  <!--
    Node: <determinant>
    Output the determinant of the incoming matrix.
  -->
  <nodedef name="ND_determinant_matrix33" node="determinant" nodegroup="math">
    <input name="in" type="matrix33" value="1.0,0.0,0.0, 0.0,1.0,0.0, 0.0,0.0,1.0" />
    <output name="out" type="float" default="1.0" />
  </nodedef>
  <nodedef name="ND_determinant_matrix44" node="determinant" nodegroup="math">
    <input name="in" type="matrix44" value="1.0,0.0,0.0,0.0, 0.0,1.0,0.0,0.0, 0.0,0.0,1.0,0.0, 0.0,0.0,0.0,1.0" />
    <output name="out" type="float" default="1.0" />
  </nodedef>

  <!--
    Node: <invertmatrix>
    Invert an incoming matrix.
  -->
  <nodedef name="ND_invertmatrix_matrix33" node="invertmatrix" nodegroup="math">
    <input name="in" type="matrix33" value="1.0,0.0,0.0, 0.0,1.0,0.0, 0.0,0.0,1.0" />
    <output name="out" type="matrix33" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_invertmatrix_matrix44" node="invertmatrix" nodegroup="math">
    <input name="in" type="matrix44" value="1.0,0.0,0.0,0.0, 0.0,1.0,0.0,0.0, 0.0,0.0,1.0,0.0, 0.0,0.0,0.0,1.0" />
    <output name="out" type="matrix44" defaultinput="in" />
  </nodedef>

  <!--
    Node: <rotate2d>
    Rotate a vector2 value about the origin.
  -->
  <nodedef name="ND_rotate2d_vector2" node="rotate2d" nodegroup="math">
    <input name="in" type="vector2" value="0.0, 0.0" />
    <input name="amount" type="float" value="0.0" unittype="angle" unit="degree" />
    <output name="out" type="vector2" defaultinput="in" />
  </nodedef>

  <!--
    Node: <rotate3d>
    Rotate a vector3 value about a specified unit axis vector
  -->
  <nodedef name="ND_rotate3d_vector3" node="rotate3d" nodegroup="math">
    <input name="in" type="vector3" value="0.0, 0.0, 0.0" />
    <input name="amount" type="float" value="0.0" unittype="angle" unit="degree" />
    <input name="axis" type="vector3" value="0.0, 1.0, 0.0" />
    <output name="out" type="vector3" defaultinput="in" />
  </nodedef>

  <!--
    Node: <place2d> Supplemental Node
    Transform incoming UV texture coordinates from one 2D frame of reference to another.
    operationorder (integer enum): the order in which to perform the transform operations.
    "0" or "SRT" performs -pivot, scale, rotate, translate, +pivot as per the original
    implementation matching the behavior of certain DCC packages, and "1" or "TRS" performs
    -pivot, translate, rotate, scale, +pivot which does not introduce texture shear.
    Default is 0 "SRT" for backward compatibility.
  -->
  <nodedef name="ND_place2d_vector2" node="place2d" nodegroup="math">
    <input name="texcoord" type="vector2" value="0.0, 0.0" />
    <input name="pivot" type="vector2" value="0.0,0.0" />
    <input name="scale" type="vector2" value="1.0,1.0" />
    <input name="rotate" type="float" value="0.0" unittype="angle" unit="degree" />
    <input name="offset" type="vector2" value="0.0,0.0" />
    <input name="operationorder" type="integer" value="0" enum="SRT, TRS" enumvalues="0, 1" />
    <output name="out" type="vector2" defaultinput="texcoord" />
  </nodedef>

  <!--
    Node: <trianglewave>
    Generate a triangle wave from the given scalar input.
    The generated wave ranges from zero to one and repeats on integer boundaries.
  -->
  <nodedef name="ND_trianglewave_float" node="trianglewave" nodegroup="math">
    <input name="in" type="float" value="0" />
    <output name="out" type="float" />
  </nodedef>

  <!--
    Node: <reflect>
    Compute the reflection vector given an incident vector and unit surface normal.
  -->
  <nodedef name="ND_reflect_vector3" node="reflect" nodegroup="math" doc="Compute the reflection vector">
    <input name="in" type="vector3" value="1.0, 0.0, 0.0" doc="Incident vector" />
    <input name="normal" type="vector3" defaultgeomprop="Nworld" doc="Surface normal" />
    <output name="out" type="vector3" />
  </nodedef>

  <!--
    Node: <refract>
    Compute the refraction vector given an incident vector, unit surface normal,
    and index of refraction.
  -->
  <nodedef name="ND_refract_vector3" node="refract" nodegroup="math" doc="Compute the refraction vector">
    <input name="in" type="vector3" value="1.0, 0.0, 0.0" doc="Incident vector" />
    <input name="normal" type="vector3" defaultgeomprop="Nworld" doc="Surface normal" />
    <input name="ior" type="float" value="1.0" doc="Index of refraction" />
    <output name="out" type="vector3" />
  </nodedef>

  <!-- ======================================================================== -->
  <!-- Adjustment nodes                                                         -->
  <!-- ======================================================================== -->

  <!--
    Node: <remap>
    Remap a value from one range of float/color/vector values to another.
  -->
  <nodedef name="ND_remap_float" node="remap" nodegroup="adjustment">
    <input name="in" type="float" value="0.0" />
    <input name="inlow" type="float" value="0.0" />
    <input name="inhigh" type="float" value="1.0" />
    <input name="outlow" type="float" value="0.0" />
    <input name="outhigh" type="float" value="1.0" />
    <output name="out" type="float" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_remap_color3" node="remap" nodegroup="adjustment">
    <input name="in" type="color3" value="0.0, 0.0, 0.0" />
    <input name="inlow" type="color3" value="0.0, 0.0, 0.0" />
    <input name="inhigh" type="color3" value="1.0, 1.0, 1.0" />
    <input name="outlow" type="color3" value="0.0, 0.0, 0.0" />
    <input name="outhigh" type="color3" value="1.0, 1.0, 1.0" />
    <output name="out" type="color3" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_remap_color4" node="remap" nodegroup="adjustment">
    <input name="in" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="inlow" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="inhigh" type="color4" value="1.0, 1.0, 1.0, 1.0" />
    <input name="outlow" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="outhigh" type="color4" value="1.0, 1.0, 1.0, 1.0" />
    <output name="out" type="color4" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_remap_vector2" node="remap" nodegroup="adjustment">
    <input name="in" type="vector2" value="0.0, 0.0" />
    <input name="inlow" type="vector2" value="0.0, 0.0" />
    <input name="inhigh" type="vector2" value="1.0, 1.0" />
    <input name="outlow" type="vector2" value="0.0, 0.0" />
    <input name="outhigh" type="vector2" value="1.0, 1.0" />
    <output name="out" type="vector2" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_remap_vector3" node="remap" nodegroup="adjustment">
    <input name="in" type="vector3" value="0.0, 0.0, 0.0" />
    <input name="inlow" type="vector3" value="0.0, 0.0, 0.0" />
    <input name="inhigh" type="vector3" value="1.0, 1.0, 1.0" />
    <input name="outlow" type="vector3" value="0.0, 0.0, 0.0" />
    <input name="outhigh" type="vector3" value="1.0, 1.0, 1.0" />
    <output name="out" type="vector3" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_remap_vector4" node="remap" nodegroup="adjustment">
    <input name="in" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="inlow" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="inhigh" type="vector4" value="1.0, 1.0, 1.0, 1.0" />
    <input name="outlow" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="outhigh" type="vector4" value="1.0, 1.0, 1.0, 1.0" />
    <output name="out" type="vector4" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_remap_color3FA" node="remap" nodegroup="adjustment">
    <input name="in" type="color3" value="0.0, 0.0, 0.0" />
    <input name="inlow" type="float" value="0.0" />
    <input name="inhigh" type="float" value="1.0" />
    <input name="outlow" type="float" value="0.0" />
    <input name="outhigh" type="float" value="1.0" />
    <output name="out" type="color3" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_remap_color4FA" node="remap" nodegroup="adjustment">
    <input name="in" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="inlow" type="float" value="0.0" />
    <input name="inhigh" type="float" value="1.0" />
    <input name="outlow" type="float" value="0.0" />
    <input name="outhigh" type="float" value="1.0" />
    <output name="out" type="color4" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_remap_vector2FA" node="remap" nodegroup="adjustment">
    <input name="in" type="vector2" value="0.0, 0.0" />
    <input name="inlow" type="float" value="0.0" />
    <input name="inhigh" type="float" value="1.0" />
    <input name="outlow" type="float" value="0.0" />
    <input name="outhigh" type="float" value="1.0" />
    <output name="out" type="vector2" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_remap_vector3FA" node="remap" nodegroup="adjustment">
    <input name="in" type="vector3" value="0.0, 0.0, 0.0" />
    <input name="inlow" type="float" value="0.0" />
    <input name="inhigh" type="float" value="1.0" />
    <input name="outlow" type="float" value="0.0" />
    <input name="outhigh" type="float" value="1.0" />
    <output name="out" type="vector3" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_remap_vector4FA" node="remap" nodegroup="adjustment">
    <input name="in" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="inlow" type="float" value="0.0" />
    <input name="inhigh" type="float" value="1.0" />
    <input name="outlow" type="float" value="0.0" />
    <input name="outhigh" type="float" value="1.0" />
    <output name="out" type="vector4" defaultinput="in" />
  </nodedef>

  <!--
    Node: <smoothstep>
    Outputs a smooth (hermite-interpolated) remapping of input values from low-high
    to output 0-1.
  -->
  <nodedef name="ND_smoothstep_float" node="smoothstep" nodegroup="adjustment">
    <input name="in" type="float" value="0.0" />
    <input name="low" type="float" value="0.0" />
    <input name="high" type="float" value="1.0" />
    <output name="out" type="float" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_smoothstep_color3" node="smoothstep" nodegroup="adjustment">
    <input name="in" type="color3" value="0.0, 0.0, 0.0" />
    <input name="low" type="color3" value="0.0, 0.0, 0.0" />
    <input name="high" type="color3" value="1.0, 1.0, 1.0" />
    <output name="out" type="color3" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_smoothstep_color4" node="smoothstep" nodegroup="adjustment">
    <input name="in" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="low" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="high" type="color4" value="1.0, 1.0, 1.0, 1.0" />
    <output name="out" type="color4" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_smoothstep_vector2" node="smoothstep" nodegroup="adjustment">
    <input name="in" type="vector2" value="0.0, 0.0" />
    <input name="low" type="vector2" value="0.0, 0.0" />
    <input name="high" type="vector2" value="1.0, 1.0" />
    <output name="out" type="vector2" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_smoothstep_vector3" node="smoothstep" nodegroup="adjustment">
    <input name="in" type="vector3" value="0.0, 0.0, 0.0" />
    <input name="low" type="vector3" value="0.0, 0.0, 0.0" />
    <input name="high" type="vector3" value="1.0, 1.0, 1.0" />
    <output name="out" type="vector3" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_smoothstep_vector4" node="smoothstep" nodegroup="adjustment">
    <input name="in" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="low" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="high" type="vector4" value="1.0, 1.0, 1.0, 1.0" />
    <output name="out" type="vector4" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_smoothstep_color3FA" node="smoothstep" nodegroup="adjustment">
    <input name="in" type="color3" value="0.0, 0.0, 0.0" />
    <input name="low" type="float" value="0.0" />
    <input name="high" type="float" value="1.0" />
    <output name="out" type="color3" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_smoothstep_color4FA" node="smoothstep" nodegroup="adjustment">
    <input name="in" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="low" type="float" value="0.0" />
    <input name="high" type="float" value="1.0" />
    <output name="out" type="color4" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_smoothstep_vector2FA" node="smoothstep" nodegroup="adjustment">
    <input name="in" type="vector2" value="0.0, 0.0" />
    <input name="low" type="float" value="0.0" />
    <input name="high" type="float" value="1.0" />
    <output name="out" type="vector2" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_smoothstep_vector3FA" node="smoothstep" nodegroup="adjustment">
    <input name="in" type="vector3" value="0.0, 0.0, 0.0" />
    <input name="low" type="float" value="0.0" />
    <input name="high" type="float" value="1.0" />
    <output name="out" type="vector3" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_smoothstep_vector4FA" node="smoothstep" nodegroup="adjustment">
    <input name="in" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="low" type="float" value="0.0" />
    <input name="high" type="float" value="1.0" />
    <output name="out" type="vector4" defaultinput="in" />
  </nodedef>

  <!--
    Node: <luminance>
    Output a grayscale image containing the luminance of the incoming RGB color in all color channels;
    the alpha channel is left unchanged if present.
  -->
  <nodedef name="ND_luminance_color3" node="luminance" nodegroup="adjustment">
    <input name="in" type="color3" value="0.0, 0.0, 0.0" />
    <input name="lumacoeffs" type="color3" value="0.2722287, 0.6740818, 0.0536895" enum="acescg, rec709, rec2020, rec2100" enumvalues="0.2722287,0.6740818,0.0536895, 0.2126,0.7152,0.0722, 0.2627,0.6780,0.0593, 0.2627,0.6780,0.0593" />
    <output name="out" type="color3" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_luminance_color4" node="luminance" nodegroup="adjustment">
    <input name="in" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="lumacoeffs" type="color3" value="0.2722287, 0.6740818, 0.0536895" enum="acescg, rec709, rec2020, rec2100" enumvalues="0.2722287,0.6740818,0.0536895, 0.2126,0.7152,0.0722, 0.2627,0.6780,0.0593, 0.2627,0.6780,0.0593" />
    <output name="out" type="color4" defaultinput="in" />
  </nodedef>

  <!--
    Nodes: <rgbtohsv> and <hsvtorgb>
    Convert an incoming color between RGB and HSV space, with H and S ranging from 0-1.
  -->
  <nodedef name="ND_rgbtohsv_color3" node="rgbtohsv" nodegroup="adjustment">
    <input name="in" type="color3" value="0.0, 0.0, 0.0" />
    <output name="out" type="color3" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_rgbtohsv_color4" node="rgbtohsv" nodegroup="adjustment">
    <input name="in" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <output name="out" type="color4" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_hsvtorgb_color3" node="hsvtorgb" nodegroup="adjustment">
    <input name="in" type="color3" value="0.0, 0.0, 0.0" />
    <output name="out" type="color3" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_hsvtorgb_color4" node="hsvtorgb" nodegroup="adjustment">
    <input name="in" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <output name="out" type="color4" defaultinput="in" />
  </nodedef>

  <!--
    Node: <contrast> Supplemental Node
    Increase or decrease contrast of a float/color value using a linear slope multiplier.
  -->
  <nodedef name="ND_contrast_float" node="contrast" nodegroup="adjustment">
    <input name="in" type="float" value="0.0" />
    <input name="amount" type="float" value="1.0" />
    <input name="pivot" type="float" value="0.5" />
    <output name="out" type="float" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_contrast_color3" node="contrast" nodegroup="adjustment">
    <input name="in" type="color3" value="0.0, 0.0, 0.0" />
    <input name="amount" type="color3" value="1.0, 1.0, 1.0" />
    <input name="pivot" type="color3" value="0.5, 0.5, 0.5" />
    <output name="out" type="color3" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_contrast_color4" node="contrast" nodegroup="adjustment">
    <input name="in" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="amount" type="color4" value="1.0, 1.0, 1.0, 1.0" />
    <input name="pivot" type="color4" value="0.5, 0.5, 0.5, 0.5" />
    <output name="out" type="color4" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_contrast_vector2" node="contrast" nodegroup="adjustment">
    <input name="in" type="vector2" value="0.0, 0.0" />
    <input name="amount" type="vector2" value="1.0, 1.0" />
    <input name="pivot" type="vector2" value="0.5, 0.5" />
    <output name="out" type="vector2" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_contrast_vector3" node="contrast" nodegroup="adjustment">
    <input name="in" type="vector3" value="0.0, 0.0, 0.0" />
    <input name="amount" type="vector3" value="1.0, 1.0, 1.0" />
    <input name="pivot" type="vector3" value="0.5, 0.5, 0.5" />
    <output name="out" type="vector3" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_contrast_vector4" node="contrast" nodegroup="adjustment">
    <input name="in" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="amount" type="vector4" value="1.0, 1.0, 1.0, 1.0" />
    <input name="pivot" type="vector4" value="0.5, 0.5, 0.5, 0.5" />
    <output name="out" type="vector4" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_contrast_color3FA" node="contrast" nodegroup="adjustment">
    <input name="in" type="color3" value="0.0, 0.0, 0.0" />
    <input name="amount" type="float" value="1.0" />
    <input name="pivot" type="float" value="0.5" />
    <output name="out" type="color3" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_contrast_color4FA" node="contrast" nodegroup="adjustment">
    <input name="in" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="amount" type="float" value="1.0" />
    <input name="pivot" type="float" value="0.5" />
    <output name="out" type="color4" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_contrast_vector2FA" node="contrast" nodegroup="adjustment">
    <input name="in" type="vector2" value="0.0, 0.0" />
    <input name="amount" type="float" value="1.0" />
    <input name="pivot" type="float" value="0.5" />
    <output name="out" type="vector2" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_contrast_vector3FA" node="contrast" nodegroup="adjustment">
    <input name="in" type="vector3" value="0.0, 0.0, 0.0" />
    <input name="amount" type="float" value="1.0" />
    <input name="pivot" type="float" value="0.5" />
    <output name="out" type="vector3" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_contrast_vector4FA" node="contrast" nodegroup="adjustment">
    <input name="in" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="amount" type="float" value="1.0" />
    <input name="pivot" type="float" value="0.5" />
    <output name="out" type="vector4" defaultinput="in" />
  </nodedef>

  <!--
    Node: <range> Supplemental Node
    Remap a value from one range of float/color/vector values to another, optionally
    applying a gamma correction in the middle, and optionally clamping output values.
  -->
  <nodedef name="ND_range_float" node="range" nodegroup="adjustment">
    <input name="in" type="float" value="0.0" />
    <input name="inlow" type="float" value="0.0" />
    <input name="inhigh" type="float" value="1.0" />
    <input name="gamma" type="float" value="1.0" />
    <input name="outlow" type="float" value="0.0" />
    <input name="outhigh" type="float" value="1.0" />
    <input name="doclamp" type="boolean" value="false" />
    <output name="out" type="float" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_range_color3" node="range" nodegroup="adjustment">
    <input name="in" type="color3" value="0.0, 0.0, 0.0" />
    <input name="inlow" type="color3" value="0.0, 0.0, 0.0" />
    <input name="inhigh" type="color3" value="1.0, 1.0, 1.0" />
    <input name="gamma" type="color3" value="1.0, 1.0, 1.0" />
    <input name="outlow" type="color3" value="0.0, 0.0, 0.0" />
    <input name="outhigh" type="color3" value="1.0, 1.0, 1.0" />
    <input name="doclamp" type="boolean" value="false" />
    <output name="out" type="color3" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_range_color4" node="range" nodegroup="adjustment">
    <input name="in" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="inlow" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="inhigh" type="color4" value="1.0, 1.0, 1.0, 1.0" />
    <input name="gamma" type="color4" value="1.0, 1.0, 1.0, 1.0" />
    <input name="outlow" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="outhigh" type="color4" value="1.0, 1.0, 1.0, 1.0" />
    <input name="doclamp" type="boolean" value="false" />
    <output name="out" type="color4" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_range_vector2" node="range" nodegroup="adjustment">
    <input name="in" type="vector2" value="0.0, 0.0" />
    <input name="inlow" type="vector2" value="0.0, 0.0" />
    <input name="inhigh" type="vector2" value="1.0, 1.0" />
    <input name="gamma" type="vector2" value="1.0, 1.0" />
    <input name="outlow" type="vector2" value="0.0, 0.0" />
    <input name="outhigh" type="vector2" value="1.0, 1.0" />
    <input name="doclamp" type="boolean" value="false" />
    <output name="out" type="vector2" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_range_vector3" node="range" nodegroup="adjustment">
    <input name="in" type="vector3" value="0.0, 0.0, 0.0" />
    <input name="inlow" type="vector3" value="0.0, 0.0, 0.0" />
    <input name="inhigh" type="vector3" value="1.0, 1.0, 1.0" />
    <input name="gamma" type="vector3" value="1.0, 1.0, 1.0" />
    <input name="outlow" type="vector3" value="0.0, 0.0, 0.0" />
    <input name="outhigh" type="vector3" value="1.0, 1.0, 1.0" />
    <input name="doclamp" type="boolean" value="false" />
    <output name="out" type="vector3" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_range_vector4" node="range" nodegroup="adjustment">
    <input name="in" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="inlow" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="inhigh" type="vector4" value="1.0, 1.0, 1.0, 1.0" />
    <input name="gamma" type="vector4" value="1.0, 1.0, 1.0, 1.0" />
    <input name="outlow" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="outhigh" type="vector4" value="1.0, 1.0, 1.0, 1.0" />
    <input name="doclamp" type="boolean" value="false" />
    <output name="out" type="vector4" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_range_color3FA" node="range" nodegroup="adjustment">
    <input name="in" type="color3" value="0.0, 0.0, 0.0" />
    <input name="inlow" type="float" value="0.0" />
    <input name="inhigh" type="float" value="1.0" />
    <input name="gamma" type="float" value="1.0" />
    <input name="outlow" type="float" value="0.0" />
    <input name="outhigh" type="float" value="1.0" />
    <input name="doclamp" type="boolean" value="false" />
    <output name="out" type="color3" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_range_color4FA" node="range" nodegroup="adjustment">
    <input name="in" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="inlow" type="float" value="0.0" />
    <input name="inhigh" type="float" value="1.0" />
    <input name="gamma" type="float" value="1.0" />
    <input name="outlow" type="float" value="0.0" />
    <input name="outhigh" type="float" value="1.0" />
    <input name="doclamp" type="boolean" value="false" />
    <output name="out" type="color4" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_range_vector2FA" node="range" nodegroup="adjustment">
    <input name="in" type="vector2" value="0.0, 0.0" />
    <input name="inlow" type="float" value="0.0" />
    <input name="inhigh" type="float" value="1.0" />
    <input name="gamma" type="float" value="1.0" />
    <input name="outlow" type="float" value="0.0" />
    <input name="outhigh" type="float" value="1.0" />
    <input name="doclamp" type="boolean" value="false" />
    <output name="out" type="vector2" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_range_vector3FA" node="range" nodegroup="adjustment">
    <input name="in" type="vector3" value="0.0, 0.0, 0.0" />
    <input name="inlow" type="float" value="0.0" />
    <input name="inhigh" type="float" value="1.0" />
    <input name="gamma" type="float" value="1.0" />
    <input name="outlow" type="float" value="0.0" />
    <input name="outhigh" type="float" value="1.0" />
    <input name="doclamp" type="boolean" value="false" />
    <output name="out" type="vector3" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_range_vector4FA" node="range" nodegroup="adjustment">
    <input name="in" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="inlow" type="float" value="0.0" />
    <input name="inhigh" type="float" value="1.0" />
    <input name="gamma" type="float" value="1.0" />
    <input name="outlow" type="float" value="0.0" />
    <input name="outhigh" type="float" value="1.0" />
    <input name="doclamp" type="boolean" value="false" />
    <output name="out" type="vector4" defaultinput="in" />
  </nodedef>

  <!--
    Node: <hsvadjust> Supplemental Node
    Adjust the hue, saturation and value of an RGB color by converting the input color
    to HSV, adding amount.x to the hue, multiplying the saturation by amount.y,
    multiplying the value by amount.z, then converting back to RGB.
  -->
  <nodedef name="ND_hsvadjust_color3" node="hsvadjust" nodegroup="adjustment">
    <input name="in" type="color3" value="0.0, 0.0, 0.0" />
    <input name="amount" type="vector3" value="0.0, 1.0, 1.0" />
    <output name="out" type="color3" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_hsvadjust_color4" node="hsvadjust" nodegroup="adjustment">
    <input name="in" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="amount" type="vector3" value="0.0, 1.0, 1.0" />
    <output name="out" type="color4" defaultinput="in" />
  </nodedef>

  <!--
    Node: <saturate> Supplemental Node
    Adjust the saturation of a color using a linear interpolation between the incoming
    color and the grayscale luminance of the input computed using the provided luma
    coefficients; the alpha channel will be unchanged if present.
  -->
  <nodedef name="ND_saturate_color3" node="saturate" nodegroup="adjustment">
    <input name="in" type="color3" value="0.0, 0.0, 0.0" />
    <input name="amount" type="float" value="1.0" />
    <input name="lumacoeffs" type="color3" value="0.2722287, 0.6740818, 0.0536895" enum="acescg, rec709, rec2020, rec2100" enumvalues="0.2722287,0.6740818,0.0536895, 0.2126,0.7152,0.0722, 0.2627,0.6780,0.0593, 0.2627,0.6780,0.0593" />
    <output name="out" type="color3" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_saturate_color4" node="saturate" nodegroup="adjustment">
    <input name="in" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="amount" type="float" value="1.0" />
    <input name="lumacoeffs" type="color3" value="0.2722287, 0.6740818, 0.0536895" enum="acescg, rec709, rec2020, rec2100" enumvalues="0.2722287,0.6740818,0.0536895, 0.2126,0.7152,0.0722, 0.2627,0.6780,0.0593, 0.2627,0.6780,0.0593" />
    <output name="out" type="color4" defaultinput="in" />
  </nodedef>

  <!--
    Node: <colorcorrect> Supplemental Node
    Combines various adjustment nodes into one, artist-friendly color correction node.
    The color4 signature does not touch the alpha channel.
  -->
  <nodedef name="ND_colorcorrect_color3" node="colorcorrect" nodegroup="adjustment">
    <input name="in" type="color3" uiname="Input Color" value="1, 1, 1" doc="The input color to be adjusted." />
    <input name="hue" type="float" uiname="Hue" uisoftmin="0.0" uisoftmax="1.0" value="0" doc="Rotates the color hue, with values wrapping at 0-1 boundaries." />
    <input name="saturation" type="float" uiname="Saturation" uisoftmin="0.0" uisoftmax="1.0" value="1" doc="Adjusts the input color saturation level." />
    <input name="gamma" type="float" uiname="Gamma" uisoftmin="0.0" uisoftmax="3.0" value="1" doc="Applies a gamma correction to the color." />
    <input name="lift" type="float" uiname="Lift" uisoftmin="0.0" uisoftmax="1.0" value="0" doc="Raise the dark color values, leaving the white values unchanged." />
    <input name="gain" type="float" uiname="Gain" uisoftmin="0.0" uisoftmax="1.0" value="1" doc="Multiplier increases lighter color values, leaving black values unchanged." />
    <input name="contrast" type="float" uiname="Contrast" uisoftmin="0.0" uisoftmax="1.0" value="1" doc="Linearly increase or decrease the color contrast." />
    <input name="contrastpivot" type="float" uiname="Contrast Pivot" uisoftmin="0.0" uisoftmax="1.0" value="0.5" doc="Pivot value around which contrast applies. This value will not change as contrast is adjusted." />
    <input name="exposure" type="float" uiname="Exposure" uisoftmin="-1.0" uisoftmax="1.0" value="0" doc="Multiplier which increases or decreases color brightness by 2^value." />
    <output name="out" type="color3" />
  </nodedef>
  <nodedef name="ND_colorcorrect_color4" node="colorcorrect" nodegroup="adjustment">
    <input name="in" type="color4" uiname="Input Color" value="1, 1, 1, 0" doc="The input color to be adjusted." />
    <input name="hue" type="float" uiname="Hue" uisoftmin="0.0" uisoftmax="1.0" value="0" doc="Rotates the color hue, with values wrapping at 0-1 boundaries." />
    <input name="saturation" type="float" uiname="Saturation" uisoftmin="0.0" uisoftmax="1.0" value="1" doc="Adjusts the input color saturation level." />
    <input name="gamma" type="float" uiname="Gamma" uisoftmin="0.0" uisoftmax="3.0" value="1" doc="Applies a gamma correction to the color." />
    <input name="lift" type="float" uiname="Lift" uisoftmin="0.0" uisoftmax="1.0" value="0" doc="Raise the dark color values, leaving the white values unchanged." />
    <input name="gain" type="float" uiname="Gain" uisoftmin="0.0" uisoftmax="1.0" value="1" doc="Multiplier increases lighter color values, leaving black values unchanged." />
    <input name="contrast" type="float" uiname="Contrast" uisoftmin="0.0" uisoftmax="1.0" value="1" doc="Linearly increase or decrease the color contrast." />
    <input name="contrastpivot" type="float" uiname="Contrast Pivot" uisoftmin="0.0" uisoftmax="1.0" value="0.5" doc="Pivot value around which contrast applies. This value will not change as contrast is adjusted." />
    <input name="exposure" type="float" uiname="Exposure" uisoftmin="-1.0" uisoftmax="1.0" value="0" doc="Multiplier which increases or decreases color brightness by 2^value." />
    <output name="out" type="color4" />
  </nodedef>

  <!-- ======================================================================== -->
  <!-- Compositing nodes                                                        -->
  <!-- ======================================================================== -->

  <!--
    Node: <premult>
    Multiply the R or RGB channels of the input by the Alpha channel of the input.
  -->
  <nodedef name="ND_premult_color4" node="premult" nodegroup="compositing">
    <input name="in" type="color4" value="0.0, 0.0, 0.0, 1.0" />
    <output name="out" type="color4" defaultinput="in" />
  </nodedef>

  <!--
    Node: <unpremult>
    Divide the R or RGB channels of the input by the Alpha channel of the input.
    If the Alpha value is zero, it is passed through unchanged.
  -->
  <nodedef name="ND_unpremult_color4" node="unpremult" nodegroup="compositing">
    <input name="in" type="color4" value="0.0, 0.0, 0.0, 1.0" />
    <output name="out" type="color4" defaultinput="in" />
  </nodedef>

  <!--
    Node: <plus>
    Add two 1-4 channel inputs, with optional mixing between the bg input and the result.
  -->
  <nodedef name="ND_plus_float" node="plus" nodegroup="compositing">
    <input name="fg" type="float" value="0.0" />
    <input name="bg" type="float" value="0.0" />
    <input name="mix" type="float" value="1.0" />
    <output name="out" type="float" defaultinput="bg" />
  </nodedef>
  <nodedef name="ND_plus_color3" node="plus" nodegroup="compositing">
    <input name="fg" type="color3" value="0.0, 0.0, 0.0" />
    <input name="bg" type="color3" value="0.0, 0.0, 0.0" />
    <input name="mix" type="float" value="1.0" />
    <output name="out" type="color3" defaultinput="bg" />
  </nodedef>
  <nodedef name="ND_plus_color4" node="plus" nodegroup="compositing">
    <input name="fg" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="bg" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="mix" type="float" value="1.0" />
    <output name="out" type="color4" defaultinput="bg" />
  </nodedef>

  <!--
    Node: <minus>
    Subtract two 1-4 channel inputs, with optional mixing between the bg input and the result.
  -->
  <nodedef name="ND_minus_float" node="minus" nodegroup="compositing">
    <input name="fg" type="float" value="0.0" />
    <input name="bg" type="float" value="0.0" />
    <input name="mix" type="float" value="1.0" />
    <output name="out" type="float" defaultinput="bg" />
  </nodedef>
  <nodedef name="ND_minus_color3" node="minus" nodegroup="compositing">
    <input name="fg" type="color3" value="0.0, 0.0, 0.0" />
    <input name="bg" type="color3" value="0.0, 0.0, 0.0" />
    <input name="mix" type="float" value="1.0" />
    <output name="out" type="color3" defaultinput="bg" />
  </nodedef>
  <nodedef name="ND_minus_color4" node="minus" nodegroup="compositing">
    <input name="fg" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="bg" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="mix" type="float" value="1.0" />
    <output name="out" type="color4" defaultinput="bg" />
  </nodedef>

  <!--
    Node: <difference>
    Absolute-value difference of two 1-4 channel inputs, with optional mixing between
    the bg input and the result.
  -->
  <nodedef name="ND_difference_float" node="difference" nodegroup="compositing">
    <input name="fg" type="float" value="0.0" />
    <input name="bg" type="float" value="0.0" />
    <input name="mix" type="float" value="1.0" />
    <output name="out" type="float" defaultinput="bg" />
  </nodedef>
  <nodedef name="ND_difference_color3" node="difference" nodegroup="compositing">
    <input name="fg" type="color3" value="0.0, 0.0, 0.0" />
    <input name="bg" type="color3" value="0.0, 0.0, 0.0" />
    <input name="mix" type="float" value="1.0" />
    <output name="out" type="color3" defaultinput="bg" />
  </nodedef>
  <nodedef name="ND_difference_color4" node="difference" nodegroup="compositing">
    <input name="fg" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="bg" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="mix" type="float" value="1.0" />
    <output name="out" type="color4" defaultinput="bg" />
  </nodedef>

  <!--
    Node: <burn>
    Take two 1-4 channel inputs and apply the same operator to all channels: 1-(1-B)/F
  -->
  <nodedef name="ND_burn_float" node="burn" nodegroup="compositing">
    <input name="fg" type="float" value="0.0" />
    <input name="bg" type="float" value="0.0" />
    <input name="mix" type="float" value="1.0" />
    <output name="out" type="float" defaultinput="bg" />
  </nodedef>
  <nodedef name="ND_burn_color3" node="burn" nodegroup="compositing">
    <input name="fg" type="color3" value="0.0, 0.0, 0.0" />
    <input name="bg" type="color3" value="0.0, 0.0, 0.0" />
    <input name="mix" type="float" value="1.0" />
    <output name="out" type="color3" defaultinput="bg" />
  </nodedef>
  <nodedef name="ND_burn_color4" node="burn" nodegroup="compositing">
    <input name="fg" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="bg" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="mix" type="float" value="1.0" />
    <output name="out" type="color4" defaultinput="bg" />
  </nodedef>

  <!--
    Node: <dodge>
    Take two 1-4 channel inputs and apply the same operator to all channels: B/(1-F)
  -->
  <nodedef name="ND_dodge_float" node="dodge" nodegroup="compositing">
    <input name="fg" type="float" value="0.0" />
    <input name="bg" type="float" value="0.0" />
    <input name="mix" type="float" value="1.0" />
    <output name="out" type="float" defaultinput="bg" />
  </nodedef>
  <nodedef name="ND_dodge_color3" node="dodge" nodegroup="compositing">
    <input name="fg" type="color3" value="0.0, 0.0, 0.0" />
    <input name="bg" type="color3" value="0.0, 0.0, 0.0" />
    <input name="mix" type="float" value="1.0" />
    <output name="out" type="color3" defaultinput="bg" />
  </nodedef>
  <nodedef name="ND_dodge_color4" node="dodge" nodegroup="compositing">
    <input name="fg" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="bg" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="mix" type="float" value="1.0" />
    <output name="out" type="color4" defaultinput="bg" />
  </nodedef>

  <!--
    Node: <screen>
    Take two 1-4 channel inputs and apply the same operator to all channels: 1-(1-F)*(1-B)
  -->
  <nodedef name="ND_screen_float" node="screen" nodegroup="compositing">
    <input name="fg" type="float" value="0.0" />
    <input name="bg" type="float" value="0.0" />
    <input name="mix" type="float" value="1.0" />
    <output name="out" type="float" defaultinput="bg" />
  </nodedef>
  <nodedef name="ND_screen_color3" node="screen" nodegroup="compositing">
    <input name="fg" type="color3" value="0.0, 0.0, 0.0" />
    <input name="bg" type="color3" value="0.0, 0.0, 0.0" />
    <input name="mix" type="float" value="1.0" />
    <output name="out" type="color3" defaultinput="bg" />
  </nodedef>
  <nodedef name="ND_screen_color4" node="screen" nodegroup="compositing">
    <input name="fg" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="bg" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="mix" type="float" value="1.0" />
    <output name="out" type="color4" defaultinput="bg" />
  </nodedef>

  <!--
    Node: <overlay>
    Take two 1-4 channel inputs and apply the same operator to all channels:
      2FB if B<0.5;
      1-2(1-F)(1-B) if B>=0.5
  -->
  <nodedef name="ND_overlay_float" node="overlay" nodegroup="compositing">
    <input name="fg" type="float" value="0.0" />
    <input name="bg" type="float" value="0.0" />
    <input name="mix" type="float" value="1.0" />
    <output name="out" type="float" defaultinput="bg" />
  </nodedef>
  <nodedef name="ND_overlay_color3" node="overlay" nodegroup="compositing">
    <input name="fg" type="color3" value="0.0, 0.0, 0.0" />
    <input name="bg" type="color3" value="0.0, 0.0, 0.0" />
    <input name="mix" type="float" value="1.0" />
    <output name="out" type="color3" defaultinput="bg" />
  </nodedef>
  <nodedef name="ND_overlay_color4" node="overlay" nodegroup="compositing">
    <input name="fg" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="bg" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="mix" type="float" value="1.0" />
    <output name="out" type="color4" defaultinput="bg" />
  </nodedef>

  <!--
    Node: <disjointover>
    Take two color4 inputs and use the built-in alpha
    channel(s) to control the compositing of the fg and bg inputs:
      F+B         if f+b<=1
      F+B(1-f)/b  if f+b>1
      alpha: min(f+b,1)
  -->
  <nodedef name="ND_disjointover_color4" node="disjointover" nodegroup="compositing">
    <input name="fg" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="bg" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="mix" type="float" value="1.0" />
    <output name="out" type="color4" defaultinput="bg" />
  </nodedef>

  <!--
    Node: <in>
    Take two color4 inputs and use the built-in alpha
    channel(s) to control the compositing of the fg and bg inputs: Fb  (alpha: fb)
  -->
  <nodedef name="ND_in_color4" node="in" nodegroup="compositing">
    <input name="fg" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="bg" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="mix" type="float" value="1.0" />
    <output name="out" type="color4" defaultinput="bg" />
  </nodedef>

  <!--
    Node: <mask>
    Take two color4 inputs and use the built-in alpha
    channel(s) to control the compositing of the fg and bg inputs: Bf  (alpha: bf)
  -->
  <nodedef name="ND_mask_color4" node="mask" nodegroup="compositing">
    <input name="fg" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="bg" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="mix" type="float" value="1.0" />
    <output name="out" type="color4" defaultinput="bg" />
  </nodedef>

  <!--
    Node: <matte>
    Take two color4 inputs and use the built-in alpha
    channel(s) to control the compositing of the fg and bg inputs: Ff+B(1-f)  (alpha: f+b(1-f))
  -->
  <nodedef name="ND_matte_color4" node="matte" nodegroup="compositing">
    <input name="fg" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="bg" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="mix" type="float" value="1.0" />
    <output name="out" type="color4" defaultinput="bg" />
  </nodedef>

  <!--
    Node: <out>
    Take two color4 inputs and use the built-in alpha
    channel(s) to control the compositing of the fg and bg inputs: F(1-b)  (alpha: f(1-b))
  -->
  <nodedef name="ND_out_color4" node="out" nodegroup="compositing">
    <input name="fg" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="bg" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="mix" type="float" value="1.0" />
    <output name="out" type="color4" defaultinput="bg" />
  </nodedef>

  <!--
    Node: <over>
    Take two color4 inputs and use the built-in alpha
    channel(s) to control the compositing of the fg and bg inputs: F+B(1-f)  (alpha: f+b(1-f))
  -->
  <nodedef name="ND_over_color4" node="over" nodegroup="compositing">
    <input name="fg" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="bg" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="mix" type="float" value="1.0" />
    <output name="out" type="color4" defaultinput="bg" />
  </nodedef>

  <!--
    Node: <inside>
    Take one 1-4 channel input "in" plus a separate float "mask" input and apply the same
    operator to all channels: in * mask
  -->
  <nodedef name="ND_inside_float" node="inside" nodegroup="compositing">
    <input name="in" type="float" value="0.0" />
    <input name="mask" type="float" value="1.0" />
    <output name="out" type="float" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_inside_color3" node="inside" nodegroup="compositing">
    <input name="in" type="color3" value="0.0, 0.0, 0.0" />
    <input name="mask" type="float" value="1.0" />
    <output name="out" type="color3" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_inside_color4" node="inside" nodegroup="compositing">
    <input name="in" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="mask" type="float" value="1.0" />
    <output name="out" type="color4" defaultinput="in" />
  </nodedef>

  <!--
    Node: <outside>
    Take one 1-4 channel input "in" plus a separate float "mask" input and apply the same
    operator to all channels: in * (1-mask)
  -->
  <nodedef name="ND_outside_float" node="outside" nodegroup="compositing">
    <input name="in" type="float" value="0.0" />
    <input name="mask" type="float" value="0.0" />
    <output name="out" type="float" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_outside_color3" node="outside" nodegroup="compositing">
    <input name="in" type="color3" value="0.0, 0.0, 0.0" />
    <input name="mask" type="float" value="0.0" />
    <output name="out" type="color3" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_outside_color4" node="outside" nodegroup="compositing">
    <input name="in" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="mask" type="float" value="0.0" />
    <output name="out" type="color4" defaultinput="in" />
  </nodedef>

  <!--
    Node: <mix>
    Mix two inputs according to an input mix amount.
  -->
  <nodedef name="ND_mix_float" node="mix" nodegroup="compositing">
    <input name="fg" type="float" value="0.0" />
    <input name="bg" type="float" value="0.0" />
    <input name="mix" type="float" value="0.0" uisoftmin="0.0" uisoftmax="1.0" />
    <output name="out" type="float" defaultinput="bg" />
  </nodedef>
  <nodedef name="ND_mix_color3" node="mix" nodegroup="compositing">
    <input name="fg" type="color3" value="0.0, 0.0, 0.0" />
    <input name="bg" type="color3" value="0.0, 0.0, 0.0" />
    <input name="mix" type="float" value="0.0" uisoftmin="0.0" uisoftmax="1.0" />
    <output name="out" type="color3" defaultinput="bg" />
  </nodedef>
  <nodedef name="ND_mix_color3_color3" node="mix" nodegroup="compositing">
    <input name="fg" type="color3" value="0.0, 0.0, 0.0" />
    <input name="bg" type="color3" value="0.0, 0.0, 0.0" />
    <input name="mix" type="color3" value="0.0, 0.0, 0.0" uisoftmin="0,0,0" uisoftmax="1,1,1" />
    <output name="out" type="color3" defaultinput="bg" />
  </nodedef>
  <nodedef name="ND_mix_color4" node="mix" nodegroup="compositing">
    <input name="fg" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="bg" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="mix" type="float" value="0.0" uisoftmin="0.0" uisoftmax="1.0" />
    <output name="out" type="color4" defaultinput="bg" />
  </nodedef>
  <nodedef name="ND_mix_color4_color4" node="mix" nodegroup="compositing">
    <input name="fg" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="bg" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="mix" type="color4" value="0.0, 0.0, 0.0, 0.0" uisoftmin="0,0,0,0" uisoftmax="1,1,1,1" />
    <output name="out" type="color4" defaultinput="bg" />
  </nodedef>
  <nodedef name="ND_mix_vector2" node="mix" nodegroup="compositing">
    <input name="fg" type="vector2" value="0.0, 0.0" />
    <input name="bg" type="vector2" value="0.0, 0.0" />
    <input name="mix" type="float" value="0.0" uisoftmin="0.0" uisoftmax="1.0" />
    <output name="out" type="vector2" defaultinput="bg" />
  </nodedef>
  <nodedef name="ND_mix_vector2_vector2" node="mix" nodegroup="compositing">
    <input name="fg" type="vector2" value="0.0, 0.0" />
    <input name="bg" type="vector2" value="0.0, 0.0" />
    <input name="mix" type="vector2" value="0.0, 0.0" uisoftmin="0,0" uisoftmax="1,1" />
    <output name="out" type="vector2" defaultinput="bg" />
  </nodedef>
  <nodedef name="ND_mix_vector3" node="mix" nodegroup="compositing">
    <input name="fg" type="vector3" value="0.0, 0.0, 0.0" />
    <input name="bg" type="vector3" value="0.0, 0.0, 0.0" />
    <input name="mix" type="float" value="0.0" uisoftmin="0.0" uisoftmax="1.0" />
    <output name="out" type="vector3" defaultinput="bg" />
  </nodedef>
  <nodedef name="ND_mix_vector3_vector3" node="mix" nodegroup="compositing">
    <input name="fg" type="vector3" value="0.0, 0.0, 0.0" />
    <input name="bg" type="vector3" value="0.0, 0.0, 0.0" />
    <input name="mix" type="vector3" value="0.0, 0.0, 0.0" uisoftmin="0,0,0" uisoftmax="1,1,1" />
    <output name="out" type="vector3" defaultinput="bg" />
  </nodedef>
  <nodedef name="ND_mix_vector4" node="mix" nodegroup="compositing">
    <input name="fg" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="bg" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="mix" type="float" value="0.0" uisoftmin="0.0" uisoftmax="1.0" />
    <output name="out" type="vector4" defaultinput="bg" />
  </nodedef>
  <nodedef name="ND_mix_vector4_vector4" node="mix" nodegroup="compositing">
    <input name="fg" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="bg" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="mix" type="vector4" value="0.0, 0.0, 0.0, 0.0" uisoftmin="0,0,0,0" uisoftmax="1,1,1,1" />
    <output name="out" type="vector4" defaultinput="bg" />
  </nodedef>
  <nodedef name="ND_mix_surfaceshader" node="mix" nodegroup="compositing">
    <input name="fg" type="surfaceshader" value="" />
    <input name="bg" type="surfaceshader" value="" />
    <input name="mix" type="float" value="0.0" uisoftmin="0.0" uisoftmax="1.0" />
    <output name="out" type="surfaceshader" defaultinput="bg" />
  </nodedef>
  <nodedef name="ND_mix_displacementshader" node="mix" nodegroup="compositing">
    <input name="fg" type="displacementshader" value="" />
    <input name="bg" type="displacementshader" value="" />
    <input name="mix" type="float" value="0.0" uisoftmin="0.0" uisoftmax="1.0" />
    <output name="out" type="displacementshader" defaultinput="bg" />
  </nodedef>
  <nodedef name="ND_mix_volumeshader" node="mix" nodegroup="compositing">
    <input name="fg" type="volumeshader" value="" />
    <input name="bg" type="volumeshader" value="" />
    <input name="mix" type="float" value="0.0" uisoftmin="0.0" uisoftmax="1.0" />
    <output name="out" type="volumeshader" defaultinput="bg" />
  </nodedef>

  <!-- ======================================================================== -->
  <!-- Conditional nodes                                                        -->
  <!-- ======================================================================== -->

  <!--
    Node: <ifgreater>
    Output the value of in1 if value1>value2, or the value of in2 if value1<=value2.
  -->
  <nodedef name="ND_ifgreater_float" node="ifgreater" nodegroup="conditional">
    <input name="value1" type="float" value="1.0" />
    <input name="value2" type="float" value="0.0" />
    <input name="in1" type="float" value="0.0" />
    <input name="in2" type="float" value="0.0" />
    <output name="out" type="float" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_ifgreater_integer" node="ifgreater" nodegroup="conditional">
    <input name="value1" type="float" value="1.0" />
    <input name="value2" type="float" value="0.0" />
    <input name="in1" type="integer" value="0" />
    <input name="in2" type="integer" value="0" />
    <output name="out" type="integer" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_ifgreater_color3" node="ifgreater" nodegroup="conditional">
    <input name="value1" type="float" value="1.0" />
    <input name="value2" type="float" value="0.0" />
    <input name="in1" type="color3" value="0.0, 0.0, 0.0" />
    <input name="in2" type="color3" value="0.0, 0.0, 0.0" />
    <output name="out" type="color3" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_ifgreater_color4" node="ifgreater" nodegroup="conditional">
    <input name="value1" type="float" value="1.0" />
    <input name="value2" type="float" value="0.0" />
    <input name="in1" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="in2" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <output name="out" type="color4" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_ifgreater_vector2" node="ifgreater" nodegroup="conditional">
    <input name="value1" type="float" value="1.0" />
    <input name="value2" type="float" value="0.0" />
    <input name="in1" type="vector2" value="0.0, 0.0" />
    <input name="in2" type="vector2" value="0.0, 0.0" />
    <output name="out" type="vector2" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_ifgreater_vector3" node="ifgreater" nodegroup="conditional">
    <input name="value1" type="float" value="1.0" />
    <input name="value2" type="float" value="0.0" />
    <input name="in1" type="vector3" value="0.0, 0.0, 0.0" />
    <input name="in2" type="vector3" value="0.0, 0.0, 0.0" />
    <output name="out" type="vector3" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_ifgreater_vector4" node="ifgreater" nodegroup="conditional">
    <input name="value1" type="float" value="1.0" />
    <input name="value2" type="float" value="0.0" />
    <input name="in1" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="in2" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <output name="out" type="vector4" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_ifgreater_matrix33" node="ifgreater" nodegroup="conditional">
    <input name="value1" type="float" value="1.0" />
    <input name="value2" type="float" value="0.0" />
    <input name="in1" type="matrix33" value="0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0" />
    <input name="in2" type="matrix33" value="0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0" />
    <output name="out" type="matrix33" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_ifgreater_matrix44" node="ifgreater" nodegroup="conditional">
    <input name="value1" type="float" value="1.0" />
    <input name="value2" type="float" value="0.0" />
    <input name="in1" type="matrix44" value="0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0" />
    <input name="in2" type="matrix44" value="0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0" />
    <output name="out" type="matrix44" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_ifgreater_boolean" node="ifgreater" nodegroup="conditional">
    <input name="value1" type="float" value="1.0" />
    <input name="value2" type="float" value="0.0" />
    <output name="out" type="boolean" default="false" />
  </nodedef>
  <nodedef name="ND_ifgreater_floatI" node="ifgreater" nodegroup="conditional">
    <input name="value1" type="integer" value="1" />
    <input name="value2" type="integer" value="0" />
    <input name="in1" type="float" value="0.0" />
    <input name="in2" type="float" value="0.0" />
    <output name="out" type="float" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_ifgreater_integerI" node="ifgreater" nodegroup="conditional">
    <input name="value1" type="integer" value="1" />
    <input name="value2" type="integer" value="0" />
    <input name="in1" type="integer" value="0" />
    <input name="in2" type="integer" value="0" />
    <output name="out" type="integer" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_ifgreater_color3I" node="ifgreater" nodegroup="conditional">
    <input name="value1" type="integer" value="1" />
    <input name="value2" type="integer" value="0" />
    <input name="in1" type="color3" value="0.0, 0.0, 0.0" />
    <input name="in2" type="color3" value="0.0, 0.0, 0.0" />
    <output name="out" type="color3" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_ifgreater_color4I" node="ifgreater" nodegroup="conditional">
    <input name="value1" type="integer" value="1" />
    <input name="value2" type="integer" value="0" />
    <input name="in1" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="in2" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <output name="out" type="color4" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_ifgreater_vector2I" node="ifgreater" nodegroup="conditional">
    <input name="value1" type="integer" value="1" />
    <input name="value2" type="integer" value="0" />
    <input name="in1" type="vector2" value="0.0, 0.0" />
    <input name="in2" type="vector2" value="0.0, 0.0" />
    <output name="out" type="vector2" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_ifgreater_vector3I" node="ifgreater" nodegroup="conditional">
    <input name="value1" type="integer" value="1" />
    <input name="value2" type="integer" value="0" />
    <input name="in1" type="vector3" value="0.0, 0.0, 0.0" />
    <input name="in2" type="vector3" value="0.0, 0.0, 0.0" />
    <output name="out" type="vector3" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_ifgreater_vector4I" node="ifgreater" nodegroup="conditional">
    <input name="value1" type="integer" value="1" />
    <input name="value2" type="integer" value="0" />
    <input name="in1" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="in2" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <output name="out" type="vector4" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_ifgreater_matrix33I" node="ifgreater" nodegroup="conditional">
    <input name="value1" type="integer" value="1" />
    <input name="value2" type="integer" value="0" />
    <input name="in1" type="matrix33" value="0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0" />
    <input name="in2" type="matrix33" value="0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0" />
    <output name="out" type="matrix33" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_ifgreater_matrix44I" node="ifgreater" nodegroup="conditional">
    <input name="value1" type="integer" value="1" />
    <input name="value2" type="integer" value="0" />
    <input name="in1" type="matrix44" value="0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0" />
    <input name="in2" type="matrix44" value="0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0" />
    <output name="out" type="matrix44" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_ifgreater_booleanI" node="ifgreater" nodegroup="conditional">
    <input name="value1" type="integer" value="1" />
    <input name="value2" type="integer" value="0" />
    <output name="out" type="boolean" default="false" />
  </nodedef>

  <!--
    Node: <ifgreatereq>
    Output the value of in1 if value1>=value2, or the value of in2 if value1<value2.
  -->
  <nodedef name="ND_ifgreatereq_float" node="ifgreatereq" nodegroup="conditional">
    <input name="value1" type="float" value="1.0" />
    <input name="value2" type="float" value="0.0" />
    <input name="in1" type="float" value="0.0" />
    <input name="in2" type="float" value="0.0" />
    <output name="out" type="float" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_ifgreatereq_integer" node="ifgreatereq" nodegroup="conditional">
    <input name="value1" type="float" value="1.0" />
    <input name="value2" type="float" value="0.0" />
    <input name="in1" type="integer" value="0" />
    <input name="in2" type="integer" value="0" />
    <output name="out" type="integer" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_ifgreatereq_color3" node="ifgreatereq" nodegroup="conditional">
    <input name="value1" type="float" value="1.0" />
    <input name="value2" type="float" value="0.0" />
    <input name="in1" type="color3" value="0.0, 0.0, 0.0" />
    <input name="in2" type="color3" value="0.0, 0.0, 0.0" />
    <output name="out" type="color3" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_ifgreatereq_color4" node="ifgreatereq" nodegroup="conditional">
    <input name="value1" type="float" value="1.0" />
    <input name="value2" type="float" value="0.0" />
    <input name="in1" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="in2" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <output name="out" type="color4" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_ifgreatereq_vector2" node="ifgreatereq" nodegroup="conditional">
    <input name="value1" type="float" value="1.0" />
    <input name="value2" type="float" value="0.0" />
    <input name="in1" type="vector2" value="0.0, 0.0" />
    <input name="in2" type="vector2" value="0.0, 0.0" />
    <output name="out" type="vector2" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_ifgreatereq_vector3" node="ifgreatereq" nodegroup="conditional">
    <input name="value1" type="float" value="1.0" />
    <input name="value2" type="float" value="0.0" />
    <input name="in1" type="vector3" value="0.0, 0.0, 0.0" />
    <input name="in2" type="vector3" value="0.0, 0.0, 0.0" />
    <output name="out" type="vector3" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_ifgreatereq_vector4" node="ifgreatereq" nodegroup="conditional">
    <input name="value1" type="float" value="1.0" />
    <input name="value2" type="float" value="0.0" />
    <input name="in1" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="in2" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <output name="out" type="vector4" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_ifgreatereq_matrix33" node="ifgreatereq" nodegroup="conditional">
    <input name="value1" type="float" value="1.0" />
    <input name="value2" type="float" value="0.0" />
    <input name="in1" type="matrix33" value="0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0" />
    <input name="in2" type="matrix33" value="0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0" />
    <output name="out" type="matrix33" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_ifgreatereq_matrix44" node="ifgreatereq" nodegroup="conditional">
    <input name="value1" type="float" value="1.0" />
    <input name="value2" type="float" value="0.0" />
    <input name="in1" type="matrix44" value="0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0" />
    <input name="in2" type="matrix44" value="0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0" />
    <output name="out" type="matrix44" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_ifgreatereq_boolean" node="ifgreatereq" nodegroup="conditional">
    <input name="value1" type="float" value="1.0" />
    <input name="value2" type="float" value="0.0" />
    <output name="out" type="boolean" default="false" />
  </nodedef>
  <nodedef name="ND_ifgreatereq_floatI" node="ifgreatereq" nodegroup="conditional">
    <input name="value1" type="integer" value="1" />
    <input name="value2" type="integer" value="0" />
    <input name="in1" type="float" value="0.0" />
    <input name="in2" type="float" value="0.0" />
    <output name="out" type="float" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_ifgreatereq_integerI" node="ifgreatereq" nodegroup="conditional">
    <input name="value1" type="integer" value="1" />
    <input name="value2" type="integer" value="0" />
    <input name="in1" type="integer" value="0" />
    <input name="in2" type="integer" value="0" />
    <output name="out" type="integer" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_ifgreatereq_color3I" node="ifgreatereq" nodegroup="conditional">
    <input name="value1" type="integer" value="1" />
    <input name="value2" type="integer" value="0" />
    <input name="in1" type="color3" value="0.0, 0.0, 0.0" />
    <input name="in2" type="color3" value="0.0, 0.0, 0.0" />
    <output name="out" type="color3" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_ifgreatereq_color4I" node="ifgreatereq" nodegroup="conditional">
    <input name="value1" type="integer" value="1" />
    <input name="value2" type="integer" value="0" />
    <input name="in1" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="in2" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <output name="out" type="color4" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_ifgreatereq_vector2I" node="ifgreatereq" nodegroup="conditional">
    <input name="value1" type="integer" value="1" />
    <input name="value2" type="integer" value="0" />
    <input name="in1" type="vector2" value="0.0, 0.0" />
    <input name="in2" type="vector2" value="0.0, 0.0" />
    <output name="out" type="vector2" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_ifgreatereq_vector3I" node="ifgreatereq" nodegroup="conditional">
    <input name="value1" type="integer" value="1" />
    <input name="value2" type="integer" value="0" />
    <input name="in1" type="vector3" value="0.0, 0.0, 0.0" />
    <input name="in2" type="vector3" value="0.0, 0.0, 0.0" />
    <output name="out" type="vector3" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_ifgreatereq_vector4I" node="ifgreatereq" nodegroup="conditional">
    <input name="value1" type="integer" value="1" />
    <input name="value2" type="integer" value="0" />
    <input name="in1" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="in2" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <output name="out" type="vector4" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_ifgreatereq_matrix33I" node="ifgreatereq" nodegroup="conditional">
    <input name="value1" type="integer" value="1" />
    <input name="value2" type="integer" value="0" />
    <input name="in1" type="matrix33" value="0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0" />
    <input name="in2" type="matrix33" value="0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0" />
    <output name="out" type="matrix33" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_ifgreatereq_matrix44I" node="ifgreatereq" nodegroup="conditional">
    <input name="value1" type="integer" value="1" />
    <input name="value2" type="integer" value="0" />
    <input name="in1" type="matrix44" value="0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0" />
    <input name="in2" type="matrix44" value="0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0" />
    <output name="out" type="matrix44" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_ifgreatereq_booleanI" node="ifgreatereq" nodegroup="conditional">
    <input name="value1" type="integer" value="1" />
    <input name="value2" type="integer" value="0" />
    <output name="out" type="boolean" default="false" />
  </nodedef>

  <!--
    Node: <ifequal>
    Output the value of in1 if value1==value2, or the value of in2 if value1!=value2.
  -->
  <nodedef name="ND_ifequal_float" node="ifequal" nodegroup="conditional">
    <input name="value1" type="float" value="0.0" />
    <input name="value2" type="float" value="0.0" />
    <input name="in1" type="float" value="0.0" />
    <input name="in2" type="float" value="0.0" />
    <output name="out" type="float" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_ifequal_integer" node="ifequal" nodegroup="conditional">
    <input name="value1" type="float" value="0.0" />
    <input name="value2" type="float" value="0.0" />
    <input name="in1" type="integer" value="0" />
    <input name="in2" type="integer" value="0" />
    <output name="out" type="integer" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_ifequal_color3" node="ifequal" nodegroup="conditional">
    <input name="value1" type="float" value="0.0" />
    <input name="value2" type="float" value="0.0" />
    <input name="in1" type="color3" value="0.0, 0.0, 0.0" />
    <input name="in2" type="color3" value="0.0, 0.0, 0.0" />
    <output name="out" type="color3" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_ifequal_color4" node="ifequal" nodegroup="conditional">
    <input name="value1" type="float" value="0.0" />
    <input name="value2" type="float" value="0.0" />
    <input name="in1" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="in2" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <output name="out" type="color4" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_ifequal_vector2" node="ifequal" nodegroup="conditional">
    <input name="value1" type="float" value="0.0" />
    <input name="value2" type="float" value="0.0" />
    <input name="in1" type="vector2" value="0.0, 0.0" />
    <input name="in2" type="vector2" value="0.0, 0.0" />
    <output name="out" type="vector2" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_ifequal_vector3" node="ifequal" nodegroup="conditional">
    <input name="value1" type="float" value="0.0" />
    <input name="value2" type="float" value="0.0" />
    <input name="in1" type="vector3" value="0.0, 0.0, 0.0" />
    <input name="in2" type="vector3" value="0.0, 0.0, 0.0" />
    <output name="out" type="vector3" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_ifequal_vector4" node="ifequal" nodegroup="conditional">
    <input name="value1" type="float" value="0.0" />
    <input name="value2" type="float" value="0.0" />
    <input name="in1" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="in2" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <output name="out" type="vector4" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_ifequal_matrix33" node="ifequal" nodegroup="conditional">
    <input name="value1" type="float" value="0.0" />
    <input name="value2" type="float" value="0.0" />
    <input name="in1" type="matrix33" value="0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0" />
    <input name="in2" type="matrix33" value="0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0" />
    <output name="out" type="matrix33" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_ifequal_matrix44" node="ifequal" nodegroup="conditional">
    <input name="value1" type="float" value="0.0" />
    <input name="value2" type="float" value="0.0" />
    <input name="in1" type="matrix44" value="0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0" />
    <input name="in2" type="matrix44" value="0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0" />
    <output name="out" type="matrix44" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_ifequal_boolean" node="ifequal" nodegroup="conditional">
    <input name="value1" type="float" value="1.0" />
    <input name="value2" type="float" value="0.0" />
    <output name="out" type="boolean" default="false" />
  </nodedef>
  <nodedef name="ND_ifequal_floatI" node="ifequal" nodegroup="conditional">
    <input name="value1" type="integer" value="0" />
    <input name="value2" type="integer" value="0" />
    <input name="in1" type="float" value="0.0" />
    <input name="in2" type="float" value="0.0" />
    <output name="out" type="float" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_ifequal_integerI" node="ifequal" nodegroup="conditional">
    <input name="value1" type="integer" value="0" />
    <input name="value2" type="integer" value="0" />
    <input name="in1" type="integer" value="0" />
    <input name="in2" type="integer" value="0" />
    <output name="out" type="integer" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_ifequal_color3I" node="ifequal" nodegroup="conditional">
    <input name="value1" type="integer" value="0" />
    <input name="value2" type="integer" value="0" />
    <input name="in1" type="color3" value="0.0, 0.0, 0.0" />
    <input name="in2" type="color3" value="0.0, 0.0, 0.0" />
    <output name="out" type="color3" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_ifequal_color4I" node="ifequal" nodegroup="conditional">
    <input name="value1" type="integer" value="0" />
    <input name="value2" type="integer" value="0" />
    <input name="in1" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="in2" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <output name="out" type="color4" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_ifequal_vector2I" node="ifequal" nodegroup="conditional">
    <input name="value1" type="integer" value="0" />
    <input name="value2" type="integer" value="0" />
    <input name="in1" type="vector2" value="0.0, 0.0" />
    <input name="in2" type="vector2" value="0.0, 0.0" />
    <output name="out" type="vector2" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_ifequal_vector3I" node="ifequal" nodegroup="conditional">
    <input name="value1" type="integer" value="0" />
    <input name="value2" type="integer" value="0" />
    <input name="in1" type="vector3" value="0.0, 0.0, 0.0" />
    <input name="in2" type="vector3" value="0.0, 0.0, 0.0" />
    <output name="out" type="vector3" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_ifequal_vector4I" node="ifequal" nodegroup="conditional">
    <input name="value1" type="integer" value="0" />
    <input name="value2" type="integer" value="0" />
    <input name="in1" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="in2" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <output name="out" type="vector4" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_ifequal_matrix33I" node="ifequal" nodegroup="conditional">
    <input name="value1" type="integer" value="0" />
    <input name="value2" type="integer" value="0" />
    <input name="in1" type="matrix33" value="0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0" />
    <input name="in2" type="matrix33" value="0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0" />
    <output name="out" type="matrix33" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_ifequal_matrix44I" node="ifequal" nodegroup="conditional">
    <input name="value1" type="integer" value="0" />
    <input name="value2" type="integer" value="0" />
    <input name="in1" type="matrix44" value="0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0" />
    <input name="in2" type="matrix44" value="0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0" />
    <output name="out" type="matrix44" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_ifequal_booleanI" node="ifequal" nodegroup="conditional">
    <input name="value1" type="integer" value="1" />
    <input name="value2" type="integer" value="0" />
    <output name="out" type="boolean" default="false" />
  </nodedef>
  <nodedef name="ND_ifequal_floatB" node="ifequal" nodegroup="conditional">
    <input name="value1" type="boolean" value="false" />
    <input name="value2" type="boolean" value="false" />
    <input name="in1" type="float" value="0.0" />
    <input name="in2" type="float" value="0.0" />
    <output name="out" type="float" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_ifequal_integerB" node="ifequal" nodegroup="conditional">
    <input name="value1" type="boolean" value="false" />
    <input name="value2" type="boolean" value="false" />
    <input name="in1" type="integer" value="0" />
    <input name="in2" type="integer" value="0" />
    <output name="out" type="integer" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_ifequal_color3B" node="ifequal" nodegroup="conditional">
    <input name="value1" type="boolean" value="false" />
    <input name="value2" type="boolean" value="false" />
    <input name="in1" type="color3" value="0.0, 0.0, 0.0" />
    <input name="in2" type="color3" value="0.0, 0.0, 0.0" />
    <output name="out" type="color3" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_ifequal_color4B" node="ifequal" nodegroup="conditional">
    <input name="value1" type="boolean" value="false" />
    <input name="value2" type="boolean" value="false" />
    <input name="in1" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="in2" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <output name="out" type="color4" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_ifequal_vector2B" node="ifequal" nodegroup="conditional">
    <input name="value1" type="boolean" value="false" />
    <input name="value2" type="boolean" value="false" />
    <input name="in1" type="vector2" value="0.0, 0.0" />
    <input name="in2" type="vector2" value="0.0, 0.0" />
    <output name="out" type="vector2" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_ifequal_vector3B" node="ifequal" nodegroup="conditional">
    <input name="value1" type="boolean" value="false" />
    <input name="value2" type="boolean" value="false" />
    <input name="in1" type="vector3" value="0.0, 0.0, 0.0" />
    <input name="in2" type="vector3" value="0.0, 0.0, 0.0" />
    <output name="out" type="vector3" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_ifequal_vector4B" node="ifequal" nodegroup="conditional">
    <input name="value1" type="boolean" value="false" />
    <input name="value2" type="boolean" value="false" />
    <input name="in1" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="in2" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <output name="out" type="vector4" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_ifequal_matrix33B" node="ifequal" nodegroup="conditional">
    <input name="value1" type="boolean" value="false" />
    <input name="value2" type="boolean" value="false" />
    <input name="in1" type="matrix33" value="0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0" />
    <input name="in2" type="matrix33" value="0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0" />
    <output name="out" type="matrix33" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_ifequal_matrix44B" node="ifequal" nodegroup="conditional">
    <input name="value1" type="boolean" value="false" />
    <input name="value2" type="boolean" value="false" />
    <input name="in1" type="matrix44" value="0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0" />
    <input name="in2" type="matrix44" value="0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0" />
    <output name="out" type="matrix44" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_ifequal_booleanB" node="ifequal" nodegroup="conditional">
    <input name="value1" type="boolean" value="false" />
    <input name="value2" type="boolean" value="false" />
    <output name="out" type="boolean" default="false" />
  </nodedef>

  <!--
    Node: <switch>
    Pass on the value of one of five input streams, according to the value of a selector parameter.
  -->
  <nodedef name="ND_switch_float" node="switch" nodegroup="conditional">
    <input name="in1" type="float" value="0.0" />
    <input name="in2" type="float" value="0.0" />
    <input name="in3" type="float" value="0.0" />
    <input name="in4" type="float" value="0.0" />
    <input name="in5" type="float" value="0.0" />
    <input name="in6" type="float" value="0.0" />
    <input name="in7" type="float" value="0.0" />
    <input name="in8" type="float" value="0.0" />
    <input name="in9" type="float" value="0.0" />
    <input name="in10" type="float" value="0.0" />
    <input name="which" type="float" value="0.0" />
    <output name="out" type="float" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_switch_color3" node="switch" nodegroup="conditional">
    <input name="in1" type="color3" value="0.0, 0.0, 0.0" />
    <input name="in2" type="color3" value="0.0, 0.0, 0.0" />
    <input name="in3" type="color3" value="0.0, 0.0, 0.0" />
    <input name="in4" type="color3" value="0.0, 0.0, 0.0" />
    <input name="in5" type="color3" value="0.0, 0.0, 0.0" />
    <input name="in6" type="color3" value="0.0, 0.0, 0.0" />
    <input name="in7" type="color3" value="0.0, 0.0, 0.0" />
    <input name="in8" type="color3" value="0.0, 0.0, 0.0" />
    <input name="in9" type="color3" value="0.0, 0.0, 0.0" />
    <input name="in10" type="color3" value="0.0, 0.0, 0.0" />
    <input name="which" type="float" value="0.0" />
    <output name="out" type="color3" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_switch_color4" node="switch" nodegroup="conditional">
    <input name="in1" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="in2" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="in3" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="in4" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="in5" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="in6" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="in7" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="in8" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="in9" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="in10" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="which" type="float" value="0.0" />
    <output name="out" type="color4" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_switch_vector2" node="switch" nodegroup="conditional">
    <input name="in1" type="vector2" value="0.0, 0.0" />
    <input name="in2" type="vector2" value="0.0, 0.0" />
    <input name="in3" type="vector2" value="0.0, 0.0" />
    <input name="in4" type="vector2" value="0.0, 0.0" />
    <input name="in5" type="vector2" value="0.0, 0.0" />
    <input name="in6" type="vector2" value="0.0, 0.0" />
    <input name="in7" type="vector2" value="0.0, 0.0" />
    <input name="in8" type="vector2" value="0.0, 0.0" />
    <input name="in9" type="vector2" value="0.0, 0.0" />
    <input name="in10" type="vector2" value="0.0, 0.0" />
    <input name="which" type="float" value="0.0" />
    <output name="out" type="vector2" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_switch_vector3" node="switch" nodegroup="conditional">
    <input name="in1" type="vector3" value="0.0, 0.0, 0.0" />
    <input name="in2" type="vector3" value="0.0, 0.0, 0.0" />
    <input name="in3" type="vector3" value="0.0, 0.0, 0.0" />
    <input name="in4" type="vector3" value="0.0, 0.0, 0.0" />
    <input name="in5" type="vector3" value="0.0, 0.0, 0.0" />
    <input name="in6" type="vector3" value="0.0, 0.0, 0.0" />
    <input name="in7" type="vector3" value="0.0, 0.0, 0.0" />
    <input name="in8" type="vector3" value="0.0, 0.0, 0.0" />
    <input name="in9" type="vector3" value="0.0, 0.0, 0.0" />
    <input name="in10" type="vector3" value="0.0, 0.0, 0.0" />
    <input name="which" type="float" value="0.0" />
    <output name="out" type="vector3" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_switch_vector4" node="switch" nodegroup="conditional">
    <input name="in1" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="in2" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="in3" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="in4" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="in5" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="in6" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="in7" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="in8" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="in9" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="in10" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="which" type="float" value="0.0" />
    <output name="out" type="vector4" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_switch_matrix33" node="switch" nodegroup="conditional">
    <input name="in1" type="matrix33" value="0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0" />
    <input name="in2" type="matrix33" value="0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0" />
    <input name="in3" type="matrix33" value="0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0" />
    <input name="in4" type="matrix33" value="0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0" />
    <input name="in5" type="matrix33" value="0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0" />
    <input name="in6" type="matrix33" value="0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0" />
    <input name="in7" type="matrix33" value="0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0" />
    <input name="in8" type="matrix33" value="0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0" />
    <input name="in9" type="matrix33" value="0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0" />
    <input name="in10" type="matrix33" value="0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0" />
    <input name="which" type="float" value="0.0" />
    <output name="out" type="matrix33" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_switch_matrix44" node="switch" nodegroup="conditional">
    <input name="in1" type="matrix44" value="0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0" />
    <input name="in2" type="matrix44" value="0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0" />
    <input name="in3" type="matrix44" value="0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0" />
    <input name="in4" type="matrix44" value="0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0" />
    <input name="in5" type="matrix44" value="0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0" />
    <input name="in6" type="matrix44" value="0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0" />
    <input name="in7" type="matrix44" value="0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0" />
    <input name="in8" type="matrix44" value="0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0" />
    <input name="in9" type="matrix44" value="0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0" />
    <input name="in10" type="matrix44" value="0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0" />
    <input name="which" type="float" value="0.0" />
    <output name="out" type="matrix44" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_switch_floatI" node="switch" nodegroup="conditional">
    <input name="in1" type="float" value="0.0" />
    <input name="in2" type="float" value="0.0" />
    <input name="in3" type="float" value="0.0" />
    <input name="in4" type="float" value="0.0" />
    <input name="in5" type="float" value="0.0" />
    <input name="in6" type="float" value="0.0" />
    <input name="in7" type="float" value="0.0" />
    <input name="in8" type="float" value="0.0" />
    <input name="in9" type="float" value="0.0" />
    <input name="in10" type="float" value="0.0" />
    <input name="which" type="integer" value="0" />
    <output name="out" type="float" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_switch_color3I" node="switch" nodegroup="conditional">
    <input name="in1" type="color3" value="0.0, 0.0, 0.0" />
    <input name="in2" type="color3" value="0.0, 0.0, 0.0" />
    <input name="in3" type="color3" value="0.0, 0.0, 0.0" />
    <input name="in4" type="color3" value="0.0, 0.0, 0.0" />
    <input name="in5" type="color3" value="0.0, 0.0, 0.0" />
    <input name="in6" type="color3" value="0.0, 0.0, 0.0" />
    <input name="in7" type="color3" value="0.0, 0.0, 0.0" />
    <input name="in8" type="color3" value="0.0, 0.0, 0.0" />
    <input name="in9" type="color3" value="0.0, 0.0, 0.0" />
    <input name="in10" type="color3" value="0.0, 0.0, 0.0" />
    <input name="which" type="integer" value="0" />
    <output name="out" type="color3" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_switch_color4I" node="switch" nodegroup="conditional">
    <input name="in1" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="in2" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="in3" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="in4" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="in5" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="in6" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="in7" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="in8" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="in9" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="in10" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="which" type="integer" value="0" />
    <output name="out" type="color4" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_switch_vector2I" node="switch" nodegroup="conditional">
    <input name="in1" type="vector2" value="0.0, 0.0" />
    <input name="in2" type="vector2" value="0.0, 0.0" />
    <input name="in3" type="vector2" value="0.0, 0.0" />
    <input name="in4" type="vector2" value="0.0, 0.0" />
    <input name="in5" type="vector2" value="0.0, 0.0" />
    <input name="in6" type="vector2" value="0.0, 0.0" />
    <input name="in7" type="vector2" value="0.0, 0.0" />
    <input name="in8" type="vector2" value="0.0, 0.0" />
    <input name="in9" type="vector2" value="0.0, 0.0" />
    <input name="in10" type="vector2" value="0.0, 0.0" />
    <input name="which" type="integer" value="0" />
    <output name="out" type="vector2" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_switch_vector3I" node="switch" nodegroup="conditional">
    <input name="in1" type="vector3" value="0.0, 0.0, 0.0" />
    <input name="in2" type="vector3" value="0.0, 0.0, 0.0" />
    <input name="in3" type="vector3" value="0.0, 0.0, 0.0" />
    <input name="in4" type="vector3" value="0.0, 0.0, 0.0" />
    <input name="in5" type="vector3" value="0.0, 0.0, 0.0" />
    <input name="in6" type="vector3" value="0.0, 0.0, 0.0" />
    <input name="in7" type="vector3" value="0.0, 0.0, 0.0" />
    <input name="in8" type="vector3" value="0.0, 0.0, 0.0" />
    <input name="in9" type="vector3" value="0.0, 0.0, 0.0" />
    <input name="in10" type="vector3" value="0.0, 0.0, 0.0" />
    <input name="which" type="integer" value="0" />
    <output name="out" type="vector3" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_switch_vector4I" node="switch" nodegroup="conditional">
    <input name="in1" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="in2" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="in3" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="in4" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="in5" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="in6" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="in7" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="in8" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="in9" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="in10" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="which" type="integer" value="0" />
    <output name="out" type="vector4" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_switch_matrix33I" node="switch" nodegroup="conditional">
    <input name="in1" type="matrix33" value="0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0" />
    <input name="in2" type="matrix33" value="0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0" />
    <input name="in3" type="matrix33" value="0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0" />
    <input name="in4" type="matrix33" value="0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0" />
    <input name="in5" type="matrix33" value="0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0" />
    <input name="in6" type="matrix33" value="0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0" />
    <input name="in7" type="matrix33" value="0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0" />
    <input name="in8" type="matrix33" value="0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0" />
    <input name="in9" type="matrix33" value="0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0" />
    <input name="in10" type="matrix33" value="0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0" />
    <input name="which" type="integer" value="0" />
    <output name="out" type="matrix33" defaultinput="in1" />
  </nodedef>
  <nodedef name="ND_switch_matrix44I" node="switch" nodegroup="conditional">
    <input name="in1" type="matrix44" value="0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0" />
    <input name="in2" type="matrix44" value="0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0" />
    <input name="in3" type="matrix44" value="0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0" />
    <input name="in4" type="matrix44" value="0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0" />
    <input name="in5" type="matrix44" value="0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0" />
    <input name="in6" type="matrix44" value="0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0" />
    <input name="in7" type="matrix44" value="0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0" />
    <input name="in8" type="matrix44" value="0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0" />
    <input name="in9" type="matrix44" value="0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0" />
    <input name="in10" type="matrix44" value="0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0" />
    <input name="which" type="integer" value="0" />
    <output name="out" type="matrix44" defaultinput="in1" />
  </nodedef>

  <!-- ======================================================================== -->
  <!-- Channel nodes                                                            -->
  <!-- ======================================================================== -->

  <!--
    Node: <convert>
    Convert a stream from one type to another; only certain unambiguous conversion
    types are supported.
  -->
  <nodedef name="ND_convert_float_color3" node="convert" nodegroup="channel">
    <input name="in" type="float" value="0.0" />
    <output name="out" type="color3" default="0.0, 0.0, 0.0" />
  </nodedef>
  <nodedef name="ND_convert_float_color4" node="convert" nodegroup="channel">
    <input name="in" type="float" value="0.0" />
    <output name="out" type="color4" default="0.0, 0.0, 0.0, 0.0" />
  </nodedef>
  <nodedef name="ND_convert_float_vector2" node="convert" nodegroup="channel">
    <input name="in" type="float" value="0.0" />
    <output name="out" type="vector2" default="0.0, 0.0" />
  </nodedef>
  <nodedef name="ND_convert_float_vector3" node="convert" nodegroup="channel">
    <input name="in" type="float" value="0.0" />
    <output name="out" type="vector3" default="0.0, 0.0, 0.0" />
  </nodedef>
  <nodedef name="ND_convert_float_vector4" node="convert" nodegroup="channel">
    <input name="in" type="float" value="0.0" />
    <output name="out" type="vector4" default="0.0, 0.0, 0.0, 0.0" />
  </nodedef>

  <nodedef name="ND_convert_color3_color4" node="convert" nodegroup="channel">
    <input name="in" type="color3" value="0.0, 0.0, 0.0" />
    <output name="out" type="color4" default="0.0, 0.0, 0.0, 0.0" />
  </nodedef>
  <nodedef name="ND_convert_color3_vector2" node="convert" nodegroup="channel">
    <input name="in" type="color3" value="0.0, 0.0, 0.0" />
    <output name="out" type="vector2" default="0.0, 0.0" />
  </nodedef>
  <nodedef name="ND_convert_color3_vector3" node="convert" nodegroup="channel">
    <input name="in" type="color3" value="0.0, 0.0, 0.0" />
    <output name="out" type="vector3" default="0.0, 0.0, 0.0" />
  </nodedef>
  <nodedef name="ND_convert_color3_vector4" node="convert" nodegroup="channel">
    <input name="in" type="color3" value="0.0, 0.0, 0.0" />
    <output name="out" type="vector4" default="0.0, 0.0, 0.0, 0.0" />
  </nodedef>

  <nodedef name="ND_convert_color4_color3" node="convert" nodegroup="channel">
    <input name="in" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <output name="out" type="color3" default="0.0, 0.0, 0.0" />
  </nodedef>
  <nodedef name="ND_convert_color4_vector2" node="convert" nodegroup="channel">
    <input name="in" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <output name="out" type="vector2" default="0.0, 0.0" />
  </nodedef>
  <nodedef name="ND_convert_color4_vector3" node="convert" nodegroup="channel">
    <input name="in" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <output name="out" type="vector3" default="0.0, 0.0, 0.0" />
  </nodedef>
  <nodedef name="ND_convert_color4_vector4" node="convert" nodegroup="channel">
    <input name="in" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <output name="out" type="vector4" default="0.0, 0.0, 0.0, 0.0" />
  </nodedef>

  <nodedef name="ND_convert_vector2_color3" node="convert" nodegroup="channel">
    <input name="in" type="vector2" value="0.0, 0.0" />
    <output name="out" type="color3" default="0.0, 0.0, 0.0" />
  </nodedef>
  <nodedef name="ND_convert_vector2_color4" node="convert" nodegroup="channel">
    <input name="in" type="vector2" value="0.0, 0.0" />
    <output name="out" type="color4" default="0.0, 0.0, 0.0, 0.0" />
  </nodedef>
  <nodedef name="ND_convert_vector2_vector3" node="convert" nodegroup="channel">
    <input name="in" type="vector2" value="0.0, 0.0" />
    <output name="out" type="vector3" default="0.0, 0.0, 0.0" />
  </nodedef>
  <nodedef name="ND_convert_vector2_vector4" node="convert" nodegroup="channel">
    <input name="in" type="vector2" value="0.0, 0.0" />
    <output name="out" type="vector4" default="0.0, 0.0, 0.0, 0.0" />
  </nodedef>

  <nodedef name="ND_convert_vector3_color3" node="convert" nodegroup="channel">
    <input name="in" type="vector3" value="0.0, 0.0, 0.0" />
    <output name="out" type="color3" default="0.0, 0.0, 0.0" />
  </nodedef>
  <nodedef name="ND_convert_vector3_color4" node="convert" nodegroup="channel">
    <input name="in" type="vector3" value="0.0, 0.0, 0.0" />
    <output name="out" type="color4" default="0.0, 0.0, 0.0, 0.0" />
  </nodedef>
  <nodedef name="ND_convert_vector3_vector2" node="convert" nodegroup="channel">
    <input name="in" type="vector3" value="0.0, 0.0, 0.0" />
    <output name="out" type="vector2" default="0.0, 0.0" />
  </nodedef>
  <nodedef name="ND_convert_vector3_vector4" node="convert" nodegroup="channel">
    <input name="in" type="vector3" value="0.0, 0.0, 0.0" />
    <output name="out" type="vector4" default="0.0, 0.0, 0.0, 0.0" />
  </nodedef>

  <nodedef name="ND_convert_vector4_color3" node="convert" nodegroup="channel">
    <input name="in" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <output name="out" type="color3" default="0.0, 0.0, 0.0" />
  </nodedef>
  <nodedef name="ND_convert_vector4_color4" node="convert" nodegroup="channel">
    <input name="in" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <output name="out" type="color4" default="0.0, 0.0, 0.0, 0.0" />
  </nodedef>
  <nodedef name="ND_convert_vector4_vector2" node="convert" nodegroup="channel">
    <input name="in" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <output name="out" type="vector2" default="0.0, 0.0" />
  </nodedef>
  <nodedef name="ND_convert_vector4_vector3" node="convert" nodegroup="channel">
    <input name="in" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <output name="out" type="vector3" default="0.0, 0.0, 0.0" />
  </nodedef>

  <nodedef name="ND_convert_boolean_float" node="convert" nodegroup="channel">
    <input name="in" type="boolean" value="false" />
    <output name="out" type="float" default="0.0" />
  </nodedef>
  <nodedef name="ND_convert_boolean_color3" node="convert" nodegroup="channel">
    <input name="in" type="boolean" value="false" />
    <output name="out" type="color3" default="0.0, 0.0, 0.0" />
  </nodedef>
  <nodedef name="ND_convert_boolean_color4" node="convert" nodegroup="channel">
    <input name="in" type="boolean" value="false" />
    <output name="out" type="color4" default="0.0, 0.0, 0.0, 0.0" />
  </nodedef>
  <nodedef name="ND_convert_boolean_vector2" node="convert" nodegroup="channel">
    <input name="in" type="boolean" value="false" />
    <output name="out" type="vector2" default="0.0, 0.0" />
  </nodedef>
  <nodedef name="ND_convert_boolean_vector3" node="convert" nodegroup="channel">
    <input name="in" type="boolean" value="false" />
    <output name="out" type="vector3" default="0.0, 0.0, 0.0" />
  </nodedef>
  <nodedef name="ND_convert_boolean_vector4" node="convert" nodegroup="channel">
    <input name="in" type="boolean" value="false" />
    <output name="out" type="vector4" default="0.0, 0.0, 0.0, 0.0" />
  </nodedef>
  <nodedef name="ND_convert_boolean_integer" node="convert" nodegroup="channel">
    <input name="in" type="boolean" value="false" />
    <output name="out" type="integer" default="0" />
  </nodedef>

  <nodedef name="ND_convert_integer_float" node="convert" nodegroup="channel">
    <input name="in" type="integer" value="0" />
    <output name="out" type="float" default="0.0" />
  </nodedef>
  <nodedef name="ND_convert_integer_color3" node="convert" nodegroup="channel">
    <input name="in" type="integer" value="0" />
    <output name="out" type="color3" default="0.0, 0.0, 0.0" />
  </nodedef>
  <nodedef name="ND_convert_integer_color4" node="convert" nodegroup="channel">
    <input name="in" type="integer" value="0" />
    <output name="out" type="color4" default="0.0, 0.0, 0.0, 0.0" />
  </nodedef>
  <nodedef name="ND_convert_integer_vector2" node="convert" nodegroup="channel">
    <input name="in" type="integer" value="0" />
    <output name="out" type="vector2" default="0.0, 0.0" />
  </nodedef>
  <nodedef name="ND_convert_integer_vector3" node="convert" nodegroup="channel">
    <input name="in" type="integer" value="0" />
    <output name="out" type="vector3" default="0.0, 0.0, 0.0" />
  </nodedef>
  <nodedef name="ND_convert_integer_vector4" node="convert" nodegroup="channel">
    <input name="in" type="integer" value="0" />
    <output name="out" type="vector4" default="0.0, 0.0, 0.0, 0.0" />
  </nodedef>
  <nodedef name="ND_convert_integer_boolean" node="convert" nodegroup="channel">
    <input name="in" type="integer" value="0" />
    <output name="out" type="boolean" default="false" />
  </nodedef>

  <nodedef name="ND_convert_color3_surfaceshader" node="convert" version="1.0" isdefaultversion="true" nodegroup="shader" doc="Convert color3 to shader">
    <input name="in" type="color3" value="0, 0, 0" />
    <output name="out" type="surfaceshader" />
  </nodedef>
  <nodedef name="ND_convert_color4_surfaceshader" node="convert" version="1.0" isdefaultversion="true" nodegroup="shader" doc="Convert color4  to shader">
    <input name="in" type="color4" value="0, 0, 0, 0" />
    <output name="out" type="surfaceshader" />
  </nodedef>
  <nodedef name="ND_convert_float_surfaceshader" node="convert" version="1.0" isdefaultversion="true" nodegroup="shader" doc="Convert float to shader">
    <input name="in" type="float" value="0" />
    <output name="out" type="surfaceshader" />
  </nodedef>
  <nodedef name="ND_convert_vector2_surfaceshader" node="convert" version="1.0" isdefaultversion="true" nodegroup="shader" doc="Convert vector2 to shader">
    <input name="in" type="vector2" value="0, 0" />
    <output name="out" type="surfaceshader" />
  </nodedef>
  <nodedef name="ND_convert_vector3_surfaceshader" node="convert" version="1.0" isdefaultversion="true" nodegroup="shader" doc="Convert vector2 to shader">
    <input name="in" type="vector3" value="0, 0, 0" />
    <output name="out" type="surfaceshader" />
  </nodedef>
  <nodedef name="ND_convert_vector4_surfaceshader" node="convert" version="1.0" isdefaultversion="true" nodegroup="shader" doc="Convert vector4 to shader">
    <input name="in" type="vector4" value="0, 0, 0, 0" />
    <output name="out" type="surfaceshader" />
  </nodedef>
  <nodedef name="ND_convert_integer_surfaceshader" node="convert" version="1.0" isdefaultversion="true" nodegroup="shader" doc="Convert integer to shader">
    <input name="in" type="integer" value="0" />
    <output name="out" type="surfaceshader" />
  </nodedef>
  <nodedef name="ND_convert_boolean_surfaceshader" node="convert" version="1.0" isdefaultversion="true" nodegroup="shader" doc="Convert boolean to shader">
    <input name="in" type="boolean" value="false" />
    <output name="out" type="surfaceshader" />
  </nodedef>

  <!--
    Node: <combine2>
    Combine the channels from two streams into the same number of channels of a
    single output stream of a specified compatible type.
  -->
  <nodedef name="ND_combine2_vector2" node="combine2" nodegroup="channel">
    <input name="in1" type="float" value="0.0" />
    <input name="in2" type="float" value="0.0" />
    <output name="out" type="vector2" default="0.0, 0.0" />
  </nodedef>
  <nodedef name="ND_combine2_color4CF" node="combine2" nodegroup="channel">
    <input name="in1" type="color3" value="0.0, 0.0, 0.0" />
    <input name="in2" type="float" value="0.0" />
    <output name="out" type="color4" default="0.0, 0.0, 0.0, 0.0" />
  </nodedef>
  <nodedef name="ND_combine2_vector4VF" node="combine2" nodegroup="channel">
    <input name="in1" type="vector3" value="0.0, 0.0, 0.0" />
    <input name="in2" type="float" value="0.0" />
    <output name="out" type="vector4" default="0.0, 0.0, 0.0, 0.0" />
  </nodedef>
  <nodedef name="ND_combine2_vector4VV" node="combine2" nodegroup="channel">
    <input name="in1" type="vector2" value="0.0, 0.0" />
    <input name="in2" type="vector2" value="0.0, 0.0" />
    <output name="out" type="vector4" default="0.0, 0.0, 0.0, 0.0" />
  </nodedef>

  <!--
    Node: <combine3>
    Combine the channels from three streams into the same number of channels of a
    single output stream of a specified compatible type.
  -->
  <nodedef name="ND_combine3_color3" node="combine3" nodegroup="channel">
    <input name="in1" type="float" value="0.0" />
    <input name="in2" type="float" value="0.0" />
    <input name="in3" type="float" value="0.0" />
    <output name="out" type="color3" default="0.0, 0.0, 0.0" />
  </nodedef>
  <nodedef name="ND_combine3_vector3" node="combine3" nodegroup="channel">
    <input name="in1" type="float" value="0.0" />
    <input name="in2" type="float" value="0.0" />
    <input name="in3" type="float" value="0.0" />
    <output name="out" type="vector3" default="0.0, 0.0, 0.0" />
  </nodedef>

  <!--
    Node: <combine4>
    Combine the channels from four streams into the same number of channels of a
    single output stream of a specified compatible type.
  -->
  <nodedef name="ND_combine4_color4" node="combine4" nodegroup="channel">
    <input name="in1" type="float" value="0.0" />
    <input name="in2" type="float" value="0.0" />
    <input name="in3" type="float" value="0.0" />
    <input name="in4" type="float" value="0.0" />
    <output name="out" type="color4" default="0.0, 0.0, 0.0, 0.0" />
  </nodedef>
  <nodedef name="ND_combine4_vector4" node="combine4" nodegroup="channel">
    <input name="in1" type="float" value="0.0" />
    <input name="in2" type="float" value="0.0" />
    <input name="in3" type="float" value="0.0" />
    <input name="in4" type="float" value="0.0" />
    <output name="out" type="vector4" default="0.0, 0.0, 0.0, 0.0" />
  </nodedef>

  <!--
    Node <creatematrix>
    Combine the the three vectors3 from stream into a matrix 33.
  -->
  <nodedef name="ND_creatematrix_vector3_matrix33" node="creatematrix" nodegroup="math">
    <input name="in1" type="vector3" value="1.0, 0.0, 0.0" />
    <input name="in2" type="vector3" value="0.0, 1.0, 0.0" />
    <input name="in3" type="vector3" value="0.0, 0.0, 1.0" />
    <output name="out" type="matrix33" default="1.0, 0.0, 0.0,  0.0, 1.0, 0.0,  0.0, 0.0, 1.0" />
  </nodedef>

  <nodedef name="ND_creatematrix_vector3_matrix44" node="creatematrix" nodegroup="math">
    <input name="in1" type="vector3" value="1.0, 0.0, 0.0" />
    <input name="in2" type="vector3" value="0.0, 1.0, 0.0" />
    <input name="in3" type="vector3" value="0.0, 0.0, 1.0" />
    <input name="in4" type="vector3" value="0.0, 0.0, 0.0" />
    <output name="out" type="matrix44" default="1.0, 0.0, 0.0, 0.0,  0.0, 1.0, 0.0, 0.0,  0.0, 0.0, 1.0, 0.0,  0.0, 0.0, 0.0, 1.0" />
  </nodedef>

  <nodedef name="ND_creatematrix_vector4_matrix44" node="creatematrix" nodegroup="math">
    <input name="in1" type="vector4" value="1.0, 0.0, 0.0, 0.0" />
    <input name="in2" type="vector4" value="0.0, 1.0, 0.0, 0.0" />
    <input name="in3" type="vector4" value="0.0, 0.0, 1.0, 0.0" />
    <input name="in4" type="vector4" value="0.0, 0.0, 0.0, 1.0" />
    <output name="out" type="matrix44" default="1.0, 0.0, 0.0, 0.0,  0.0, 1.0, 0.0, 0.0,  0.0, 0.0, 1.0, 0.0,  0.0, 0.0, 0.0, 1.0" />
  </nodedef>

  <!--
    Node: <extract>
    Extract a single channel from a colorN or vectorN stream, outputting a float.
  -->
  <nodedef name="ND_extract_color3" node="extract" nodegroup="channel">
    <input name="in" type="color3" value="0.0, 0.0, 0.0" />
    <input name="index" type="integer" value="0" uimin="0" uimax="2" uniform="true" />
    <output name="out" type="float" default="0.0" />
  </nodedef>
  <nodedef name="ND_extract_color4" node="extract" nodegroup="channel">
    <input name="in" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="index" type="integer" value="0" uimin="0" uimax="3" uniform="true" />
    <output name="out" type="float" default="0.0" />
  </nodedef>
  <nodedef name="ND_extract_vector2" node="extract" nodegroup="channel">
    <input name="in" type="vector2" value="0.0, 0.0" />
    <input name="index" type="integer" value="0" uimin="0" uimax="1" uniform="true" />
    <output name="out" type="float" default="0.0" />
  </nodedef>
  <nodedef name="ND_extract_vector3" node="extract" nodegroup="channel">
    <input name="in" type="vector3" value="0.0, 0.0, 0.0" />
    <input name="index" type="integer" value="0" uimin="0" uimax="2" uniform="true" />
    <output name="out" type="float" default="0.0" />
  </nodedef>
  <nodedef name="ND_extract_vector4" node="extract" nodegroup="channel">
    <input name="in" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="index" type="integer" value="0" uimin="0" uimax="3" uniform="true" />
    <output name="out" type="float" default="0.0" />
  </nodedef>

  <!--
    Node: <separate2>, <separate3>, <separate4> Supplemental Nodes
    Output each of the channels of a color/vector stream as a separate float output.
  -->
  <nodedef name="ND_separate2_vector2" node="separate2" nodegroup="channel">
    <input name="in" type="vector2" value="0.0, 0.0" />
    <output name="outx" type="float" default="0.0" />
    <output name="outy" type="float" default="0.0" />
  </nodedef>
  <nodedef name="ND_separate3_color3" node="separate3" nodegroup="channel">
    <input name="in" type="color3" value="0.0, 0.0, 0.0" />
    <output name="outr" type="float" default="0.0" />
    <output name="outg" type="float" default="0.0" />
    <output name="outb" type="float" default="0.0" />
  </nodedef>
  <nodedef name="ND_separate3_vector3" node="separate3" nodegroup="channel">
    <input name="in" type="vector3" value="0.0, 0.0, 0.0" />
    <output name="outx" type="float" default="0.0" />
    <output name="outy" type="float" default="0.0" />
    <output name="outz" type="float" default="0.0" />
  </nodedef>
  <nodedef name="ND_separate4_color4" node="separate4" nodegroup="channel">
    <input name="in" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <output name="outr" type="float" default="0.0" />
    <output name="outg" type="float" default="0.0" />
    <output name="outb" type="float" default="0.0" />
    <output name="outa" type="float" default="0.0" />
  </nodedef>
  <nodedef name="ND_separate4_vector4" node="separate4" nodegroup="channel">
    <input name="in" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <output name="outx" type="float" default="0.0" />
    <output name="outy" type="float" default="0.0" />
    <output name="outz" type="float" default="0.0" />
    <output name="outw" type="float" default="0.0" />
  </nodedef>

  <!-- ======================================================================== -->
  <!-- Convolution nodes                                                        -->
  <!-- ======================================================================== -->

  <!--
    Node: <blur>
    A gaussian-falloff blur.
  -->
  <nodedef name="ND_blur_float" node="blur" nodegroup="convolution2d">
    <input name="in" type="float" value="0.0" />
    <input name="size" type="float" value="0.0" />
    <input name="filtertype" type="string" value="box" enum="box,gaussian" uniform="true" />
    <output name="out" type="float" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_blur_color3" node="blur" nodegroup="convolution2d">
    <input name="in" type="color3" value="0.0, 0.0, 0.0" />
    <input name="size" type="float" value="0.0" />
    <input name="filtertype" type="string" value="box" enum="box,gaussian" uniform="true" />
    <output name="out" type="color3" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_blur_color4" node="blur" nodegroup="convolution2d">
    <input name="in" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="size" type="float" value="0.0" />
    <input name="filtertype" type="string" value="box" enum="box,gaussian" uniform="true" />
    <output name="out" type="color4" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_blur_vector2" node="blur" nodegroup="convolution2d">
    <input name="in" type="vector2" value="0.0, 0.0" />
    <input name="size" type="float" value="0.0" />
    <input name="filtertype" type="string" value="box" enum="box,gaussian" uniform="true" />
    <output name="out" type="vector2" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_blur_vector3" node="blur" nodegroup="convolution2d">
    <input name="in" type="vector3" value="0.0, 0.0, 0.0" />
    <input name="size" type="float" value="0.0" />
    <input name="filtertype" type="string" value="box" enum="box,gaussian" uniform="true" />
    <output name="out" type="vector3" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_blur_vector4" node="blur" nodegroup="convolution2d">
    <input name="in" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="size" type="float" value="0.0" />
    <input name="filtertype" type="string" value="box" enum="box,gaussian" uniform="true" />
    <output name="out" type="vector4" defaultinput="in" />
  </nodedef>

  <!--
    Node: <heighttonormal>
    Convert a scalar height map to a normal map of type vector3.
  -->
  <nodedef name="ND_heighttonormal_vector3" node="heighttonormal" nodegroup="convolution2d">
    <input name="in" type="float" value="0.0" />
    <input name="scale" type="float" value="1.0" />
    <output name="out" type="vector3" default="0.5, 0.5, 1.0" />
  </nodedef>

  <!-- ======================================================================== -->
  <!-- Logical operator nodes                                                   -->
  <!-- ======================================================================== -->

  <!--
    Node: <and>
    Logical And operation for two boolean values.
  -->
  <nodedef name="ND_logical_and" node="and" nodegroup="conditional">
    <input name="in1" type="boolean" value="false" />
    <input name="in2" type="boolean" value="false" />
    <output name="out" type="boolean" defaultinput="in1" />
  </nodedef>

  <!--
    Node: <or>
    Logical Inclusive Or operation for two boolean values.
  -->
  <nodedef name="ND_logical_or" node="or" nodegroup="conditional">
    <input name="in1" type="boolean" value="false" />
    <input name="in2" type="boolean" value="false" />
    <output name="out" type="boolean" defaultinput="in1" />
  </nodedef>

  <!--
    Node: <xor>
    Logical Exclusive Or operation for two boolean values.
  -->
  <nodedef name="ND_logical_xor" node="xor" nodegroup="conditional">
    <input name="in1" type="boolean" value="false" />
    <input name="in2" type="boolean" value="false" />
    <output name="out" type="boolean" defaultinput="in1" />
  </nodedef>

  <!--
    Node: <not>
    Returns logical Not of input.
  -->
  <nodedef name="ND_logical_not" node="not" nodegroup="conditional">
    <input name="in" type="boolean" value="false" />
    <output name="out" type="boolean" default="true"/>
  </nodedef>

  <!-- ======================================================================== -->
  <!-- Organization nodes                                                       -->
  <!-- ======================================================================== -->

  <!--
    Node: <dot>
    No-op; passes its input to the output unchanged.
  -->
  <nodedef name="ND_dot_float" node="dot" nodegroup="organization">
    <input name="in" type="float" value="0.0" />
    <input name="note" type="string" value="" uniform="true" />
    <output name="out" type="float" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_dot_color3" node="dot" nodegroup="organization">
    <input name="in" type="color3" value="0.0, 0.0, 0.0" />
    <input name="note" type="string" value="" uniform="true" />
    <output name="out" type="color3" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_dot_color4" node="dot" nodegroup="organization">
    <input name="in" type="color4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="note" type="string" value="" uniform="true" />
    <output name="out" type="color4" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_dot_vector2" node="dot" nodegroup="organization">
    <input name="in" type="vector2" value="0.0, 0.0" />
    <input name="note" type="string" value="" uniform="true" />
    <output name="out" type="vector2" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_dot_vector3" node="dot" nodegroup="organization">
    <input name="in" type="vector3" value="0.0, 0.0, 0.0" />
    <input name="note" type="string" value="" uniform="true" />
    <output name="out" type="vector3" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_dot_vector4" node="dot" nodegroup="organization">
    <input name="in" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
    <input name="note" type="string" value="" uniform="true" />
    <output name="out" type="vector4" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_dot_boolean" node="dot" nodegroup="organization">
    <input name="in" type="boolean" value="false" />
    <input name="note" type="string" value="" uniform="true" />
    <output name="out" type="boolean" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_dot_integer" node="dot" nodegroup="organization">
    <input name="in" type="integer" value="0" />
    <input name="note" type="string" value="" uniform="true" />
    <output name="out" type="integer" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_dot_matrix33" node="dot" nodegroup="organization">
    <input name="in" type="matrix33" value="1.0,0.0,0.0, 0.0,1.0,0.0, 0.0,0.0,1.0" />
    <input name="note" type="string" value="" uniform="true" />
    <output name="out" type="matrix33" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_dot_matrix44" node="dot" nodegroup="organization">
    <input name="in" type="matrix44" value="1.0,0.0,0.0,0.0, 0.0,1.0,0.0,0.0, 0.0,0.0,1.0,0.0, 0.0,0.0,0.0,1.0" />
    <input name="note" type="string" value="" uniform="true" />
    <output name="out" type="matrix44" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_dot_string" node="dot" nodegroup="organization">
    <input name="in" type="string" value="" />
    <input name="note" type="string" value="" uniform="true" />
    <output name="out" type="string" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_dot_filename" node="dot" nodegroup="organization">
    <input name="in" type="filename" value="" />
    <input name="note" type="string" value="" uniform="true" />
    <output name="out" type="filename" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_dot_surfaceshader" node="dot" nodegroup="organization">
    <input name="in" type="surfaceshader" value="" />
    <input name="note" type="string" value="" uniform="true" />
    <output name="out" type="surfaceshader" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_dot_displacementshader" node="dot" nodegroup="organization">
    <input name="in" type="displacementshader" value="" />
    <input name="note" type="string" value="" uniform="true" />
    <output name="out" type="displacementshader" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_dot_volumeshader" node="dot" nodegroup="organization">
    <input name="in" type="volumeshader" value="" />
    <input name="note" type="string" value="" uniform="true" />
    <output name="out" type="volumeshader" defaultinput="in" />
  </nodedef>
  <nodedef name="ND_dot_lightshader" node="dot" nodegroup="organization">
    <input name="in" type="lightshader" value="" />
    <input name="note" type="string" value="" uniform="true" />
    <output name="out" type="lightshader" defaultinput="in" />
  </nodedef>

</materialx>
