<?xml version="1.0"?>
<materialx version="1.39">
  <!--
    Copyright Contributors to the MaterialX Project
    SPDX-License-Identifier: Apache-2.0

    Graph definitions of standard nodes included in the MaterialX specification.
  -->

  <!-- ======================================================================== -->
  <!-- Texture nodes                                                            -->
  <!-- ======================================================================== -->

  <!--
    Node: <tiledimage>
  -->
  <nodegraph name="NG_tiledimage_float" nodedef="ND_tiledimage_float">
    <multiply name="N_mult_float" type="vector2">
      <input name="in1" type="vector2" interfacename="texcoord" />
      <input name="in2" type="vector2" interfacename="uvtiling" />
    </multiply>
    <subtract name="N_sub_float" type="vector2">
      <input name="in1" type="vector2" nodename="N_mult_float" />
      <input name="in2" type="vector2" interfacename="uvoffset" />
    </subtract>
    <divide name="N_divtilesize_float" type="vector2">
      <input name="in1" type="vector2" nodename="N_sub_float" />
      <input name="in2" type="vector2" interfacename="realworldimagesize" />
    </divide>
    <multiply name="N_multtilesize_float" type="vector2">
      <input name="in1" type="vector2" nodename="N_divtilesize_float" />
      <input name="in2" type="vector2" interfacename="realworldtilesize" />
    </multiply>
    <image name="N_img_float" type="float">
      <input name="file" type="filename" interfacename="file" />
      <input name="default" type="float" interfacename="default" />
      <input name="texcoord" type="vector2" nodename="N_multtilesize_float" />
      <input name="uaddressmode" type="string" value="periodic" />
      <input name="vaddressmode" type="string" value="periodic" />
      <input name="filtertype" type="string" interfacename="filtertype" />
      <input name="framerange" type="string" interfacename="framerange" />
      <input name="frameoffset" type="integer" interfacename="frameoffset" />
      <input name="frameendaction" type="string" interfacename="frameendaction" />
    </image>
    <output name="out" type="float" nodename="N_img_float" />
  </nodegraph>
  <nodegraph name="NG_tiledimage_color3" nodedef="ND_tiledimage_color3">
    <multiply name="N_mult_color3" type="vector2">
      <input name="in1" type="vector2" interfacename="texcoord" />
      <input name="in2" type="vector2" interfacename="uvtiling" />
    </multiply>
    <subtract name="N_sub_color3" type="vector2">
      <input name="in1" type="vector2" nodename="N_mult_color3" />
      <input name="in2" type="vector2" interfacename="uvoffset" />
    </subtract>
    <divide name="N_divtilesize_color3" type="vector2">
      <input name="in1" type="vector2" nodename="N_sub_color3" />
      <input name="in2" type="vector2" interfacename="realworldimagesize" />
    </divide>
    <multiply name="N_multtilesize_color3" type="vector2">
      <input name="in1" type="vector2" nodename="N_divtilesize_color3" />
      <input name="in2" type="vector2" interfacename="realworldtilesize" />
    </multiply>
    <image name="N_img_color3" type="color3">
      <input name="file" type="filename" interfacename="file" />
      <input name="default" type="color3" interfacename="default" />
      <input name="texcoord" type="vector2" nodename="N_multtilesize_color3" />
      <input name="uaddressmode" type="string" value="periodic" />
      <input name="vaddressmode" type="string" value="periodic" />
      <input name="filtertype" type="string" interfacename="filtertype" />
      <input name="framerange" type="string" interfacename="framerange" />
      <input name="frameoffset" type="integer" interfacename="frameoffset" />
      <input name="frameendaction" type="string" interfacename="frameendaction" />
    </image>
    <output name="out" type="color3" nodename="N_img_color3" />
  </nodegraph>
  <nodegraph name="NG_tiledimage_color4" nodedef="ND_tiledimage_color4">
    <multiply name="N_mult_color4" type="vector2">
      <input name="in1" type="vector2" interfacename="texcoord" />
      <input name="in2" type="vector2" interfacename="uvtiling" />
    </multiply>
    <subtract name="N_sub_color4" type="vector2">
      <input name="in1" type="vector2" nodename="N_mult_color4" />
      <input name="in2" type="vector2" interfacename="uvoffset" />
    </subtract>
    <divide name="N_divtilesize_color4" type="vector2">
      <input name="in1" type="vector2" nodename="N_sub_color4" />
      <input name="in2" type="vector2" interfacename="realworldimagesize" />
    </divide>
    <multiply name="N_multtilesize_color4" type="vector2">
      <input name="in1" type="vector2" nodename="N_divtilesize_color4" />
      <input name="in2" type="vector2" interfacename="realworldtilesize" />
    </multiply>
    <image name="N_img_color4" type="color4">
      <input name="file" type="filename" interfacename="file" />
      <input name="default" type="color4" interfacename="default" />
      <input name="texcoord" type="vector2" nodename="N_multtilesize_color4" />
      <input name="uaddressmode" type="string" value="periodic" />
      <input name="vaddressmode" type="string" value="periodic" />
      <input name="filtertype" type="string" interfacename="filtertype" />
      <input name="framerange" type="string" interfacename="framerange" />
      <input name="frameoffset" type="integer" interfacename="frameoffset" />
      <input name="frameendaction" type="string" interfacename="frameendaction" />
    </image>
    <output name="out" type="color4" nodename="N_img_color4" />
  </nodegraph>
  <nodegraph name="NG_tiledimage_vector2" nodedef="ND_tiledimage_vector2">
    <multiply name="N_mult_vector2" type="vector2">
      <input name="in1" type="vector2" interfacename="texcoord" />
      <input name="in2" type="vector2" interfacename="uvtiling" />
    </multiply>
    <subtract name="N_sub_vector2" type="vector2">
      <input name="in1" type="vector2" nodename="N_mult_vector2" />
      <input name="in2" type="vector2" interfacename="uvoffset" />
    </subtract>
    <divide name="N_divtilesize_vector2" type="vector2">
      <input name="in1" type="vector2" nodename="N_sub_vector2" />
      <input name="in2" type="vector2" interfacename="realworldimagesize" />
    </divide>
    <multiply name="N_multtilesize_vector2" type="vector2">
      <input name="in1" type="vector2" nodename="N_divtilesize_vector2" />
      <input name="in2" type="vector2" interfacename="realworldtilesize" />
    </multiply>
    <image name="N_img_vector2" type="vector2">
      <input name="file" type="filename" interfacename="file" />
      <input name="default" type="vector2" interfacename="default" />
      <input name="texcoord" type="vector2" nodename="N_multtilesize_vector2" />
      <input name="uaddressmode" type="string" value="periodic" />
      <input name="vaddressmode" type="string" value="periodic" />
      <input name="filtertype" type="string" interfacename="filtertype" />
      <input name="framerange" type="string" interfacename="framerange" />
      <input name="frameoffset" type="integer" interfacename="frameoffset" />
      <input name="frameendaction" type="string" interfacename="frameendaction" />
    </image>
    <output name="out" type="vector2" nodename="N_img_vector2" />
  </nodegraph>
  <nodegraph name="NG_tiledimage_vector3" nodedef="ND_tiledimage_vector3">
    <multiply name="N_mult_vector3" type="vector2">
      <input name="in1" type="vector2" interfacename="texcoord" />
      <input name="in2" type="vector2" interfacename="uvtiling" />
    </multiply>
    <subtract name="N_sub_vector3" type="vector2">
      <input name="in1" type="vector2" nodename="N_mult_vector3" />
      <input name="in2" type="vector2" interfacename="uvoffset" />
    </subtract>
    <divide name="N_divtilesize_vector3" type="vector2">
      <input name="in1" type="vector2" nodename="N_sub_vector3" />
      <input name="in2" type="vector2" interfacename="realworldimagesize" />
    </divide>
    <multiply name="N_multtilesize_vector3" type="vector2">
      <input name="in1" type="vector2" nodename="N_divtilesize_vector3" />
      <input name="in2" type="vector2" interfacename="realworldtilesize" />
    </multiply>
    <image name="N_img_vector3" type="vector3">
      <input name="file" type="filename" interfacename="file" />
      <input name="default" type="vector3" interfacename="default" />
      <input name="texcoord" type="vector2" nodename="N_multtilesize_vector3" />
      <input name="uaddressmode" type="string" value="periodic" />
      <input name="vaddressmode" type="string" value="periodic" />
      <input name="filtertype" type="string" interfacename="filtertype" />
      <input name="framerange" type="string" interfacename="framerange" />
      <input name="frameoffset" type="integer" interfacename="frameoffset" />
      <input name="frameendaction" type="string" interfacename="frameendaction" />
    </image>
    <output name="out" type="vector3" nodename="N_img_vector3" />
  </nodegraph>
  <nodegraph name="NG_tiledimage_vector4" nodedef="ND_tiledimage_vector4">
    <multiply name="N_mult_vector4" type="vector2">
      <input name="in1" type="vector2" interfacename="texcoord" />
      <input name="in2" type="vector2" interfacename="uvtiling" />
    </multiply>
    <subtract name="N_sub_vector4" type="vector2">
      <input name="in1" type="vector2" nodename="N_mult_vector4" />
      <input name="in2" type="vector2" interfacename="uvoffset" />
    </subtract>
    <divide name="N_divtilesize_vector4" type="vector2">
      <input name="in1" type="vector2" nodename="N_sub_vector4" />
      <input name="in2" type="vector2" interfacename="realworldimagesize" />
    </divide>
    <multiply name="N_multtilesize_vector4" type="vector2">
      <input name="in1" type="vector2" nodename="N_divtilesize_vector4" />
      <input name="in2" type="vector2" interfacename="realworldtilesize" />
    </multiply>
    <image name="N_img_vector4" type="vector4">
      <input name="file" type="filename" interfacename="file" />
      <input name="default" type="vector4" interfacename="default" />
      <input name="texcoord" type="vector2" nodename="N_multtilesize_vector4" />
      <input name="uaddressmode" type="string" value="periodic" />
      <input name="vaddressmode" type="string" value="periodic" />
      <input name="filtertype" type="string" interfacename="filtertype" />
      <input name="framerange" type="string" interfacename="framerange" />
      <input name="frameoffset" type="integer" interfacename="frameoffset" />
      <input name="frameendaction" type="string" interfacename="frameendaction" />
    </image>
    <output name="out" type="vector4" nodename="N_img_vector4" />
  </nodegraph>

  <!--
    Node: <triplanarprojection>
    Samples data from three images, or layers within multi-layer images, and projects a tiled
    representation of the images along each of the three respective coordinate axes, computing
    a weighted blend of the three samples using the geometric normal.
  -->
  <nodegraph name="NG_triplanarprojection_float" nodedef="ND_triplanarprojection_float">
    <extract name="N_extX_float" type="float">
      <input name="in" type="vector3" interfacename="position" />
      <input name="index" type="integer" value="0" />
    </extract>
    <extract name="N_extY_float" type="float">
      <input name="in" type="vector3" interfacename="position" />
      <input name="index" type="integer" value="1" />
    </extract>
    <extract name="N_extZ_float" type="float">
      <input name="in" type="vector3" interfacename="position" />
      <input name="index" type="integer" value="2" />
    </extract>
    <combine2 name="N_vecYZ_float" type="vector2">
      <input name="in1" type="float" nodename="N_extY_float" />
      <input name="in2" type="float" nodename="N_extZ_float" />
    </combine2>
    <combine2 name="N_vecXZ_float" type="vector2">
      <input name="in1" type="float" nodename="N_extX_float" />
      <input name="in2" type="float" nodename="N_extZ_float" />
    </combine2>
    <combine2 name="N_vecXY_float" type="vector2">
      <input name="in1" type="float" nodename="N_extX_float" />
      <input name="in2" type="float" nodename="N_extY_float" />
    </combine2>
    <multiply name="N_vecXY_invert_float" type="float">
      <input name="in1" type="float" nodename="N_extY_float" />
      <input name="in2" type="float" value="-1" />
    </multiply>
    <combine2 name="N_vecXY_xUp_float" type="vector2">
      <input name="in1" type="float" nodename="N_vecXY_invert_float" />
      <input name="in2" type="float" nodename="N_extX_float" />
    </combine2>
    <combine2 name="N_vecXZ_xUp_float" type="vector2">
      <input name="in1" type="float" nodename="N_extZ_float" />
      <input name="in2" type="float" nodename="N_extX_float" />
    </combine2>
    <combine2 name="N_vecYZ_yUp_float" type="vector2">
      <input name="in1" type="float" nodename="N_extZ_float" />
      <input name="in2" type="float" nodename="N_extY_float" />
    </combine2>
    <switch name="N_upDirSwitchXY_float" type="vector2">
      <input name="in1" type="vector2" nodename="N_vecXY_xUp_float" />
      <input name="in2" type="vector2" nodename="N_vecXY_float" />
      <input name="in3" type="vector2" nodename="N_vecXY_float" />
      <input name="which" type="integer" interfacename="upaxis" />
    </switch>
    <switch name="N_upDirSwitchXZ_float" type="vector2">
      <input name="in1" type="vector2" nodename="N_vecXZ_xUp_float" />
      <input name="in2" type="vector2" nodename="N_vecXZ_float" />
      <input name="in3" type="vector2" nodename="N_vecXZ_float" />
      <input name="which" type="integer" interfacename="upaxis" />
    </switch>
    <switch name="N_upDirSwitchYZ_float" type="vector2">
      <input name="in1" type="vector2" nodename="N_vecYZ_yUp_float" />
      <input name="in2" type="vector2" nodename="N_vecYZ_yUp_float" />
      <input name="in3" type="vector2" nodename="N_vecYZ_float" />
      <input name="which" type="integer" interfacename="upaxis" />
    </switch>
    <image name="N_imgX_float" type="float">
      <input name="file" type="filename" interfacename="filex" />
      <input name="layer" type="string" interfacename="layerx" />
      <input name="default" type="float" interfacename="default" />
      <input name="texcoord" type="vector2" nodename="N_upDirSwitchYZ_float" />
      <input name="uaddressmode" type="string" value="periodic" />
      <input name="vaddressmode" type="string" value="periodic" />
      <input name="filtertype" type="string" interfacename="filtertype" />
      <input name="framerange" type="string" interfacename="framerange" />
      <input name="frameoffset" type="integer" interfacename="frameoffset" />
      <input name="frameendaction" type="string" interfacename="frameendaction" />
    </image>
    <image name="N_imgY_float" type="float">
      <input name="file" type="filename" interfacename="filey" />
      <input name="layer" type="string" interfacename="layery" />
      <input name="default" type="float" interfacename="default" />
      <input name="texcoord" type="vector2" nodename="N_upDirSwitchXZ_float" />
      <input name="uaddressmode" type="string" value="periodic" />
      <input name="vaddressmode" type="string" value="periodic" />
      <input name="filtertype" type="string" interfacename="filtertype" />
      <input name="framerange" type="string" interfacename="framerange" />
      <input name="frameoffset" type="integer" interfacename="frameoffset" />
      <input name="frameendaction" type="string" interfacename="frameendaction" />
    </image>
    <image name="N_imgZ_float" type="float">
      <input name="file" type="filename" interfacename="filez" />
      <input name="layer" type="string" interfacename="layerz" />
      <input name="default" type="float" interfacename="default" />
      <input name="texcoord" type="vector2" nodename="N_upDirSwitchXY_float" />
      <input name="uaddressmode" type="string" value="periodic" />
      <input name="vaddressmode" type="string" value="periodic" />
      <input name="filtertype" type="string" interfacename="filtertype" />
      <input name="framerange" type="string" interfacename="framerange" />
      <input name="frameoffset" type="integer" interfacename="frameoffset" />
      <input name="frameendaction" type="string" interfacename="frameendaction" />
    </image>
    <normalize name="N_norm_vector3" type="vector3">
      <input name="in" type="vector3" interfacename="normal" />
    </normalize>
    <absval name="N_absN" type="vector3">
      <input name="in" type="vector3" nodename="N_norm_vector3" />
    </absval>
    <dotproduct name="N_dotN" type="float">
      <input name="in1" type="vector3" nodename="N_absN" />
      <input name="in2" type="vector3" value="1.0, 1.0, 1.0" />
    </dotproduct>
    <divide name="N_normalizeWeights" type="vector3">
      <input name="in1" type="vector3" nodename="N_absN" />
      <input name="in2" type="float" nodename="N_dotN" />
    </divide>
    <clamp name="N_clampForPrecision" type="float">
      <input name="in" type="float" interfacename="blend" />
      <input name="low" type="float" value="0.03" />
    </clamp>
    <divide name="N_oneOverBlend" type="float">
      <input name="in1" type="float" value="1.0" />
      <input name="in2" type="float" nodename="N_clampForPrecision" />
    </divide>
    <power name="N_blendPower" type="vector3">
      <input name="in1" type="vector3" nodename="N_normalizeWeights" />
      <input name="in2" type="float" nodename="N_oneOverBlend" />
    </power>
    <dotproduct name="N_dotBlendedN" type="float">
      <input name="in1" type="vector3" nodename="N_blendPower" />
      <input name="in2" type="vector3" value="1.0, 1.0, 1.0" />
    </dotproduct>
    <divide name="N_normalizeBlendedWeights" type="vector3">
      <input name="in1" type="vector3" nodename="N_blendPower" />
      <input name="in2" type="float" nodename="N_dotBlendedN" />
    </divide>
    <separate3 name="N_separateWeights" type="multioutput">
      <input name="in" type="vector3" nodename="N_normalizeBlendedWeights" />
    </separate3>
    <multiply name="N_nX_float" type="float">
      <input name="in1" type="float" nodename="N_imgX_float" />
      <input name="in2" type="float" nodename="N_separateWeights" output="outx" />
    </multiply>
    <multiply name="N_nY_float" type="float">
      <input name="in1" type="float" nodename="N_imgY_float" />
      <input name="in2" type="float" nodename="N_separateWeights" output="outy" />
    </multiply>
    <multiply name="N_nZ_float" type="float">
      <input name="in1" type="float" nodename="N_imgZ_float" />
      <input name="in2" type="float" nodename="N_separateWeights" output="outz" />
    </multiply>
    <add name="N_add1_float" type="float">
      <input name="in1" type="float" nodename="N_nX_float" />
      <input name="in2" type="float" nodename="N_nY_float" />
    </add>
    <add name="N_add2_float" type="float">
      <input name="in1" type="float" nodename="N_add1_float" />
      <input name="in2" type="float" nodename="N_nZ_float" />
    </add>
    <output name="out" type="float" nodename="N_add2_float" />
  </nodegraph>
  <nodegraph name="NG_triplanarprojection_color3" nodedef="ND_triplanarprojection_color3">
    <extract name="N_extX_color3" type="float">
      <input name="in" type="vector3" interfacename="position" />
      <input name="index" type="integer" value="0" />
    </extract>
    <extract name="N_extY_color3" type="float">
      <input name="in" type="vector3" interfacename="position" />
      <input name="index" type="integer" value="1" />
    </extract>
    <extract name="N_extZ_color3" type="float">
      <input name="in" type="vector3" interfacename="position" />
      <input name="index" type="integer" value="2" />
    </extract>
    <combine2 name="N_vecYZ_color3" type="vector2">
      <input name="in1" type="float" nodename="N_extY_color3" />
      <input name="in2" type="float" nodename="N_extZ_color3" />
    </combine2>
    <combine2 name="N_vecXZ_color3" type="vector2">
      <input name="in1" type="float" nodename="N_extX_color3" />
      <input name="in2" type="float" nodename="N_extZ_color3" />
    </combine2>
    <combine2 name="N_vecXY_color3" type="vector2">
      <input name="in1" type="float" nodename="N_extX_color3" />
      <input name="in2" type="float" nodename="N_extY_color3" />
    </combine2>
    <multiply name="N_vecXY_invert_color3" type="float">
      <input name="in1" type="float" nodename="N_extY_color3" />
      <input name="in2" type="float" value="-1" />
    </multiply>
    <combine2 name="N_vecXY_xUp_color3" type="vector2">
      <input name="in1" type="float" nodename="N_vecXY_invert_color3" />
      <input name="in2" type="float" nodename="N_extX_color3" />
    </combine2>
    <combine2 name="N_vecXZ_xUp_color3" type="vector2">
      <input name="in1" type="float" nodename="N_extZ_color3" />
      <input name="in2" type="float" nodename="N_extX_color3" />
    </combine2>
    <combine2 name="N_vecYZ_yUp_color3" type="vector2">
      <input name="in1" type="float" nodename="N_extZ_color3" />
      <input name="in2" type="float" nodename="N_extY_color3" />
    </combine2>
    <switch name="N_upDirSwitchXY_color3" type="vector2">
      <input name="in1" type="vector2" nodename="N_vecXY_xUp_color3" />
      <input name="in2" type="vector2" nodename="N_vecXY_color3" />
      <input name="in3" type="vector2" nodename="N_vecXY_color3" />
      <input name="which" type="integer" interfacename="upaxis" />
    </switch>
    <switch name="N_upDirSwitchXZ_color3" type="vector2">
      <input name="in1" type="vector2" nodename="N_vecXZ_xUp_color3" />
      <input name="in2" type="vector2" nodename="N_vecXZ_color3" />
      <input name="in3" type="vector2" nodename="N_vecXZ_color3" />
      <input name="which" type="integer" interfacename="upaxis" />
    </switch>
    <switch name="N_upDirSwitchYZ_color3" type="vector2">
      <input name="in1" type="vector2" nodename="N_vecYZ_yUp_color3" />
      <input name="in2" type="vector2" nodename="N_vecYZ_yUp_color3" />
      <input name="in3" type="vector2" nodename="N_vecYZ_color3" />
      <input name="which" type="integer" interfacename="upaxis" />
    </switch>
    <image name="N_imgX_color3" type="color3">
      <input name="file" type="filename" interfacename="filex" />
      <input name="layer" type="string" interfacename="layerx" />
      <input name="default" type="color3" interfacename="default" />
      <input name="texcoord" type="vector2" nodename="N_upDirSwitchYZ_color3" />
      <input name="uaddressmode" type="string" value="periodic" />
      <input name="vaddressmode" type="string" value="periodic" />
      <input name="filtertype" type="string" interfacename="filtertype" />
      <input name="framerange" type="string" interfacename="framerange" />
      <input name="frameoffset" type="integer" interfacename="frameoffset" />
      <input name="frameendaction" type="string" interfacename="frameendaction" />
    </image>
    <image name="N_imgY_color3" type="color3">
      <input name="file" type="filename" interfacename="filey" />
      <input name="layer" type="string" interfacename="layery" />
      <input name="default" type="color3" interfacename="default" />
      <input name="texcoord" type="vector2" nodename="N_upDirSwitchXZ_color3" />
      <input name="uaddressmode" type="string" value="periodic" />
      <input name="vaddressmode" type="string" value="periodic" />
      <input name="filtertype" type="string" interfacename="filtertype" />
      <input name="framerange" type="string" interfacename="framerange" />
      <input name="frameoffset" type="integer" interfacename="frameoffset" />
      <input name="frameendaction" type="string" interfacename="frameendaction" />
    </image>
    <image name="N_imgZ_color3" type="color3">
      <input name="file" type="filename" interfacename="filez" />
      <input name="layer" type="string" interfacename="layerz" />
      <input name="default" type="color3" interfacename="default" />
      <input name="texcoord" type="vector2" nodename="N_upDirSwitchXY_color3" />
      <input name="uaddressmode" type="string" value="periodic" />
      <input name="vaddressmode" type="string" value="periodic" />
      <input name="filtertype" type="string" interfacename="filtertype" />
      <input name="framerange" type="string" interfacename="framerange" />
      <input name="frameoffset" type="integer" interfacename="frameoffset" />
      <input name="frameendaction" type="string" interfacename="frameendaction" />
    </image>
    <normalize name="N_norm_vector3" type="vector3">
      <input name="in" type="vector3" interfacename="normal" />
    </normalize>
    <absval name="N_absN" type="vector3">
      <input name="in" type="vector3" nodename="N_norm_vector3" />
    </absval>
    <dotproduct name="N_dotN" type="float">
      <input name="in1" type="vector3" nodename="N_absN" />
      <input name="in2" type="vector3" value="1.0, 1.0, 1.0" />
    </dotproduct>
    <divide name="N_normalizeWeights" type="vector3">
      <input name="in1" type="vector3" nodename="N_absN" />
      <input name="in2" type="float" nodename="N_dotN" />
    </divide>
    <clamp name="N_clampForPrecision" type="float">
      <input name="in" type="float" interfacename="blend" />
      <input name="low" type="float" value="0.03" />
    </clamp>
    <divide name="N_oneOverBlend" type="float">
      <input name="in1" type="float" value="1.0" />
      <input name="in2" type="float" nodename="N_clampForPrecision" />
    </divide>
    <power name="N_blendPower" type="vector3">
      <input name="in1" type="vector3" nodename="N_normalizeWeights" />
      <input name="in2" type="float" nodename="N_oneOverBlend" />
    </power>
    <dotproduct name="N_dotBlendedN" type="float">
      <input name="in1" type="vector3" nodename="N_blendPower" />
      <input name="in2" type="vector3" value="1.0, 1.0, 1.0" />
    </dotproduct>
    <divide name="N_normalizeBlendedWeights" type="vector3">
      <input name="in1" type="vector3" nodename="N_blendPower" />
      <input name="in2" type="float" nodename="N_dotBlendedN" />
    </divide>
    <separate3 name="N_separateWeights" type="multioutput">
      <input name="in" type="vector3" nodename="N_normalizeBlendedWeights" />
    </separate3>
    <multiply name="N_nX_color3" type="color3">
      <input name="in1" type="color3" nodename="N_imgX_color3" />
      <input name="in2" type="float" nodename="N_separateWeights" output="outx" />
    </multiply>
    <multiply name="N_nY_color3" type="color3">
      <input name="in1" type="color3" nodename="N_imgY_color3" />
      <input name="in2" type="float" nodename="N_separateWeights" output="outy" />
    </multiply>
    <multiply name="N_nZ_color3" type="color3">
      <input name="in1" type="color3" nodename="N_imgZ_color3" />
      <input name="in2" type="float" nodename="N_separateWeights" output="outz" />
    </multiply>
    <add name="N_add1_color3" type="color3">
      <input name="in1" type="color3" nodename="N_nX_color3" />
      <input name="in2" type="color3" nodename="N_nY_color3" />
    </add>
    <add name="N_add2_color3" type="color3">
      <input name="in1" type="color3" nodename="N_add1_color3" />
      <input name="in2" type="color3" nodename="N_nZ_color3" />
    </add>
    <output name="out" type="color3" nodename="N_add2_color3" />
  </nodegraph>
  <nodegraph name="NG_triplanarprojection_color4" nodedef="ND_triplanarprojection_color4">
    <extract name="N_extX_color4" type="float">
      <input name="in" type="vector3" interfacename="position" />
      <input name="index" type="integer" value="0" />
    </extract>
    <extract name="N_extY_color4" type="float">
      <input name="in" type="vector3" interfacename="position" />
      <input name="index" type="integer" value="1" />
    </extract>
    <extract name="N_extZ_color4" type="float">
      <input name="in" type="vector3" interfacename="position" />
      <input name="index" type="integer" value="2" />
    </extract>
    <combine2 name="N_vecYZ_color4" type="vector2">
      <input name="in1" type="float" nodename="N_extY_color4" />
      <input name="in2" type="float" nodename="N_extZ_color4" />
    </combine2>
    <combine2 name="N_vecXZ_color4" type="vector2">
      <input name="in1" type="float" nodename="N_extX_color4" />
      <input name="in2" type="float" nodename="N_extZ_color4" />
    </combine2>
    <combine2 name="N_vecXY_color4" type="vector2">
      <input name="in1" type="float" nodename="N_extX_color4" />
      <input name="in2" type="float" nodename="N_extY_color4" />
    </combine2>
    <multiply name="N_vecXY_invert_color4" type="float">
      <input name="in1" type="float" nodename="N_extY_color4" />
      <input name="in2" type="float" value="-1" />
    </multiply>
    <combine2 name="N_vecXY_xUp_color4" type="vector2">
      <input name="in1" type="float" nodename="N_vecXY_invert_color4" />
      <input name="in2" type="float" nodename="N_extX_color4" />
    </combine2>
    <combine2 name="N_vecXZ_xUp_color4" type="vector2">
      <input name="in1" type="float" nodename="N_extZ_color4" />
      <input name="in2" type="float" nodename="N_extX_color4" />
    </combine2>
    <combine2 name="N_vecYZ_yUp_color4" type="vector2">
      <input name="in1" type="float" nodename="N_extZ_color4" />
      <input name="in2" type="float" nodename="N_extY_color4" />
    </combine2>
    <switch name="N_upDirSwitchXY_color4" type="vector2">
      <input name="in1" type="vector2" nodename="N_vecXY_xUp_color4" />
      <input name="in2" type="vector2" nodename="N_vecXY_color4" />
      <input name="in3" type="vector2" nodename="N_vecXY_color4" />
      <input name="which" type="integer" interfacename="upaxis" />
    </switch>
    <switch name="N_upDirSwitchXZ_color4" type="vector2">
      <input name="in1" type="vector2" nodename="N_vecXZ_xUp_color4" />
      <input name="in2" type="vector2" nodename="N_vecXZ_color4" />
      <input name="in3" type="vector2" nodename="N_vecXZ_color4" />
      <input name="which" type="integer" interfacename="upaxis" />
    </switch>
    <switch name="N_upDirSwitchYZ_color4" type="vector2">
      <input name="in1" type="vector2" nodename="N_vecYZ_yUp_color4" />
      <input name="in2" type="vector2" nodename="N_vecYZ_yUp_color4" />
      <input name="in3" type="vector2" nodename="N_vecYZ_color4" />
      <input name="which" type="integer" interfacename="upaxis" />
    </switch>
    <image name="N_imgX_color4" type="color4">
      <input name="file" type="filename" interfacename="filex" />
      <input name="layer" type="string" interfacename="layerx" />
      <input name="default" type="color4" interfacename="default" />
      <input name="texcoord" type="vector2" nodename="N_upDirSwitchYZ_color4" />
      <input name="uaddressmode" type="string" value="periodic" />
      <input name="vaddressmode" type="string" value="periodic" />
      <input name="filtertype" type="string" interfacename="filtertype" />
      <input name="framerange" type="string" interfacename="framerange" />
      <input name="frameoffset" type="integer" interfacename="frameoffset" />
      <input name="frameendaction" type="string" interfacename="frameendaction" />
    </image>
    <image name="N_imgY_color4" type="color4">
      <input name="file" type="filename" interfacename="filey" />
      <input name="layer" type="string" interfacename="layery" />
      <input name="default" type="color4" interfacename="default" />
      <input name="texcoord" type="vector2" nodename="N_upDirSwitchXZ_color4" />
      <input name="uaddressmode" type="string" value="periodic" />
      <input name="vaddressmode" type="string" value="periodic" />
      <input name="filtertype" type="string" interfacename="filtertype" />
      <input name="framerange" type="string" interfacename="framerange" />
      <input name="frameoffset" type="integer" interfacename="frameoffset" />
      <input name="frameendaction" type="string" interfacename="frameendaction" />
    </image>
    <image name="N_imgZ_color4" type="color4">
      <input name="file" type="filename" interfacename="filez" />
      <input name="layer" type="string" interfacename="layerz" />
      <input name="default" type="color4" interfacename="default" />
      <input name="texcoord" type="vector2" nodename="N_upDirSwitchXY_color4" />
      <input name="uaddressmode" type="string" value="periodic" />
      <input name="vaddressmode" type="string" value="periodic" />
      <input name="filtertype" type="string" interfacename="filtertype" />
      <input name="framerange" type="string" interfacename="framerange" />
      <input name="frameoffset" type="integer" interfacename="frameoffset" />
      <input name="frameendaction" type="string" interfacename="frameendaction" />
    </image>
    <normalize name="N_norm_vector3" type="vector3">
      <input name="in" type="vector3" interfacename="normal" />
    </normalize>
    <absval name="N_absN" type="vector3">
      <input name="in" type="vector3" nodename="N_norm_vector3" />
    </absval>
    <dotproduct name="N_dotN" type="float">
      <input name="in1" type="vector3" nodename="N_absN" />
      <input name="in2" type="vector3" value="1.0, 1.0, 1.0" />
    </dotproduct>
    <divide name="N_normalizeWeights" type="vector3">
      <input name="in1" type="vector3" nodename="N_absN" />
      <input name="in2" type="float" nodename="N_dotN" />
    </divide>
    <clamp name="N_clampForPrecision" type="float">
      <input name="in" type="float" interfacename="blend" />
      <input name="low" type="float" value="0.03" />
    </clamp>
    <divide name="N_oneOverBlend" type="float">
      <input name="in1" type="float" value="1.0" />
      <input name="in2" type="float" nodename="N_clampForPrecision" />
    </divide>
    <power name="N_blendPower" type="vector3">
      <input name="in1" type="vector3" nodename="N_normalizeWeights" />
      <input name="in2" type="float" nodename="N_oneOverBlend" />
    </power>
    <dotproduct name="N_dotBlendedN" type="float">
      <input name="in1" type="vector3" nodename="N_blendPower" />
      <input name="in2" type="vector3" value="1.0, 1.0, 1.0" />
    </dotproduct>
    <divide name="N_normalizeBlendedWeights" type="vector3">
      <input name="in1" type="vector3" nodename="N_blendPower" />
      <input name="in2" type="float" nodename="N_dotBlendedN" />
    </divide>
    <separate3 name="N_separateWeights" type="multioutput">
      <input name="in" type="vector3" nodename="N_normalizeBlendedWeights" />
    </separate3>
    <multiply name="N_nX_color4" type="color4">
      <input name="in1" type="color4" nodename="N_imgX_color4" />
      <input name="in2" type="float" nodename="N_separateWeights" output="outx" />
    </multiply>
    <multiply name="N_nY_color4" type="color4">
      <input name="in1" type="color4" nodename="N_imgY_color4" />
      <input name="in2" type="float" nodename="N_separateWeights" output="outy" />
    </multiply>
    <multiply name="N_nZ_color4" type="color4">
      <input name="in1" type="color4" nodename="N_imgZ_color4" />
      <input name="in2" type="float" nodename="N_separateWeights" output="outz" />
    </multiply>
    <add name="N_add1_color4" type="color4">
      <input name="in1" type="color4" nodename="N_nX_color4" />
      <input name="in2" type="color4" nodename="N_nY_color4" />
    </add>
    <add name="N_add2_color4" type="color4">
      <input name="in1" type="color4" nodename="N_add1_color4" />
      <input name="in2" type="color4" nodename="N_nZ_color4" />
    </add>
    <output name="out" type="color4" nodename="N_add2_color4" />
  </nodegraph>
  <nodegraph name="NG_triplanarprojection_vector2" nodedef="ND_triplanarprojection_vector2">
    <extract name="N_extX_vector2" type="float">
      <input name="in" type="vector3" interfacename="position" />
      <input name="index" type="integer" value="0" />
    </extract>
    <extract name="N_extY_vector2" type="float">
      <input name="in" type="vector3" interfacename="position" />
      <input name="index" type="integer" value="1" />
    </extract>
    <extract name="N_extZ_vector2" type="float">
      <input name="in" type="vector3" interfacename="position" />
      <input name="index" type="integer" value="2" />
    </extract>
    <combine2 name="N_vecYZ_vector2" type="vector2">
      <input name="in1" type="float" nodename="N_extY_vector2" />
      <input name="in2" type="float" nodename="N_extZ_vector2" />
    </combine2>
    <combine2 name="N_vecXZ_vector2" type="vector2">
      <input name="in1" type="float" nodename="N_extX_vector2" />
      <input name="in2" type="float" nodename="N_extZ_vector2" />
    </combine2>
    <combine2 name="N_vecXY_vector2" type="vector2">
      <input name="in1" type="float" nodename="N_extX_vector2" />
      <input name="in2" type="float" nodename="N_extY_vector2" />
    </combine2>
    <multiply name="N_vecXY_invert_vector2" type="float">
      <input name="in1" type="float" nodename="N_extY_vector2" />
      <input name="in2" type="float" value="-1" />
    </multiply>
    <combine2 name="N_vecXY_xUp_vector2" type="vector2">
      <input name="in1" type="float" nodename="N_vecXY_invert_vector2" />
      <input name="in2" type="float" nodename="N_extX_vector2" />
    </combine2>
    <combine2 name="N_vecXZ_xUp_vector2" type="vector2">
      <input name="in1" type="float" nodename="N_extZ_vector2" />
      <input name="in2" type="float" nodename="N_extX_vector2" />
    </combine2>
    <combine2 name="N_vecYZ_yUp_vector2" type="vector2">
      <input name="in1" type="float" nodename="N_extZ_vector2" />
      <input name="in2" type="float" nodename="N_extY_vector2" />
    </combine2>
    <switch name="N_upDirSwitchXY_vector2" type="vector2">
      <input name="in1" type="vector2" nodename="N_vecXY_xUp_vector2" />
      <input name="in2" type="vector2" nodename="N_vecXY_vector2" />
      <input name="in3" type="vector2" nodename="N_vecXY_vector2" />
      <input name="which" type="integer" interfacename="upaxis" />
    </switch>
    <switch name="N_upDirSwitchXZ_vector2" type="vector2">
      <input name="in1" type="vector2" nodename="N_vecXZ_xUp_vector2" />
      <input name="in2" type="vector2" nodename="N_vecXZ_vector2" />
      <input name="in3" type="vector2" nodename="N_vecXZ_vector2" />
      <input name="which" type="integer" interfacename="upaxis" />
    </switch>
    <switch name="N_upDirSwitchYZ_vector2" type="vector2">
      <input name="in1" type="vector2" nodename="N_vecYZ_yUp_vector2" />
      <input name="in2" type="vector2" nodename="N_vecYZ_yUp_vector2" />
      <input name="in3" type="vector2" nodename="N_vecYZ_vector2" />
      <input name="which" type="integer" interfacename="upaxis" />
    </switch>
    <image name="N_imgX_vector2" type="vector2">
      <input name="file" type="filename" interfacename="filex" />
      <input name="layer" type="string" interfacename="layerx" />
      <input name="default" type="vector2" interfacename="default" />
      <input name="texcoord" type="vector2" nodename="N_upDirSwitchYZ_vector2" />
      <input name="uaddressmode" type="string" value="periodic" />
      <input name="vaddressmode" type="string" value="periodic" />
      <input name="filtertype" type="string" interfacename="filtertype" />
      <input name="framerange" type="string" interfacename="framerange" />
      <input name="frameoffset" type="integer" interfacename="frameoffset" />
      <input name="frameendaction" type="string" interfacename="frameendaction" />
    </image>
    <image name="N_imgY_vector2" type="vector2">
      <input name="file" type="filename" interfacename="filey" />
      <input name="layer" type="string" interfacename="layery" />
      <input name="default" type="vector2" interfacename="default" />
      <input name="texcoord" type="vector2" nodename="N_upDirSwitchXZ_vector2" />
      <input name="uaddressmode" type="string" value="periodic" />
      <input name="vaddressmode" type="string" value="periodic" />
      <input name="filtertype" type="string" interfacename="filtertype" />
      <input name="framerange" type="string" interfacename="framerange" />
      <input name="frameoffset" type="integer" interfacename="frameoffset" />
      <input name="frameendaction" type="string" interfacename="frameendaction" />
    </image>
    <image name="N_imgZ_vector2" type="vector2">
      <input name="file" type="filename" interfacename="filez" />
      <input name="layer" type="string" interfacename="layerz" />
      <input name="default" type="vector2" interfacename="default" />
      <input name="texcoord" type="vector2" nodename="N_upDirSwitchXY_vector2" />
      <input name="uaddressmode" type="string" value="periodic" />
      <input name="vaddressmode" type="string" value="periodic" />
      <input name="filtertype" type="string" interfacename="filtertype" />
      <input name="framerange" type="string" interfacename="framerange" />
      <input name="frameoffset" type="integer" interfacename="frameoffset" />
      <input name="frameendaction" type="string" interfacename="frameendaction" />
    </image>
    <normalize name="N_norm_vector3" type="vector3">
      <input name="in" type="vector3" interfacename="normal" />
    </normalize>
    <absval name="N_absN" type="vector3">
      <input name="in" type="vector3" nodename="N_norm_vector3" />
    </absval>
    <dotproduct name="N_dotN" type="float">
      <input name="in1" type="vector3" nodename="N_absN" />
      <input name="in2" type="vector3" value="1.0, 1.0, 1.0" />
    </dotproduct>
    <divide name="N_normalizeWeights" type="vector3">
      <input name="in1" type="vector3" nodename="N_absN" />
      <input name="in2" type="float" nodename="N_dotN" />
    </divide>
    <clamp name="N_clampForPrecision" type="float">
      <input name="in" type="float" interfacename="blend" />
      <input name="low" type="float" value="0.03" />
    </clamp>
    <divide name="N_oneOverBlend" type="float">
      <input name="in1" type="float" value="1.0" />
      <input name="in2" type="float" nodename="N_clampForPrecision" />
    </divide>
    <power name="N_blendPower" type="vector3">
      <input name="in1" type="vector3" nodename="N_normalizeWeights" />
      <input name="in2" type="float" nodename="N_oneOverBlend" />
    </power>
    <dotproduct name="N_dotBlendedN" type="float">
      <input name="in1" type="vector3" nodename="N_blendPower" />
      <input name="in2" type="vector3" value="1.0, 1.0, 1.0" />
    </dotproduct>
    <divide name="N_normalizeBlendedWeights" type="vector3">
      <input name="in1" type="vector3" nodename="N_blendPower" />
      <input name="in2" type="float" nodename="N_dotBlendedN" />
    </divide>
    <separate3 name="N_separateWeights" type="multioutput">
      <input name="in" type="vector3" nodename="N_normalizeBlendedWeights" />
    </separate3>
    <multiply name="N_nX_vector2" type="vector2">
      <input name="in1" type="vector2" nodename="N_imgX_vector2" />
      <input name="in2" type="float" nodename="N_separateWeights" output="outx" />
    </multiply>
    <multiply name="N_nY_vector2" type="vector2">
      <input name="in1" type="vector2" nodename="N_imgY_vector2" />
      <input name="in2" type="float" nodename="N_separateWeights" output="outy" />
    </multiply>
    <multiply name="N_nZ_vector2" type="vector2">
      <input name="in1" type="vector2" nodename="N_imgZ_vector2" />
      <input name="in2" type="float" nodename="N_separateWeights" output="outz" />
    </multiply>
    <add name="N_add1_vector2" type="vector2">
      <input name="in1" type="vector2" nodename="N_nX_vector2" />
      <input name="in2" type="vector2" nodename="N_nY_vector2" />
    </add>
    <add name="N_add2_vector2" type="vector2">
      <input name="in1" type="vector2" nodename="N_add1_vector2" />
      <input name="in2" type="vector2" nodename="N_nZ_vector2" />
    </add>
    <output name="out" type="vector2" nodename="N_add2_vector2" />
  </nodegraph>
  <nodegraph name="NG_triplanarprojection_vector3" nodedef="ND_triplanarprojection_vector3">
    <extract name="N_extX_vector3" type="float">
      <input name="in" type="vector3" interfacename="position" />
      <input name="index" type="integer" value="0" />
    </extract>
    <extract name="N_extY_vector3" type="float">
      <input name="in" type="vector3" interfacename="position" />
      <input name="index" type="integer" value="1" />
    </extract>
    <extract name="N_extZ_vector3" type="float">
      <input name="in" type="vector3" interfacename="position" />
      <input name="index" type="integer" value="2" />
    </extract>
    <combine2 name="N_vecYZ_vector3" type="vector2">
      <input name="in1" type="float" nodename="N_extY_vector3" />
      <input name="in2" type="float" nodename="N_extZ_vector3" />
    </combine2>
    <combine2 name="N_vecXZ_vector3" type="vector2">
      <input name="in1" type="float" nodename="N_extX_vector3" />
      <input name="in2" type="float" nodename="N_extZ_vector3" />
    </combine2>
    <combine2 name="N_vecXY_vector3" type="vector2">
      <input name="in1" type="float" nodename="N_extX_vector3" />
      <input name="in2" type="float" nodename="N_extY_vector3" />
    </combine2>
    <multiply name="N_vecXY_invert_vector3" type="float">
      <input name="in1" type="float" nodename="N_extY_vector3" />
      <input name="in2" type="float" value="-1" />
    </multiply>
    <combine2 name="N_vecXY_xUp_vector3" type="vector2">
      <input name="in1" type="float" nodename="N_vecXY_invert_vector3" />
      <input name="in2" type="float" nodename="N_extX_vector3" />
    </combine2>
    <combine2 name="N_vecXZ_xUp_vector3" type="vector2">
      <input name="in1" type="float" nodename="N_extZ_vector3" />
      <input name="in2" type="float" nodename="N_extX_vector3" />
    </combine2>
    <combine2 name="N_vecYZ_yUp_vector3" type="vector2">
      <input name="in1" type="float" nodename="N_extZ_vector3" />
      <input name="in2" type="float" nodename="N_extY_vector3" />
    </combine2>
    <switch name="N_upDirSwitchXY_vector3" type="vector2">
      <input name="in1" type="vector2" nodename="N_vecXY_xUp_vector3" />
      <input name="in2" type="vector2" nodename="N_vecXY_vector3" />
      <input name="in3" type="vector2" nodename="N_vecXY_vector3" />
      <input name="which" type="integer" interfacename="upaxis" />
    </switch>
    <switch name="N_upDirSwitchXZ_vector3" type="vector2">
      <input name="in1" type="vector2" nodename="N_vecXZ_xUp_vector3" />
      <input name="in2" type="vector2" nodename="N_vecXZ_vector3" />
      <input name="in3" type="vector2" nodename="N_vecXZ_vector3" />
      <input name="which" type="integer" interfacename="upaxis" />
    </switch>
    <switch name="N_upDirSwitchYZ_vector3" type="vector2">
      <input name="in1" type="vector2" nodename="N_vecYZ_yUp_vector3" />
      <input name="in2" type="vector2" nodename="N_vecYZ_yUp_vector3" />
      <input name="in3" type="vector2" nodename="N_vecYZ_vector3" />
      <input name="which" type="integer" interfacename="upaxis" />
    </switch>
    <image name="N_imgX_vector3" type="vector3">
      <input name="file" type="filename" interfacename="filex" />
      <input name="layer" type="string" interfacename="layerx" />
      <input name="default" type="vector3" interfacename="default" />
      <input name="texcoord" type="vector2" nodename="N_upDirSwitchYZ_vector3" />
      <input name="uaddressmode" type="string" value="periodic" />
      <input name="vaddressmode" type="string" value="periodic" />
      <input name="filtertype" type="string" interfacename="filtertype" />
      <input name="framerange" type="string" interfacename="framerange" />
      <input name="frameoffset" type="integer" interfacename="frameoffset" />
      <input name="frameendaction" type="string" interfacename="frameendaction" />
    </image>
    <image name="N_imgY_vector3" type="vector3">
      <input name="file" type="filename" interfacename="filey" />
      <input name="layer" type="string" interfacename="layery" />
      <input name="default" type="vector3" interfacename="default" />
      <input name="texcoord" type="vector2" nodename="N_upDirSwitchXZ_vector3" />
      <input name="uaddressmode" type="string" value="periodic" />
      <input name="vaddressmode" type="string" value="periodic" />
      <input name="filtertype" type="string" interfacename="filtertype" />
      <input name="framerange" type="string" interfacename="framerange" />
      <input name="frameoffset" type="integer" interfacename="frameoffset" />
      <input name="frameendaction" type="string" interfacename="frameendaction" />
    </image>
    <image name="N_imgZ_vector3" type="vector3">
      <input name="file" type="filename" interfacename="filez" />
      <input name="layer" type="string" interfacename="layerz" />
      <input name="default" type="vector3" interfacename="default" />
      <input name="texcoord" type="vector2" nodename="N_upDirSwitchXY_vector3" />
      <input name="uaddressmode" type="string" value="periodic" />
      <input name="vaddressmode" type="string" value="periodic" />
      <input name="filtertype" type="string" interfacename="filtertype" />
      <input name="framerange" type="string" interfacename="framerange" />
      <input name="frameoffset" type="integer" interfacename="frameoffset" />
      <input name="frameendaction" type="string" interfacename="frameendaction" />
    </image>
    <normalize name="N_norm_vector3" type="vector3">
      <input name="in" type="vector3" interfacename="normal" />
    </normalize>
    <absval name="N_absN" type="vector3">
      <input name="in" type="vector3" nodename="N_norm_vector3" />
    </absval>
    <dotproduct name="N_dotN" type="float">
      <input name="in1" type="vector3" nodename="N_absN" />
      <input name="in2" type="vector3" value="1.0, 1.0, 1.0" />
    </dotproduct>
    <divide name="N_normalizeWeights" type="vector3">
      <input name="in1" type="vector3" nodename="N_absN" />
      <input name="in2" type="float" nodename="N_dotN" />
    </divide>
    <clamp name="N_clampForPrecision" type="float">
      <input name="in" type="float" interfacename="blend" />
      <input name="low" type="float" value="0.03" />
    </clamp>
    <divide name="N_oneOverBlend" type="float">
      <input name="in1" type="float" value="1.0" />
      <input name="in2" type="float" nodename="N_clampForPrecision" />
    </divide>
    <power name="N_blendPower" type="vector3">
      <input name="in1" type="vector3" nodename="N_normalizeWeights" />
      <input name="in2" type="float" nodename="N_oneOverBlend" />
    </power>
    <dotproduct name="N_dotBlendedN" type="float">
      <input name="in1" type="vector3" nodename="N_blendPower" />
      <input name="in2" type="vector3" value="1.0, 1.0, 1.0" />
    </dotproduct>
    <divide name="N_normalizeBlendedWeights" type="vector3">
      <input name="in1" type="vector3" nodename="N_blendPower" />
      <input name="in2" type="float" nodename="N_dotBlendedN" />
    </divide>
    <separate3 name="N_separateWeights" type="multioutput">
      <input name="in" type="vector3" nodename="N_normalizeBlendedWeights" />
    </separate3>
    <multiply name="N_nX_vector3" type="vector3">
      <input name="in1" type="vector3" nodename="N_imgX_vector3" />
      <input name="in2" type="float" nodename="N_separateWeights" output="outx" />
    </multiply>
    <multiply name="N_nY_vector3" type="vector3">
      <input name="in1" type="vector3" nodename="N_imgY_vector3" />
      <input name="in2" type="float" nodename="N_separateWeights" output="outy" />
    </multiply>
    <multiply name="N_nZ_vector3" type="vector3">
      <input name="in1" type="vector3" nodename="N_imgZ_vector3" />
      <input name="in2" type="float" nodename="N_separateWeights" output="outz" />
    </multiply>
    <add name="N_add1_vector3" type="vector3">
      <input name="in1" type="vector3" nodename="N_nX_vector3" />
      <input name="in2" type="vector3" nodename="N_nY_vector3" />
    </add>
    <add name="N_add2_vector3" type="vector3">
      <input name="in1" type="vector3" nodename="N_add1_vector3" />
      <input name="in2" type="vector3" nodename="N_nZ_vector3" />
    </add>
    <output name="out" type="vector3" nodename="N_add2_vector3" />
  </nodegraph>
  <nodegraph name="NG_triplanarprojection_vector4" nodedef="ND_triplanarprojection_vector4">
    <extract name="N_extX_vector4" type="float">
      <input name="in" type="vector3" interfacename="position" />
      <input name="index" type="integer" value="0" />
    </extract>
    <extract name="N_extY_vector4" type="float">
      <input name="in" type="vector3" interfacename="position" />
      <input name="index" type="integer" value="1" />
    </extract>
    <extract name="N_extZ_vector4" type="float">
      <input name="in" type="vector3" interfacename="position" />
      <input name="index" type="integer" value="2" />
    </extract>
    <combine2 name="N_vecYZ_vector4" type="vector2">
      <input name="in1" type="float" nodename="N_extY_vector4" />
      <input name="in2" type="float" nodename="N_extZ_vector4" />
    </combine2>
    <combine2 name="N_vecXZ_vector4" type="vector2">
      <input name="in1" type="float" nodename="N_extX_vector4" />
      <input name="in2" type="float" nodename="N_extZ_vector4" />
    </combine2>
    <combine2 name="N_vecXY_vector4" type="vector2">
      <input name="in1" type="float" nodename="N_extX_vector4" />
      <input name="in2" type="float" nodename="N_extY_vector4" />
    </combine2>
    <multiply name="N_vecXY_invert_vector4" type="float">
      <input name="in1" type="float" nodename="N_extY_vector4" />
      <input name="in2" type="float" value="-1" />
    </multiply>
    <combine2 name="N_vecXY_xUp_vector4" type="vector2">
      <input name="in1" type="float" nodename="N_vecXY_invert_vector4" />
      <input name="in2" type="float" nodename="N_extX_vector4" />
    </combine2>
    <combine2 name="N_vecXZ_xUp_vector4" type="vector2">
      <input name="in1" type="float" nodename="N_extZ_vector4" />
      <input name="in2" type="float" nodename="N_extX_vector4" />
    </combine2>
    <combine2 name="N_vecYZ_yUp_vector4" type="vector2">
      <input name="in1" type="float" nodename="N_extZ_vector4" />
      <input name="in2" type="float" nodename="N_extY_vector4" />
    </combine2>
    <switch name="N_upDirSwitchXY_vector4" type="vector2">
      <input name="in1" type="vector2" nodename="N_vecXY_xUp_vector4" />
      <input name="in2" type="vector2" nodename="N_vecXY_vector4" />
      <input name="in3" type="vector2" nodename="N_vecXY_vector4" />
      <input name="which" type="integer" interfacename="upaxis" />
    </switch>
    <switch name="N_upDirSwitchXZ_vector4" type="vector2">
      <input name="in1" type="vector2" nodename="N_vecXZ_xUp_vector4" />
      <input name="in2" type="vector2" nodename="N_vecXZ_vector4" />
      <input name="in3" type="vector2" nodename="N_vecXZ_vector4" />
      <input name="which" type="integer" interfacename="upaxis" />
    </switch>
    <switch name="N_upDirSwitchYZ_vector4" type="vector2">
      <input name="in1" type="vector2" nodename="N_vecYZ_yUp_vector4" />
      <input name="in2" type="vector2" nodename="N_vecYZ_yUp_vector4" />
      <input name="in3" type="vector2" nodename="N_vecYZ_vector4" />
      <input name="which" type="integer" interfacename="upaxis" />
    </switch>
    <image name="N_imgX_vector4" type="vector4">
      <input name="file" type="filename" interfacename="filex" />
      <input name="layer" type="string" interfacename="layerx" />
      <input name="default" type="vector4" interfacename="default" />
      <input name="texcoord" type="vector2" nodename="N_upDirSwitchYZ_vector4" />
      <input name="uaddressmode" type="string" value="periodic" />
      <input name="vaddressmode" type="string" value="periodic" />
      <input name="filtertype" type="string" interfacename="filtertype" />
      <input name="framerange" type="string" interfacename="framerange" />
      <input name="frameoffset" type="integer" interfacename="frameoffset" />
      <input name="frameendaction" type="string" interfacename="frameendaction" />
    </image>
    <image name="N_imgY_vector4" type="vector4">
      <input name="file" type="filename" interfacename="filey" />
      <input name="layer" type="string" interfacename="layery" />
      <input name="default" type="vector4" interfacename="default" />
      <input name="texcoord" type="vector2" nodename="N_upDirSwitchXZ_vector4" />
      <input name="uaddressmode" type="string" value="periodic" />
      <input name="vaddressmode" type="string" value="periodic" />
      <input name="filtertype" type="string" interfacename="filtertype" />
      <input name="framerange" type="string" interfacename="framerange" />
      <input name="frameoffset" type="integer" interfacename="frameoffset" />
      <input name="frameendaction" type="string" interfacename="frameendaction" />
    </image>
    <image name="N_imgZ_vector4" type="vector4">
      <input name="file" type="filename" interfacename="filez" />
      <input name="layer" type="string" interfacename="layerz" />
      <input name="default" type="vector4" interfacename="default" />
      <input name="texcoord" type="vector2" nodename="N_upDirSwitchXY_vector4" />
      <input name="uaddressmode" type="string" value="periodic" />
      <input name="vaddressmode" type="string" value="periodic" />
      <input name="filtertype" type="string" interfacename="filtertype" />
      <input name="framerange" type="string" interfacename="framerange" />
      <input name="frameoffset" type="integer" interfacename="frameoffset" />
      <input name="frameendaction" type="string" interfacename="frameendaction" />
    </image>
    <normalize name="N_norm_vector3" type="vector3">
      <input name="in" type="vector3" interfacename="normal" />
    </normalize>
    <absval name="N_absN" type="vector3">
      <input name="in" type="vector3" nodename="N_norm_vector3" />
    </absval>
    <dotproduct name="N_dotN" type="float">
      <input name="in1" type="vector3" nodename="N_absN" />
      <input name="in2" type="vector3" value="1.0, 1.0, 1.0" />
    </dotproduct>
    <divide name="N_normalizeWeights" type="vector3">
      <input name="in1" type="vector3" nodename="N_absN" />
      <input name="in2" type="float" nodename="N_dotN" />
    </divide>
    <clamp name="N_clampForPrecision" type="float">
      <input name="in" type="float" interfacename="blend" />
      <input name="low" type="float" value="0.03" />
    </clamp>
    <divide name="N_oneOverBlend" type="float">
      <input name="in1" type="float" value="1.0" />
      <input name="in2" type="float" nodename="N_clampForPrecision" />
    </divide>
    <power name="N_blendPower" type="vector3">
      <input name="in1" type="vector3" nodename="N_normalizeWeights" />
      <input name="in2" type="float" nodename="N_oneOverBlend" />
    </power>
    <dotproduct name="N_dotBlendedN" type="float">
      <input name="in1" type="vector3" nodename="N_blendPower" />
      <input name="in2" type="vector3" value="1.0, 1.0, 1.0" />
    </dotproduct>
    <divide name="N_normalizeBlendedWeights" type="vector3">
      <input name="in1" type="vector3" nodename="N_blendPower" />
      <input name="in2" type="float" nodename="N_dotBlendedN" />
    </divide>
    <separate3 name="N_separateWeights" type="multioutput">
      <input name="in" type="vector3" nodename="N_normalizeBlendedWeights" />
    </separate3>
    <multiply name="N_nX_vector4" type="vector4">
      <input name="in1" type="vector4" nodename="N_imgX_vector4" />
      <input name="in2" type="float" nodename="N_separateWeights" output="outx" />
    </multiply>
    <multiply name="N_nY_vector4" type="vector4">
      <input name="in1" type="vector4" nodename="N_imgY_vector4" />
      <input name="in2" type="float" nodename="N_separateWeights" output="outy" />
    </multiply>
    <multiply name="N_nZ_vector4" type="vector4">
      <input name="in1" type="vector4" nodename="N_imgZ_vector4" />
      <input name="in2" type="float" nodename="N_separateWeights" output="outz" />
    </multiply>
    <add name="N_add1_vector4" type="vector4">
      <input name="in1" type="vector4" nodename="N_nX_vector4" />
      <input name="in2" type="vector4" nodename="N_nY_vector4" />
    </add>
    <add name="N_add2_vector4" type="vector4">
      <input name="in1" type="vector4" nodename="N_add1_vector4" />
      <input name="in2" type="vector4" nodename="N_nZ_vector4" />
    </add>
    <output name="out" type="vector4" nodename="N_add2_vector4" />
  </nodegraph>

  <!-- ======================================================================== -->
  <!-- Procedural nodes                                                         -->
  <!-- ======================================================================== -->

  <!--
    Node: <ramp>
    A ramp that supports up to 10 control points.
  -->
  <nodegraph name="NG_ramp" nodedef="ND_ramp">
    <separate2 name="separate1" type="multioutput" nodedef="ND_separate2_vector2">
      <input name="in" type="vector2" interfacename="texcoord" />
    </separate2>
    <ramp_gradient name="ramp_gradient1" type="color4" nodedef="ND_ramp_gradient">
      <input name="interpolation" type="integer" interfacename="interpolation" />
      <input name="num_intervals" type="integer" interfacename="num_intervals" />
      <input name="interval_num" type="integer" value="1" />
      <input name="interval1" type="float" interfacename="interval1" />
      <input name="interval2" type="float" interfacename="interval2" />
      <input name="color1" type="color4" interfacename="color1" />
      <input name="prev_color" type="color4" interfacename="color1" />
      <input name="color2" type="color4" interfacename="color2" />
      <input name="x" type="float" nodename="ramp_type_selector" />
    </ramp_gradient> 
    <ramp_gradient name="ramp_gradient2" type="color4" nodedef="ND_ramp_gradient">
      <input name="prev_color" type="color4" nodename="ramp_gradient1" />
      <input name="interpolation" type="integer" interfacename="interpolation" />
      <input name="interval_num" type="integer" value="2" />
      <input name="num_intervals" type="integer" interfacename="num_intervals" />
      <input name="interval1" type="float" interfacename="interval2" />
      <input name="interval2" type="float" interfacename="interval3" />
      <input name="color1" type="color4" interfacename="color2" />
      <input name="color2" type="color4" interfacename="color3" />
      <input name="x" type="float" nodename="ramp_type_selector" />
    </ramp_gradient> 
    <ramp_gradient name="ramp_gradient3" type="color4" nodedef="ND_ramp_gradient">
      <input name="prev_color" type="color4" nodename="ramp_gradient2" />
      <input name="interpolation" type="integer" interfacename="interpolation" />
      <input name="interval_num" type="integer" value="3" />
      <input name="num_intervals" type="integer" interfacename="num_intervals" />
      <input name="interval1" type="float" interfacename="interval3" />
      <input name="interval2" type="float" interfacename="interval4" />
      <input name="color1" type="color4" interfacename="color3" />
      <input name="color2" type="color4" interfacename="color4" />
      <input name="x" type="float" nodename="ramp_type_selector" />
    </ramp_gradient>
    <ramp_gradient name="ramp_gradient4" type="color4" nodedef="ND_ramp_gradient">
      <input name="prev_color" type="color4" nodename="ramp_gradient3" />
      <input name="interpolation" type="integer" interfacename="interpolation" />
      <input name="interval_num" type="integer" value="4" />
      <input name="num_intervals" type="integer" interfacename="num_intervals" />
      <input name="interval1" type="float" interfacename="interval4" />
      <input name="interval2" type="float" interfacename="interval5" />
      <input name="color1" type="color4" interfacename="color4" />
      <input name="color2" type="color4" interfacename="color5" />
      <input name="x" type="float" nodename="ramp_type_selector" />
    </ramp_gradient>
    <ramp_gradient name="ramp_gradient5" type="color4" nodedef="ND_ramp_gradient">
      <input name="prev_color" type="color4" nodename="ramp_gradient4" />
      <input name="interpolation" type="integer" interfacename="interpolation" />
      <input name="interval_num" type="integer" value="5" />
      <input name="num_intervals" type="integer" interfacename="num_intervals" />
      <input name="interval1" type="float" interfacename="interval5" />
      <input name="interval2" type="float" interfacename="interval6" />
      <input name="color1" type="color4" interfacename="color5" />
      <input name="color2" type="color4" interfacename="color6" />
      <input name="x" type="float" nodename="ramp_type_selector" />
    </ramp_gradient>
    <ramp_gradient name="ramp_gradient6" type="color4" nodedef="ND_ramp_gradient">
      <input name="interpolation" type="integer" interfacename="interpolation" />
      <input name="prev_color" type="color4" nodename="ramp_gradient5" />
      <input name="interval_num" type="integer" value="6" />
      <input name="num_intervals" type="integer" interfacename="num_intervals" />
      <input name="interval1" type="float" interfacename="interval6" />
      <input name="interval2" type="float" interfacename="interval7" />
      <input name="color1" type="color4" interfacename="color6" />
      <input name="color2" type="color4" interfacename="color7" />
      <input name="x" type="float" nodename="ramp_type_selector" />
    </ramp_gradient>
    <ramp_gradient name="ramp_gradient7" type="color4" nodedef="ND_ramp_gradient">
      <input name="prev_color" type="color4" nodename="ramp_gradient6" />
      <input name="interpolation" type="integer" interfacename="interpolation" />
      <input name="interval_num" type="integer" value="7" />
      <input name="num_intervals" type="integer" interfacename="num_intervals" />
      <input name="interval1" type="float" interfacename="interval7" />
      <input name="interval2" type="float" interfacename="interval8" />
      <input name="color1" type="color4" interfacename="color7" />
      <input name="color2" type="color4" interfacename="color8" />
      <input name="x" type="float" nodename="ramp_type_selector" />
    </ramp_gradient>
    <ramp_gradient name="ramp_gradient8" type="color4" nodedef="ND_ramp_gradient">
      <input name="interpolation" type="integer" interfacename="interpolation" />
      <input name="prev_color" type="color4" nodename="ramp_gradient7" />
      <input name="interval_num" type="integer" value="8" />
      <input name="num_intervals" type="integer" interfacename="num_intervals" />
      <input name="interval1" type="float" interfacename="interval8" />
      <input name="interval2" type="float" interfacename="interval9" />
      <input name="color1" type="color4" interfacename="color8" />
      <input name="color2" type="color4" interfacename="color9" />
      <input name="x" type="float" nodename="ramp_type_selector" />
    </ramp_gradient>
    <ramp_gradient name="ramp_gradient9" type="color4" nodedef="ND_ramp_gradient">
      <input name="interpolation" type="integer" interfacename="interpolation" />
      <input name="prev_color" type="color4" nodename="ramp_gradient8" />
      <input name="interval_num" type="integer" value="9" />
      <input name="num_intervals" type="integer" interfacename="num_intervals" />
      <input name="interval1" type="float" interfacename="interval9" />
      <input name="interval2" type="float" interfacename="interval10" />
      <input name="color1" type="color4" interfacename="color9" />
      <input name="color2" type="color4" interfacename="color10" />
      <input name="x" type="float" nodename="ramp_type_selector" />
    </ramp_gradient>
    <switch name="ramp_type_selector" type="float" nodedef="ND_switch_floatI">
      <input name="which" type="integer" interfacename="type" />
      <input name="in1" type="float" nodename="separate1" output="outx" />
      <input name="in2" type="float" nodename="radial_add" />
      <input name="in3" type="float" nodename="circular_magnitude" />
      <input name="in4" type="float" nodename="box_ifgreater" />
    </switch>
    <multiply name="box_multiply" type="vector2" nodedef="ND_multiply_vector2FA">
      <input name="in1" type="vector2" nodename="box_absval" />
      <input name="in2" type="float" value="2" />
    </multiply>
    <ifgreater name="box_ifgreater" type="float" nodedef="ND_ifgreater_float">
      <input name="value1" type="float" nodename="separate6" output="outx" />
      <input name="value2" type="float" nodename="separate6" output="outy" />
      <input name="in1" type="float" nodename="separate7" output="outx" />
      <input name="in2" type="float" nodename="separate7" output="outy" />
    </ifgreater>
    <absval name="box_absval" type="vector2" nodedef="ND_absval_vector2">
      <input name="in" type="vector2" nodename="subtract_half" />
    </absval>
    <separate2 name="separate7" type="multioutput" nodedef="ND_separate2_vector2">
      <input name="in" type="vector2" nodename="box_multiply" />
    </separate2>
    <separate2 name="separate6" type="multioutput" nodedef="ND_separate2_vector2">
      <input name="in" type="vector2" nodename="box_absval" />
    </separate2>
    <atan2 name="radial_atan2" type="float" nodedef="ND_atan2_float">
      <input name="inx" type="float" nodename="separate9" output="outy" />
      <input name="iny" type="float" nodename="separate9" output="outx" />
    </atan2>
    <divide name="radial_divide" type="float" nodedef="ND_divide_float">
      <input name="in1" type="float" nodename="radial_atan2" />
      <input name="in2" type="float" value="6.28319" />
    </divide>
    <add name="radial_add" type="float" nodedef="ND_add_float">
      <input name="in1" type="float" nodename="radial_divide" />
      <input name="in2" type="float" value="0.5" />
    </add>
    <multiply name="circular_multiply" type="vector2" nodedef="ND_multiply_vector2FA">
      <input name="in1" type="vector2" nodename="subtract_half" />
      <input name="in2" type="float" value="1.414" />
    </multiply>
    <magnitude name="circular_magnitude" type="float" nodedef="ND_magnitude_vector2">
      <input name="in" type="vector2" nodename="circular_multiply" />
    </magnitude>
    <subtract name="subtract_half" type="vector2" nodedef="ND_subtract_vector2FA">
      <input name="in1" type="vector2" interfacename="texcoord" />
      <input name="in2" type="float" value="0.5" />
    </subtract>
    <separate2 name="separate9" type="multioutput" nodedef="ND_separate2_vector2">
      <input name="in" type="vector2" nodename="subtract_half" />
    </separate2>
    <output name="out" type="color4" nodename="ramp_gradient9" />
  </nodegraph>

  <!--
    Node: <ramp4>
    A 4-corner bilinear value ramp.
  -->
  <nodegraph name="NG_ramp4_float" nodedef="ND_ramp4_float">
    <clamp name="N_txclamp_float" type="vector2">
      <input name="in" type="vector2" interfacename="texcoord" />
    </clamp>
    <extract name="N_s_float" type="float">
      <input name="in" type="vector2" nodename="N_txclamp_float" />
      <input name="index" type="integer" value="0" />
    </extract>
    <extract name="N_t_float" type="float">
      <input name="in" type="vector2" nodename="N_txclamp_float" />
      <input name="index" type="integer" value="1" />
    </extract>
    <mix name="N_mixtop_float" type="float">
      <input name="bg" type="float" interfacename="valuetl" />
      <input name="fg" type="float" interfacename="valuetr" />
      <input name="mix" type="float" nodename="N_s_float" />
    </mix>
    <mix name="N_mixbot_float" type="float">
      <input name="bg" type="float" interfacename="valuebl" />
      <input name="fg" type="float" interfacename="valuebr" />
      <input name="mix" type="float" nodename="N_s_float" />
    </mix>
    <mix name="N_mix_float" type="float">
      <input name="bg" type="float" nodename="N_mixtop_float" />
      <input name="fg" type="float" nodename="N_mixbot_float" />
      <input name="mix" type="float" nodename="N_t_float" />
    </mix>
    <output name="out" type="float" nodename="N_mix_float" />
  </nodegraph>
  <nodegraph name="NG_ramp4_color3" nodedef="ND_ramp4_color3">
    <clamp name="N_txclamp_color3" type="vector2">
      <input name="in" type="vector2" interfacename="texcoord" />
    </clamp>
    <extract name="N_s_color3" type="float">
      <input name="in" type="vector2" nodename="N_txclamp_color3" />
      <input name="index" type="integer" value="0" />
    </extract>
    <extract name="N_t_color3" type="float">
      <input name="in" type="vector2" nodename="N_txclamp_color3" />
      <input name="index" type="integer" value="1" />
    </extract>
    <mix name="N_mixtop_color3" type="color3">
      <input name="bg" type="color3" interfacename="valuetl" />
      <input name="fg" type="color3" interfacename="valuetr" />
      <input name="mix" type="float" nodename="N_s_color3" />
    </mix>
    <mix name="N_mixbot_color3" type="color3">
      <input name="bg" type="color3" interfacename="valuebl" />
      <input name="fg" type="color3" interfacename="valuebr" />
      <input name="mix" type="float" nodename="N_s_color3" />
    </mix>
    <mix name="N_mix_color3" type="color3">
      <input name="bg" type="color3" nodename="N_mixtop_color3" />
      <input name="fg" type="color3" nodename="N_mixbot_color3" />
      <input name="mix" type="float" nodename="N_t_color3" />
    </mix>
    <output name="out" type="color3" nodename="N_mix_color3" />
  </nodegraph>
  <nodegraph name="NG_ramp4_color4" nodedef="ND_ramp4_color4">
    <clamp name="N_txclamp_color4" type="vector2">
      <input name="in" type="vector2" interfacename="texcoord" />
    </clamp>
    <extract name="N_s_color4" type="float">
      <input name="in" type="vector2" nodename="N_txclamp_color4" />
      <input name="index" type="integer" value="0" />
    </extract>
    <extract name="N_t_color4" type="float">
      <input name="in" type="vector2" nodename="N_txclamp_color4" />
      <input name="index" type="integer" value="1" />
    </extract>
    <mix name="N_mixtop_color4" type="color4">
      <input name="bg" type="color4" interfacename="valuetl" />
      <input name="fg" type="color4" interfacename="valuetr" />
      <input name="mix" type="float" nodename="N_s_color4" />
    </mix>
    <mix name="N_mixbot_color4" type="color4">
      <input name="bg" type="color4" interfacename="valuebl" />
      <input name="fg" type="color4" interfacename="valuebr" />
      <input name="mix" type="float" nodename="N_s_color4" />
    </mix>
    <mix name="N_mix_color4" type="color4">
      <input name="bg" type="color4" nodename="N_mixtop_color4" />
      <input name="fg" type="color4" nodename="N_mixbot_color4" />
      <input name="mix" type="float" nodename="N_t_color4" />
    </mix>
    <output name="out" type="color4" nodename="N_mix_color4" />
  </nodegraph>
  <nodegraph name="NG_ramp4_vector2" nodedef="ND_ramp4_vector2">
    <clamp name="N_txclamp_vector2" type="vector2">
      <input name="in" type="vector2" interfacename="texcoord" />
    </clamp>
    <extract name="N_s_vector2" type="float">
      <input name="in" type="vector2" nodename="N_txclamp_vector2" />
      <input name="index" type="integer" value="0" />
    </extract>
    <extract name="N_t_vector2" type="float">
      <input name="in" type="vector2" nodename="N_txclamp_vector2" />
      <input name="index" type="integer" value="1" />
    </extract>
    <mix name="N_mixtop_vector2" type="vector2">
      <input name="bg" type="vector2" interfacename="valuetl" />
      <input name="fg" type="vector2" interfacename="valuetr" />
      <input name="mix" type="float" nodename="N_s_vector2" />
    </mix>
    <mix name="N_mixbot_vector2" type="vector2">
      <input name="bg" type="vector2" interfacename="valuebl" />
      <input name="fg" type="vector2" interfacename="valuebr" />
      <input name="mix" type="float" nodename="N_s_vector2" />
    </mix>
    <mix name="N_mix_vector2" type="vector2">
      <input name="bg" type="vector2" nodename="N_mixtop_vector2" />
      <input name="fg" type="vector2" nodename="N_mixbot_vector2" />
      <input name="mix" type="float" nodename="N_t_vector2" />
    </mix>
    <output name="out" type="vector2" nodename="N_mix_vector2" />
  </nodegraph>
  <nodegraph name="NG_ramp4_vector3" nodedef="ND_ramp4_vector3">
    <clamp name="N_txclamp_vector3" type="vector2">
      <input name="in" type="vector2" interfacename="texcoord" />
    </clamp>
    <extract name="N_s_vector3" type="float">
      <input name="in" type="vector2" nodename="N_txclamp_vector3" />
      <input name="index" type="integer" value="0" />
    </extract>
    <extract name="N_t_vector3" type="float">
      <input name="in" type="vector2" nodename="N_txclamp_vector3" />
      <input name="index" type="integer" value="1" />
    </extract>
    <mix name="N_mixtop_vector3" type="vector3">
      <input name="bg" type="vector3" interfacename="valuetl" />
      <input name="fg" type="vector3" interfacename="valuetr" />
      <input name="mix" type="float" nodename="N_s_vector3" />
    </mix>
    <mix name="N_mixbot_vector3" type="vector3">
      <input name="bg" type="vector3" interfacename="valuebl" />
      <input name="fg" type="vector3" interfacename="valuebr" />
      <input name="mix" type="float" nodename="N_s_vector3" />
    </mix>
    <mix name="N_mix_vector3" type="vector3">
      <input name="bg" type="vector3" nodename="N_mixtop_vector3" />
      <input name="fg" type="vector3" nodename="N_mixbot_vector3" />
      <input name="mix" type="float" nodename="N_t_vector3" />
    </mix>
    <output name="out" type="vector3" nodename="N_mix_vector3" />
  </nodegraph>
  <nodegraph name="NG_ramp4_vector4" nodedef="ND_ramp4_vector4">
    <clamp name="N_txclamp_vector4" type="vector2">
      <input name="in" type="vector2" interfacename="texcoord" />
    </clamp>
    <extract name="N_s_vector4" type="float">
      <input name="in" type="vector2" nodename="N_txclamp_vector4" />
      <input name="index" type="integer" value="0" />
    </extract>
    <extract name="N_t_vector4" type="float">
      <input name="in" type="vector2" nodename="N_txclamp_vector4" />
      <input name="index" type="integer" value="1" />
    </extract>
    <mix name="N_mixtop_vector4" type="vector4">
      <input name="bg" type="vector4" interfacename="valuetl" />
      <input name="fg" type="vector4" interfacename="valuetr" />
      <input name="mix" type="float" nodename="N_s_vector4" />
    </mix>
    <mix name="N_mixbot_vector4" type="vector4">
      <input name="bg" type="vector4" interfacename="valuebl" />
      <input name="fg" type="vector4" interfacename="valuebr" />
      <input name="mix" type="float" nodename="N_s_vector4" />
    </mix>
    <mix name="N_mix_vector4" type="vector4">
      <input name="bg" type="vector4" nodename="N_mixtop_vector4" />
      <input name="fg" type="vector4" nodename="N_mixbot_vector4" />
      <input name="mix" type="float" nodename="N_t_vector4" />
    </mix>
    <output name="out" type="vector4" nodename="N_mix_vector4" />
  </nodegraph>

  <!--
    Node <ramp_gradient>
    A helper node that handles a single control point within a <ramp>.
  -->
  <nodegraph name="NG_ramp_gradient" nodedef="ND_ramp_gradient">
    <clamp name="linear_clamp" type="float" nodedef="ND_clamp_float">
      <input name="in" type="float" interfacename="x" />
      <input name="low" type="float" interfacename="interval1" />
      <input name="high" type="float" interfacename="interval2" />
    </clamp>
    <remap name="linear_remap" type="float" nodedef="ND_remap_float">
      <input name="in" type="float" nodename="linear_clamp" />
      <input name="inlow" type="float" interfacename="interval1" />
      <input name="inhigh" type="float" interfacename="interval2" />
      <input name="outlow" type="float" value="0" />
      <input name="outhigh" type="float" value="1" />
    </remap>
    <smoothstep name="smoothstep" type="float" nodedef="ND_smoothstep_float">
      <input name="in" type="float" interfacename="x" />
      <input name="low" type="float" interfacename="interval1" />
      <input name="high" type="float" interfacename="interval2" />
    </smoothstep>
    <switch name="interpolation_selector" type="float" nodedef="ND_switch_floatI">
      <input name="which" type="integer" interfacename="interpolation" />
      <input name="in1" type="float" nodename="linear_remap" />
      <input name="in2" type="float" nodename="smoothstep" />
    </switch>
    <mix name="mix3" type="color4" nodedef="ND_mix_color4">
      <input name="fg" type="color4" interfacename="color2" />
      <input name="bg" type="color4" interfacename="color1" />
      <input name="mix" type="float" nodename="interpolation_selector" />
    </mix>
    <ifgreater name="step_ifgreater" type="color4" nodedef="ND_ifgreater_color4">
      <input name="value2" type="float" interfacename="x" />
      <input name="in1" type="color4" interfacename="color1" />
      <input name="in2" type="color4" interfacename="color2" />
      <input name="value1" type="float" interfacename="interval2" />
    </ifgreater>
    <switch name="interpolation_selector2" type="color4" nodedef="ND_switch_color4I">
      <input name="in1" type="color4" nodename="mix3" />
      <input name="in2" type="color4" nodename="mix3" />
      <input name="which" type="integer" interfacename="interpolation" />
      <input name="in3" type="color4" nodename="step_ifgreater" />
    </switch>
    <ifgreater name="ifgreater3" type="color4" nodedef="ND_ifgreater_color4">
      <input name="value1" type="float" interfacename="x" />
      <input name="value2" type="float" interfacename="interval1" />
      <input name="in1" type="color4" nodename="interpolation_selector2" />
      <input name="in2" type="color4" interfacename="prev_color" />
    </ifgreater>
    <ifgreater name="ifgreatereq1" type="color4" nodedef="ND_ifgreatereq_color4I">
      <input name="value1" type="integer" interfacename="interval_num" />
      <input name="value2" type="integer" interfacename="num_intervals" />
      <input name="in1" type="color4" interfacename="prev_color" />
      <input name="in2" type="color4" nodename="ifgreater3" />
    </ifgreater>
    <output name="out" type="color4" nodename="ifgreatereq1" />
  </nodegraph>

  <!--
    Node: <noise2d>
  -->
  <nodegraph name="NG_noise2d_color3" nodedef="ND_noise2d_color3">
    <noise2d name="N_noise2d" type="vector3">
      <input name="amplitude" type="vector3" interfacename="amplitude" />
      <input name="pivot" type="float" interfacename="pivot" />
      <input name="texcoord" type="vector2" interfacename="texcoord" />
    </noise2d>
    <convert name="N_convert" type="color3">
      <input name="in" type="vector3" nodename="N_noise2d" />
    </convert>
    <output name="out" type="color3" nodename="N_convert" />
  </nodegraph>
  <nodegraph name="NG_noise2d_color4" nodedef="ND_noise2d_color4">
    <noise2d name="N_noise2d" type="vector4">
      <input name="amplitude" type="vector4" interfacename="amplitude" />
      <input name="pivot" type="float" interfacename="pivot" />
      <input name="texcoord" type="vector2" interfacename="texcoord" />
    </noise2d>
    <convert name="N_convert" type="color4">
      <input name="in" type="vector4" nodename="N_noise2d" />
    </convert>
    <output name="out" type="color4" nodename="N_convert" />
  </nodegraph>
  <nodegraph name="NG_noise2d_color3FA" nodedef="ND_noise2d_color3FA">
    <convert name="N_convert" type="vector3">
      <input name="in" type="float" interfacename="amplitude" />
    </convert>
    <noise2d name="N_noise2d" type="color3">
      <input name="amplitude" type="vector3" nodename="N_convert" />
      <input name="pivot" type="float" interfacename="pivot" />
      <input name="texcoord" type="vector2" interfacename="texcoord" />
    </noise2d>
    <output name="out" type="color3" nodename="N_noise2d" />
  </nodegraph>
  <nodegraph name="NG_noise2d_color4FA" nodedef="ND_noise2d_color4FA">
    <convert name="N_convert" type="vector4">
      <input name="in" type="float" interfacename="amplitude" />
    </convert>
    <noise2d name="N_noise2d" type="color4">
      <input name="amplitude" type="vector4" nodename="N_convert" />
      <input name="pivot" type="float" interfacename="pivot" />
      <input name="texcoord" type="vector2" interfacename="texcoord" />
    </noise2d>
    <output name="out" type="color4" nodename="N_noise2d" />
  </nodegraph>
  <nodegraph name="NG_noise2d_vector2FA" nodedef="ND_noise2d_vector2FA">
    <convert name="N_convert" type="vector2">
      <input name="in" type="float" interfacename="amplitude" />
    </convert>
    <noise2d name="N_noise2d" type="vector2">
      <input name="amplitude" type="vector2" nodename="N_convert" />
      <input name="pivot" type="float" interfacename="pivot" />
      <input name="texcoord" type="vector2" interfacename="texcoord" />
    </noise2d>
    <output name="out" type="vector2" nodename="N_noise2d" />
  </nodegraph>
  <nodegraph name="NG_noise2d_vector3FA" nodedef="ND_noise2d_vector3FA">
    <convert name="N_convert" type="vector3">
      <input name="in" type="float" interfacename="amplitude" />
    </convert>
    <noise2d name="N_noise2d" type="vector3">
      <input name="amplitude" type="vector3" nodename="N_convert" />
      <input name="pivot" type="float" interfacename="pivot" />
      <input name="texcoord" type="vector2" interfacename="texcoord" />
    </noise2d>
    <output name="out" type="vector3" nodename="N_noise2d" />
  </nodegraph>
  <nodegraph name="NG_noise2d_vector4FA" nodedef="ND_noise2d_vector4FA">
    <convert name="N_convert" type="vector4">
      <input name="in" type="float" interfacename="amplitude" />
    </convert>
    <noise2d name="N_noise2d" type="vector4">
      <input name="amplitude" type="vector4" nodename="N_convert" />
      <input name="pivot" type="float" interfacename="pivot" />
      <input name="texcoord" type="vector2" interfacename="texcoord" />
    </noise2d>
    <output name="out" type="vector4" nodename="N_noise2d" />
  </nodegraph>

  <!--
    Node: <noise3d>
  -->
  <nodegraph name="NG_noise3d_color3" nodedef="ND_noise3d_color3">
    <noise3d name="N_noise3d" type="vector3">
      <input name="amplitude" type="vector3" interfacename="amplitude" />
      <input name="pivot" type="float" interfacename="pivot" />
      <input name="position" type="vector3" interfacename="position" />
    </noise3d>
    <convert name="N_convert" type="color3">
      <input name="in" type="vector3" nodename="N_noise3d" />
    </convert>
    <output name="out" type="color3" nodename="N_convert" />
  </nodegraph>
  <nodegraph name="NG_noise3d_color4" nodedef="ND_noise3d_color4">
    <noise3d name="N_noise3d" type="vector4">
      <input name="amplitude" type="vector4" interfacename="amplitude" />
      <input name="pivot" type="float" interfacename="pivot" />
      <input name="position" type="vector3" interfacename="position" />
    </noise3d>
    <convert name="N_convert" type="color4">
      <input name="in" type="vector4" nodename="N_noise3d" />
    </convert>
    <output name="out" type="color4" nodename="N_convert" />
  </nodegraph>
  <nodegraph name="NG_noise3d_color3FA" nodedef="ND_noise3d_color3FA">
    <convert name="N_convert" type="vector3">
      <input name="in" type="float" interfacename="amplitude" />
    </convert>
    <noise3d name="N_noise3d" type="color3">
      <input name="amplitude" type="vector3" nodename="N_convert" />
      <input name="pivot" type="float" interfacename="pivot" />
      <input name="position" type="vector3" interfacename="position" />
    </noise3d>
    <output name="out" type="color3" nodename="N_noise3d" />
  </nodegraph>
  <nodegraph name="NG_noise3d_color4FA" nodedef="ND_noise3d_color4FA">
    <convert name="N_convert" type="vector4">
      <input name="in" type="float" interfacename="amplitude" />
    </convert>
    <noise3d name="N_noise3d" type="color4">
      <input name="amplitude" type="vector4" nodename="N_convert" />
      <input name="pivot" type="float" interfacename="pivot" />
      <input name="position" type="vector3" interfacename="position" />
    </noise3d>
    <output name="out" type="color4" nodename="N_noise3d" />
  </nodegraph>
  <nodegraph name="NG_noise3d_vector2FA" nodedef="ND_noise3d_vector2FA">
    <convert name="N_convert" type="vector2">
      <input name="in" type="float" interfacename="amplitude" />
    </convert>
    <noise3d name="N_noise3d" type="vector2">
      <input name="amplitude" type="vector2" nodename="N_convert" />
      <input name="pivot" type="float" interfacename="pivot" />
      <input name="position" type="vector3" interfacename="position" />
    </noise3d>
    <output name="out" type="vector2" nodename="N_noise3d" />
  </nodegraph>
  <nodegraph name="NG_noise3d_vector3FA" nodedef="ND_noise3d_vector3FA">
    <convert name="N_convert" type="vector3">
      <input name="in" type="float" interfacename="amplitude" />
    </convert>
    <noise3d name="N_noise3d" type="vector3">
      <input name="amplitude" type="vector3" nodename="N_convert" />
      <input name="pivot" type="float" interfacename="pivot" />
      <input name="position" type="vector3" interfacename="position" />
    </noise3d>
    <output name="out" type="vector3" nodename="N_noise3d" />
  </nodegraph>
  <nodegraph name="NG_noise3d_vector4FA" nodedef="ND_noise3d_vector4FA">
    <convert name="N_convert" type="vector4">
      <input name="in" type="float" interfacename="amplitude" />
    </convert>
    <noise3d name="N_noise3d" type="vector4">
      <input name="amplitude" type="vector4" nodename="N_convert" />
      <input name="pivot" type="float" interfacename="pivot" />
      <input name="position" type="vector3" interfacename="position" />
    </noise3d>
    <output name="out" type="vector4" nodename="N_noise3d" />
  </nodegraph>

  <!--
    Node: <fractal3d>
  -->
  <nodegraph name="NG_fractal3d_color3" nodedef="ND_fractal3d_color3">
    <fractal3d name="N_fractal3d" type="vector3">
      <input name="amplitude" type="vector3" interfacename="amplitude" />
      <input name="octaves" type="integer" interfacename="octaves" />
      <input name="lacunarity" type="float" interfacename="lacunarity" />
      <input name="diminish" type="float" interfacename="diminish" />
      <input name="position" type="vector3" interfacename="position" />
    </fractal3d>
    <convert name="N_convert" type="color3">
      <input name="in" type="vector3" nodename="N_fractal3d" />
    </convert>
    <output name="out" type="color3" nodename="N_convert" />
  </nodegraph>
  <nodegraph name="NG_fractal3d_color4" nodedef="ND_fractal3d_color4">
    <fractal3d name="N_fractal3d" type="vector4">
      <input name="amplitude" type="vector4" interfacename="amplitude" />
      <input name="octaves" type="integer" interfacename="octaves" />
      <input name="lacunarity" type="float" interfacename="lacunarity" />
      <input name="diminish" type="float" interfacename="diminish" />
      <input name="position" type="vector3" interfacename="position" />
    </fractal3d>
    <convert name="N_convert" type="color4">
      <input name="in" type="vector4" nodename="N_fractal3d" />
    </convert>
    <output name="out" type="color4" nodename="N_convert" />
  </nodegraph>
  <nodegraph name="NG_fractal3d_color3FA" nodedef="ND_fractal3d_color3FA">
    <convert name="N_convert" type="vector3">
      <input name="in" type="float" interfacename="amplitude" />
    </convert>
    <fractal3d name="N_fractal3d" type="color3">
      <input name="amplitude" type="vector3" nodename="N_convert" />
      <input name="octaves" type="integer" interfacename="octaves" />
      <input name="lacunarity" type="float" interfacename="lacunarity" />
      <input name="diminish" type="float" interfacename="diminish" />
      <input name="position" type="vector3" interfacename="position" />
    </fractal3d>
    <output name="out" type="color3" nodename="N_fractal3d" />
  </nodegraph>
  <nodegraph name="NG_fractal3d_color4FA" nodedef="ND_fractal3d_color4FA">
    <convert name="N_convert" type="vector4">
      <input name="in" type="float" interfacename="amplitude" />
    </convert>
    <fractal3d name="N_fractal3d" type="color4">
      <input name="amplitude" type="vector4" nodename="N_convert" />
      <input name="octaves" type="integer" interfacename="octaves" />
      <input name="lacunarity" type="float" interfacename="lacunarity" />
      <input name="diminish" type="float" interfacename="diminish" />
      <input name="position" type="vector3" interfacename="position" />
    </fractal3d>
    <output name="out" type="color4" nodename="N_fractal3d" />
  </nodegraph>
  <nodegraph name="NG_fractal3d_vector2FA" nodedef="ND_fractal3d_vector2FA">
    <convert name="N_convert" type="vector2">
      <input name="in" type="float" interfacename="amplitude" />
    </convert>
    <fractal3d name="N_fractal3d" type="vector2">
      <input name="amplitude" type="vector2" nodename="N_convert" />
      <input name="octaves" type="integer" interfacename="octaves" />
      <input name="lacunarity" type="float" interfacename="lacunarity" />
      <input name="diminish" type="float" interfacename="diminish" />
      <input name="position" type="vector3" interfacename="position" />
    </fractal3d>
    <output name="out" type="vector2" nodename="N_fractal3d" />
  </nodegraph>
  <nodegraph name="NG_fractal3d_vector3FA" nodedef="ND_fractal3d_vector3FA">
    <convert name="N_convert" type="vector3">
      <input name="in" type="float" interfacename="amplitude" />
    </convert>
    <fractal3d name="N_fractal3d" type="vector3">
      <input name="amplitude" type="vector3" nodename="N_convert" />
      <input name="octaves" type="integer" interfacename="octaves" />
      <input name="lacunarity" type="float" interfacename="lacunarity" />
      <input name="diminish" type="float" interfacename="diminish" />
      <input name="position" type="vector3" interfacename="position" />
    </fractal3d>
    <output name="out" type="vector3" nodename="N_fractal3d" />
  </nodegraph>
  <nodegraph name="NG_fractal3d_vector4FA" nodedef="ND_fractal3d_vector4FA">
    <convert name="N_convert" type="vector4">
      <input name="in" type="float" interfacename="amplitude" />
    </convert>
    <fractal3d name="N_fractal3d" type="vector4">
      <input name="amplitude" type="vector4" nodename="N_convert" />
      <input name="octaves" type="integer" interfacename="octaves" />
      <input name="lacunarity" type="float" interfacename="lacunarity" />
      <input name="diminish" type="float" interfacename="diminish" />
      <input name="position" type="vector3" interfacename="position" />
    </fractal3d>
    <output name="out" type="vector4" nodename="N_fractal3d" />
  </nodegraph>

  <!--
    <unifiednoise2d>
    Combined 2d noises for artists.
  -->
  <nodegraph name="NG_unifiednoise2d_float" nodedef="ND_unifiednoise2d_float">
    <range name="N_range" type="float">
      <input name="in" type="float" nodename="N_switch_type" />
      <input name="outlow" type="float" interfacename="outmin" />
      <input name="outhigh" type="float" interfacename="outmax" />
      <input name="doclamp" type="boolean" interfacename="clampoutput" />
      <input name="inlow" type="float" value="0" />
      <input name="inhigh" type="float" value="1" />
    </range>
    <switch name="N_switch_type" type="float">
      <input name="in1" type="float" nodename="N_perlin_noise2d" />
      <input name="in2" type="float" nodename="N_cellnoise2d" />
      <input name="in3" type="float" nodename="N_worleynoise2d" />
      <input name="in4" type="float" nodename="N_fractal3d" />
      <input name="which" type="integer" interfacename="type" />
    </switch>
    <noise2d name="N_perlin_noise2d" type="float">
      <input name="texcoord" type="vector2" nodename="N_apply_cell_jitter" />
      <input name="amplitude" type="float" value="0.5" />
      <input name="pivot" type="float" value="0.5" />
    </noise2d>
    <cellnoise2d name="N_cellnoise2d" type="float">
      <input name="texcoord" type="vector2" nodename="N_apply_cell_jitter" />
    </cellnoise2d>
    <worleynoise2d name="N_worleynoise2d" type="float">
      <input name="texcoord" type="vector2" nodename="N_apply_offset" />
      <input name="jitter" type="float" interfacename="jitter" />
      <input name="style" type="integer" interfacename="style" />
    </worleynoise2d>
    <fractal3d name="N_fractal3d" type="float">
      <input name="octaves" type="integer" interfacename="octaves" />
      <input name="lacunarity" type="float" interfacename="lacunarity" />
      <input name="diminish" type="float" interfacename="diminish" />
      <input name="position" type="vector3" nodename="N_combine_with_jitter" />
      <input name="amplitude" type="float" value="1" />
    </fractal3d>
    <rotate2d name="N_apply_cell_jitter" type="vector2">
      <input name="in" type="vector2" nodename="N_apply_offset" />
      <input name="amount" type="float" nodename="N_cell_jitter_mult" />
    </rotate2d>
    <add name="N_apply_offset" type="vector2">
      <input name="in1" type="vector2" nodename="N_apply_freq" />
      <input name="in2" type="vector2" interfacename="offset" />
    </add>
    <combine3 name="N_combine_with_jitter" type="vector3">
      <input name="in1" type="float" nodename="N_separate" output="outx" />
      <input name="in2" type="float" nodename="N_separate" output="outy" />
      <input name="in3" type="float" nodename="N_cell_jitter_mult" />
    </combine3>
    <multiply name="N_cell_jitter_mult" type="float">
      <input name="in1" type="float" nodename="N_jitter_minus_1" />
      <input name="in2" type="float" value="90000" />
    </multiply>
    <multiply name="N_apply_freq" type="vector2">
      <input name="in1" type="vector2" interfacename="texcoord" />
      <input name="in2" type="vector2" interfacename="freq" />
    </multiply>
    <separate2 name="N_separate" type="multioutput">
      <input name="in" type="vector2" nodename="N_apply_offset" />
    </separate2>
    <subtract name="N_jitter_minus_1" type="float">
      <input name="in1" type="float" interfacename="jitter" />
      <input name="in2" type="float" value="1" />
    </subtract>
    <output name="out" type="float" nodename="N_range" />
  </nodegraph>

  <!--
    Node: <unifiednoise3d>
    Combined 3d noises for artists.
  -->
  <nodegraph name="NG_unifiednoise3d_float" nodedef="ND_unifiednoise3d_float">
    <range name="N_range" type="float">
      <input name="in" type="float" nodename="N_switch_type" />
      <input name="outlow" type="float" interfacename="outmin" />
      <input name="outhigh" type="float" interfacename="outmax" />
      <input name="doclamp" type="boolean" interfacename="clampoutput" />
      <input name="inlow" type="float" value="0" />
      <input name="inhigh" type="float" value="1" />
    </range>
    <switch name="N_switch_type" type="float">
      <input name="in1" type="float" nodename="N_perlin_noise3d" />
      <input name="in2" type="float" nodename="N_cellnoise3d" />
      <input name="in3" type="float" nodename="N_worleynoise3d" />
      <input name="in4" type="float" nodename="N_fractal3d" />
      <input name="which" type="integer" interfacename="type" />
    </switch>
    <noise3d name="N_perlin_noise3d" type="float">
      <input name="position" type="vector3" nodename="N_apply_cell_jitter" />
      <input name="amplitude" type="float" value="0.5" />
      <input name="pivot" type="float" value="0.5" />
    </noise3d>
    <cellnoise3d name="N_cellnoise3d" type="float">
      <input name="position" type="vector3" nodename="N_apply_cell_jitter" />
    </cellnoise3d>
    <worleynoise3d name="N_worleynoise3d" type="float">
      <input name="position" type="vector3" nodename="N_apply_offset" />
      <input name="jitter" type="float" interfacename="jitter" />
      <input name="style" type="integer" interfacename="style" />
    </worleynoise3d>
    <fractal3d name="N_fractal3d" type="float">
      <input name="octaves" type="integer" interfacename="octaves" />
      <input name="lacunarity" type="float" interfacename="lacunarity" />
      <input name="diminish" type="float" interfacename="diminish" />
      <input name="position" type="vector3" nodename="N_apply_cell_jitter" />
      <input name="amplitude" type="float" value="1" />
    </fractal3d>
    <rotate3d name="N_apply_cell_jitter" type="vector3">
      <input name="in" type="vector3" nodename="N_apply_offset" />
      <input name="amount" type="float" nodename="N_cell_jitter_mult" />
      <input name="axis" type="vector3" value="0.1, 1, 0" />
    </rotate3d>
    <add name="N_apply_offset" type="vector3">
      <input name="in1" type="vector3" nodename="N_apply_freq" />
      <input name="in2" type="vector3" interfacename="offset" />
    </add>
    <multiply name="N_cell_jitter_mult" type="float">
      <input name="in1" type="float" nodename="N_jitter_minus_one" />
      <input name="in2" type="float" value="90000" />
    </multiply>
    <multiply name="N_apply_freq" type="vector3">
      <input name="in1" type="vector3" interfacename="position" />
      <input name="in2" type="vector3" interfacename="freq" />
    </multiply>
    <subtract name="N_jitter_minus_one" type="float">
      <input name="in1" type="float" interfacename="jitter" />
      <input name="in2" type="float" value="1" />
    </subtract>
    <output name="out" type="float" nodename="N_range" />
  </nodegraph>

  <!--
    Node: <randomfloat>
    Produces a randomized float, based on an 'input' signal and 'seed' value.
  -->
  <nodegraph name="NG_randomfloat_float" nodedef="ND_randomfloat_float">
    <convert name="N_convertSeed1" type="float">
      <input name="in" type="integer" interfacename="seed" />
    </convert>
    <multiply name="N_scaleInput" type="float">
      <input name="in1" type="float" interfacename="in" />
      <input name="in2" type="float" value="4096" />
    </multiply>
    <combine2 name="N_combine2" type="vector2">
      <input name="in1" type="float" nodename="N_scaleInput" />
      <input name="in2" type="float" nodename="N_convertSeed1" />
    </combine2>
    <cellnoise2d name="N_cellnoise1" type="float">
      <input name="texcoord" type="vector2" nodename="N_combine2" />
    </cellnoise2d>
    <range name="N_remapRange" type="float">
      <input name="in" type="float" nodename="N_cellnoise1" />
      <input name="outlow" type="float" interfacename="min" />
      <input name="outhigh" type="float" interfacename="max" />
      <input name="doclamp" type="boolean" value="true" />
    </range>
    <output name="out" type="float" nodename="N_remapRange" />
  </nodegraph>
  <nodegraph name="NG_randomfloat_integer" nodedef="ND_randomfloat_integer">
    <convert name="N_convertInput1" type="float">
      <input name="in" type="integer" interfacename="in" />
    </convert>
    <convert name="N_convertSeed1" type="float">
      <input name="in" type="integer" interfacename="seed" />
    </convert>
    <combine2 name="N_combine2" type="vector2">
      <input name="in1" type="float" nodename="N_convertInput1" />
      <input name="in2" type="float" nodename="N_convertSeed1" />
    </combine2>
    <cellnoise2d name="N_cellnoise1" type="float">
      <input name="texcoord" type="vector2" nodename="N_combine2" />
    </cellnoise2d>
    <range name="N_remapRange" type="float">
      <input name="in" type="float" nodename="N_cellnoise1" />
      <input name="outlow" type="float" interfacename="min" />
      <input name="outhigh" type="float" interfacename="max" />
      <input name="doclamp" type="boolean" value="true" />
    </range>
    <output name="out" type="float" nodename="N_remapRange" />
  </nodegraph>

  <!--
    Node: <randomcolor>
    Produces a randomized color, based on an input signal and seed value.
  -->
  <nodegraph name="NG_randomcolor_float" nodedef="ND_randomcolor_float">
    <convert name="N_convertSeed1" type="float">
      <input name="in" type="integer" interfacename="seed" />
    </convert>
    <add name="N_offset_hue" type="float">
      <input name="in1" type="float" nodename="N_convertSeed1" />
      <input name="in2" type="float" value="413.3" />
    </add>
    <add name="N_offset_saturation" type="float">
      <input name="in1" type="float" nodename="N_convertSeed1" />
      <input name="in2" type="float" value="1522.4" />
    </add>
    <add name="N_offset_brightness" type="float">
      <input name="in1" type="float" nodename="N_convertSeed1" />
      <input name="in2" type="float" value="1813.8" />
    </add>
    <ceil name="N_seed_hue" type="integer">
      <input name="in" type="float" nodename="N_offset_hue" />
    </ceil>
    <ceil name="N_seed_saturation" type="integer">
      <input name="in" type="float" nodename="N_offset_saturation" />
    </ceil>
    <ceil name="N_seed_brightness" type="integer">
      <input name="in" type="float" nodename="N_offset_brightness" />
    </ceil>
    <randomfloat name="N_rand_hue" type="float">
      <input name="in" type="float" interfacename="in" />
      <input name="seed" type="integer" nodename="N_seed_hue" />
    </randomfloat>
    <randomfloat name="N_rand_saturation" type="float">
      <input name="in" type="float" interfacename="in" />
      <input name="seed" type="integer" nodename="N_seed_saturation" />
    </randomfloat>
    <randomfloat name="N_rand_brightness" type="float">
      <input name="in" type="float" interfacename="in" />
      <input name="seed" type="integer" nodename="N_seed_brightness" />
    </randomfloat>
    <range name="N_range_hue" type="float">
      <input name="in" type="float" nodename="N_rand_hue" />
      <input name="outlow" type="float" interfacename="huelow" />
      <input name="outhigh" type="float" interfacename="huehigh" />
    </range>
    <range name="N_range_saturation" type="float">
      <input name="in" type="float" nodename="N_rand_saturation" />
      <input name="outlow" type="float" interfacename="saturationlow" />
      <input name="outhigh" type="float" interfacename="saturationhigh" />
    </range>
    <range name="N_range_brightness" type="float">
      <input name="in" type="float" nodename="N_rand_brightness" />
      <input name="outlow" type="float" interfacename="brightnesslow" />
      <input name="outhigh" type="float" interfacename="brightnesshigh" />
    </range>
    <combine3 name="N_combine_HSV" type="color3">
      <input name="in1" type="float" nodename="N_range_hue" />
      <input name="in2" type="float" nodename="N_range_saturation" />
      <input name="in3" type="float" nodename="N_range_brightness" />
    </combine3>
    <hsvtorgb name="N_HSV_to_RGB" type="color3">
      <input name="in" type="color3" nodename="N_combine_HSV" />
    </hsvtorgb>
    <output name="out" type="color3" nodename="N_HSV_to_RGB" />
  </nodegraph>
  <nodegraph name="NG_randomcolor_integer" nodedef="ND_randomcolor_integer">
    <convert name="N_convert1" type="float">
      <input name="in" type="integer" interfacename="in" />
    </convert>
    <randomcolor name="N_randomcolor1" type="color3">
      <input name="in" type="float" nodename="N_convert1" />
      <input name="huelow" type="float" interfacename="huelow" />
      <input name="huehigh" type="float" interfacename="huehigh" />
      <input name="saturationlow" type="float" interfacename="saturationlow" />
      <input name="saturationhigh" type="float" interfacename="saturationhigh" />
      <input name="brightnesslow" type="float" interfacename="brightnesslow" />
      <input name="brightnesshigh" type="float" interfacename="brightnesshigh" />
      <input name="seed" type="integer" interfacename="seed" />
    </randomcolor>
    <output name="out" type="color3" nodename="N_randomcolor1" />
  </nodegraph>

  <!--
    Node: <checkerboard>
    A 2D checkerboard pattern.
  -->
  <nodegraph name="NG_checkerboard_color3" nodedef="ND_checkerboard_color3">
    <multiply name="N_mtlxmult" type="vector2">
      <input name="in1" type="vector2" interfacename="texcoord" />
      <input name="in2" type="vector2" interfacename="uvtiling" />
    </multiply>
    <subtract name="N_mtlxsubtract" type="vector2">
      <input name="in1" type="vector2" nodename="N_mtlxmult" />
      <input name="in2" type="vector2" interfacename="uvoffset" />
    </subtract>
    <floor name="N_mtlxfloor" type="vector2">
      <input name="in" type="vector2" nodename="N_mtlxsubtract" />
    </floor>
    <dotproduct name="N_mtlxdotproduct" type="float">
      <input name="in1" type="vector2" nodename="N_mtlxfloor" />
      <input name="in2" type="vector2" value="1, 1" />
    </dotproduct>
    <modulo name="N_modulo" type="float">
      <input name="in1" type="float" nodename="N_mtlxdotproduct" />
      <input name="in2" type="float" value="2" />
    </modulo>
    <mix name="N_mtlxmix" type="color3">
      <input name="bg" type="color3" interfacename="color2" />
      <input name="fg" type="color3" interfacename="color1" />
      <input name="mix" type="float" nodename="N_modulo" />
    </mix>
    <output name="out" type="color3" nodename="N_mtlxmix" />
  </nodegraph>

  <!--
    Node: <line>
    Returns 1 if texcoord is at less than radius distance from a line segment defined by point1 and point2; otherwise returns 0.
    Segment ends will be rounded.
    Uses formulas from Inigo Quilez SDF samples (iquilezles.org)
  -->
  <nodegraph name="NG_line_float" nodedef="ND_line_float">
    <subtract name="delta" type="vector2">
      <input name="in1" type="vector2" interfacename="texcoord" />
      <input name="in2" type="vector2" interfacename="center" />
    </subtract>
    <subtract name="p_a" type="vector2">
      <input name="in1" type="vector2" nodename="delta" />
      <input name="in2" type="vector2" interfacename="point1" />
    </subtract>
    <subtract name="b_a" type="vector2">
      <input name="in1" type="vector2" interfacename="point2" />
      <input name="in2" type="vector2" interfacename="point1" />
    </subtract>
    <dotproduct name="dot_pa_ba" type="float">
      <input name="in1" type="vector2" nodename="p_a" />
      <input name="in2" type="vector2" nodename="b_a" />
    </dotproduct>
    <dotproduct name="dot_ba_ba" type="float">
      <input name="in1" type="vector2" nodename="b_a" />
      <input name="in2" type="vector2" nodename="b_a" />
    </dotproduct>
    <divide name="divide_dots" type="float">
      <input name="in1" type="float" nodename="dot_pa_ba" />
      <input name="in2" type="float" nodename="dot_ba_ba" />
    </divide>
    <clamp name="clamp" type="float">
      <input name="in" type="float" nodename="divide_dots" />
    </clamp>
    <multiply name="multiply_clamp_ba" type="vector2">
      <input name="in1" type="vector2" nodename="b_a" />
      <input name="in2" type="float" nodename="clamp" />
    </multiply>
    <distance name="distance" type="float">
      <input name="in1" type="vector2" nodename="p_a" />
      <input name="in2" type="vector2" nodename="multiply_clamp_ba" />
    </distance>
    <ifgreater name="dist_comp" type="float">
      <input name="value1" type="float" nodename="distance" />
      <input name="value2" type="float" interfacename="radius" />
      <input name="in1" type="float" value="0" />
      <input name="in2" type="float" value="1" />
    </ifgreater>
    <output name="out" type="float" nodename="dist_comp" />
  </nodegraph>

  <!--
    Node: <circle>
    Returns 1 if texcoord is inside a circle defined by center and radius; otherwise returns 0.
  -->
  <nodegraph name="NG_circle_float" nodedef="ND_circle_float">
    <subtract name="delta" type="vector2">
      <input name="in1" type="vector2" interfacename="texcoord" />
      <input name="in2" type="vector2" interfacename="center" />
    </subtract>
    <dotproduct name="dist_square" type="float">
      <input name="in1" type="vector2" nodename="delta" />
      <input name="in2" type="vector2" nodename="delta" />
    </dotproduct>
    <multiply name="rad_square" type="float">
      <input name="in1" type="float" interfacename="radius" />
      <input name="in2" type="float" interfacename="radius" />
    </multiply>
    <ifgreater name="dist_comp" type="float">
      <input name="value1" type="float" nodename="dist_square" />
      <input name="value2" type="float" nodename="rad_square" />
      <input name="in1" type="float" value="0" />
      <input name="in2" type="float" value="1" />
    </ifgreater>
    <output name="out" type="float" nodename="dist_comp" />
  </nodegraph>

  <!--
    Node: <cloverleaf>
    Returns 1 if texcoord is inside a cloverleaf shape inscribed by a circle defined by center and radius; otherwise returns 0.
  -->
  <nodegraph name="NG_cloverleaf_float" nodedef="ND_cloverleaf_float">
    <add name="sample_double" type="vector2">
      <input name="in1" type="vector2" interfacename="texcoord" />
      <input name="in2" type="vector2" interfacename="texcoord" />
    </add>
    <add name="sample_add" type="vector2">
      <input name="in1" type="vector2" nodename="sample_double" />
      <input name="in2" type="float" interfacename="radius" />
    </add>
    <subtract name="sample_subtract" type="vector2">
      <input name="in1" type="vector2" nodename="sample_double" />
      <input name="in2" type="float" interfacename="radius" />
    </subtract>
    <separate2 name="sample_double_separate" type="multioutput">
      <input name="in" type="vector2" nodename="sample_double" />
    </separate2>
    <separate2 name="sample_add_separate" type="multioutput">
      <input name="in" type="vector2" nodename="sample_add" />
    </separate2>
    <separate2 name="sample_subtract_separate" type="multioutput">
      <input name="in" type="vector2" nodename="sample_subtract" />
    </separate2>
    <combine2 name="coord1" type="vector2">
      <input name="in1" type="float" nodename="sample_add_separate" output="outx" />
      <input name="in2" type="float" nodename="sample_double_separate" output="outy" />
    </combine2>
    <combine2 name="coord2" type="vector2">
      <input name="in1" type="float" nodename="sample_subtract_separate" output="outx" />
      <input name="in2" type="float" nodename="sample_double_separate" output="outy" />
    </combine2>
    <combine2 name="coord3" type="vector2">
      <input name="in1" type="float" nodename="sample_double_separate" output="outx" />
      <input name="in2" type="float" nodename="sample_subtract_separate" output="outy" />
    </combine2>
    <combine2 name="coord4" type="vector2">
      <input name="in1" type="float" nodename="sample_double_separate" output="outx" />
      <input name="in2" type="float" nodename="sample_add_separate" output="outy" />
    </combine2>
    <circle name="circle1" type="float">
      <input name="texcoord" type="vector2" nodename="coord1" />
      <input name="center" type="vector2" interfacename="center" />
      <input name="radius" type="float" interfacename="radius" />
    </circle>
    <circle name="circle2" type="float">
      <input name="texcoord" type="vector2" nodename="coord2" />
      <input name="center" type="vector2" interfacename="center" />
      <input name="radius" type="float" interfacename="radius" />
    </circle>
    <circle name="circle3" type="float">
      <input name="texcoord" type="vector2" nodename="coord3" />
      <input name="center" type="vector2" interfacename="center" />
      <input name="radius" type="float" interfacename="radius" />
    </circle>
    <circle name="circle4" type="float">
      <input name="texcoord" type="vector2" nodename="coord4" />
      <input name="center" type="vector2" interfacename="center" />
      <input name="radius" type="float" interfacename="radius" />
    </circle>
    <max name="max1" type="float">
      <input name="in1" type="float" nodename="circle1" />
      <input name="in2" type="float" nodename="circle2" />
    </max>
    <max name="max2" type="float">
      <input name="in1" type="float" nodename="circle3" />
      <input name="in2" type="float" nodename="circle4" />
    </max>
    <max name="max" type="float">
      <input name="in1" type="float" nodename="max1" />
      <input name="in2" type="float" nodename="max2" />
    </max>
    <output name="out" type="float" nodename="max" />
  </nodegraph>

  <!--
    Node: <hexagon>
    Returns 1 if texcoord is inside a hexagon shape inscribed by a circle defined by center and radius; otherwise returns 0.
    Uses formulas from Inigo Quilez SDF samples (iquilezles.org)
  -->
  <nodegraph name="NG_hexagon_float" nodedef="ND_hexagon_float">
    <constant name="k" type="vector3">
      <input name="value" type="vector3" value="-0.866025, 0.5, 0.57735" />
    </constant>
    <multiply name="minus_k" type="vector3">
      <input name="in1" type="vector3" nodename="k" />
      <input name="in2" type="float" value="-1.0" />
    </multiply>
    <separate3 name="k_separate" type="multioutput">
      <input name="in" type="vector3" nodename="k" />
    </separate3>
    <separate3 name="minus_k_separate" type="multioutput">
      <input name="in" type="vector3" nodename="minus_k" />
    </separate3>
    <subtract name="delta" type="vector2">
      <input name="in1" type="vector2" interfacename="texcoord" />
      <input name="in2" type="vector2" interfacename="center" />
    </subtract>
    <absval name="delta_abs" type="vector2">
      <input name="in" type="vector2" nodename="delta" />
    </absval>
    <separate2 name="delta_abs_separate" type="multioutput">
      <input name="in" type="vector2" nodename="delta_abs" />
    </separate2>
    <combine2 name="p" type="vector2">
      <input name="in1" type="float" nodename="delta_abs_separate" output="outy" />
      <input name="in2" type="float" nodename="delta_abs_separate" output="outx" />
    </combine2>
    <multiply name="kz_r1" type="float">
      <input name="in1" type="float" nodename="k_separate" output="outz" />
      <input name="in2" type="float" interfacename="radius" />
    </multiply>
    <multiply name="minus_kz_r" type="float">
      <input name="in1" type="float" nodename="minus_k_separate" output="outz" />
      <input name="in2" type="float" interfacename="radius" />
    </multiply>
    <combine2 name="combine_mkx_ky" type="vector2">
      <input name="in1" type="float" nodename="minus_k_separate" output="outx" />
      <input name="in2" type="float" nodename="k_separate" output="outy" />
    </combine2>
    <combine2 name="kxy" type="vector2">
      <input name="in1" type="float" nodename="k_separate" output="outx" />
      <input name="in2" type="float" nodename="k_separate" output="outy" />
    </combine2>
    <dotproduct name="dot_kxy_p" type="float">
      <input name="in1" type="vector2" nodename="kxy" />
      <input name="in2" type="vector2" nodename="p" />
    </dotproduct>
    <dotproduct name="dot_kxy_p1" type="float">
      <input name="in1" type="vector2" nodename="combine_mkx_ky" />
      <input name="in2" type="vector2" nodename="new_p1" />
    </dotproduct>
    <min name="min_dotkxyp_p" type="float">
      <input name="in1" type="float" nodename="dot_kxy_p" />
    </min>
    <min name="min_0" type="float">
      <input name="in1" type="float" nodename="dot_kxy_p1" />
    </min>
    <multiply name="multiply_kxy_min" type="vector2">
      <input name="in1" type="vector2" nodename="kxy" />
      <input name="in2" type="float" nodename="min_dotkxyp_p" />
    </multiply>
    <multiply name="multiply2_1" type="vector2">
      <input name="in1" type="vector2" nodename="multiply_kxy_min" />
      <input name="in2" type="float" value="2" />
    </multiply>
    <multiply name="multiply_min_comb" type="vector2">
      <input name="in1" type="vector2" nodename="combine_mkx_ky" />
      <input name="in2" type="float" nodename="min_0" />
    </multiply>
    <multiply name="multiply2_2" type="vector2">
      <input name="in1" type="vector2" nodename="multiply_min_comb" />
      <input name="in2" type="float" value="2" />
    </multiply>
    <extract name="new_p2_x" type="float">
      <input name="in" type="vector2" nodename="new_p2" />
      <input name="index" type="integer" value="0" />
    </extract>
    <clamp name="clamp" type="float">
      <input name="in" type="float" nodename="new_p2_x" />
      <input name="low" type="float" nodename="minus_kz_r" />
      <input name="high" type="float" nodename="kz_r1" />
    </clamp>
    <combine2 name="combine_clamp_rad" type="vector2">
      <input name="in1" type="float" nodename="clamp" />
      <input name="in2" type="float" interfacename="radius" />
    </combine2>
    <subtract name="new_p1" type="vector2">
      <input name="in1" type="vector2" nodename="p" />
      <input name="in2" type="vector2" nodename="multiply2_1" />
    </subtract>
    <subtract name="new_p2" type="vector2">
      <input name="in1" type="vector2" nodename="new_p1" />
      <input name="in2" type="vector2" nodename="multiply2_2" />
    </subtract>
    <subtract name="new_p3" type="vector2">
      <input name="in1" type="vector2" nodename="new_p2" />
      <input name="in2" type="vector2" nodename="combine_clamp_rad" />
    </subtract>
    <dotproduct name="p3_sum" type="float">
      <input name="in1" type="vector2" nodename="new_p3" />
      <input name="in2" type="vector2" value="1, 1" />
    </dotproduct>
    <sqrt name="p3_sqrt" type="float">
      <input name="in" type="float" nodename="p3_sum" />
    </sqrt>
    <ifgreater name="ifgreater_p3" type="float">
      <input name="value1" type="float" nodename="p3_sqrt" />
      <input name="value2" type="float" value="0" />
      <input name="in1" type="float" value="0" />
      <input name="in2" type="float" value="1" />
    </ifgreater>
    <output name="out" type="float" nodename="ifgreater_p3" />
  </nodegraph>

  <!--
    Node: <grid>
    Creates a grid pattern with the given tiling, offset, and line thickness.
    Pattern can be regular or staggered.
  -->
  <nodegraph name="NG_grid_color3" nodedef="ND_grid_color3">
    <multiply name="texcoord_scale" type="vector2">
      <input name="in1" type="vector2" interfacename="texcoord" />
      <input name="in2" type="vector2" interfacename="uvtiling" />
    </multiply>
    <subtract name="texcoord_bias" type="vector2">
      <input name="in1" type="vector2" nodename="texcoord_scale" />
      <input name="in2" type="vector2" interfacename="uvoffset" />
    </subtract>
    <separate2 name="texcoord_bias_separate" type="multioutput">
      <input name="in" type="vector2" nodename="texcoord_bias" />
    </separate2>
    <subtract name="thick_to_size" type="float">
      <input name="in1" type="float" value="1.0" />
      <input name="in2" type="float" interfacename="thickness" />
    </subtract>
    <modulo name="mod_Y" type="float">
      <input name="in1" type="float" nodename="texcoord_bias_separate" output="outy" />
    </modulo>
    <modulo name="mod_Y_row" type="float">
      <input name="in1" type="float" nodename="texcoord_bias_separate" output="outy" />
      <input name="in2" type="float" value="2" />
    </modulo>
    <multiply name="mody_2" type="float">
      <input name="in1" type="float" nodename="mod_Y" />
      <input name="in2" type="float" value="2" />
    </multiply>
    <ifgreater name="alt_rows_shift" type="float">
      <input name="value1" type="float" nodename="mod_Y_row" />
      <input name="value2" type="float" value="1" />
      <input name="in1" type="float" value="0.5" />
    </ifgreater>
    <add name="shift_X" type="float">
      <input name="in1" type="float" nodename="texcoord_bias_separate" output="outx" />
      <input name="in2" type="float" nodename="alt_rows_shift" />
    </add>
    <ifequal name="stagger_selection" type="float">
      <input name="value1" type="boolean" interfacename="staggered" />
      <input name="value2" type="boolean" value="true" />
      <input name="in1" type="float" nodename="shift_X" />
      <input name="in2" type="float" nodename="texcoord_bias_separate" output="outx" />
    </ifequal>
    <modulo name="mod_X" type="float">
      <input name="in1" type="float" nodename="stagger_selection" />
    </modulo>
    <multiply name="modx_2" type="float">
      <input name="in1" type="float" nodename="mod_X" />
      <input name="in2" type="float" value="2" />
    </multiply>
    <subtract name="subX_1" type="float">
      <input name="in1" type="float" nodename="modx_2" />
      <input name="in2" type="float" value="1" />
    </subtract>
    <subtract name="subY_1" type="float">
      <input name="in1" type="float" nodename="mody_2" />
      <input name="in2" type="float" value="1" />
    </subtract>
    <absval name="abs_X" type="float">
      <input name="in" type="float" nodename="subX_1" />
    </absval>
    <absval name="abs_Y" type="float">
      <input name="in" type="float" nodename="subY_1" />
    </absval>
    <ifgreater name="X_detect" type="float">
      <input name="value1" type="float" nodename="abs_X" />
      <input name="value2" type="float" nodename="thick_to_size" />
      <input name="in1" type="float" value="0" />
      <input name="in2" type="float" value="1" />
    </ifgreater>
    <ifgreater name="Y_detect" type="float">
      <input name="value1" type="float" nodename="abs_Y" />
      <input name="value2" type="float" nodename="thick_to_size" />
      <input name="in1" type="float" value="0" />
      <input name="in2" type="float" value="1" />
    </ifgreater>
    <min name="min" type="float">
      <input name="in1" type="float" nodename="X_detect" />
      <input name="in2" type="float" nodename="Y_detect" />
    </min>
    <subtract name="inv_result" type="float">
      <input name="in1" type="float" value="1" />
      <input name="in2" type="float" nodename="min" />
    </subtract>
    <convert name="to_rgb" type="color3">
      <input name="in" type="float" nodename="inv_result" />
    </convert>
    <output name="out" type="color3" nodename="to_rgb" />
  </nodegraph>

  <!--
    Node: <crosshatch>
    Creates a crosshatch pattern with the given tiling, offset, and line thickness.
    Pattern can be regular or staggered.
  -->
  <nodegraph name="NG_crosshatch_color3" nodedef="ND_crosshatch_color3">
    <multiply name="texcoord_scale" type="vector2">
      <input name="in1" type="vector2" interfacename="texcoord" />
      <input name="in2" type="vector2" interfacename="uvtiling" />
    </multiply>
    <subtract name="texcoord_bias" type="vector2">
      <input name="in1" type="vector2" nodename="texcoord_scale" />
      <input name="in2" type="vector2" interfacename="uvoffset" />
    </subtract>
    <separate2 name="texcoord_bias_separate" type="multioutput">
      <input name="in" type="vector2" nodename="texcoord_bias" />
    </separate2>
    <modulo name="mod_Y" type="float">
      <input name="in1" type="float" nodename="texcoord_bias_separate" output="outy" />
    </modulo>
    <modulo name="mod_Y_row" type="float">
      <input name="in1" type="float" nodename="texcoord_bias_separate" output="outy" />
      <input name="in2" type="float" value="2" />
    </modulo>
    <multiply name="mody_2" type="float">
      <input name="in1" type="float" nodename="mod_Y" />
      <input name="in2" type="float" value="2" />
    </multiply>
    <ifgreater name="alt_rows_shift" type="float">
      <input name="value1" type="float" nodename="mod_Y_row" />
      <input name="value2" type="float" value="1" />
      <input name="in1" type="float" value="0.5" />
    </ifgreater>
    <add name="shift_X" type="float">
      <input name="in1" type="float" nodename="texcoord_bias_separate" output="outx" />
      <input name="in2" type="float" nodename="alt_rows_shift" />
    </add>
    <ifequal name="stagger_selection" type="float">
      <input name="value1" type="boolean" interfacename="staggered" />
      <input name="value2" type="boolean" value="true" />
      <input name="in1" type="float" nodename="shift_X" />
      <input name="in2" type="float" nodename="texcoord_bias_separate" output="outx" />
    </ifequal>
    <modulo name="mod_X" type="float">
      <input name="in1" type="float" nodename="stagger_selection" />
    </modulo>
    <multiply name="modx_2" type="float">
      <input name="in1" type="float" nodename="mod_X" />
      <input name="in2" type="float" value="2" />
    </multiply>
    <subtract name="subX_1" type="float">
      <input name="in1" type="float" nodename="modx_2" />
      <input name="in2" type="float" value="1" />
    </subtract>
    <subtract name="subY_1" type="float">
      <input name="in1" type="float" nodename="mody_2" />
      <input name="in2" type="float" value="1" />
    </subtract>
    <combine2 name="sample_vec" type="vector2">
      <input name="in1" type="float" nodename="subX_1" />
      <input name="in2" type="float" nodename="subY_1" />
    </combine2>
    <line name="line_diag1" type="float">
      <input name="texcoord" type="vector2" nodename="sample_vec" />
      <input name="radius" type="float" interfacename="thickness" />
      <input name="point1" type="vector2" value="1, 1" />
      <input name="point2" type="vector2" value="-1, -1" />
    </line>
    <line name="line_diag2" type="float">
      <input name="texcoord" type="vector2" nodename="sample_vec" />
      <input name="radius" type="float" interfacename="thickness" />
      <input name="point1" type="vector2" value="-1, 1" />
      <input name="point2" type="vector2" value="1, -1" />
    </line>
    <max name="composite_diags" type="float">
      <input name="in1" type="float" nodename="line_diag1" />
      <input name="in2" type="float" nodename="line_diag2" />
    </max>
    <max name="max" type="float">
      <input name="in1" type="float" nodename="composite_diags" />
      <input name="in2" type="float" nodename="composite_diags" />
    </max>
    <convert name="to_rgb" type="color3">
      <input name="in" type="float" nodename="max" />
    </convert>
    <output name="out" type="color3" nodename="to_rgb" />
  </nodegraph>

  <!--
    Node: <tiledcircles>
    Creates a black and white pattern of circles with a defined spacing and size (diameter).
    Pattern can be regular or staggered.
  -->
  <nodegraph name="NG_tiledcircles_color3" nodedef="ND_tiledcircles_color3">
    <multiply name="texcoord_scale" type="vector2">
      <input name="in1" type="vector2" interfacename="texcoord" />
      <input name="in2" type="vector2" interfacename="uvtiling" />
    </multiply>
    <subtract name="texcoord_bias" type="vector2">
      <input name="in1" type="vector2" nodename="texcoord_scale" />
      <input name="in2" type="vector2" interfacename="uvoffset" />
    </subtract>
    <separate2 name="texcoord_bias_separate" type="multioutput">
      <input name="in" type="vector2" nodename="texcoord_bias" />
    </separate2>
    <modulo name="mod_texcoord" type="vector2">
      <input name="in1" type="vector2" nodename="texcoord_bias" />
    </modulo>
    <multiply name="mod_texcoord_2" type="vector2">
      <input name="in1" type="vector2" nodename="mod_texcoord" />
      <input name="in2" type="float" value="2" />
    </multiply>
    <subtract name="recenter" type="vector2">
      <input name="in1" type="vector2" nodename="mod_texcoord_2" />
      <input name="in2" type="float" value="1" />
    </subtract>
    <modulo name="stagg_Y" type="float">
      <input name="in1" type="float" nodename="texcoord_bias_separate" output="outy" />
      <input name="in2" type="float" value="1.73205" />
    </modulo>
    <ifgreater name="delta_X" type="float">
      <input name="value1" type="float" nodename="stagg_Y" />
      <input name="value2" type="float" value="0.866025" />
      <input name="in1" type="float" value="0.5" />
    </ifgreater>
    <add name="shift_X" type="float">
      <input name="in1" type="float" nodename="texcoord_bias_separate" output="outx" />
      <input name="in2" type="float" nodename="delta_X" />
    </add>
    <modulo name="mod_X_1" type="float">
      <input name="in1" type="float" nodename="shift_X" />
    </modulo>
    <modulo name="mod_Y_1" type="float">
      <input name="in1" type="float" nodename="texcoord_bias_separate" output="outy" />
      <input name="in2" type="float" value="0.866025" />
    </modulo>
    <subtract name="coord_adj_1" type="float">
      <input name="in1" type="float" value="1" />
      <input name="in2" type="float" nodename="mod_X_1" />
    </subtract>
    <subtract name="coord_adj_2" type="float">
      <input name="in1" type="float" nodename="mod_X_1" />
      <input name="in2" type="float" value="0.5" />
    </subtract>
    <subtract name="coord_adj_3" type="float">
      <input name="in1" type="float" value="0.866025" />
      <input name="in2" type="float" nodename="mod_Y_1" />
    </subtract>
    <combine2 name="coord_circ1" type="vector2">
      <input name="in1" type="float" nodename="mod_X_1" />
      <input name="in2" type="float" nodename="mod_Y_1" />
    </combine2>
    <combine2 name="coord_circ2" type="vector2">
      <input name="in1" type="float" nodename="coord_adj_1" />
      <input name="in2" type="float" nodename="mod_Y_1" />
    </combine2>
    <combine2 name="coord_circ3" type="vector2">
      <input name="in1" type="float" nodename="coord_adj_2" />
      <input name="in2" type="float" nodename="coord_adj_3" />
    </combine2>
    <divide name="scale_half" type="float">
      <input name="in1" type="float" interfacename="size" />
      <input name="in2" type="float" value="2" />
    </divide>
    <circle name="circle_stagg1" type="float">
      <input name="texcoord" type="vector2" nodename="coord_circ1" />
      <input name="radius" type="float" nodename="scale_half" />
    </circle>
    <circle name="circle_stagg2" type="float">
      <input name="texcoord" type="vector2" nodename="coord_circ2" />
      <input name="radius" type="float" nodename="scale_half" />
    </circle>
    <circle name="circle_stagg3" type="float">
      <input name="texcoord" type="vector2" nodename="coord_circ3" />
      <input name="radius" type="float" nodename="scale_half" />
    </circle>
    <max name="max1" type="float">
      <input name="in1" type="float" nodename="circle_stagg1" />
      <input name="in2" type="float" nodename="circle_stagg2" />
    </max>
    <max name="max" type="float">
      <input name="in1" type="float" nodename="max1" />
      <input name="in2" type="float" nodename="circle_stagg3" />
    </max>
    <circle name="circle_regular" type="float">
      <input name="texcoord" type="vector2" nodename="recenter" />
      <input name="radius" type="float" interfacename="size" />
    </circle>
    <ifequal name="pattern_selection" type="float">
      <input name="value1" type="boolean" interfacename="staggered" />
      <input name="value2" type="boolean" value="true" />
      <input name="in1" type="float" nodename="max" />
      <input name="in2" type="float" nodename="circle_regular" />
    </ifequal>
    <convert name="to_rgb" type="color3">
      <input name="in" type="float" nodename="pattern_selection" />
    </convert>
    <output name="out" type="color3" nodename="to_rgb" />
  </nodegraph>

  <!--
    Node: <tiledcloverleafs>
    Creates a black and white pattern of cloverleafs with a defined spacing and size (diameter of the circles circumscribing the shape).
    Pattern can be regular or staggered.
  -->
  <nodegraph name="NG_tiledcloverleafs_color3" nodedef="ND_tiledcloverleafs_color3">
    <multiply name="texcoord_scale" type="vector2">
      <input name="in1" type="vector2" interfacename="texcoord" />
      <input name="in2" type="vector2" interfacename="uvtiling" />
    </multiply>
    <subtract name="texcoord_bias" type="vector2">
      <input name="in1" type="vector2" nodename="texcoord_scale" />
      <input name="in2" type="vector2" interfacename="uvoffset" />
    </subtract>
    <separate2 name="texcoord_bias_separate" type="multioutput">
      <input name="in" type="vector2" nodename="texcoord_bias" />
    </separate2>
    <modulo name="mod_texcoord" type="vector2">
      <input name="in1" type="vector2" nodename="texcoord_bias" />
    </modulo>
    <multiply name="mod_texcoord_2" type="vector2">
      <input name="in1" type="vector2" nodename="mod_texcoord" />
      <input name="in2" type="float" value="2" />
    </multiply>
    <subtract name="recenter" type="vector2">
      <input name="in1" type="vector2" nodename="mod_texcoord_2" />
      <input name="in2" type="float" value="1" />
    </subtract>
    <modulo name="stagg_Y" type="float">
      <input name="in1" type="float" nodename="texcoord_bias_separate" output="outy" />
      <input name="in2" type="float" value="1" />
    </modulo>
    <ifgreater name="delta_X" type="float">
      <input name="value1" type="float" nodename="stagg_Y" />
      <input name="value2" type="float" value="0.5" />
      <input name="in1" type="float" value="0.5" />
    </ifgreater>
    <add name="shift_X" type="float">
      <input name="in1" type="float" nodename="texcoord_bias_separate" output="outx" />
      <input name="in2" type="float" nodename="delta_X" />
    </add>
    <modulo name="mod_X_1" type="float">
      <input name="in1" type="float" nodename="shift_X" />
    </modulo>
    <modulo name="mod_Y_1" type="float">
      <input name="in1" type="float" nodename="texcoord_bias_separate" output="outy" />
      <input name="in2" type="float" value="0.5" />
    </modulo>
    <subtract name="coord_adj_1" type="float">
      <input name="in1" type="float" value="1" />
      <input name="in2" type="float" nodename="mod_X_1" />
    </subtract>
    <subtract name="coord_adj_2" type="float">
      <input name="in1" type="float" nodename="mod_X_1" />
      <input name="in2" type="float" value="0.5" />
    </subtract>
    <subtract name="coord_adj_3" type="float">
      <input name="in1" type="float" value="0.5" />
      <input name="in2" type="float" nodename="mod_Y_1" />
    </subtract>
    <combine2 name="coord_circ1" type="vector2">
      <input name="in1" type="float" nodename="mod_X_1" />
      <input name="in2" type="float" nodename="mod_Y_1" />
    </combine2>
    <combine2 name="coord_circ2" type="vector2">
      <input name="in1" type="float" nodename="coord_adj_1" />
      <input name="in2" type="float" nodename="mod_Y_1" />
    </combine2>
    <combine2 name="coord_circ3" type="vector2">
      <input name="in1" type="float" nodename="coord_adj_2" />
      <input name="in2" type="float" nodename="coord_adj_3" />
    </combine2>
    <divide name="scale_half" type="float">
      <input name="in1" type="float" interfacename="size" />
      <input name="in2" type="float" value="2" />
    </divide>
    <cloverleaf name="cloverleaf_stagg1" type="float">
      <input name="texcoord" type="vector2" nodename="coord_circ1" />
      <input name="radius" type="float" nodename="scale_half" />
    </cloverleaf>
    <cloverleaf name="cloverleaf_stagg2" type="float">
      <input name="texcoord" type="vector2" nodename="coord_circ2" />
      <input name="radius" type="float" nodename="scale_half" />
    </cloverleaf>
    <cloverleaf name="cloverleaf_stagg3" type="float">
      <input name="texcoord" type="vector2" nodename="coord_circ3" />
      <input name="radius" type="float" nodename="scale_half" />
    </cloverleaf>
    <max name="max1" type="float">
      <input name="in1" type="float" nodename="cloverleaf_stagg1" />
      <input name="in2" type="float" nodename="cloverleaf_stagg2" />
    </max>
    <max name="max" type="float">
      <input name="in1" type="float" nodename="max1" />
      <input name="in2" type="float" nodename="cloverleaf_stagg3" />
    </max>
    <cloverleaf name="cloverleaf_regular" type="float">
      <input name="texcoord" type="vector2" nodename="recenter" />
      <input name="radius" type="float" interfacename="size" />
    </cloverleaf>
    <ifequal name="pattern_selection" type="float">
      <input name="value1" type="boolean" interfacename="staggered" />
      <input name="value2" type="boolean" value="true" />
      <input name="in1" type="float" nodename="max" />
      <input name="in2" type="float" nodename="cloverleaf_regular" />
    </ifequal>
    <convert name="to_rgb" type="color3">
      <input name="in" type="float" nodename="pattern_selection" />
    </convert>
    <output name="out" type="color3" nodename="to_rgb" />
  </nodegraph>

  <!--
    Node: <tiledhexagons>
    Creates a black and white pattern of hexagons with a defined spacing and size (diameter of the circles circumscribing the shape).
    Pattern can be regular or staggered.
  -->
  <nodegraph name="NG_tiledhexagons_color3" nodedef="ND_tiledhexagons_color3">
    <multiply name="texcoord_scale" type="vector2">
      <input name="in1" type="vector2" interfacename="texcoord" />
      <input name="in2" type="vector2" interfacename="uvtiling" />
    </multiply>
    <subtract name="texcoord_bias" type="vector2">
      <input name="in1" type="vector2" nodename="texcoord_scale" />
      <input name="in2" type="vector2" interfacename="uvoffset" />
    </subtract>
    <separate2 name="texcoord_bias_separate" type="multioutput">
      <input name="in" type="vector2" nodename="texcoord_bias" />
    </separate2>
    <modulo name="mod_texcoord" type="vector2">
      <input name="in1" type="vector2" nodename="texcoord_bias" />
    </modulo>
    <multiply name="mod_texcoord_2" type="vector2">
      <input name="in1" type="vector2" nodename="mod_texcoord" />
      <input name="in2" type="float" value="2" />
    </multiply>
    <subtract name="recenter" type="vector2">
      <input name="in1" type="vector2" nodename="mod_texcoord_2" />
      <input name="in2" type="float" value="1" />
    </subtract>
    <modulo name="stagg_Y" type="float">
      <input name="in1" type="float" nodename="texcoord_bias_separate" output="outy" />
      <input name="in2" type="float" value="1.73205" />
    </modulo>
    <ifgreater name="delta_X" type="float">
      <input name="value1" type="float" nodename="stagg_Y" />
      <input name="value2" type="float" value="0.866025" />
      <input name="in1" type="float" value="0.5" />
    </ifgreater>
    <add name="shift_X" type="float">
      <input name="in1" type="float" nodename="texcoord_bias_separate" output="outx" />
      <input name="in2" type="float" nodename="delta_X" />
    </add>
    <modulo name="mod_X_1" type="float">
      <input name="in1" type="float" nodename="shift_X" />
    </modulo>
    <modulo name="mod_Y_1" type="float">
      <input name="in1" type="float" nodename="texcoord_bias_separate" output="outy" />
      <input name="in2" type="float" value="0.866025" />
    </modulo>
    <subtract name="coord_adj_1" type="float">
      <input name="in1" type="float" value="1" />
      <input name="in2" type="float" nodename="mod_X_1" />
    </subtract>
    <subtract name="coord_adj_2" type="float">
      <input name="in1" type="float" nodename="mod_X_1" />
      <input name="in2" type="float" value="0.5" />
    </subtract>
    <subtract name="coord_adj_3" type="float">
      <input name="in1" type="float" value="0.866025" />
      <input name="in2" type="float" nodename="mod_Y_1" />
    </subtract>
    <combine2 name="coord_circ1" type="vector2">
      <input name="in1" type="float" nodename="mod_X_1" />
      <input name="in2" type="float" nodename="mod_Y_1" />
    </combine2>
    <combine2 name="coord_circ2" type="vector2">
      <input name="in1" type="float" nodename="coord_adj_1" />
      <input name="in2" type="float" nodename="mod_Y_1" />
    </combine2>
    <combine2 name="coord_circ3" type="vector2">
      <input name="in1" type="float" nodename="coord_adj_2" />
      <input name="in2" type="float" nodename="coord_adj_3" />
    </combine2>
    <divide name="scale_half" type="float">
      <input name="in1" type="float" interfacename="size" />
      <input name="in2" type="float" value="2" />
    </divide>
    <hexagon name="hexagon_stagg1" type="float">
      <input name="texcoord" type="vector2" nodename="coord_circ1" />
      <input name="radius" type="float" nodename="scale_half" />
    </hexagon>
    <hexagon name="hexagon_stagg2" type="float">
      <input name="texcoord" type="vector2" nodename="coord_circ2" />
      <input name="radius" type="float" nodename="scale_half" />
    </hexagon>
    <hexagon name="hexagon_stagg3" type="float">
      <input name="texcoord" type="vector2" nodename="coord_circ3" />
      <input name="radius" type="float" nodename="scale_half" />
    </hexagon>
    <max name="max1" type="float">
      <input name="in1" type="float" nodename="hexagon_stagg1" />
      <input name="in2" type="float" nodename="hexagon_stagg2" />
    </max>
    <max name="max" type="float">
      <input name="in1" type="float" nodename="max1" />
      <input name="in2" type="float" nodename="hexagon_stagg3" />
    </max>
    <hexagon name="hexagon_regular" type="float">
      <input name="texcoord" type="vector2" nodename="recenter" />
      <input name="radius" type="float" interfacename="size" />
    </hexagon>
    <ifequal name="pattern_selection" type="float">
      <input name="value1" type="boolean" interfacename="staggered" />
      <input name="value2" type="boolean" value="true" />
      <input name="in1" type="float" nodename="max" />
      <input name="in2" type="float" nodename="hexagon_regular" />
    </ifequal>
    <convert name="to_rgb" type="color3">
      <input name="in" type="float" nodename="pattern_selection" />
    </convert>
    <output name="out" type="color3" nodename="to_rgb" />
  </nodegraph>

  <!-- ======================================================================== -->
  <!-- Geometric nodes                                                          -->
  <!-- ======================================================================== -->

  <!--
    Node: <bump>
  -->
  <nodegraph name="NG_bump_vector3" nodedef="ND_bump_vector3">
    <heighttonormal name="N_heighttonormal" type="vector3">
      <input name="in" type="float" interfacename="height" />
    </heighttonormal>
    <normalmap name="N_normalmap" type="vector3">
      <input name="in" type="vector3" nodename="N_heighttonormal" />
      <input name="normal" type="vector3" interfacename="normal" />
      <input name="scale" type="float" interfacename="scale" />
      <input name="tangent" type="vector3" interfacename="tangent" />
    </normalmap>
    <output name="out" type="vector3" nodename="N_normalmap" />
  </nodegraph>

  <!-- ======================================================================== -->
  <!-- Global nodes                                                             -->
  <!-- ======================================================================== -->

  <!-- ======================================================================== -->
  <!-- Application nodes                                                        -->
  <!-- ======================================================================== -->

  <!-- ======================================================================== -->
  <!-- Math nodes                                                               -->
  <!-- ======================================================================== -->

  <!--
    Node: <place2d>
    Transform incoming UV texture coordinates from one 2D frame of reference to another.
    operationorder (integer enum): the order in which to perform the transform operations.
    "0" or "SRT" performs -pivot, scale, rotate, translate, +pivot as per the original
    implementation matching the behavior of certain DCC packages, and "1" or "TRS" performs
    -pivot, translate, rotate, scale, +pivot which does not introduce texture shear.
    Default is 0 "SRT" for backward compatibility.
  -->
  <nodegraph name="NG_place2d_vector2" nodedef="ND_place2d_vector2">
    <subtract name="N_subpivot" type="vector2">
      <input name="in1" type="vector2" interfacename="texcoord" />
      <input name="in2" type="vector2" interfacename="pivot" />
    </subtract>
    <divide name="N_applyscale" type="vector2">
      <input name="in1" type="vector2" nodename="N_subpivot" />
      <input name="in2" type="vector2" interfacename="scale" />
    </divide>
    <rotate2d name="N_applyrot" type="vector2">
      <input name="in" type="vector2" nodename="N_applyscale" />
      <input name="amount" type="float" interfacename="rotate" />
    </rotate2d>
    <subtract name="N_applyoffset" type="vector2">
      <input name="in1" type="vector2" nodename="N_applyrot" />
      <input name="in2" type="vector2" interfacename="offset" />
    </subtract>
    <add name="N_addpivot" type="vector2">
      <input name="in1" type="vector2" nodename="N_applyoffset" />
      <input name="in2" type="vector2" interfacename="pivot" />
    </add>
    <subtract name="N_applyoffset2" type="vector2">
      <input name="in1" type="vector2" nodename="N_subpivot" />
      <input name="in2" type="vector2" interfacename="offset" />
    </subtract>
    <rotate2d name="N_applyrot2" type="vector2">
      <input name="in" type="vector2" nodename="N_applyoffset2" />
      <input name="amount" type="float" interfacename="rotate" />
    </rotate2d>
    <divide name="N_applyscale2" type="vector2">
      <input name="in1" type="vector2" nodename="N_applyrot2" />
      <input name="in2" type="vector2" interfacename="scale" />
    </divide>
    <add name="N_addpivot2" type="vector2">
      <input name="in1" type="vector2" nodename="N_applyscale2" />
      <input name="in2" type="vector2" interfacename="pivot" />
    </add>
    <switch name="N_switch_operationorder" type="vector2">
      <input name="in1" type="vector2" nodename="N_addpivot" />
      <input name="in2" type="vector2" nodename="N_addpivot2" />
      <input name="which" type="integer" interfacename="operationorder" />
    </switch>
    <output name="out" type="vector2" nodename="N_switch_operationorder" />
  </nodegraph>

  <!--
    Node: <distance>
    Measures the distance between two points in 2D, 3D, or 4D.
  -->
  <nodegraph name="NG_distance_vector2" nodedef="ND_distance_vector2">
    <subtract name="N_mtlxsubtract" type="vector2">
      <input name="in1" type="vector2" interfacename="in1" />
      <input name="in2" type="vector2" interfacename="in2" />
    </subtract>
    <magnitude name="N_mtlxmagnitude" type="float">
      <input name="in" type="vector2" nodename="N_mtlxsubtract" />
    </magnitude>
    <output name="out" type="float" nodename="N_mtlxmagnitude" />
  </nodegraph>
  <nodegraph name="NG_distance_vector3" nodedef="ND_distance_vector3">
    <subtract name="N_mtlxsubtract" type="vector3">
      <input name="in1" type="vector3" interfacename="in1" />
      <input name="in2" type="vector3" interfacename="in2" />
    </subtract>
    <magnitude name="N_mtlxmagnitude" type="float">
      <input name="in" type="vector3" nodename="N_mtlxsubtract" />
    </magnitude>
    <output name="out" type="float" nodename="N_mtlxmagnitude" />
  </nodegraph>
  <nodegraph name="NG_distance_vector4" nodedef="ND_distance_vector4">
    <subtract name="N_mtlxsubtract" type="vector4">
      <input name="in1" type="vector4" interfacename="in1" />
      <input name="in2" type="vector4" interfacename="in2" />
    </subtract>
    <magnitude name="N_mtlxmagnitude" type="float">
      <input name="in" type="vector4" nodename="N_mtlxsubtract" />
    </magnitude>
    <output name="out" type="float" nodename="N_mtlxmagnitude" />
  </nodegraph>

  <!--
    Node: <trianglewave>
    Generate a triangle wave from the given scalar input.
    The generated wave ranges from zero to one and repeats on integer boundaries.
  -->
  <nodegraph name="NG_trianglewave_float" nodedef="ND_trianglewave_float">
    <absval name="absval1" type="float">
      <input name="in" type="float" interfacename="in" />
    </absval>
    <modulo name="modulo1" type="float">
      <input name="in1" type="float" nodename="absval1" />
    </modulo>
    <subtract name="subtract1" type="float">
      <input name="in1" type="float" nodename="modulo1" />
      <input name="in2" type="float" value="0.5" />
    </subtract>
    <absval name="absval2" type="float">
      <input name="in" type="float" nodename="subtract1" />
    </absval>
    <subtract name="subtract2" type="float">
      <input name="in1" type="float" value="0.5" />
      <input name="in2" type="float" nodename="absval2" />
    </subtract>
    <output name="out" type="float" nodename="subtract2" />
  </nodegraph>

  <!--
    Node: <reflect>
    Compute the reflection vector given an incident vector and unit surface normal.
  -->
  <nodegraph name="NG_reflect_vector3" type="vector3" nodedef="ND_reflect_vector3">
    <dotproduct name="NdotI" type="float">
      <input name="in1" type="vector3" interfacename="normal" />
      <input name="in2" type="vector3" interfacename="in" />
    </dotproduct>
    <multiply name="NdotI_2" type="float">
      <input name="in1" type="float" nodename="NdotI" />
      <input name="in2" type="float" value="2" />
    </multiply>
    <multiply name="NdotI_N_2" type="vector3">
      <input name="in1" type="vector3" interfacename="normal" />
      <input name="in2" type="float" nodename="NdotI_2" />
    </multiply>
    <subtract name="reflection_vector" type="vector3">
      <input name="in1" type="vector3" interfacename="in" />
      <input name="in2" type="vector3" nodename="NdotI_N_2" />
    </subtract>
    <output name="out" type="vector3" nodename="reflection_vector" />
  </nodegraph>

  <!--
    Node: <refract>
    Compute the refraction vector given an incident vector, unit surface normal,
    and index of refraction.
  -->
  <nodegraph name="NG_refract_vector3" nodedef="ND_refract_vector3">
    <dotproduct name="IdotN" type="float">
      <input name="in1" type="vector3" interfacename="in" />
      <input name="in2" type="vector3" interfacename="normal" />
    </dotproduct>
    <multiply name="IdotNsq" type="float">
      <input name="in1" type="float" nodename="IdotN" />
      <input name="in2" type="float" nodename="IdotN" />
    </multiply>
    <multiply name="iorsq" type="float">
      <input name="in1" type="float" interfacename="ior" />
      <input name="in2" type="float" interfacename="ior" />
    </multiply>
    <subtract name="one_IdotNsq" type="float">
      <input name="in1" type="float" value="1.0" />
      <input name="in2" type="float" nodename="IdotNsq" />
    </subtract>
    <multiply name="iorsq_one_IdotNsq" type="float">
      <input name="in1" type="float" nodename="iorsq" />
      <input name="in2" type="float" nodename="one_IdotNsq" />
    </multiply>
    <subtract name="k" type="float">
      <input name="in1" type="float" value="1.0" />
      <input name="in2" type="float" nodename="iorsq_one_IdotNsq" />
    </subtract>
    <multiply name="I_scaled" type="vector3">
      <input name="in1" type="vector3" interfacename="in" />
      <input name="in2" type="float" interfacename="ior" />
    </multiply>
    <sqrt name="sqrt_k" type="float">
      <input name="in" type="float" nodename="k" />
    </sqrt>
    <multiply name="ior_NdotI" type="float">
      <input name="in1" type="float" interfacename="ior" />
      <input name="in2" type="float" nodename="IdotN" />
    </multiply>
    <add name="ior_NdotI_sqrt_k" type="float">
      <input name="in1" type="float" nodename="ior_NdotI" />
      <input name="in2" type="float" nodename="sqrt_k" />
    </add>
    <multiply name="N_scaled" type="vector3">
      <input name="in1" type="vector3" interfacename="normal" />
      <input name="in2" type="float" nodename="ior_NdotI_sqrt_k" />
    </multiply>
    <subtract name="refract_dir" type="vector3">
      <input name="in1" type="vector3" nodename="I_scaled" />
      <input name="in2" type="vector3" nodename="N_scaled" />
    </subtract>
    <ifgreater name="result" type="vector3">
      <input name="value1" type="float" value="0.0" />
      <input name="value2" type="float" nodename="k" />
      <input name="in1" type="vector3" value="0.0, 0.0, 0.0" />
      <input name="in2" type="vector3" nodename="refract_dir" />
    </ifgreater>
    <output name="out" type="vector3" nodename="result" />
  </nodegraph>

  <!-- ======================================================================== -->
  <!-- Adjustment nodes                                                         -->
  <!-- ======================================================================== -->

  <!--
    Node: <smoothstep>
    Outputs a smooth (hermite-interpolated) remapping of input values from low-high
    to output 0-1.
  -->
  <nodegraph name="NG_smoothstep_color3" nodedef="ND_smoothstep_color3">
    <separate3 name="separate_in" type="multioutput">
      <input name="in" type="color3" interfacename="in" />
    </separate3>
    <separate3 name="separate_low" type="multioutput">
      <input name="in" type="color3" interfacename="low" />
    </separate3>
    <separate3 name="separate_high" type="multioutput">
      <input name="in" type="color3" interfacename="high" />
    </separate3>
    <smoothstep name="smoothstep_r" type="float">
      <input name="in" type="float" nodename="separate_in" output="outr"/>
      <input name="low" type="float" nodename="separate_low" output="outr"/>
      <input name="high" type="float" nodename="separate_high" output="outr"/>
    </smoothstep>
    <smoothstep name="smoothstep_g" type="float">
      <input name="in" type="float" nodename="separate_in" output="outg"/>
      <input name="low" type="float" nodename="separate_low" output="outg"/>
      <input name="high" type="float" nodename="separate_high" output="outg"/>
    </smoothstep>
    <smoothstep name="smoothstep_b" type="float">
      <input name="in" type="float" nodename="separate_in" output="outb"/>
      <input name="low" type="float" nodename="separate_low" output="outb"/>
      <input name="high" type="float" nodename="separate_high" output="outb"/>
    </smoothstep>
    <combine3 name="combine" type="color3">
      <input name="in1" type="float" nodename="smoothstep_r"/>
      <input name="in2" type="float" nodename="smoothstep_g"/>
      <input name="in3" type="float" nodename="smoothstep_b"/>
    </combine3>
    <output name="out" type="color3" nodename="combine"/>
  </nodegraph>
  <nodegraph name="NG_smoothstep_color4" nodedef="ND_smoothstep_color4">
    <separate4 name="separate_in" type="multioutput">
      <input name="in" type="color4" interfacename="in" />
    </separate4>
    <separate4 name="separate_low" type="multioutput">
      <input name="in" type="color4" interfacename="low" />
    </separate4>
    <separate4 name="separate_high" type="multioutput">
      <input name="in" type="color4" interfacename="high" />
    </separate4>
    <smoothstep name="smoothstep_r" type="float">
      <input name="in" type="float" nodename="separate_in" output="outr"/>
      <input name="low" type="float" nodename="separate_low" output="outr"/>
      <input name="high" type="float" nodename="separate_high" output="outr"/>
    </smoothstep>
    <smoothstep name="smoothstep_g" type="float">
      <input name="in" type="float" nodename="separate_in" output="outg"/>
      <input name="low" type="float" nodename="separate_low" output="outg"/>
      <input name="high" type="float" nodename="separate_high" output="outg"/>
    </smoothstep>
    <smoothstep name="smoothstep_b" type="float">
      <input name="in" type="float" nodename="separate_in" output="outb"/>
      <input name="low" type="float" nodename="separate_low" output="outb"/>
      <input name="high" type="float" nodename="separate_high" output="outb"/>
    </smoothstep>
    <smoothstep name="smoothstep_a" type="float">
      <input name="in" type="float" nodename="separate_in" output="outa"/>
      <input name="low" type="float" nodename="separate_low" output="outa"/>
      <input name="high" type="float" nodename="separate_high" output="outa"/>
    </smoothstep>
    <combine4 name="combine" type="color4">
      <input name="in1" type="float" nodename="smoothstep_r"/>
      <input name="in2" type="float" nodename="smoothstep_g"/>
      <input name="in3" type="float" nodename="smoothstep_b"/>
      <input name="in4" type="float" nodename="smoothstep_a"/>
    </combine4>
    <output name="out" type="color4" nodename="combine"/>
  </nodegraph>
  <nodegraph name="NG_smoothstep_color3FA" nodedef="ND_smoothstep_color3FA">
    <separate3 name="separate_in" type="multioutput">
      <input name="in" type="color3" interfacename="in" />
    </separate3>
    <smoothstep name="smoothstep_r" type="float">
      <input name="in" type="float" nodename="separate_in" output="outr"/>
      <input name="low" type="float" interfacename="low"/>
      <input name="high" type="float" interfacename="high"/>
    </smoothstep>
    <smoothstep name="smoothstep_g" type="float">
      <input name="in" type="float" nodename="separate_in" output="outg"/>
      <input name="low" type="float" interfacename="low"/>
      <input name="high" type="float" interfacename="high"/>
    </smoothstep>
    <smoothstep name="smoothstep_b" type="float">
      <input name="in" type="float" nodename="separate_in" output="outb"/>
      <input name="low" type="float" interfacename="low"/>
      <input name="high" type="float" interfacename="high"/>
    </smoothstep>
    <combine3 name="combine" type="color3">
      <input name="in1" type="float" nodename="smoothstep_r"/>
      <input name="in2" type="float" nodename="smoothstep_g"/>
      <input name="in3" type="float" nodename="smoothstep_b"/>
    </combine3>
    <output name="out" type="color3" nodename="combine"/>
  </nodegraph>
  <nodegraph name="NG_smoothstep_color4FA" nodedef="ND_smoothstep_color4FA">
    <separate4 name="separate_in" type="multioutput">
      <input name="in" type="color4" interfacename="in" />
    </separate4>
    <smoothstep name="smoothstep_r" type="float">
      <input name="in" type="float" nodename="separate_in" output="outr"/>
      <input name="low" type="float" interfacename="low"/>
      <input name="high" type="float" interfacename="high"/>
    </smoothstep>
    <smoothstep name="smoothstep_g" type="float">
      <input name="in" type="float" nodename="separate_in" output="outg"/>
      <input name="low" type="float" interfacename="low"/>
      <input name="high" type="float" interfacename="high"/>
    </smoothstep>
    <smoothstep name="smoothstep_b" type="float">
      <input name="in" type="float" nodename="separate_in" output="outb"/>
      <input name="low" type="float" interfacename="low"/>
      <input name="high" type="float" interfacename="high"/>
    </smoothstep>
    <smoothstep name="smoothstep_a" type="float">
      <input name="in" type="float" nodename="separate_in" output="outa"/>
      <input name="low" type="float" interfacename="low"/>
      <input name="high" type="float" interfacename="high"/>
    </smoothstep>
    <combine4 name="combine" type="color4">
      <input name="in1" type="float" nodename="smoothstep_r"/>
      <input name="in2" type="float" nodename="smoothstep_g"/>
      <input name="in3" type="float" nodename="smoothstep_b"/>
      <input name="in4" type="float" nodename="smoothstep_a"/>
    </combine4>
    <output name="out" type="color4" nodename="combine"/>
  </nodegraph>
  <nodegraph name="NG_smoothstep_vector2" nodedef="ND_smoothstep_vector2">
    <separate2 name="separate_in" type="multioutput">
      <input name="in" type="vector2" interfacename="in"/>
    </separate2>
    <separate2 name="separate_low" type="multioutput">
      <input name="in" type="vector2" interfacename="low"/>
    </separate2>
    <separate2 name="separate_high" type="multioutput">
      <input name="in" type="vector2" interfacename="high"/>
    </separate2>
    <smoothstep name="smoothstep_x" type="float">
      <input name="in" type="float" nodename="separate_in" output="outx"/>
      <input name="low" type="float" nodename="separate_low" output="outx"/>
      <input name="high" type="float" nodename="separate_high" output="outx"/>
    </smoothstep>
    <smoothstep name="smoothstep_y" type="float">
      <input name="in" type="float" nodename="separate_in" output="outy"/>
      <input name="low" type="float" nodename="separate_low" output="outy"/>
      <input name="high" type="float" nodename="separate_high" output="outy"/>
    </smoothstep>
    <combine2 name="combine" type="vector2">
      <input name="in1" type="float" nodename="smoothstep_x"/>
      <input name="in2" type="float" nodename="smoothstep_y"/>
    </combine2>
    <output name="out" type="vector2" nodename="combine"/>
  </nodegraph>
  <nodegraph name="NG_smoothstep_vector3" nodedef="ND_smoothstep_vector3">
    <separate3 name="separate_in" type="multioutput">
      <input name="in" type="vector3" interfacename="in"/>
    </separate3>
    <separate3 name="separate_low" type="multioutput">
      <input name="in" type="vector3" interfacename="low"/>
    </separate3>
    <separate3 name="separate_high" type="multioutput">
      <input name="in" type="vector3" interfacename="high"/>
    </separate3>
    <smoothstep name="smoothstep_x" type="float">
      <input name="in" type="float" nodename="separate_in" output="outx"/>
      <input name="low" type="float" nodename="separate_low" output="outx"/>
      <input name="high" type="float" nodename="separate_high" output="outx"/>
    </smoothstep>
    <smoothstep name="smoothstep_y" type="float">
      <input name="in" type="float" nodename="separate_in" output="outy"/>
      <input name="low" type="float" nodename="separate_low" output="outy"/>
      <input name="high" type="float" nodename="separate_high" output="outy"/>
    </smoothstep>
    <smoothstep name="smoothstep_z" type="float">
      <input name="in" type="float" nodename="separate_in" output="outz"/>
      <input name="low" type="float" nodename="separate_low" output="outz"/>
      <input name="high" type="float" nodename="separate_high" output="outz"/>
    </smoothstep>
    <combine3 name="combine" type="vector3">
      <input name="in1" type="float" nodename="smoothstep_x"/>
      <input name="in2" type="float" nodename="smoothstep_y"/>
      <input name="in3" type="float" nodename="smoothstep_z"/>
    </combine3>
    <output name="out" type="vector3" nodename="combine"/>
  </nodegraph>
  <nodegraph name="NG_smoothstep_vector4" nodedef="ND_smoothstep_vector4">
    <separate4 name="separate_in" type="multioutput">
      <input name="in" type="vector4" interfacename="in"/>
    </separate4>
    <separate4 name="separate_low" type="multioutput">
      <input name="in" type="vector4" interfacename="low"/>
    </separate4>
    <separate4 name="separate_high" type="multioutput">
      <input name="in" type="vector4" interfacename="high"/>
    </separate4>
    <smoothstep name="smoothstep_x" type="float">
      <input name="in" type="float" nodename="separate_in" output="outx"/>
      <input name="low" type="float" nodename="separate_low" output="outx"/>
      <input name="high" type="float" nodename="separate_high" output="outx"/>
    </smoothstep>
    <smoothstep name="smoothstep_y" type="float">
      <input name="in" type="float" nodename="separate_in" output="outy"/>
      <input name="low" type="float" nodename="separate_low" output="outy"/>
      <input name="high" type="float" nodename="separate_high" output="outy"/>
    </smoothstep>
    <smoothstep name="smoothstep_z" type="float">
      <input name="in" type="float" nodename="separate_in" output="outz"/>
      <input name="low" type="float" nodename="separate_low" output="outz"/>
      <input name="high" type="float" nodename="separate_high" output="outz"/>
    </smoothstep>
    <smoothstep name="smoothstep_w" type="float">
      <input name="in" type="float" nodename="separate_in" output="outw"/>
      <input name="low" type="float" nodename="separate_low" output="outw"/>
      <input name="high" type="float" nodename="separate_high" output="outw"/>
    </smoothstep>
    <combine4 name="combine" type="vector4">
      <input name="in1" type="float" nodename="smoothstep_x"/>
      <input name="in2" type="float" nodename="smoothstep_y"/>
      <input name="in3" type="float" nodename="smoothstep_z"/>
      <input name="in4" type="float" nodename="smoothstep_w"/>
    </combine4>
    <output name="out" type="vector4" nodename="combine"/>
  </nodegraph>
  <nodegraph name="NG_smoothstep_vector2FA" nodedef="ND_smoothstep_vector2FA">
    <separate2 name="separate_in" type="multioutput">
      <input name="in" type="vector2" interfacename="in" />
    </separate2>
    <smoothstep name="smoothstep_x" type="float">
      <input name="in" type="float" nodename="separate_in" output="outx"/>
      <input name="low" type="float" interfacename="low"/>
      <input name="high" type="float" interfacename="high"/>
    </smoothstep>
    <smoothstep name="smoothstep_y" type="float">
      <input name="in" type="float" nodename="separate_in" output="outy"/>
      <input name="low" type="float" interfacename="low"/>
      <input name="high" type="float" interfacename="high"/>
    </smoothstep>
    <combine2 name="combine" type="vector2">
      <input name="in1" type="float" nodename="smoothstep_x"/>
      <input name="in2" type="float" nodename="smoothstep_y"/>
    </combine2>
    <output name="out" type="vector2" nodename="combine"/>
  </nodegraph>
  <nodegraph name="NG_smoothstep_vector3FA" nodedef="ND_smoothstep_vector3FA">
    <separate3 name="separate_in" type="multioutput">
      <input name="in" type="vector3" interfacename="in" />
    </separate3>
    <smoothstep name="smoothstep_x" type="float">
      <input name="in" type="float" nodename="separate_in" output="outx"/>
      <input name="low" type="float" interfacename="low"/>
      <input name="high" type="float" interfacename="high"/>
    </smoothstep>
    <smoothstep name="smoothstep_y" type="float">
      <input name="in" type="float" nodename="separate_in" output="outy"/>
      <input name="low" type="float" interfacename="low"/>
      <input name="high" type="float" interfacename="high"/>
    </smoothstep>
    <smoothstep name="smoothstep_z" type="float">
      <input name="in" type="float" nodename="separate_in" output="outz"/>
      <input name="low" type="float" interfacename="low"/>
      <input name="high" type="float" interfacename="high"/>
    </smoothstep>
    <combine3 name="combine" type="vector3">
      <input name="in1" type="float" nodename="smoothstep_x"/>
      <input name="in2" type="float" nodename="smoothstep_y"/>
      <input name="in3" type="float" nodename="smoothstep_z"/>
    </combine3>
    <output name="out" type="vector3" nodename="combine"/>
  </nodegraph>
  <nodegraph name="NG_smoothstep_vector4FA" nodedef="ND_smoothstep_vector4FA">
    <separate4 name="separate_in" type="multioutput">
      <input name="in" type="vector4" interfacename="in" />
    </separate4>
    <smoothstep name="smoothstep_x" type="float">
      <input name="in" type="float" nodename="separate_in" output="outx"/>
      <input name="low" type="float" interfacename="low"/>
      <input name="high" type="float" interfacename="high"/>
    </smoothstep>
    <smoothstep name="smoothstep_y" type="float">
      <input name="in" type="float" nodename="separate_in" output="outy"/>
      <input name="low" type="float" interfacename="low"/>
      <input name="high" type="float" interfacename="high"/>
    </smoothstep>
    <smoothstep name="smoothstep_z" type="float">
      <input name="in" type="float" nodename="separate_in" output="outz"/>
      <input name="low" type="float" interfacename="low"/>
      <input name="high" type="float" interfacename="high"/>
    </smoothstep>
    <smoothstep name="smoothstep_w" type="float">
      <input name="in" type="float" nodename="separate_in" output="outw"/>
      <input name="low" type="float" interfacename="low"/>
      <input name="high" type="float" interfacename="high"/>
    </smoothstep>
    <combine4 name="combine" type="vector4">
      <input name="in1" type="float" nodename="smoothstep_x"/>
      <input name="in2" type="float" nodename="smoothstep_y"/>
      <input name="in3" type="float" nodename="smoothstep_z"/>
      <input name="in4" type="float" nodename="smoothstep_w"/>
    </combine4>
    <output name="out" type="vector4" nodename="combine"/>
  </nodegraph>

  <!--
    Node: <safepower>
    Raise incoming half/float/color/vector values to the "in2" power.
    Negative "in1" values will result in negative output values. ie. out = sign(in1)*pow(abs(in1),in2)
  -->
  <nodegraph name="NG_safepower_float" nodedef="ND_safepower_float">
    <sign name="sign_in1" type="float">
      <input name="in" type="float" interfacename="in1" />
    </sign>
    <absval name="abs_in1" type="float">
      <input name="in" type="float" interfacename="in1" />
    </absval>
    <power name="power" type="float">
      <input name="in1" type="float" nodename="abs_in1" />
      <input name="in2" type="float" interfacename="in2" />
    </power>
    <multiply name="safepower" type="float">
      <input name="in1" type="float" nodename="sign_in1" />
      <input name="in2" type="float" nodename="power" />
    </multiply>
    <output name="out" type="float" nodename="safepower" />
  </nodegraph>
  <nodegraph name="NG_safepower_color3" nodedef="ND_safepower_color3">
    <sign name="sign_in1" type="color3">
      <input name="in" type="color3" interfacename="in1" />
    </sign>
    <absval name="abs_in1" type="color3">
      <input name="in" type="color3" interfacename="in1" />
    </absval>
    <power name="power" type="color3">
      <input name="in1" type="color3" nodename="abs_in1" />
      <input name="in2" type="color3" interfacename="in2" />
    </power>
    <multiply name="safepower" type="color3">
      <input name="in1" type="color3" nodename="sign_in1" />
      <input name="in2" type="color3" nodename="power" />
    </multiply>
    <output name="out" type="color3" nodename="safepower" />
  </nodegraph>
  <nodegraph name="NG_safepower_color4" nodedef="ND_safepower_color4">
    <sign name="sign_in1" type="color4">
      <input name="in" type="color4" interfacename="in1" />
    </sign>
    <absval name="abs_in1" type="color4">
      <input name="in" type="color4" interfacename="in1" />
    </absval>
    <power name="power" type="color4">
      <input name="in1" type="color4" nodename="abs_in1" />
      <input name="in2" type="color4" interfacename="in2" />
    </power>
    <multiply name="safepower" type="color4">
      <input name="in1" type="color4" nodename="sign_in1" />
      <input name="in2" type="color4" nodename="power" />
    </multiply>
    <output name="out" type="color4" nodename="safepower" />
  </nodegraph>
  <nodegraph name="NG_safepower_vector2" nodedef="ND_safepower_vector2">
    <sign name="sign_in1" type="vector2">
      <input name="in" type="vector2" interfacename="in1" />
    </sign>
    <absval name="abs_in1" type="vector2">
      <input name="in" type="vector2" interfacename="in1" />
    </absval>
    <power name="power" type="vector2">
      <input name="in1" type="vector2" nodename="abs_in1" />
      <input name="in2" type="vector2" interfacename="in2" />
    </power>
    <multiply name="safepower" type="vector2">
      <input name="in1" type="vector2" nodename="sign_in1" />
      <input name="in2" type="vector2" nodename="power" />
    </multiply>
    <output name="out" type="vector2" nodename="safepower" />
  </nodegraph>
  <nodegraph name="NG_safepower_vector3" nodedef="ND_safepower_vector3">
    <sign name="sign_in1" type="vector3">
      <input name="in" type="vector3" interfacename="in1" />
    </sign>
    <absval name="abs_in1" type="vector3">
      <input name="in" type="vector3" interfacename="in1" />
    </absval>
    <power name="power" type="vector3">
      <input name="in1" type="vector3" nodename="abs_in1" />
      <input name="in2" type="vector3" interfacename="in2" />
    </power>
    <multiply name="safepower" type="vector3">
      <input name="in1" type="vector3" nodename="sign_in1" />
      <input name="in2" type="vector3" nodename="power" />
    </multiply>
    <output name="out" type="vector3" nodename="safepower" />
  </nodegraph>
  <nodegraph name="NG_safepower_vector4" nodedef="ND_safepower_vector4">
    <sign name="sign_in1" type="vector4">
      <input name="in" type="vector4" interfacename="in1" />
    </sign>
    <absval name="abs_in1" type="vector4">
      <input name="in" type="vector4" interfacename="in1" />
    </absval>
    <power name="power" type="vector4">
      <input name="in1" type="vector4" nodename="abs_in1" />
      <input name="in2" type="vector4" interfacename="in2" />
    </power>
    <multiply name="safepower" type="vector4">
      <input name="in1" type="vector4" nodename="sign_in1" />
      <input name="in2" type="vector4" nodename="power" />
    </multiply>
    <output name="out" type="vector4" nodename="safepower" />
  </nodegraph>
  <nodegraph name="NG_safepower_color3FA" nodedef="ND_safepower_color3FA">
    <sign name="sign_in1" type="color3">
      <input name="in" type="color3" interfacename="in1" />
    </sign>
    <absval name="abs_in1" type="color3">
      <input name="in" type="color3" interfacename="in1" />
    </absval>
    <power name="power" type="color3">
      <input name="in1" type="color3" nodename="abs_in1" />
      <input name="in2" type="float" interfacename="in2" />
    </power>
    <multiply name="safepower" type="color3">
      <input name="in1" type="color3" nodename="sign_in1" />
      <input name="in2" type="color3" nodename="power" />
    </multiply>
    <output name="out" type="color3" nodename="safepower" />
  </nodegraph>
  <nodegraph name="NG_safepower_color4FA" nodedef="ND_safepower_color4FA">
    <sign name="sign_in1" type="color4">
      <input name="in" type="color4" interfacename="in1" />
    </sign>
    <absval name="abs_in1" type="color4">
      <input name="in" type="color4" interfacename="in1" />
    </absval>
    <power name="power" type="color4">
      <input name="in1" type="color4" nodename="abs_in1" />
      <input name="in2" type="float" interfacename="in2" />
    </power>
    <multiply name="safepower" type="color4">
      <input name="in1" type="color4" nodename="sign_in1" />
      <input name="in2" type="color4" nodename="power" />
    </multiply>
    <output name="out" type="color4" nodename="safepower" />
  </nodegraph>
  <nodegraph name="NG_safepower_vector2FA" nodedef="ND_safepower_vector2FA">
    <sign name="sign_in1" type="vector2">
      <input name="in" type="vector2" interfacename="in1" />
    </sign>
    <absval name="abs_in1" type="vector2">
      <input name="in" type="vector2" interfacename="in1" />
    </absval>
    <power name="power" type="vector2">
      <input name="in1" type="vector2" nodename="abs_in1" />
      <input name="in2" type="float" interfacename="in2" />
    </power>
    <multiply name="safepower" type="vector2">
      <input name="in1" type="vector2" nodename="sign_in1" />
      <input name="in2" type="vector2" nodename="power" />
    </multiply>
    <output name="out" type="vector2" nodename="safepower" />
  </nodegraph>
  <nodegraph name="NG_safepower_vector3FA" nodedef="ND_safepower_vector3FA">
    <sign name="sign_in1" type="vector3">
      <input name="in" type="vector3" interfacename="in1" />
    </sign>
    <absval name="abs_in1" type="vector3">
      <input name="in" type="vector3" interfacename="in1" />
    </absval>
    <power name="power" type="vector3">
      <input name="in1" type="vector3" nodename="abs_in1" />
      <input name="in2" type="float" interfacename="in2" />
    </power>
    <multiply name="safepower" type="vector3">
      <input name="in1" type="vector3" nodename="sign_in1" />
      <input name="in2" type="vector3" nodename="power" />
    </multiply>
    <output name="out" type="vector3" nodename="safepower" />
  </nodegraph>
  <nodegraph name="NG_safepower_vector4FA" nodedef="ND_safepower_vector4FA">
    <sign name="sign_in1" type="vector4">
      <input name="in" type="vector4" interfacename="in1" />
    </sign>
    <absval name="abs_in1" type="vector4">
      <input name="in" type="vector4" interfacename="in1" />
    </absval>
    <power name="power" type="vector4">
      <input name="in1" type="vector4" nodename="abs_in1" />
      <input name="in2" type="float" interfacename="in2" />
    </power>
    <multiply name="safepower" type="vector4">
      <input name="in1" type="vector4" nodename="sign_in1" />
      <input name="in2" type="vector4" nodename="power" />
    </multiply>
    <output name="out" type="vector4" nodename="safepower" />
  </nodegraph>

  <!--
    Node: <contrast>
    Increase or decrease contrast of a float/color value using a linear slope multiplier.
  -->
  <nodegraph name="NG_contrast_float" nodedef="ND_contrast_float">
    <subtract name="N_sub_float" type="float">
      <input name="in1" type="float" interfacename="in" />
      <input name="in2" type="float" interfacename="pivot" />
    </subtract>
    <multiply name="N_mul_float" type="float">
      <input name="in1" type="float" nodename="N_sub_float" />
      <input name="in2" type="float" interfacename="amount" />
    </multiply>
    <add name="N_add_float" type="float">
      <input name="in1" type="float" nodename="N_mul_float" />
      <input name="in2" type="float" interfacename="pivot" />
    </add>
    <output name="out" type="float" nodename="N_add_float" />
  </nodegraph>
  <nodegraph name="NG_contrast_color3" nodedef="ND_contrast_color3">
    <subtract name="N_sub_color3" type="color3">
      <input name="in1" type="color3" interfacename="in" />
      <input name="in2" type="color3" interfacename="pivot" />
    </subtract>
    <multiply name="N_mul_color3" type="color3">
      <input name="in1" type="color3" nodename="N_sub_color3" />
      <input name="in2" type="color3" interfacename="amount" />
    </multiply>
    <add name="N_add_color3" type="color3">
      <input name="in1" type="color3" nodename="N_mul_color3" />
      <input name="in2" type="color3" interfacename="pivot" />
    </add>
    <output name="out" type="color3" nodename="N_add_color3" />
  </nodegraph>
  <nodegraph name="NG_contrast_color4" nodedef="ND_contrast_color4">
    <subtract name="N_sub_color4" type="color4">
      <input name="in1" type="color4" interfacename="in" />
      <input name="in2" type="color4" interfacename="pivot" />
    </subtract>
    <multiply name="N_mul_color4" type="color4">
      <input name="in1" type="color4" nodename="N_sub_color4" />
      <input name="in2" type="color4" interfacename="amount" />
    </multiply>
    <add name="N_add_color4" type="color4">
      <input name="in1" type="color4" nodename="N_mul_color4" />
      <input name="in2" type="color4" interfacename="pivot" />
    </add>
    <output name="out" type="color4" nodename="N_add_color4" />
  </nodegraph>
  <nodegraph name="NG_contrast_vector2" nodedef="ND_contrast_vector2">
    <subtract name="N_sub_vector2" type="vector2">
      <input name="in1" type="vector2" interfacename="in" />
      <input name="in2" type="vector2" interfacename="pivot" />
    </subtract>
    <multiply name="N_mul_vector2" type="vector2">
      <input name="in1" type="vector2" nodename="N_sub_vector2" />
      <input name="in2" type="vector2" interfacename="amount" />
    </multiply>
    <add name="N_add_vector2" type="vector2">
      <input name="in1" type="vector2" nodename="N_mul_vector2" />
      <input name="in2" type="vector2" interfacename="pivot" />
    </add>
    <output name="out" type="vector2" nodename="N_add_vector2" />
  </nodegraph>
  <nodegraph name="NG_contrast_vector3" nodedef="ND_contrast_vector3">
    <subtract name="N_sub_vector3" type="vector3">
      <input name="in1" type="vector3" interfacename="in" />
      <input name="in2" type="vector3" interfacename="pivot" />
    </subtract>
    <multiply name="N_mul_vector3" type="vector3">
      <input name="in1" type="vector3" nodename="N_sub_vector3" />
      <input name="in2" type="vector3" interfacename="amount" />
    </multiply>
    <add name="N_add_vector3" type="vector3">
      <input name="in1" type="vector3" nodename="N_mul_vector3" />
      <input name="in2" type="vector3" interfacename="pivot" />
    </add>
    <output name="out" type="vector3" nodename="N_add_vector3" />
  </nodegraph>
  <nodegraph name="NG_contrast_vector4" nodedef="ND_contrast_vector4">
    <subtract name="N_sub_vector4" type="vector4">
      <input name="in1" type="vector4" interfacename="in" />
      <input name="in2" type="vector4" interfacename="pivot" />
    </subtract>
    <multiply name="N_mul_vector4" type="vector4">
      <input name="in1" type="vector4" nodename="N_sub_vector4" />
      <input name="in2" type="vector4" interfacename="amount" />
    </multiply>
    <add name="N_add_vector4" type="vector4">
      <input name="in1" type="vector4" nodename="N_mul_vector4" />
      <input name="in2" type="vector4" interfacename="pivot" />
    </add>
    <output name="out" type="vector4" nodename="N_add_vector4" />
  </nodegraph>
  <nodegraph name="NG_contrast_color3FA" nodedef="ND_contrast_color3FA">
    <subtract name="N_sub_color3FA" type="color3">
      <input name="in1" type="color3" interfacename="in" />
      <input name="in2" type="float" interfacename="pivot" />
    </subtract>
    <multiply name="N_mul_color3FA" type="color3">
      <input name="in1" type="color3" nodename="N_sub_color3FA" />
      <input name="in2" type="float" interfacename="amount" />
    </multiply>
    <add name="N_add_color3FA" type="color3">
      <input name="in1" type="color3" nodename="N_mul_color3FA" />
      <input name="in2" type="float" interfacename="pivot" />
    </add>
    <output name="out" type="color3" nodename="N_add_color3FA" />
  </nodegraph>
  <nodegraph name="NG_contrast_color4FA" nodedef="ND_contrast_color4FA">
    <subtract name="N_sub_color4FA" type="color4">
      <input name="in1" type="color4" interfacename="in" />
      <input name="in2" type="float" interfacename="pivot" />
    </subtract>
    <multiply name="N_mul_color4FA" type="color4">
      <input name="in1" type="color4" nodename="N_sub_color4FA" />
      <input name="in2" type="float" interfacename="amount" />
    </multiply>
    <add name="N_add_color4FA" type="color4">
      <input name="in1" type="color4" nodename="N_mul_color4FA" />
      <input name="in2" type="float" interfacename="pivot" />
    </add>
    <output name="out" type="color4" nodename="N_add_color4FA" />
  </nodegraph>
  <nodegraph name="NG_contrast_vector2FA" nodedef="ND_contrast_vector2FA">
    <subtract name="N_sub_vector2FA" type="vector2">
      <input name="in1" type="vector2" interfacename="in" />
      <input name="in2" type="float" interfacename="pivot" />
    </subtract>
    <multiply name="N_mul_vector2FA" type="vector2">
      <input name="in1" type="vector2" nodename="N_sub_vector2FA" />
      <input name="in2" type="float" interfacename="amount" />
    </multiply>
    <add name="N_add_vector2FA" type="vector2">
      <input name="in1" type="vector2" nodename="N_mul_vector2FA" />
      <input name="in2" type="float" interfacename="pivot" />
    </add>
    <output name="out" type="vector2" nodename="N_add_vector2FA" />
  </nodegraph>
  <nodegraph name="NG_contrast_vector3FA" nodedef="ND_contrast_vector3FA">
    <subtract name="N_sub_vector3FA" type="vector3">
      <input name="in1" type="vector3" interfacename="in" />
      <input name="in2" type="float" interfacename="pivot" />
    </subtract>
    <multiply name="N_mul_vector3FA" type="vector3">
      <input name="in1" type="vector3" nodename="N_sub_vector3FA" />
      <input name="in2" type="float" interfacename="amount" />
    </multiply>
    <add name="N_add_vector3FA" type="vector3">
      <input name="in1" type="vector3" nodename="N_mul_vector3FA" />
      <input name="in2" type="float" interfacename="pivot" />
    </add>
    <output name="out" type="vector3" nodename="N_add_vector3FA" />
  </nodegraph>
  <nodegraph name="NG_contrast_vector4FA" nodedef="ND_contrast_vector4FA">
    <subtract name="N_sub_vector4FA" type="vector4">
      <input name="in1" type="vector4" interfacename="in" />
      <input name="in2" type="float" interfacename="pivot" />
    </subtract>
    <multiply name="N_mul_vector4FA" type="vector4">
      <input name="in1" type="vector4" nodename="N_sub_vector4FA" />
      <input name="in2" type="float" interfacename="amount" />
    </multiply>
    <add name="N_add_vector4FA" type="vector4">
      <input name="in1" type="vector4" nodename="N_mul_vector4FA" />
      <input name="in2" type="float" interfacename="pivot" />
    </add>
    <output name="out" type="vector4" nodename="N_add_vector4FA" />
  </nodegraph>

  <!--
    Node: <range>
    Remap a value from one range of float/color/vector values to another, optionally
    applying a gamma correction in the middle, and optionally clamping output values.
  -->
  <nodegraph name="NG_range_float" nodedef="ND_range_float">
    <remap name="N_remap1_float" type="float">
      <input name="in" type="float" interfacename="in" />
      <input name="inlow" type="float" interfacename="inlow" />
      <input name="inhigh" type="float" interfacename="inhigh" />
      <input name="outlow" type="float" value="0.0" />
      <input name="outhigh" type="float" value="1.0" />
    </remap>
    <divide name="N_recip_float" type="float">
      <input name="in1" type="float" value="1.0" />
      <input name="in2" type="float" interfacename="gamma" />
    </divide>
    <absval name="N_abs_float" type="float">
      <input name="in" type="float" nodename="N_remap1_float" />
    </absval>
    <power name="N_pow_float" type="float">
      <input name="in1" type="float" nodename="N_abs_float" />
      <input name="in2" type="float" nodename="N_recip_float" />
    </power>
    <sign name="N_sign_float" type="float">
      <input name="in" type="float" nodename="N_remap1_float" />
    </sign>
    <multiply name="N_gamma_float" type="float">
      <input name="in1" type="float" nodename="N_pow_float" />
      <input name="in2" type="float" nodename="N_sign_float" />
    </multiply>
    <remap name="N_remap2_float" type="float">
      <input name="in" type="float" nodename="N_gamma_float" />
      <input name="inlow" type="float" value="0.0" />
      <input name="inhigh" type="float" value="1.0" />
      <input name="outlow" type="float" interfacename="outlow" />
      <input name="outhigh" type="float" interfacename="outhigh" />
    </remap>
    <clamp name="N_clamp_float" type="float">
      <input name="in" type="float" nodename="N_remap2_float" />
      <input name="low" type="float" interfacename="outlow" />
      <input name="high" type="float" interfacename="outhigh" />
    </clamp>
    <ifequal name="N_switch_float" type="float">
      <input name="in1" type="float" nodename="N_clamp_float" />
      <input name="in2" type="float" nodename="N_remap2_float" />
      <input name="value1" type="boolean" interfacename="doclamp" />
      <input name="value2" type="boolean" value="true" />
    </ifequal>
    <output name="out" type="float" nodename="N_switch_float" />
  </nodegraph>
  <nodegraph name="NG_range_color3" nodedef="ND_range_color3">
    <remap name="N_remap1_color3" type="color3">
      <input name="in" type="color3" interfacename="in" />
      <input name="inlow" type="color3" interfacename="inlow" />
      <input name="inhigh" type="color3" interfacename="inhigh" />
      <input name="outlow" type="color3" value="0.0, 0.0, 0.0" />
      <input name="outhigh" type="color3" value="1.0, 1.0, 1.0" />
    </remap>
    <divide name="N_recip_color3" type="color3">
      <input name="in1" type="color3" value="1.0, 1.0, 1.0" />
      <input name="in2" type="color3" interfacename="gamma" />
    </divide>
    <absval name="N_abs_color3" type="color3">
      <input name="in" type="color3" nodename="N_remap1_color3" />
    </absval>
    <power name="N_pow_color3" type="color3">
      <input name="in1" type="color3" nodename="N_abs_color3" />
      <input name="in2" type="color3" nodename="N_recip_color3" />
    </power>
    <sign name="N_sign_color3" type="color3">
      <input name="in" type="color3" nodename="N_remap1_color3" />
    </sign>
    <multiply name="N_gamma_color3" type="color3">
      <input name="in1" type="color3" nodename="N_pow_color3" />
      <input name="in2" type="color3" nodename="N_sign_color3" />
    </multiply>
    <remap name="N_remap2_color3" type="color3">
      <input name="in" type="color3" nodename="N_gamma_color3" />
      <input name="inlow" type="color3" value="0.0, 0.0, 0.0" />
      <input name="inhigh" type="color3" value="1.0, 1.0, 1.0" />
      <input name="outlow" type="color3" interfacename="outlow" />
      <input name="outhigh" type="color3" interfacename="outhigh" />
    </remap>
    <clamp name="N_clamp_color3" type="color3">
      <input name="in" type="color3" nodename="N_remap2_color3" />
      <input name="low" type="color3" interfacename="outlow" />
      <input name="high" type="color3" interfacename="outhigh" />
    </clamp>
    <ifequal name="N_switch_color3" type="color3">
      <input name="in1" type="color3" nodename="N_clamp_color3" />
      <input name="in2" type="color3" nodename="N_remap2_color3" />
      <input name="value1" type="boolean" interfacename="doclamp" />
      <input name="value2" type="boolean" value="true" />
    </ifequal>
    <output name="out" type="color3" nodename="N_switch_color3" />
  </nodegraph>
  <nodegraph name="NG_range_color4" nodedef="ND_range_color4">
    <remap name="N_remap1_color4" type="color4">
      <input name="in" type="color4" interfacename="in" />
      <input name="inlow" type="color4" interfacename="inlow" />
      <input name="inhigh" type="color4" interfacename="inhigh" />
      <input name="outlow" type="color4" value="0.0, 0.0, 0.0, 0.0" />
      <input name="outhigh" type="color4" value="1.0, 1.0, 1.0, 1.0" />
    </remap>
    <divide name="N_recip_color4" type="color4">
      <input name="in1" type="color4" value="1.0, 1.0, 1.0, 1.0" />
      <input name="in2" type="color4" interfacename="gamma" />
    </divide>
    <absval name="N_abs_color4" type="color4">
      <input name="in" type="color4" nodename="N_remap1_color4" />
    </absval>
    <power name="N_pow_color4" type="color4">
      <input name="in1" type="color4" nodename="N_abs_color4" />
      <input name="in2" type="color4" nodename="N_recip_color4" />
    </power>
    <sign name="N_sign_color4" type="color4">
      <input name="in" type="color4" nodename="N_remap1_color4" />
    </sign>
    <multiply name="N_gamma_color4" type="color4">
      <input name="in1" type="color4" nodename="N_pow_color4" />
      <input name="in2" type="color4" nodename="N_sign_color4" />
    </multiply>
    <remap name="N_remap2_color4" type="color4">
      <input name="in" type="color4" nodename="N_gamma_color4" />
      <input name="inlow" type="color4" value="0.0, 0.0, 0.0, 0.0" />
      <input name="inhigh" type="color4" value="1.0, 1.0, 1.0, 1.0" />
      <input name="outlow" type="color4" interfacename="outlow" />
      <input name="outhigh" type="color4" interfacename="outhigh" />
    </remap>
    <clamp name="N_clamp_color4" type="color4">
      <input name="in" type="color4" nodename="N_remap2_color4" />
      <input name="low" type="color4" interfacename="outlow" />
      <input name="high" type="color4" interfacename="outhigh" />
    </clamp>
    <ifequal name="N_switch_color4" type="color4">
      <input name="in1" type="color4" nodename="N_clamp_color4" />
      <input name="in2" type="color4" nodename="N_remap2_color4" />
      <input name="value1" type="boolean" interfacename="doclamp" />
      <input name="value2" type="boolean" value="true" />
    </ifequal>
    <output name="out" type="color4" nodename="N_switch_color4" />
  </nodegraph>
  <nodegraph name="NG_range_vector2" nodedef="ND_range_vector2">
    <remap name="N_remap1_vector2" type="vector2">
      <input name="in" type="vector2" interfacename="in" />
      <input name="inlow" type="vector2" interfacename="inlow" />
      <input name="inhigh" type="vector2" interfacename="inhigh" />
      <input name="outlow" type="vector2" value="0.0, 0.0" />
      <input name="outhigh" type="vector2" value="1.0, 1.0" />
    </remap>
    <divide name="N_recip_vector2" type="vector2">
      <input name="in1" type="vector2" value="1.0, 1.0" />
      <input name="in2" type="vector2" interfacename="gamma" />
    </divide>
    <absval name="N_abs_vector2" type="vector2">
      <input name="in" type="vector2" nodename="N_remap1_vector2" />
    </absval>
    <power name="N_pow_vector2" type="vector2">
      <input name="in1" type="vector2" nodename="N_abs_vector2" />
      <input name="in2" type="vector2" nodename="N_recip_vector2" />
    </power>
    <sign name="N_sign_vector2" type="vector2">
      <input name="in" type="vector2" nodename="N_remap1_vector2" />
    </sign>
    <multiply name="N_gamma_vector2" type="vector2">
      <input name="in1" type="vector2" nodename="N_pow_vector2" />
      <input name="in2" type="vector2" nodename="N_sign_vector2" />
    </multiply>
    <remap name="N_remap2_vector2" type="vector2">
      <input name="in" type="vector2" nodename="N_gamma_vector2" />
      <input name="inlow" type="vector2" value="0.0, 0.0" />
      <input name="inhigh" type="vector2" value="1.0, 1.0" />
      <input name="outlow" type="vector2" interfacename="outlow" />
      <input name="outhigh" type="vector2" interfacename="outhigh" />
    </remap>
    <clamp name="N_clamp_vector2" type="vector2">
      <input name="in" type="vector2" nodename="N_remap2_vector2" />
      <input name="low" type="vector2" interfacename="outlow" />
      <input name="high" type="vector2" interfacename="outhigh" />
    </clamp>
    <ifequal name="N_switch_vector2" type="vector2">
      <input name="in1" type="vector2" nodename="N_clamp_vector2" />
      <input name="in2" type="vector2" nodename="N_remap2_vector2" />
      <input name="value1" type="boolean" interfacename="doclamp" />
      <input name="value2" type="boolean" value="true" />
    </ifequal>
    <output name="out" type="vector2" nodename="N_switch_vector2" />
  </nodegraph>
  <nodegraph name="NG_range_vector3" nodedef="ND_range_vector3">
    <remap name="N_remap1_vector3" type="vector3">
      <input name="in" type="vector3" interfacename="in" />
      <input name="inlow" type="vector3" interfacename="inlow" />
      <input name="inhigh" type="vector3" interfacename="inhigh" />
      <input name="outlow" type="vector3" value="0.0, 0.0, 0.0" />
      <input name="outhigh" type="vector3" value="1.0, 1.0, 1.0" />
    </remap>
    <divide name="N_recip_vector3" type="vector3">
      <input name="in1" type="vector3" value="1.0, 1.0, 1.0" />
      <input name="in2" type="vector3" interfacename="gamma" />
    </divide>
    <absval name="N_abs_vector3" type="vector3">
      <input name="in" type="vector3" nodename="N_remap1_vector3" />
    </absval>
    <power name="N_pow_vector3" type="vector3">
      <input name="in1" type="vector3" nodename="N_abs_vector3" />
      <input name="in2" type="vector3" nodename="N_recip_vector3" />
    </power>
    <sign name="N_sign_vector3" type="vector3">
      <input name="in" type="vector3" nodename="N_remap1_vector3" />
    </sign>
    <multiply name="N_gamma_vector3" type="vector3">
      <input name="in1" type="vector3" nodename="N_pow_vector3" />
      <input name="in2" type="vector3" nodename="N_sign_vector3" />
    </multiply>
    <remap name="N_remap2_vector3" type="vector3">
      <input name="in" type="vector3" nodename="N_gamma_vector3" />
      <input name="inlow" type="vector3" value="0.0, 0.0, 0.0" />
      <input name="inhigh" type="vector3" value="1.0, 1.0, 1.0" />
      <input name="outlow" type="vector3" interfacename="outlow" />
      <input name="outhigh" type="vector3" interfacename="outhigh" />
    </remap>
    <clamp name="N_clamp_vector3" type="vector3">
      <input name="in" type="vector3" nodename="N_remap2_vector3" />
      <input name="low" type="vector3" interfacename="outlow" />
      <input name="high" type="vector3" interfacename="outhigh" />
    </clamp>
    <ifequal name="N_switch_vector3" type="vector3">
      <input name="in1" type="vector3" nodename="N_clamp_vector3" />
      <input name="in2" type="vector3" nodename="N_remap2_vector3" />
      <input name="value1" type="boolean" interfacename="doclamp" />
      <input name="value2" type="boolean" value="true" />
    </ifequal>
    <output name="out" type="vector3" nodename="N_switch_vector3" />
  </nodegraph>
  <nodegraph name="NG_range_vector4" nodedef="ND_range_vector4">
    <remap name="N_remap1_vector4" type="vector4">
      <input name="in" type="vector4" interfacename="in" />
      <input name="inlow" type="vector4" interfacename="inlow" />
      <input name="inhigh" type="vector4" interfacename="inhigh" />
      <input name="outlow" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
      <input name="outhigh" type="vector4" value="1.0, 1.0, 1.0, 1.0" />
    </remap>
    <divide name="N_recip_vector4" type="vector4">
      <input name="in1" type="vector4" value="1.0, 1.0, 1.0, 1.0" />
      <input name="in2" type="vector4" interfacename="gamma" />
    </divide>
    <absval name="N_abs_vector4" type="vector4">
      <input name="in" type="vector4" nodename="N_remap1_vector4" />
    </absval>
    <power name="N_pow_vector4" type="vector4">
      <input name="in1" type="vector4" nodename="N_abs_vector4" />
      <input name="in2" type="vector4" nodename="N_recip_vector4" />
    </power>
    <sign name="N_sign_vector4" type="vector4">
      <input name="in" type="vector4" nodename="N_remap1_vector4" />
    </sign>
    <multiply name="N_gamma_vector4" type="vector4">
      <input name="in1" type="vector4" nodename="N_pow_vector4" />
      <input name="in2" type="vector4" nodename="N_sign_vector4" />
    </multiply>
    <remap name="N_remap2_vector4" type="vector4">
      <input name="in" type="vector4" nodename="N_gamma_vector4" />
      <input name="inlow" type="vector4" value="0.0, 0.0, 0.0, 0.0" />
      <input name="inhigh" type="vector4" value="1.0, 1.0, 1.0, 1.0" />
      <input name="outlow" type="vector4" interfacename="outlow" />
      <input name="outhigh" type="vector4" interfacename="outhigh" />
    </remap>
    <clamp name="N_clamp_vector4" type="vector4">
      <input name="in" type="vector4" nodename="N_remap2_vector4" />
      <input name="low" type="vector4" interfacename="outlow" />
      <input name="high" type="vector4" interfacename="outhigh" />
    </clamp>
    <ifequal name="N_switch_vector4" type="vector4">
      <input name="in1" type="vector4" nodename="N_clamp_vector4" />
      <input name="in2" type="vector4" nodename="N_remap2_vector4" />
      <input name="value1" type="boolean" interfacename="doclamp" />
      <input name="value2" type="boolean" value="true" />
    </ifequal>
    <output name="out" type="vector4" nodename="N_switch_vector4" />
  </nodegraph>
  <nodegraph name="NG_range_color3FA" nodedef="ND_range_color3FA">
    <remap name="N_remap1_color3FA" type="color3">
      <input name="in" type="color3" interfacename="in" />
      <input name="inlow" type="float" interfacename="inlow" />
      <input name="inhigh" type="float" interfacename="inhigh" />
      <input name="outlow" type="float" value="0.0" />
      <input name="outhigh" type="float" value="1.0" />
    </remap>
    <divide name="N_recip_color3FA" type="float">
      <input name="in1" type="float" value="1.0" />
      <input name="in2" type="float" interfacename="gamma" />
    </divide>
    <absval name="N_abs_color3FA" type="color3">
      <input name="in" type="color3" nodename="N_remap1_color3FA" />
    </absval>
    <power name="N_pow_color3FA" type="color3">
      <input name="in1" type="color3" nodename="N_abs_color3FA" />
      <input name="in2" type="float" nodename="N_recip_color3FA" />
    </power>
    <sign name="N_sign_color3FA" type="color3">
      <input name="in" type="color3" nodename="N_remap1_color3FA" />
    </sign>
    <multiply name="N_gamma_color3FA" type="color3">
      <input name="in1" type="color3" nodename="N_pow_color3FA" />
      <input name="in2" type="color3" nodename="N_sign_color3FA" />
    </multiply>
    <remap name="N_remap2_color3FA" type="color3">
      <input name="in" type="color3" nodename="N_gamma_color3FA" />
      <input name="inlow" type="float" value="0.0" />
      <input name="inhigh" type="float" value="1.0" />
      <input name="outlow" type="float" interfacename="outlow" />
      <input name="outhigh" type="float" interfacename="outhigh" />
    </remap>
    <clamp name="N_clamp_color3FA" type="color3">
      <input name="in" type="color3" nodename="N_remap2_color3FA" />
      <input name="low" type="float" interfacename="outlow" />
      <input name="high" type="float" interfacename="outhigh" />
    </clamp>
    <ifequal name="N_switch_color3FA" type="color3">
      <input name="in1" type="color3" nodename="N_clamp_color3FA" />
      <input name="in2" type="color3" nodename="N_remap2_color3FA" />
      <input name="value1" type="boolean" interfacename="doclamp" />
      <input name="value2" type="boolean" value="true" />
    </ifequal>
    <output name="out" type="color3" nodename="N_switch_color3FA" />
  </nodegraph>
  <nodegraph name="NG_range_color4FA" nodedef="ND_range_color4FA">
    <remap name="N_remap1_color4FA" type="color4">
      <input name="in" type="color4" interfacename="in" />
      <input name="inlow" type="float" interfacename="inlow" />
      <input name="inhigh" type="float" interfacename="inhigh" />
      <input name="outlow" type="float" value="0.0" />
      <input name="outhigh" type="float" value="1.0" />
    </remap>
    <divide name="N_recip_color4FA" type="float">
      <input name="in1" type="float" value="1.0" />
      <input name="in2" type="float" interfacename="gamma" />
    </divide>
    <absval name="N_abs_color4FA" type="color4">
      <input name="in" type="color4" nodename="N_remap1_color4FA" />
    </absval>
    <power name="N_pow_color4FA" type="color4">
      <input name="in1" type="color4" nodename="N_abs_color4FA" />
      <input name="in2" type="float" nodename="N_recip_color4FA" />
    </power>
    <sign name="N_sign_color4FA" type="color4">
      <input name="in" type="color4" nodename="N_remap1_color4FA" />
    </sign>
    <multiply name="N_gamma_color4FA" type="color4">
      <input name="in1" type="color4" nodename="N_pow_color4FA" />
      <input name="in2" type="color4" nodename="N_sign_color4FA" />
    </multiply>
    <remap name="N_remap2_color4FA" type="color4">
      <input name="in" type="color4" nodename="N_gamma_color4FA" />
      <input name="inlow" type="float" value="0.0" />
      <input name="inhigh" type="float" value="1.0" />
      <input name="outlow" type="float" interfacename="outlow" />
      <input name="outhigh" type="float" interfacename="outhigh" />
    </remap>
    <clamp name="N_clamp_color4FA" type="color4">
      <input name="in" type="color4" nodename="N_remap2_color4FA" />
      <input name="low" type="float" interfacename="outlow" />
      <input name="high" type="float" interfacename="outhigh" />
    </clamp>
    <ifequal name="N_switch_color4FA" type="color4">
      <input name="in1" type="color4" nodename="N_clamp_color4FA" />
      <input name="in2" type="color4" nodename="N_remap2_color4FA" />
      <input name="value1" type="boolean" interfacename="doclamp" />
      <input name="value2" type="boolean" value="true" />
    </ifequal>
    <output name="out" type="color4" nodename="N_switch_color4FA" />
  </nodegraph>
  <nodegraph name="NG_range_vector2FA" nodedef="ND_range_vector2FA">
    <remap name="N_remap1_vector2FA" type="vector2">
      <input name="in" type="vector2" interfacename="in" />
      <input name="inlow" type="float" interfacename="inlow" />
      <input name="inhigh" type="float" interfacename="inhigh" />
      <input name="outlow" type="float" value="0.0" />
      <input name="outhigh" type="float" value="1.0" />
    </remap>
    <divide name="N_recip_vector2FA" type="float">
      <input name="in1" type="float" value="1.0" />
      <input name="in2" type="float" interfacename="gamma" />
    </divide>
    <absval name="N_abs_vector2FA" type="vector2">
      <input name="in" type="vector2" nodename="N_remap1_vector2FA" />
    </absval>
    <power name="N_pow_vector2FA" type="vector2">
      <input name="in1" type="vector2" nodename="N_abs_vector2FA" />
      <input name="in2" type="float" nodename="N_recip_vector2FA" />
    </power>
    <sign name="N_sign_vector2FA" type="vector2">
      <input name="in" type="vector2" nodename="N_remap1_vector2FA" />
    </sign>
    <multiply name="N_gamma_vector2FA" type="vector2">
      <input name="in1" type="vector2" nodename="N_pow_vector2FA" />
      <input name="in2" type="vector2" nodename="N_sign_vector2FA" />
    </multiply>
    <remap name="N_remap2_vector2FA" type="vector2">
      <input name="in" type="vector2" nodename="N_gamma_vector2FA" />
      <input name="inlow" type="float" value="0.0" />
      <input name="inhigh" type="float" value="1.0" />
      <input name="outlow" type="float" interfacename="outlow" />
      <input name="outhigh" type="float" interfacename="outhigh" />
    </remap>
    <clamp name="N_clamp_vector2FA" type="vector2">
      <input name="in" type="vector2" nodename="N_remap2_vector2FA" />
      <input name="low" type="float" interfacename="outlow" />
      <input name="high" type="float" interfacename="outhigh" />
    </clamp>
    <ifequal name="N_switch_vector2FA" type="vector2">
      <input name="in1" type="vector2" nodename="N_clamp_vector2FA" />
      <input name="in2" type="vector2" nodename="N_remap2_vector2FA" />
      <input name="value1" type="boolean" interfacename="doclamp" />
      <input name="value2" type="boolean" value="true" />
    </ifequal>
    <output name="out" type="vector2" nodename="N_switch_vector2FA" />
  </nodegraph>
  <nodegraph name="NG_range_vector3FA" nodedef="ND_range_vector3FA">
    <remap name="N_remap1_vector3FA" type="vector3">
      <input name="in" type="vector3" interfacename="in" />
      <input name="inlow" type="float" interfacename="inlow" />
      <input name="inhigh" type="float" interfacename="inhigh" />
      <input name="outlow" type="float" value="0.0" />
      <input name="outhigh" type="float" value="1.0" />
    </remap>
    <divide name="N_recip_vector3FA" type="float">
      <input name="in1" type="float" value="1.0" />
      <input name="in2" type="float" interfacename="gamma" />
    </divide>
    <absval name="N_abs_vector3FA" type="vector3">
      <input name="in" type="vector3" nodename="N_remap1_vector3FA" />
    </absval>
    <power name="N_pow_vector3FA" type="vector3">
      <input name="in1" type="vector3" nodename="N_abs_vector3FA" />
      <input name="in2" type="float" nodename="N_recip_vector3FA" />
    </power>
    <sign name="N_sign_vector3FA" type="vector3">
      <input name="in" type="vector3" nodename="N_remap1_vector3FA" />
    </sign>
    <multiply name="N_gamma_vector3FA" type="vector3">
      <input name="in1" type="vector3" nodename="N_pow_vector3FA" />
      <input name="in2" type="vector3" nodename="N_sign_vector3FA" />
    </multiply>
    <remap name="N_remap2_vector3FA" type="vector3">
      <input name="in" type="vector3" nodename="N_gamma_vector3FA" />
      <input name="inlow" type="float" value="0.0" />
      <input name="inhigh" type="float" value="1.0" />
      <input name="outlow" type="float" interfacename="outlow" />
      <input name="outhigh" type="float" interfacename="outhigh" />
    </remap>
    <clamp name="N_clamp_vector3FA" type="vector3">
      <input name="in" type="vector3" nodename="N_remap2_vector3FA" />
      <input name="low" type="float" interfacename="outlow" />
      <input name="high" type="float" interfacename="outhigh" />
    </clamp>
    <ifequal name="N_switch_vector3FA" type="vector3">
      <input name="in1" type="vector3" nodename="N_clamp_vector3FA" />
      <input name="in2" type="vector3" nodename="N_remap2_vector3FA" />
      <input name="value1" type="boolean" interfacename="doclamp" />
      <input name="value2" type="boolean" value="true" />
    </ifequal>
    <output name="out" type="vector3" nodename="N_switch_vector3FA" />
  </nodegraph>
  <nodegraph name="NG_range_vector4FA" nodedef="ND_range_vector4FA">
    <remap name="N_remap1_vector4FA" type="vector4">
      <input name="in" type="vector4" interfacename="in" />
      <input name="inlow" type="float" interfacename="inlow" />
      <input name="inhigh" type="float" interfacename="inhigh" />
      <input name="outlow" type="float" value="0.0" />
      <input name="outhigh" type="float" value="1.0" />
    </remap>
    <divide name="N_recip_vector4FA" type="float">
      <input name="in1" type="float" value="1.0" />
      <input name="in2" type="float" interfacename="gamma" />
    </divide>
    <absval name="N_abs_vector4FA" type="vector4">
      <input name="in" type="vector4" nodename="N_remap1_vector4FA" />
    </absval>
    <power name="N_pow_vector4FA" type="vector4">
      <input name="in1" type="vector4" nodename="N_abs_vector4FA" />
      <input name="in2" type="float" nodename="N_recip_vector4FA" />
    </power>
    <sign name="N_sign_vector4FA" type="vector4">
      <input name="in" type="vector4" nodename="N_remap1_vector4FA" />
    </sign>
    <multiply name="N_gamma_vector4FA" type="vector4">
      <input name="in1" type="vector4" nodename="N_pow_vector4FA" />
      <input name="in2" type="vector4" nodename="N_sign_vector4FA" />
    </multiply>
    <remap name="N_remap2_vector4FA" type="vector4">
      <input name="in" type="vector4" nodename="N_gamma_vector4FA" />
      <input name="inlow" type="float" value="0.0" />
      <input name="inhigh" type="float" value="1.0" />
      <input name="outlow" type="float" interfacename="outlow" />
      <input name="outhigh" type="float" interfacename="outhigh" />
    </remap>
    <clamp name="N_clamp_vector4FA" type="vector4">
      <input name="in" type="vector4" nodename="N_remap2_vector4FA" />
      <input name="low" type="float" interfacename="outlow" />
      <input name="high" type="float" interfacename="outhigh" />
    </clamp>
    <ifequal name="N_switch_vector4FA" type="vector4">
      <input name="in1" type="vector4" nodename="N_clamp_vector4FA" />
      <input name="in2" type="vector4" nodename="N_remap2_vector4FA" />
      <input name="value1" type="boolean" interfacename="doclamp" />
      <input name="value2" type="boolean" value="true" />
    </ifequal>
    <output name="out" type="vector4" nodename="N_switch_vector4FA" />
  </nodegraph>

  <!--
    Node: <hsvadjust>
    Adjust the hue, saturation and value of an RGB color by converting the input color
    to HSV, adding amount.x to the hue, multiplying the saturation by amount.y,
    multiplying the value by amount.z, then converting back to RGB.
  -->
  <nodegraph name="NG_hsvadjust_color3" nodedef="ND_hsvadjust_color3">
    <rgbtohsv name="N_inhsv_color3" type="color3">
      <input name="in" type="color3" interfacename="in" />
    </rgbtohsv>
    <convert name="N_camount_color3" type="color3">
      <input name="in" type="vector3" interfacename="amount" />
    </convert>
    <multiply name="N_hchans_color3" type="color3">
      <input name="in1" type="color3" nodename="N_camount_color3" />
      <input name="in2" type="color3" value="1.0, 0.0, 0.0" />
    </multiply>
    <multiply name="N_tmp1_color3" type="color3">
      <input name="in1" type="color3" nodename="N_camount_color3" />
      <input name="in2" type="color3" value="0.0, 1.0, 1.0" />
    </multiply>
    <add name="N_svchans_color3" type="color3">
      <input name="in1" type="color3" nodename="N_tmp1_color3" />
      <input name="in2" type="color3" value="1.0, 0.0, 0.0" />
    </add>
    <add name="N_tmp2_color3" type="color3">
      <input name="in1" type="color3" nodename="N_inhsv_color3" />
      <input name="in2" type="color3" nodename="N_hchans_color3" />
    </add>
    <multiply name="N_tmp3_color3" type="color3">
      <input name="in1" type="color3" nodename="N_tmp2_color3" />
      <input name="in2" type="color3" nodename="N_svchans_color3" />
    </multiply>
    <hsvtorgb name="N_torgb_color3" type="color3">
      <input name="in" type="color3" nodename="N_tmp3_color3" />
    </hsvtorgb>
    <output name="out" type="color3" nodename="N_torgb_color3" />
  </nodegraph>
  <nodegraph name="NG_hsvadjust_color4" nodedef="ND_hsvadjust_color4">
    <rgbtohsv name="N_inhsv_color4" type="color4">
      <input name="in" type="color4" interfacename="in" />
    </rgbtohsv>
    <convert name="N_camt_color3" type="color3">
      <input name="in" type="vector3" interfacename="amount" />
    </convert>
    <convert name="N_camount_color4" type="color4">
      <input name="in" type="color3" nodename="N_camt_color3" />
    </convert>
    <multiply name="N_hchans_color4" type="color4">
      <input name="in1" type="color4" nodename="N_camount_color4" />
      <input name="in2" type="color4" value="1.0, 0.0, 0.0, 0.0" />
    </multiply>
    <multiply name="N_tmp1_color4" type="color4">
      <input name="in1" type="color4" nodename="N_camount_color4" />
      <input name="in2" type="color4" value="0.0, 1.0, 1.0, 0.0" />
    </multiply>
    <add name="N_svchans_color4" type="color4">
      <input name="in1" type="color4" nodename="N_tmp1_color4" />
      <input name="in2" type="color4" value="1.0, 0.0, 0.0, 1.0" />
    </add>
    <add name="N_tmp2_color4" type="color4">
      <input name="in1" type="color4" nodename="N_inhsv_color4" />
      <input name="in2" type="color4" nodename="N_hchans_color4" />
    </add>
    <multiply name="N_tmp3_color4" type="color4">
      <input name="in1" type="color4" nodename="N_tmp2_color4" />
      <input name="in2" type="color4" nodename="N_svchans_color4" />
    </multiply>
    <hsvtorgb name="N_torgb_color4" type="color4">
      <input name="in" type="color4" nodename="N_tmp3_color4" />
    </hsvtorgb>
    <output name="out" type="color4" nodename="N_torgb_color4" />
  </nodegraph>

  <!--
    Node: <saturate>
    Adjust the saturation of a color using a linear interpolation between the incoming
    color and the grayscale luminance of the input computed using the provided luma
    coefficients; the alpha channel will be unchanged if present.
  -->
  <nodegraph name="NG_saturate_color3" nodedef="ND_saturate_color3">
    <luminance name="N_gray_color3" type="color3">
      <input name="in" type="color3" interfacename="in" />
      <input name="lumacoeffs" type="color3" interfacename="lumacoeffs" />
    </luminance>
    <mix name="N_mix_color3" type="color3">
      <input name="bg" type="color3" nodename="N_gray_color3" />
      <input name="fg" type="color3" interfacename="in" />
      <input name="mix" type="float" interfacename="amount" />
    </mix>
    <output name="out" type="color3" nodename="N_mix_color3" />
  </nodegraph>
  <nodegraph name="NG_saturate_color4" nodedef="ND_saturate_color4">
    <luminance name="N_gray_color4" type="color4">
      <input name="in" type="color4" interfacename="in" />
      <input name="lumacoeffs" type="color3" interfacename="lumacoeffs" />
    </luminance>
    <mix name="N_mix_color4" type="color4">
      <input name="bg" type="color4" nodename="N_gray_color4" />
      <input name="fg" type="color4" interfacename="in" />
      <input name="mix" type="float" interfacename="amount" />
    </mix>
    <output name="out" type="color4" nodename="N_mix_color4" />
  </nodegraph>

  <!--
    Node: <colorcorrect>
    Combines various adjustment nodes into one, artist-friendly color correction node.
  -->
  <nodegraph name="NG_colorcorrect_color3" nodedef="ND_colorcorrect_color3">
    <multiply name="N_exposure" type="color3">
      <input name="in1" type="color3" nodename="N_contrast" />
      <input name="in2" type="float" nodename="N_exposurepwr" />
    </multiply>
    <contrast name="N_contrast" type="color3">
      <input name="in" type="color3" nodename="N_gain" />
      <input name="amount" type="float" interfacename="contrast" />
      <input name="pivot" type="float" interfacename="contrastpivot" />
    </contrast>
    <power name="N_exposurepwr" type="float">
      <input name="in2" type="float" interfacename="exposure" />
      <input name="in1" type="float" value="2" />
    </power>
    <multiply name="N_gain" type="color3">
      <input name="in1" type="color3" nodename="N_liftadd" />
      <input name="in2" type="float" interfacename="gain" />
    </multiply>
    <add name="N_liftadd" type="color3">
      <input name="in1" type="color3" nodename="N_liftmult" />
      <input name="in2" type="float" interfacename="lift" />
    </add>
    <multiply name="N_liftmult" type="color3">
      <input name="in1" type="color3" nodename="N_gamma" />
      <input name="in2" type="float" nodename="N_liftsubtract" />
    </multiply>
    <range name="N_gamma" type="color3">
      <input name="in" type="color3" nodename="N_saturation" />
      <input name="gamma" type="float" interfacename="gamma" />
      <input name="inlow" type="float" value="0" />
      <input name="inhigh" type="float" value="1" />
      <input name="outlow" type="float" value="0" />
      <input name="outhigh" type="float" value="1" />
      <input name="doclamp" type="boolean" value="false" />
    </range>
    <subtract name="N_liftsubtract" type="float">
      <input name="in2" type="float" interfacename="lift" />
      <input name="in1" type="float" value="1" />
    </subtract>
    <saturate name="N_saturation" type="color3">
      <input name="in" type="color3" nodename="N_hsvadjust" />
      <input name="amount" type="float" interfacename="saturation" />
    </saturate>
    <hsvadjust name="N_hsvadjust" type="color3">
      <input name="in" type="color3" interfacename="in" />
      <input name="amount" type="vector3" nodename="N_parm2hue" />
    </hsvadjust>
    <combine3 name="N_parm2hue" type="vector3">
      <input name="in1" type="float" interfacename="hue" />
      <input name="in2" type="float" value="1" />
      <input name="in3" type="float" value="1" />
    </combine3>
    <output name="out" type="color3" nodename="N_exposure" />
  </nodegraph>
  <nodegraph name="NG_colorcorrect_color4" nodedef="ND_colorcorrect_color4">
    <separate4 name="N_split_color4" type="multioutput">
      <input name="in" type="color4" interfacename="in" />
    </separate4>
    <combine3 name="N_combine_color" type="color3">
      <input name="in1" type="float" nodename="N_split_color4" output="outr" />
      <input name="in2" type="float" nodename="N_split_color4" output="outg" />
      <input name="in3" type="float" nodename="N_split_color4" output="outb" />
    </combine3>
    <colorcorrect name="N_colorcorrect" type="color3">
      <input name="in" type="color3" nodename="N_combine_color" />
      <input name="hue" type="float" interfacename="hue" />
      <input name="saturation" type="float" interfacename="saturation" />
      <input name="gamma" type="float" interfacename="gamma" />
      <input name="lift" type="float" interfacename="lift" />
      <input name="gain" type="float" interfacename="gain" />
      <input name="contrast" type="float" interfacename="contrast" />
      <input name="contrastpivot" type="float" interfacename="contrastpivot" />
      <input name="exposure" type="float" interfacename="exposure" />
    </colorcorrect>
    <separate3 name="N_split_color" type="multioutput">
      <input name="in" type="color3" nodename="N_colorcorrect" />
    </separate3>
    <combine4 name="N_combine_with_alpha" type="color4">
      <input name="in1" type="float" nodename="N_split_color" output="outr" />
      <input name="in2" type="float" nodename="N_split_color" output="outg" />
      <input name="in3" type="float" nodename="N_split_color" output="outb" />
      <input name="in4" type="float" nodename="N_split_color4" output="outa" />
    </combine4>
    <output name="out" type="color4" nodename="N_combine_with_alpha" />
  </nodegraph>

  <!-- ======================================================================== -->
  <!-- Compositing nodes                                                        -->
  <!-- ======================================================================== -->

  <!--
    Node: <overlay>
    Overlay is a compositing node which is a combination of multiply and screen.
  -->
  <nodegraph name="NG_overlay_float" nodedef="ND_overlay_float">
    <output name="out" type="float" nodename="N_mix" />
    <ifgreatereq name="N_ifgreatereq0_overlay_r" type="float" nodedef="ND_ifgreatereq_float">
      <input name="value1" type="float" interfacename="bg" />
      <input name="value2" type="float" value="0.5" />
      <input name="in1" type="float" nodename="N_subtract_lower_one" />
      <input name="in2" type="float" nodename="N_multiply_upper_two" />
    </ifgreatereq>
    <multiply name="N_multiply_upper_fg_bg" type="float" nodedef="ND_multiply_float">
      <input name="in1" type="float" interfacename="fg" />
      <input name="in2" type="float" interfacename="bg" />
    </multiply>
    <multiply name="N_multiply_upper_two" type="float" nodedef="ND_multiply_float">
      <input name="in1" type="float" nodename="N_multiply_upper_fg_bg" />
      <input name="in2" type="float" value="2" />
    </multiply>
    <subtract name="N_subtract_lower_one_bg" type="float" nodedef="ND_subtract_float">
      <input name="in1" type="float" value="1" />
      <input name="in2" type="float" interfacename="bg" />
    </subtract>
    <subtract name="N_subtract_lower_one_fg" type="float" nodedef="ND_subtract_float">
      <input name="in1" type="float" value="1" />
      <input name="in2" type="float" interfacename="fg" />
    </subtract>
    <multiply name="N_multiply_lower_fg_bg" type="float" nodedef="ND_multiply_float">
      <input name="in1" type="float" nodename="N_subtract_lower_one_bg" />
      <input name="in2" type="float" nodename="N_subtract_lower_one_fg" />
    </multiply>
    <subtract name="N_subtract_lower_one" type="float" nodedef="ND_subtract_float">
      <input name="in1" type="float" value="1" />
      <input name="in2" type="float" nodename="N_multiply_lower_two" />
    </subtract>
    <mix name="N_mix" type="float" nodedef="ND_mix_float">
      <input name="fg" type="float" nodename="N_ifgreatereq0_overlay_r" />
      <input name="bg" type="float" interfacename="bg" />
      <input name="mix" type="float" interfacename="mix" />
    </mix>
    <multiply name="N_multiply_lower_two" type="float" nodedef="ND_multiply_float">
      <input name="in1" type="float" nodename="N_multiply_lower_fg_bg" />
      <input name="in2" type="float" value="2" />
    </multiply>
  </nodegraph>
  <nodegraph name="NG_overlay_color3" nodedef="ND_overlay_color3">
    <output name="out" type="color3" nodename="N_combine" />
    <separate3 name="N_split_color3_fg" type="multioutput">
      <input name="in" type="color3" interfacename="fg" />
    </separate3>
    <separate3 name="N_split_color3_bg" type="multioutput">
      <input name="in" type="color3" interfacename="bg" />
    </separate3>
    <combine3 name="N_combine" type="color3" nodedef="ND_combine3_color3">
      <input name="in1" type="float" nodename="N_overlay_r" />
      <input name="in2" type="float" nodename="N_overlay_g" />
      <input name="in3" type="float" nodename="N_overlay_b" />
    </combine3>
    <overlay name="N_overlay_r" type="float" nodedef="ND_overlay_float">
      <input name="fg" type="float" nodename="N_split_color3_fg" output="outr" />
      <input name="bg" type="float" nodename="N_split_color3_bg" output="outr" />
      <input name="mix" type="float" interfacename="mix" />
    </overlay>
    <overlay name="N_overlay_g" type="float" nodedef="ND_overlay_float">
      <input name="fg" type="float" nodename="N_split_color3_fg" output="outg" />
      <input name="bg" type="float" nodename="N_split_color3_bg" output="outg" />
      <input name="mix" type="float" interfacename="mix" />
    </overlay>
    <overlay name="N_overlay_b" type="float" nodedef="ND_overlay_float">
      <input name="fg" type="float" nodename="N_split_color3_fg" output="outb" />
      <input name="bg" type="float" nodename="N_split_color3_bg" output="outb" />
      <input name="mix" type="float" interfacename="mix" />
    </overlay>
  </nodegraph>
  <nodegraph name="NG_overlay_color4" nodedef="ND_overlay_color4">
    <output name="out" type="color4" nodename="N_combine" />
    <separate4 name="N_split_fg" type="multioutput">
      <input name="in" type="color4" interfacename="fg" />
    </separate4>
    <separate4 name="N_split_bg" type="multioutput">
      <input name="in" type="color4" interfacename="bg" />
    </separate4>
    <overlay name="N_overlay_r" type="float" nodedef="ND_overlay_float">
      <input name="fg" type="float" nodename="N_split_fg" output="outr" />
      <input name="bg" type="float" nodename="N_split_bg" output="outr" />
      <input name="mix" type="float" interfacename="mix" />
    </overlay>
    <overlay name="N_overlay_g" type="float" nodedef="ND_overlay_float">
      <input name="fg" type="float" nodename="N_split_fg" output="outg" />
      <input name="bg" type="float" nodename="N_split_bg" output="outg" />
      <input name="mix" type="float" interfacename="mix" />
    </overlay>
    <overlay name="N_overlay_b" type="float" nodedef="ND_overlay_float">
      <input name="fg" type="float" nodename="N_split_fg" output="outb" />
      <input name="bg" type="float" nodename="N_split_bg" output="outb" />
      <input name="mix" type="float" interfacename="mix" />
    </overlay>
    <overlay name="N_overlay_a" type="float" nodedef="ND_overlay_float">
      <input name="fg" type="float" nodename="N_split_fg" output="outa" />
      <input name="bg" type="float" nodename="N_split_bg" output="outa" />
      <input name="mix" type="float" interfacename="mix" />
    </overlay>
    <combine4 name="N_combine" type="color4" nodedef="ND_combine4_color4">
      <input name="in1" type="float" nodename="N_overlay_r" />
      <input name="in2" type="float" nodename="N_overlay_g" />
      <input name="in3" type="float" nodename="N_overlay_b" />
      <input name="in4" type="float" nodename="N_overlay_a" />
    </combine4>
  </nodegraph>

  <!-- ======================================================================== -->
  <!-- Conditional nodes                                                        -->
  <!-- ======================================================================== -->

  <!-- ======================================================================== -->
  <!-- Channel nodes                                                            -->
  <!-- ======================================================================== -->

  <!--
    Node: <convert>
    Convert a stream from one type to another; only certain unambiguous conversion
    types are supported.
  -->
  <nodegraph name="NG_convert_float_color3" nodedef="ND_convert_float_color3">
    <combine3 name="combine" type="color3">
      <input name="in1" type="float" interfacename="in" />
      <input name="in2" type="float" interfacename="in" />
      <input name="in3" type="float" interfacename="in" />
    </combine3>
    <output name="out" type="color3" nodename="combine" />
  </nodegraph>
  <nodegraph name="NG_convert_float_color4" nodedef="ND_convert_float_color4">
    <combine4 name="combine" type="color4">
      <input name="in1" type="float" interfacename="in" />
      <input name="in2" type="float" interfacename="in" />
      <input name="in3" type="float" interfacename="in" />
      <input name="in4" type="float" interfacename="in" />
    </combine4>
    <output name="out" type="color4" nodename="combine" />
  </nodegraph>
  <nodegraph name="NG_convert_float_vector2" nodedef="ND_convert_float_vector2">
    <combine2 name="combine" type="vector2">
      <input name="in1" type="float" interfacename="in" />
      <input name="in2" type="float" interfacename="in" />
    </combine2>
    <output name="out" type="vector2" nodename="combine" />
  </nodegraph>
  <nodegraph name="NG_convert_float_vector3" nodedef="ND_convert_float_vector3">
    <combine3 name="combine" type="vector3">
      <input name="in1" type="float" interfacename="in" />
      <input name="in2" type="float" interfacename="in" />
      <input name="in3" type="float" interfacename="in" />
    </combine3>
    <output name="out" type="vector3" nodename="combine" />
  </nodegraph>
  <nodegraph name="NG_convert_float_vector4" nodedef="ND_convert_float_vector4">
    <combine4 name="combine" type="vector4">
      <input name="in1" type="float" interfacename="in" />
      <input name="in2" type="float" interfacename="in" />
      <input name="in3" type="float" interfacename="in" />
      <input name="in4" type="float" interfacename="in" />
    </combine4>
    <output name="out" type="vector4" nodename="combine" />
  </nodegraph>
  <nodegraph name="NG_convert_color3_color4" nodedef="ND_convert_color3_color4">
    <separate3 name="separate" type="multioutput">
      <input name="in" type="color3" interfacename="in" />
    </separate3>
    <combine4 name="combine" type="color4">
      <input name="in1" type="float" nodename="separate" output="outr" />
      <input name="in2" type="float" nodename="separate" output="outg" />
      <input name="in3" type="float" nodename="separate" output="outb" />
      <input name="in4" type="float" value="1.0" />
    </combine4>
    <output name="out" type="color4" nodename="combine" />
  </nodegraph>
  <nodegraph name="NG_convert_color3_vector2" nodedef="ND_convert_color3_vector2">
    <separate3 name="separate" type="multioutput">
      <input name="in" type="color3" interfacename="in" />
    </separate3>
    <combine2 name="combine" type="vector2">
      <input name="in1" type="float" nodename="separate" output="outr" />
      <input name="in2" type="float" nodename="separate" output="outg" />
    </combine2>
    <output name="out" type="vector2" nodename="combine" />
  </nodegraph>
  <nodegraph name="NG_convert_color3_vector3" nodedef="ND_convert_color3_vector3">
    <separate3 name="separate" type="multioutput">
      <input name="in" type="color3" interfacename="in" />
    </separate3>
    <combine3 name="combine" type="vector3">
      <input name="in1" type="float" nodename="separate" output="outr" />
      <input name="in2" type="float" nodename="separate" output="outg" />
      <input name="in3" type="float" nodename="separate" output="outb" />
    </combine3>
    <output name="out" type="vector3" nodename="combine" />
  </nodegraph>
  <nodegraph name="NG_convert_color3_vector4" nodedef="ND_convert_color3_vector4">
    <separate3 name="separate" type="multioutput">
      <input name="in" type="color3" interfacename="in" />
    </separate3>
    <combine4 name="combine" type="vector4">
      <input name="in1" type="float" nodename="separate" output="outr" />
      <input name="in2" type="float" nodename="separate" output="outg" />
      <input name="in3" type="float" nodename="separate" output="outb" />
      <input name="in4" type="float" value="1.0" />
    </combine4>
    <output name="out" type="vector4" nodename="combine" />
  </nodegraph>
  <nodegraph name="NG_convert_color4_color3" nodedef="ND_convert_color4_color3">
    <separate4 name="separate" type="multioutput">
      <input name="in" type="color4" interfacename="in" />
    </separate4>
    <combine3 name="combine" type="color3">
      <input name="in1" type="float" nodename="separate" output="outr" />
      <input name="in2" type="float" nodename="separate" output="outg" />
      <input name="in3" type="float" nodename="separate" output="outb" />
    </combine3>
    <output name="out" type="color3" nodename="combine" />
  </nodegraph>
  <nodegraph name="NG_convert_color4_vector2" nodedef="ND_convert_color4_vector2">
    <separate4 name="separate" type="multioutput">
      <input name="in" type="color4" interfacename="in" />
    </separate4>
    <combine2 name="combine" type="vector2">
      <input name="in1" type="float" nodename="separate" output="outr" />
      <input name="in2" type="float" nodename="separate" output="outg" />
    </combine2>
    <output name="out" type="vector2" nodename="combine" />
  </nodegraph>
  <nodegraph name="NG_convert_color4_vector3" nodedef="ND_convert_color4_vector3">
    <separate4 name="separate" type="multioutput">
      <input name="in" type="color4" interfacename="in" />
    </separate4>
    <combine3 name="combine" type="vector3">
      <input name="in1" type="float" nodename="separate" output="outr" />
      <input name="in2" type="float" nodename="separate" output="outg" />
      <input name="in3" type="float" nodename="separate" output="outb" />
    </combine3>
    <output name="out" type="vector3" nodename="combine" />
  </nodegraph>
  <nodegraph name="NG_convert_color4_vector4" nodedef="ND_convert_color4_vector4">
    <separate4 name="separate" type="multioutput">
      <input name="in" type="color4" interfacename="in" />
    </separate4>
    <combine4 name="combine" type="vector4">
      <input name="in1" type="float" nodename="separate" output="outr" />
      <input name="in2" type="float" nodename="separate" output="outg" />
      <input name="in3" type="float" nodename="separate" output="outb" />
      <input name="in4" type="float" nodename="separate" output="outa" />
    </combine4>
    <output name="out" type="vector4" nodename="combine" />
  </nodegraph>
  <nodegraph name="NG_convert_vector2_color3" nodedef="ND_convert_vector2_color3">
    <separate2 name="separate" type="multioutput">
      <input name="in" type="vector2" interfacename="in" />
    </separate2>
    <combine3 name="combine" type="color3">
      <input name="in1" type="float" nodename="separate" output="outx" />
      <input name="in2" type="float" nodename="separate" output="outy" />
      <input name="in3" type="float" value="0.0" />
    </combine3>
    <output name="out" type="color3" nodename="combine" />
  </nodegraph>
  <nodegraph name="NG_convert_vector2_color4" nodedef="ND_convert_vector2_color4">
    <separate2 name="separate" type="multioutput">
      <input name="in" type="vector2" interfacename="in" />
    </separate2>
    <combine4 name="combine" type="color4">
      <input name="in1" type="float" nodename="separate" output="outx" />
      <input name="in2" type="float" nodename="separate" output="outy" />
      <input name="in3" type="float" value="0.0" />
      <input name="in4" type="float" value="1.0" />
    </combine4>
    <output name="out" type="color4" nodename="combine" />
  </nodegraph>
  <nodegraph name="NG_convert_vector2_vector3" nodedef="ND_convert_vector2_vector3">
    <separate2 name="separate" type="multioutput">
      <input name="in" type="vector2" interfacename="in" />
    </separate2>
    <combine3 name="combine" type="vector3">
      <input name="in1" type="float" nodename="separate" output="outx" />
      <input name="in2" type="float" nodename="separate" output="outy" />
      <input name="in3" type="float" value="0.0" />
    </combine3>
    <output name="out" type="vector3" nodename="combine" />
  </nodegraph>
  <nodegraph name="NG_convert_vector2_vector4" nodedef="ND_convert_vector2_vector4">
    <separate2 name="separate" type="multioutput">
      <input name="in" type="vector2" interfacename="in" />
    </separate2>
    <combine4 name="combine" type="vector4">
      <input name="in1" type="float" nodename="separate" output="outx" />
      <input name="in2" type="float" nodename="separate" output="outy" />
      <input name="in3" type="float" value="0.0" />
      <input name="in4" type="float" value="1.0" />
    </combine4>
    <output name="out" type="vector4" nodename="combine" />
  </nodegraph>
  <nodegraph name="NG_convert_vector3_color3" nodedef="ND_convert_vector3_color3">
    <separate3 name="separate" type="multioutput">
      <input name="in" type="vector3" interfacename="in" />
    </separate3>
    <combine3 name="combine" type="color3">
      <input name="in1" type="float" nodename="separate" output="outx" />
      <input name="in2" type="float" nodename="separate" output="outy" />
      <input name="in3" type="float" nodename="separate" output="outz" />
    </combine3>
    <output name="out" type="color3" nodename="combine" />
  </nodegraph>
  <nodegraph name="NG_convert_vector3_color4" nodedef="ND_convert_vector3_color4">
    <separate3 name="separate" type="multioutput">
      <input name="in" type="vector3" interfacename="in" />
    </separate3>
    <combine4 name="combine" type="color4">
      <input name="in1" type="float" nodename="separate" output="outx" />
      <input name="in2" type="float" nodename="separate" output="outy" />
      <input name="in3" type="float" nodename="separate" output="outz" />
      <input name="in4" type="float" value="1.0" />
    </combine4>
    <output name="out" type="color4" nodename="combine" />
  </nodegraph>
  <nodegraph name="NG_convert_vector3_vector2" nodedef="ND_convert_vector3_vector2">
    <separate3 name="separate" type="multioutput">
      <input name="in" type="vector3" interfacename="in" />
    </separate3>
    <combine2 name="combine" type="vector2">
      <input name="in1" type="float" nodename="separate" output="outx" />
      <input name="in2" type="float" nodename="separate" output="outy" />
    </combine2>
    <output name="out" type="vector2" nodename="combine" />
  </nodegraph>
  <nodegraph name="NG_convert_vector3_vector4" nodedef="ND_convert_vector3_vector4">
    <separate3 name="separate" type="multioutput">
      <input name="in" type="vector3" interfacename="in" />
    </separate3>
    <combine4 name="combine" type="vector4">
      <input name="in1" type="float" nodename="separate" output="outx" />
      <input name="in2" type="float" nodename="separate" output="outy" />
      <input name="in3" type="float" nodename="separate" output="outz" />
      <input name="in4" type="float" value="1.0" />
    </combine4>
    <output name="out" type="vector4" nodename="combine" />
  </nodegraph>
  <nodegraph name="NG_convert_vector4_color3" nodedef="ND_convert_vector4_color3">
    <separate4 name="separate" type="multioutput">
      <input name="in" type="vector4" interfacename="in" />
    </separate4>
    <combine3 name="combine" type="color3">
      <input name="in1" type="float" nodename="separate" output="outx" />
      <input name="in2" type="float" nodename="separate" output="outy" />
      <input name="in3" type="float" nodename="separate" output="outz" />
    </combine3>
    <output name="out" type="color3" nodename="combine" />
  </nodegraph>
  <nodegraph name="NG_convert_vector4_color4" nodedef="ND_convert_vector4_color4">
    <separate4 name="separate" type="multioutput">
      <input name="in" type="vector4" interfacename="in" />
    </separate4>
    <combine4 name="combine" type="color4">
      <input name="in1" type="float" nodename="separate" output="outx" />
      <input name="in2" type="float" nodename="separate" output="outy" />
      <input name="in3" type="float" nodename="separate" output="outz" />
      <input name="in4" type="float" nodename="separate" output="outw" />
    </combine4>
    <output name="out" type="color4" nodename="combine" />
  </nodegraph>
  <nodegraph name="NG_convert_vector4_vector2" nodedef="ND_convert_vector4_vector2">
    <separate4 name="separate" type="multioutput">
      <input name="in" type="vector4" interfacename="in" />
    </separate4>
    <combine2 name="combine" type="vector2">
      <input name="in1" type="float" nodename="separate" output="outx" />
      <input name="in2" type="float" nodename="separate" output="outy" />
    </combine2>
    <output name="out" type="vector2" nodename="combine" />
  </nodegraph>
  <nodegraph name="NG_convert_vector4_vector3" nodedef="ND_convert_vector4_vector3">
    <separate4 name="separate" type="multioutput">
      <input name="in" type="vector4" interfacename="in" />
    </separate4>
    <combine3 name="combine" type="vector3">
      <input name="in1" type="float" nodename="separate" output="outx" />
      <input name="in2" type="float" nodename="separate" output="outy" />
      <input name="in3" type="float" nodename="separate" output="outz" />
    </combine3>
    <output name="out" type="vector3" nodename="combine" />
  </nodegraph>
  <nodegraph name="NG_convert_boolean_color3" nodedef="ND_convert_boolean_color3">
    <convert name="convert_to_float" type="float">
      <input name="in" type="boolean" interfacename="in" />
    </convert>
    <convert name="convert" type="color3">
      <input name="in" type="float" nodename="convert_to_float" />
    </convert>
    <output name="out" type="color3" nodename="convert" />
  </nodegraph>
  <nodegraph name="NG_convert_boolean_color4" nodedef="ND_convert_boolean_color4">
    <convert name="convert_to_float" type="float">
      <input name="in" type="boolean" interfacename="in" />
    </convert>
    <convert name="convert" type="color4">
      <input name="in" type="float" nodename="convert_to_float" />
    </convert>
    <output name="out" type="color4" nodename="convert" />
  </nodegraph>
  <nodegraph name="NG_convert_boolean_vector2" nodedef="ND_convert_boolean_vector2">
    <convert name="convert_to_float" type="float">
      <input name="in" type="boolean" interfacename="in" />
    </convert>
    <convert name="convert" type="vector2">
      <input name="in" type="float" nodename="convert_to_float" />
    </convert>
    <output name="out" type="vector2" nodename="convert" />
  </nodegraph>
  <nodegraph name="NG_convert_boolean_vector3" nodedef="ND_convert_boolean_vector3">
    <convert name="convert_to_float" type="float">
      <input name="in" type="boolean" interfacename="in" />
    </convert>
    <convert name="convert" type="vector3">
      <input name="in" type="float" nodename="convert_to_float" />
    </convert>
    <output name="out" type="vector3" nodename="convert" />
  </nodegraph>
  <nodegraph name="NG_convert_boolean_vector4" nodedef="ND_convert_boolean_vector4">
    <convert name="convert_to_float" type="float">
      <input name="in" type="boolean" interfacename="in" />
    </convert>
    <convert name="convert" type="vector4">
      <input name="in" type="float" nodename="convert_to_float" />
    </convert>
    <output name="out" type="vector4" nodename="convert" />
  </nodegraph>
  <nodegraph name="NG_convert_boolean_integer" nodedef="ND_convert_boolean_integer">
    <ifequal name="ifequal" type="integer">
      <input name="value1" type="boolean" interfacename="in" />
      <input name="value2" type="boolean" value="true" />
      <input name="in1" type="integer" value="1" />
      <input name="in2" type="integer" value="0" />
    </ifequal>
    <output name="out" type="integer" nodename="ifequal" />
  </nodegraph>
  <nodegraph name="NG_convert_integer_color3" nodedef="ND_convert_integer_color3">
    <convert name="convert_to_float" type="float">
      <input name="in" type="integer" interfacename="in" />
    </convert>
    <convert name="convert" type="color3">
      <input name="in" type="float" nodename="convert_to_float" />
    </convert>
    <output name="out" type="color3" nodename="convert" />
  </nodegraph>
  <nodegraph name="NG_convert_integer_color4" nodedef="ND_convert_integer_color4">
    <convert name="convert_to_float" type="float">
      <input name="in" type="integer" interfacename="in" />
    </convert>
    <convert name="convert" type="color4">
      <input name="in" type="float" nodename="convert_to_float" />
    </convert>
    <output name="out" type="color4" nodename="convert" />
  </nodegraph>
  <nodegraph name="NG_convert_integer_vector2" nodedef="ND_convert_integer_vector2">
    <convert name="convert_to_float" type="float">
      <input name="in" type="integer" interfacename="in" />
    </convert>
    <convert name="convert" type="vector2">
      <input name="in" type="float" nodename="convert_to_float" />
    </convert>
    <output name="out" type="vector2" nodename="convert" />
  </nodegraph>
  <nodegraph name="NG_convert_integer_vector3" nodedef="ND_convert_integer_vector3">
    <convert name="convert_to_float" type="float">
      <input name="in" type="integer" interfacename="in" />
    </convert>
    <convert name="convert" type="vector3">
      <input name="in" type="float" nodename="convert_to_float" />
    </convert>
    <output name="out" type="vector3" nodename="convert" />
  </nodegraph>
  <nodegraph name="NG_convert_integer_vector4" nodedef="ND_convert_integer_vector4">
    <convert name="convert_to_float" type="float">
      <input name="in" type="integer" interfacename="in" />
    </convert>
    <convert name="convert" type="vector4">
      <input name="in" type="float" nodename="convert_to_float" />
    </convert>
    <output name="out" type="vector4" nodename="convert" />
  </nodegraph>
  <nodegraph name="NG_convert_integer_boolean" nodedef="ND_convert_integer_boolean">
    <ifequal name="ifequal" type="boolean">
      <input name="value1" type="integer" interfacename="in" />
      <input name="value2" type="integer" value="0" />
    </ifequal>
    <not name="not" type="boolean">
      <input name="in" type="boolean" nodename="ifequal" />
    </not>
    <output name="out" type="boolean" nodename="not" />
  </nodegraph>
  <nodegraph name="NG_convert_color3_surfaceshader" nodedef="ND_convert_color3_surfaceshader">
    <surface_unlit name="surface" type="surfaceshader">
      <input name="emission_color" type="color3" interfacename="in" />
    </surface_unlit>
    <output name="out" type="surfaceshader" nodename="surface" />
  </nodegraph>
  <nodegraph name="NG_convert_color4_surfaceshader" nodedef="ND_convert_color4_surfaceshader">
    <surface_unlit name="surface" type="surfaceshader">
      <input name="emission_color" type="color3" nodename="convert" />
      <input name="opacity" type="float" nodename="extract" />
    </surface_unlit>
    <extract name="extract" type="float">
      <input name="in" type="color4" interfacename="in" />
      <input name="index" type="integer" uniform="true" value="3" />
    </extract>
    <convert name="convert" type="color3">
      <input name="in" type="color4" interfacename="in" />
    </convert>
    <output name="out" type="surfaceshader" nodename="surface" />
  </nodegraph>
  <nodegraph name="NG_convert_float_surfaceshader" nodedef="ND_convert_float_surfaceshader">
    <convert name="float_to_color3" type="color3">
      <input name="in" type="float" interfacename="in" />
    </convert>
    <surface_unlit name="surface" type="surfaceshader">
      <input name="emission_color" type="color3" nodename="float_to_color3" />
    </surface_unlit>
    <output name="out" type="surfaceshader" nodename="surface" />
  </nodegraph>
  <nodegraph name="NG_convert_vector2_surfaceshader" nodedef="ND_convert_vector2_surfaceshader">
    <surface_unlit name="surface" type="surfaceshader">
      <input name="emission_color" type="color3" nodename="vec3_to_color3" />
    </surface_unlit>
    <convert name="vec2_to_vec3" type="vector3">
      <input name="in" type="vector2" interfacename="in" />
    </convert>
    <convert name="vec3_to_color3" type="color3">
      <input name="in" type="vector3" nodename="vec2_to_vec3" />
    </convert>
    <output name="out" type="surfaceshader" nodename="surface" />
  </nodegraph>
  <nodegraph name="NG_convert_vector3_surfaceshader" nodedef="ND_convert_vector3_surfaceshader">
    <surface_unlit name="surface" type="surfaceshader">
      <input name="emission_color" type="color3" nodename="vec3_to_color3" />
    </surface_unlit>
    <convert name="vec3_to_color3" type="color3">
      <input name="in" type="vector3" interfacename="in" />
    </convert>
    <output name="out" type="surfaceshader" nodename="surface" />
  </nodegraph>
  <nodegraph name="NG_convert_vector4_surfaceshader" nodedef="ND_convert_vector4_surfaceshader">
    <surface_unlit name="surface" type="surfaceshader">
      <input name="emission_color" type="color3" nodename="color4_to_color3" />
      <input name="opacity" type="float" nodename="color4_to_float" />
    </surface_unlit>
    <convert name="vec4_to_color4" type="color4">
      <input name="in" type="vector4" interfacename="in" />
    </convert>
    <extract name="color4_to_float" type="float">
      <input name="in" type="color4" nodename="vec4_to_color4" />
      <input name="index" type="integer" uniform="true" value="3" />
    </extract>
    <convert name="color4_to_color3" type="color3">
      <input name="in" type="color4" nodename="vec4_to_color4" />
    </convert>
    <output name="out" type="surfaceshader" nodename="surface" />
  </nodegraph>
  <nodegraph name="NG_convert_integer_surfaceshader" nodedef="ND_convert_integer_surfaceshader">
    <convert name="int_to_float" type="float">
      <input name="in" type="integer" interfacename="in" />
    </convert>
    <convert name="float_to_color3" type="color3">
      <input name="in" type="float" nodename="int_to_float" />
    </convert>
    <surface_unlit name="surface" type="surfaceshader">
      <input name="emission_color" type="color3" nodename="float_to_color3" />
    </surface_unlit>
    <output name="out" type="surfaceshader" nodename="surface" />
  </nodegraph>
  <nodegraph name="NG_convert_boolean_surfaceshader" nodedef="ND_convert_boolean_surfaceshader">
    <convert name="bool_to_float" type="float">
      <input name="in" type="boolean" interfacename="in" />
    </convert>
    <convert name="float_to_color3" type="color3">
      <input name="in" type="float" nodename="bool_to_float" />
    </convert>
    <surface_unlit name="surface" type="surfaceshader">
      <input name="emission_color" type="color3" nodename="float_to_color3" />
    </surface_unlit>
    <output name="out" type="surfaceshader" nodename="surface" />
  </nodegraph>

  <!--
    Nodes: <separate2>, <separate3>, <separate4>
    Output each of the channels of a color/vector stream as a separate float output.
  -->
  <nodegraph name="NG_separate3_color3" nodedef="ND_separate3_color3">
    <extract name="N_extract_0" type="float">
      <input name="in" type="color3" interfacename="in" />
      <input name="index" type="integer" value="0" />
    </extract>
    <extract name="N_extract_1" type="float">
      <input name="in" type="color3" interfacename="in" />
      <input name="index" type="integer" value="1" />
    </extract>
    <extract name="N_extract_2" type="float">
      <input name="in" type="color3" interfacename="in" />
      <input name="index" type="integer" value="2" />
    </extract>
    <output name="outr" type="float" nodename="N_extract_0" />
    <output name="outg" type="float" nodename="N_extract_1" />
    <output name="outb" type="float" nodename="N_extract_2" />
  </nodegraph>
  <nodegraph name="NG_separate4_color4" nodedef="ND_separate4_color4">
    <extract name="N_extract_0" type="float">
      <input name="in" type="color4" interfacename="in" />
      <input name="index" type="integer" value="0" />
    </extract>
    <extract name="N_extract_1" type="float">
      <input name="in" type="color4" interfacename="in" />
      <input name="index" type="integer" value="1" />
    </extract>
    <extract name="N_extract_2" type="float">
      <input name="in" type="color4" interfacename="in" />
      <input name="index" type="integer" value="2" />
    </extract>
    <extract name="N_extract_3" type="float">
      <input name="in" type="color4" interfacename="in" />
      <input name="index" type="integer" value="3" />
    </extract>
    <output name="outr" type="float" nodename="N_extract_0" />
    <output name="outg" type="float" nodename="N_extract_1" />
    <output name="outb" type="float" nodename="N_extract_2" />
    <output name="outa" type="float" nodename="N_extract_3" />
  </nodegraph>
  <nodegraph name="NG_separate2_vector2" nodedef="ND_separate2_vector2">
    <extract name="N_extract_0" type="float">
      <input name="in" type="vector2" interfacename="in" />
      <input name="index" type="integer" value="0" />
    </extract>
    <extract name="N_extract_1" type="float">
      <input name="in" type="vector2" interfacename="in" />
      <input name="index" type="integer" value="1" />
    </extract>
    <output name="outx" type="float" nodename="N_extract_0" />
    <output name="outy" type="float" nodename="N_extract_1" />
  </nodegraph>
  <nodegraph name="NG_separate3_vector3" nodedef="ND_separate3_vector3">
    <extract name="N_extract_0" type="float">
      <input name="in" type="vector3" interfacename="in" />
      <input name="index" type="integer" value="0" />
    </extract>
    <extract name="N_extract_1" type="float">
      <input name="in" type="vector3" interfacename="in" />
      <input name="index" type="integer" value="1" />
    </extract>
    <extract name="N_extract_2" type="float">
      <input name="in" type="vector3" interfacename="in" />
      <input name="index" type="integer" value="2" />
    </extract>
    <output name="outx" type="float" nodename="N_extract_0" />
    <output name="outy" type="float" nodename="N_extract_1" />
    <output name="outz" type="float" nodename="N_extract_2" />
  </nodegraph>
  <nodegraph name="NG_separate4_vector4" nodedef="ND_separate4_vector4">
    <extract name="N_extract_0" type="float">
      <input name="in" type="vector4" interfacename="in" />
      <input name="index" type="integer" value="0" />
    </extract>
    <extract name="N_extract_1" type="float">
      <input name="in" type="vector4" interfacename="in" />
      <input name="index" type="integer" value="1" />
    </extract>
    <extract name="N_extract_2" type="float">
      <input name="in" type="vector4" interfacename="in" />
      <input name="index" type="integer" value="2" />
    </extract>
    <extract name="N_extract_3" type="float">
      <input name="in" type="vector4" interfacename="in" />
      <input name="index" type="integer" value="3" />
    </extract>
    <output name="outx" type="float" nodename="N_extract_0" />
    <output name="outy" type="float" nodename="N_extract_1" />
    <output name="outz" type="float" nodename="N_extract_2" />
    <output name="outw" type="float" nodename="N_extract_3" />
  </nodegraph>

  <!-- ======================================================================== -->
  <!-- Switch nodes                                                             -->
  <!-- ======================================================================== -->

  <nodegraph name="NG_switch_float" nodedef="ND_switch_float">
    <ifgreater name="ifgreater_10" type="float">
      <input name="value1" type="float" value="10.0" />
      <input name="value2" type="float" interfacename="which" />
      <input name="in1" type="float" interfacename="in10" />
      <input name="in2" type="float" value="0.0" />
    </ifgreater>
    <ifgreater name="ifgreater_9" type="float">
      <input name="value1" type="float" value="9.0" />
      <input name="value2" type="float" interfacename="which" />
      <input name="in1" type="float" interfacename="in9" />
      <input name="in2" type="float" nodename="ifgreater_10" />
    </ifgreater>
    <ifgreater name="ifgreater_8" type="float">
      <input name="value1" type="float" value="8.0" />
      <input name="value2" type="float" interfacename="which" />
      <input name="in1" type="float" interfacename="in8" />
      <input name="in2" type="float" nodename="ifgreater_9" />
    </ifgreater>
    <ifgreater name="ifgreater_7" type="float">
      <input name="value1" type="float" value="7.0" />
      <input name="value2" type="float" interfacename="which" />
      <input name="in1" type="float" interfacename="in7" />
      <input name="in2" type="float" nodename="ifgreater_8" />
    </ifgreater>
    <ifgreater name="ifgreater_6" type="float">
      <input name="value1" type="float" value="6.0" />
      <input name="value2" type="float" interfacename="which" />
      <input name="in1" type="float" interfacename="in6" />
      <input name="in2" type="float" nodename="ifgreater_7" />
    </ifgreater>
    <ifgreater name="ifgreater_5" type="float">
      <input name="value1" type="float" value="5.0" />
      <input name="value2" type="float" interfacename="which" />
      <input name="in1" type="float" interfacename="in5" />
      <input name="in2" type="float" nodename="ifgreater_6" />
    </ifgreater>
    <ifgreater name="ifgreater_4" type="float">
      <input name="value1" type="float" value="4.0" />
      <input name="value2" type="float" interfacename="which" />
      <input name="in1" type="float" interfacename="in4" />
      <input name="in2" type="float" nodename="ifgreater_5" />
    </ifgreater>
    <ifgreater name="ifgreater_3" type="float">
      <input name="value1" type="float" value="3.0" />
      <input name="value2" type="float" interfacename="which" />
      <input name="in1" type="float" interfacename="in3" />
      <input name="in2" type="float" nodename="ifgreater_4" />
    </ifgreater>
    <ifgreater name="ifgreater_2" type="float">
      <input name="value1" type="float" value="2.0" />
      <input name="value2" type="float" interfacename="which" />
      <input name="in1" type="float" interfacename="in2" />
      <input name="in2" type="float" nodename="ifgreater_3" />
    </ifgreater>
    <ifgreater name="ifgreater_1" type="float">
      <input name="value1" type="float" value="1.0" />
      <input name="value2" type="float" interfacename="which" />
      <input name="in1" type="float" interfacename="in1" />
      <input name="in2" type="float" nodename="ifgreater_2" />
    </ifgreater>
    <output name="out" type="float" nodename="ifgreater_1" />
  </nodegraph>
  <nodegraph name="NG_switch_color3" nodedef="ND_switch_color3">
    <ifgreater name="ifgreater_10" type="color3">
      <input name="value1" type="float" value="10.0" />
      <input name="value2" type="float" interfacename="which" />
      <input name="in1" type="color3" interfacename="in10" />
      <input name="in2" type="color3" value="0,0,0" />
    </ifgreater>
    <ifgreater name="ifgreater_9" type="color3">
      <input name="value1" type="float" value="9.0" />
      <input name="value2" type="float" interfacename="which" />
      <input name="in1" type="color3" interfacename="in9" />
      <input name="in2" type="color3" nodename="ifgreater_10" />
    </ifgreater>
    <ifgreater name="ifgreater_8" type="color3">
      <input name="value1" type="float" value="8.0" />
      <input name="value2" type="float" interfacename="which" />
      <input name="in1" type="color3" interfacename="in8" />
      <input name="in2" type="color3" nodename="ifgreater_9" />
    </ifgreater>
    <ifgreater name="ifgreater_7" type="color3">
      <input name="value1" type="float" value="7.0" />
      <input name="value2" type="float" interfacename="which" />
      <input name="in1" type="color3" interfacename="in7" />
      <input name="in2" type="color3" nodename="ifgreater_8" />
    </ifgreater>
    <ifgreater name="ifgreater_6" type="color3">
      <input name="value1" type="float" value="6.0" />
      <input name="value2" type="float" interfacename="which" />
      <input name="in1" type="color3" interfacename="in6" />
      <input name="in2" type="color3" nodename="ifgreater_7" />
    </ifgreater>
    <ifgreater name="ifgreater_5" type="color3">
      <input name="value1" type="float" value="5.0" />
      <input name="value2" type="float" interfacename="which" />
      <input name="in1" type="color3" interfacename="in5" />
      <input name="in2" type="color3" nodename="ifgreater_6" />
    </ifgreater>
    <ifgreater name="ifgreater_4" type="color3">
      <input name="value1" type="float" value="4.0" />
      <input name="value2" type="float" interfacename="which" />
      <input name="in1" type="color3" interfacename="in4" />
      <input name="in2" type="color3" nodename="ifgreater_5" />
    </ifgreater>
    <ifgreater name="ifgreater_3" type="color3">
      <input name="value1" type="float" value="3.0" />
      <input name="value2" type="float" interfacename="which" />
      <input name="in1" type="color3" interfacename="in3" />
      <input name="in2" type="color3" nodename="ifgreater_4" />
    </ifgreater>
    <ifgreater name="ifgreater_2" type="color3">
      <input name="value1" type="float" value="2.0" />
      <input name="value2" type="float" interfacename="which" />
      <input name="in1" type="color3" interfacename="in2" />
      <input name="in2" type="color3" nodename="ifgreater_3" />
    </ifgreater>
    <ifgreater name="ifgreater_1" type="color3">
      <input name="value1" type="float" value="1.0" />
      <input name="value2" type="float" interfacename="which" />
      <input name="in1" type="color3" interfacename="in1" />
      <input name="in2" type="color3" nodename="ifgreater_2" />
    </ifgreater>
    <output name="out" type="color3" nodename="ifgreater_1" />
  </nodegraph>
  <nodegraph name="NG_switch_color4" nodedef="ND_switch_color4">
    <ifgreater name="ifgreater_10" type="color4">
      <input name="value1" type="float" value="10.0" />
      <input name="value2" type="float" interfacename="which" />
      <input name="in1" type="color4" interfacename="in10" />
      <input name="in2" type="color4" value="0,0,0,0" />
    </ifgreater>
    <ifgreater name="ifgreater_9" type="color4">
      <input name="value1" type="float" value="9.0" />
      <input name="value2" type="float" interfacename="which" />
      <input name="in1" type="color4" interfacename="in9" />
      <input name="in2" type="color4" nodename="ifgreater_10" />
    </ifgreater>
    <ifgreater name="ifgreater_8" type="color4">
      <input name="value1" type="float" value="8.0" />
      <input name="value2" type="float" interfacename="which" />
      <input name="in1" type="color4" interfacename="in8" />
      <input name="in2" type="color4" nodename="ifgreater_9" />
    </ifgreater>
    <ifgreater name="ifgreater_7" type="color4">
      <input name="value1" type="float" value="7.0" />
      <input name="value2" type="float" interfacename="which" />
      <input name="in1" type="color4" interfacename="in7" />
      <input name="in2" type="color4" nodename="ifgreater_8" />
    </ifgreater>
    <ifgreater name="ifgreater_6" type="color4">
      <input name="value1" type="float" value="6.0" />
      <input name="value2" type="float" interfacename="which" />
      <input name="in1" type="color4" interfacename="in6" />
      <input name="in2" type="color4" nodename="ifgreater_7" />
    </ifgreater>
    <ifgreater name="ifgreater_5" type="color4">
      <input name="value1" type="float" value="5.0" />
      <input name="value2" type="float" interfacename="which" />
      <input name="in1" type="color4" interfacename="in5" />
      <input name="in2" type="color4" nodename="ifgreater_6" />
    </ifgreater>
    <ifgreater name="ifgreater_4" type="color4">
      <input name="value1" type="float" value="4.0" />
      <input name="value2" type="float" interfacename="which" />
      <input name="in1" type="color4" interfacename="in4" />
      <input name="in2" type="color4" nodename="ifgreater_5" />
    </ifgreater>
    <ifgreater name="ifgreater_3" type="color4">
      <input name="value1" type="float" value="3.0" />
      <input name="value2" type="float" interfacename="which" />
      <input name="in1" type="color4" interfacename="in3" />
      <input name="in2" type="color4" nodename="ifgreater_4" />
    </ifgreater>
    <ifgreater name="ifgreater_2" type="color4">
      <input name="value1" type="float" value="2.0" />
      <input name="value2" type="float" interfacename="which" />
      <input name="in1" type="color4" interfacename="in2" />
      <input name="in2" type="color4" nodename="ifgreater_3" />
    </ifgreater>
    <ifgreater name="ifgreater_1" type="color4">
      <input name="value1" type="float" value="1.0" />
      <input name="value2" type="float" interfacename="which" />
      <input name="in1" type="color4" interfacename="in1" />
      <input name="in2" type="color4" nodename="ifgreater_2" />
    </ifgreater>
    <output name="out" type="color4" nodename="ifgreater_1" />
  </nodegraph>
  <nodegraph name="NG_switch_vector2" nodedef="ND_switch_vector2">
    <ifgreater name="ifgreater_10" type="vector2">
      <input name="value1" type="float" value="10.0" />
      <input name="value2" type="float" interfacename="which" />
      <input name="in1" type="vector2" interfacename="in10" />
      <input name="in2" type="vector2" value="0,0" />
    </ifgreater>
    <ifgreater name="ifgreater_9" type="vector2">
      <input name="value1" type="float" value="9.0" />
      <input name="value2" type="float" interfacename="which" />
      <input name="in1" type="vector2" interfacename="in9" />
      <input name="in2" type="vector2" nodename="ifgreater_10" />
    </ifgreater>
    <ifgreater name="ifgreater_8" type="vector2">
      <input name="value1" type="float" value="8.0" />
      <input name="value2" type="float" interfacename="which" />
      <input name="in1" type="vector2" interfacename="in8" />
      <input name="in2" type="vector2" nodename="ifgreater_9" />
    </ifgreater>
    <ifgreater name="ifgreater_7" type="vector2">
      <input name="value1" type="float" value="7.0" />
      <input name="value2" type="float" interfacename="which" />
      <input name="in1" type="vector2" interfacename="in7" />
      <input name="in2" type="vector2" nodename="ifgreater_8" />
    </ifgreater>
    <ifgreater name="ifgreater_6" type="vector2">
      <input name="value1" type="float" value="6.0" />
      <input name="value2" type="float" interfacename="which" />
      <input name="in1" type="vector2" interfacename="in6" />
      <input name="in2" type="vector2" nodename="ifgreater_7" />
    </ifgreater>
    <ifgreater name="ifgreater_5" type="vector2">
      <input name="value1" type="float" value="5.0" />
      <input name="value2" type="float" interfacename="which" />
      <input name="in1" type="vector2" interfacename="in5" />
      <input name="in2" type="vector2" nodename="ifgreater_6" />
    </ifgreater>
    <ifgreater name="ifgreater_4" type="vector2">
      <input name="value1" type="float" value="4.0" />
      <input name="value2" type="float" interfacename="which" />
      <input name="in1" type="vector2" interfacename="in4" />
      <input name="in2" type="vector2" nodename="ifgreater_5" />
    </ifgreater>
    <ifgreater name="ifgreater_3" type="vector2">
      <input name="value1" type="float" value="3.0" />
      <input name="value2" type="float" interfacename="which" />
      <input name="in1" type="vector2" interfacename="in3" />
      <input name="in2" type="vector2" nodename="ifgreater_4" />
    </ifgreater>
    <ifgreater name="ifgreater_2" type="vector2">
      <input name="value1" type="float" value="2.0" />
      <input name="value2" type="float" interfacename="which" />
      <input name="in1" type="vector2" interfacename="in2" />
      <input name="in2" type="vector2" nodename="ifgreater_3" />
    </ifgreater>
    <ifgreater name="ifgreater_1" type="vector2">
      <input name="value1" type="float" value="1.0" />
      <input name="value2" type="float" interfacename="which" />
      <input name="in1" type="vector2" interfacename="in1" />
      <input name="in2" type="vector2" nodename="ifgreater_2" />
    </ifgreater>
    <output name="out" type="vector2" nodename="ifgreater_1" />
  </nodegraph>
  <nodegraph name="NG_switch_vector3" nodedef="ND_switch_vector3">
    <ifgreater name="ifgreater_10" type="vector3">
      <input name="value1" type="float" value="10.0" />
      <input name="value2" type="float" interfacename="which" />
      <input name="in1" type="vector3" interfacename="in10" />
      <input name="in2" type="vector3" value="0,0,0" />
    </ifgreater>
    <ifgreater name="ifgreater_9" type="vector3">
      <input name="value1" type="float" value="9.0" />
      <input name="value2" type="float" interfacename="which" />
      <input name="in1" type="vector3" interfacename="in9" />
      <input name="in2" type="vector3" nodename="ifgreater_10" />
    </ifgreater>
    <ifgreater name="ifgreater_8" type="vector3">
      <input name="value1" type="float" value="8.0" />
      <input name="value2" type="float" interfacename="which" />
      <input name="in1" type="vector3" interfacename="in8" />
      <input name="in2" type="vector3" nodename="ifgreater_9" />
    </ifgreater>
    <ifgreater name="ifgreater_7" type="vector3">
      <input name="value1" type="float" value="7.0" />
      <input name="value2" type="float" interfacename="which" />
      <input name="in1" type="vector3" interfacename="in7" />
      <input name="in2" type="vector3" nodename="ifgreater_8" />
    </ifgreater>
    <ifgreater name="ifgreater_6" type="vector3">
      <input name="value1" type="float" value="6.0" />
      <input name="value2" type="float" interfacename="which" />
      <input name="in1" type="vector3" interfacename="in6" />
      <input name="in2" type="vector3" nodename="ifgreater_7" />
    </ifgreater>
    <ifgreater name="ifgreater_5" type="vector3">
      <input name="value1" type="float" value="5.0" />
      <input name="value2" type="float" interfacename="which" />
      <input name="in1" type="vector3" interfacename="in5" />
      <input name="in2" type="vector3" nodename="ifgreater_6" />
    </ifgreater>
    <ifgreater name="ifgreater_4" type="vector3">
      <input name="value1" type="float" value="4.0" />
      <input name="value2" type="float" interfacename="which" />
      <input name="in1" type="vector3" interfacename="in4" />
      <input name="in2" type="vector3" nodename="ifgreater_5" />
    </ifgreater>
    <ifgreater name="ifgreater_3" type="vector3">
      <input name="value1" type="float" value="3.0" />
      <input name="value2" type="float" interfacename="which" />
      <input name="in1" type="vector3" interfacename="in3" />
      <input name="in2" type="vector3" nodename="ifgreater_4" />
    </ifgreater>
    <ifgreater name="ifgreater_2" type="vector3">
      <input name="value1" type="float" value="2.0" />
      <input name="value2" type="float" interfacename="which" />
      <input name="in1" type="vector3" interfacename="in2" />
      <input name="in2" type="vector3" nodename="ifgreater_3" />
    </ifgreater>
    <ifgreater name="ifgreater_1" type="vector3">
      <input name="value1" type="float" value="1.0" />
      <input name="value2" type="float" interfacename="which" />
      <input name="in1" type="vector3" interfacename="in1" />
      <input name="in2" type="vector3" nodename="ifgreater_2" />
    </ifgreater>
    <output name="out" type="vector3" nodename="ifgreater_1" />
  </nodegraph>
  <nodegraph name="NG_switch_vector4" nodedef="ND_switch_vector4">
    <ifgreater name="ifgreater_10" type="vector4">
      <input name="value1" type="float" value="10.0" />
      <input name="value2" type="float" interfacename="which" />
      <input name="in1" type="vector4" interfacename="in10" />
      <input name="in2" type="vector4" value="0,0,0,0" />
    </ifgreater>
    <ifgreater name="ifgreater_9" type="vector4">
      <input name="value1" type="float" value="9.0" />
      <input name="value2" type="float" interfacename="which" />
      <input name="in1" type="vector4" interfacename="in9" />
      <input name="in2" type="vector4" nodename="ifgreater_10" />
    </ifgreater>
    <ifgreater name="ifgreater_8" type="vector4">
      <input name="value1" type="float" value="8.0" />
      <input name="value2" type="float" interfacename="which" />
      <input name="in1" type="vector4" interfacename="in8" />
      <input name="in2" type="vector4" nodename="ifgreater_9" />
    </ifgreater>
    <ifgreater name="ifgreater_7" type="vector4">
      <input name="value1" type="float" value="7.0" />
      <input name="value2" type="float" interfacename="which" />
      <input name="in1" type="vector4" interfacename="in7" />
      <input name="in2" type="vector4" nodename="ifgreater_8" />
    </ifgreater>
    <ifgreater name="ifgreater_6" type="vector4">
      <input name="value1" type="float" value="6.0" />
      <input name="value2" type="float" interfacename="which" />
      <input name="in1" type="vector4" interfacename="in6" />
      <input name="in2" type="vector4" nodename="ifgreater_7" />
    </ifgreater>
    <ifgreater name="ifgreater_5" type="vector4">
      <input name="value1" type="float" value="5.0" />
      <input name="value2" type="float" interfacename="which" />
      <input name="in1" type="vector4" interfacename="in5" />
      <input name="in2" type="vector4" nodename="ifgreater_6" />
    </ifgreater>
    <ifgreater name="ifgreater_4" type="vector4">
      <input name="value1" type="float" value="4.0" />
      <input name="value2" type="float" interfacename="which" />
      <input name="in1" type="vector4" interfacename="in4" />
      <input name="in2" type="vector4" nodename="ifgreater_5" />
    </ifgreater>
    <ifgreater name="ifgreater_3" type="vector4">
      <input name="value1" type="float" value="3.0" />
      <input name="value2" type="float" interfacename="which" />
      <input name="in1" type="vector4" interfacename="in3" />
      <input name="in2" type="vector4" nodename="ifgreater_4" />
    </ifgreater>
    <ifgreater name="ifgreater_2" type="vector4">
      <input name="value1" type="float" value="2.0" />
      <input name="value2" type="float" interfacename="which" />
      <input name="in1" type="vector4" interfacename="in2" />
      <input name="in2" type="vector4" nodename="ifgreater_3" />
    </ifgreater>
    <ifgreater name="ifgreater_1" type="vector4">
      <input name="value1" type="float" value="1.0" />
      <input name="value2" type="float" interfacename="which" />
      <input name="in1" type="vector4" interfacename="in1" />
      <input name="in2" type="vector4" nodename="ifgreater_2" />
    </ifgreater>
    <output name="out" type="vector4" nodename="ifgreater_1" />
  </nodegraph>
  <nodegraph name="NG_switch_matrix33" nodedef="ND_switch_matrix33">
    <ifgreater name="ifgreater_10" type="matrix33">
      <input name="value1" type="float" value="10.0" />
      <input name="value2" type="float" interfacename="which" />
      <input name="in1" type="matrix33" interfacename="in10" />
      <input name="in2" type="matrix33" value="0,0,0,0,0,0,0,0,0" />
    </ifgreater>
    <ifgreater name="ifgreater_9" type="matrix33">
      <input name="value1" type="float" value="9.0" />
      <input name="value2" type="float" interfacename="which" />
      <input name="in1" type="matrix33" interfacename="in9" />
      <input name="in2" type="matrix33" nodename="ifgreater_10" />
    </ifgreater>
    <ifgreater name="ifgreater_8" type="matrix33">
      <input name="value1" type="float" value="8.0" />
      <input name="value2" type="float" interfacename="which" />
      <input name="in1" type="matrix33" interfacename="in8" />
      <input name="in2" type="matrix33" nodename="ifgreater_9" />
    </ifgreater>
    <ifgreater name="ifgreater_7" type="matrix33">
      <input name="value1" type="float" value="7.0" />
      <input name="value2" type="float" interfacename="which" />
      <input name="in1" type="matrix33" interfacename="in7" />
      <input name="in2" type="matrix33" nodename="ifgreater_8" />
    </ifgreater>
    <ifgreater name="ifgreater_6" type="matrix33">
      <input name="value1" type="float" value="6.0" />
      <input name="value2" type="float" interfacename="which" />
      <input name="in1" type="matrix33" interfacename="in6" />
      <input name="in2" type="matrix33" nodename="ifgreater_7" />
    </ifgreater>
    <ifgreater name="ifgreater_5" type="matrix33">
      <input name="value1" type="float" value="5.0" />
      <input name="value2" type="float" interfacename="which" />
      <input name="in1" type="matrix33" interfacename="in5" />
      <input name="in2" type="matrix33" nodename="ifgreater_6" />
    </ifgreater>
    <ifgreater name="ifgreater_4" type="matrix33">
      <input name="value1" type="float" value="4.0" />
      <input name="value2" type="float" interfacename="which" />
      <input name="in1" type="matrix33" interfacename="in4" />
      <input name="in2" type="matrix33" nodename="ifgreater_5" />
    </ifgreater>
    <ifgreater name="ifgreater_3" type="matrix33">
      <input name="value1" type="float" value="3.0" />
      <input name="value2" type="float" interfacename="which" />
      <input name="in1" type="matrix33" interfacename="in3" />
      <input name="in2" type="matrix33" nodename="ifgreater_4" />
    </ifgreater>
    <ifgreater name="ifgreater_2" type="matrix33">
      <input name="value1" type="float" value="2.0" />
      <input name="value2" type="float" interfacename="which" />
      <input name="in1" type="matrix33" interfacename="in2" />
      <input name="in2" type="matrix33" nodename="ifgreater_3" />
    </ifgreater>
    <ifgreater name="ifgreater_1" type="matrix33">
      <input name="value1" type="float" value="1.0" />
      <input name="value2" type="float" interfacename="which" />
      <input name="in1" type="matrix33" interfacename="in1" />
      <input name="in2" type="matrix33" nodename="ifgreater_2" />
    </ifgreater>
    <output name="out" type="matrix33" nodename="ifgreater_1" />
  </nodegraph>
  <nodegraph name="NG_switch_matrix44" nodedef="ND_switch_matrix44">
    <ifgreater name="ifgreater_10" type="matrix44">
      <input name="value1" type="float" value="10.0" />
      <input name="value2" type="float" interfacename="which" />
      <input name="in1" type="matrix44" interfacename="in10" />
      <input name="in2" type="matrix44" value="0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0" />
    </ifgreater>
    <ifgreater name="ifgreater_9" type="matrix44">
      <input name="value1" type="float" value="9.0" />
      <input name="value2" type="float" interfacename="which" />
      <input name="in1" type="matrix44" interfacename="in9" />
      <input name="in2" type="matrix44" nodename="ifgreater_10" />
    </ifgreater>
    <ifgreater name="ifgreater_8" type="matrix44">
      <input name="value1" type="float" value="8.0" />
      <input name="value2" type="float" interfacename="which" />
      <input name="in1" type="matrix44" interfacename="in8" />
      <input name="in2" type="matrix44" nodename="ifgreater_9" />
    </ifgreater>
    <ifgreater name="ifgreater_7" type="matrix44">
      <input name="value1" type="float" value="7.0" />
      <input name="value2" type="float" interfacename="which" />
      <input name="in1" type="matrix44" interfacename="in7" />
      <input name="in2" type="matrix44" nodename="ifgreater_8" />
    </ifgreater>
    <ifgreater name="ifgreater_6" type="matrix44">
      <input name="value1" type="float" value="6.0" />
      <input name="value2" type="float" interfacename="which" />
      <input name="in1" type="matrix44" interfacename="in6" />
      <input name="in2" type="matrix44" nodename="ifgreater_7" />
    </ifgreater>
    <ifgreater name="ifgreater_5" type="matrix44">
      <input name="value1" type="float" value="5.0" />
      <input name="value2" type="float" interfacename="which" />
      <input name="in1" type="matrix44" interfacename="in5" />
      <input name="in2" type="matrix44" nodename="ifgreater_6" />
    </ifgreater>
    <ifgreater name="ifgreater_4" type="matrix44">
      <input name="value1" type="float" value="4.0" />
      <input name="value2" type="float" interfacename="which" />
      <input name="in1" type="matrix44" interfacename="in4" />
      <input name="in2" type="matrix44" nodename="ifgreater_5" />
    </ifgreater>
    <ifgreater name="ifgreater_3" type="matrix44">
      <input name="value1" type="float" value="3.0" />
      <input name="value2" type="float" interfacename="which" />
      <input name="in1" type="matrix44" interfacename="in3" />
      <input name="in2" type="matrix44" nodename="ifgreater_4" />
    </ifgreater>
    <ifgreater name="ifgreater_2" type="matrix44">
      <input name="value1" type="float" value="2.0" />
      <input name="value2" type="float" interfacename="which" />
      <input name="in1" type="matrix44" interfacename="in2" />
      <input name="in2" type="matrix44" nodename="ifgreater_3" />
    </ifgreater>
    <ifgreater name="ifgreater_1" type="matrix44">
      <input name="value1" type="float" value="1.0" />
      <input name="value2" type="float" interfacename="which" />
      <input name="in1" type="matrix44" interfacename="in1" />
      <input name="in2" type="matrix44" nodename="ifgreater_2" />
    </ifgreater>
    <output name="out" type="matrix44" nodename="ifgreater_1" />
  </nodegraph>

  <nodegraph name="NG_switch_floatI" nodedef="ND_switch_floatI">
    <ifgreater name="ifgreater_10" type="float">
      <input name="value1" type="integer" value="10" />
      <input name="value2" type="integer" interfacename="which" />
      <input name="in1" type="float" interfacename="in10" />
      <input name="in2" type="float" value="0.0" />
    </ifgreater>
    <ifgreater name="ifgreater_9" type="float">
      <input name="value1" type="integer" value="9" />
      <input name="value2" type="integer" interfacename="which" />
      <input name="in1" type="float" interfacename="in9" />
      <input name="in2" type="float" nodename="ifgreater_10" />
    </ifgreater>
    <ifgreater name="ifgreater_8" type="float">
      <input name="value1" type="integer" value="8" />
      <input name="value2" type="integer" interfacename="which" />
      <input name="in1" type="float" interfacename="in8" />
      <input name="in2" type="float" nodename="ifgreater_9" />
    </ifgreater>
    <ifgreater name="ifgreater_7" type="float">
      <input name="value1" type="integer" value="7" />
      <input name="value2" type="integer" interfacename="which" />
      <input name="in1" type="float" interfacename="in7" />
      <input name="in2" type="float" nodename="ifgreater_8" />
    </ifgreater>
    <ifgreater name="ifgreater_6" type="float">
      <input name="value1" type="integer" value="6" />
      <input name="value2" type="integer" interfacename="which" />
      <input name="in1" type="float" interfacename="in6" />
      <input name="in2" type="float" nodename="ifgreater_7" />
    </ifgreater>
    <ifgreater name="ifgreater_5" type="float">
      <input name="value1" type="integer" value="5" />
      <input name="value2" type="integer" interfacename="which" />
      <input name="in1" type="float" interfacename="in5" />
      <input name="in2" type="float" nodename="ifgreater_6" />
    </ifgreater>
    <ifgreater name="ifgreater_4" type="float">
      <input name="value1" type="integer" value="4" />
      <input name="value2" type="integer" interfacename="which" />
      <input name="in1" type="float" interfacename="in4" />
      <input name="in2" type="float" nodename="ifgreater_5" />
    </ifgreater>
    <ifgreater name="ifgreater_3" type="float">
      <input name="value1" type="integer" value="3" />
      <input name="value2" type="integer" interfacename="which" />
      <input name="in1" type="float" interfacename="in3" />
      <input name="in2" type="float" nodename="ifgreater_4" />
    </ifgreater>
    <ifgreater name="ifgreater_2" type="float">
      <input name="value1" type="integer" value="2" />
      <input name="value2" type="integer" interfacename="which" />
      <input name="in1" type="float" interfacename="in2" />
      <input name="in2" type="float" nodename="ifgreater_3" />
    </ifgreater>
    <ifgreater name="ifgreater_1" type="float">
      <input name="value1" type="integer" value="1" />
      <input name="value2" type="integer" interfacename="which" />
      <input name="in1" type="float" interfacename="in1" />
      <input name="in2" type="float" nodename="ifgreater_2" />
    </ifgreater>
    <output name="out" type="float" nodename="ifgreater_1" />
  </nodegraph>
  <nodegraph name="NG_switch_color3I" nodedef="ND_switch_color3I">
    <ifgreater name="ifgreater_10" type="color3">
      <input name="value1" type="integer" value="10" />
      <input name="value2" type="integer" interfacename="which" />
      <input name="in1" type="color3" interfacename="in10" />
      <input name="in2" type="color3" value="0,0,0" />
    </ifgreater>
    <ifgreater name="ifgreater_9" type="color3">
      <input name="value1" type="integer" value="9" />
      <input name="value2" type="integer" interfacename="which" />
      <input name="in1" type="color3" interfacename="in9" />
      <input name="in2" type="color3" nodename="ifgreater_10" />
    </ifgreater>
    <ifgreater name="ifgreater_8" type="color3">
      <input name="value1" type="integer" value="8" />
      <input name="value2" type="integer" interfacename="which" />
      <input name="in1" type="color3" interfacename="in8" />
      <input name="in2" type="color3" nodename="ifgreater_9" />
    </ifgreater>
    <ifgreater name="ifgreater_7" type="color3">
      <input name="value1" type="integer" value="7" />
      <input name="value2" type="integer" interfacename="which" />
      <input name="in1" type="color3" interfacename="in7" />
      <input name="in2" type="color3" nodename="ifgreater_8" />
    </ifgreater>
    <ifgreater name="ifgreater_6" type="color3">
      <input name="value1" type="integer" value="6" />
      <input name="value2" type="integer" interfacename="which" />
      <input name="in1" type="color3" interfacename="in6" />
      <input name="in2" type="color3" nodename="ifgreater_7" />
    </ifgreater>
    <ifgreater name="ifgreater_5" type="color3">
      <input name="value1" type="integer" value="5" />
      <input name="value2" type="integer" interfacename="which" />
      <input name="in1" type="color3" interfacename="in5" />
      <input name="in2" type="color3" nodename="ifgreater_6" />
    </ifgreater>
    <ifgreater name="ifgreater_4" type="color3">
      <input name="value1" type="integer" value="4" />
      <input name="value2" type="integer" interfacename="which" />
      <input name="in1" type="color3" interfacename="in4" />
      <input name="in2" type="color3" nodename="ifgreater_5" />
    </ifgreater>
    <ifgreater name="ifgreater_3" type="color3">
      <input name="value1" type="integer" value="3" />
      <input name="value2" type="integer" interfacename="which" />
      <input name="in1" type="color3" interfacename="in3" />
      <input name="in2" type="color3" nodename="ifgreater_4" />
    </ifgreater>
    <ifgreater name="ifgreater_2" type="color3">
      <input name="value1" type="integer" value="2" />
      <input name="value2" type="integer" interfacename="which" />
      <input name="in1" type="color3" interfacename="in2" />
      <input name="in2" type="color3" nodename="ifgreater_3" />
    </ifgreater>
    <ifgreater name="ifgreater_1" type="color3">
      <input name="value1" type="integer" value="1" />
      <input name="value2" type="integer" interfacename="which" />
      <input name="in1" type="color3" interfacename="in1" />
      <input name="in2" type="color3" nodename="ifgreater_2" />
    </ifgreater>
    <output name="out" type="color3" nodename="ifgreater_1" />
  </nodegraph>
  <nodegraph name="NG_switch_color4I" nodedef="ND_switch_color4I">
    <ifgreater name="ifgreater_10" type="color4">
      <input name="value1" type="integer" value="10" />
      <input name="value2" type="integer" interfacename="which" />
      <input name="in1" type="color4" interfacename="in10" />
      <input name="in2" type="color4" value="0,0,0,0" />
    </ifgreater>
    <ifgreater name="ifgreater_9" type="color4">
      <input name="value1" type="integer" value="9" />
      <input name="value2" type="integer" interfacename="which" />
      <input name="in1" type="color4" interfacename="in9" />
      <input name="in2" type="color4" nodename="ifgreater_10" />
    </ifgreater>
    <ifgreater name="ifgreater_8" type="color4">
      <input name="value1" type="integer" value="8" />
      <input name="value2" type="integer" interfacename="which" />
      <input name="in1" type="color4" interfacename="in8" />
      <input name="in2" type="color4" nodename="ifgreater_9" />
    </ifgreater>
    <ifgreater name="ifgreater_7" type="color4">
      <input name="value1" type="integer" value="7" />
      <input name="value2" type="integer" interfacename="which" />
      <input name="in1" type="color4" interfacename="in7" />
      <input name="in2" type="color4" nodename="ifgreater_8" />
    </ifgreater>
    <ifgreater name="ifgreater_6" type="color4">
      <input name="value1" type="integer" value="6" />
      <input name="value2" type="integer" interfacename="which" />
      <input name="in1" type="color4" interfacename="in6" />
      <input name="in2" type="color4" nodename="ifgreater_7" />
    </ifgreater>
    <ifgreater name="ifgreater_5" type="color4">
      <input name="value1" type="integer" value="5" />
      <input name="value2" type="integer" interfacename="which" />
      <input name="in1" type="color4" interfacename="in5" />
      <input name="in2" type="color4" nodename="ifgreater_6" />
    </ifgreater>
    <ifgreater name="ifgreater_4" type="color4">
      <input name="value1" type="integer" value="4" />
      <input name="value2" type="integer" interfacename="which" />
      <input name="in1" type="color4" interfacename="in4" />
      <input name="in2" type="color4" nodename="ifgreater_5" />
    </ifgreater>
    <ifgreater name="ifgreater_3" type="color4">
      <input name="value1" type="integer" value="3" />
      <input name="value2" type="integer" interfacename="which" />
      <input name="in1" type="color4" interfacename="in3" />
      <input name="in2" type="color4" nodename="ifgreater_4" />
    </ifgreater>
    <ifgreater name="ifgreater_2" type="color4">
      <input name="value1" type="integer" value="2" />
      <input name="value2" type="integer" interfacename="which" />
      <input name="in1" type="color4" interfacename="in2" />
      <input name="in2" type="color4" nodename="ifgreater_3" />
    </ifgreater>
    <ifgreater name="ifgreater_1" type="color4">
      <input name="value1" type="integer" value="1" />
      <input name="value2" type="integer" interfacename="which" />
      <input name="in1" type="color4" interfacename="in1" />
      <input name="in2" type="color4" nodename="ifgreater_2" />
    </ifgreater>
    <output name="out" type="color4" nodename="ifgreater_1" />
  </nodegraph>
  <nodegraph name="NG_switch_vector2I" nodedef="ND_switch_vector2I">
    <ifgreater name="ifgreater_10" type="vector2">
      <input name="value1" type="integer" value="10" />
      <input name="value2" type="integer" interfacename="which" />
      <input name="in1" type="vector2" interfacename="in10" />
      <input name="in2" type="vector2" value="0,0" />
    </ifgreater>
    <ifgreater name="ifgreater_9" type="vector2">
      <input name="value1" type="integer" value="9" />
      <input name="value2" type="integer" interfacename="which" />
      <input name="in1" type="vector2" interfacename="in9" />
      <input name="in2" type="vector2" nodename="ifgreater_10" />
    </ifgreater>
    <ifgreater name="ifgreater_8" type="vector2">
      <input name="value1" type="integer" value="8" />
      <input name="value2" type="integer" interfacename="which" />
      <input name="in1" type="vector2" interfacename="in8" />
      <input name="in2" type="vector2" nodename="ifgreater_9" />
    </ifgreater>
    <ifgreater name="ifgreater_7" type="vector2">
      <input name="value1" type="integer" value="7" />
      <input name="value2" type="integer" interfacename="which" />
      <input name="in1" type="vector2" interfacename="in7" />
      <input name="in2" type="vector2" nodename="ifgreater_8" />
    </ifgreater>
    <ifgreater name="ifgreater_6" type="vector2">
      <input name="value1" type="integer" value="6" />
      <input name="value2" type="integer" interfacename="which" />
      <input name="in1" type="vector2" interfacename="in6" />
      <input name="in2" type="vector2" nodename="ifgreater_7" />
    </ifgreater>
    <ifgreater name="ifgreater_5" type="vector2">
      <input name="value1" type="integer" value="5" />
      <input name="value2" type="integer" interfacename="which" />
      <input name="in1" type="vector2" interfacename="in5" />
      <input name="in2" type="vector2" nodename="ifgreater_6" />
    </ifgreater>
    <ifgreater name="ifgreater_4" type="vector2">
      <input name="value1" type="integer" value="4" />
      <input name="value2" type="integer" interfacename="which" />
      <input name="in1" type="vector2" interfacename="in4" />
      <input name="in2" type="vector2" nodename="ifgreater_5" />
    </ifgreater>
    <ifgreater name="ifgreater_3" type="vector2">
      <input name="value1" type="integer" value="3" />
      <input name="value2" type="integer" interfacename="which" />
      <input name="in1" type="vector2" interfacename="in3" />
      <input name="in2" type="vector2" nodename="ifgreater_4" />
    </ifgreater>
    <ifgreater name="ifgreater_2" type="vector2">
      <input name="value1" type="integer" value="2" />
      <input name="value2" type="integer" interfacename="which" />
      <input name="in1" type="vector2" interfacename="in2" />
      <input name="in2" type="vector2" nodename="ifgreater_3" />
    </ifgreater>
    <ifgreater name="ifgreater_1" type="vector2">
      <input name="value1" type="integer" value="1" />
      <input name="value2" type="integer" interfacename="which" />
      <input name="in1" type="vector2" interfacename="in1" />
      <input name="in2" type="vector2" nodename="ifgreater_2" />
    </ifgreater>
    <output name="out" type="vector2" nodename="ifgreater_1" />
  </nodegraph>
  <nodegraph name="NG_switch_vector3I" nodedef="ND_switch_vector3I">
    <ifgreater name="ifgreater_10" type="vector3">
      <input name="value1" type="integer" value="10" />
      <input name="value2" type="integer" interfacename="which" />
      <input name="in1" type="vector3" interfacename="in10" />
      <input name="in2" type="vector3" value="0,0,0" />
    </ifgreater>
    <ifgreater name="ifgreater_9" type="vector3">
      <input name="value1" type="integer" value="9" />
      <input name="value2" type="integer" interfacename="which" />
      <input name="in1" type="vector3" interfacename="in9" />
      <input name="in2" type="vector3" nodename="ifgreater_10" />
    </ifgreater>
    <ifgreater name="ifgreater_8" type="vector3">
      <input name="value1" type="integer" value="8" />
      <input name="value2" type="integer" interfacename="which" />
      <input name="in1" type="vector3" interfacename="in8" />
      <input name="in2" type="vector3" nodename="ifgreater_9" />
    </ifgreater>
    <ifgreater name="ifgreater_7" type="vector3">
      <input name="value1" type="integer" value="7" />
      <input name="value2" type="integer" interfacename="which" />
      <input name="in1" type="vector3" interfacename="in7" />
      <input name="in2" type="vector3" nodename="ifgreater_8" />
    </ifgreater>
    <ifgreater name="ifgreater_6" type="vector3">
      <input name="value1" type="integer" value="6" />
      <input name="value2" type="integer" interfacename="which" />
      <input name="in1" type="vector3" interfacename="in6" />
      <input name="in2" type="vector3" nodename="ifgreater_7" />
    </ifgreater>
    <ifgreater name="ifgreater_5" type="vector3">
      <input name="value1" type="integer" value="5" />
      <input name="value2" type="integer" interfacename="which" />
      <input name="in1" type="vector3" interfacename="in5" />
      <input name="in2" type="vector3" nodename="ifgreater_6" />
    </ifgreater>
    <ifgreater name="ifgreater_4" type="vector3">
      <input name="value1" type="integer" value="4" />
      <input name="value2" type="integer" interfacename="which" />
      <input name="in1" type="vector3" interfacename="in4" />
      <input name="in2" type="vector3" nodename="ifgreater_5" />
    </ifgreater>
    <ifgreater name="ifgreater_3" type="vector3">
      <input name="value1" type="integer" value="3" />
      <input name="value2" type="integer" interfacename="which" />
      <input name="in1" type="vector3" interfacename="in3" />
      <input name="in2" type="vector3" nodename="ifgreater_4" />
    </ifgreater>
    <ifgreater name="ifgreater_2" type="vector3">
      <input name="value1" type="integer" value="2" />
      <input name="value2" type="integer" interfacename="which" />
      <input name="in1" type="vector3" interfacename="in2" />
      <input name="in2" type="vector3" nodename="ifgreater_3" />
    </ifgreater>
    <ifgreater name="ifgreater_1" type="vector3">
      <input name="value1" type="integer" value="1" />
      <input name="value2" type="integer" interfacename="which" />
      <input name="in1" type="vector3" interfacename="in1" />
      <input name="in2" type="vector3" nodename="ifgreater_2" />
    </ifgreater>
    <output name="out" type="vector3" nodename="ifgreater_1" />
  </nodegraph>
  <nodegraph name="NG_switch_vector4I" nodedef="ND_switch_vector4I">
    <ifgreater name="ifgreater_10" type="vector4">
      <input name="value1" type="integer" value="10" />
      <input name="value2" type="integer" interfacename="which" />
      <input name="in1" type="vector4" interfacename="in10" />
      <input name="in2" type="vector4" value="0,0,0,0" />
    </ifgreater>
    <ifgreater name="ifgreater_9" type="vector4">
      <input name="value1" type="integer" value="9" />
      <input name="value2" type="integer" interfacename="which" />
      <input name="in1" type="vector4" interfacename="in9" />
      <input name="in2" type="vector4" nodename="ifgreater_10" />
    </ifgreater>
    <ifgreater name="ifgreater_8" type="vector4">
      <input name="value1" type="integer" value="8" />
      <input name="value2" type="integer" interfacename="which" />
      <input name="in1" type="vector4" interfacename="in8" />
      <input name="in2" type="vector4" nodename="ifgreater_9" />
    </ifgreater>
    <ifgreater name="ifgreater_7" type="vector4">
      <input name="value1" type="integer" value="7" />
      <input name="value2" type="integer" interfacename="which" />
      <input name="in1" type="vector4" interfacename="in7" />
      <input name="in2" type="vector4" nodename="ifgreater_8" />
    </ifgreater>
    <ifgreater name="ifgreater_6" type="vector4">
      <input name="value1" type="integer" value="6" />
      <input name="value2" type="integer" interfacename="which" />
      <input name="in1" type="vector4" interfacename="in6" />
      <input name="in2" type="vector4" nodename="ifgreater_7" />
    </ifgreater>
    <ifgreater name="ifgreater_5" type="vector4">
      <input name="value1" type="integer" value="5" />
      <input name="value2" type="integer" interfacename="which" />
      <input name="in1" type="vector4" interfacename="in5" />
      <input name="in2" type="vector4" nodename="ifgreater_6" />
    </ifgreater>
    <ifgreater name="ifgreater_4" type="vector4">
      <input name="value1" type="integer" value="4" />
      <input name="value2" type="integer" interfacename="which" />
      <input name="in1" type="vector4" interfacename="in4" />
      <input name="in2" type="vector4" nodename="ifgreater_5" />
    </ifgreater>
    <ifgreater name="ifgreater_3" type="vector4">
      <input name="value1" type="integer" value="3" />
      <input name="value2" type="integer" interfacename="which" />
      <input name="in1" type="vector4" interfacename="in3" />
      <input name="in2" type="vector4" nodename="ifgreater_4" />
    </ifgreater>
    <ifgreater name="ifgreater_2" type="vector4">
      <input name="value1" type="integer" value="2" />
      <input name="value2" type="integer" interfacename="which" />
      <input name="in1" type="vector4" interfacename="in2" />
      <input name="in2" type="vector4" nodename="ifgreater_3" />
    </ifgreater>
    <ifgreater name="ifgreater_1" type="vector4">
      <input name="value1" type="integer" value="1" />
      <input name="value2" type="integer" interfacename="which" />
      <input name="in1" type="vector4" interfacename="in1" />
      <input name="in2" type="vector4" nodename="ifgreater_2" />
    </ifgreater>
    <output name="out" type="vector4" nodename="ifgreater_1" />
  </nodegraph>
  <nodegraph name="NG_switch_matrix33I" nodedef="ND_switch_matrix33I">
    <ifgreater name="ifgreater_10" type="matrix33">
      <input name="value1" type="integer" value="10" />
      <input name="value2" type="integer" interfacename="which" />
      <input name="in1" type="matrix33" interfacename="in10" />
      <input name="in2" type="matrix33" value="0,0,0,0,0,0,0,0,0" />
    </ifgreater>
    <ifgreater name="ifgreater_9" type="matrix33">
      <input name="value1" type="integer" value="9" />
      <input name="value2" type="integer" interfacename="which" />
      <input name="in1" type="matrix33" interfacename="in9" />
      <input name="in2" type="matrix33" nodename="ifgreater_10" />
    </ifgreater>
    <ifgreater name="ifgreater_8" type="matrix33">
      <input name="value1" type="integer" value="8" />
      <input name="value2" type="integer" interfacename="which" />
      <input name="in1" type="matrix33" interfacename="in8" />
      <input name="in2" type="matrix33" nodename="ifgreater_9" />
    </ifgreater>
    <ifgreater name="ifgreater_7" type="matrix33">
      <input name="value1" type="integer" value="7" />
      <input name="value2" type="integer" interfacename="which" />
      <input name="in1" type="matrix33" interfacename="in7" />
      <input name="in2" type="matrix33" nodename="ifgreater_8" />
    </ifgreater>
    <ifgreater name="ifgreater_6" type="matrix33">
      <input name="value1" type="integer" value="6" />
      <input name="value2" type="integer" interfacename="which" />
      <input name="in1" type="matrix33" interfacename="in6" />
      <input name="in2" type="matrix33" nodename="ifgreater_7" />
    </ifgreater>
    <ifgreater name="ifgreater_5" type="matrix33">
      <input name="value1" type="integer" value="5" />
      <input name="value2" type="integer" interfacename="which" />
      <input name="in1" type="matrix33" interfacename="in5" />
      <input name="in2" type="matrix33" nodename="ifgreater_6" />
    </ifgreater>
    <ifgreater name="ifgreater_4" type="matrix33">
      <input name="value1" type="integer" value="4" />
      <input name="value2" type="integer" interfacename="which" />
      <input name="in1" type="matrix33" interfacename="in4" />
      <input name="in2" type="matrix33" nodename="ifgreater_5" />
    </ifgreater>
    <ifgreater name="ifgreater_3" type="matrix33">
      <input name="value1" type="integer" value="3" />
      <input name="value2" type="integer" interfacename="which" />
      <input name="in1" type="matrix33" interfacename="in3" />
      <input name="in2" type="matrix33" nodename="ifgreater_4" />
    </ifgreater>
    <ifgreater name="ifgreater_2" type="matrix33">
      <input name="value1" type="integer" value="2" />
      <input name="value2" type="integer" interfacename="which" />
      <input name="in1" type="matrix33" interfacename="in2" />
      <input name="in2" type="matrix33" nodename="ifgreater_3" />
    </ifgreater>
    <ifgreater name="ifgreater_1" type="matrix33">
      <input name="value1" type="integer" value="1" />
      <input name="value2" type="integer" interfacename="which" />
      <input name="in1" type="matrix33" interfacename="in1" />
      <input name="in2" type="matrix33" nodename="ifgreater_2" />
    </ifgreater>
    <output name="out" type="matrix33" nodename="ifgreater_1" />
  </nodegraph>
  <nodegraph name="NG_switch_matrix44I" nodedef="ND_switch_matrix44I">
    <ifgreater name="ifgreater_10" type="matrix44">
      <input name="value1" type="integer" value="10" />
      <input name="value2" type="integer" interfacename="which" />
      <input name="in1" type="matrix44" interfacename="in10" />
      <input name="in2" type="matrix44" value="0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0" />
    </ifgreater>
    <ifgreater name="ifgreater_9" type="matrix44">
      <input name="value1" type="integer" value="9" />
      <input name="value2" type="integer" interfacename="which" />
      <input name="in1" type="matrix44" interfacename="in9" />
      <input name="in2" type="matrix44" nodename="ifgreater_10" />
    </ifgreater>
    <ifgreater name="ifgreater_8" type="matrix44">
      <input name="value1" type="integer" value="8" />
      <input name="value2" type="integer" interfacename="which" />
      <input name="in1" type="matrix44" interfacename="in8" />
      <input name="in2" type="matrix44" nodename="ifgreater_9" />
    </ifgreater>
    <ifgreater name="ifgreater_7" type="matrix44">
      <input name="value1" type="integer" value="7" />
      <input name="value2" type="integer" interfacename="which" />
      <input name="in1" type="matrix44" interfacename="in7" />
      <input name="in2" type="matrix44" nodename="ifgreater_8" />
    </ifgreater>
    <ifgreater name="ifgreater_6" type="matrix44">
      <input name="value1" type="integer" value="6" />
      <input name="value2" type="integer" interfacename="which" />
      <input name="in1" type="matrix44" interfacename="in6" />
      <input name="in2" type="matrix44" nodename="ifgreater_7" />
    </ifgreater>
    <ifgreater name="ifgreater_5" type="matrix44">
      <input name="value1" type="integer" value="5" />
      <input name="value2" type="integer" interfacename="which" />
      <input name="in1" type="matrix44" interfacename="in5" />
      <input name="in2" type="matrix44" nodename="ifgreater_6" />
    </ifgreater>
    <ifgreater name="ifgreater_4" type="matrix44">
      <input name="value1" type="integer" value="4" />
      <input name="value2" type="integer" interfacename="which" />
      <input name="in1" type="matrix44" interfacename="in4" />
      <input name="in2" type="matrix44" nodename="ifgreater_5" />
    </ifgreater>
    <ifgreater name="ifgreater_3" type="matrix44">
      <input name="value1" type="integer" value="3" />
      <input name="value2" type="integer" interfacename="which" />
      <input name="in1" type="matrix44" interfacename="in3" />
      <input name="in2" type="matrix44" nodename="ifgreater_4" />
    </ifgreater>
    <ifgreater name="ifgreater_2" type="matrix44">
      <input name="value1" type="integer" value="2" />
      <input name="value2" type="integer" interfacename="which" />
      <input name="in1" type="matrix44" interfacename="in2" />
      <input name="in2" type="matrix44" nodename="ifgreater_3" />
    </ifgreater>
    <ifgreater name="ifgreater_1" type="matrix44">
      <input name="value1" type="integer" value="1" />
      <input name="value2" type="integer" interfacename="which" />
      <input name="in1" type="matrix44" interfacename="in1" />
      <input name="in2" type="matrix44" nodename="ifgreater_2" />
    </ifgreater>
    <output name="out" type="matrix44" nodename="ifgreater_1" />
  </nodegraph>


  <!-- ======================================================================== -->
  <!-- Logical operator nodes                                                   -->
  <!-- ======================================================================== -->

  <!--
    Node: <xor>
    Logical XOR operation for two boolean values.
  -->
  <nodegraph name="NG_logical_xor" nodedef="ND_logical_xor">
    <not name="not_in1" type="boolean">
      <input name="in" type="boolean" interfacename="in1"/>
    </not>
    <not name="not_in2" type="boolean">
      <input name="in" type="boolean" interfacename="in2"/>
    </not>
    <and name="in1_and_not_in2" type="boolean">
      <input name="in1" type="boolean" interfacename="in1" />
      <input name="in2" type="boolean" nodename="not_in2" />
    </and>
    <and name="in2_and_not_in1" type="boolean">
      <input name="in1" type="boolean" interfacename="in2" />
      <input name="in2" type="boolean" nodename="not_in1" />
    </and>
    <or name="or" type="boolean">
      <input name="in1" type="boolean" nodename="in1_and_not_in2"/>
      <input name="in2" type="boolean" nodename="in2_and_not_in1"/>
    </or>
    <output name="out" type="boolean" nodename="or" />
  </nodegraph>

</materialx>
