<?xml version="1.0"?>
<materialx version="1.39" colorspace="acescg">
  <nodedef name="ND_lama_dielectric" node="LamaDielectric" nodegroup="pbr" doc="Lama dielectric" version="1.0" isdefaultversion="true">
    <input name="reflectionTint" type="color3" value="1.0, 1.0, 1.0" uiname="Reflection Tint" uifolder="Main"
           doc="Color multiplier for external reflection. It should be used with parsimony, as a non-white value breaks physicality." />
    <input name="transmissionTint" type="color3" value="1.0, 1.0, 1.0" uiname="Transmission Tint" uifolder="Main"
           doc="Color multiplier for rays going inside the medium (covers external transmission and internal reflection). It should be used with parcimony, as a non-white value breaks physicality. The preferred way to define the color of a dielectric is through the Interior attributes right below." />
    <input name="fresnelMode" type="integer" uniform="true" enum="Artistic,Scientific" enumvalues="0,1" value="0" uiname="Fresnel Mode" uifolder="Main"
           doc="Switches the Fresnel parameterization between Artistic and Scientific mode. The Artistic mode offers a scalar value defining the reflectivity in the normal direction. The Scientific mode proposes an index of refraction. Both parameterizations can achieve identical results, as one can be mapped to the other." />
    <input name="IOR" type="float" value="1.5" uimin="1.0" uimax="3.0" uiname="IOR" uifolder="Main"
           doc="Index of refraction (often denoted by eta), defining the amount reflected by the surface in the normal direction, and how the rays are bent by refraction." />
    <input name="reflectivity" type="float" value="0.04" uimin="0.0" uimax="1.0" uisoftmin="0.0" uisoftmax="0.25" uiname="Reflectivity" uifolder="Main"
           doc="Amount reflected by the surface in the normal direction. Also affects how the rays are bent by refraction." />
    <input name="roughness" type="float" value="0.1" uimin="0.0" uimax="1.0" uiname="Roughness" uifolder="Main"
           doc="Micro-facet distribution roughness." />
    <input name="normal" type="vector3" defaultgeomprop="Nworld" uiname="Normal" uifolder="Main"
           doc="Shading normal, typically defined by bump or normal mapping. Defaults to the smooth surface normal if not set." />
    <input name="anisotropy" type="float" value="0.0" uimin="-1.0" uimax="1.0" uiname="Anisotropy" uifolder="Anisotropy"
           doc="Defines the amount of anisotropy, changing the co-tangent axis roughness from the original value to 1 (or to 0 with a negative value)." />
    <input name="direction" type="vector3" defaultgeomprop="Tworld" uiname="Direction" uifolder="Anisotropy"
           doc="Overrides the surface tangent as the anisotropy direction." />
    <input name="rotation" type="float" value="0.0" uisoftmin="0.0" uisoftmax="1.0" uiname="Rotation" uifolder="Anisotropy"
           doc="Rotates the anisotropy direction (possibly overridden by the previous attribute) around the normal, from 0 to 360 degrees." />
    <input name="absorptionColor" type="color3" value="1.0, 1.0, 1.0" uiname="Absorption Color" uifolder="Interior"
           doc="Interior volume absorption color. It is defined as the negative logarithm of the extinction coefficient, with values between 0 and 1, such that the object itself and its shadows are tinted proportionnally. If set to 1, there is no absorption, and if set to 0, the object will be completely opaque. Note that for single scattering to kick in, the value must be inferior to 1. This value will also act as coating color, when this node is used as the top material in a layer node." />
    <input name="absorptionRadius" type="float" value="1.0" uimin="0.0" uiname="Absorption Radius" uifolder="Interior"
           doc="Distance, in world space units, at which the Absorption Color is assumed to be reached. Can be used to control the rate of absorption." />
    <input name="scatterColor" type="color3" value="0.0, 0.0, 0.0" uiname="Scatter Color" uifolder="Interior"
           doc="Color (aka scattering albedo) of the medium, defines what proportion of the light hitting volumetric particles is scattered around by the phase function (as opposed to just absorbed), for each color channel. Only takes effect if the corresponding channel has a non-null density, in other words if the Absorption Color value for that channel is inferior to 1." />
    <input name="scatterAnisotropy" type="float" value="0.0" uimin="-1.0" uimax="1.0" uiname="Scatter Anisotropy" uifolder="Interior"
           doc="Anisotropy of the medium's phase function, ranging from full backward scattering at -1 to forward scattering at 1. Only takes effect if the Scatter Color is non-null." />
    <output name="out" type="BSDF" />
  </nodedef>

  <nodegraph name="IMPL_lama_dielectric" nodedef="ND_lama_dielectric">

    <!-- IOR -->
    <convert name="reflectivity_color" type="color3">
      <input name="in" type="float" interfacename="reflectivity" uivisible="false" />
    </convert>
    <artistic_ior name="artistic_ior" type="multioutput">
      <input name="reflectivity" type="color3" nodename="reflectivity_color" />
      <input name="edge_color" type="color3" value="0.0, 0.0, 0.0" />
    </artistic_ior>
    <extract name="ior_float" type="float">
      <input name="in" type="color3" nodename="artistic_ior" output="ior" />
      <input name="index" type="integer" value="0" />
    </extract>
    <switch name="fresnel_mode_switch" type="float">
      <input name="in1" type="float" nodename="ior_float" />
      <input name="in2" type="float" interfacename="IOR" />
      <input name="which" type="integer" interfacename="fresnelMode" />
    </switch>

    <!-- Roughness -->
    <subtract name="roughness_inverse" type="float">
      <input name="in1" type="float" value="1.0" />
      <input name="in2" type="float" interfacename="roughness" />
    </subtract>
    <ifgreatereq name="delta" type="float">
      <input name="in1" type="float" nodename="roughness_inverse" />
      <input name="in2" type="float" interfacename="roughness" />
      <input name="value1" type="float" interfacename="anisotropy" />
      <input name="value2" type="float" value="0" />
    </ifgreatereq>
    <multiply name="roughness_additional" type="float">
      <input name="in1" type="float" interfacename="anisotropy" />
      <input name="in2" type="float" nodename="delta" />
    </multiply>
    <add name="roughness_bitangent" type="float">
      <input name="in1" type="float" interfacename="roughness" />
      <input name="in2" type="float" nodename="roughness_additional" />
    </add>
    <clamp name="roughness_bitangent_clamped" type="float">
      <input name="in" type="float" nodename="roughness_bitangent" />
    </clamp>
    <combine2 name="roughness_linear" type="vector2">
      <input name="in1" type="float" interfacename="roughness" />
      <input name="in2" type="float" nodename="roughness_bitangent_clamped" />
    </combine2>
    <power name="roughness_anisotropic_squared" type="vector2">
      <input name="in1" type="vector2" nodename="roughness_linear" />
      <input name="in2" type="float" value="2" />
    </power>
    <max name="roughness_anisotropic_squared_clamped" type="vector2">
      <input name="in1" type="vector2" nodename="roughness_anisotropic_squared" />
      <input name="in2" type="float" value="0.000001" />
    </max>

    <!-- Tangent -->
    <multiply name="tangent_rotate_degree" type="float">
      <input name="in1" type="float" interfacename="rotation" />
      <input name="in2" type="float" value="-360" />
    </multiply>
    <subtract name="tangent_rotate_degree_offset" type="float">
      <input name="in1" type="float" nodename="tangent_rotate_degree" />
      <input name="in2" type="float" value="0" />
    </subtract>
    <rotate3d name="tangent_rotate" type="vector3">
      <input name="in" type="vector3" interfacename="direction" />
      <input name="amount" type="float" nodename="tangent_rotate_degree_offset" />
      <input name="axis" type="vector3" interfacename="normal" />
    </rotate3d>
    <normalize name="tangent_rotate_normalize" type="vector3">
      <input name="in" type="vector3" nodename="tangent_rotate" />
    </normalize>

    <!-- Interior -->
    <divide name="absorption" type="color3">
      <input name="in1" type="color3" interfacename="absorptionColor" />
      <input name="in2" type="float" interfacename="absorptionRadius" />
    </divide>
    <convert name="absorption_vector" type="vector3">
      <input name="in" type="color3" nodename="absorption" />
    </convert>
    <convert name="scatter_vector" type="vector3">
      <input name="in" type="color3" interfacename="scatterColor" />
    </convert>
    <anisotropic_vdf name="interior_vdf" type="VDF">
      <input name="absorption" type="vector3" nodename="absorption_vector" />
      <input name="scattering" type="vector3" nodename="scatter_vector" />
      <input name="anisotropy" type="float" interfacename="scatterAnisotropy" />
    </anisotropic_vdf>

    <!-- BTDF -->
    <dielectric_bsdf name="transmission_bsdf" type="BSDF">
      <input name="weight" type="float" value="1.0" />
      <input name="tint" type="color3" interfacename="transmissionTint" />
      <input name="ior" type="float" nodename="fresnel_mode_switch" />
      <input name="roughness" type="vector2" nodename="roughness_anisotropic_squared_clamped" />
      <input name="normal" type="vector3" interfacename="normal" />
      <input name="tangent" type="vector3" nodename="tangent_rotate_normalize" />
      <input name="distribution" type="string" value="ggx" />
      <input name="scatter_mode" type="string" value="T" />
    </dielectric_bsdf>
    <layer name="transmission_layer" type="BSDF">
      <input name="top" type="BSDF" nodename="transmission_bsdf" />
      <input name="base" type="VDF" nodename="interior_vdf" />
    </layer>

    <!-- BRDF -->
    <dielectric_bsdf name="reflection_bsdf" type="BSDF">
      <input name="weight" type="float" value="1.0" />
      <input name="tint" type="color3" interfacename="reflectionTint" />
      <input name="ior" type="float" nodename="fresnel_mode_switch" />
      <input name="roughness" type="vector2" nodename="roughness_anisotropic_squared_clamped" />
      <input name="normal" type="vector3" interfacename="normal" />
      <input name="tangent" type="vector3" nodename="tangent_rotate_normalize" />
      <input name="distribution" type="string" value="ggx" />
      <input name="scatter_mode" type="string" value="R" />
    </dielectric_bsdf>

    <!-- BSDF -->
    <layer name="dielectric_bsdf" type="BSDF">
      <input name="top" type="BSDF" nodename="reflection_bsdf" />
      <input name="base" type="BSDF" nodename="transmission_layer" />
    </layer>

    <!-- Output -->
    <output name="out" type="BSDF" nodename="dielectric_bsdf" />

  </nodegraph>
</materialx>
