/*===- TableGen'erated file -------------------------------------*- C++ -*-===*\
|*                                                                            *|
|* Intrinsic Function Source Fragment                                         *|
|*                                                                            *|
|* Automatically generated file, do not edit!                                 *|
|*                                                                            *|
\*===----------------------------------------------------------------------===*/

#ifndef LLVM_IR_INTRINSIC_AMDGCN_ENUMS_H
#define LLVM_IR_INTRINSIC_AMDGCN_ENUMS_H

namespace llvm {
namespace Intrinsic {
enum AMDGCNIntrinsics : unsigned {
// Enum values for intrinsics
    amdgcn_alignbyte = 1812,                          // llvm.amdgcn.alignbyte
    amdgcn_ballot,                             // llvm.amdgcn.ballot
    amdgcn_buffer_atomic_add,                  // llvm.amdgcn.buffer.atomic.add
    amdgcn_buffer_atomic_and,                  // llvm.amdgcn.buffer.atomic.and
    amdgcn_buffer_atomic_cmpswap,              // llvm.amdgcn.buffer.atomic.cmpswap
    amdgcn_buffer_atomic_csub,                 // llvm.amdgcn.buffer.atomic.csub
    amdgcn_buffer_atomic_fadd,                 // llvm.amdgcn.buffer.atomic.fadd
    amdgcn_buffer_atomic_or,                   // llvm.amdgcn.buffer.atomic.or
    amdgcn_buffer_atomic_smax,                 // llvm.amdgcn.buffer.atomic.smax
    amdgcn_buffer_atomic_smin,                 // llvm.amdgcn.buffer.atomic.smin
    amdgcn_buffer_atomic_sub,                  // llvm.amdgcn.buffer.atomic.sub
    amdgcn_buffer_atomic_swap,                 // llvm.amdgcn.buffer.atomic.swap
    amdgcn_buffer_atomic_umax,                 // llvm.amdgcn.buffer.atomic.umax
    amdgcn_buffer_atomic_umin,                 // llvm.amdgcn.buffer.atomic.umin
    amdgcn_buffer_atomic_xor,                  // llvm.amdgcn.buffer.atomic.xor
    amdgcn_buffer_load,                        // llvm.amdgcn.buffer.load
    amdgcn_buffer_load_format,                 // llvm.amdgcn.buffer.load.format
    amdgcn_buffer_store,                       // llvm.amdgcn.buffer.store
    amdgcn_buffer_store_format,                // llvm.amdgcn.buffer.store.format
    amdgcn_buffer_wbinvl1,                     // llvm.amdgcn.buffer.wbinvl1
    amdgcn_buffer_wbinvl1_sc,                  // llvm.amdgcn.buffer.wbinvl1.sc
    amdgcn_buffer_wbinvl1_vol,                 // llvm.amdgcn.buffer.wbinvl1.vol
    amdgcn_class,                              // llvm.amdgcn.class
    amdgcn_cos,                                // llvm.amdgcn.cos
    amdgcn_cs_chain,                           // llvm.amdgcn.cs.chain
    amdgcn_cubeid,                             // llvm.amdgcn.cubeid
    amdgcn_cubema,                             // llvm.amdgcn.cubema
    amdgcn_cubesc,                             // llvm.amdgcn.cubesc
    amdgcn_cubetc,                             // llvm.amdgcn.cubetc
    amdgcn_cvt_f32_bf8,                        // llvm.amdgcn.cvt.f32.bf8
    amdgcn_cvt_f32_fp8,                        // llvm.amdgcn.cvt.f32.fp8
    amdgcn_cvt_pk_bf8_f32,                     // llvm.amdgcn.cvt.pk.bf8.f32
    amdgcn_cvt_pk_f32_bf8,                     // llvm.amdgcn.cvt.pk.f32.bf8
    amdgcn_cvt_pk_f32_fp8,                     // llvm.amdgcn.cvt.pk.f32.fp8
    amdgcn_cvt_pk_fp8_f32,                     // llvm.amdgcn.cvt.pk.fp8.f32
    amdgcn_cvt_pk_i16,                         // llvm.amdgcn.cvt.pk.i16
    amdgcn_cvt_pk_u16,                         // llvm.amdgcn.cvt.pk.u16
    amdgcn_cvt_pk_u8_f32,                      // llvm.amdgcn.cvt.pk.u8.f32
    amdgcn_cvt_pknorm_i16,                     // llvm.amdgcn.cvt.pknorm.i16
    amdgcn_cvt_pknorm_u16,                     // llvm.amdgcn.cvt.pknorm.u16
    amdgcn_cvt_pkrtz,                          // llvm.amdgcn.cvt.pkrtz
    amdgcn_cvt_sr_bf8_f32,                     // llvm.amdgcn.cvt.sr.bf8.f32
    amdgcn_cvt_sr_fp8_f32,                     // llvm.amdgcn.cvt.sr.fp8.f32
    amdgcn_dispatch_id,                        // llvm.amdgcn.dispatch.id
    amdgcn_dispatch_ptr,                       // llvm.amdgcn.dispatch.ptr
    amdgcn_div_fixup,                          // llvm.amdgcn.div.fixup
    amdgcn_div_fmas,                           // llvm.amdgcn.div.fmas
    amdgcn_div_scale,                          // llvm.amdgcn.div.scale
    amdgcn_ds_add_gs_reg_rtn,                  // llvm.amdgcn.ds.add.gs.reg.rtn
    amdgcn_ds_append,                          // llvm.amdgcn.ds.append
    amdgcn_ds_bpermute,                        // llvm.amdgcn.ds.bpermute
    amdgcn_ds_bvh_stack_rtn,                   // llvm.amdgcn.ds.bvh.stack.rtn
    amdgcn_ds_consume,                         // llvm.amdgcn.ds.consume
    amdgcn_ds_fadd,                            // llvm.amdgcn.ds.fadd
    amdgcn_ds_fadd_v2bf16,                     // llvm.amdgcn.ds.fadd.v2bf16
    amdgcn_ds_fmax,                            // llvm.amdgcn.ds.fmax
    amdgcn_ds_fmin,                            // llvm.amdgcn.ds.fmin
    amdgcn_ds_gws_barrier,                     // llvm.amdgcn.ds.gws.barrier
    amdgcn_ds_gws_init,                        // llvm.amdgcn.ds.gws.init
    amdgcn_ds_gws_sema_br,                     // llvm.amdgcn.ds.gws.sema.br
    amdgcn_ds_gws_sema_p,                      // llvm.amdgcn.ds.gws.sema.p
    amdgcn_ds_gws_sema_release_all,            // llvm.amdgcn.ds.gws.sema.release.all
    amdgcn_ds_gws_sema_v,                      // llvm.amdgcn.ds.gws.sema.v
    amdgcn_ds_ordered_add,                     // llvm.amdgcn.ds.ordered.add
    amdgcn_ds_ordered_swap,                    // llvm.amdgcn.ds.ordered.swap
    amdgcn_ds_permute,                         // llvm.amdgcn.ds.permute
    amdgcn_ds_sub_gs_reg_rtn,                  // llvm.amdgcn.ds.sub.gs.reg.rtn
    amdgcn_ds_swizzle,                         // llvm.amdgcn.ds.swizzle
    amdgcn_else,                               // llvm.amdgcn.else
    amdgcn_end_cf,                             // llvm.amdgcn.end.cf
    amdgcn_endpgm,                             // llvm.amdgcn.endpgm
    amdgcn_exp,                                // llvm.amdgcn.exp
    amdgcn_exp_compr,                          // llvm.amdgcn.exp.compr
    amdgcn_exp_row,                            // llvm.amdgcn.exp.row
    amdgcn_exp2,                               // llvm.amdgcn.exp2
    amdgcn_fcmp,                               // llvm.amdgcn.fcmp
    amdgcn_fdiv_fast,                          // llvm.amdgcn.fdiv.fast
    amdgcn_fdot2,                              // llvm.amdgcn.fdot2
    amdgcn_fdot2_bf16_bf16,                    // llvm.amdgcn.fdot2.bf16.bf16
    amdgcn_fdot2_f16_f16,                      // llvm.amdgcn.fdot2.f16.f16
    amdgcn_fdot2_f32_bf16,                     // llvm.amdgcn.fdot2.f32.bf16
    amdgcn_flat_atomic_fadd,                   // llvm.amdgcn.flat.atomic.fadd
    amdgcn_flat_atomic_fadd_v2bf16,            // llvm.amdgcn.flat.atomic.fadd.v2bf16
    amdgcn_flat_atomic_fmax,                   // llvm.amdgcn.flat.atomic.fmax
    amdgcn_flat_atomic_fmin,                   // llvm.amdgcn.flat.atomic.fmin
    amdgcn_fma_legacy,                         // llvm.amdgcn.fma.legacy
    amdgcn_fmad_ftz,                           // llvm.amdgcn.fmad.ftz
    amdgcn_fmed3,                              // llvm.amdgcn.fmed3
    amdgcn_fmul_legacy,                        // llvm.amdgcn.fmul.legacy
    amdgcn_fract,                              // llvm.amdgcn.fract
    amdgcn_frexp_exp,                          // llvm.amdgcn.frexp.exp
    amdgcn_frexp_mant,                         // llvm.amdgcn.frexp.mant
    amdgcn_global_atomic_csub,                 // llvm.amdgcn.global.atomic.csub
    amdgcn_global_atomic_fadd,                 // llvm.amdgcn.global.atomic.fadd
    amdgcn_global_atomic_fadd_v2bf16,          // llvm.amdgcn.global.atomic.fadd.v2bf16
    amdgcn_global_atomic_fmax,                 // llvm.amdgcn.global.atomic.fmax
    amdgcn_global_atomic_fmin,                 // llvm.amdgcn.global.atomic.fmin
    amdgcn_global_load_lds,                    // llvm.amdgcn.global.load.lds
    amdgcn_groupstaticsize,                    // llvm.amdgcn.groupstaticsize
    amdgcn_icmp,                               // llvm.amdgcn.icmp
    amdgcn_if,                                 // llvm.amdgcn.if
    amdgcn_if_break,                           // llvm.amdgcn.if.break
    amdgcn_iglp_opt,                           // llvm.amdgcn.iglp.opt
    amdgcn_image_atomic_add_1d,                // llvm.amdgcn.image.atomic.add.1d
    amdgcn_image_atomic_add_1darray,           // llvm.amdgcn.image.atomic.add.1darray
    amdgcn_image_atomic_add_2d,                // llvm.amdgcn.image.atomic.add.2d
    amdgcn_image_atomic_add_2darray,           // llvm.amdgcn.image.atomic.add.2darray
    amdgcn_image_atomic_add_2darraymsaa,       // llvm.amdgcn.image.atomic.add.2darraymsaa
    amdgcn_image_atomic_add_2dmsaa,            // llvm.amdgcn.image.atomic.add.2dmsaa
    amdgcn_image_atomic_add_3d,                // llvm.amdgcn.image.atomic.add.3d
    amdgcn_image_atomic_add_cube,              // llvm.amdgcn.image.atomic.add.cube
    amdgcn_image_atomic_and_1d,                // llvm.amdgcn.image.atomic.and.1d
    amdgcn_image_atomic_and_1darray,           // llvm.amdgcn.image.atomic.and.1darray
    amdgcn_image_atomic_and_2d,                // llvm.amdgcn.image.atomic.and.2d
    amdgcn_image_atomic_and_2darray,           // llvm.amdgcn.image.atomic.and.2darray
    amdgcn_image_atomic_and_2darraymsaa,       // llvm.amdgcn.image.atomic.and.2darraymsaa
    amdgcn_image_atomic_and_2dmsaa,            // llvm.amdgcn.image.atomic.and.2dmsaa
    amdgcn_image_atomic_and_3d,                // llvm.amdgcn.image.atomic.and.3d
    amdgcn_image_atomic_and_cube,              // llvm.amdgcn.image.atomic.and.cube
    amdgcn_image_atomic_cmpswap_1d,            // llvm.amdgcn.image.atomic.cmpswap.1d
    amdgcn_image_atomic_cmpswap_1darray,       // llvm.amdgcn.image.atomic.cmpswap.1darray
    amdgcn_image_atomic_cmpswap_2d,            // llvm.amdgcn.image.atomic.cmpswap.2d
    amdgcn_image_atomic_cmpswap_2darray,       // llvm.amdgcn.image.atomic.cmpswap.2darray
    amdgcn_image_atomic_cmpswap_2darraymsaa,   // llvm.amdgcn.image.atomic.cmpswap.2darraymsaa
    amdgcn_image_atomic_cmpswap_2dmsaa,        // llvm.amdgcn.image.atomic.cmpswap.2dmsaa
    amdgcn_image_atomic_cmpswap_3d,            // llvm.amdgcn.image.atomic.cmpswap.3d
    amdgcn_image_atomic_cmpswap_cube,          // llvm.amdgcn.image.atomic.cmpswap.cube
    amdgcn_image_atomic_dec_1d,                // llvm.amdgcn.image.atomic.dec.1d
    amdgcn_image_atomic_dec_1darray,           // llvm.amdgcn.image.atomic.dec.1darray
    amdgcn_image_atomic_dec_2d,                // llvm.amdgcn.image.atomic.dec.2d
    amdgcn_image_atomic_dec_2darray,           // llvm.amdgcn.image.atomic.dec.2darray
    amdgcn_image_atomic_dec_2darraymsaa,       // llvm.amdgcn.image.atomic.dec.2darraymsaa
    amdgcn_image_atomic_dec_2dmsaa,            // llvm.amdgcn.image.atomic.dec.2dmsaa
    amdgcn_image_atomic_dec_3d,                // llvm.amdgcn.image.atomic.dec.3d
    amdgcn_image_atomic_dec_cube,              // llvm.amdgcn.image.atomic.dec.cube
    amdgcn_image_atomic_fmax_1d,               // llvm.amdgcn.image.atomic.fmax.1d
    amdgcn_image_atomic_fmax_1darray,          // llvm.amdgcn.image.atomic.fmax.1darray
    amdgcn_image_atomic_fmax_2d,               // llvm.amdgcn.image.atomic.fmax.2d
    amdgcn_image_atomic_fmax_2darray,          // llvm.amdgcn.image.atomic.fmax.2darray
    amdgcn_image_atomic_fmax_2darraymsaa,      // llvm.amdgcn.image.atomic.fmax.2darraymsaa
    amdgcn_image_atomic_fmax_2dmsaa,           // llvm.amdgcn.image.atomic.fmax.2dmsaa
    amdgcn_image_atomic_fmax_3d,               // llvm.amdgcn.image.atomic.fmax.3d
    amdgcn_image_atomic_fmax_cube,             // llvm.amdgcn.image.atomic.fmax.cube
    amdgcn_image_atomic_fmin_1d,               // llvm.amdgcn.image.atomic.fmin.1d
    amdgcn_image_atomic_fmin_1darray,          // llvm.amdgcn.image.atomic.fmin.1darray
    amdgcn_image_atomic_fmin_2d,               // llvm.amdgcn.image.atomic.fmin.2d
    amdgcn_image_atomic_fmin_2darray,          // llvm.amdgcn.image.atomic.fmin.2darray
    amdgcn_image_atomic_fmin_2darraymsaa,      // llvm.amdgcn.image.atomic.fmin.2darraymsaa
    amdgcn_image_atomic_fmin_2dmsaa,           // llvm.amdgcn.image.atomic.fmin.2dmsaa
    amdgcn_image_atomic_fmin_3d,               // llvm.amdgcn.image.atomic.fmin.3d
    amdgcn_image_atomic_fmin_cube,             // llvm.amdgcn.image.atomic.fmin.cube
    amdgcn_image_atomic_inc_1d,                // llvm.amdgcn.image.atomic.inc.1d
    amdgcn_image_atomic_inc_1darray,           // llvm.amdgcn.image.atomic.inc.1darray
    amdgcn_image_atomic_inc_2d,                // llvm.amdgcn.image.atomic.inc.2d
    amdgcn_image_atomic_inc_2darray,           // llvm.amdgcn.image.atomic.inc.2darray
    amdgcn_image_atomic_inc_2darraymsaa,       // llvm.amdgcn.image.atomic.inc.2darraymsaa
    amdgcn_image_atomic_inc_2dmsaa,            // llvm.amdgcn.image.atomic.inc.2dmsaa
    amdgcn_image_atomic_inc_3d,                // llvm.amdgcn.image.atomic.inc.3d
    amdgcn_image_atomic_inc_cube,              // llvm.amdgcn.image.atomic.inc.cube
    amdgcn_image_atomic_or_1d,                 // llvm.amdgcn.image.atomic.or.1d
    amdgcn_image_atomic_or_1darray,            // llvm.amdgcn.image.atomic.or.1darray
    amdgcn_image_atomic_or_2d,                 // llvm.amdgcn.image.atomic.or.2d
    amdgcn_image_atomic_or_2darray,            // llvm.amdgcn.image.atomic.or.2darray
    amdgcn_image_atomic_or_2darraymsaa,        // llvm.amdgcn.image.atomic.or.2darraymsaa
    amdgcn_image_atomic_or_2dmsaa,             // llvm.amdgcn.image.atomic.or.2dmsaa
    amdgcn_image_atomic_or_3d,                 // llvm.amdgcn.image.atomic.or.3d
    amdgcn_image_atomic_or_cube,               // llvm.amdgcn.image.atomic.or.cube
    amdgcn_image_atomic_smax_1d,               // llvm.amdgcn.image.atomic.smax.1d
    amdgcn_image_atomic_smax_1darray,          // llvm.amdgcn.image.atomic.smax.1darray
    amdgcn_image_atomic_smax_2d,               // llvm.amdgcn.image.atomic.smax.2d
    amdgcn_image_atomic_smax_2darray,          // llvm.amdgcn.image.atomic.smax.2darray
    amdgcn_image_atomic_smax_2darraymsaa,      // llvm.amdgcn.image.atomic.smax.2darraymsaa
    amdgcn_image_atomic_smax_2dmsaa,           // llvm.amdgcn.image.atomic.smax.2dmsaa
    amdgcn_image_atomic_smax_3d,               // llvm.amdgcn.image.atomic.smax.3d
    amdgcn_image_atomic_smax_cube,             // llvm.amdgcn.image.atomic.smax.cube
    amdgcn_image_atomic_smin_1d,               // llvm.amdgcn.image.atomic.smin.1d
    amdgcn_image_atomic_smin_1darray,          // llvm.amdgcn.image.atomic.smin.1darray
    amdgcn_image_atomic_smin_2d,               // llvm.amdgcn.image.atomic.smin.2d
    amdgcn_image_atomic_smin_2darray,          // llvm.amdgcn.image.atomic.smin.2darray
    amdgcn_image_atomic_smin_2darraymsaa,      // llvm.amdgcn.image.atomic.smin.2darraymsaa
    amdgcn_image_atomic_smin_2dmsaa,           // llvm.amdgcn.image.atomic.smin.2dmsaa
    amdgcn_image_atomic_smin_3d,               // llvm.amdgcn.image.atomic.smin.3d
    amdgcn_image_atomic_smin_cube,             // llvm.amdgcn.image.atomic.smin.cube
    amdgcn_image_atomic_sub_1d,                // llvm.amdgcn.image.atomic.sub.1d
    amdgcn_image_atomic_sub_1darray,           // llvm.amdgcn.image.atomic.sub.1darray
    amdgcn_image_atomic_sub_2d,                // llvm.amdgcn.image.atomic.sub.2d
    amdgcn_image_atomic_sub_2darray,           // llvm.amdgcn.image.atomic.sub.2darray
    amdgcn_image_atomic_sub_2darraymsaa,       // llvm.amdgcn.image.atomic.sub.2darraymsaa
    amdgcn_image_atomic_sub_2dmsaa,            // llvm.amdgcn.image.atomic.sub.2dmsaa
    amdgcn_image_atomic_sub_3d,                // llvm.amdgcn.image.atomic.sub.3d
    amdgcn_image_atomic_sub_cube,              // llvm.amdgcn.image.atomic.sub.cube
    amdgcn_image_atomic_swap_1d,               // llvm.amdgcn.image.atomic.swap.1d
    amdgcn_image_atomic_swap_1darray,          // llvm.amdgcn.image.atomic.swap.1darray
    amdgcn_image_atomic_swap_2d,               // llvm.amdgcn.image.atomic.swap.2d
    amdgcn_image_atomic_swap_2darray,          // llvm.amdgcn.image.atomic.swap.2darray
    amdgcn_image_atomic_swap_2darraymsaa,      // llvm.amdgcn.image.atomic.swap.2darraymsaa
    amdgcn_image_atomic_swap_2dmsaa,           // llvm.amdgcn.image.atomic.swap.2dmsaa
    amdgcn_image_atomic_swap_3d,               // llvm.amdgcn.image.atomic.swap.3d
    amdgcn_image_atomic_swap_cube,             // llvm.amdgcn.image.atomic.swap.cube
    amdgcn_image_atomic_umax_1d,               // llvm.amdgcn.image.atomic.umax.1d
    amdgcn_image_atomic_umax_1darray,          // llvm.amdgcn.image.atomic.umax.1darray
    amdgcn_image_atomic_umax_2d,               // llvm.amdgcn.image.atomic.umax.2d
    amdgcn_image_atomic_umax_2darray,          // llvm.amdgcn.image.atomic.umax.2darray
    amdgcn_image_atomic_umax_2darraymsaa,      // llvm.amdgcn.image.atomic.umax.2darraymsaa
    amdgcn_image_atomic_umax_2dmsaa,           // llvm.amdgcn.image.atomic.umax.2dmsaa
    amdgcn_image_atomic_umax_3d,               // llvm.amdgcn.image.atomic.umax.3d
    amdgcn_image_atomic_umax_cube,             // llvm.amdgcn.image.atomic.umax.cube
    amdgcn_image_atomic_umin_1d,               // llvm.amdgcn.image.atomic.umin.1d
    amdgcn_image_atomic_umin_1darray,          // llvm.amdgcn.image.atomic.umin.1darray
    amdgcn_image_atomic_umin_2d,               // llvm.amdgcn.image.atomic.umin.2d
    amdgcn_image_atomic_umin_2darray,          // llvm.amdgcn.image.atomic.umin.2darray
    amdgcn_image_atomic_umin_2darraymsaa,      // llvm.amdgcn.image.atomic.umin.2darraymsaa
    amdgcn_image_atomic_umin_2dmsaa,           // llvm.amdgcn.image.atomic.umin.2dmsaa
    amdgcn_image_atomic_umin_3d,               // llvm.amdgcn.image.atomic.umin.3d
    amdgcn_image_atomic_umin_cube,             // llvm.amdgcn.image.atomic.umin.cube
    amdgcn_image_atomic_xor_1d,                // llvm.amdgcn.image.atomic.xor.1d
    amdgcn_image_atomic_xor_1darray,           // llvm.amdgcn.image.atomic.xor.1darray
    amdgcn_image_atomic_xor_2d,                // llvm.amdgcn.image.atomic.xor.2d
    amdgcn_image_atomic_xor_2darray,           // llvm.amdgcn.image.atomic.xor.2darray
    amdgcn_image_atomic_xor_2darraymsaa,       // llvm.amdgcn.image.atomic.xor.2darraymsaa
    amdgcn_image_atomic_xor_2dmsaa,            // llvm.amdgcn.image.atomic.xor.2dmsaa
    amdgcn_image_atomic_xor_3d,                // llvm.amdgcn.image.atomic.xor.3d
    amdgcn_image_atomic_xor_cube,              // llvm.amdgcn.image.atomic.xor.cube
    amdgcn_image_bvh_intersect_ray,            // llvm.amdgcn.image.bvh.intersect.ray
    amdgcn_image_gather4_2d,                   // llvm.amdgcn.image.gather4.2d
    amdgcn_image_gather4_2darray,              // llvm.amdgcn.image.gather4.2darray
    amdgcn_image_gather4_b_2d,                 // llvm.amdgcn.image.gather4.b.2d
    amdgcn_image_gather4_b_2darray,            // llvm.amdgcn.image.gather4.b.2darray
    amdgcn_image_gather4_b_cl_2d,              // llvm.amdgcn.image.gather4.b.cl.2d
    amdgcn_image_gather4_b_cl_2darray,         // llvm.amdgcn.image.gather4.b.cl.2darray
    amdgcn_image_gather4_b_cl_cube,            // llvm.amdgcn.image.gather4.b.cl.cube
    amdgcn_image_gather4_b_cl_o_2d,            // llvm.amdgcn.image.gather4.b.cl.o.2d
    amdgcn_image_gather4_b_cl_o_2darray,       // llvm.amdgcn.image.gather4.b.cl.o.2darray
    amdgcn_image_gather4_b_cl_o_cube,          // llvm.amdgcn.image.gather4.b.cl.o.cube
    amdgcn_image_gather4_b_cube,               // llvm.amdgcn.image.gather4.b.cube
    amdgcn_image_gather4_b_o_2d,               // llvm.amdgcn.image.gather4.b.o.2d
    amdgcn_image_gather4_b_o_2darray,          // llvm.amdgcn.image.gather4.b.o.2darray
    amdgcn_image_gather4_b_o_cube,             // llvm.amdgcn.image.gather4.b.o.cube
    amdgcn_image_gather4_c_2d,                 // llvm.amdgcn.image.gather4.c.2d
    amdgcn_image_gather4_c_2darray,            // llvm.amdgcn.image.gather4.c.2darray
    amdgcn_image_gather4_c_b_2d,               // llvm.amdgcn.image.gather4.c.b.2d
    amdgcn_image_gather4_c_b_2darray,          // llvm.amdgcn.image.gather4.c.b.2darray
    amdgcn_image_gather4_c_b_cl_2d,            // llvm.amdgcn.image.gather4.c.b.cl.2d
    amdgcn_image_gather4_c_b_cl_2darray,       // llvm.amdgcn.image.gather4.c.b.cl.2darray
    amdgcn_image_gather4_c_b_cl_cube,          // llvm.amdgcn.image.gather4.c.b.cl.cube
    amdgcn_image_gather4_c_b_cl_o_2d,          // llvm.amdgcn.image.gather4.c.b.cl.o.2d
    amdgcn_image_gather4_c_b_cl_o_2darray,     // llvm.amdgcn.image.gather4.c.b.cl.o.2darray
    amdgcn_image_gather4_c_b_cl_o_cube,        // llvm.amdgcn.image.gather4.c.b.cl.o.cube
    amdgcn_image_gather4_c_b_cube,             // llvm.amdgcn.image.gather4.c.b.cube
    amdgcn_image_gather4_c_b_o_2d,             // llvm.amdgcn.image.gather4.c.b.o.2d
    amdgcn_image_gather4_c_b_o_2darray,        // llvm.amdgcn.image.gather4.c.b.o.2darray
    amdgcn_image_gather4_c_b_o_cube,           // llvm.amdgcn.image.gather4.c.b.o.cube
    amdgcn_image_gather4_c_cl_2d,              // llvm.amdgcn.image.gather4.c.cl.2d
    amdgcn_image_gather4_c_cl_2darray,         // llvm.amdgcn.image.gather4.c.cl.2darray
    amdgcn_image_gather4_c_cl_cube,            // llvm.amdgcn.image.gather4.c.cl.cube
    amdgcn_image_gather4_c_cl_o_2d,            // llvm.amdgcn.image.gather4.c.cl.o.2d
    amdgcn_image_gather4_c_cl_o_2darray,       // llvm.amdgcn.image.gather4.c.cl.o.2darray
    amdgcn_image_gather4_c_cl_o_cube,          // llvm.amdgcn.image.gather4.c.cl.o.cube
    amdgcn_image_gather4_c_cube,               // llvm.amdgcn.image.gather4.c.cube
    amdgcn_image_gather4_c_l_2d,               // llvm.amdgcn.image.gather4.c.l.2d
    amdgcn_image_gather4_c_l_2darray,          // llvm.amdgcn.image.gather4.c.l.2darray
    amdgcn_image_gather4_c_l_cube,             // llvm.amdgcn.image.gather4.c.l.cube
    amdgcn_image_gather4_c_l_o_2d,             // llvm.amdgcn.image.gather4.c.l.o.2d
    amdgcn_image_gather4_c_l_o_2darray,        // llvm.amdgcn.image.gather4.c.l.o.2darray
    amdgcn_image_gather4_c_l_o_cube,           // llvm.amdgcn.image.gather4.c.l.o.cube
    amdgcn_image_gather4_c_lz_2d,              // llvm.amdgcn.image.gather4.c.lz.2d
    amdgcn_image_gather4_c_lz_2darray,         // llvm.amdgcn.image.gather4.c.lz.2darray
    amdgcn_image_gather4_c_lz_cube,            // llvm.amdgcn.image.gather4.c.lz.cube
    amdgcn_image_gather4_c_lz_o_2d,            // llvm.amdgcn.image.gather4.c.lz.o.2d
    amdgcn_image_gather4_c_lz_o_2darray,       // llvm.amdgcn.image.gather4.c.lz.o.2darray
    amdgcn_image_gather4_c_lz_o_cube,          // llvm.amdgcn.image.gather4.c.lz.o.cube
    amdgcn_image_gather4_c_o_2d,               // llvm.amdgcn.image.gather4.c.o.2d
    amdgcn_image_gather4_c_o_2darray,          // llvm.amdgcn.image.gather4.c.o.2darray
    amdgcn_image_gather4_c_o_cube,             // llvm.amdgcn.image.gather4.c.o.cube
    amdgcn_image_gather4_cl_2d,                // llvm.amdgcn.image.gather4.cl.2d
    amdgcn_image_gather4_cl_2darray,           // llvm.amdgcn.image.gather4.cl.2darray
    amdgcn_image_gather4_cl_cube,              // llvm.amdgcn.image.gather4.cl.cube
    amdgcn_image_gather4_cl_o_2d,              // llvm.amdgcn.image.gather4.cl.o.2d
    amdgcn_image_gather4_cl_o_2darray,         // llvm.amdgcn.image.gather4.cl.o.2darray
    amdgcn_image_gather4_cl_o_cube,            // llvm.amdgcn.image.gather4.cl.o.cube
    amdgcn_image_gather4_cube,                 // llvm.amdgcn.image.gather4.cube
    amdgcn_image_gather4_l_2d,                 // llvm.amdgcn.image.gather4.l.2d
    amdgcn_image_gather4_l_2darray,            // llvm.amdgcn.image.gather4.l.2darray
    amdgcn_image_gather4_l_cube,               // llvm.amdgcn.image.gather4.l.cube
    amdgcn_image_gather4_l_o_2d,               // llvm.amdgcn.image.gather4.l.o.2d
    amdgcn_image_gather4_l_o_2darray,          // llvm.amdgcn.image.gather4.l.o.2darray
    amdgcn_image_gather4_l_o_cube,             // llvm.amdgcn.image.gather4.l.o.cube
    amdgcn_image_gather4_lz_2d,                // llvm.amdgcn.image.gather4.lz.2d
    amdgcn_image_gather4_lz_2darray,           // llvm.amdgcn.image.gather4.lz.2darray
    amdgcn_image_gather4_lz_cube,              // llvm.amdgcn.image.gather4.lz.cube
    amdgcn_image_gather4_lz_o_2d,              // llvm.amdgcn.image.gather4.lz.o.2d
    amdgcn_image_gather4_lz_o_2darray,         // llvm.amdgcn.image.gather4.lz.o.2darray
    amdgcn_image_gather4_lz_o_cube,            // llvm.amdgcn.image.gather4.lz.o.cube
    amdgcn_image_gather4_o_2d,                 // llvm.amdgcn.image.gather4.o.2d
    amdgcn_image_gather4_o_2darray,            // llvm.amdgcn.image.gather4.o.2darray
    amdgcn_image_gather4_o_cube,               // llvm.amdgcn.image.gather4.o.cube
    amdgcn_image_getlod_1d,                    // llvm.amdgcn.image.getlod.1d
    amdgcn_image_getlod_1darray,               // llvm.amdgcn.image.getlod.1darray
    amdgcn_image_getlod_2d,                    // llvm.amdgcn.image.getlod.2d
    amdgcn_image_getlod_2darray,               // llvm.amdgcn.image.getlod.2darray
    amdgcn_image_getlod_3d,                    // llvm.amdgcn.image.getlod.3d
    amdgcn_image_getlod_cube,                  // llvm.amdgcn.image.getlod.cube
    amdgcn_image_getresinfo_1d,                // llvm.amdgcn.image.getresinfo.1d
    amdgcn_image_getresinfo_1darray,           // llvm.amdgcn.image.getresinfo.1darray
    amdgcn_image_getresinfo_2d,                // llvm.amdgcn.image.getresinfo.2d
    amdgcn_image_getresinfo_2darray,           // llvm.amdgcn.image.getresinfo.2darray
    amdgcn_image_getresinfo_2darraymsaa,       // llvm.amdgcn.image.getresinfo.2darraymsaa
    amdgcn_image_getresinfo_2dmsaa,            // llvm.amdgcn.image.getresinfo.2dmsaa
    amdgcn_image_getresinfo_3d,                // llvm.amdgcn.image.getresinfo.3d
    amdgcn_image_getresinfo_cube,              // llvm.amdgcn.image.getresinfo.cube
    amdgcn_image_load_1d,                      // llvm.amdgcn.image.load.1d
    amdgcn_image_load_1darray,                 // llvm.amdgcn.image.load.1darray
    amdgcn_image_load_2d,                      // llvm.amdgcn.image.load.2d
    amdgcn_image_load_2darray,                 // llvm.amdgcn.image.load.2darray
    amdgcn_image_load_2darraymsaa,             // llvm.amdgcn.image.load.2darraymsaa
    amdgcn_image_load_2dmsaa,                  // llvm.amdgcn.image.load.2dmsaa
    amdgcn_image_load_3d,                      // llvm.amdgcn.image.load.3d
    amdgcn_image_load_cube,                    // llvm.amdgcn.image.load.cube
    amdgcn_image_load_mip_1d,                  // llvm.amdgcn.image.load.mip.1d
    amdgcn_image_load_mip_1darray,             // llvm.amdgcn.image.load.mip.1darray
    amdgcn_image_load_mip_2d,                  // llvm.amdgcn.image.load.mip.2d
    amdgcn_image_load_mip_2darray,             // llvm.amdgcn.image.load.mip.2darray
    amdgcn_image_load_mip_3d,                  // llvm.amdgcn.image.load.mip.3d
    amdgcn_image_load_mip_cube,                // llvm.amdgcn.image.load.mip.cube
    amdgcn_image_msaa_load_2darraymsaa,        // llvm.amdgcn.image.msaa.load.2darraymsaa
    amdgcn_image_msaa_load_2dmsaa,             // llvm.amdgcn.image.msaa.load.2dmsaa
    amdgcn_image_msaa_load_x_2darraymsaa,      // llvm.amdgcn.image.msaa.load.x.2darraymsaa
    amdgcn_image_msaa_load_x_2dmsaa,           // llvm.amdgcn.image.msaa.load.x.2dmsaa
    amdgcn_image_sample_1d,                    // llvm.amdgcn.image.sample.1d
    amdgcn_image_sample_1darray,               // llvm.amdgcn.image.sample.1darray
    amdgcn_image_sample_2d,                    // llvm.amdgcn.image.sample.2d
    amdgcn_image_sample_2darray,               // llvm.amdgcn.image.sample.2darray
    amdgcn_image_sample_3d,                    // llvm.amdgcn.image.sample.3d
    amdgcn_image_sample_b_1d,                  // llvm.amdgcn.image.sample.b.1d
    amdgcn_image_sample_b_1darray,             // llvm.amdgcn.image.sample.b.1darray
    amdgcn_image_sample_b_2d,                  // llvm.amdgcn.image.sample.b.2d
    amdgcn_image_sample_b_2darray,             // llvm.amdgcn.image.sample.b.2darray
    amdgcn_image_sample_b_3d,                  // llvm.amdgcn.image.sample.b.3d
    amdgcn_image_sample_b_cl_1d,               // llvm.amdgcn.image.sample.b.cl.1d
    amdgcn_image_sample_b_cl_1darray,          // llvm.amdgcn.image.sample.b.cl.1darray
    amdgcn_image_sample_b_cl_2d,               // llvm.amdgcn.image.sample.b.cl.2d
    amdgcn_image_sample_b_cl_2darray,          // llvm.amdgcn.image.sample.b.cl.2darray
    amdgcn_image_sample_b_cl_3d,               // llvm.amdgcn.image.sample.b.cl.3d
    amdgcn_image_sample_b_cl_cube,             // llvm.amdgcn.image.sample.b.cl.cube
    amdgcn_image_sample_b_cl_o_1d,             // llvm.amdgcn.image.sample.b.cl.o.1d
    amdgcn_image_sample_b_cl_o_1darray,        // llvm.amdgcn.image.sample.b.cl.o.1darray
    amdgcn_image_sample_b_cl_o_2d,             // llvm.amdgcn.image.sample.b.cl.o.2d
    amdgcn_image_sample_b_cl_o_2darray,        // llvm.amdgcn.image.sample.b.cl.o.2darray
    amdgcn_image_sample_b_cl_o_3d,             // llvm.amdgcn.image.sample.b.cl.o.3d
    amdgcn_image_sample_b_cl_o_cube,           // llvm.amdgcn.image.sample.b.cl.o.cube
    amdgcn_image_sample_b_cube,                // llvm.amdgcn.image.sample.b.cube
    amdgcn_image_sample_b_o_1d,                // llvm.amdgcn.image.sample.b.o.1d
    amdgcn_image_sample_b_o_1darray,           // llvm.amdgcn.image.sample.b.o.1darray
    amdgcn_image_sample_b_o_2d,                // llvm.amdgcn.image.sample.b.o.2d
    amdgcn_image_sample_b_o_2darray,           // llvm.amdgcn.image.sample.b.o.2darray
    amdgcn_image_sample_b_o_3d,                // llvm.amdgcn.image.sample.b.o.3d
    amdgcn_image_sample_b_o_cube,              // llvm.amdgcn.image.sample.b.o.cube
    amdgcn_image_sample_c_1d,                  // llvm.amdgcn.image.sample.c.1d
    amdgcn_image_sample_c_1darray,             // llvm.amdgcn.image.sample.c.1darray
    amdgcn_image_sample_c_2d,                  // llvm.amdgcn.image.sample.c.2d
    amdgcn_image_sample_c_2darray,             // llvm.amdgcn.image.sample.c.2darray
    amdgcn_image_sample_c_3d,                  // llvm.amdgcn.image.sample.c.3d
    amdgcn_image_sample_c_b_1d,                // llvm.amdgcn.image.sample.c.b.1d
    amdgcn_image_sample_c_b_1darray,           // llvm.amdgcn.image.sample.c.b.1darray
    amdgcn_image_sample_c_b_2d,                // llvm.amdgcn.image.sample.c.b.2d
    amdgcn_image_sample_c_b_2darray,           // llvm.amdgcn.image.sample.c.b.2darray
    amdgcn_image_sample_c_b_3d,                // llvm.amdgcn.image.sample.c.b.3d
    amdgcn_image_sample_c_b_cl_1d,             // llvm.amdgcn.image.sample.c.b.cl.1d
    amdgcn_image_sample_c_b_cl_1darray,        // llvm.amdgcn.image.sample.c.b.cl.1darray
    amdgcn_image_sample_c_b_cl_2d,             // llvm.amdgcn.image.sample.c.b.cl.2d
    amdgcn_image_sample_c_b_cl_2darray,        // llvm.amdgcn.image.sample.c.b.cl.2darray
    amdgcn_image_sample_c_b_cl_3d,             // llvm.amdgcn.image.sample.c.b.cl.3d
    amdgcn_image_sample_c_b_cl_cube,           // llvm.amdgcn.image.sample.c.b.cl.cube
    amdgcn_image_sample_c_b_cl_o_1d,           // llvm.amdgcn.image.sample.c.b.cl.o.1d
    amdgcn_image_sample_c_b_cl_o_1darray,      // llvm.amdgcn.image.sample.c.b.cl.o.1darray
    amdgcn_image_sample_c_b_cl_o_2d,           // llvm.amdgcn.image.sample.c.b.cl.o.2d
    amdgcn_image_sample_c_b_cl_o_2darray,      // llvm.amdgcn.image.sample.c.b.cl.o.2darray
    amdgcn_image_sample_c_b_cl_o_3d,           // llvm.amdgcn.image.sample.c.b.cl.o.3d
    amdgcn_image_sample_c_b_cl_o_cube,         // llvm.amdgcn.image.sample.c.b.cl.o.cube
    amdgcn_image_sample_c_b_cube,              // llvm.amdgcn.image.sample.c.b.cube
    amdgcn_image_sample_c_b_o_1d,              // llvm.amdgcn.image.sample.c.b.o.1d
    amdgcn_image_sample_c_b_o_1darray,         // llvm.amdgcn.image.sample.c.b.o.1darray
    amdgcn_image_sample_c_b_o_2d,              // llvm.amdgcn.image.sample.c.b.o.2d
    amdgcn_image_sample_c_b_o_2darray,         // llvm.amdgcn.image.sample.c.b.o.2darray
    amdgcn_image_sample_c_b_o_3d,              // llvm.amdgcn.image.sample.c.b.o.3d
    amdgcn_image_sample_c_b_o_cube,            // llvm.amdgcn.image.sample.c.b.o.cube
    amdgcn_image_sample_c_cd_1d,               // llvm.amdgcn.image.sample.c.cd.1d
    amdgcn_image_sample_c_cd_1darray,          // llvm.amdgcn.image.sample.c.cd.1darray
    amdgcn_image_sample_c_cd_2d,               // llvm.amdgcn.image.sample.c.cd.2d
    amdgcn_image_sample_c_cd_2darray,          // llvm.amdgcn.image.sample.c.cd.2darray
    amdgcn_image_sample_c_cd_3d,               // llvm.amdgcn.image.sample.c.cd.3d
    amdgcn_image_sample_c_cd_cl_1d,            // llvm.amdgcn.image.sample.c.cd.cl.1d
    amdgcn_image_sample_c_cd_cl_1darray,       // llvm.amdgcn.image.sample.c.cd.cl.1darray
    amdgcn_image_sample_c_cd_cl_2d,            // llvm.amdgcn.image.sample.c.cd.cl.2d
    amdgcn_image_sample_c_cd_cl_2darray,       // llvm.amdgcn.image.sample.c.cd.cl.2darray
    amdgcn_image_sample_c_cd_cl_3d,            // llvm.amdgcn.image.sample.c.cd.cl.3d
    amdgcn_image_sample_c_cd_cl_cube,          // llvm.amdgcn.image.sample.c.cd.cl.cube
    amdgcn_image_sample_c_cd_cl_o_1d,          // llvm.amdgcn.image.sample.c.cd.cl.o.1d
    amdgcn_image_sample_c_cd_cl_o_1darray,     // llvm.amdgcn.image.sample.c.cd.cl.o.1darray
    amdgcn_image_sample_c_cd_cl_o_2d,          // llvm.amdgcn.image.sample.c.cd.cl.o.2d
    amdgcn_image_sample_c_cd_cl_o_2darray,     // llvm.amdgcn.image.sample.c.cd.cl.o.2darray
    amdgcn_image_sample_c_cd_cl_o_3d,          // llvm.amdgcn.image.sample.c.cd.cl.o.3d
    amdgcn_image_sample_c_cd_cl_o_cube,        // llvm.amdgcn.image.sample.c.cd.cl.o.cube
    amdgcn_image_sample_c_cd_cube,             // llvm.amdgcn.image.sample.c.cd.cube
    amdgcn_image_sample_c_cd_o_1d,             // llvm.amdgcn.image.sample.c.cd.o.1d
    amdgcn_image_sample_c_cd_o_1darray,        // llvm.amdgcn.image.sample.c.cd.o.1darray
    amdgcn_image_sample_c_cd_o_2d,             // llvm.amdgcn.image.sample.c.cd.o.2d
    amdgcn_image_sample_c_cd_o_2darray,        // llvm.amdgcn.image.sample.c.cd.o.2darray
    amdgcn_image_sample_c_cd_o_3d,             // llvm.amdgcn.image.sample.c.cd.o.3d
    amdgcn_image_sample_c_cd_o_cube,           // llvm.amdgcn.image.sample.c.cd.o.cube
    amdgcn_image_sample_c_cl_1d,               // llvm.amdgcn.image.sample.c.cl.1d
    amdgcn_image_sample_c_cl_1darray,          // llvm.amdgcn.image.sample.c.cl.1darray
    amdgcn_image_sample_c_cl_2d,               // llvm.amdgcn.image.sample.c.cl.2d
    amdgcn_image_sample_c_cl_2darray,          // llvm.amdgcn.image.sample.c.cl.2darray
    amdgcn_image_sample_c_cl_3d,               // llvm.amdgcn.image.sample.c.cl.3d
    amdgcn_image_sample_c_cl_cube,             // llvm.amdgcn.image.sample.c.cl.cube
    amdgcn_image_sample_c_cl_o_1d,             // llvm.amdgcn.image.sample.c.cl.o.1d
    amdgcn_image_sample_c_cl_o_1darray,        // llvm.amdgcn.image.sample.c.cl.o.1darray
    amdgcn_image_sample_c_cl_o_2d,             // llvm.amdgcn.image.sample.c.cl.o.2d
    amdgcn_image_sample_c_cl_o_2darray,        // llvm.amdgcn.image.sample.c.cl.o.2darray
    amdgcn_image_sample_c_cl_o_3d,             // llvm.amdgcn.image.sample.c.cl.o.3d
    amdgcn_image_sample_c_cl_o_cube,           // llvm.amdgcn.image.sample.c.cl.o.cube
    amdgcn_image_sample_c_cube,                // llvm.amdgcn.image.sample.c.cube
    amdgcn_image_sample_c_d_1d,                // llvm.amdgcn.image.sample.c.d.1d
    amdgcn_image_sample_c_d_1darray,           // llvm.amdgcn.image.sample.c.d.1darray
    amdgcn_image_sample_c_d_2d,                // llvm.amdgcn.image.sample.c.d.2d
    amdgcn_image_sample_c_d_2darray,           // llvm.amdgcn.image.sample.c.d.2darray
    amdgcn_image_sample_c_d_3d,                // llvm.amdgcn.image.sample.c.d.3d
    amdgcn_image_sample_c_d_cl_1d,             // llvm.amdgcn.image.sample.c.d.cl.1d
    amdgcn_image_sample_c_d_cl_1darray,        // llvm.amdgcn.image.sample.c.d.cl.1darray
    amdgcn_image_sample_c_d_cl_2d,             // llvm.amdgcn.image.sample.c.d.cl.2d
    amdgcn_image_sample_c_d_cl_2darray,        // llvm.amdgcn.image.sample.c.d.cl.2darray
    amdgcn_image_sample_c_d_cl_3d,             // llvm.amdgcn.image.sample.c.d.cl.3d
    amdgcn_image_sample_c_d_cl_cube,           // llvm.amdgcn.image.sample.c.d.cl.cube
    amdgcn_image_sample_c_d_cl_o_1d,           // llvm.amdgcn.image.sample.c.d.cl.o.1d
    amdgcn_image_sample_c_d_cl_o_1darray,      // llvm.amdgcn.image.sample.c.d.cl.o.1darray
    amdgcn_image_sample_c_d_cl_o_2d,           // llvm.amdgcn.image.sample.c.d.cl.o.2d
    amdgcn_image_sample_c_d_cl_o_2darray,      // llvm.amdgcn.image.sample.c.d.cl.o.2darray
    amdgcn_image_sample_c_d_cl_o_3d,           // llvm.amdgcn.image.sample.c.d.cl.o.3d
    amdgcn_image_sample_c_d_cl_o_cube,         // llvm.amdgcn.image.sample.c.d.cl.o.cube
    amdgcn_image_sample_c_d_cube,              // llvm.amdgcn.image.sample.c.d.cube
    amdgcn_image_sample_c_d_o_1d,              // llvm.amdgcn.image.sample.c.d.o.1d
    amdgcn_image_sample_c_d_o_1darray,         // llvm.amdgcn.image.sample.c.d.o.1darray
    amdgcn_image_sample_c_d_o_2d,              // llvm.amdgcn.image.sample.c.d.o.2d
    amdgcn_image_sample_c_d_o_2darray,         // llvm.amdgcn.image.sample.c.d.o.2darray
    amdgcn_image_sample_c_d_o_3d,              // llvm.amdgcn.image.sample.c.d.o.3d
    amdgcn_image_sample_c_d_o_cube,            // llvm.amdgcn.image.sample.c.d.o.cube
    amdgcn_image_sample_c_l_1d,                // llvm.amdgcn.image.sample.c.l.1d
    amdgcn_image_sample_c_l_1darray,           // llvm.amdgcn.image.sample.c.l.1darray
    amdgcn_image_sample_c_l_2d,                // llvm.amdgcn.image.sample.c.l.2d
    amdgcn_image_sample_c_l_2darray,           // llvm.amdgcn.image.sample.c.l.2darray
    amdgcn_image_sample_c_l_3d,                // llvm.amdgcn.image.sample.c.l.3d
    amdgcn_image_sample_c_l_cube,              // llvm.amdgcn.image.sample.c.l.cube
    amdgcn_image_sample_c_l_o_1d,              // llvm.amdgcn.image.sample.c.l.o.1d
    amdgcn_image_sample_c_l_o_1darray,         // llvm.amdgcn.image.sample.c.l.o.1darray
    amdgcn_image_sample_c_l_o_2d,              // llvm.amdgcn.image.sample.c.l.o.2d
    amdgcn_image_sample_c_l_o_2darray,         // llvm.amdgcn.image.sample.c.l.o.2darray
    amdgcn_image_sample_c_l_o_3d,              // llvm.amdgcn.image.sample.c.l.o.3d
    amdgcn_image_sample_c_l_o_cube,            // llvm.amdgcn.image.sample.c.l.o.cube
    amdgcn_image_sample_c_lz_1d,               // llvm.amdgcn.image.sample.c.lz.1d
    amdgcn_image_sample_c_lz_1darray,          // llvm.amdgcn.image.sample.c.lz.1darray
    amdgcn_image_sample_c_lz_2d,               // llvm.amdgcn.image.sample.c.lz.2d
    amdgcn_image_sample_c_lz_2darray,          // llvm.amdgcn.image.sample.c.lz.2darray
    amdgcn_image_sample_c_lz_3d,               // llvm.amdgcn.image.sample.c.lz.3d
    amdgcn_image_sample_c_lz_cube,             // llvm.amdgcn.image.sample.c.lz.cube
    amdgcn_image_sample_c_lz_o_1d,             // llvm.amdgcn.image.sample.c.lz.o.1d
    amdgcn_image_sample_c_lz_o_1darray,        // llvm.amdgcn.image.sample.c.lz.o.1darray
    amdgcn_image_sample_c_lz_o_2d,             // llvm.amdgcn.image.sample.c.lz.o.2d
    amdgcn_image_sample_c_lz_o_2darray,        // llvm.amdgcn.image.sample.c.lz.o.2darray
    amdgcn_image_sample_c_lz_o_3d,             // llvm.amdgcn.image.sample.c.lz.o.3d
    amdgcn_image_sample_c_lz_o_cube,           // llvm.amdgcn.image.sample.c.lz.o.cube
    amdgcn_image_sample_c_o_1d,                // llvm.amdgcn.image.sample.c.o.1d
    amdgcn_image_sample_c_o_1darray,           // llvm.amdgcn.image.sample.c.o.1darray
    amdgcn_image_sample_c_o_2d,                // llvm.amdgcn.image.sample.c.o.2d
    amdgcn_image_sample_c_o_2darray,           // llvm.amdgcn.image.sample.c.o.2darray
    amdgcn_image_sample_c_o_3d,                // llvm.amdgcn.image.sample.c.o.3d
    amdgcn_image_sample_c_o_cube,              // llvm.amdgcn.image.sample.c.o.cube
    amdgcn_image_sample_cd_1d,                 // llvm.amdgcn.image.sample.cd.1d
    amdgcn_image_sample_cd_1darray,            // llvm.amdgcn.image.sample.cd.1darray
    amdgcn_image_sample_cd_2d,                 // llvm.amdgcn.image.sample.cd.2d
    amdgcn_image_sample_cd_2darray,            // llvm.amdgcn.image.sample.cd.2darray
    amdgcn_image_sample_cd_3d,                 // llvm.amdgcn.image.sample.cd.3d
    amdgcn_image_sample_cd_cl_1d,              // llvm.amdgcn.image.sample.cd.cl.1d
    amdgcn_image_sample_cd_cl_1darray,         // llvm.amdgcn.image.sample.cd.cl.1darray
    amdgcn_image_sample_cd_cl_2d,              // llvm.amdgcn.image.sample.cd.cl.2d
    amdgcn_image_sample_cd_cl_2darray,         // llvm.amdgcn.image.sample.cd.cl.2darray
    amdgcn_image_sample_cd_cl_3d,              // llvm.amdgcn.image.sample.cd.cl.3d
    amdgcn_image_sample_cd_cl_cube,            // llvm.amdgcn.image.sample.cd.cl.cube
    amdgcn_image_sample_cd_cl_o_1d,            // llvm.amdgcn.image.sample.cd.cl.o.1d
    amdgcn_image_sample_cd_cl_o_1darray,       // llvm.amdgcn.image.sample.cd.cl.o.1darray
    amdgcn_image_sample_cd_cl_o_2d,            // llvm.amdgcn.image.sample.cd.cl.o.2d
    amdgcn_image_sample_cd_cl_o_2darray,       // llvm.amdgcn.image.sample.cd.cl.o.2darray
    amdgcn_image_sample_cd_cl_o_3d,            // llvm.amdgcn.image.sample.cd.cl.o.3d
    amdgcn_image_sample_cd_cl_o_cube,          // llvm.amdgcn.image.sample.cd.cl.o.cube
    amdgcn_image_sample_cd_cube,               // llvm.amdgcn.image.sample.cd.cube
    amdgcn_image_sample_cd_o_1d,               // llvm.amdgcn.image.sample.cd.o.1d
    amdgcn_image_sample_cd_o_1darray,          // llvm.amdgcn.image.sample.cd.o.1darray
    amdgcn_image_sample_cd_o_2d,               // llvm.amdgcn.image.sample.cd.o.2d
    amdgcn_image_sample_cd_o_2darray,          // llvm.amdgcn.image.sample.cd.o.2darray
    amdgcn_image_sample_cd_o_3d,               // llvm.amdgcn.image.sample.cd.o.3d
    amdgcn_image_sample_cd_o_cube,             // llvm.amdgcn.image.sample.cd.o.cube
    amdgcn_image_sample_cl_1d,                 // llvm.amdgcn.image.sample.cl.1d
    amdgcn_image_sample_cl_1darray,            // llvm.amdgcn.image.sample.cl.1darray
    amdgcn_image_sample_cl_2d,                 // llvm.amdgcn.image.sample.cl.2d
    amdgcn_image_sample_cl_2darray,            // llvm.amdgcn.image.sample.cl.2darray
    amdgcn_image_sample_cl_3d,                 // llvm.amdgcn.image.sample.cl.3d
    amdgcn_image_sample_cl_cube,               // llvm.amdgcn.image.sample.cl.cube
    amdgcn_image_sample_cl_o_1d,               // llvm.amdgcn.image.sample.cl.o.1d
    amdgcn_image_sample_cl_o_1darray,          // llvm.amdgcn.image.sample.cl.o.1darray
    amdgcn_image_sample_cl_o_2d,               // llvm.amdgcn.image.sample.cl.o.2d
    amdgcn_image_sample_cl_o_2darray,          // llvm.amdgcn.image.sample.cl.o.2darray
    amdgcn_image_sample_cl_o_3d,               // llvm.amdgcn.image.sample.cl.o.3d
    amdgcn_image_sample_cl_o_cube,             // llvm.amdgcn.image.sample.cl.o.cube
    amdgcn_image_sample_cube,                  // llvm.amdgcn.image.sample.cube
    amdgcn_image_sample_d_1d,                  // llvm.amdgcn.image.sample.d.1d
    amdgcn_image_sample_d_1darray,             // llvm.amdgcn.image.sample.d.1darray
    amdgcn_image_sample_d_2d,                  // llvm.amdgcn.image.sample.d.2d
    amdgcn_image_sample_d_2darray,             // llvm.amdgcn.image.sample.d.2darray
    amdgcn_image_sample_d_3d,                  // llvm.amdgcn.image.sample.d.3d
    amdgcn_image_sample_d_cl_1d,               // llvm.amdgcn.image.sample.d.cl.1d
    amdgcn_image_sample_d_cl_1darray,          // llvm.amdgcn.image.sample.d.cl.1darray
    amdgcn_image_sample_d_cl_2d,               // llvm.amdgcn.image.sample.d.cl.2d
    amdgcn_image_sample_d_cl_2darray,          // llvm.amdgcn.image.sample.d.cl.2darray
    amdgcn_image_sample_d_cl_3d,               // llvm.amdgcn.image.sample.d.cl.3d
    amdgcn_image_sample_d_cl_cube,             // llvm.amdgcn.image.sample.d.cl.cube
    amdgcn_image_sample_d_cl_o_1d,             // llvm.amdgcn.image.sample.d.cl.o.1d
    amdgcn_image_sample_d_cl_o_1darray,        // llvm.amdgcn.image.sample.d.cl.o.1darray
    amdgcn_image_sample_d_cl_o_2d,             // llvm.amdgcn.image.sample.d.cl.o.2d
    amdgcn_image_sample_d_cl_o_2darray,        // llvm.amdgcn.image.sample.d.cl.o.2darray
    amdgcn_image_sample_d_cl_o_3d,             // llvm.amdgcn.image.sample.d.cl.o.3d
    amdgcn_image_sample_d_cl_o_cube,           // llvm.amdgcn.image.sample.d.cl.o.cube
    amdgcn_image_sample_d_cube,                // llvm.amdgcn.image.sample.d.cube
    amdgcn_image_sample_d_o_1d,                // llvm.amdgcn.image.sample.d.o.1d
    amdgcn_image_sample_d_o_1darray,           // llvm.amdgcn.image.sample.d.o.1darray
    amdgcn_image_sample_d_o_2d,                // llvm.amdgcn.image.sample.d.o.2d
    amdgcn_image_sample_d_o_2darray,           // llvm.amdgcn.image.sample.d.o.2darray
    amdgcn_image_sample_d_o_3d,                // llvm.amdgcn.image.sample.d.o.3d
    amdgcn_image_sample_d_o_cube,              // llvm.amdgcn.image.sample.d.o.cube
    amdgcn_image_sample_l_1d,                  // llvm.amdgcn.image.sample.l.1d
    amdgcn_image_sample_l_1darray,             // llvm.amdgcn.image.sample.l.1darray
    amdgcn_image_sample_l_2d,                  // llvm.amdgcn.image.sample.l.2d
    amdgcn_image_sample_l_2darray,             // llvm.amdgcn.image.sample.l.2darray
    amdgcn_image_sample_l_3d,                  // llvm.amdgcn.image.sample.l.3d
    amdgcn_image_sample_l_cube,                // llvm.amdgcn.image.sample.l.cube
    amdgcn_image_sample_l_o_1d,                // llvm.amdgcn.image.sample.l.o.1d
    amdgcn_image_sample_l_o_1darray,           // llvm.amdgcn.image.sample.l.o.1darray
    amdgcn_image_sample_l_o_2d,                // llvm.amdgcn.image.sample.l.o.2d
    amdgcn_image_sample_l_o_2darray,           // llvm.amdgcn.image.sample.l.o.2darray
    amdgcn_image_sample_l_o_3d,                // llvm.amdgcn.image.sample.l.o.3d
    amdgcn_image_sample_l_o_cube,              // llvm.amdgcn.image.sample.l.o.cube
    amdgcn_image_sample_lz_1d,                 // llvm.amdgcn.image.sample.lz.1d
    amdgcn_image_sample_lz_1darray,            // llvm.amdgcn.image.sample.lz.1darray
    amdgcn_image_sample_lz_2d,                 // llvm.amdgcn.image.sample.lz.2d
    amdgcn_image_sample_lz_2darray,            // llvm.amdgcn.image.sample.lz.2darray
    amdgcn_image_sample_lz_3d,                 // llvm.amdgcn.image.sample.lz.3d
    amdgcn_image_sample_lz_cube,               // llvm.amdgcn.image.sample.lz.cube
    amdgcn_image_sample_lz_o_1d,               // llvm.amdgcn.image.sample.lz.o.1d
    amdgcn_image_sample_lz_o_1darray,          // llvm.amdgcn.image.sample.lz.o.1darray
    amdgcn_image_sample_lz_o_2d,               // llvm.amdgcn.image.sample.lz.o.2d
    amdgcn_image_sample_lz_o_2darray,          // llvm.amdgcn.image.sample.lz.o.2darray
    amdgcn_image_sample_lz_o_3d,               // llvm.amdgcn.image.sample.lz.o.3d
    amdgcn_image_sample_lz_o_cube,             // llvm.amdgcn.image.sample.lz.o.cube
    amdgcn_image_sample_o_1d,                  // llvm.amdgcn.image.sample.o.1d
    amdgcn_image_sample_o_1darray,             // llvm.amdgcn.image.sample.o.1darray
    amdgcn_image_sample_o_2d,                  // llvm.amdgcn.image.sample.o.2d
    amdgcn_image_sample_o_2darray,             // llvm.amdgcn.image.sample.o.2darray
    amdgcn_image_sample_o_3d,                  // llvm.amdgcn.image.sample.o.3d
    amdgcn_image_sample_o_cube,                // llvm.amdgcn.image.sample.o.cube
    amdgcn_image_store_1d,                     // llvm.amdgcn.image.store.1d
    amdgcn_image_store_1darray,                // llvm.amdgcn.image.store.1darray
    amdgcn_image_store_2d,                     // llvm.amdgcn.image.store.2d
    amdgcn_image_store_2darray,                // llvm.amdgcn.image.store.2darray
    amdgcn_image_store_2darraymsaa,            // llvm.amdgcn.image.store.2darraymsaa
    amdgcn_image_store_2dmsaa,                 // llvm.amdgcn.image.store.2dmsaa
    amdgcn_image_store_3d,                     // llvm.amdgcn.image.store.3d
    amdgcn_image_store_cube,                   // llvm.amdgcn.image.store.cube
    amdgcn_image_store_mip_1d,                 // llvm.amdgcn.image.store.mip.1d
    amdgcn_image_store_mip_1darray,            // llvm.amdgcn.image.store.mip.1darray
    amdgcn_image_store_mip_2d,                 // llvm.amdgcn.image.store.mip.2d
    amdgcn_image_store_mip_2darray,            // llvm.amdgcn.image.store.mip.2darray
    amdgcn_image_store_mip_3d,                 // llvm.amdgcn.image.store.mip.3d
    amdgcn_image_store_mip_cube,               // llvm.amdgcn.image.store.mip.cube
    amdgcn_implicit_buffer_ptr,                // llvm.amdgcn.implicit.buffer.ptr
    amdgcn_implicitarg_ptr,                    // llvm.amdgcn.implicitarg.ptr
    amdgcn_init_exec,                          // llvm.amdgcn.init.exec
    amdgcn_init_exec_from_input,               // llvm.amdgcn.init.exec.from.input
    amdgcn_interp_inreg_p10,                   // llvm.amdgcn.interp.inreg.p10
    amdgcn_interp_inreg_p10_f16,               // llvm.amdgcn.interp.inreg.p10.f16
    amdgcn_interp_inreg_p2,                    // llvm.amdgcn.interp.inreg.p2
    amdgcn_interp_inreg_p2_f16,                // llvm.amdgcn.interp.inreg.p2.f16
    amdgcn_interp_mov,                         // llvm.amdgcn.interp.mov
    amdgcn_interp_p1,                          // llvm.amdgcn.interp.p1
    amdgcn_interp_p1_f16,                      // llvm.amdgcn.interp.p1.f16
    amdgcn_interp_p2,                          // llvm.amdgcn.interp.p2
    amdgcn_interp_p2_f16,                      // llvm.amdgcn.interp.p2.f16
    amdgcn_inverse_ballot,                     // llvm.amdgcn.inverse.ballot
    amdgcn_is_private,                         // llvm.amdgcn.is.private
    amdgcn_is_shared,                          // llvm.amdgcn.is.shared
    amdgcn_kernarg_segment_ptr,                // llvm.amdgcn.kernarg.segment.ptr
    amdgcn_kill,                               // llvm.amdgcn.kill
    amdgcn_ldexp,                              // llvm.amdgcn.ldexp
    amdgcn_lds_direct_load,                    // llvm.amdgcn.lds.direct.load
    amdgcn_lds_kernel_id,                      // llvm.amdgcn.lds.kernel.id
    amdgcn_lds_param_load,                     // llvm.amdgcn.lds.param.load
    amdgcn_lerp,                               // llvm.amdgcn.lerp
    amdgcn_live_mask,                          // llvm.amdgcn.live.mask
    amdgcn_log,                                // llvm.amdgcn.log
    amdgcn_log_clamp,                          // llvm.amdgcn.log.clamp
    amdgcn_loop,                               // llvm.amdgcn.loop
    amdgcn_make_buffer_rsrc,                   // llvm.amdgcn.make.buffer.rsrc
    amdgcn_mbcnt_hi,                           // llvm.amdgcn.mbcnt.hi
    amdgcn_mbcnt_lo,                           // llvm.amdgcn.mbcnt.lo
    amdgcn_mfma_f32_16x16x16bf16_1k,           // llvm.amdgcn.mfma.f32.16x16x16bf16.1k
    amdgcn_mfma_f32_16x16x16f16,               // llvm.amdgcn.mfma.f32.16x16x16f16
    amdgcn_mfma_f32_16x16x1f32,                // llvm.amdgcn.mfma.f32.16x16x1f32
    amdgcn_mfma_f32_16x16x2bf16,               // llvm.amdgcn.mfma.f32.16x16x2bf16
    amdgcn_mfma_f32_16x16x32_bf8_bf8,          // llvm.amdgcn.mfma.f32.16x16x32.bf8.bf8
    amdgcn_mfma_f32_16x16x32_bf8_fp8,          // llvm.amdgcn.mfma.f32.16x16x32.bf8.fp8
    amdgcn_mfma_f32_16x16x32_fp8_bf8,          // llvm.amdgcn.mfma.f32.16x16x32.fp8.bf8
    amdgcn_mfma_f32_16x16x32_fp8_fp8,          // llvm.amdgcn.mfma.f32.16x16x32.fp8.fp8
    amdgcn_mfma_f32_16x16x4bf16_1k,            // llvm.amdgcn.mfma.f32.16x16x4bf16.1k
    amdgcn_mfma_f32_16x16x4f16,                // llvm.amdgcn.mfma.f32.16x16x4f16
    amdgcn_mfma_f32_16x16x4f32,                // llvm.amdgcn.mfma.f32.16x16x4f32
    amdgcn_mfma_f32_16x16x8_xf32,              // llvm.amdgcn.mfma.f32.16x16x8.xf32
    amdgcn_mfma_f32_16x16x8bf16,               // llvm.amdgcn.mfma.f32.16x16x8bf16
    amdgcn_mfma_f32_32x32x16_bf8_bf8,          // llvm.amdgcn.mfma.f32.32x32x16.bf8.bf8
    amdgcn_mfma_f32_32x32x16_bf8_fp8,          // llvm.amdgcn.mfma.f32.32x32x16.bf8.fp8
    amdgcn_mfma_f32_32x32x16_fp8_bf8,          // llvm.amdgcn.mfma.f32.32x32x16.fp8.bf8
    amdgcn_mfma_f32_32x32x16_fp8_fp8,          // llvm.amdgcn.mfma.f32.32x32x16.fp8.fp8
    amdgcn_mfma_f32_32x32x1f32,                // llvm.amdgcn.mfma.f32.32x32x1f32
    amdgcn_mfma_f32_32x32x2bf16,               // llvm.amdgcn.mfma.f32.32x32x2bf16
    amdgcn_mfma_f32_32x32x2f32,                // llvm.amdgcn.mfma.f32.32x32x2f32
    amdgcn_mfma_f32_32x32x4_xf32,              // llvm.amdgcn.mfma.f32.32x32x4.xf32
    amdgcn_mfma_f32_32x32x4bf16,               // llvm.amdgcn.mfma.f32.32x32x4bf16
    amdgcn_mfma_f32_32x32x4bf16_1k,            // llvm.amdgcn.mfma.f32.32x32x4bf16.1k
    amdgcn_mfma_f32_32x32x4f16,                // llvm.amdgcn.mfma.f32.32x32x4f16
    amdgcn_mfma_f32_32x32x8bf16_1k,            // llvm.amdgcn.mfma.f32.32x32x8bf16.1k
    amdgcn_mfma_f32_32x32x8f16,                // llvm.amdgcn.mfma.f32.32x32x8f16
    amdgcn_mfma_f32_4x4x1f32,                  // llvm.amdgcn.mfma.f32.4x4x1f32
    amdgcn_mfma_f32_4x4x2bf16,                 // llvm.amdgcn.mfma.f32.4x4x2bf16
    amdgcn_mfma_f32_4x4x4bf16_1k,              // llvm.amdgcn.mfma.f32.4x4x4bf16.1k
    amdgcn_mfma_f32_4x4x4f16,                  // llvm.amdgcn.mfma.f32.4x4x4f16
    amdgcn_mfma_f64_16x16x4f64,                // llvm.amdgcn.mfma.f64.16x16x4f64
    amdgcn_mfma_f64_4x4x4f64,                  // llvm.amdgcn.mfma.f64.4x4x4f64
    amdgcn_mfma_i32_16x16x16i8,                // llvm.amdgcn.mfma.i32.16x16x16i8
    amdgcn_mfma_i32_16x16x32_i8,               // llvm.amdgcn.mfma.i32.16x16x32.i8
    amdgcn_mfma_i32_16x16x4i8,                 // llvm.amdgcn.mfma.i32.16x16x4i8
    amdgcn_mfma_i32_32x32x16_i8,               // llvm.amdgcn.mfma.i32.32x32x16.i8
    amdgcn_mfma_i32_32x32x4i8,                 // llvm.amdgcn.mfma.i32.32x32x4i8
    amdgcn_mfma_i32_32x32x8i8,                 // llvm.amdgcn.mfma.i32.32x32x8i8
    amdgcn_mfma_i32_4x4x4i8,                   // llvm.amdgcn.mfma.i32.4x4x4i8
    amdgcn_mov_dpp,                            // llvm.amdgcn.mov.dpp
    amdgcn_mov_dpp8,                           // llvm.amdgcn.mov.dpp8
    amdgcn_mqsad_pk_u16_u8,                    // llvm.amdgcn.mqsad.pk.u16.u8
    amdgcn_mqsad_u32_u8,                       // llvm.amdgcn.mqsad.u32.u8
    amdgcn_msad_u8,                            // llvm.amdgcn.msad.u8
    amdgcn_mul_i24,                            // llvm.amdgcn.mul.i24
    amdgcn_mul_u24,                            // llvm.amdgcn.mul.u24
    amdgcn_mulhi_i24,                          // llvm.amdgcn.mulhi.i24
    amdgcn_mulhi_u24,                          // llvm.amdgcn.mulhi.u24
    amdgcn_perm,                               // llvm.amdgcn.perm
    amdgcn_permlane16,                         // llvm.amdgcn.permlane16
    amdgcn_permlane64,                         // llvm.amdgcn.permlane64
    amdgcn_permlanex16,                        // llvm.amdgcn.permlanex16
    amdgcn_ps_live,                            // llvm.amdgcn.ps.live
    amdgcn_qsad_pk_u16_u8,                     // llvm.amdgcn.qsad.pk.u16.u8
    amdgcn_queue_ptr,                          // llvm.amdgcn.queue.ptr
    amdgcn_raw_buffer_atomic_add,              // llvm.amdgcn.raw.buffer.atomic.add
    amdgcn_raw_buffer_atomic_and,              // llvm.amdgcn.raw.buffer.atomic.and
    amdgcn_raw_buffer_atomic_cmpswap,          // llvm.amdgcn.raw.buffer.atomic.cmpswap
    amdgcn_raw_buffer_atomic_dec,              // llvm.amdgcn.raw.buffer.atomic.dec
    amdgcn_raw_buffer_atomic_fadd,             // llvm.amdgcn.raw.buffer.atomic.fadd
    amdgcn_raw_buffer_atomic_fmax,             // llvm.amdgcn.raw.buffer.atomic.fmax
    amdgcn_raw_buffer_atomic_fmin,             // llvm.amdgcn.raw.buffer.atomic.fmin
    amdgcn_raw_buffer_atomic_inc,              // llvm.amdgcn.raw.buffer.atomic.inc
    amdgcn_raw_buffer_atomic_or,               // llvm.amdgcn.raw.buffer.atomic.or
    amdgcn_raw_buffer_atomic_smax,             // llvm.amdgcn.raw.buffer.atomic.smax
    amdgcn_raw_buffer_atomic_smin,             // llvm.amdgcn.raw.buffer.atomic.smin
    amdgcn_raw_buffer_atomic_sub,              // llvm.amdgcn.raw.buffer.atomic.sub
    amdgcn_raw_buffer_atomic_swap,             // llvm.amdgcn.raw.buffer.atomic.swap
    amdgcn_raw_buffer_atomic_umax,             // llvm.amdgcn.raw.buffer.atomic.umax
    amdgcn_raw_buffer_atomic_umin,             // llvm.amdgcn.raw.buffer.atomic.umin
    amdgcn_raw_buffer_atomic_xor,              // llvm.amdgcn.raw.buffer.atomic.xor
    amdgcn_raw_buffer_load,                    // llvm.amdgcn.raw.buffer.load
    amdgcn_raw_buffer_load_format,             // llvm.amdgcn.raw.buffer.load.format
    amdgcn_raw_buffer_load_lds,                // llvm.amdgcn.raw.buffer.load.lds
    amdgcn_raw_buffer_store,                   // llvm.amdgcn.raw.buffer.store
    amdgcn_raw_buffer_store_format,            // llvm.amdgcn.raw.buffer.store.format
    amdgcn_raw_ptr_buffer_atomic_add,          // llvm.amdgcn.raw.ptr.buffer.atomic.add
    amdgcn_raw_ptr_buffer_atomic_and,          // llvm.amdgcn.raw.ptr.buffer.atomic.and
    amdgcn_raw_ptr_buffer_atomic_cmpswap,      // llvm.amdgcn.raw.ptr.buffer.atomic.cmpswap
    amdgcn_raw_ptr_buffer_atomic_dec,          // llvm.amdgcn.raw.ptr.buffer.atomic.dec
    amdgcn_raw_ptr_buffer_atomic_fadd,         // llvm.amdgcn.raw.ptr.buffer.atomic.fadd
    amdgcn_raw_ptr_buffer_atomic_fmax,         // llvm.amdgcn.raw.ptr.buffer.atomic.fmax
    amdgcn_raw_ptr_buffer_atomic_fmin,         // llvm.amdgcn.raw.ptr.buffer.atomic.fmin
    amdgcn_raw_ptr_buffer_atomic_inc,          // llvm.amdgcn.raw.ptr.buffer.atomic.inc
    amdgcn_raw_ptr_buffer_atomic_or,           // llvm.amdgcn.raw.ptr.buffer.atomic.or
    amdgcn_raw_ptr_buffer_atomic_smax,         // llvm.amdgcn.raw.ptr.buffer.atomic.smax
    amdgcn_raw_ptr_buffer_atomic_smin,         // llvm.amdgcn.raw.ptr.buffer.atomic.smin
    amdgcn_raw_ptr_buffer_atomic_sub,          // llvm.amdgcn.raw.ptr.buffer.atomic.sub
    amdgcn_raw_ptr_buffer_atomic_swap,         // llvm.amdgcn.raw.ptr.buffer.atomic.swap
    amdgcn_raw_ptr_buffer_atomic_umax,         // llvm.amdgcn.raw.ptr.buffer.atomic.umax
    amdgcn_raw_ptr_buffer_atomic_umin,         // llvm.amdgcn.raw.ptr.buffer.atomic.umin
    amdgcn_raw_ptr_buffer_atomic_xor,          // llvm.amdgcn.raw.ptr.buffer.atomic.xor
    amdgcn_raw_ptr_buffer_load,                // llvm.amdgcn.raw.ptr.buffer.load
    amdgcn_raw_ptr_buffer_load_format,         // llvm.amdgcn.raw.ptr.buffer.load.format
    amdgcn_raw_ptr_buffer_load_lds,            // llvm.amdgcn.raw.ptr.buffer.load.lds
    amdgcn_raw_ptr_buffer_store,               // llvm.amdgcn.raw.ptr.buffer.store
    amdgcn_raw_ptr_buffer_store_format,        // llvm.amdgcn.raw.ptr.buffer.store.format
    amdgcn_raw_ptr_tbuffer_load,               // llvm.amdgcn.raw.ptr.tbuffer.load
    amdgcn_raw_ptr_tbuffer_store,              // llvm.amdgcn.raw.ptr.tbuffer.store
    amdgcn_raw_tbuffer_load,                   // llvm.amdgcn.raw.tbuffer.load
    amdgcn_raw_tbuffer_store,                  // llvm.amdgcn.raw.tbuffer.store
    amdgcn_rcp,                                // llvm.amdgcn.rcp
    amdgcn_rcp_legacy,                         // llvm.amdgcn.rcp.legacy
    amdgcn_readfirstlane,                      // llvm.amdgcn.readfirstlane
    amdgcn_readlane,                           // llvm.amdgcn.readlane
    amdgcn_reloc_constant,                     // llvm.amdgcn.reloc.constant
    amdgcn_rsq,                                // llvm.amdgcn.rsq
    amdgcn_rsq_clamp,                          // llvm.amdgcn.rsq.clamp
    amdgcn_rsq_legacy,                         // llvm.amdgcn.rsq.legacy
    amdgcn_s_barrier,                          // llvm.amdgcn.s.barrier
    amdgcn_s_buffer_load,                      // llvm.amdgcn.s.buffer.load
    amdgcn_s_dcache_inv,                       // llvm.amdgcn.s.dcache.inv
    amdgcn_s_dcache_inv_vol,                   // llvm.amdgcn.s.dcache.inv.vol
    amdgcn_s_dcache_wb,                        // llvm.amdgcn.s.dcache.wb
    amdgcn_s_dcache_wb_vol,                    // llvm.amdgcn.s.dcache.wb.vol
    amdgcn_s_decperflevel,                     // llvm.amdgcn.s.decperflevel
    amdgcn_s_get_waveid_in_workgroup,          // llvm.amdgcn.s.get.waveid.in.workgroup
    amdgcn_s_getpc,                            // llvm.amdgcn.s.getpc
    amdgcn_s_getreg,                           // llvm.amdgcn.s.getreg
    amdgcn_s_incperflevel,                     // llvm.amdgcn.s.incperflevel
    amdgcn_s_memrealtime,                      // llvm.amdgcn.s.memrealtime
    amdgcn_s_memtime,                          // llvm.amdgcn.s.memtime
    amdgcn_s_sendmsg,                          // llvm.amdgcn.s.sendmsg
    amdgcn_s_sendmsg_rtn,                      // llvm.amdgcn.s.sendmsg.rtn
    amdgcn_s_sendmsghalt,                      // llvm.amdgcn.s.sendmsghalt
    amdgcn_s_sethalt,                          // llvm.amdgcn.s.sethalt
    amdgcn_s_setprio,                          // llvm.amdgcn.s.setprio
    amdgcn_s_setreg,                           // llvm.amdgcn.s.setreg
    amdgcn_s_sleep,                            // llvm.amdgcn.s.sleep
    amdgcn_s_wait_event_export_ready,          // llvm.amdgcn.s.wait.event.export.ready
    amdgcn_s_waitcnt,                          // llvm.amdgcn.s.waitcnt
    amdgcn_sad_hi_u8,                          // llvm.amdgcn.sad.hi.u8
    amdgcn_sad_u16,                            // llvm.amdgcn.sad.u16
    amdgcn_sad_u8,                             // llvm.amdgcn.sad.u8
    amdgcn_sbfe,                               // llvm.amdgcn.sbfe
    amdgcn_sched_barrier,                      // llvm.amdgcn.sched.barrier
    amdgcn_sched_group_barrier,                // llvm.amdgcn.sched.group.barrier
    amdgcn_sdot2,                              // llvm.amdgcn.sdot2
    amdgcn_sdot4,                              // llvm.amdgcn.sdot4
    amdgcn_sdot8,                              // llvm.amdgcn.sdot8
    amdgcn_set_inactive,                       // llvm.amdgcn.set.inactive
    amdgcn_sffbh,                              // llvm.amdgcn.sffbh
    amdgcn_sin,                                // llvm.amdgcn.sin
    amdgcn_smfmac_f32_16x16x32_bf16,           // llvm.amdgcn.smfmac.f32.16x16x32.bf16
    amdgcn_smfmac_f32_16x16x32_f16,            // llvm.amdgcn.smfmac.f32.16x16x32.f16
    amdgcn_smfmac_f32_16x16x64_bf8_bf8,        // llvm.amdgcn.smfmac.f32.16x16x64.bf8.bf8
    amdgcn_smfmac_f32_16x16x64_bf8_fp8,        // llvm.amdgcn.smfmac.f32.16x16x64.bf8.fp8
    amdgcn_smfmac_f32_16x16x64_fp8_bf8,        // llvm.amdgcn.smfmac.f32.16x16x64.fp8.bf8
    amdgcn_smfmac_f32_16x16x64_fp8_fp8,        // llvm.amdgcn.smfmac.f32.16x16x64.fp8.fp8
    amdgcn_smfmac_f32_32x32x16_bf16,           // llvm.amdgcn.smfmac.f32.32x32x16.bf16
    amdgcn_smfmac_f32_32x32x16_f16,            // llvm.amdgcn.smfmac.f32.32x32x16.f16
    amdgcn_smfmac_f32_32x32x32_bf8_bf8,        // llvm.amdgcn.smfmac.f32.32x32x32.bf8.bf8
    amdgcn_smfmac_f32_32x32x32_bf8_fp8,        // llvm.amdgcn.smfmac.f32.32x32x32.bf8.fp8
    amdgcn_smfmac_f32_32x32x32_fp8_bf8,        // llvm.amdgcn.smfmac.f32.32x32x32.fp8.bf8
    amdgcn_smfmac_f32_32x32x32_fp8_fp8,        // llvm.amdgcn.smfmac.f32.32x32x32.fp8.fp8
    amdgcn_smfmac_i32_16x16x64_i8,             // llvm.amdgcn.smfmac.i32.16x16x64.i8
    amdgcn_smfmac_i32_32x32x32_i8,             // llvm.amdgcn.smfmac.i32.32x32x32.i8
    amdgcn_softwqm,                            // llvm.amdgcn.softwqm
    amdgcn_sqrt,                               // llvm.amdgcn.sqrt
    amdgcn_strict_wqm,                         // llvm.amdgcn.strict.wqm
    amdgcn_strict_wwm,                         // llvm.amdgcn.strict.wwm
    amdgcn_struct_buffer_atomic_add,           // llvm.amdgcn.struct.buffer.atomic.add
    amdgcn_struct_buffer_atomic_and,           // llvm.amdgcn.struct.buffer.atomic.and
    amdgcn_struct_buffer_atomic_cmpswap,       // llvm.amdgcn.struct.buffer.atomic.cmpswap
    amdgcn_struct_buffer_atomic_dec,           // llvm.amdgcn.struct.buffer.atomic.dec
    amdgcn_struct_buffer_atomic_fadd,          // llvm.amdgcn.struct.buffer.atomic.fadd
    amdgcn_struct_buffer_atomic_fmax,          // llvm.amdgcn.struct.buffer.atomic.fmax
    amdgcn_struct_buffer_atomic_fmin,          // llvm.amdgcn.struct.buffer.atomic.fmin
    amdgcn_struct_buffer_atomic_inc,           // llvm.amdgcn.struct.buffer.atomic.inc
    amdgcn_struct_buffer_atomic_or,            // llvm.amdgcn.struct.buffer.atomic.or
    amdgcn_struct_buffer_atomic_smax,          // llvm.amdgcn.struct.buffer.atomic.smax
    amdgcn_struct_buffer_atomic_smin,          // llvm.amdgcn.struct.buffer.atomic.smin
    amdgcn_struct_buffer_atomic_sub,           // llvm.amdgcn.struct.buffer.atomic.sub
    amdgcn_struct_buffer_atomic_swap,          // llvm.amdgcn.struct.buffer.atomic.swap
    amdgcn_struct_buffer_atomic_umax,          // llvm.amdgcn.struct.buffer.atomic.umax
    amdgcn_struct_buffer_atomic_umin,          // llvm.amdgcn.struct.buffer.atomic.umin
    amdgcn_struct_buffer_atomic_xor,           // llvm.amdgcn.struct.buffer.atomic.xor
    amdgcn_struct_buffer_load,                 // llvm.amdgcn.struct.buffer.load
    amdgcn_struct_buffer_load_format,          // llvm.amdgcn.struct.buffer.load.format
    amdgcn_struct_buffer_load_lds,             // llvm.amdgcn.struct.buffer.load.lds
    amdgcn_struct_buffer_store,                // llvm.amdgcn.struct.buffer.store
    amdgcn_struct_buffer_store_format,         // llvm.amdgcn.struct.buffer.store.format
    amdgcn_struct_ptr_buffer_atomic_add,       // llvm.amdgcn.struct.ptr.buffer.atomic.add
    amdgcn_struct_ptr_buffer_atomic_and,       // llvm.amdgcn.struct.ptr.buffer.atomic.and
    amdgcn_struct_ptr_buffer_atomic_cmpswap,   // llvm.amdgcn.struct.ptr.buffer.atomic.cmpswap
    amdgcn_struct_ptr_buffer_atomic_dec,       // llvm.amdgcn.struct.ptr.buffer.atomic.dec
    amdgcn_struct_ptr_buffer_atomic_fadd,      // llvm.amdgcn.struct.ptr.buffer.atomic.fadd
    amdgcn_struct_ptr_buffer_atomic_fmax,      // llvm.amdgcn.struct.ptr.buffer.atomic.fmax
    amdgcn_struct_ptr_buffer_atomic_fmin,      // llvm.amdgcn.struct.ptr.buffer.atomic.fmin
    amdgcn_struct_ptr_buffer_atomic_inc,       // llvm.amdgcn.struct.ptr.buffer.atomic.inc
    amdgcn_struct_ptr_buffer_atomic_or,        // llvm.amdgcn.struct.ptr.buffer.atomic.or
    amdgcn_struct_ptr_buffer_atomic_smax,      // llvm.amdgcn.struct.ptr.buffer.atomic.smax
    amdgcn_struct_ptr_buffer_atomic_smin,      // llvm.amdgcn.struct.ptr.buffer.atomic.smin
    amdgcn_struct_ptr_buffer_atomic_sub,       // llvm.amdgcn.struct.ptr.buffer.atomic.sub
    amdgcn_struct_ptr_buffer_atomic_swap,      // llvm.amdgcn.struct.ptr.buffer.atomic.swap
    amdgcn_struct_ptr_buffer_atomic_umax,      // llvm.amdgcn.struct.ptr.buffer.atomic.umax
    amdgcn_struct_ptr_buffer_atomic_umin,      // llvm.amdgcn.struct.ptr.buffer.atomic.umin
    amdgcn_struct_ptr_buffer_atomic_xor,       // llvm.amdgcn.struct.ptr.buffer.atomic.xor
    amdgcn_struct_ptr_buffer_load,             // llvm.amdgcn.struct.ptr.buffer.load
    amdgcn_struct_ptr_buffer_load_format,      // llvm.amdgcn.struct.ptr.buffer.load.format
    amdgcn_struct_ptr_buffer_load_lds,         // llvm.amdgcn.struct.ptr.buffer.load.lds
    amdgcn_struct_ptr_buffer_store,            // llvm.amdgcn.struct.ptr.buffer.store
    amdgcn_struct_ptr_buffer_store_format,     // llvm.amdgcn.struct.ptr.buffer.store.format
    amdgcn_struct_ptr_tbuffer_load,            // llvm.amdgcn.struct.ptr.tbuffer.load
    amdgcn_struct_ptr_tbuffer_store,           // llvm.amdgcn.struct.ptr.tbuffer.store
    amdgcn_struct_tbuffer_load,                // llvm.amdgcn.struct.tbuffer.load
    amdgcn_struct_tbuffer_store,               // llvm.amdgcn.struct.tbuffer.store
    amdgcn_sudot4,                             // llvm.amdgcn.sudot4
    amdgcn_sudot8,                             // llvm.amdgcn.sudot8
    amdgcn_tbuffer_load,                       // llvm.amdgcn.tbuffer.load
    amdgcn_tbuffer_store,                      // llvm.amdgcn.tbuffer.store
    amdgcn_trig_preop,                         // llvm.amdgcn.trig.preop
    amdgcn_ubfe,                               // llvm.amdgcn.ubfe
    amdgcn_udot2,                              // llvm.amdgcn.udot2
    amdgcn_udot4,                              // llvm.amdgcn.udot4
    amdgcn_udot8,                              // llvm.amdgcn.udot8
    amdgcn_unreachable,                        // llvm.amdgcn.unreachable
    amdgcn_update_dpp,                         // llvm.amdgcn.update.dpp
    amdgcn_wave_barrier,                       // llvm.amdgcn.wave.barrier
    amdgcn_wave_reduce_umax,                   // llvm.amdgcn.wave.reduce.umax
    amdgcn_wave_reduce_umin,                   // llvm.amdgcn.wave.reduce.umin
    amdgcn_wavefrontsize,                      // llvm.amdgcn.wavefrontsize
    amdgcn_wmma_bf16_16x16x16_bf16,            // llvm.amdgcn.wmma.bf16.16x16x16.bf16
    amdgcn_wmma_f16_16x16x16_f16,              // llvm.amdgcn.wmma.f16.16x16x16.f16
    amdgcn_wmma_f32_16x16x16_bf16,             // llvm.amdgcn.wmma.f32.16x16x16.bf16
    amdgcn_wmma_f32_16x16x16_f16,              // llvm.amdgcn.wmma.f32.16x16x16.f16
    amdgcn_wmma_i32_16x16x16_iu4,              // llvm.amdgcn.wmma.i32.16x16x16.iu4
    amdgcn_wmma_i32_16x16x16_iu8,              // llvm.amdgcn.wmma.i32.16x16x16.iu8
    amdgcn_workgroup_id_x,                     // llvm.amdgcn.workgroup.id.x
    amdgcn_workgroup_id_y,                     // llvm.amdgcn.workgroup.id.y
    amdgcn_workgroup_id_z,                     // llvm.amdgcn.workgroup.id.z
    amdgcn_workitem_id_x,                      // llvm.amdgcn.workitem.id.x
    amdgcn_workitem_id_y,                      // llvm.amdgcn.workitem.id.y
    amdgcn_workitem_id_z,                      // llvm.amdgcn.workitem.id.z
    amdgcn_wqm,                                // llvm.amdgcn.wqm
    amdgcn_wqm_demote,                         // llvm.amdgcn.wqm.demote
    amdgcn_wqm_vote,                           // llvm.amdgcn.wqm.vote
    amdgcn_writelane,                          // llvm.amdgcn.writelane
    amdgcn_wwm,                                // llvm.amdgcn.wwm
}; // enum
} // namespace Intrinsic
} // namespace llvm

#endif
