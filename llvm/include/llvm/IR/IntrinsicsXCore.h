/*===- TableGen'erated file -------------------------------------*- C++ -*-===*\
|*                                                                            *|
|* Intrinsic Function Source Fragment                                         *|
|*                                                                            *|
|* Automatically generated file, do not edit!                                 *|
|*                                                                            *|
\*===----------------------------------------------------------------------===*/

#ifndef LLVM_IR_INTRINSIC_XCORE_ENUMS_H
#define LLVM_IR_INTRINSIC_XCORE_ENUMS_H

namespace llvm {
namespace Intrinsic {
enum XCOREIntrinsics : unsigned {
// Enum values for intrinsics
    xcore_bitrev = 11873,                              // llvm.xcore.bitrev
    xcore_checkevent,                          // llvm.xcore.checkevent
    xcore_chkct,                               // llvm.xcore.chkct
    xcore_clre,                                // llvm.xcore.clre
    xcore_clrpt,                               // llvm.xcore.clrpt
    xcore_clrsr,                               // llvm.xcore.clrsr
    xcore_crc32,                               // llvm.xcore.crc32
    xcore_crc8,                                // llvm.xcore.crc8
    xcore_edu,                                 // llvm.xcore.edu
    xcore_eeu,                                 // llvm.xcore.eeu
    xcore_endin,                               // llvm.xcore.endin
    xcore_freer,                               // llvm.xcore.freer
    xcore_geted,                               // llvm.xcore.geted
    xcore_getet,                               // llvm.xcore.getet
    xcore_getid,                               // llvm.xcore.getid
    xcore_getps,                               // llvm.xcore.getps
    xcore_getr,                                // llvm.xcore.getr
    xcore_getst,                               // llvm.xcore.getst
    xcore_getts,                               // llvm.xcore.getts
    xcore_in,                                  // llvm.xcore.in
    xcore_inct,                                // llvm.xcore.inct
    xcore_initcp,                              // llvm.xcore.initcp
    xcore_initdp,                              // llvm.xcore.initdp
    xcore_initlr,                              // llvm.xcore.initlr
    xcore_initpc,                              // llvm.xcore.initpc
    xcore_initsp,                              // llvm.xcore.initsp
    xcore_inshr,                               // llvm.xcore.inshr
    xcore_int,                                 // llvm.xcore.int
    xcore_mjoin,                               // llvm.xcore.mjoin
    xcore_msync,                               // llvm.xcore.msync
    xcore_out,                                 // llvm.xcore.out
    xcore_outct,                               // llvm.xcore.outct
    xcore_outshr,                              // llvm.xcore.outshr
    xcore_outt,                                // llvm.xcore.outt
    xcore_peek,                                // llvm.xcore.peek
    xcore_setc,                                // llvm.xcore.setc
    xcore_setclk,                              // llvm.xcore.setclk
    xcore_setd,                                // llvm.xcore.setd
    xcore_setev,                               // llvm.xcore.setev
    xcore_setps,                               // llvm.xcore.setps
    xcore_setpsc,                              // llvm.xcore.setpsc
    xcore_setpt,                               // llvm.xcore.setpt
    xcore_setrdy,                              // llvm.xcore.setrdy
    xcore_setsr,                               // llvm.xcore.setsr
    xcore_settw,                               // llvm.xcore.settw
    xcore_setv,                                // llvm.xcore.setv
    xcore_sext,                                // llvm.xcore.sext
    xcore_ssync,                               // llvm.xcore.ssync
    xcore_syncr,                               // llvm.xcore.syncr
    xcore_testct,                              // llvm.xcore.testct
    xcore_testwct,                             // llvm.xcore.testwct
    xcore_waitevent,                           // llvm.xcore.waitevent
    xcore_zext,                                // llvm.xcore.zext
}; // enum
} // namespace Intrinsic
} // namespace llvm

#endif
