/*===- TableGen'erated file -------------------------------------*- C++ -*-===*\
|*                                                                            *|
|* Intrinsic Function Source Fragment                                         *|
|*                                                                            *|
|* Automatically generated file, do not edit!                                 *|
|*                                                                            *|
\*===----------------------------------------------------------------------===*/

#ifdef GET_INTRINSIC_ENUM_VALUES
// Enum values for intrinsics
    abs = 1,                                       // llvm.abs
    addressofreturnaddress,                    // llvm.addressofreturnaddress
    adjust_trampoline,                         // llvm.adjust.trampoline
    annotation,                                // llvm.annotation
    arithmetic_fence,                          // llvm.arithmetic.fence
    asan_check_memaccess,                      // llvm.asan.check.memaccess
    assume,                                    // llvm.assume
    bitreverse,                                // llvm.bitreverse
    bswap,                                     // llvm.bswap
    call_preallocated_arg,                     // llvm.call.preallocated.arg
    call_preallocated_setup,                   // llvm.call.preallocated.setup
    call_preallocated_teardown,                // llvm.call.preallocated.teardown
    callbr_landingpad,                         // llvm.callbr.landingpad
    canonicalize,                              // llvm.canonicalize
    ceil,                                      // llvm.ceil
    clear_cache,                               // llvm.clear_cache
    codeview_annotation,                       // llvm.codeview.annotation
    convert_from_fp16,                         // llvm.convert.from.fp16
    convert_to_fp16,                           // llvm.convert.to.fp16
    copysign,                                  // llvm.copysign
    coro_align,                                // llvm.coro.align
    coro_alloc,                                // llvm.coro.alloc
    coro_alloca_alloc,                         // llvm.coro.alloca.alloc
    coro_alloca_free,                          // llvm.coro.alloca.free
    coro_alloca_get,                           // llvm.coro.alloca.get
    coro_async_context_alloc,                  // llvm.coro.async.context.alloc
    coro_async_context_dealloc,                // llvm.coro.async.context.dealloc
    coro_async_resume,                         // llvm.coro.async.resume
    coro_async_size_replace,                   // llvm.coro.async.size.replace
    coro_begin,                                // llvm.coro.begin
    coro_destroy,                              // llvm.coro.destroy
    coro_done,                                 // llvm.coro.done
    coro_end,                                  // llvm.coro.end
    coro_end_async,                            // llvm.coro.end.async
    coro_frame,                                // llvm.coro.frame
    coro_free,                                 // llvm.coro.free
    coro_id,                                   // llvm.coro.id
    coro_id_async,                             // llvm.coro.id.async
    coro_id_retcon,                            // llvm.coro.id.retcon
    coro_id_retcon_once,                       // llvm.coro.id.retcon.once
    coro_noop,                                 // llvm.coro.noop
    coro_prepare_async,                        // llvm.coro.prepare.async
    coro_prepare_retcon,                       // llvm.coro.prepare.retcon
    coro_promise,                              // llvm.coro.promise
    coro_resume,                               // llvm.coro.resume
    coro_save,                                 // llvm.coro.save
    coro_size,                                 // llvm.coro.size
    coro_subfn_addr,                           // llvm.coro.subfn.addr
    coro_suspend,                              // llvm.coro.suspend
    coro_suspend_async,                        // llvm.coro.suspend.async
    coro_suspend_retcon,                       // llvm.coro.suspend.retcon
    cos,                                       // llvm.cos
    ctlz,                                      // llvm.ctlz
    ctpop,                                     // llvm.ctpop
    cttz,                                      // llvm.cttz
    dbg_assign,                                // llvm.dbg.assign
    dbg_declare,                               // llvm.dbg.declare
    dbg_label,                                 // llvm.dbg.label
    dbg_value,                                 // llvm.dbg.value
    debugtrap,                                 // llvm.debugtrap
    donothing,                                 // llvm.donothing
    eh_dwarf_cfa,                              // llvm.eh.dwarf.cfa
    eh_exceptioncode,                          // llvm.eh.exceptioncode
    eh_exceptionpointer,                       // llvm.eh.exceptionpointer
    eh_recoverfp,                              // llvm.eh.recoverfp
    eh_return_i32,                             // llvm.eh.return.i32
    eh_return_i64,                             // llvm.eh.return.i64
    eh_sjlj_callsite,                          // llvm.eh.sjlj.callsite
    eh_sjlj_functioncontext,                   // llvm.eh.sjlj.functioncontext
    eh_sjlj_longjmp,                           // llvm.eh.sjlj.longjmp
    eh_sjlj_lsda,                              // llvm.eh.sjlj.lsda
    eh_sjlj_setjmp,                            // llvm.eh.sjlj.setjmp
    eh_sjlj_setup_dispatch,                    // llvm.eh.sjlj.setup.dispatch
    eh_typeid_for,                             // llvm.eh.typeid.for
    eh_unwind_init,                            // llvm.eh.unwind.init
    exp,                                       // llvm.exp
    exp2,                                      // llvm.exp2
    expect,                                    // llvm.expect
    expect_with_probability,                   // llvm.expect.with.probability
    experimental_constrained_ceil,             // llvm.experimental.constrained.ceil
    experimental_constrained_cos,              // llvm.experimental.constrained.cos
    experimental_constrained_exp,              // llvm.experimental.constrained.exp
    experimental_constrained_exp2,             // llvm.experimental.constrained.exp2
    experimental_constrained_fadd,             // llvm.experimental.constrained.fadd
    experimental_constrained_fcmp,             // llvm.experimental.constrained.fcmp
    experimental_constrained_fcmps,            // llvm.experimental.constrained.fcmps
    experimental_constrained_fdiv,             // llvm.experimental.constrained.fdiv
    experimental_constrained_floor,            // llvm.experimental.constrained.floor
    experimental_constrained_fma,              // llvm.experimental.constrained.fma
    experimental_constrained_fmul,             // llvm.experimental.constrained.fmul
    experimental_constrained_fmuladd,          // llvm.experimental.constrained.fmuladd
    experimental_constrained_fpext,            // llvm.experimental.constrained.fpext
    experimental_constrained_fptosi,           // llvm.experimental.constrained.fptosi
    experimental_constrained_fptoui,           // llvm.experimental.constrained.fptoui
    experimental_constrained_fptrunc,          // llvm.experimental.constrained.fptrunc
    experimental_constrained_frem,             // llvm.experimental.constrained.frem
    experimental_constrained_fsub,             // llvm.experimental.constrained.fsub
    experimental_constrained_ldexp,            // llvm.experimental.constrained.ldexp
    experimental_constrained_llrint,           // llvm.experimental.constrained.llrint
    experimental_constrained_llround,          // llvm.experimental.constrained.llround
    experimental_constrained_log,              // llvm.experimental.constrained.log
    experimental_constrained_log10,            // llvm.experimental.constrained.log10
    experimental_constrained_log2,             // llvm.experimental.constrained.log2
    experimental_constrained_lrint,            // llvm.experimental.constrained.lrint
    experimental_constrained_lround,           // llvm.experimental.constrained.lround
    experimental_constrained_maximum,          // llvm.experimental.constrained.maximum
    experimental_constrained_maxnum,           // llvm.experimental.constrained.maxnum
    experimental_constrained_minimum,          // llvm.experimental.constrained.minimum
    experimental_constrained_minnum,           // llvm.experimental.constrained.minnum
    experimental_constrained_nearbyint,        // llvm.experimental.constrained.nearbyint
    experimental_constrained_pow,              // llvm.experimental.constrained.pow
    experimental_constrained_powi,             // llvm.experimental.constrained.powi
    experimental_constrained_rint,             // llvm.experimental.constrained.rint
    experimental_constrained_round,            // llvm.experimental.constrained.round
    experimental_constrained_roundeven,        // llvm.experimental.constrained.roundeven
    experimental_constrained_sin,              // llvm.experimental.constrained.sin
    experimental_constrained_sitofp,           // llvm.experimental.constrained.sitofp
    experimental_constrained_sqrt,             // llvm.experimental.constrained.sqrt
    experimental_constrained_trunc,            // llvm.experimental.constrained.trunc
    experimental_constrained_uitofp,           // llvm.experimental.constrained.uitofp
    experimental_convergence_anchor,           // llvm.experimental.convergence.anchor
    experimental_convergence_entry,            // llvm.experimental.convergence.entry
    experimental_convergence_loop,             // llvm.experimental.convergence.loop
    experimental_deoptimize,                   // llvm.experimental.deoptimize
    experimental_gc_get_pointer_base,          // llvm.experimental.gc.get.pointer.base
    experimental_gc_get_pointer_offset,        // llvm.experimental.gc.get.pointer.offset
    experimental_gc_relocate,                  // llvm.experimental.gc.relocate
    experimental_gc_result,                    // llvm.experimental.gc.result
    experimental_gc_statepoint,                // llvm.experimental.gc.statepoint
    experimental_get_vector_length,            // llvm.experimental.get.vector.length
    experimental_guard,                        // llvm.experimental.guard
    experimental_noalias_scope_decl,           // llvm.experimental.noalias.scope.decl
    experimental_patchpoint_i64,               // llvm.experimental.patchpoint.i64
    experimental_patchpoint_void,              // llvm.experimental.patchpoint.void
    experimental_stackmap,                     // llvm.experimental.stackmap
    experimental_stepvector,                   // llvm.experimental.stepvector
    experimental_vector_deinterleave2,         // llvm.experimental.vector.deinterleave2
    experimental_vector_interleave2,           // llvm.experimental.vector.interleave2
    experimental_vector_reverse,               // llvm.experimental.vector.reverse
    experimental_vector_splice,                // llvm.experimental.vector.splice
    experimental_vp_splice,                    // llvm.experimental.vp.splice
    experimental_vp_strided_load,              // llvm.experimental.vp.strided.load
    experimental_vp_strided_store,             // llvm.experimental.vp.strided.store
    experimental_widenable_condition,          // llvm.experimental.widenable.condition
    fabs,                                      // llvm.fabs
    floor,                                     // llvm.floor
    fma,                                       // llvm.fma
    fmuladd,                                   // llvm.fmuladd
    fptosi_sat,                                // llvm.fptosi.sat
    fptoui_sat,                                // llvm.fptoui.sat
    fptrunc_round,                             // llvm.fptrunc.round
    frameaddress,                              // llvm.frameaddress
    frexp,                                     // llvm.frexp
    fshl,                                      // llvm.fshl
    fshr,                                      // llvm.fshr
    gcread,                                    // llvm.gcread
    gcroot,                                    // llvm.gcroot
    gcwrite,                                   // llvm.gcwrite
    get_active_lane_mask,                      // llvm.get.active.lane.mask
    get_dynamic_area_offset,                   // llvm.get.dynamic.area.offset
    get_fpenv,                                 // llvm.get.fpenv
    get_rounding,                              // llvm.get.rounding
    hwasan_check_memaccess,                    // llvm.hwasan.check.memaccess
    hwasan_check_memaccess_shortgranules,      // llvm.hwasan.check.memaccess.shortgranules
    icall_branch_funnel,                       // llvm.icall.branch.funnel
    init_trampoline,                           // llvm.init.trampoline
    instrprof_cover,                           // llvm.instrprof.cover
    instrprof_increment,                       // llvm.instrprof.increment
    instrprof_increment_step,                  // llvm.instrprof.increment.step
    instrprof_timestamp,                       // llvm.instrprof.timestamp
    instrprof_value_profile,                   // llvm.instrprof.value.profile
    invariant_end,                             // llvm.invariant.end
    invariant_start,                           // llvm.invariant.start
    is_constant,                               // llvm.is.constant
    is_fpclass,                                // llvm.is.fpclass
    launder_invariant_group,                   // llvm.launder.invariant.group
    ldexp,                                     // llvm.ldexp
    lifetime_end,                              // llvm.lifetime.end
    lifetime_start,                            // llvm.lifetime.start
    llrint,                                    // llvm.llrint
    llround,                                   // llvm.llround
    load_relative,                             // llvm.load.relative
    localaddress,                              // llvm.localaddress
    localescape,                               // llvm.localescape
    localrecover,                              // llvm.localrecover
    log,                                       // llvm.log
    log10,                                     // llvm.log10
    log2,                                      // llvm.log2
    loop_decrement,                            // llvm.loop.decrement
    loop_decrement_reg,                        // llvm.loop.decrement.reg
    lrint,                                     // llvm.lrint
    lround,                                    // llvm.lround
    masked_compressstore,                      // llvm.masked.compressstore
    masked_expandload,                         // llvm.masked.expandload
    masked_gather,                             // llvm.masked.gather
    masked_load,                               // llvm.masked.load
    masked_scatter,                            // llvm.masked.scatter
    masked_store,                              // llvm.masked.store
    matrix_column_major_load,                  // llvm.matrix.column.major.load
    matrix_column_major_store,                 // llvm.matrix.column.major.store
    matrix_multiply,                           // llvm.matrix.multiply
    matrix_transpose,                          // llvm.matrix.transpose
    maximum,                                   // llvm.maximum
    maxnum,                                    // llvm.maxnum
    memcpy,                                    // llvm.memcpy
    memcpy_element_unordered_atomic,           // llvm.memcpy.element.unordered.atomic
    memcpy_inline,                             // llvm.memcpy.inline
    memmove,                                   // llvm.memmove
    memmove_element_unordered_atomic,          // llvm.memmove.element.unordered.atomic
    memset,                                    // llvm.memset
    memset_element_unordered_atomic,           // llvm.memset.element.unordered.atomic
    memset_inline,                             // llvm.memset.inline
    minimum,                                   // llvm.minimum
    minnum,                                    // llvm.minnum
    nearbyint,                                 // llvm.nearbyint
    objc_arc_annotation_bottomup_bbend,        // llvm.objc.arc.annotation.bottomup.bbend
    objc_arc_annotation_bottomup_bbstart,      // llvm.objc.arc.annotation.bottomup.bbstart
    objc_arc_annotation_topdown_bbend,         // llvm.objc.arc.annotation.topdown.bbend
    objc_arc_annotation_topdown_bbstart,       // llvm.objc.arc.annotation.topdown.bbstart
    objc_autorelease,                          // llvm.objc.autorelease
    objc_autoreleasePoolPop,                   // llvm.objc.autoreleasePoolPop
    objc_autoreleasePoolPush,                  // llvm.objc.autoreleasePoolPush
    objc_autoreleaseReturnValue,               // llvm.objc.autoreleaseReturnValue
    objc_clang_arc_noop_use,                   // llvm.objc.clang.arc.noop.use
    objc_clang_arc_use,                        // llvm.objc.clang.arc.use
    objc_copyWeak,                             // llvm.objc.copyWeak
    objc_destroyWeak,                          // llvm.objc.destroyWeak
    objc_initWeak,                             // llvm.objc.initWeak
    objc_loadWeak,                             // llvm.objc.loadWeak
    objc_loadWeakRetained,                     // llvm.objc.loadWeakRetained
    objc_moveWeak,                             // llvm.objc.moveWeak
    objc_release,                              // llvm.objc.release
    objc_retain,                               // llvm.objc.retain
    objc_retain_autorelease,                   // llvm.objc.retain.autorelease
    objc_retainAutorelease,                    // llvm.objc.retainAutorelease
    objc_retainAutoreleaseReturnValue,         // llvm.objc.retainAutoreleaseReturnValue
    objc_retainAutoreleasedReturnValue,        // llvm.objc.retainAutoreleasedReturnValue
    objc_retainBlock,                          // llvm.objc.retainBlock
    objc_retainedObject,                       // llvm.objc.retainedObject
    objc_storeStrong,                          // llvm.objc.storeStrong
    objc_storeWeak,                            // llvm.objc.storeWeak
    objc_sync_enter,                           // llvm.objc.sync.enter
    objc_sync_exit,                            // llvm.objc.sync.exit
    objc_unretainedObject,                     // llvm.objc.unretainedObject
    objc_unretainedPointer,                    // llvm.objc.unretainedPointer
    objc_unsafeClaimAutoreleasedReturnValue,   // llvm.objc.unsafeClaimAutoreleasedReturnValue
    objectsize,                                // llvm.objectsize
    pcmarker,                                  // llvm.pcmarker
    pow,                                       // llvm.pow
    powi,                                      // llvm.powi
    prefetch,                                  // llvm.prefetch
    preserve_array_access_index,               // llvm.preserve.array.access.index
    preserve_struct_access_index,              // llvm.preserve.struct.access.index
    preserve_union_access_index,               // llvm.preserve.union.access.index
    pseudoprobe,                               // llvm.pseudoprobe
    ptr_annotation,                            // llvm.ptr.annotation
    ptrauth_auth,                              // llvm.ptrauth.auth
    ptrauth_blend,                             // llvm.ptrauth.blend
    ptrauth_resign,                            // llvm.ptrauth.resign
    ptrauth_sign,                              // llvm.ptrauth.sign
    ptrauth_sign_generic,                      // llvm.ptrauth.sign.generic
    ptrauth_strip,                             // llvm.ptrauth.strip
    ptrmask,                                   // llvm.ptrmask
    public_type_test,                          // llvm.public.type.test
    read_register,                             // llvm.read_register
    read_volatile_register,                    // llvm.read_volatile_register
    readcyclecounter,                          // llvm.readcyclecounter
    reset_fpenv,                               // llvm.reset.fpenv
    returnaddress,                             // llvm.returnaddress
    rint,                                      // llvm.rint
    round,                                     // llvm.round
    roundeven,                                 // llvm.roundeven
    sadd_sat,                                  // llvm.sadd.sat
    sadd_with_overflow,                        // llvm.sadd.with.overflow
    sdiv_fix,                                  // llvm.sdiv.fix
    sdiv_fix_sat,                              // llvm.sdiv.fix.sat
    seh_scope_begin,                           // llvm.seh.scope.begin
    seh_scope_end,                             // llvm.seh.scope.end
    seh_try_begin,                             // llvm.seh.try.begin
    seh_try_end,                               // llvm.seh.try.end
    set_fpenv,                                 // llvm.set.fpenv
    set_loop_iterations,                       // llvm.set.loop.iterations
    set_rounding,                              // llvm.set.rounding
    sideeffect,                                // llvm.sideeffect
    sin,                                       // llvm.sin
    smax,                                      // llvm.smax
    smin,                                      // llvm.smin
    smul_fix,                                  // llvm.smul.fix
    smul_fix_sat,                              // llvm.smul.fix.sat
    smul_with_overflow,                        // llvm.smul.with.overflow
    sponentry,                                 // llvm.sponentry
    sqrt,                                      // llvm.sqrt
    ssa_copy,                                  // llvm.ssa.copy
    sshl_sat,                                  // llvm.sshl.sat
    ssub_sat,                                  // llvm.ssub.sat
    ssub_with_overflow,                        // llvm.ssub.with.overflow
    stackguard,                                // llvm.stackguard
    stackprotector,                            // llvm.stackprotector
    stackrestore,                              // llvm.stackrestore
    stacksave,                                 // llvm.stacksave
    start_loop_iterations,                     // llvm.start.loop.iterations
    strip_invariant_group,                     // llvm.strip.invariant.group
    swift_async_context_addr,                  // llvm.swift.async.context.addr
    test_set_loop_iterations,                  // llvm.test.set.loop.iterations
    test_start_loop_iterations,                // llvm.test.start.loop.iterations
    thread_pointer,                            // llvm.thread.pointer
    threadlocal_address,                       // llvm.threadlocal.address
    trap,                                      // llvm.trap
    trunc,                                     // llvm.trunc
    type_checked_load,                         // llvm.type.checked.load
    type_checked_load_relative,                // llvm.type.checked.load.relative
    type_test,                                 // llvm.type.test
    uadd_sat,                                  // llvm.uadd.sat
    uadd_with_overflow,                        // llvm.uadd.with.overflow
    ubsantrap,                                 // llvm.ubsantrap
    udiv_fix,                                  // llvm.udiv.fix
    udiv_fix_sat,                              // llvm.udiv.fix.sat
    umax,                                      // llvm.umax
    umin,                                      // llvm.umin
    umul_fix,                                  // llvm.umul.fix
    umul_fix_sat,                              // llvm.umul.fix.sat
    umul_with_overflow,                        // llvm.umul.with.overflow
    ushl_sat,                                  // llvm.ushl.sat
    usub_sat,                                  // llvm.usub.sat
    usub_with_overflow,                        // llvm.usub.with.overflow
    vacopy,                                    // llvm.va_copy
    vaend,                                     // llvm.va_end
    vastart,                                   // llvm.va_start
    var_annotation,                            // llvm.var.annotation
    vector_extract,                            // llvm.vector.extract
    vector_insert,                             // llvm.vector.insert
    vector_reduce_add,                         // llvm.vector.reduce.add
    vector_reduce_and,                         // llvm.vector.reduce.and
    vector_reduce_fadd,                        // llvm.vector.reduce.fadd
    vector_reduce_fmax,                        // llvm.vector.reduce.fmax
    vector_reduce_fmaximum,                    // llvm.vector.reduce.fmaximum
    vector_reduce_fmin,                        // llvm.vector.reduce.fmin
    vector_reduce_fminimum,                    // llvm.vector.reduce.fminimum
    vector_reduce_fmul,                        // llvm.vector.reduce.fmul
    vector_reduce_mul,                         // llvm.vector.reduce.mul
    vector_reduce_or,                          // llvm.vector.reduce.or
    vector_reduce_smax,                        // llvm.vector.reduce.smax
    vector_reduce_smin,                        // llvm.vector.reduce.smin
    vector_reduce_umax,                        // llvm.vector.reduce.umax
    vector_reduce_umin,                        // llvm.vector.reduce.umin
    vector_reduce_xor,                         // llvm.vector.reduce.xor
    vp_abs,                                    // llvm.vp.abs
    vp_add,                                    // llvm.vp.add
    vp_and,                                    // llvm.vp.and
    vp_ashr,                                   // llvm.vp.ashr
    vp_bitreverse,                             // llvm.vp.bitreverse
    vp_bswap,                                  // llvm.vp.bswap
    vp_ceil,                                   // llvm.vp.ceil
    vp_copysign,                               // llvm.vp.copysign
    vp_ctlz,                                   // llvm.vp.ctlz
    vp_ctpop,                                  // llvm.vp.ctpop
    vp_cttz,                                   // llvm.vp.cttz
    vp_fabs,                                   // llvm.vp.fabs
    vp_fadd,                                   // llvm.vp.fadd
    vp_fcmp,                                   // llvm.vp.fcmp
    vp_fdiv,                                   // llvm.vp.fdiv
    vp_floor,                                  // llvm.vp.floor
    vp_fma,                                    // llvm.vp.fma
    vp_fmul,                                   // llvm.vp.fmul
    vp_fmuladd,                                // llvm.vp.fmuladd
    vp_fneg,                                   // llvm.vp.fneg
    vp_fpext,                                  // llvm.vp.fpext
    vp_fptosi,                                 // llvm.vp.fptosi
    vp_fptoui,                                 // llvm.vp.fptoui
    vp_fptrunc,                                // llvm.vp.fptrunc
    vp_frem,                                   // llvm.vp.frem
    vp_fshl,                                   // llvm.vp.fshl
    vp_fshr,                                   // llvm.vp.fshr
    vp_fsub,                                   // llvm.vp.fsub
    vp_gather,                                 // llvm.vp.gather
    vp_icmp,                                   // llvm.vp.icmp
    vp_inttoptr,                               // llvm.vp.inttoptr
    vp_load,                                   // llvm.vp.load
    vp_lshr,                                   // llvm.vp.lshr
    vp_maxnum,                                 // llvm.vp.maxnum
    vp_merge,                                  // llvm.vp.merge
    vp_minnum,                                 // llvm.vp.minnum
    vp_mul,                                    // llvm.vp.mul
    vp_nearbyint,                              // llvm.vp.nearbyint
    vp_or,                                     // llvm.vp.or
    vp_ptrtoint,                               // llvm.vp.ptrtoint
    vp_reduce_add,                             // llvm.vp.reduce.add
    vp_reduce_and,                             // llvm.vp.reduce.and
    vp_reduce_fadd,                            // llvm.vp.reduce.fadd
    vp_reduce_fmax,                            // llvm.vp.reduce.fmax
    vp_reduce_fmin,                            // llvm.vp.reduce.fmin
    vp_reduce_fmul,                            // llvm.vp.reduce.fmul
    vp_reduce_mul,                             // llvm.vp.reduce.mul
    vp_reduce_or,                              // llvm.vp.reduce.or
    vp_reduce_smax,                            // llvm.vp.reduce.smax
    vp_reduce_smin,                            // llvm.vp.reduce.smin
    vp_reduce_umax,                            // llvm.vp.reduce.umax
    vp_reduce_umin,                            // llvm.vp.reduce.umin
    vp_reduce_xor,                             // llvm.vp.reduce.xor
    vp_rint,                                   // llvm.vp.rint
    vp_round,                                  // llvm.vp.round
    vp_roundeven,                              // llvm.vp.roundeven
    vp_roundtozero,                            // llvm.vp.roundtozero
    vp_scatter,                                // llvm.vp.scatter
    vp_sdiv,                                   // llvm.vp.sdiv
    vp_select,                                 // llvm.vp.select
    vp_sext,                                   // llvm.vp.sext
    vp_shl,                                    // llvm.vp.shl
    vp_sitofp,                                 // llvm.vp.sitofp
    vp_smax,                                   // llvm.vp.smax
    vp_smin,                                   // llvm.vp.smin
    vp_sqrt,                                   // llvm.vp.sqrt
    vp_srem,                                   // llvm.vp.srem
    vp_store,                                  // llvm.vp.store
    vp_sub,                                    // llvm.vp.sub
    vp_trunc,                                  // llvm.vp.trunc
    vp_udiv,                                   // llvm.vp.udiv
    vp_uitofp,                                 // llvm.vp.uitofp
    vp_umax,                                   // llvm.vp.umax
    vp_umin,                                   // llvm.vp.umin
    vp_urem,                                   // llvm.vp.urem
    vp_xor,                                    // llvm.vp.xor
    vp_zext,                                   // llvm.vp.zext
    vscale,                                    // llvm.vscale
    write_register,                            // llvm.write_register
    xray_customevent,                          // llvm.xray.customevent
    xray_typedevent,                           // llvm.xray.typedevent
    num_intrinsics = 11926
#endif

// llvm::Intrinsic::IITDescriptor::ArgKind
#ifdef GET_INTRINSIC_ARGKIND
    AK_Any = 0,
    AK_AnyInteger = 1,
    AK_AnyFloat = 2,
    AK_AnyVector = 3,
    AK_AnyPointer = 4,
    AK_MatchType = 7,
#endif

