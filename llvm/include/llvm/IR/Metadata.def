//===- llvm/IR/Metadata.def - Metadata definitions --------------*- C++ -*-===//
//
// Part of the LLVM Project, under the Apache License v2.0 with LLVM Exceptions.
// See https://llvm.org/LICENSE.txt for license information.
// SPDX-License-Identifier: Apache-2.0 WITH LLVM-exception
//
//===----------------------------------------------------------------------===//
//
// Macros for running through all types of metadata.
//
//===----------------------------------------------------------------------===//

#if !(defined HANDLE_METADATA || defined HANDLE_METADATA_LEAF ||               \
      defined HANDLE_METADATA_BRANCH || defined HANDLE_MDNODE_LEAF ||          \
      defined HANDLE_MDNODE_LEAF_UNIQUABLE || defined HANDLE_MDNODE_BRANCH ||  \
      defined HANDLE_SPECIALIZED_MDNODE_LEAF_UNIQUABLE ||                      \
      defined HANDLE_SPECIALIZED_MDNODE_LEAF ||                                \
      defined HANDLE_SPECIALIZED_MDNODE_BRANCH)
#error "Missing macro definition of HANDLE_METADATA*"
#endif

// Handler for all types of metadata.
#ifndef HANDLE_METADATA
#define HANDLE_METADATA(CLASS)
#endif

// Handler for leaf nodes in the class hierarchy.
#ifndef HANDLE_METADATA_LEAF
#define HANDLE_METADATA_LEAF(CLASS) HANDLE_METADATA(CLASS)
#endif

// Handler for non-leaf nodes in the class hierarchy.
#ifndef HANDLE_METADATA_BRANCH
#define HANDLE_METADATA_BRANCH(CLASS) HANDLE_METADATA(CLASS)
#endif

// Handler for specialized and uniquable leaf nodes under MDNode.  Defers to
// HANDLE_MDNODE_LEAF_UNIQUABLE if it's defined, otherwise to
// HANDLE_SPECIALIZED_MDNODE_LEAF.
#ifndef HANDLE_SPECIALIZED_MDNODE_LEAF_UNIQUABLE
#ifdef HANDLE_MDNODE_LEAF_UNIQUABLE
#define HANDLE_SPECIALIZED_MDNODE_LEAF_UNIQUABLE(CLASS)                        \
  HANDLE_MDNODE_LEAF_UNIQUABLE(CLASS)
#else
#define HANDLE_SPECIALIZED_MDNODE_LEAF_UNIQUABLE(CLASS)                        \
  HANDLE_SPECIALIZED_MDNODE_LEAF(CLASS)
#endif
#endif

// Handler for leaf nodes under MDNode.
#ifndef HANDLE_MDNODE_LEAF_UNIQUABLE
#define HANDLE_MDNODE_LEAF_UNIQUABLE(CLASS) HANDLE_MDNODE_LEAF(CLASS)
#endif

// Handler for leaf nodes under MDNode.
#ifndef HANDLE_MDNODE_LEAF
#define HANDLE_MDNODE_LEAF(CLASS) HANDLE_METADATA_LEAF(CLASS)
#endif

// Handler for non-leaf nodes under MDNode.
#ifndef HANDLE_MDNODE_BRANCH
#define HANDLE_MDNODE_BRANCH(CLASS) HANDLE_METADATA_BRANCH(CLASS)
#endif

// Handler for specialized leaf nodes under MDNode.
#ifndef HANDLE_SPECIALIZED_MDNODE_LEAF
#define HANDLE_SPECIALIZED_MDNODE_LEAF(CLASS) HANDLE_MDNODE_LEAF(CLASS)
#endif

// Handler for specialized non-leaf nodes under MDNode.
#ifndef HANDLE_SPECIALIZED_MDNODE_BRANCH
#define HANDLE_SPECIALIZED_MDNODE_BRANCH(CLASS) HANDLE_MDNODE_BRANCH(CLASS)
#endif

HANDLE_METADATA_LEAF(MDString)
HANDLE_METADATA_BRANCH(ValueAsMetadata)
HANDLE_METADATA_LEAF(ConstantAsMetadata)
HANDLE_METADATA_LEAF(LocalAsMetadata)
HANDLE_METADATA_LEAF(DistinctMDOperandPlaceholder)
HANDLE_MDNODE_BRANCH(MDNode)
HANDLE_MDNODE_LEAF_UNIQUABLE(MDTuple)
HANDLE_SPECIALIZED_MDNODE_LEAF_UNIQUABLE(DILocation)
HANDLE_SPECIALIZED_MDNODE_LEAF_UNIQUABLE(DIExpression)
HANDLE_SPECIALIZED_MDNODE_LEAF_UNIQUABLE(DIGlobalVariableExpression)
HANDLE_SPECIALIZED_MDNODE_BRANCH(DINode)
HANDLE_SPECIALIZED_MDNODE_LEAF_UNIQUABLE(GenericDINode)
HANDLE_SPECIALIZED_MDNODE_LEAF_UNIQUABLE(DISubrange)
HANDLE_SPECIALIZED_MDNODE_LEAF_UNIQUABLE(DIEnumerator)
HANDLE_SPECIALIZED_MDNODE_BRANCH(DIScope)
HANDLE_SPECIALIZED_MDNODE_BRANCH(DIType)
HANDLE_SPECIALIZED_MDNODE_LEAF_UNIQUABLE(DIBasicType)
HANDLE_SPECIALIZED_MDNODE_LEAF_UNIQUABLE(DIDerivedType)
HANDLE_SPECIALIZED_MDNODE_LEAF_UNIQUABLE(DICompositeType)
HANDLE_SPECIALIZED_MDNODE_LEAF_UNIQUABLE(DISubroutineType)
HANDLE_SPECIALIZED_MDNODE_LEAF_UNIQUABLE(DIFile)
HANDLE_SPECIALIZED_MDNODE_LEAF(DICompileUnit)
HANDLE_SPECIALIZED_MDNODE_BRANCH(DILocalScope)
HANDLE_SPECIALIZED_MDNODE_LEAF_UNIQUABLE(DISubprogram)
HANDLE_SPECIALIZED_MDNODE_BRANCH(DILexicalBlockBase)
HANDLE_SPECIALIZED_MDNODE_LEAF_UNIQUABLE(DILexicalBlock)
HANDLE_SPECIALIZED_MDNODE_LEAF_UNIQUABLE(DILexicalBlockFile)
HANDLE_SPECIALIZED_MDNODE_LEAF_UNIQUABLE(DINamespace)
HANDLE_SPECIALIZED_MDNODE_LEAF_UNIQUABLE(DIModule)
HANDLE_SPECIALIZED_MDNODE_BRANCH(DITemplateParameter)
HANDLE_SPECIALIZED_MDNODE_LEAF_UNIQUABLE(DITemplateTypeParameter)
HANDLE_SPECIALIZED_MDNODE_LEAF_UNIQUABLE(DITemplateValueParameter)
HANDLE_SPECIALIZED_MDNODE_BRANCH(DIVariable)
HANDLE_SPECIALIZED_MDNODE_LEAF_UNIQUABLE(DIGlobalVariable)
HANDLE_SPECIALIZED_MDNODE_LEAF_UNIQUABLE(DILocalVariable)
HANDLE_SPECIALIZED_MDNODE_LEAF_UNIQUABLE(DILabel)
HANDLE_SPECIALIZED_MDNODE_LEAF_UNIQUABLE(DIObjCProperty)
HANDLE_SPECIALIZED_MDNODE_LEAF_UNIQUABLE(DIImportedEntity)
HANDLE_SPECIALIZED_MDNODE_LEAF(DIAssignID)
HANDLE_SPECIALIZED_MDNODE_BRANCH(DIMacroNode)
HANDLE_SPECIALIZED_MDNODE_LEAF_UNIQUABLE(DIMacro)
HANDLE_SPECIALIZED_MDNODE_LEAF_UNIQUABLE(DIMacroFile)
HANDLE_SPECIALIZED_MDNODE_LEAF_UNIQUABLE(DICommonBlock)
HANDLE_SPECIALIZED_MDNODE_LEAF_UNIQUABLE(DIArgList)
HANDLE_SPECIALIZED_MDNODE_LEAF_UNIQUABLE(DIStringType)
HANDLE_SPECIALIZED_MDNODE_LEAF_UNIQUABLE(DIGenericSubrange)

#undef HANDLE_METADATA
#undef HANDLE_METADATA_LEAF
#undef HANDLE_METADATA_BRANCH
#undef HANDLE_MDNODE_LEAF
#undef HANDLE_MDNODE_LEAF_UNIQUABLE
#undef HANDLE_MDNODE_BRANCH
#undef HANDLE_SPECIALIZED_MDNODE_LEAF
#undef HANDLE_SPECIALIZED_MDNODE_LEAF_UNIQUABLE
#undef HANDLE_SPECIALIZED_MDNODE_BRANCH
