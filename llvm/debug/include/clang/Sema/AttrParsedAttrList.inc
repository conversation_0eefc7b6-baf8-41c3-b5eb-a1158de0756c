/*===- TableGen'erated file -------------------------------------*- C++ -*-===*\
|*                                                                            *|
|* List of all attributes that <PERSON><PERSON> recognizes                               *|
|*                                                                            *|
|* Automatically generated file, do not edit!                                 *|
|*                                                                            *|
\*===----------------------------------------------------------------------===*/

#ifndef PARSED_ATTR
#define PARSED_ATTR(NAME) NAME
#endif

PARSED_ATTR(AArch64SVEPcs)
PARSED_ATTR(AArch64VectorPcs)
PARSED_ATTR(AMDGPUFlatWorkGroupSize)
PARSED_ATTR(AMDGPUKernelCall)
PARSED_ATTR(AMDGPUNumSGPR)
PARSED_ATTR(AMDGPUNumVGPR)
PARSED_ATTR(AMDGPUWavesPerEU)
PARSED_ATTR(Interrupt)
PARSED_ATTR(AVRSignal)
PARSED_ATTR(AbiTag)
PARSED_ATTR(AcquireCapability)
PARSED_ATTR(AcquireHandle)
PARSED_ATTR(AcquiredAfter)
PARSED_ATTR(AcquiredBefore)
PARSED_ATTR(AddressSpace)
PARSED_ATTR(Alias)
PARSED_ATTR(AlignValue)
PARSED_ATTR(Aligned)
PARSED_ATTR(AllocAlign)
PARSED_ATTR(AllocSize)
PARSED_ATTR(AlwaysDestroy)
PARSED_ATTR(AlwaysInline)
PARSED_ATTR(AnalyzerNoReturn)
PARSED_ATTR(Annotate)
PARSED_ATTR(AnnotateType)
PARSED_ATTR(AnyX86NoCallerSavedRegisters)
PARSED_ATTR(AnyX86NoCfCheck)
PARSED_ATTR(ArcWeakrefUnavailable)
PARSED_ATTR(ArgumentWithTypeTag)
PARSED_ATTR(ArmBuiltinAlias)
PARSED_ATTR(ArmMveStrictPolymorphism)
PARSED_ATTR(ArmStreaming)
PARSED_ATTR(ArmSveVectorBits)
PARSED_ATTR(Artificial)
PARSED_ATTR(AssertCapability)
PARSED_ATTR(AssertExclusiveLock)
PARSED_ATTR(AssertSharedLock)
PARSED_ATTR(AssumeAligned)
PARSED_ATTR(Assumption)
PARSED_ATTR(Availability)
PARSED_ATTR(AvailableOnlyInDefaultEvalMethod)
PARSED_ATTR(BPFPreserveAccessIndex)
PARSED_ATTR(BTFDeclTag)
PARSED_ATTR(BTFTypeTag)
PARSED_ATTR(Blocks)
PARSED_ATTR(BuiltinAlias)
PARSED_ATTR(CDecl)
PARSED_ATTR(CFAuditedTransfer)
PARSED_ATTR(CFConsumed)
PARSED_ATTR(CFGuard)
PARSED_ATTR(CFICanonicalJumpTable)
PARSED_ATTR(CFReturnsNotRetained)
PARSED_ATTR(CFReturnsRetained)
PARSED_ATTR(CFUnknownTransfer)
PARSED_ATTR(CPUDispatch)
PARSED_ATTR(CPUSpecific)
PARSED_ATTR(CUDAConstant)
PARSED_ATTR(CUDADevice)
PARSED_ATTR(CUDADeviceBuiltinSurfaceType)
PARSED_ATTR(CUDADeviceBuiltinTextureType)
PARSED_ATTR(CUDAGlobal)
PARSED_ATTR(CUDAHost)
PARSED_ATTR(CUDAInvalidTarget)
PARSED_ATTR(CUDALaunchBounds)
PARSED_ATTR(CUDAShared)
PARSED_ATTR(CXX11NoReturn)
PARSED_ATTR(CallableWhen)
PARSED_ATTR(Callback)
PARSED_ATTR(CalledOnce)
PARSED_ATTR(Capability)
PARSED_ATTR(CarriesDependency)
PARSED_ATTR(Cleanup)
PARSED_ATTR(CmseNSCall)
PARSED_ATTR(CmseNSEntry)
PARSED_ATTR(CodeSeg)
PARSED_ATTR(Cold)
PARSED_ATTR(Common)
PARSED_ATTR(Const)
PARSED_ATTR(ConstInit)
PARSED_ATTR(Constructor)
PARSED_ATTR(Consumable)
PARSED_ATTR(ConsumableAutoCast)
PARSED_ATTR(ConsumableSetOnRead)
PARSED_ATTR(Convergent)
PARSED_ATTR(DLLExport)
PARSED_ATTR(DLLExportStaticLocal)
PARSED_ATTR(DLLImport)
PARSED_ATTR(DLLImportStaticLocal)
PARSED_ATTR(Deprecated)
PARSED_ATTR(Destructor)
PARSED_ATTR(DiagnoseAsBuiltin)
PARSED_ATTR(DiagnoseIf)
PARSED_ATTR(DisableSanitizerInstrumentation)
PARSED_ATTR(DisableTailCalls)
PARSED_ATTR(EmptyBases)
PARSED_ATTR(EnableIf)
PARSED_ATTR(EnforceTCB)
PARSED_ATTR(EnforceTCBLeaf)
PARSED_ATTR(EnumExtensibility)
PARSED_ATTR(Error)
PARSED_ATTR(ExcludeFromExplicitInstantiation)
PARSED_ATTR(ExclusiveTrylockFunction)
PARSED_ATTR(ExtVectorType)
PARSED_ATTR(ExternalSourceSymbol)
PARSED_ATTR(FallThrough)
PARSED_ATTR(FastCall)
PARSED_ATTR(FlagEnum)
PARSED_ATTR(Flatten)
PARSED_ATTR(Format)
PARSED_ATTR(FormatArg)
PARSED_ATTR(FunctionReturnThunks)
PARSED_ATTR(GNUInline)
PARSED_ATTR(GuardedBy)
PARSED_ATTR(GuardedVar)
PARSED_ATTR(HIPManaged)
PARSED_ATTR(HLSLGroupSharedAddressSpace)
PARSED_ATTR(HLSLNumThreads)
PARSED_ATTR(HLSLResource)
PARSED_ATTR(HLSLResourceBinding)
PARSED_ATTR(HLSLSV_DispatchThreadID)
PARSED_ATTR(HLSLSV_GroupIndex)
PARSED_ATTR(HLSLShader)
PARSED_ATTR(Hot)
PARSED_ATTR(IBAction)
PARSED_ATTR(IBOutlet)
PARSED_ATTR(IBOutletCollection)
PARSED_ATTR(IFunc)
PARSED_ATTR(InitPriority)
PARSED_ATTR(IntelOclBicc)
PARSED_ATTR(InternalLinkage)
PARSED_ATTR(LTOVisibilityPublic)
PARSED_ATTR(LayoutVersion)
PARSED_ATTR(Leaf)
PARSED_ATTR(LifetimeBound)
PARSED_ATTR(Likely)
PARSED_ATTR(LoaderUninitialized)
PARSED_ATTR(LockReturned)
PARSED_ATTR(Lockable)
PARSED_ATTR(LocksExcluded)
PARSED_ATTR(LoopHint)
PARSED_ATTR(MIGServerRoutine)
PARSED_ATTR(MSABI)
PARSED_ATTR(MSAllocator)
PARSED_ATTR(MSInheritance)
PARSED_ATTR(MSNoVTable)
PARSED_ATTR(MSStruct)
PARSED_ATTR(MatrixType)
PARSED_ATTR(MayAlias)
PARSED_ATTR(MaybeUndef)
PARSED_ATTR(MicroMips)
PARSED_ATTR(MinSize)
PARSED_ATTR(MinVectorWidth)
PARSED_ATTR(Mips16)
PARSED_ATTR(MipsLongCall)
PARSED_ATTR(MipsShortCall)
PARSED_ATTR(Mode)
PARSED_ATTR(MustTail)
PARSED_ATTR(NSConsumed)
PARSED_ATTR(NSConsumesSelf)
PARSED_ATTR(NSErrorDomain)
PARSED_ATTR(NSReturnsAutoreleased)
PARSED_ATTR(NSReturnsNotRetained)
PARSED_ATTR(NSReturnsRetained)
PARSED_ATTR(NVPTXKernel)
PARSED_ATTR(Naked)
PARSED_ATTR(NeonPolyVectorType)
PARSED_ATTR(NeonVectorType)
PARSED_ATTR(NoAlias)
PARSED_ATTR(NoBuiltin)
PARSED_ATTR(NoCommon)
PARSED_ATTR(NoDebug)
PARSED_ATTR(NoDeref)
PARSED_ATTR(NoDestroy)
PARSED_ATTR(NoDuplicate)
PARSED_ATTR(NoEscape)
PARSED_ATTR(NoInline)
PARSED_ATTR(NoInstrumentFunction)
PARSED_ATTR(NoMerge)
PARSED_ATTR(NoMicroMips)
PARSED_ATTR(NoMips16)
PARSED_ATTR(NoProfileFunction)
PARSED_ATTR(NoRandomizeLayout)
PARSED_ATTR(NoReturn)
PARSED_ATTR(NoSanitize)
PARSED_ATTR(NoSanitizeSpecific)
PARSED_ATTR(NoSpeculativeLoadHardening)
PARSED_ATTR(NoSplitStack)
PARSED_ATTR(NoStackProtector)
PARSED_ATTR(NoThreadSafetyAnalysis)
PARSED_ATTR(NoThrow)
PARSED_ATTR(NoUniqueAddress)
PARSED_ATTR(NoUwtable)
PARSED_ATTR(NonNull)
PARSED_ATTR(NotTailCalled)
PARSED_ATTR(OSConsumed)
PARSED_ATTR(OSConsumesThis)
PARSED_ATTR(OSReturnsNotRetained)
PARSED_ATTR(OSReturnsRetained)
PARSED_ATTR(OSReturnsRetainedOnNonZero)
PARSED_ATTR(OSReturnsRetainedOnZero)
PARSED_ATTR(ObjCBoxable)
PARSED_ATTR(ObjCBridge)
PARSED_ATTR(ObjCBridgeMutable)
PARSED_ATTR(ObjCBridgeRelated)
PARSED_ATTR(ObjCClassStub)
PARSED_ATTR(ObjCDesignatedInitializer)
PARSED_ATTR(ObjCDirect)
PARSED_ATTR(ObjCDirectMembers)
PARSED_ATTR(ObjCException)
PARSED_ATTR(ObjCExplicitProtocolImpl)
PARSED_ATTR(ObjCExternallyRetained)
PARSED_ATTR(ObjCGC)
PARSED_ATTR(ObjCIndependentClass)
PARSED_ATTR(ObjCInertUnsafeUnretained)
PARSED_ATTR(ObjCKindOf)
PARSED_ATTR(ObjCMethodFamily)
PARSED_ATTR(ObjCNSObject)
PARSED_ATTR(ObjCNonLazyClass)
PARSED_ATTR(ObjCNonRuntimeProtocol)
PARSED_ATTR(ObjCOwnership)
PARSED_ATTR(ObjCPreciseLifetime)
PARSED_ATTR(ObjCRequiresPropertyDefs)
PARSED_ATTR(ObjCRequiresSuper)
PARSED_ATTR(ObjCReturnsInnerPointer)
PARSED_ATTR(ObjCRootClass)
PARSED_ATTR(ObjCRuntimeName)
PARSED_ATTR(ObjCRuntimeVisible)
PARSED_ATTR(ObjCSubclassingRestricted)
PARSED_ATTR(OpenCLAccess)
PARSED_ATTR(OpenCLConstantAddressSpace)
PARSED_ATTR(OpenCLGenericAddressSpace)
PARSED_ATTR(OpenCLGlobalAddressSpace)
PARSED_ATTR(OpenCLGlobalDeviceAddressSpace)
PARSED_ATTR(OpenCLGlobalHostAddressSpace)
PARSED_ATTR(OpenCLIntelReqdSubGroupSize)
PARSED_ATTR(OpenCLKernel)
PARSED_ATTR(OpenCLLocalAddressSpace)
PARSED_ATTR(OpenCLNoSVM)
PARSED_ATTR(OpenCLPrivateAddressSpace)
PARSED_ATTR(OpenCLUnrollHint)
PARSED_ATTR(OptimizeNone)
PARSED_ATTR(Overloadable)
PARSED_ATTR(Owner)
PARSED_ATTR(Ownership)
PARSED_ATTR(Packed)
PARSED_ATTR(ParamTypestate)
PARSED_ATTR(Pascal)
PARSED_ATTR(PassObjectSize)
PARSED_ATTR(PatchableFunctionEntry)
PARSED_ATTR(Pcs)
PARSED_ATTR(Pointer)
PARSED_ATTR(PragmaClangBSSSection)
PARSED_ATTR(PragmaClangDataSection)
PARSED_ATTR(PragmaClangRelroSection)
PARSED_ATTR(PragmaClangRodataSection)
PARSED_ATTR(PragmaClangTextSection)
PARSED_ATTR(PreferredName)
PARSED_ATTR(PreserveAll)
PARSED_ATTR(PreserveMost)
PARSED_ATTR(PtGuardedBy)
PARSED_ATTR(PtGuardedVar)
PARSED_ATTR(Ptr32)
PARSED_ATTR(Ptr64)
PARSED_ATTR(Pure)
PARSED_ATTR(RISCVRVVVectorBits)
PARSED_ATTR(RandomizeLayout)
PARSED_ATTR(ReadOnlyPlacement)
PARSED_ATTR(RegCall)
PARSED_ATTR(Regparm)
PARSED_ATTR(Reinitializes)
PARSED_ATTR(ReleaseCapability)
PARSED_ATTR(ReleaseHandle)
PARSED_ATTR(RenderScriptKernel)
PARSED_ATTR(ReqdWorkGroupSize)
PARSED_ATTR(RequiresCapability)
PARSED_ATTR(Restrict)
PARSED_ATTR(Retain)
PARSED_ATTR(ReturnTypestate)
PARSED_ATTR(ReturnsNonNull)
PARSED_ATTR(ReturnsTwice)
PARSED_ATTR(SPtr)
PARSED_ATTR(SYCLKernel)
PARSED_ATTR(SYCLSpecialClass)
PARSED_ATTR(ScopedLockable)
PARSED_ATTR(Section)
PARSED_ATTR(SelectAny)
PARSED_ATTR(Sentinel)
PARSED_ATTR(SetTypestate)
PARSED_ATTR(SharedTrylockFunction)
PARSED_ATTR(SpeculativeLoadHardening)
PARSED_ATTR(StandaloneDebug)
PARSED_ATTR(StdCall)
PARSED_ATTR(StrictFP)
PARSED_ATTR(StrictGuardStackCheck)
PARSED_ATTR(Suppress)
PARSED_ATTR(SwiftAsync)
PARSED_ATTR(SwiftAsyncCall)
PARSED_ATTR(SwiftAsyncContext)
PARSED_ATTR(SwiftAsyncError)
PARSED_ATTR(SwiftAsyncName)
PARSED_ATTR(SwiftAttr)
PARSED_ATTR(SwiftBridge)
PARSED_ATTR(SwiftBridgedTypedef)
PARSED_ATTR(SwiftCall)
PARSED_ATTR(SwiftContext)
PARSED_ATTR(SwiftError)
PARSED_ATTR(SwiftErrorResult)
PARSED_ATTR(SwiftIndirectResult)
PARSED_ATTR(SwiftName)
PARSED_ATTR(SwiftNewType)
PARSED_ATTR(SwiftObjCMembers)
PARSED_ATTR(SwiftPrivate)
PARSED_ATTR(SysVABI)
PARSED_ATTR(TLSModel)
PARSED_ATTR(Target)
PARSED_ATTR(TargetClones)
PARSED_ATTR(TargetVersion)
PARSED_ATTR(TestTypestate)
PARSED_ATTR(ThisCall)
PARSED_ATTR(Thread)
PARSED_ATTR(TransparentUnion)
PARSED_ATTR(TrivialABI)
PARSED_ATTR(TryAcquireCapability)
PARSED_ATTR(TypeNonNull)
PARSED_ATTR(TypeNullUnspecified)
PARSED_ATTR(TypeNullable)
PARSED_ATTR(TypeNullableResult)
PARSED_ATTR(TypeTagForDatatype)
PARSED_ATTR(TypeVisibility)
PARSED_ATTR(UPtr)
PARSED_ATTR(Unavailable)
PARSED_ATTR(Uninitialized)
PARSED_ATTR(Unlikely)
PARSED_ATTR(UnsafeBufferUsage)
PARSED_ATTR(Unused)
PARSED_ATTR(UseHandle)
PARSED_ATTR(Used)
PARSED_ATTR(UsingIfExists)
PARSED_ATTR(Uuid)
PARSED_ATTR(VecReturn)
PARSED_ATTR(VecTypeHint)
PARSED_ATTR(VectorCall)
PARSED_ATTR(VectorSize)
PARSED_ATTR(Visibility)
PARSED_ATTR(WarnUnused)
PARSED_ATTR(WarnUnusedResult)
PARSED_ATTR(Weak)
PARSED_ATTR(WeakImport)
PARSED_ATTR(WeakRef)
PARSED_ATTR(WebAssemblyExportName)
PARSED_ATTR(WebAssemblyFuncref)
PARSED_ATTR(WebAssemblyImportModule)
PARSED_ATTR(WebAssemblyImportName)
PARSED_ATTR(WorkGroupSizeHint)
PARSED_ATTR(X86ForceAlignArgPointer)
PARSED_ATTR(XRayInstrument)
PARSED_ATTR(XRayLogArgs)
PARSED_ATTR(ZeroCallUsedRegs)
