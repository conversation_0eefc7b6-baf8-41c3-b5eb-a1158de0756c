//===-- BuiltinsHexagon.def - Hexagon Builtin function database --*- C++ -*-==//
//
// Part of the LLVM Project, under the Apache License v2.0 with LLVM Exceptions.
// See https://llvm.org/LICENSE.txt for license information.
// SPDX-License-Identifier: Apache-2.0 WITH LLVM-exception
//
//===----------------------------------------------------------------------===//
//
// This file defines the Hexagon-specific builtin function database. Users of
// this file must define the BUILTIN macro to make use of this information.
//
//===----------------------------------------------------------------------===//

// The format of this database matches clang/Basic/Builtins.def.

#if defined(BUILTIN) && !defined(TARGET_BUILTIN)
#   define TARGET_BUILTIN(ID, TYPE, ATTRS, FEATURE) BUILTIN(ID, TYPE, ATTRS)
#endif

#pragma push_macro("V73")
#define V73 "v73"
#pragma push_macro("V71")
#define V71 "v71|" V73
#pragma push_macro("V69")
#define V69 "v69|" V71
#pragma push_macro("V68")
#define V68 "v68|" V69
#pragma push_macro("V67")
#define V67 "v67|" V68
#pragma push_macro("V66")
#define V66 "v66|" V67
#pragma push_macro("V65")
#define V65 "v65|" V66
#pragma push_macro("V62")
#define V62 "v62|" V65
#pragma push_macro("V60")
#define V60 "v60|" V62
#pragma push_macro("V55")
#define V55 "v55|" V60
#pragma push_macro("V5")
#define V5 "v5|" V55

#pragma push_macro("HVXV73")
#define HVXV73 "hvxv73"
#pragma push_macro("HVXV71")
#define HVXV71 "hvxv71|" HVXV73
#pragma push_macro("HVXV69")
#define HVXV69 "hvxv69|" HVXV71
#pragma push_macro("HVXV68")
#define HVXV68 "hvxv68|" HVXV69
#pragma push_macro("HVXV67")
#define HVXV67 "hvxv67|" HVXV68
#pragma push_macro("HVXV66")
#define HVXV66 "hvxv66|" HVXV67
#pragma push_macro("HVXV65")
#define HVXV65 "hvxv65|" HVXV66
#pragma push_macro("HVXV62")
#define HVXV62 "hvxv62|" HVXV65
#pragma push_macro("HVXV60")
#define HVXV60 "hvxv60|" HVXV62


// The builtins below are not autogenerated from iset.py.
// Make sure you do not overwrite these.
TARGET_BUILTIN(__builtin_SI_to_SXTHI_asrh, "ii", "", V5)
TARGET_BUILTIN(__builtin_brev_ldd,   "v*LLi*CLLi*iC", "", V5)
TARGET_BUILTIN(__builtin_brev_ldw,   "v*i*Ci*iC", "", V5)
TARGET_BUILTIN(__builtin_brev_ldh,   "v*s*Cs*iC", "", V5)
TARGET_BUILTIN(__builtin_brev_lduh,  "v*Us*CUs*iC", "", V5)
TARGET_BUILTIN(__builtin_brev_ldb,   "v*Sc*CSc*iC", "", V5)
TARGET_BUILTIN(__builtin_brev_ldub,  "v*Uc*CUc*iC", "", V5)
TARGET_BUILTIN(__builtin_circ_ldd,   "LLi*LLi*LLi*iIi", "", V5)
TARGET_BUILTIN(__builtin_circ_ldw,   "i*i*i*iIi", "", V5)
TARGET_BUILTIN(__builtin_circ_ldh,   "s*s*s*iIi", "", V5)
TARGET_BUILTIN(__builtin_circ_lduh,  "Us*Us*Us*iIi", "", V5)
TARGET_BUILTIN(__builtin_circ_ldb,   "c*c*c*iIi", "", V5)
TARGET_BUILTIN(__builtin_circ_ldub,  "Uc*Uc*Uc*iIi", "", V5)
TARGET_BUILTIN(__builtin_brev_std,   "LLi*CLLi*LLiiC", "", V5)
TARGET_BUILTIN(__builtin_brev_stw,   "i*Ci*iiC", "", V5)
TARGET_BUILTIN(__builtin_brev_sth,   "s*Cs*iiC", "", V5)
TARGET_BUILTIN(__builtin_brev_sthhi, "s*Cs*iiC", "", V5)
TARGET_BUILTIN(__builtin_brev_stb,   "c*Cc*iiC", "", V5)
TARGET_BUILTIN(__builtin_circ_std,   "LLi*LLi*LLiiIi", "", V5)
TARGET_BUILTIN(__builtin_circ_stw,   "i*i*iiIi", "", V5)
TARGET_BUILTIN(__builtin_circ_sth,   "s*s*iiIi", "", V5)
TARGET_BUILTIN(__builtin_circ_sthhi, "s*s*iiIi", "", V5)
TARGET_BUILTIN(__builtin_circ_stb,   "c*c*iiIi", "", V5)
TARGET_BUILTIN(__builtin_HEXAGON_L2_loadrub_pci, "iv*IiivC*", "", V5)
TARGET_BUILTIN(__builtin_HEXAGON_L2_loadrb_pci, "iv*IiivC*", "", V5)
TARGET_BUILTIN(__builtin_HEXAGON_L2_loadruh_pci, "iv*IiivC*", "", V5)
TARGET_BUILTIN(__builtin_HEXAGON_L2_loadrh_pci, "iv*IiivC*", "", V5)
TARGET_BUILTIN(__builtin_HEXAGON_L2_loadri_pci, "iv*IiivC*", "", V5)
TARGET_BUILTIN(__builtin_HEXAGON_L2_loadrd_pci, "LLiv*IiivC*", "", V5)
TARGET_BUILTIN(__builtin_HEXAGON_L2_loadrub_pcr, "iv*ivC*", "", V5)
TARGET_BUILTIN(__builtin_HEXAGON_L2_loadrb_pcr, "iv*ivC*", "", V5)
TARGET_BUILTIN(__builtin_HEXAGON_L2_loadruh_pcr, "iv*ivC*", "", V5)
TARGET_BUILTIN(__builtin_HEXAGON_L2_loadrh_pcr, "iv*ivC*", "", V5)
TARGET_BUILTIN(__builtin_HEXAGON_L2_loadri_pcr, "iv*ivC*", "", V5)
TARGET_BUILTIN(__builtin_HEXAGON_L2_loadrd_pcr, "LLiv*ivC*", "", V5)

TARGET_BUILTIN(__builtin_HEXAGON_S2_storerb_pci, "vv*IiiivC*", "", V5)
TARGET_BUILTIN(__builtin_HEXAGON_S2_storerh_pci, "vv*IiiivC*", "", V5)
TARGET_BUILTIN(__builtin_HEXAGON_S2_storerf_pci, "vv*IiiivC*", "", V5)
TARGET_BUILTIN(__builtin_HEXAGON_S2_storeri_pci, "vv*IiiivC*", "", V5)
TARGET_BUILTIN(__builtin_HEXAGON_S2_storerd_pci, "vv*IiiLLivC*", "", V5)
TARGET_BUILTIN(__builtin_HEXAGON_S2_storerb_pcr, "vv*iivC*", "", V5)
TARGET_BUILTIN(__builtin_HEXAGON_S2_storerh_pcr, "vv*iivC*", "", V5)
TARGET_BUILTIN(__builtin_HEXAGON_S2_storerf_pcr, "vv*iivC*", "", V5)
TARGET_BUILTIN(__builtin_HEXAGON_S2_storeri_pcr, "vv*iivC*", "", V5)
TARGET_BUILTIN(__builtin_HEXAGON_S2_storerd_pcr, "vv*iLLivC*", "", V5)

TARGET_BUILTIN(__builtin_HEXAGON_prefetch,"vv*","", V5)
TARGET_BUILTIN(__builtin_HEXAGON_A6_vminub_RdP,"LLiLLiLLi","", V62)

TARGET_BUILTIN(__builtin_HEXAGON_V6_vmaskedstoreq,"vV64bv*V16i","", HVXV60)
TARGET_BUILTIN(__builtin_HEXAGON_V6_vmaskedstorenq,"vV64bv*V16i","", HVXV60)
TARGET_BUILTIN(__builtin_HEXAGON_V6_vmaskedstorentq,"vV64bv*V16i","", HVXV60)
TARGET_BUILTIN(__builtin_HEXAGON_V6_vmaskedstorentnq,"vV64bv*V16i","", HVXV60)
TARGET_BUILTIN(__builtin_HEXAGON_V6_vmaskedstoreq_128B,"vV128bv*V32i","", HVXV60)
TARGET_BUILTIN(__builtin_HEXAGON_V6_vmaskedstorenq_128B,"vV128bv*V32i","", HVXV60)
TARGET_BUILTIN(__builtin_HEXAGON_V6_vmaskedstorentq_128B,"vV128bv*V32i","", HVXV60)
TARGET_BUILTIN(__builtin_HEXAGON_V6_vmaskedstorentnq_128B,"vV128bv*V32i","", HVXV60)


// These are only valid on v65
TARGET_BUILTIN(__builtin_HEXAGON_V6_vrmpybub_rtt,"V32iV16iLLi","", "hvxv65")
TARGET_BUILTIN(__builtin_HEXAGON_V6_vrmpybub_rtt_128B,"V64iV32iLLi","", "hvxv65")
TARGET_BUILTIN(__builtin_HEXAGON_V6_vrmpybub_rtt_acc,"V32iV32iV16iLLi","", "hvxv65")
TARGET_BUILTIN(__builtin_HEXAGON_V6_vrmpybub_rtt_acc_128B,"V64iV64iV32iLLi","", "hvxv65")
TARGET_BUILTIN(__builtin_HEXAGON_V6_vrmpyub_rtt,"V32iV16iLLi","", "hvxv65")
TARGET_BUILTIN(__builtin_HEXAGON_V6_vrmpyub_rtt_128B,"V64iV32iLLi","", "hvxv65")
TARGET_BUILTIN(__builtin_HEXAGON_V6_vrmpyub_rtt_acc,"V32iV32iV16iLLi","", "hvxv65")
TARGET_BUILTIN(__builtin_HEXAGON_V6_vrmpyub_rtt_acc_128B,"V64iV64iV32iLLi","", "hvxv65")

#include "clang/Basic/BuiltinsHexagonDep.def"

#pragma pop_macro("HVXV60")
#pragma pop_macro("HVXV62")
#pragma pop_macro("HVXV65")
#pragma pop_macro("HVXV66")
#pragma pop_macro("HVXV67")
#pragma pop_macro("HVXV68")
#pragma pop_macro("HVXV69")
#pragma pop_macro("HVXV71")
#pragma pop_macro("HVXV73")

#pragma pop_macro("V5")
#pragma pop_macro("V55")
#pragma pop_macro("V60")
#pragma pop_macro("V62")
#pragma pop_macro("V65")
#pragma pop_macro("V66")
#pragma pop_macro("V67")
#pragma pop_macro("V68")
#pragma pop_macro("V69")
#pragma pop_macro("V71")
#pragma pop_macro("V73")

#undef BUILTIN
#undef TARGET_BUILTIN

