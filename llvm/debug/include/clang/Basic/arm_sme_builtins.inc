#ifdef GET_SME_BUILTINS
TARGET_BUILTIN(__builtin_sme_svaddha_za32_s32_m, "vIUWiq16bq16bq4i", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svaddha_za32_u32_m, "vIUWiq16bq16bq4Ui", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svaddha_za64_s64_m, "vIUWiq16bq16bq2Wi", "n", "sme-i16i64")
TARGET_BUILTIN(__builtin_sme_svaddha_za64_u64_m, "vIUWiq16bq16bq2UWi", "n", "sme-i16i64")
TARGET_BUILTIN(__builtin_sme_svaddva_za32_s32_m, "vIUWiq16bq16bq4i", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svaddva_za32_u32_m, "vIUWiq16bq16bq4Ui", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svaddva_za64_s64_m, "vIUWiq16bq16bq2Wi", "n", "sme-i16i64")
TARGET_BUILTIN(__builtin_sme_svaddva_za64_u64_m, "vIUWiq16bq16bq2UWi", "n", "sme-i16i64")
TARGET_BUILTIN(__builtin_sme_svcntsb, "UWiv", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svcntsd, "UWiv", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svcntsh, "UWiv", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svcntsw, "UWiv", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svld1_hor_vnum_za128, "vIUWiUiIUWiq16bvC*Wi", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svld1_hor_vnum_za16, "vIUWiUiIUWiq16bvC*Wi", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svld1_hor_vnum_za32, "vIUWiUiIUWiq16bvC*Wi", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svld1_hor_vnum_za64, "vIUWiUiIUWiq16bvC*Wi", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svld1_hor_vnum_za8, "vIUWiUiIUWiq16bvC*Wi", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svld1_hor_za128, "vIUWiUiIUWiq16bvC*", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svld1_hor_za16, "vIUWiUiIUWiq16bvC*", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svld1_hor_za32, "vIUWiUiIUWiq16bvC*", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svld1_hor_za64, "vIUWiUiIUWiq16bvC*", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svld1_hor_za8, "vIUWiUiIUWiq16bvC*", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svld1_ver_vnum_za128, "vIUWiUiIUWiq16bvC*Wi", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svld1_ver_vnum_za16, "vIUWiUiIUWiq16bvC*Wi", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svld1_ver_vnum_za32, "vIUWiUiIUWiq16bvC*Wi", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svld1_ver_vnum_za64, "vIUWiUiIUWiq16bvC*Wi", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svld1_ver_vnum_za8, "vIUWiUiIUWiq16bvC*Wi", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svld1_ver_za128, "vIUWiUiIUWiq16bvC*", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svld1_ver_za16, "vIUWiUiIUWiq16bvC*", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svld1_ver_za32, "vIUWiUiIUWiq16bvC*", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svld1_ver_za64, "vIUWiUiIUWiq16bvC*", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svld1_ver_za8, "vIUWiUiIUWiq16bvC*", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svldr_vnum_za, "vUiIUWivC*", "n", "sve")
TARGET_BUILTIN(__builtin_sme_svmopa_za32_bf16_m, "vIUWiq16bq16bq8yq8y", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svmopa_za32_f16_m, "vIUWiq16bq16bq8hq8h", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svmopa_za32_f32_m, "vIUWiq16bq16bq4fq4f", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svmopa_za32_s8_m, "vIUWiq16bq16bq16Scq16Sc", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svmopa_za32_u8_m, "vIUWiq16bq16bq16Ucq16Uc", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svmopa_za64_f64_m, "vIUWiq16bq16bq2dq2d", "n", "sme-f64f64")
TARGET_BUILTIN(__builtin_sme_svmopa_za64_s16_m, "vIUWiq16bq16bq8sq8s", "n", "sme-i16i64")
TARGET_BUILTIN(__builtin_sme_svmopa_za64_u16_m, "vIUWiq16bq16bq8Usq8Us", "n", "sme-i16i64")
TARGET_BUILTIN(__builtin_sme_svmops_za32_bf16_m, "vIUWiq16bq16bq8yq8y", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svmops_za32_f16_m, "vIUWiq16bq16bq8hq8h", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svmops_za32_f32_m, "vIUWiq16bq16bq4fq4f", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svmops_za32_s8_m, "vIUWiq16bq16bq16Scq16Sc", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svmops_za32_u8_m, "vIUWiq16bq16bq16Ucq16Uc", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svmops_za64_f64_m, "vIUWiq16bq16bq2dq2d", "n", "sme-f64f64")
TARGET_BUILTIN(__builtin_sme_svmops_za64_s16_m, "vIUWiq16bq16bq8sq8s", "n", "sme-i16i64")
TARGET_BUILTIN(__builtin_sme_svmops_za64_u16_m, "vIUWiq16bq16bq8Usq8Us", "n", "sme-i16i64")
TARGET_BUILTIN(__builtin_sme_svread_hor_za128_bf16_m, "q8yq8yq16bIUWiUiIUWi", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svread_hor_za128_f16_m, "q8hq8hq16bIUWiUiIUWi", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svread_hor_za128_f32_m, "q4fq4fq16bIUWiUiIUWi", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svread_hor_za128_f64_m, "q2dq2dq16bIUWiUiIUWi", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svread_hor_za128_s16_m, "q8sq8sq16bIUWiUiIUWi", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svread_hor_za128_s32_m, "q4iq4iq16bIUWiUiIUWi", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svread_hor_za128_s64_m, "q2Wiq2Wiq16bIUWiUiIUWi", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svread_hor_za128_s8_m, "q16Scq16Scq16bIUWiUiIUWi", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svread_hor_za128_u16_m, "q8Usq8Usq16bIUWiUiIUWi", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svread_hor_za128_u32_m, "q4Uiq4Uiq16bIUWiUiIUWi", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svread_hor_za128_u64_m, "q2UWiq2UWiq16bIUWiUiIUWi", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svread_hor_za128_u8_m, "q16Ucq16Ucq16bIUWiUiIUWi", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svread_hor_za16_bf16_m, "q8yq8yq16bIUWiUiIUWi", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svread_hor_za16_f16_m, "q8hq8hq16bIUWiUiIUWi", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svread_hor_za16_s16_m, "q8sq8sq16bIUWiUiIUWi", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svread_hor_za16_u16_m, "q8Usq8Usq16bIUWiUiIUWi", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svread_hor_za32_f32_m, "q4fq4fq16bIUWiUiIUWi", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svread_hor_za32_s32_m, "q4iq4iq16bIUWiUiIUWi", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svread_hor_za32_u32_m, "q4Uiq4Uiq16bIUWiUiIUWi", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svread_hor_za64_f64_m, "q2dq2dq16bIUWiUiIUWi", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svread_hor_za64_s64_m, "q2Wiq2Wiq16bIUWiUiIUWi", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svread_hor_za64_u64_m, "q2UWiq2UWiq16bIUWiUiIUWi", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svread_hor_za8_s8_m, "q16Scq16Scq16bIUWiUiIUWi", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svread_hor_za8_u8_m, "q16Ucq16Ucq16bIUWiUiIUWi", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svread_ver_za128_bf16_m, "q8yq8yq16bIUWiUiIUWi", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svread_ver_za128_f16_m, "q8hq8hq16bIUWiUiIUWi", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svread_ver_za128_f32_m, "q4fq4fq16bIUWiUiIUWi", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svread_ver_za128_f64_m, "q2dq2dq16bIUWiUiIUWi", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svread_ver_za128_s16_m, "q8sq8sq16bIUWiUiIUWi", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svread_ver_za128_s32_m, "q4iq4iq16bIUWiUiIUWi", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svread_ver_za128_s64_m, "q2Wiq2Wiq16bIUWiUiIUWi", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svread_ver_za128_s8_m, "q16Scq16Scq16bIUWiUiIUWi", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svread_ver_za128_u16_m, "q8Usq8Usq16bIUWiUiIUWi", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svread_ver_za128_u32_m, "q4Uiq4Uiq16bIUWiUiIUWi", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svread_ver_za128_u64_m, "q2UWiq2UWiq16bIUWiUiIUWi", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svread_ver_za128_u8_m, "q16Ucq16Ucq16bIUWiUiIUWi", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svread_ver_za16_bf16_m, "q8yq8yq16bIUWiUiIUWi", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svread_ver_za16_f16_m, "q8hq8hq16bIUWiUiIUWi", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svread_ver_za16_s16_m, "q8sq8sq16bIUWiUiIUWi", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svread_ver_za16_u16_m, "q8Usq8Usq16bIUWiUiIUWi", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svread_ver_za32_f32_m, "q4fq4fq16bIUWiUiIUWi", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svread_ver_za32_s32_m, "q4iq4iq16bIUWiUiIUWi", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svread_ver_za32_u32_m, "q4Uiq4Uiq16bIUWiUiIUWi", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svread_ver_za64_f64_m, "q2dq2dq16bIUWiUiIUWi", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svread_ver_za64_s64_m, "q2Wiq2Wiq16bIUWiUiIUWi", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svread_ver_za64_u64_m, "q2UWiq2UWiq16bIUWiUiIUWi", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svread_ver_za8_s8_m, "q16Scq16Scq16bIUWiUiIUWi", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svread_ver_za8_u8_m, "q16Ucq16Ucq16bIUWiUiIUWi", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svst1_hor_vnum_za128, "vIUWiUiIUWiq16bv*Wi", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svst1_hor_vnum_za16, "vIUWiUiIUWiq16bv*Wi", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svst1_hor_vnum_za32, "vIUWiUiIUWiq16bv*Wi", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svst1_hor_vnum_za64, "vIUWiUiIUWiq16bv*Wi", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svst1_hor_vnum_za8, "vIUWiUiIUWiq16bv*Wi", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svst1_hor_za128, "vIUWiUiIUWiq16bv*", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svst1_hor_za16, "vIUWiUiIUWiq16bv*", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svst1_hor_za32, "vIUWiUiIUWiq16bv*", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svst1_hor_za64, "vIUWiUiIUWiq16bv*", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svst1_hor_za8, "vIUWiUiIUWiq16bv*", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svst1_ver_vnum_za128, "vIUWiUiIUWiq16bv*Wi", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svst1_ver_vnum_za16, "vIUWiUiIUWiq16bv*Wi", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svst1_ver_vnum_za32, "vIUWiUiIUWiq16bv*Wi", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svst1_ver_vnum_za64, "vIUWiUiIUWiq16bv*Wi", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svst1_ver_vnum_za8, "vIUWiUiIUWiq16bv*Wi", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svst1_ver_za128, "vIUWiUiIUWiq16bv*", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svst1_ver_za16, "vIUWiUiIUWiq16bv*", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svst1_ver_za32, "vIUWiUiIUWiq16bv*", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svst1_ver_za64, "vIUWiUiIUWiq16bv*", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svst1_ver_za8, "vIUWiUiIUWiq16bv*", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svstr_vnum_za, "vUiIUWiv*", "n", "sve")
TARGET_BUILTIN(__builtin_sme_svsumopa_za32_s8_m, "vIUWiq16bq16bq16Scq16Uc", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svsumopa_za64_s16_m, "vIUWiq16bq16bq8sq8Us", "n", "sme-i16i64")
TARGET_BUILTIN(__builtin_sme_svsumops_za32_s8_m, "vIUWiq16bq16bq16Scq16Uc", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svsumops_za64_s16_m, "vIUWiq16bq16bq8sq8Us", "n", "sme-i16i64")
TARGET_BUILTIN(__builtin_sme_svusmopa_za32_u8_m, "vIUWiq16bq16bq16Ucq16Sc", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svusmopa_za64_u16_m, "vIUWiq16bq16bq8Usq8s", "n", "sme-i16i64")
TARGET_BUILTIN(__builtin_sme_svusmops_za32_u8_m, "vIUWiq16bq16bq16Ucq16Sc", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svusmops_za64_u16_m, "vIUWiq16bq16bq8Usq8s", "n", "sme-i16i64")
TARGET_BUILTIN(__builtin_sme_svwrite_hor_za128_bf16_m, "vIUWiUiIUWiq16bq8y", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svwrite_hor_za128_f16_m, "vIUWiUiIUWiq16bq8h", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svwrite_hor_za128_f32_m, "vIUWiUiIUWiq16bq4f", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svwrite_hor_za128_f64_m, "vIUWiUiIUWiq16bq2d", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svwrite_hor_za128_s16_m, "vIUWiUiIUWiq16bq8s", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svwrite_hor_za128_s32_m, "vIUWiUiIUWiq16bq4i", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svwrite_hor_za128_s64_m, "vIUWiUiIUWiq16bq2Wi", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svwrite_hor_za128_s8_m, "vIUWiUiIUWiq16bq16Sc", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svwrite_hor_za128_u16_m, "vIUWiUiIUWiq16bq8Us", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svwrite_hor_za128_u32_m, "vIUWiUiIUWiq16bq4Ui", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svwrite_hor_za128_u64_m, "vIUWiUiIUWiq16bq2UWi", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svwrite_hor_za128_u8_m, "vIUWiUiIUWiq16bq16Uc", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svwrite_hor_za16_bf16_m, "vIUWiUiIUWiq16bq8y", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svwrite_hor_za16_f16_m, "vIUWiUiIUWiq16bq8h", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svwrite_hor_za16_s16_m, "vIUWiUiIUWiq16bq8s", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svwrite_hor_za16_u16_m, "vIUWiUiIUWiq16bq8Us", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svwrite_hor_za32_f32_m, "vIUWiUiIUWiq16bq4f", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svwrite_hor_za32_s32_m, "vIUWiUiIUWiq16bq4i", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svwrite_hor_za32_u32_m, "vIUWiUiIUWiq16bq4Ui", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svwrite_hor_za64_f64_m, "vIUWiUiIUWiq16bq2d", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svwrite_hor_za64_s64_m, "vIUWiUiIUWiq16bq2Wi", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svwrite_hor_za64_u64_m, "vIUWiUiIUWiq16bq2UWi", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svwrite_hor_za8_s8_m, "vIUWiUiIUWiq16bq16Sc", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svwrite_hor_za8_u8_m, "vIUWiUiIUWiq16bq16Uc", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svwrite_ver_za128_bf16_m, "vIUWiUiIUWiq16bq8y", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svwrite_ver_za128_f16_m, "vIUWiUiIUWiq16bq8h", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svwrite_ver_za128_f32_m, "vIUWiUiIUWiq16bq4f", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svwrite_ver_za128_f64_m, "vIUWiUiIUWiq16bq2d", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svwrite_ver_za128_s16_m, "vIUWiUiIUWiq16bq8s", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svwrite_ver_za128_s32_m, "vIUWiUiIUWiq16bq4i", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svwrite_ver_za128_s64_m, "vIUWiUiIUWiq16bq2Wi", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svwrite_ver_za128_s8_m, "vIUWiUiIUWiq16bq16Sc", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svwrite_ver_za128_u16_m, "vIUWiUiIUWiq16bq8Us", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svwrite_ver_za128_u32_m, "vIUWiUiIUWiq16bq4Ui", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svwrite_ver_za128_u64_m, "vIUWiUiIUWiq16bq2UWi", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svwrite_ver_za128_u8_m, "vIUWiUiIUWiq16bq16Uc", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svwrite_ver_za16_bf16_m, "vIUWiUiIUWiq16bq8y", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svwrite_ver_za16_f16_m, "vIUWiUiIUWiq16bq8h", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svwrite_ver_za16_s16_m, "vIUWiUiIUWiq16bq8s", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svwrite_ver_za16_u16_m, "vIUWiUiIUWiq16bq8Us", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svwrite_ver_za32_f32_m, "vIUWiUiIUWiq16bq4f", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svwrite_ver_za32_s32_m, "vIUWiUiIUWiq16bq4i", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svwrite_ver_za32_u32_m, "vIUWiUiIUWiq16bq4Ui", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svwrite_ver_za64_f64_m, "vIUWiUiIUWiq16bq2d", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svwrite_ver_za64_s64_m, "vIUWiUiIUWiq16bq2Wi", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svwrite_ver_za64_u64_m, "vIUWiUiIUWiq16bq2UWi", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svwrite_ver_za8_s8_m, "vIUWiUiIUWiq16bq16Sc", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svwrite_ver_za8_u8_m, "vIUWiUiIUWiq16bq16Uc", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svzero_mask_za, "vIUWi", "n", "sme")
TARGET_BUILTIN(__builtin_sme_svzero_za, "v", "n", "sme")
#endif

