#if defined(TARGET_BUILTIN) && !defined(RISCVV_BUILTIN)
#define RISCVV_BUILTIN(ID, TYPE, ATTRS) TARGET_BUILTIN(ID, TYPE, ATTRS, "zve32x")
#endif
RISCVV_BUILTIN(__builtin_rvv_sf_vc_v_x,"", "n")
RISCVV_BUILTIN(__builtin_rvv_sf_vc_xv_se,"", "n")
RISCVV_BUILTIN(__builtin_rvv_sf_vc_x_se_u8mf8,"", "n")
RISCVV_BUILTIN(__builtin_rvv_sf_vc_x_se_u8mf4,"", "n")
RISCVV_BUILTIN(__builtin_rvv_sf_vc_x_se_u8mf2,"", "n")
RISCVV_BUILTIN(__builtin_rvv_sf_vc_x_se_u8m1,"", "n")
RISCVV_BUILTIN(__builtin_rvv_sf_vc_x_se_u8m2,"", "n")
RISCVV_BUILTIN(__builtin_rvv_sf_vc_x_se_u8m4,"", "n")
RISCVV_BUILTIN(__builtin_rvv_sf_vc_x_se_u8m8,"", "n")
RISCVV_BUILTIN(__builtin_rvv_sf_vc_x_se_u16mf4,"", "n")
RISCVV_BUILTIN(__builtin_rvv_sf_vc_x_se_u16mf2,"", "n")
RISCVV_BUILTIN(__builtin_rvv_sf_vc_x_se_u16m1,"", "n")
RISCVV_BUILTIN(__builtin_rvv_sf_vc_x_se_u16m2,"", "n")
RISCVV_BUILTIN(__builtin_rvv_sf_vc_x_se_u16m4,"", "n")
RISCVV_BUILTIN(__builtin_rvv_sf_vc_x_se_u16m8,"", "n")
RISCVV_BUILTIN(__builtin_rvv_sf_vc_x_se_u32mf2,"", "n")
RISCVV_BUILTIN(__builtin_rvv_sf_vc_x_se_u32m1,"", "n")
RISCVV_BUILTIN(__builtin_rvv_sf_vc_x_se_u32m2,"", "n")
RISCVV_BUILTIN(__builtin_rvv_sf_vc_x_se_u32m4,"", "n")
RISCVV_BUILTIN(__builtin_rvv_sf_vc_x_se_u32m8,"", "n")
RISCVV_BUILTIN(__builtin_rvv_sf_vc_x_se_u64m1,"", "n")
RISCVV_BUILTIN(__builtin_rvv_sf_vc_x_se_u64m2,"", "n")
RISCVV_BUILTIN(__builtin_rvv_sf_vc_x_se_u64m4,"", "n")
RISCVV_BUILTIN(__builtin_rvv_sf_vc_x_se_u64m8,"", "n")
RISCVV_BUILTIN(__builtin_rvv_sf_vc_i_se_u8mf8,"", "n")
RISCVV_BUILTIN(__builtin_rvv_sf_vc_i_se_u8mf4,"", "n")
RISCVV_BUILTIN(__builtin_rvv_sf_vc_i_se_u8mf2,"", "n")
RISCVV_BUILTIN(__builtin_rvv_sf_vc_i_se_u8m1,"", "n")
RISCVV_BUILTIN(__builtin_rvv_sf_vc_i_se_u8m2,"", "n")
RISCVV_BUILTIN(__builtin_rvv_sf_vc_i_se_u8m4,"", "n")
RISCVV_BUILTIN(__builtin_rvv_sf_vc_i_se_u8m8,"", "n")
RISCVV_BUILTIN(__builtin_rvv_sf_vc_i_se_u16mf4,"", "n")
RISCVV_BUILTIN(__builtin_rvv_sf_vc_i_se_u16mf2,"", "n")
RISCVV_BUILTIN(__builtin_rvv_sf_vc_i_se_u16m1,"", "n")
RISCVV_BUILTIN(__builtin_rvv_sf_vc_i_se_u16m2,"", "n")
RISCVV_BUILTIN(__builtin_rvv_sf_vc_i_se_u16m4,"", "n")
RISCVV_BUILTIN(__builtin_rvv_sf_vc_i_se_u16m8,"", "n")
RISCVV_BUILTIN(__builtin_rvv_sf_vc_i_se_u32mf2,"", "n")
RISCVV_BUILTIN(__builtin_rvv_sf_vc_i_se_u32m1,"", "n")
RISCVV_BUILTIN(__builtin_rvv_sf_vc_i_se_u32m2,"", "n")
RISCVV_BUILTIN(__builtin_rvv_sf_vc_i_se_u32m4,"", "n")
RISCVV_BUILTIN(__builtin_rvv_sf_vc_i_se_u32m8,"", "n")
RISCVV_BUILTIN(__builtin_rvv_sf_vc_i_se_u64m1,"", "n")
RISCVV_BUILTIN(__builtin_rvv_sf_vc_i_se_u64m2,"", "n")
RISCVV_BUILTIN(__builtin_rvv_sf_vc_i_se_u64m4,"", "n")
RISCVV_BUILTIN(__builtin_rvv_sf_vc_i_se_u64m8,"", "n")
RISCVV_BUILTIN(__builtin_rvv_sf_vc_iv_se,"", "n")
RISCVV_BUILTIN(__builtin_rvv_sf_vc_vv_se,"", "n")
RISCVV_BUILTIN(__builtin_rvv_sf_vc_fv_se,"", "n")
RISCVV_BUILTIN(__builtin_rvv_sf_vc_xvv_se,"", "n")
RISCVV_BUILTIN(__builtin_rvv_sf_vc_ivv_se,"", "n")
RISCVV_BUILTIN(__builtin_rvv_sf_vc_vvv_se,"", "n")
RISCVV_BUILTIN(__builtin_rvv_sf_vc_fvv_se,"", "n")
RISCVV_BUILTIN(__builtin_rvv_sf_vc_v_x_se,"", "n")
RISCVV_BUILTIN(__builtin_rvv_sf_vc_v_i,"", "n")
RISCVV_BUILTIN(__builtin_rvv_sf_vc_v_i_se,"", "n")
RISCVV_BUILTIN(__builtin_rvv_sf_vc_v_xv,"", "n")
RISCVV_BUILTIN(__builtin_rvv_sf_vc_v_xv_se,"", "n")
RISCVV_BUILTIN(__builtin_rvv_sf_vc_v_iv,"", "n")
RISCVV_BUILTIN(__builtin_rvv_sf_vc_v_iv_se,"", "n")
RISCVV_BUILTIN(__builtin_rvv_sf_vc_v_vv,"", "n")
RISCVV_BUILTIN(__builtin_rvv_sf_vc_v_vv_se,"", "n")
RISCVV_BUILTIN(__builtin_rvv_sf_vc_v_fv,"", "n")
RISCVV_BUILTIN(__builtin_rvv_sf_vc_v_fv_se,"", "n")
RISCVV_BUILTIN(__builtin_rvv_sf_vc_v_xvv,"", "n")
RISCVV_BUILTIN(__builtin_rvv_sf_vc_v_xvv_se,"", "n")
RISCVV_BUILTIN(__builtin_rvv_sf_vc_v_ivv,"", "n")
RISCVV_BUILTIN(__builtin_rvv_sf_vc_v_ivv_se,"", "n")
RISCVV_BUILTIN(__builtin_rvv_sf_vc_v_vvv,"", "n")
RISCVV_BUILTIN(__builtin_rvv_sf_vc_v_vvv_se,"", "n")
RISCVV_BUILTIN(__builtin_rvv_sf_vc_v_fvv,"", "n")
RISCVV_BUILTIN(__builtin_rvv_sf_vc_v_fvv_se,"", "n")
RISCVV_BUILTIN(__builtin_rvv_sf_vc_xvw_se,"", "n")
RISCVV_BUILTIN(__builtin_rvv_sf_vc_ivw_se,"", "n")
RISCVV_BUILTIN(__builtin_rvv_sf_vc_vvw_se,"", "n")
RISCVV_BUILTIN(__builtin_rvv_sf_vc_fvw_se,"", "n")
RISCVV_BUILTIN(__builtin_rvv_sf_vc_v_xvw,"", "n")
RISCVV_BUILTIN(__builtin_rvv_sf_vc_v_xvw_se,"", "n")
RISCVV_BUILTIN(__builtin_rvv_sf_vc_v_ivw,"", "n")
RISCVV_BUILTIN(__builtin_rvv_sf_vc_v_ivw_se,"", "n")
RISCVV_BUILTIN(__builtin_rvv_sf_vc_v_vvw,"", "n")
RISCVV_BUILTIN(__builtin_rvv_sf_vc_v_vvw_se,"", "n")
RISCVV_BUILTIN(__builtin_rvv_sf_vc_v_fvw,"", "n")
RISCVV_BUILTIN(__builtin_rvv_sf_vc_v_fvw_se,"", "n")
#undef RISCVV_BUILTIN
