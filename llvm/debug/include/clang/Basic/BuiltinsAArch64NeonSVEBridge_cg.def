#ifdef GET_SVE_LLVM_INTRINSIC_MAP
SVEMAP2(svget_neonq_s8, SVETypeFlags::EltTyInt8),
SVEMAP2(svget_neonq_s16, SVETypeFlags::EltTyInt16),
SVEMAP2(svget_neonq_s32, SVETypeFlags::EltTyInt32),
SVEMAP2(svget_neonq_s64, SVETypeFlags::EltTyInt64),
SVEMAP2(svget_neonq_u8, SVETypeFlags::EltTyInt8),
SVEMAP2(svget_neonq_u16, SVETypeFlags::EltTyInt16),
SVEMAP2(svget_neonq_u32, SVETypeFlags::EltTyInt32),
SVEMAP2(svget_neonq_u64, SVETypeFlags::EltTyInt64),
SVEMAP2(svget_neonq_f16, SVETypeFlags::EltTyFloat16),
SVEMAP2(svget_neonq_f32, SVETypeFlags::EltTyFloat32),
SVEMAP2(svget_neonq_f64, SVETypeFlags::EltTyFloat64),
SVEMAP2(svget_neonq_bf16, SVETypeFlags::EltTyBFloat16),
SVEMAP2(svset_neonq_s8, SVETypeFlags::EltTyInt8),
SVEMAP2(svset_neonq_s16, SVETypeFlags::EltTyInt16),
SVEMAP2(svset_neonq_s32, SVETypeFlags::EltTyInt32),
SVEMAP2(svset_neonq_s64, SVETypeFlags::EltTyInt64),
SVEMAP2(svset_neonq_u8, SVETypeFlags::EltTyInt8),
SVEMAP2(svset_neonq_u16, SVETypeFlags::EltTyInt16),
SVEMAP2(svset_neonq_u32, SVETypeFlags::EltTyInt32),
SVEMAP2(svset_neonq_u64, SVETypeFlags::EltTyInt64),
SVEMAP2(svset_neonq_f16, SVETypeFlags::EltTyFloat16),
SVEMAP2(svset_neonq_f32, SVETypeFlags::EltTyFloat32),
SVEMAP2(svset_neonq_f64, SVETypeFlags::EltTyFloat64),
SVEMAP2(svset_neonq_bf16, SVETypeFlags::EltTyBFloat16),
SVEMAP2(svdup_neonq_s8, SVETypeFlags::EltTyInt8),
SVEMAP2(svdup_neonq_s16, SVETypeFlags::EltTyInt16),
SVEMAP2(svdup_neonq_s32, SVETypeFlags::EltTyInt32),
SVEMAP2(svdup_neonq_s64, SVETypeFlags::EltTyInt64),
SVEMAP2(svdup_neonq_u8, SVETypeFlags::EltTyInt8),
SVEMAP2(svdup_neonq_u16, SVETypeFlags::EltTyInt16),
SVEMAP2(svdup_neonq_u32, SVETypeFlags::EltTyInt32),
SVEMAP2(svdup_neonq_u64, SVETypeFlags::EltTyInt64),
SVEMAP2(svdup_neonq_f16, SVETypeFlags::EltTyFloat16),
SVEMAP2(svdup_neonq_f32, SVETypeFlags::EltTyFloat32),
SVEMAP2(svdup_neonq_f64, SVETypeFlags::EltTyFloat64),
SVEMAP2(svdup_neonq_bf16, SVETypeFlags::EltTyBFloat16),
#endif

