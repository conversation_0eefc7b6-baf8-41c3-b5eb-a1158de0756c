#ifdef FRONTENDSTART
__FRONTENDSTART = DIAG_START_FRONTEND,
#undef FRONTENDSTART
#endif

DIAG(err_alias_to_undefined, CLASS_ERROR, (unsigned)diag::Severity::Error, "%select{alias|ifunc}0 must point to a defined %select{variable or |}1function", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_avx_calling_convention, CLASS_ERROR, (unsigned)diag::Severity::Error, "AVX vector %select{return|argument}0 of type %1 without '%2' enabled changes the ABI", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_builtin_needs_feature, CLASS_ERROR, (unsigned)diag::Severity::Error, "%0 needs target feature %1", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_cyclic_alias, CLASS_ERROR, (unsigned)diag::Severity::Error, "%select{alias|ifunc}0 definition is part of a cycle", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_duplicate_mangled_name, CLASS_ERROR, (unsigned)diag::Severity::Error, "definition with same mangled name '%0' as another definition", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_extract_api_ignores_file_not_found, CLASS_ERROR, (unsigned)diag::Severity::Fatal, "file '%0' specified by '--extract-api-ignores=' not found", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_fe_action_not_available, CLASS_ERROR, (unsigned)diag::Severity::Error, "action %0 not compiled in", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_fe_backend_error_attr, CLASS_ERROR, (unsigned)diag::Severity::Error, "call to '%0' declared with 'error' attribute: %1", 0, SFINAE_SubstitutionFailure, false, true, true, false, 18)
DIAG(err_fe_backend_frame_larger_than, CLASS_ERROR, (unsigned)diag::Severity::Error, "%0", 0, SFINAE_SubstitutionFailure, false, true, true, false, 18)
DIAG(err_fe_backend_plugin, CLASS_ERROR, (unsigned)diag::Severity::Error, "%0", 0, SFINAE_SubstitutionFailure, false, true, true, false, 18)
DIAG(err_fe_backend_resource_limit, CLASS_ERROR, (unsigned)diag::Severity::Error, "%0 (%1) exceeds limit (%2) in '%3'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 18)
DIAG(err_fe_backend_unsupported, CLASS_ERROR, (unsigned)diag::Severity::Error, "%0", 0, SFINAE_SubstitutionFailure, false, true, true, false, 18)
DIAG(err_fe_dependency_file_requires_MT, CLASS_ERROR, (unsigned)diag::Severity::Error, "-dependency-file requires at least one -MT or -MQ option", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_fe_error_backend, CLASS_ERROR, (unsigned)diag::Severity::Fatal, "error in backend: %0", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_fe_error_opening, CLASS_ERROR, (unsigned)diag::Severity::Error, "error opening '%0': %1", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_fe_error_reading, CLASS_ERROR, (unsigned)diag::Severity::Error, "error reading '%0': %1", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_fe_error_reading_stdin, CLASS_ERROR, (unsigned)diag::Severity::Error, "error reading stdin: %0", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_fe_expected_clang_command, CLASS_ERROR, (unsigned)diag::Severity::Error, "expected a clang compiler command", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_fe_expected_compiler_job, CLASS_ERROR, (unsigned)diag::Severity::Error, "unable to handle compilation, expected exactly one compiler job in '%0'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_fe_inline_asm, CLASS_ERROR, (unsigned)diag::Severity::Error, "%0", 0, SFINAE_SubstitutionFailure, false, true, true, false, 12)
DIAG(err_fe_invalid_alignment, CLASS_ERROR, (unsigned)diag::Severity::Error, "invalid value '%1' in '%0'; alignment must be a power of 2", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_fe_invalid_code_complete_file, CLASS_ERROR, (unsigned)diag::Severity::Fatal, "cannot locate code-completion file %0", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_fe_invalid_exception_model, CLASS_ERROR, (unsigned)diag::Severity::Error, "invalid exception model '%select{none|sjlj|seh|dwarf|wasm}0' for target '%1'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_fe_invalid_plugin_name, CLASS_ERROR, (unsigned)diag::Severity::Error, "unable to find plugin '%0'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_fe_invalid_source_date_epoch, CLASS_ERROR, (unsigned)diag::Severity::Error, "environment variable 'SOURCE_DATE_EPOCH' ('%0') must be a non-negative decimal integer <= %1", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_fe_linking_module, CLASS_ERROR, (unsigned)diag::Severity::Fatal, "cannot link module '%0': %1", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_fe_no_pch_in_dir, CLASS_ERROR, (unsigned)diag::Severity::Error, "no suitable precompiled header file found in directory '%0'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_fe_remap_missing_from_file, CLASS_ERROR, (unsigned)diag::Severity::Fatal, "could not remap from missing file '%0'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_fe_remap_missing_to_file, CLASS_ERROR, (unsigned)diag::Severity::Fatal, "could not remap file '%0' to the contents of file '%1'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_fe_source_mgr, CLASS_ERROR, (unsigned)diag::Severity::Error, "%0", 0, SFINAE_SubstitutionFailure, false, true, true, false, 19)
DIAG(err_fe_unable_to_create_target, CLASS_ERROR, (unsigned)diag::Severity::Error, "unable to create target: '%0'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_fe_unable_to_interface_with_target, CLASS_ERROR, (unsigned)diag::Severity::Error, "unable to interface with target machine", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_fe_unable_to_load_basic_block_sections_file, CLASS_ERROR, (unsigned)diag::Severity::Error, "unable to load basic block sections function list: '%0'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_fe_unable_to_load_pch, CLASS_ERROR, (unsigned)diag::Severity::Error, "unable to load PCH file", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_fe_unable_to_load_plugin, CLASS_ERROR, (unsigned)diag::Severity::Error, "unable to load plugin '%0': '%1'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_fe_unable_to_open_output, CLASS_ERROR, (unsigned)diag::Severity::Error, "unable to open output file '%0': '%1'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_function_needs_feature, CLASS_ERROR, (unsigned)diag::Severity::Error, "always_inline function %1 requires target feature '%2', but would be inlined into function %0 that is compiled without support for '%2'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_hidden_visibility_dllexport, CLASS_ERROR, (unsigned)diag::Severity::Error, "hidden visibility cannot be applied to 'dllexport' declaration", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_ifunc_resolver_return, CLASS_ERROR, (unsigned)diag::Severity::Error, "ifunc resolver function must return a pointer", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_incompatible_fp_eval_method_options, CLASS_ERROR, (unsigned)diag::Severity::Error, "option 'ffp-eval-method' cannot be used with option %select{'fapprox-func'|'mreassociate'|'freciprocal'}0", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_invalid_vfs_overlay, CLASS_ERROR, (unsigned)diag::Severity::Fatal, "invalid virtual filesystem overlay file '%0'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_missing_module, CLASS_ERROR, (unsigned)diag::Severity::Fatal, "no module named '%0' declared in module map file '%1'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_missing_module_name, CLASS_ERROR, (unsigned)diag::Severity::Fatal, "no module name provided; specify one with -fmodule-name=", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_missing_vfs_overlay_file, CLASS_ERROR, (unsigned)diag::Severity::Fatal, "virtual filesystem overlay file '%0' not found", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_module_build_requires_fmodules, CLASS_ERROR, (unsigned)diag::Severity::Error, "module compilation requires '-fmodules'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_module_cannot_create_includes, CLASS_ERROR, (unsigned)diag::Severity::Error, "cannot create includes file for module %0: %1", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_module_header_file_not_found, CLASS_ERROR, (unsigned)diag::Severity::Fatal, "module header file '%0' not found", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_module_interface_requires_cpp_modules, CLASS_ERROR, (unsigned)diag::Severity::Error, "module interface compilation requires '-std=c++20'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_module_map_not_found, CLASS_ERROR, (unsigned)diag::Severity::Fatal, "module map file '%0' not found", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_modules_embed_file_not_found, CLASS_ERROR, (unsigned)diag::Severity::Fatal, "file '%0' specified by '-fmodules-embed-file=' not found", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_no_submodule, CLASS_ERROR, (unsigned)diag::Severity::Error, "no submodule named %0 in module '%1'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_no_submodule_suggest, CLASS_ERROR, (unsigned)diag::Severity::Error, "no submodule named %0 in module '%1'; did you mean '%2'?", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_non_default_visibility_dllimport, CLASS_ERROR, (unsigned)diag::Severity::Error, "non-default visibility cannot be applied to 'dllimport' declaration", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_relocatable_without_isysroot, CLASS_ERROR, (unsigned)diag::Severity::Error, "must specify system root with -isysroot when building a relocatable PCH file", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_test_module_file_extension_version, CLASS_ERROR, (unsigned)diag::Severity::Error, "test module file extension '%0' has different version (%1.%2) than expected (%3.%4)", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_verify_ambiguous_marker, CLASS_ERROR, (unsigned)diag::Severity::Error, "reference to marker '%0' is ambiguous", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_verify_inconsistent_diags, CLASS_ERROR, (unsigned)diag::Severity::Error, "'%0' diagnostics %select{expected|seen}1 but not %select{seen|expected}1: %2", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_verify_invalid_content, CLASS_ERROR, (unsigned)diag::Severity::Error, "invalid expected %0: %1", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_verify_invalid_no_diags, CLASS_ERROR, (unsigned)diag::Severity::Error, "%select{expected|'expected-no-diagnostics'}0 directive cannot follow %select{'expected-no-diagnostics' directive|other expected directives}0", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_verify_invalid_range, CLASS_ERROR, (unsigned)diag::Severity::Error, "invalid range following '-' in expected %0", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_verify_missing_end, CLASS_ERROR, (unsigned)diag::Severity::Error, "cannot find end ('}}') of expected %0", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_verify_missing_file, CLASS_ERROR, (unsigned)diag::Severity::Error, "file '%0' could not be located in expected %1", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_verify_missing_line, CLASS_ERROR, (unsigned)diag::Severity::Error, "missing or invalid line number following '@' in expected %0", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_verify_missing_regex, CLASS_ERROR, (unsigned)diag::Severity::Error, "cannot find start of regex ('{{') in %0", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_verify_missing_start, CLASS_ERROR, (unsigned)diag::Severity::Error, "cannot find start ('{{') of expected %0", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_verify_no_directives, CLASS_ERROR, (unsigned)diag::Severity::Error, "no expected directives found: consider use of 'expected-no-diagnostics'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_verify_no_such_marker, CLASS_ERROR, (unsigned)diag::Severity::Error, "use of undefined marker '%0'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_verify_nonconst_addrspace, CLASS_ERROR, (unsigned)diag::Severity::Error, "qualifier 'const' is needed for variables in address space '%0'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(note_alias_mangled_name_alternative, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "function by that name is mangled as \"%0\"", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_alias_requires_mangled_name, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "the %select{function or variable|function}0 specified in an %select{alias|ifunc}1 must refer to its mangled name", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_fe_backend_frame_larger_than, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "%0", 0, SFINAE_Suppress, false, true, true, false, 18)
DIAG(note_fe_backend_invalid_loc, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "could not determine the original source location for %0:%1:%2", 0, SFINAE_Suppress, false, true, true, false, 18)
DIAG(note_fe_backend_plugin, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "%0", 0, SFINAE_Suppress, false, true, true, false, 18)
DIAG(note_fe_backend_resource_limit, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "%0 (%1) exceeds limit (%2) in '%3'", 0, SFINAE_Suppress, false, true, true, false, 18)
DIAG(note_fe_inline_asm, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "%0", 0, SFINAE_Suppress, false, false, true, false, 12)
DIAG(note_fe_inline_asm_here, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "instantiated into assembly here", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_fe_linking_module, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "linking module '%0': %1", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_fe_source_mgr, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "%0", 0, SFINAE_Suppress, false, false, true, false, 19)
DIAG(note_fixit_applied, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "FIX-IT applied suggested code changes", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_fixit_failed, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "FIX-IT unable to apply suggested code changes", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_fixit_in_macro, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "FIX-IT unable to apply suggested code changes in a macro", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_fixit_unfixed_error, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "FIX-IT detected an error it cannot fix", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_incompatible_analyzer_plugin_api, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "current API version is '%0', but plugin was compiled with version '%1'", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_module_def_undef_here, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "macro was %select{defined|#undef'd}0 here", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_module_import_here, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "module imported here", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_private_top_level_defined, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "module defined here", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_verify_ambiguous_marker, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "ambiguous marker '%0' is defined here", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(remark_fe_backend_optimization_remark, CLASS_REMARK, (unsigned)diag::Severity::Ignored, "%0", 668, SFINAE_Suppress, false, true, true, false, 18)
DIAG(remark_fe_backend_optimization_remark_analysis, CLASS_REMARK, (unsigned)diag::Severity::Ignored, "%0", 669, SFINAE_Suppress, false, true, true, false, 18)
DIAG(remark_fe_backend_optimization_remark_analysis_aliasing, CLASS_REMARK, (unsigned)diag::Severity::Ignored, "%0; allow reordering by specifying '#pragma clang loop vectorize(enable)' before the loop. If the arrays will always be independent specify '#pragma clang loop vectorize(assume_safety)' before the loop or provide the '__restrict__' qualifier with the independent array arguments. Erroneous results will occur if these options are incorrectly applied!", 669, SFINAE_Suppress, false, true, true, false, 18)
DIAG(remark_fe_backend_optimization_remark_analysis_fpcommute, CLASS_REMARK, (unsigned)diag::Severity::Ignored, "%0; allow reordering by specifying '#pragma clang loop vectorize(enable)' before the loop or by providing the compiler option '-ffast-math'.", 669, SFINAE_Suppress, false, true, true, false, 18)
DIAG(remark_fe_backend_optimization_remark_missed, CLASS_REMARK, (unsigned)diag::Severity::Ignored, "%0", 671, SFINAE_Suppress, false, true, true, false, 18)
DIAG(remark_fe_backend_plugin, CLASS_REMARK, (unsigned)diag::Severity::Ignored, "%0", 738, SFINAE_Suppress, false, true, true, false, 18)
DIAG(remark_module_build, CLASS_REMARK, (unsigned)diag::Severity::Ignored, "building module '%0' as '%1'", 546, SFINAE_Suppress, false, true, true, false, 0)
DIAG(remark_module_build_done, CLASS_REMARK, (unsigned)diag::Severity::Ignored, "finished building module '%0'", 546, SFINAE_Suppress, false, true, true, false, 0)
DIAG(remark_module_lock, CLASS_REMARK, (unsigned)diag::Severity::Ignored, "locking '%0' to build module '%1'", 553, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_alias_to_weak_alias, CLASS_WARNING, (unsigned)diag::Severity::Warning, "%select{alias|ifunc}2 will always resolve to %0 even if weak definition of %1 is overridden", 384, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_alias_with_section, CLASS_WARNING, (unsigned)diag::Severity::Warning, "%select{alias|ifunc}1 will not be in section '%0' but in the same section as the %select{aliasee|resolver}2", 384, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_atomic_op_misaligned, CLASS_WARNING, (unsigned)diag::Severity::Warning, "misaligned atomic operation may incur significant performance penalty; the expected alignment (%0 bytes) exceeds the actual alignment (%1 bytes)", 49, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_atomic_op_oversized, CLASS_WARNING, (unsigned)diag::Severity::Warning, "large atomic operation may incur significant performance penalty; the access size (%0 bytes) exceeds the max lock-free size (%1  bytes)", 49, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_avx_calling_convention, CLASS_WARNING, (unsigned)diag::Severity::Warning, "AVX vector %select{return|argument}0 of type %1 without '%2' enabled changes the ABI", 720, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_fe_backend_frame_larger_than, CLASS_WARNING, (unsigned)diag::Severity::Warning, "%0", 331, SFINAE_Suppress, false, true, true, false, 18)
DIAG(warn_fe_backend_invalid_feature_flag, CLASS_WARNING, (unsigned)diag::Severity::Warning, "feature flag '%0' must start with either '+' to enable the feature or '-' to disable it; flag ignored", 446, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_fe_backend_optimization_failure, CLASS_WARNING, (unsigned)diag::Severity::Warning, "%0", 670, SFINAE_Suppress, false, true, true, false, 18)
DIAG(warn_fe_backend_plugin, CLASS_WARNING, (unsigned)diag::Severity::Warning, "%0", 63, SFINAE_Suppress, false, true, true, false, 18)
DIAG(warn_fe_backend_readonly_feature_flag, CLASS_WARNING, (unsigned)diag::Severity::Warning, "feature flag '%0' is ignored since the feature is read only", 446, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_fe_backend_resource_limit, CLASS_WARNING, (unsigned)diag::Severity::Warning, "%0 (%1) exceeds limit (%2) in '%3'", 63, SFINAE_Suppress, false, true, true, false, 18)
DIAG(warn_fe_backend_unsupported, CLASS_WARNING, (unsigned)diag::Severity::Warning, "%0", 0, SFINAE_Suppress, false, true, true, false, 18)
DIAG(warn_fe_backend_unsupported_fp_exceptions, CLASS_WARNING, (unsigned)diag::Severity::Warning, "overriding currently unsupported use of floating point exceptions on this target", 928, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_fe_backend_unsupported_fp_rounding, CLASS_WARNING, (unsigned)diag::Severity::Warning, "overriding currently unsupported rounding mode on this target", 928, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_fe_backend_warning_attr, CLASS_WARNING, (unsigned)diag::Severity::Warning, "call to '%0' declared with 'warning' attribute: %1", 55, SFINAE_Suppress, false, true, true, false, 18)
DIAG(warn_fe_cc_log_diagnostics_failure, CLASS_WARNING, (unsigned)diag::Severity::Warning, "unable to open CC_LOG_DIAGNOSTICS file: %0 (using stderr)", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_fe_cc_print_header_failure, CLASS_WARNING, (unsigned)diag::Severity::Warning, "unable to open CC_PRINT_HEADERS file: %0 (using stderr)", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_fe_frame_larger_than, CLASS_WARNING, (unsigned)diag::Severity::Warning, "stack frame size (%0) exceeds limit (%1) in '%2'", 331, SFINAE_Suppress, false, true, true, false, 18)
DIAG(warn_fe_inline_asm, CLASS_WARNING, (unsigned)diag::Severity::Warning, "%0", 435, SFINAE_Suppress, false, false, true, false, 12)
DIAG(warn_fe_linking_module, CLASS_WARNING, (unsigned)diag::Severity::Warning, "linking module '%0': %1", 470, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_fe_macro_contains_embedded_newline, CLASS_WARNING, (unsigned)diag::Severity::Warning, "macro '%0' contains embedded newline; text after the newline is ignored", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_fe_override_module, CLASS_WARNING, (unsigned)diag::Severity::Warning, "overriding the module target triple with %0", 659, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_fe_serialized_diag_failure, CLASS_WARNING, (unsigned)diag::Severity::Warning, "unable to open file %0 for serializing diagnostics (%1)", 771, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_fe_serialized_diag_failure_during_finalisation, CLASS_WARNING, (unsigned)diag::Severity::Warning, "Received warning after diagnostic serialization teardown was underway: %0", 771, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_fe_serialized_diag_merge_failure, CLASS_WARNING, (unsigned)diag::Severity::Warning, "unable to merge a subprocess's serialized diagnostics", 771, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_fe_source_mgr, CLASS_WARNING, (unsigned)diag::Severity::Warning, "%0", 801, SFINAE_Suppress, false, false, true, false, 19)
DIAG(warn_fe_unable_to_open_stats_file, CLASS_WARNING, (unsigned)diag::Severity::Warning, "unable to open statistics output file '%0': '%1'", 877, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_fixit_no_changes, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "FIX-IT detected errors it could not fix; no output will be generated", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_incompatible_analyzer_plugin_api, CLASS_WARNING, (unsigned)diag::Severity::Warning, "checker plugin '%0' is not compatible with this version of the analyzer", 26, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_missing_submodule, CLASS_WARNING, (unsigned)diag::Severity::Warning, "missing submodule '%0'", 425, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_module_config_macro_undef, CLASS_WARNING, (unsigned)diag::Severity::Warning, "%select{definition|#undef}0 of configuration macro '%1' has no effect on the import of '%2'; pass '%select{-D%1=...|-U%1}0' on the command line to configure the module", 172, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_module_config_mismatch, CLASS_WARNING, (unsigned)diag::Severity::Error, "module file %0 cannot be loaded due to a configuration mismatch with the current compilation", 548, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_no_priv_submodule_use_toplevel, CLASS_WARNING, (unsigned)diag::Severity::Warning, "no submodule named %0 in module '%1'; using top level '%2'", 712, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_option_invalid_ocl_version, CLASS_WARNING, (unsigned)diag::Severity::Warning, "%0 does not support the option '%1'", 210, SFINAE_Suppress, false, false, true, false, 30)
DIAG(warn_profile_data_misexpect, CLASS_WARNING, (unsigned)diag::Severity::Warning, "Potential performance regression from use of __builtin_expect(): Annotation was correct on %0 of profiled executions.", 523, SFINAE_Suppress, false, true, true, false, 34)
DIAG(warn_profile_data_missing, CLASS_WARNING, (unsigned)diag::Severity::Ignored, "profile data may be incomplete: of %0 function%s0, %1 %plural{1:has|:have}1 no data", 713, SFINAE_Suppress, false, false, true, false, 34)
DIAG(warn_profile_data_out_of_date, CLASS_WARNING, (unsigned)diag::Severity::Warning, "profile data may be out of date: of %0 function%s0, %1 %plural{1:has|:have}1 mismatched data that will be ignored", 714, SFINAE_Suppress, false, false, true, false, 34)
DIAG(warn_profile_data_unprofiled, CLASS_WARNING, (unsigned)diag::Severity::Warning, "no profile data available for file \"%0\"", 715, SFINAE_Suppress, false, false, true, false, 34)
DIAG(warn_sync_op_misaligned, CLASS_WARNING, (unsigned)diag::Severity::Warning, "__sync builtin operation MUST have natural alignment (consider using __atomic).", 844, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_unknown_diag_option, CLASS_WARNING, (unsigned)diag::Severity::Warning, "unknown %select{warning|remark}0 option '%1'%select{|; did you mean '%3'?}2", 909, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_unknown_warning_specifier, CLASS_WARNING, (unsigned)diag::Severity::Warning, "unknown %0 warning specifier: '%1'", 909, SFINAE_Suppress, false, false, true, false, 0)
