#ifdef LEXSTART
__LEXSTART = DIAG_START_LEX,
#undef LEXSTART
#endif

DIAG(backslash_newline_space, CLASS_WARNING, (unsigned)diag::Severity::Warning, "backslash and newline separated by space", 64, SFINAE_Suppress, false, false, true, false, 1)
DIAG(err__Pragma_malformed, CLASS_ERROR, (unsigned)diag::Severity::Error, "_<PERSON><PERSON><PERSON> takes a parenthesized string literal", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_bad_character_encoding, CLASS_ERROR, (unsigned)diag::Severity::Error, "illegal character encoding in character literal", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_bad_string_encoding, CLASS_ERROR, (unsigned)diag::Severity::Error, "illegal character encoding in string literal", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_character_not_allowed, CLASS_ERROR, (unsigned)diag::Severity::Error, "unexpected character <U+%0>", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_character_not_allowed_identifier, CLASS_ERROR, (unsigned)diag::Severity::Error, "character <U+%0> not allowed %select{in|at the start of}1 an identifier", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_character_too_large, CLASS_ERROR, (unsigned)diag::Severity::Error, "character too large for enclosing character literal type", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_conflict_marker, CLASS_ERROR, (unsigned)diag::Severity::Error, "version control conflict marker in file", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_defined_macro_name, CLASS_ERROR, (unsigned)diag::Severity::Error, "'defined' cannot be used as a macro name", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_delimited_escape_empty, CLASS_ERROR, (unsigned)diag::Severity::Error, "delimited escape sequence cannot be empty", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_delimited_escape_invalid, CLASS_ERROR, (unsigned)diag::Severity::Error, "invalid digit '%0' in escape sequence", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_delimited_escape_missing_brace, CLASS_ERROR, (unsigned)diag::Severity::Error, "expected '{' after '\\%0' escape sequence", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_dep_source_scanner_missing_semi_after_at_import, CLASS_ERROR, (unsigned)diag::Severity::Error, "could not find ';' after @import", 0, SFINAE_SubstitutionFailure, false, true, true, false, 17)
DIAG(err_dep_source_scanner_unexpected_tokens_at_import, CLASS_ERROR, (unsigned)diag::Severity::Error, "unexpected extra tokens at end of @import declaration", 0, SFINAE_SubstitutionFailure, false, true, true, false, 17)
DIAG(err_digit_separator_not_between_digits, CLASS_ERROR, (unsigned)diag::Severity::Error, "digit separator cannot appear at %select{start|end}0 of digit sequence", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_embedded_directive, CLASS_ERROR, (unsigned)diag::Severity::Error, "embedding a #%0 directive within macro arguments is not supported", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_escape_too_large, CLASS_ERROR, (unsigned)diag::Severity::Error, "%select{hex|octal}0 escape sequence out of range", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_expected_id_building_module, CLASS_ERROR, (unsigned)diag::Severity::Error, "expected a module name in '__building_module' expression", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_exponent_has_no_digits, CLASS_ERROR, (unsigned)diag::Severity::Error, "exponent has no digits", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_feature_check_malformed, CLASS_ERROR, (unsigned)diag::Severity::Error, "builtin feature check macro requires a parenthesized identifier", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_header_import_not_header_unit, CLASS_ERROR, (unsigned)diag::Severity::Error, "header file %0 (aka '%1') cannot be imported because it is not known to be a header unit", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_header_import_semi_in_macro, CLASS_ERROR, (unsigned)diag::Severity::Error, "semicolon terminating header import declaration cannot be produced by a macro", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_hex_constant_requires, CLASS_ERROR, (unsigned)diag::Severity::Error, "hexadecimal floating %select{constant|literal}0 requires %select{an exponent|a significand}1", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_hex_escape_no_digits, CLASS_ERROR, (unsigned)diag::Severity::Error, "\\%0 used with no following hex digits", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_illegal_use_of_flt_eval_macro, CLASS_ERROR, (unsigned)diag::Severity::Error, "'__FLT_EVAL_METHOD__' cannot be expanded inside a scope containing '#pragma clang fp eval_method'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_invalid_char_raw_delim, CLASS_ERROR, (unsigned)diag::Severity::Error, "invalid character '%0' character in raw string delimiter; use PREFIX( )PREFIX to delimit raw string", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_invalid_character_to_charify, CLASS_ERROR, (unsigned)diag::Severity::Error, "invalid argument to convert to character", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_invalid_digit, CLASS_ERROR, (unsigned)diag::Severity::Error, "invalid digit '%0' in %select{decimal|octal|binary}1 constant", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_invalid_suffix_constant, CLASS_ERROR, (unsigned)diag::Severity::Error, "invalid suffix '%0' on %select{integer|floating|fixed-point}1 constant", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_invalid_ucn_name, CLASS_ERROR, (unsigned)diag::Severity::Error, "'%0' is not a valid Unicode character name", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_invalid_utf8, CLASS_ERROR, (unsigned)diag::Severity::Error, "source file is not valid UTF-8", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_lexing_char, CLASS_ERROR, (unsigned)diag::Severity::Error, "failure when lexing a character literal", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_lexing_numeric, CLASS_ERROR, (unsigned)diag::Severity::Error, "failure when lexing a numeric literal", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_lexing_string, CLASS_ERROR, (unsigned)diag::Severity::Error, "failure when lexing a string literal", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_mmap_config_macro_submodule, CLASS_ERROR, (unsigned)diag::Severity::Error, "configuration macros are only allowed in top-level modules", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_mmap_conflicting_export_as, CLASS_ERROR, (unsigned)diag::Severity::Error, "conflicting re-export of module '%0' as '%1' or '%2'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_mmap_duplicate_header_attribute, CLASS_ERROR, (unsigned)diag::Severity::Error, "header attribute '%0' specified multiple times", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_mmap_expected_attribute, CLASS_ERROR, (unsigned)diag::Severity::Error, "expected an attribute name", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_mmap_expected_config_macro, CLASS_ERROR, (unsigned)diag::Severity::Error, "expected configuration macro name after ','", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_mmap_expected_conflicts_comma, CLASS_ERROR, (unsigned)diag::Severity::Error, "expected ',' after conflicting module name", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_mmap_expected_conflicts_message, CLASS_ERROR, (unsigned)diag::Severity::Error, "expected a message describing the conflict with '%0'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_mmap_expected_export_wildcard, CLASS_ERROR, (unsigned)diag::Severity::Error, "only '*' can be exported from an inferred submodule", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_mmap_expected_feature, CLASS_ERROR, (unsigned)diag::Severity::Error, "expected a feature name", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_mmap_expected_header, CLASS_ERROR, (unsigned)diag::Severity::Error, "expected a header name after '%0'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_mmap_expected_header_attribute, CLASS_ERROR, (unsigned)diag::Severity::Error, "expected a header attribute name ('size' or 'mtime')", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_mmap_expected_inferred_member, CLASS_ERROR, (unsigned)diag::Severity::Error, "expected %select{module exclusion with 'exclude'|'export *'}0", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_mmap_expected_lbrace, CLASS_ERROR, (unsigned)diag::Severity::Error, "expected '{' to start module '%0'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_mmap_expected_lbrace_wildcard, CLASS_ERROR, (unsigned)diag::Severity::Error, "expected '{' to start inferred submodule", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_mmap_expected_library_name, CLASS_ERROR, (unsigned)diag::Severity::Error, "expected %select{library|framework}0 name as a string", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_mmap_expected_member, CLASS_ERROR, (unsigned)diag::Severity::Error, "expected umbrella, header, submodule, or module export", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_mmap_expected_mmap_file, CLASS_ERROR, (unsigned)diag::Severity::Error, "expected a module map file name", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_mmap_expected_module, CLASS_ERROR, (unsigned)diag::Severity::Error, "expected module declaration", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_mmap_expected_module_name, CLASS_ERROR, (unsigned)diag::Severity::Error, "expected module name", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_mmap_expected_rbrace, CLASS_ERROR, (unsigned)diag::Severity::Error, "expected '}'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_mmap_expected_rsquare, CLASS_ERROR, (unsigned)diag::Severity::Error, "expected ']' to close attribute", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_mmap_explicit_inferred_framework, CLASS_ERROR, (unsigned)diag::Severity::Error, "inferred framework modules cannot be 'explicit'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_mmap_explicit_top_level, CLASS_ERROR, (unsigned)diag::Severity::Error, "'explicit' is not permitted on top-level modules", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_mmap_inferred_framework_submodule, CLASS_ERROR, (unsigned)diag::Severity::Error, "inferred submodule cannot be a framework submodule", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_mmap_inferred_no_umbrella, CLASS_ERROR, (unsigned)diag::Severity::Error, "inferred submodules require a module with an umbrella", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_mmap_inferred_redef, CLASS_ERROR, (unsigned)diag::Severity::Error, "redefinition of inferred submodule", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_mmap_invalid_header_attribute_value, CLASS_ERROR, (unsigned)diag::Severity::Error, "expected integer literal as value for header attribute '%0'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_mmap_missing_exclude_name, CLASS_ERROR, (unsigned)diag::Severity::Error, "expected excluded module name", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_mmap_missing_module_qualified, CLASS_ERROR, (unsigned)diag::Severity::Error, "no module named '%0' in '%1'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_mmap_missing_module_unqualified, CLASS_ERROR, (unsigned)diag::Severity::Error, "no module named '%0' visible from '%1'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_mmap_missing_parent_module, CLASS_ERROR, (unsigned)diag::Severity::Error, "no module named '%0' %select{found|in '%2'}1, parent module must be defined before the submodule", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_mmap_module_id, CLASS_ERROR, (unsigned)diag::Severity::Error, "expected a module name or '*'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_mmap_module_redefinition, CLASS_ERROR, (unsigned)diag::Severity::Error, "redefinition of module '%0'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_mmap_nested_submodule_id, CLASS_ERROR, (unsigned)diag::Severity::Error, "qualified module name can only be used to define modules at the top level", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_mmap_submodule_export_as, CLASS_ERROR, (unsigned)diag::Severity::Error, "only top-level modules can be re-exported as public", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_mmap_top_level_inferred_submodule, CLASS_ERROR, (unsigned)diag::Severity::Error, "only submodules and framework modules may be inferred with wildcard syntax", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_mmap_umbrella_clash, CLASS_ERROR, (unsigned)diag::Severity::Error, "umbrella for module '%0' already covers this directory", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_mmap_unknown_token, CLASS_ERROR, (unsigned)diag::Severity::Error, "skipping stray token", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_mmap_use_decl_submodule, CLASS_ERROR, (unsigned)diag::Severity::Error, "use declarations are only allowed in top-level modules", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_multichar_character_literal, CLASS_ERROR, (unsigned)diag::Severity::Error, "%select{wide|Unicode}0 character literals may not contain multiple characters", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_pascal_string_too_long, CLASS_ERROR, (unsigned)diag::Severity::Error, "Pascal string is too long", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_paste_at_end, CLASS_ERROR, (unsigned)diag::Severity::Error, "'##' cannot appear at end of macro expansion", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_paste_at_start, CLASS_ERROR, (unsigned)diag::Severity::Error, "'##' cannot appear at start of macro expansion", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_placeholder_in_source, CLASS_ERROR, (unsigned)diag::Severity::Error, "editor placeholder in source file", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_pp_arc_cf_code_audited_syntax, CLASS_ERROR, (unsigned)diag::Severity::Error, "expected 'begin' or 'end'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_pp_assume_nonnull_syntax, CLASS_ERROR, (unsigned)diag::Severity::Error, "expected 'begin' or 'end'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 22)
DIAG(err_pp_bad_paste, CLASS_ERROR, (unsigned)diag::Severity::Error, "pasting formed '%0', an invalid preprocessing token", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_pp_colon_without_question, CLASS_ERROR, (unsigned)diag::Severity::Error, "':' without preceding '?'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_pp_directive_required, CLASS_ERROR, (unsigned)diag::Severity::Error, "%0 must be used within a preprocessing directive", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_pp_division_by_zero, CLASS_ERROR, (unsigned)diag::Severity::Error, "division by zero in preprocessor expression", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_pp_double_begin_of_arc_cf_code_audited, CLASS_ERROR, (unsigned)diag::Severity::Error, "already inside '#pragma clang arc_cf_code_audited'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_pp_double_begin_of_assume_nonnull, CLASS_ERROR, (unsigned)diag::Severity::Error, "already inside '#pragma clang assume_nonnull'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 22)
DIAG(err_pp_double_begin_pragma_unsafe_buffer_usage, CLASS_ERROR, (unsigned)diag::Severity::Error, "already inside '#pragma unsafe_buffer_usage'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_pp_duplicate_name_in_arg_list, CLASS_ERROR, (unsigned)diag::Severity::Error, "duplicate macro parameter name %0", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_pp_empty_filename, CLASS_ERROR, (unsigned)diag::Severity::Error, "empty filename", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_pp_endif_without_if, CLASS_ERROR, (unsigned)diag::Severity::Error, "#endif without #if", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_pp_eof_in_arc_cf_code_audited, CLASS_ERROR, (unsigned)diag::Severity::Error, "'#pragma clang arc_cf_code_audited' was not ended within this file", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_pp_eof_in_assume_nonnull, CLASS_ERROR, (unsigned)diag::Severity::Error, "'#pragma clang assume_nonnull' was not ended within this file", 0, SFINAE_SubstitutionFailure, false, true, true, false, 22)
DIAG(err_pp_error_opening_file, CLASS_ERROR, (unsigned)diag::Severity::Fatal, "error opening file '%0': %1", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_pp_expected_after, CLASS_ERROR, (unsigned)diag::Severity::Error, "missing %1 after %0", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_pp_expected_comma_in_arg_list, CLASS_ERROR, (unsigned)diag::Severity::Error, "expected comma in macro parameter list", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_pp_expected_eol, CLASS_ERROR, (unsigned)diag::Severity::Error, "expected end of line in preprocessor expression", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_pp_expected_ident_in_arg_list, CLASS_ERROR, (unsigned)diag::Severity::Error, "expected identifier in macro parameter list", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_pp_expected_module_name, CLASS_ERROR, (unsigned)diag::Severity::Error, "expected %select{identifier after '.' in |}0module name", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_pp_expected_rparen, CLASS_ERROR, (unsigned)diag::Severity::Error, "expected ')' in preprocessor expression", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_pp_expected_value_in_expr, CLASS_ERROR, (unsigned)diag::Severity::Error, "expected value in expression", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_pp_expects_filename, CLASS_ERROR, (unsigned)diag::Severity::Error, "expected \"FILENAME\" or <FILENAME>", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_pp_expr_bad_token_binop, CLASS_ERROR, (unsigned)diag::Severity::Error, "token is not a valid binary operator in a preprocessor subexpression", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_pp_expr_bad_token_lparen, CLASS_ERROR, (unsigned)diag::Severity::Error, "function-like macro %0 is not defined", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_pp_expr_bad_token_start_expr, CLASS_ERROR, (unsigned)diag::Severity::Error, "invalid token at start of a preprocessor expression", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_pp_file_not_found, CLASS_ERROR, (unsigned)diag::Severity::Fatal, "'%0' file not found", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_pp_file_not_found_angled_include_not_fatal, CLASS_ERROR, (unsigned)diag::Severity::Error, "'%0' file not found with <angled> %select{include|import}1; use \"quotes\" instead", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_pp_file_not_found_typo_not_fatal, CLASS_ERROR, (unsigned)diag::Severity::Error, "'%0' file not found, did you mean '%1'?", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_pp_hash_error, CLASS_ERROR, (unsigned)diag::Severity::Error, "%0", 0, SFINAE_SubstitutionFailure, false, true, true, false, 24)
DIAG(err_pp_identifier_arg_not_identifier, CLASS_ERROR, (unsigned)diag::Severity::Error, "cannot convert %0 token to an identifier", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_pp_illegal_floating_literal, CLASS_ERROR, (unsigned)diag::Severity::Error, "floating point literal in preprocessor expression", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_pp_import_directive_ms, CLASS_ERROR, (unsigned)diag::Severity::Error, "#import of type library is an unsupported Microsoft feature", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_pp_include_in_arc_cf_code_audited, CLASS_ERROR, (unsigned)diag::Severity::Error, "cannot %select{#include files|import headers}0 inside '#pragma clang arc_cf_code_audited'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_pp_include_in_assume_nonnull, CLASS_ERROR, (unsigned)diag::Severity::Error, "cannot %select{#include files|import headers}0 inside '#pragma clang assume_nonnull'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 22)
DIAG(err_pp_include_too_deep, CLASS_ERROR, (unsigned)diag::Severity::Error, "#include nested too deeply", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_pp_including_mainfile_in_preamble, CLASS_ERROR, (unsigned)diag::Severity::Error, "main file cannot be included recursively when building a preamble", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_pp_invalid_directive, CLASS_ERROR, (unsigned)diag::Severity::Error, "invalid preprocessing directive%select{|, did you mean '#%1'?}0", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_pp_invalid_poison, CLASS_ERROR, (unsigned)diag::Severity::Error, "can only poison identifier tokens", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_pp_invalid_tok_in_arg_list, CLASS_ERROR, (unsigned)diag::Severity::Error, "invalid token in macro parameter list", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_pp_invalid_udl, CLASS_ERROR, (unsigned)diag::Severity::Error, "%select{character|integer}0 literal with user-defined suffix cannot be used in preprocessor constant expression", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_pp_line_digit_sequence, CLASS_ERROR, (unsigned)diag::Severity::Error, "%select{#line|GNU line marker}0 directive requires a simple digit sequence", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_pp_line_invalid_filename, CLASS_ERROR, (unsigned)diag::Severity::Error, "invalid filename for #line directive", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_pp_line_requires_integer, CLASS_ERROR, (unsigned)diag::Severity::Error, "#line directive requires a positive integer argument", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_pp_linemarker_invalid_filename, CLASS_ERROR, (unsigned)diag::Severity::Error, "invalid filename for line marker directive", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_pp_linemarker_invalid_flag, CLASS_ERROR, (unsigned)diag::Severity::Error, "invalid flag line marker directive", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_pp_linemarker_invalid_pop, CLASS_ERROR, (unsigned)diag::Severity::Error, "invalid line marker flag '2': cannot pop empty include stack", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_pp_linemarker_requires_integer, CLASS_ERROR, (unsigned)diag::Severity::Error, "line marker directive requires a positive integer argument", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_pp_macro_not_identifier, CLASS_ERROR, (unsigned)diag::Severity::Error, "macro name must be an identifier", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_pp_malformed_ident, CLASS_ERROR, (unsigned)diag::Severity::Error, "invalid #ident directive", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_pp_missing_lparen_in_vaopt_use, CLASS_ERROR, (unsigned)diag::Severity::Error, "missing '(' following __VA_OPT__", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_pp_missing_macro_name, CLASS_ERROR, (unsigned)diag::Severity::Error, "macro name missing", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_pp_missing_rparen_in_macro_def, CLASS_ERROR, (unsigned)diag::Severity::Error, "missing ')' in macro parameter list", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_pp_module_begin_no_module_map, CLASS_ERROR, (unsigned)diag::Severity::Error, "no module map available for module %0", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_pp_module_begin_no_submodule, CLASS_ERROR, (unsigned)diag::Severity::Error, "submodule %0.%1 not declared in module map", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_pp_module_begin_without_module_end, CLASS_ERROR, (unsigned)diag::Severity::Error, "no matching '#pragma clang module end' for this '#pragma clang module begin'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_pp_module_begin_wrong_module, CLASS_ERROR, (unsigned)diag::Severity::Error, "must specify '-fmodule-name=%0' to enter %select{|submodule of }1this module%select{ (current module is %3)|}2", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_pp_module_build_missing_end, CLASS_ERROR, (unsigned)diag::Severity::Error, "no matching '#pragma clang module endbuild' for this '#pragma clang module build'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_pp_module_end_without_module_begin, CLASS_ERROR, (unsigned)diag::Severity::Error, "no matching '#pragma clang module begin' for this '#pragma clang module end'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_pp_nested_paren, CLASS_ERROR, (unsigned)diag::Severity::Error, "nested parentheses not permitted in %0", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_pp_operator_used_as_macro_name, CLASS_ERROR, (unsigned)diag::Severity::Error, "C++ operator %0 (aka %1) used as a macro name", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_pp_pragma_hdrstop_not_seen, CLASS_ERROR, (unsigned)diag::Severity::Fatal, "#pragma hdrstop not seen while attempting to use precompiled header", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_pp_pragma_unsafe_buffer_usage_syntax, CLASS_ERROR, (unsigned)diag::Severity::Error, "Expected 'begin' or 'end'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_pp_remainder_by_zero, CLASS_ERROR, (unsigned)diag::Severity::Error, "remainder by zero in preprocessor expression", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_pp_stringize_not_parameter, CLASS_ERROR, (unsigned)diag::Severity::Error, "'%select{#|#@}0' is not followed by a macro parameter", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_pp_through_header_not_found, CLASS_ERROR, (unsigned)diag::Severity::Fatal, "'%0' required for precompiled header not found", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_pp_through_header_not_seen, CLASS_ERROR, (unsigned)diag::Severity::Fatal, "#include of '%0' not seen while attempting to %select{create|use}1 precompiled header", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_pp_unclosed_pragma_unsafe_buffer_usage, CLASS_ERROR, (unsigned)diag::Severity::Error, "'#pragma unsafe_buffer_usage' was not ended", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_pp_unmatched_end_begin_pragma_unsafe_buffer_usage, CLASS_ERROR, (unsigned)diag::Severity::Error, "not currently inside '#pragma unsafe_buffer_usage'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_pp_unmatched_end_of_arc_cf_code_audited, CLASS_ERROR, (unsigned)diag::Severity::Error, "not currently inside '#pragma clang arc_cf_code_audited'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_pp_unmatched_end_of_assume_nonnull, CLASS_ERROR, (unsigned)diag::Severity::Error, "not currently inside '#pragma clang assume_nonnull'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 22)
DIAG(err_pp_unterminated_conditional, CLASS_ERROR, (unsigned)diag::Severity::Error, "unterminated conditional directive", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_pp_used_poisoned_id, CLASS_ERROR, (unsigned)diag::Severity::Error, "attempt to use a poisoned identifier", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_pp_vaopt_nested_use, CLASS_ERROR, (unsigned)diag::Severity::Error, "__VA_OPT__ cannot be nested within its own replacement tokens", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_pp_visibility_non_macro, CLASS_ERROR, (unsigned)diag::Severity::Error, "no macro named %0", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_pragma_message, CLASS_ERROR, (unsigned)diag::Severity::Error, "%0", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_pragma_message_malformed, CLASS_ERROR, (unsigned)diag::Severity::Error, "pragma %select{message|warning|error}0 requires parenthesized string", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_pragma_push_pop_macro_malformed, CLASS_ERROR, (unsigned)diag::Severity::Error, "pragma %0 requires a parenthesized string", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_raw_delim_too_long, CLASS_ERROR, (unsigned)diag::Severity::Error, "raw string delimiter longer than 16 characters; use PREFIX( )PREFIX to delimit raw string", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_string_concat_mixed_suffix, CLASS_ERROR, (unsigned)diag::Severity::Error, "differing user-defined suffixes ('%0' and '%1') in string literal concatenation", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_too_few_args_in_macro_invoc, CLASS_ERROR, (unsigned)diag::Severity::Error, "too few arguments provided to function-like macro invocation", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_too_many_args_in_macro_invoc, CLASS_ERROR, (unsigned)diag::Severity::Error, "too many arguments provided to function-like macro invocation", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_ucn_control_character, CLASS_ERROR, (unsigned)diag::Severity::Error, "universal character name refers to a control character", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_ucn_escape_basic_scs, CLASS_ERROR, (unsigned)diag::Severity::Error, "character '%0' cannot be specified by a universal character name", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_ucn_escape_incomplete, CLASS_ERROR, (unsigned)diag::Severity::Error, "incomplete universal character name", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_ucn_escape_invalid, CLASS_ERROR, (unsigned)diag::Severity::Error, "invalid universal character", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_undeclared_use_of_module, CLASS_ERROR, (unsigned)diag::Severity::Error, "module %0 does not depend on a module exporting '%1'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_undeclared_use_of_module_indirect, CLASS_ERROR, (unsigned)diag::Severity::Error, "module %0 does not directly depend on a module exporting '%1', which is part of indirectly-used module %2", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_unevaluated_string_invalid_escape_sequence, CLASS_ERROR, (unsigned)diag::Severity::Error, "invalid escape sequence '%0' in an unevaluated string literal", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_unevaluated_string_prefix, CLASS_ERROR, (unsigned)diag::Severity::Error, "an unevaluated string literal cannot have an encoding prefix", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_unevaluated_string_udl, CLASS_ERROR, (unsigned)diag::Severity::Error, "an unevaluated string literal cannot be a user-defined literal", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_unsupported_string_concat, CLASS_ERROR, (unsigned)diag::Severity::Error, "unsupported non-standard concatenation of string literals", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_unterm_macro_invoc, CLASS_ERROR, (unsigned)diag::Severity::Error, "unterminated function-like macro invocation", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_unterminated___pragma, CLASS_ERROR, (unsigned)diag::Severity::Error, "missing terminating ')' character", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_unterminated_block_comment, CLASS_ERROR, (unsigned)diag::Severity::Error, "unterminated /* comment", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_unterminated_raw_string, CLASS_ERROR, (unsigned)diag::Severity::Error, "raw string missing terminating delimiter )%0\"", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_vaopt_paste_at_end, CLASS_ERROR, (unsigned)diag::Severity::Error, "'##' cannot appear at end of __VA_OPT__ argument", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(err_vaopt_paste_at_start, CLASS_ERROR, (unsigned)diag::Severity::Error, "'##' cannot appear at start of __VA_OPT__ argument", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(escaped_newline_block_comment_end, CLASS_WARNING, (unsigned)diag::Severity::Warning, "escaped newline between */ characters at block comment end", 162, SFINAE_Suppress, false, false, true, false, 1)
DIAG(ext_binary_literal, CLASS_EXTENSION, (unsigned)diag::Severity::Ignored, "binary integer literals are a GNU extension", 349, SFINAE_Suppress, false, false, true, false, 1)
DIAG(ext_binary_literal_cxx14, CLASS_EXTENSION, (unsigned)diag::Severity::Ignored, "binary integer literals are a C++14 extension", 101, SFINAE_Suppress, false, false, true, false, 1)
DIAG(ext_c2x_pp_directive, CLASS_EXTENSION, (unsigned)diag::Severity::Warning, "use of a '#%select{<BUG IF SEEN>|elifdef|elifndef}0' directive is a C2x extension", 142, SFINAE_Suppress, false, false, true, false, 1)
DIAG(ext_c99_whitespace_required_after_macro_name, CLASS_EXTENSION, (unsigned)diag::Severity::Warning, "ISO C99 requires whitespace after the macro name", 145, SFINAE_Suppress, false, false, true, false, 1)
DIAG(ext_charize_microsoft, CLASS_EXTENSION, (unsigned)diag::Severity::Ignored, "charizing operator #@ is a Microsoft extension", 493, SFINAE_Suppress, false, false, true, false, 1)
DIAG(ext_comment_paste_microsoft, CLASS_EXTENSION, (unsigned)diag::Severity::Ignored, "pasting two '/' tokens into a '//' comment is a Microsoft extension", 494, SFINAE_Suppress, false, false, true, false, 1)
DIAG(ext_ctrl_z_eof_microsoft, CLASS_EXTENSION, (unsigned)diag::Severity::Ignored, "treating Ctrl-Z as end-of-file is a Microsoft extension", 499, SFINAE_Suppress, false, false, true, false, 1)
DIAG(ext_cxx23_pp_directive, CLASS_EXTENSION, (unsigned)diag::Severity::Warning, "use of a '#%select{<BUG IF SEEN>|elifdef|elifndef}0' directive is a C++23 extension", 120, SFINAE_Suppress, false, false, true, false, 1)
DIAG(ext_delimited_escape_sequence, CLASS_EXTENSION, (unsigned)diag::Severity::Ignored, "%select{delimited|named}0 escape sequences are a %select{Clang|C++23}1 extension", 208, SFINAE_Suppress, false, false, true, false, 1)
DIAG(ext_dollar_in_identifier, CLASS_EXTENSION, (unsigned)diag::Severity::Ignored, "'$' in identifier", 259, SFINAE_Suppress, false, false, true, false, 1)
DIAG(ext_embedded_directive, CLASS_EXTENSION, (unsigned)diag::Severity::Ignored, "embedding a directive within macro arguments has undefined behavior", 275, SFINAE_Suppress, false, false, true, false, 1)
DIAG(ext_empty_character, CLASS_EXTENSION, (unsigned)diag::Severity::Warning, "empty character constant", 457, SFINAE_Suppress, false, false, true, false, 1)
DIAG(ext_empty_fnmacro_arg, CLASS_EXTENSION, (unsigned)diag::Severity::Ignored, "empty macro arguments are a C99 feature", 145, SFINAE_Suppress, false, false, true, false, 1)
DIAG(ext_hex_constant_invalid, CLASS_EXTENSION, (unsigned)diag::Severity::Ignored, "hexadecimal floating constants are a C99 feature", 145, SFINAE_Suppress, false, false, true, false, 1)
DIAG(ext_hex_literal_invalid, CLASS_EXTENSION, (unsigned)diag::Severity::Ignored, "hexadecimal floating literals are a C++17 feature", 109, SFINAE_Suppress, false, false, true, false, 1)
DIAG(ext_line_comment, CLASS_EXTENSION, (unsigned)diag::Severity::Ignored, "// comments are not allowed in this language", 162, SFINAE_Suppress, false, false, true, false, 1)
DIAG(ext_mathematical_notation, CLASS_EXTENSION, (unsigned)diag::Severity::Warning, "mathematical notation character <U+%0> in an identifier is a Clang extension", 483, SFINAE_Suppress, false, false, true, false, 1)
DIAG(ext_missing_varargs_arg, CLASS_EXTENSION, (unsigned)diag::Severity::Ignored, "must specify at least one argument for '...' parameter of variadic macro", 376, SFINAE_Suppress, false, false, true, false, 1)
DIAG(ext_missing_whitespace_after_macro_name, CLASS_EXTENSION, (unsigned)diag::Severity::Warning, "whitespace required after macro name", 0, SFINAE_Suppress, false, false, true, false, 1)
DIAG(ext_ms_reserved_user_defined_literal, CLASS_EXTENSION, (unsigned)diag::Severity::Warning, "invalid suffix on literal; C++11 requires a space between literal and identifier", 747, SFINAE_Suppress, false, false, true, false, 1)
DIAG(ext_multi_line_line_comment, CLASS_EXTENSION, (unsigned)diag::Severity::Ignored, "multi-line // comment", 162, SFINAE_Suppress, false, false, true, false, 1)
DIAG(ext_named_variadic_macro, CLASS_EXTENSION, (unsigned)diag::Severity::Ignored, "named variadic macros are a GNU extension", 963, SFINAE_Suppress, false, false, true, false, 1)
DIAG(ext_no_newline_eof, CLASS_EXTENSION, (unsigned)diag::Severity::Ignored, "no newline at end of file", 567, SFINAE_Suppress, false, false, true, false, 1)
DIAG(ext_nonstandard_escape, CLASS_EXTENSION, (unsigned)diag::Severity::Ignored, "use of non-standard escape character '\\%0'", 673, SFINAE_Suppress, false, false, true, false, 1)
DIAG(ext_on_off_switch_syntax, CLASS_EXTENSION, (unsigned)diag::Severity::Warning, "expected 'ON' or 'OFF' or 'DEFAULT' in pragma", 907, SFINAE_Suppress, false, false, true, false, 1)
DIAG(ext_paste_comma, CLASS_EXTENSION, (unsigned)diag::Severity::Ignored, "token pasting of ',' and __VA_ARGS__ is a GNU extension", 376, SFINAE_Suppress, false, false, true, false, 1)
DIAG(ext_pp_bad_paste_ms, CLASS_EXTENSION, (unsigned)diag::Severity::Error, "pasting formed '%0', an invalid preprocessing token", 460, SFINAE_Suppress, false, false, true, false, 1)
DIAG(ext_pp_bad_vaargs_use, CLASS_EXTENSION, (unsigned)diag::Severity::Ignored, "__VA_ARGS__ can only appear in the expansion of a C99 variadic macro", 673, SFINAE_Suppress, false, false, true, false, 1)
DIAG(ext_pp_bad_vaopt_use, CLASS_EXTENSION, (unsigned)diag::Severity::Warning, "__VA_OPT__ can only appear in the expansion of a variadic macro", 963, SFINAE_Suppress, false, false, true, false, 1)
DIAG(ext_pp_comma_expr, CLASS_EXTENSION, (unsigned)diag::Severity::Ignored, "comma operator in operand of #if", 673, SFINAE_Suppress, false, false, true, false, 1)
DIAG(ext_pp_extra_tokens_at_eol, CLASS_EXTENSION, (unsigned)diag::Severity::Warning, "extra tokens at end of #%0 directive", 304, SFINAE_Suppress, false, false, true, false, 1)
DIAG(ext_pp_gnu_line_directive, CLASS_EXTENSION, (unsigned)diag::Severity::Ignored, "this style of line directive is a GNU extension", 364, SFINAE_Suppress, false, false, true, false, 1)
DIAG(ext_pp_ident_directive, CLASS_EXTENSION, (unsigned)diag::Severity::Ignored, "#ident is a language extension", 673, SFINAE_Suppress, false, false, true, false, 1)
DIAG(ext_pp_import_directive, CLASS_EXTENSION, (unsigned)diag::Severity::Ignored, "#import is a language extension", 408, SFINAE_Suppress, false, false, true, false, 1)
DIAG(ext_pp_include_next_directive, CLASS_EXTENSION, (unsigned)diag::Severity::Ignored, "#include_next is a language extension", 361, SFINAE_Suppress, false, false, true, false, 1)
DIAG(ext_pp_include_search_ms, CLASS_EXTENSION, (unsigned)diag::Severity::Warning, "#include resolved using non-portable Microsoft search rules as: %0", 510, SFINAE_Suppress, false, false, true, false, 1)
DIAG(ext_pp_line_too_big, CLASS_EXTENSION, (unsigned)diag::Severity::Ignored, "C requires #line number to be less than %0, allowed as extension", 673, SFINAE_Suppress, false, false, true, false, 1)
DIAG(ext_pp_line_zero, CLASS_EXTENSION, (unsigned)diag::Severity::Ignored, "#line directive with zero argument is a GNU extension", 375, SFINAE_Suppress, false, false, true, false, 1)
DIAG(ext_pp_macro_redef, CLASS_EXTENSION, (unsigned)diag::Severity::Warning, "%0 macro redefined", 478, SFINAE_Suppress, false, false, true, false, 1)
DIAG(ext_pp_opencl_variadic_macros, CLASS_EXTENSION, (unsigned)diag::Severity::Ignored, "variadic macros are a Clang extension in OpenCL", 673, SFINAE_Suppress, false, false, true, false, 1)
DIAG(ext_pp_operator_used_as_macro_name, CLASS_EXTENSION, (unsigned)diag::Severity::Ignored, "C++ operator %0 (aka %1) used as a macro name", 496, SFINAE_Suppress, false, false, true, false, 1)
DIAG(ext_pp_redef_builtin_macro, CLASS_EXTENSION, (unsigned)diag::Severity::Warning, "redefining builtin macro", 84, SFINAE_Suppress, false, false, true, false, 1)
DIAG(ext_pp_undef_builtin_macro, CLASS_EXTENSION, (unsigned)diag::Severity::Warning, "undefining builtin macro", 84, SFINAE_Suppress, false, false, true, false, 1)
DIAG(ext_pp_warning_directive, CLASS_EXTENSION, (unsigned)diag::Severity::Ignored, "#warning is a %select{C2x|C++23}0 extension", 673, SFINAE_Suppress, false, false, true, false, 1)
DIAG(ext_pragma_syntax_eod, CLASS_EXTENSION, (unsigned)diag::Severity::Warning, "expected end of directive in pragma", 907, SFINAE_Suppress, false, false, true, false, 1)
DIAG(ext_reserved_user_defined_literal, CLASS_EXTENSION, (unsigned)diag::Severity::Error, "invalid suffix on literal; C++11 requires a space between literal and identifier", 747, SFINAE_Suppress, false, false, true, false, 1)
DIAG(ext_string_too_long, CLASS_EXTENSION, (unsigned)diag::Severity::Ignored, "string literal of length %0 exceeds maximum length %1 that %select{C90|ISO C99|C++}2 compilers are required to support", 655, SFINAE_Suppress, false, false, true, false, 1)
DIAG(ext_token_used, CLASS_EXTENSION, (unsigned)diag::Severity::Ignored, "extension used", 467, SFINAE_Suppress, false, false, true, false, 1)
DIAG(ext_unicode_whitespace, CLASS_EXTENSION, (unsigned)diag::Severity::Warning, "treating Unicode character as whitespace", 897, SFINAE_Suppress, false, false, true, false, 1)
DIAG(ext_unknown_escape, CLASS_EXTENSION, (unsigned)diag::Severity::Warning, "unknown escape sequence '\\%0'", 906, SFINAE_Suppress, false, false, true, false, 1)
DIAG(ext_unterminated_char_or_string, CLASS_EXTENSION, (unsigned)diag::Severity::Warning, "missing terminating %select{'|'\"'}0 character", 457, SFINAE_Suppress, false, false, true, false, 1)
DIAG(ext_variadic_macro, CLASS_EXTENSION, (unsigned)diag::Severity::Ignored, "variadic macros are a C99 feature", 963, SFINAE_Suppress, false, false, true, false, 1)
DIAG(note_header_guard, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "%0 is defined here; did you mean %1?", 0, SFINAE_Suppress, false, false, true, false, 1)
DIAG(note_implicit_top_level_module_import_here, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "submodule of top-level module '%0' implicitly imported here", 0, SFINAE_Suppress, false, false, true, false, 1)
DIAG(note_init_list_at_beginning_of_macro_argument, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "cannot use initializer list at the beginning of a macro argument", 0, SFINAE_Suppress, false, false, true, false, 1)
DIAG(note_invalid_ucn_name_candidate, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "did you mean %0 ('%2' U+%1)?", 0, SFINAE_Suppress, false, false, true, false, 1)
DIAG(note_invalid_ucn_name_loose_matching, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "characters names in Unicode escape sequences are sensitive to case and whitespaces", 0, SFINAE_Suppress, false, false, true, false, 1)
DIAG(note_macro_expansion_here, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "expansion of macro %0 requested here", 0, SFINAE_Suppress, false, false, true, false, 1)
DIAG(note_macro_here, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "macro %0 defined here", 0, SFINAE_Suppress, false, false, true, false, 1)
DIAG(note_mmap_add_framework_keyword, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "use 'framework module' to declare module '%0'", 0, SFINAE_Suppress, false, false, true, false, 1)
DIAG(note_mmap_lbrace_match, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "to match this '{'", 0, SFINAE_Suppress, false, false, true, false, 1)
DIAG(note_mmap_lsquare_match, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "to match this ']'", 0, SFINAE_Suppress, false, false, true, false, 1)
DIAG(note_mmap_prev_definition, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "previously defined here", 0, SFINAE_Suppress, false, false, true, false, 1)
DIAG(note_mmap_rename_top_level_private_module, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "rename '%0' to ensure it can be found by name", 0, SFINAE_Suppress, false, false, true, false, 1)
DIAG(note_pp_ambiguous_macro_chosen, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "expanding this definition of %0", 0, SFINAE_Suppress, false, false, true, false, 1)
DIAG(note_pp_ambiguous_macro_other, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "other definition of %0", 0, SFINAE_Suppress, false, false, true, false, 1)
DIAG(note_pp_framework_without_header, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "did not find header '%0' in framework '%1' (loaded from '%2')", 0, SFINAE_Suppress, false, false, true, false, 1)
DIAG(note_pp_macro_annotation, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "macro marked '%select{deprecated|restrict_expansion|final}0' here", 0, SFINAE_Suppress, false, false, true, false, 1)
DIAG(note_pp_module_begin_here, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "entering module '%0' due to this pragma", 0, SFINAE_Suppress, false, false, true, false, 1)
DIAG(note_suggest_parens_for_macro, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "parentheses are required around macro argument containing braced initializer list", 0, SFINAE_Suppress, false, false, true, false, 1)
DIAG(note_ucn_four_not_eight, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "did you mean to use '\\u'?", 0, SFINAE_Suppress, false, false, true, false, 1)
DIAG(null_in_char_or_string, CLASS_WARNING, (unsigned)diag::Severity::Warning, "null character(s) preserved in %select{char|string}0 literal", 587, SFINAE_Suppress, false, false, true, false, 1)
DIAG(null_in_file, CLASS_WARNING, (unsigned)diag::Severity::Warning, "null character ignored", 587, SFINAE_Suppress, false, false, true, false, 1)
DIAG(pp_disabled_macro_expansion, CLASS_WARNING, (unsigned)diag::Severity::Ignored, "disabled expansion of recursive macro", 245, SFINAE_Suppress, false, false, true, false, 1)
DIAG(pp_err_elif_after_else, CLASS_ERROR, (unsigned)diag::Severity::Error, "%select{#elif|#elifdef|#elifndef}0 after #else", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(pp_err_elif_without_if, CLASS_ERROR, (unsigned)diag::Severity::Error, "%select{#elif|#elifdef|#elifndef}0 without #if", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(pp_err_else_after_else, CLASS_ERROR, (unsigned)diag::Severity::Error, "#else after #else", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(pp_err_else_without_if, CLASS_ERROR, (unsigned)diag::Severity::Error, "#else without #if", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(pp_hash_warning, CLASS_WARNING, (unsigned)diag::Severity::Warning, "%0", 2, SFINAE_Suppress, false, true, true, false, 24)
DIAG(pp_include_macros_out_of_predefines, CLASS_ERROR, (unsigned)diag::Severity::Error, "the #__include_macros directive is only for internal use by -imacros", 0, SFINAE_SubstitutionFailure, false, true, true, false, 1)
DIAG(pp_include_next_absolute_path, CLASS_WARNING, (unsigned)diag::Severity::Warning, "#include_next in file found relative to primary source file or found by absolute path; will search from start of include path", 410, SFINAE_Suppress, false, false, true, false, 1)
DIAG(pp_include_next_in_primary, CLASS_WARNING, (unsigned)diag::Severity::Warning, "#include_next in primary source file; will search from start of include path", 411, SFINAE_Suppress, false, false, true, false, 1)
DIAG(pp_invalid_string_literal, CLASS_WARNING, (unsigned)diag::Severity::Warning, "invalid string literal, ignoring final '\\'", 0, SFINAE_Suppress, false, false, true, false, 1)
DIAG(pp_macro_not_used, CLASS_WARNING, (unsigned)diag::Severity::Ignored, "macro is not used", 949, SFINAE_Suppress, false, false, true, false, 1)
DIAG(pp_nonportable_path, CLASS_WARNING, (unsigned)diag::Severity::Warning, "non-portable path to file '%0'; specified path differs in case from file name on disk", 580, SFINAE_Suppress, false, false, true, false, 1)
DIAG(pp_nonportable_system_path, CLASS_WARNING, (unsigned)diag::Severity::Ignored, "non-portable path to file '%0'; specified path differs in case from file name on disk", 581, SFINAE_Suppress, false, false, true, false, 1)
DIAG(pp_out_of_date_dependency, CLASS_WARNING, (unsigned)diag::Severity::Warning, "current file is older than dependency %0", 0, SFINAE_Suppress, false, false, true, false, 1)
DIAG(pp_poisoning_existing_macro, CLASS_WARNING, (unsigned)diag::Severity::Warning, "poisoning existing macro", 0, SFINAE_Suppress, false, false, true, false, 1)
DIAG(pp_pragma_once_in_main_file, CLASS_WARNING, (unsigned)diag::Severity::Warning, "#pragma once in main file", 689, SFINAE_Suppress, false, false, true, false, 1)
DIAG(pp_pragma_sysheader_in_main_file, CLASS_WARNING, (unsigned)diag::Severity::Warning, "#pragma system_header ignored in main file", 692, SFINAE_Suppress, false, false, true, false, 1)
DIAG(remark_pp_include_directive_modular_translation, CLASS_REMARK, (unsigned)diag::Severity::Ignored, "treating #%select{include|import|include_next|__include_macros}0 as an import of module '%1'", 552, SFINAE_Suppress, false, false, true, false, 1)
DIAG(remark_pp_search_path_usage, CLASS_REMARK, (unsigned)diag::Severity::Ignored, "search path used: '%0'", 760, SFINAE_Suppress, false, false, true, false, 1)
DIAG(trigraph_converted, CLASS_WARNING, (unsigned)diag::Severity::Warning, "trigraph converted to '%0' character", 872, SFINAE_Suppress, false, false, true, false, 1)
DIAG(trigraph_ends_block_comment, CLASS_WARNING, (unsigned)diag::Severity::Warning, "trigraph ends block comment", 872, SFINAE_Suppress, false, false, true, false, 1)
DIAG(trigraph_ignored, CLASS_WARNING, (unsigned)diag::Severity::Warning, "trigraph ignored", 872, SFINAE_Suppress, false, false, true, false, 1)
DIAG(trigraph_ignored_block_comment, CLASS_WARNING, (unsigned)diag::Severity::Warning, "ignored trigraph would end block comment", 872, SFINAE_Suppress, false, false, true, false, 1)
DIAG(warn_bad_character_encoding, CLASS_EXTENSION, (unsigned)diag::Severity::Warning, "illegal character encoding in character literal", 458, SFINAE_Suppress, false, false, true, false, 1)
DIAG(warn_bad_string_encoding, CLASS_EXTENSION, (unsigned)diag::Severity::Warning, "illegal character encoding in string literal", 458, SFINAE_Suppress, false, false, true, false, 1)
DIAG(warn_c2x_compat_digit_separator, CLASS_WARNING, (unsigned)diag::Severity::Ignored, "digit separators are incompatible with C standards before C2x", 706, SFINAE_Suppress, false, false, true, false, 1)
DIAG(warn_c2x_compat_literal_ucn_control_character, CLASS_WARNING, (unsigned)diag::Severity::Ignored, "universal character name referring to a control character is incompatible with C standards before C2x", 706, SFINAE_Suppress, false, false, true, false, 1)
DIAG(warn_c2x_compat_literal_ucn_escape_basic_scs, CLASS_WARNING, (unsigned)diag::Severity::Ignored, "specifying character '%0' with a universal character name is incompatible with C standards before C2x", 706, SFINAE_Suppress, false, false, true, false, 1)
DIAG(warn_c2x_compat_pp_directive, CLASS_WARNING, (unsigned)diag::Severity::Ignored, "use of a '#%select{<BUG IF SEEN>|elifdef|elifndef}0' directive is incompatible with C standards before C2x", 706, SFINAE_Suppress, false, false, true, false, 1)
DIAG(warn_c2x_compat_warning_directive, CLASS_WARNING, (unsigned)diag::Severity::Ignored, "#warning is incompatible with C standards before C2x", 706, SFINAE_Suppress, false, false, true, false, 1)
DIAG(warn_c2x_keyword, CLASS_WARNING, (unsigned)diag::Severity::Ignored, "'%0' is a keyword in C2x", 141, SFINAE_Suppress, false, false, true, false, 1)
DIAG(warn_c99_compat_unicode_id, CLASS_WARNING, (unsigned)diag::Severity::Ignored, "%select{using this character in an identifier|starting an identifier with this character}0 is incompatible with C99", 143, SFINAE_Suppress, false, false, true, false, 1)
DIAG(warn_c99_compat_unicode_literal, CLASS_WARNING, (unsigned)diag::Severity::Ignored, "unicode literals are incompatible with C99", 143, SFINAE_Suppress, false, false, true, false, 1)
DIAG(warn_c99_keyword, CLASS_WARNING, (unsigned)diag::Severity::Ignored, "'%0' is a keyword in C99", 143, SFINAE_Suppress, false, false, true, false, 1)
DIAG(warn_char_constant_too_large, CLASS_WARNING, (unsigned)diag::Severity::Warning, "character constant too long for its type", 0, SFINAE_Suppress, false, false, true, false, 1)
DIAG(warn_cxx11_compat_binary_literal, CLASS_WARNING, (unsigned)diag::Severity::Ignored, "binary integer literals are incompatible with C++ standards before C++14", 132, SFINAE_Suppress, false, false, true, false, 1)
DIAG(warn_cxx11_compat_digit_separator, CLASS_WARNING, (unsigned)diag::Severity::Ignored, "digit separators are incompatible with C++ standards before C++14", 694, SFINAE_Suppress, false, false, true, false, 1)
DIAG(warn_cxx11_compat_reserved_user_defined_literal, CLASS_WARNING, (unsigned)diag::Severity::Ignored, "identifier after literal will be treated as a reserved user-defined literal suffix in C++11", 94, SFINAE_Suppress, false, false, true, false, 1)
DIAG(warn_cxx11_compat_user_defined_literal, CLASS_WARNING, (unsigned)diag::Severity::Ignored, "identifier after literal will be treated as a user-defined literal suffix in C++11", 91, SFINAE_Suppress, false, false, true, false, 1)
DIAG(warn_cxx11_keyword, CLASS_WARNING, (unsigned)diag::Severity::Ignored, "'%0' is a keyword in C++11", 91, SFINAE_Suppress, false, false, true, false, 1)
DIAG(warn_cxx14_compat_u8_character_literal, CLASS_WARNING, (unsigned)diag::Severity::Ignored, "unicode literals are incompatible with C++ standards before C++17", 696, SFINAE_Suppress, false, false, true, false, 1)
DIAG(warn_cxx17_compat_missing_varargs_arg, CLASS_WARNING, (unsigned)diag::Severity::Ignored, "passing no argument for the '...' parameter of a variadic macro is incompatible with C++ standards before C++20", 698, SFINAE_Suppress, false, false, true, false, 1)
DIAG(warn_cxx17_compat_spaceship, CLASS_WARNING, (unsigned)diag::Severity::Ignored, "'<=>' operator is incompatible with C++ standards before C++20", 698, SFINAE_Suppress, false, false, true, false, 1)
DIAG(warn_cxx17_hex_literal, CLASS_WARNING, (unsigned)diag::Severity::Ignored, "hexadecimal floating literals are incompatible with C++ standards before C++17", 697, SFINAE_Suppress, false, false, true, false, 1)
DIAG(warn_cxx20_compat_spaceship, CLASS_WARNING, (unsigned)diag::Severity::Warning, "'<=>' is a single token in C++20; add a space to avoid a change in behavior", 115, SFINAE_Suppress, false, false, true, false, 1)
DIAG(warn_cxx20_keyword, CLASS_WARNING, (unsigned)diag::Severity::Ignored, "'%0' is a keyword in C++20", 115, SFINAE_Suppress, false, false, true, false, 1)
DIAG(warn_cxx23_compat_pp_directive, CLASS_WARNING, (unsigned)diag::Severity::Ignored, "use of a '#%select{<BUG IF SEEN>|elifdef|elifndef}0' directive is incompatible with C++ standards before C++23", 700, SFINAE_Suppress, false, false, true, false, 1)
DIAG(warn_cxx23_compat_warning_directive, CLASS_WARNING, (unsigned)diag::Severity::Ignored, "#warning is incompatible with C++ standards before C++23", 700, SFINAE_Suppress, false, false, true, false, 1)
DIAG(warn_cxx23_delimited_escape_sequence, CLASS_WARNING, (unsigned)diag::Severity::Ignored, "%select{delimited|named}0 escape sequences are incompatible with C++ standards before C++23", 700, SFINAE_Suppress, false, false, true, false, 1)
DIAG(warn_cxx98_compat_empty_fnmacro_arg, CLASS_WARNING, (unsigned)diag::Severity::Ignored, "empty macro arguments are incompatible with C++98", 138, SFINAE_Suppress, false, false, true, false, 1)
DIAG(warn_cxx98_compat_less_colon_colon, CLASS_WARNING, (unsigned)diag::Severity::Ignored, "'<::' is treated as digraph '<:' (aka '[') followed by ':' in C++98", 134, SFINAE_Suppress, false, false, true, false, 1)
DIAG(warn_cxx98_compat_literal_ucn_control_character, CLASS_WARNING, (unsigned)diag::Severity::Ignored, "universal character name referring to a control character is incompatible with C++98", 134, SFINAE_Suppress, false, false, true, false, 1)
DIAG(warn_cxx98_compat_literal_ucn_escape_basic_scs, CLASS_WARNING, (unsigned)diag::Severity::Ignored, "specifying character '%0' with a universal character name is incompatible with C++98", 134, SFINAE_Suppress, false, false, true, false, 1)
DIAG(warn_cxx98_compat_no_newline_eof, CLASS_WARNING, (unsigned)diag::Severity::Ignored, "C++98 requires newline at end of file", 138, SFINAE_Suppress, false, false, true, false, 1)
DIAG(warn_cxx98_compat_pp_line_too_big, CLASS_WARNING, (unsigned)diag::Severity::Ignored, "#line number greater than 32767 is incompatible with C++98", 138, SFINAE_Suppress, false, false, true, false, 1)
DIAG(warn_cxx98_compat_raw_string_literal, CLASS_WARNING, (unsigned)diag::Severity::Ignored, "raw string literals are incompatible with C++98", 134, SFINAE_Suppress, false, false, true, false, 1)
DIAG(warn_cxx98_compat_unicode_literal, CLASS_WARNING, (unsigned)diag::Severity::Ignored, "unicode literals are incompatible with C++98", 134, SFINAE_Suppress, false, false, true, false, 1)
DIAG(warn_cxx98_compat_variadic_macro, CLASS_WARNING, (unsigned)diag::Severity::Ignored, "variadic macros are incompatible with C++98", 138, SFINAE_Suppress, false, false, true, false, 1)
DIAG(warn_defined_in_function_type_macro, CLASS_EXTENSION, (unsigned)diag::Severity::Ignored, "macro expansion producing 'defined' has undefined behavior", 293, SFINAE_Suppress, false, false, true, false, 1)
DIAG(warn_defined_in_object_type_macro, CLASS_WARNING, (unsigned)diag::Severity::Warning, "macro expansion producing 'defined' has undefined behavior", 293, SFINAE_Suppress, false, false, true, false, 1)
DIAG(warn_delimited_ucn_empty, CLASS_WARNING, (unsigned)diag::Severity::Warning, "empty delimited universal character name; treating as '\\' '%0' '{' '}'", 895, SFINAE_Suppress, false, false, true, false, 1)
DIAG(warn_delimited_ucn_incomplete, CLASS_WARNING, (unsigned)diag::Severity::Warning, "incomplete delimited universal character name; treating as '\\' '%0' '{' identifier", 895, SFINAE_Suppress, false, false, true, false, 1)
DIAG(warn_four_char_character_literal, CLASS_WARNING, (unsigned)diag::Severity::Ignored, "multi-character character constant", 329, SFINAE_Suppress, false, false, true, false, 1)
DIAG(warn_framework_include_private_from_public, CLASS_WARNING, (unsigned)diag::Severity::Warning, "public framework header includes private framework header '%0'", 333, SFINAE_Suppress, false, false, true, false, 1)
DIAG(warn_has_warning_invalid_option, CLASS_EXTENSION, (unsigned)diag::Severity::Warning, "__has_warning expected option name (e.g. \"-Wundef\")", 481, SFINAE_Suppress, false, false, true, false, 1)
DIAG(warn_header_guard, CLASS_WARNING, (unsigned)diag::Severity::Warning, "%0 is used as a header guard here, followed by #define of a different macro", 378, SFINAE_Suppress, false, false, true, false, 1)
DIAG(warn_invalid_utf8_in_comment, CLASS_EXTENSION, (unsigned)diag::Severity::Ignored, "invalid UTF-8 in comment", 462, SFINAE_Suppress, false, false, true, false, 1)
DIAG(warn_missing_whitespace_after_macro_name, CLASS_WARNING, (unsigned)diag::Severity::Warning, "whitespace recommended after macro name", 0, SFINAE_Suppress, false, false, true, false, 1)
DIAG(warn_mmap_incomplete_framework_module_declaration, CLASS_WARNING, (unsigned)diag::Severity::Warning, "skipping '%0' because module declaration of '%1' lacks the 'framework' qualifier", 421, SFINAE_Suppress, false, false, true, false, 1)
DIAG(warn_mmap_mismatched_private_module_name, CLASS_WARNING, (unsigned)diag::Severity::Warning, "expected canonical name for private module '%0'", 712, SFINAE_Suppress, false, false, true, false, 1)
DIAG(warn_mmap_mismatched_private_submodule, CLASS_WARNING, (unsigned)diag::Severity::Warning, "private submodule '%0' in private module map, expected top-level module", 712, SFINAE_Suppress, false, false, true, false, 1)
DIAG(warn_mmap_redundant_export_as, CLASS_WARNING, (unsigned)diag::Severity::Warning, "module '%0' already re-exported as '%1'", 712, SFINAE_Suppress, false, false, true, false, 1)
DIAG(warn_mmap_umbrella_dir_not_found, CLASS_WARNING, (unsigned)diag::Severity::Warning, "umbrella directory '%0' not found", 425, SFINAE_Suppress, false, false, true, false, 1)
DIAG(warn_mmap_unknown_attribute, CLASS_WARNING, (unsigned)diag::Severity::Warning, "unknown attribute '%0'", 384, SFINAE_Suppress, false, false, true, false, 1)
DIAG(warn_module_conflict, CLASS_WARNING, (unsigned)diag::Severity::Warning, "module '%0' conflicts with already-imported module '%1': %2", 547, SFINAE_Suppress, false, false, true, false, 1)
DIAG(warn_multichar_character_literal, CLASS_WARNING, (unsigned)diag::Severity::Warning, "multi-character character constant", 561, SFINAE_Suppress, false, false, true, false, 1)
DIAG(warn_nested_block_comment, CLASS_WARNING, (unsigned)diag::Severity::Warning, "'/*' within block comment", 162, SFINAE_Suppress, false, false, true, false, 1)
DIAG(warn_no_newline_eof, CLASS_WARNING, (unsigned)diag::Severity::Ignored, "no newline at end of file", 567, SFINAE_Suppress, false, false, true, false, 1)
DIAG(warn_non_modular_include_in_framework_module, CLASS_WARNING, (unsigned)diag::Severity::Ignored, "include of non-modular header inside framework module '%0': '%1'", 573, SFINAE_Suppress, false, false, true, false, 1)
DIAG(warn_non_modular_include_in_module, CLASS_WARNING, (unsigned)diag::Severity::Ignored, "include of non-modular header inside module '%0': '%1'", 574, SFINAE_Suppress, false, false, true, false, 1)
DIAG(warn_pp_ambiguous_macro, CLASS_WARNING, (unsigned)diag::Severity::Warning, "ambiguous expansion of macro %0", 23, SFINAE_Suppress, false, false, true, false, 1)
DIAG(warn_pp_convert_to_positive, CLASS_WARNING, (unsigned)diag::Severity::Warning, "%select{left|right}0 side of operator converted from negative value to unsigned: %1", 0, SFINAE_Suppress, false, false, true, false, 1)
DIAG(warn_pp_date_time, CLASS_WARNING, (unsigned)diag::Severity::Ignored, "expansion of date or time macro is not reproducible", 198, SFINAE_Suppress, false, true, true, false, 1)
DIAG(warn_pp_expr_overflow, CLASS_WARNING, (unsigned)diag::Severity::Warning, "integer overflow in preprocessor expression", 0, SFINAE_Suppress, false, false, true, false, 1)
DIAG(warn_pp_hdrstop_filename_ignored, CLASS_WARNING, (unsigned)diag::Severity::Warning, "#pragma hdrstop filename not supported, /Fp can be used to specify precompiled header filename", 157, SFINAE_Suppress, false, false, true, false, 1)
DIAG(warn_pp_invalid_directive, CLASS_WARNING, (unsigned)diag::Severity::Warning, "invalid preprocessing directive%select{|, did you mean '#%1'?}0", 905, SFINAE_Suppress, false, false, true, false, 1)
DIAG(warn_pp_line_decimal, CLASS_WARNING, (unsigned)diag::Severity::Warning, "%select{#line|GNU line marker}0 directive interprets number as decimal, not octal", 0, SFINAE_Suppress, false, false, true, false, 1)
DIAG(warn_pp_macro_def_mismatch_with_pch, CLASS_WARNING, (unsigned)diag::Severity::Warning, "definition of macro %0 does not match definition in precompiled header", 157, SFINAE_Suppress, false, false, true, false, 1)
DIAG(warn_pp_macro_hides_keyword, CLASS_EXTENSION, (unsigned)diag::Severity::Ignored, "keyword is hidden by macro definition", 465, SFINAE_Suppress, false, false, true, false, 1)
DIAG(warn_pp_macro_is_reserved_id, CLASS_WARNING, (unsigned)diag::Severity::Ignored, "macro name is a reserved identifier", 745, SFINAE_Suppress, false, false, true, false, 1)
DIAG(warn_pp_objc_macro_redef_ignored, CLASS_WARNING, (unsigned)diag::Severity::Warning, "ignoring redefinition of Objective-C qualifier macro", 612, SFINAE_Suppress, false, false, true, false, 1)
DIAG(warn_pp_undef_identifier, CLASS_WARNING, (unsigned)diag::Severity::Ignored, "%0 is not defined, evaluates to 0", 882, SFINAE_Suppress, false, false, true, false, 1)
DIAG(warn_pp_undef_prefix, CLASS_WARNING, (unsigned)diag::Severity::Ignored, "%0 is not defined, evaluates to 0", 883, SFINAE_Suppress, false, false, true, false, 1)
DIAG(warn_pragma_debug_missing_command, CLASS_WARNING, (unsigned)diag::Severity::Warning, "missing debug command", 389, SFINAE_Suppress, false, false, true, false, 1)
DIAG(warn_pragma_debug_unexpected_command, CLASS_WARNING, (unsigned)diag::Severity::Warning, "unexpected debug command '%0'", 389, SFINAE_Suppress, false, false, true, false, 1)
DIAG(warn_pragma_debug_unknown_module, CLASS_WARNING, (unsigned)diag::Severity::Warning, "unknown module '%0'", 389, SFINAE_Suppress, false, false, true, false, 1)
DIAG(warn_pragma_deprecated_macro_use, CLASS_EXTENSION, (unsigned)diag::Severity::Warning, "macro %0 has been marked as deprecated%select{|: %2}1", 236, SFINAE_Suppress, false, false, true, false, 30)
DIAG(warn_pragma_diagnostic_cannot_pop, CLASS_EXTENSION, (unsigned)diag::Severity::Warning, "pragma diagnostic pop could not pop, no matching push", 907, SFINAE_Suppress, false, false, true, false, 1)
DIAG(warn_pragma_diagnostic_invalid, CLASS_EXTENSION, (unsigned)diag::Severity::Warning, "pragma diagnostic expected 'error', 'warning', 'ignored', 'fatal', 'push', or 'pop'", 907, SFINAE_Suppress, false, false, true, false, 1)
DIAG(warn_pragma_diagnostic_invalid_option, CLASS_EXTENSION, (unsigned)diag::Severity::Warning, "pragma diagnostic expected option name (e.g. \"-Wundef\")", 907, SFINAE_Suppress, false, false, true, false, 1)
DIAG(warn_pragma_diagnostic_invalid_token, CLASS_EXTENSION, (unsigned)diag::Severity::Warning, "unexpected token in pragma diagnostic", 907, SFINAE_Suppress, false, false, true, false, 1)
DIAG(warn_pragma_diagnostic_unknown_warning, CLASS_EXTENSION, (unsigned)diag::Severity::Warning, "unknown warning group '%0', ignored", 909, SFINAE_Suppress, false, false, true, false, 1)
DIAG(warn_pragma_exec_charset_expected, CLASS_EXTENSION, (unsigned)diag::Severity::Warning, "#pragma execution_character_set expected '%0'", 907, SFINAE_Suppress, false, false, true, false, 1)
DIAG(warn_pragma_exec_charset_push_invalid, CLASS_EXTENSION, (unsigned)diag::Severity::Warning, "#pragma execution_character_set invalid value '%0', only 'UTF-8' is supported", 907, SFINAE_Suppress, false, false, true, false, 1)
DIAG(warn_pragma_exec_charset_spec_invalid, CLASS_EXTENSION, (unsigned)diag::Severity::Warning, "#pragma execution_character_set expected 'push' or 'pop'", 907, SFINAE_Suppress, false, false, true, false, 1)
DIAG(warn_pragma_final_macro, CLASS_EXTENSION, (unsigned)diag::Severity::Warning, "macro %0 has been marked as final and should not be %select{undefined|redefined}1", 306, SFINAE_Suppress, false, true, true, false, 1)
DIAG(warn_pragma_ignored, CLASS_WARNING, (unsigned)diag::Severity::Ignored, "unknown pragma ignored", 907, SFINAE_Suppress, false, false, true, false, 1)
DIAG(warn_pragma_include_alias_expected, CLASS_EXTENSION, (unsigned)diag::Severity::Warning, "pragma include_alias expected '%0'", 907, SFINAE_Suppress, false, false, true, false, 1)
DIAG(warn_pragma_include_alias_expected_filename, CLASS_EXTENSION, (unsigned)diag::Severity::Warning, "pragma include_alias expected include filename", 907, SFINAE_Suppress, false, false, true, false, 1)
DIAG(warn_pragma_include_alias_mismatch_angle, CLASS_EXTENSION, (unsigned)diag::Severity::Warning, "angle-bracketed include <%0> cannot be aliased to double-quoted include \"%1\"", 907, SFINAE_Suppress, false, false, true, false, 1)
DIAG(warn_pragma_include_alias_mismatch_quote, CLASS_EXTENSION, (unsigned)diag::Severity::Warning, "double-quoted include \"%0\" cannot be aliased to angle-bracketed include <%1>", 907, SFINAE_Suppress, false, false, true, false, 1)
DIAG(warn_pragma_message, CLASS_WARNING, (unsigned)diag::Severity::Warning, "%0", 1, SFINAE_Suppress, true, false, true, false, 33)
DIAG(warn_pragma_pop_macro_no_push, CLASS_WARNING, (unsigned)diag::Severity::Warning, "pragma pop_macro could not pop '%0', no matching push_macro", 389, SFINAE_Suppress, false, false, true, false, 1)
DIAG(warn_pragma_restrict_expansion_macro_use, CLASS_EXTENSION, (unsigned)diag::Severity::Warning, "macro %0 has been marked as unsafe for use in headers%select{|: %2}1", 748, SFINAE_Suppress, false, false, true, false, 1)
DIAG(warn_pragma_warning_expected, CLASS_EXTENSION, (unsigned)diag::Severity::Warning, "#pragma warning expected '%0'", 907, SFINAE_Suppress, false, false, true, false, 1)
DIAG(warn_pragma_warning_expected_number, CLASS_EXTENSION, (unsigned)diag::Severity::Warning, "#pragma warning expected a warning number", 907, SFINAE_Suppress, false, false, true, false, 1)
DIAG(warn_pragma_warning_push_level, CLASS_EXTENSION, (unsigned)diag::Severity::Warning, "#pragma warning(push, level) requires a level between 0 and 4", 907, SFINAE_Suppress, false, false, true, false, 1)
DIAG(warn_pragma_warning_spec_invalid, CLASS_EXTENSION, (unsigned)diag::Severity::Warning, "#pragma warning expected 'push', 'pop', 'default', 'disable', 'error', 'once', 'suppress', 1, 2, 3, or 4", 907, SFINAE_Suppress, false, false, true, false, 1)
DIAG(warn_quoted_include_in_framework_header, CLASS_WARNING, (unsigned)diag::Severity::Ignored, "double-quoted include \"%0\" in framework header, expected angle-bracketed instead", 722, SFINAE_Suppress, false, false, true, false, 1)
DIAG(warn_ucn_escape_incomplete, CLASS_WARNING, (unsigned)diag::Severity::Warning, "incomplete universal character name; treating as '\\' followed by identifier", 895, SFINAE_Suppress, false, false, true, false, 1)
DIAG(warn_ucn_escape_no_digits, CLASS_WARNING, (unsigned)diag::Severity::Warning, "\\%0 used with no following hex digits; treating as '\\' followed by identifier", 895, SFINAE_Suppress, false, false, true, false, 1)
DIAG(warn_ucn_escape_surrogate, CLASS_WARNING, (unsigned)diag::Severity::Warning, "universal character name refers to a surrogate character", 895, SFINAE_Suppress, false, false, true, false, 1)
DIAG(warn_ucn_not_valid_in_c89, CLASS_WARNING, (unsigned)diag::Severity::Warning, "universal character names are only valid in C99 or C++; treating as '\\' followed by identifier", 895, SFINAE_Suppress, false, false, true, false, 1)
DIAG(warn_ucn_not_valid_in_c89_literal, CLASS_EXTENSION, (unsigned)diag::Severity::Warning, "universal character names are only valid in C99 or C++", 895, SFINAE_Suppress, false, false, true, false, 1)
DIAG(warn_uncovered_module_header, CLASS_WARNING, (unsigned)diag::Severity::Warning, "umbrella header for module '%0' does not include header '%1'", 425, SFINAE_Suppress, false, false, true, false, 1)
DIAG(warn_unevaluated_string_prefix, CLASS_WARNING, (unsigned)diag::Severity::Warning, "encoding prefix '%0' on an unevaluated string literal has no effect%select{| and is incompatible with c++2c}1", 461, SFINAE_Suppress, false, false, true, false, 1)
DIAG(warn_use_of_private_header_outside_module, CLASS_WARNING, (unsigned)diag::Severity::Error, "use of private header from outside its module: '%0'", 711, SFINAE_Suppress, false, false, true, false, 1)
DIAG(warn_utf8_symbol_homoglyph, CLASS_WARNING, (unsigned)diag::Severity::Warning, "treating Unicode character <U+%0> as an identifier character rather than as '%1' symbol", 896, SFINAE_Suppress, false, false, true, false, 1)
DIAG(warn_utf8_symbol_zero_width, CLASS_WARNING, (unsigned)diag::Severity::Warning, "identifier contains Unicode character <U+%0> that is invisible in some environments", 898, SFINAE_Suppress, false, false, true, false, 1)
