DIAG_NAME_INDEX(backslash_newline_space)
DIAG_NAME_INDEX(err_32_bit_builtin_64_bit_tgt)
DIAG_NAME_INDEX(err_64_bit_builtin_32_bit_tgt)
DIAG_NAME_INDEX(err__Pragma_malformed)
DIAG_NAME_INDEX(err_abi_tag_on_redeclaration)
DIAG_NAME_INDEX(err_abstract_type_in_decl)
DIAG_NAME_INDEX(err_access)
DIAG_NAME_INDEX(err_access_base_ctor)
DIAG_NAME_INDEX(err_access_ctor)
DIAG_NAME_INDEX(err_access_decl)
DIAG_NAME_INDEX(err_access_dtor)
DIAG_NAME_INDEX(err_access_dtor_base)
DIAG_NAME_INDEX(err_access_dtor_exception)
DIAG_NAME_INDEX(err_access_dtor_field)
DIAG_NAME_INDEX(err_access_dtor_ivar)
DIAG_NAME_INDEX(err_access_dtor_temp)
DIAG_NAME_INDEX(err_access_dtor_var)
DIAG_NAME_INDEX(err_access_dtor_vbase)
DIAG_NAME_INDEX(err_access_field_ctor)
DIAG_NAME_INDEX(err_access_friend_function)
DIAG_NAME_INDEX(err_access_lambda_capture)
DIAG_NAME_INDEX(err_access_specifier_interface)
DIAG_NAME_INDEX(err_addr_ovl_ambiguous)
DIAG_NAME_INDEX(err_addr_ovl_no_qualifier)
DIAG_NAME_INDEX(err_addr_ovl_no_viable)
DIAG_NAME_INDEX(err_addr_ovl_not_func_ptrref)
DIAG_NAME_INDEX(err_address_of_function_with_pass_object_size_params)
DIAG_NAME_INDEX(err_address_of_label_outside_fn)
DIAG_NAME_INDEX(err_address_space_mismatch_templ_inst)
DIAG_NAME_INDEX(err_address_space_qualified_delete)
DIAG_NAME_INDEX(err_address_space_qualified_new)
DIAG_NAME_INDEX(err_addrof_function_constraints_not_satisfied)
DIAG_NAME_INDEX(err_addrof_function_disabled_by_enable_if_attr)
DIAG_NAME_INDEX(err_aix_attr_unsupported_tls_model)
DIAG_NAME_INDEX(err_aix_unsupported_tls_model)
DIAG_NAME_INDEX(err_alias_after_tentative)
DIAG_NAME_INDEX(err_alias_declaration_not_identifier)
DIAG_NAME_INDEX(err_alias_declaration_pack_expansion)
DIAG_NAME_INDEX(err_alias_declaration_specialization)
DIAG_NAME_INDEX(err_alias_is_definition)
DIAG_NAME_INDEX(err_alias_not_supported_on_darwin)
DIAG_NAME_INDEX(err_alias_not_supported_on_nvptx)
DIAG_NAME_INDEX(err_alias_template_extra_headers)
DIAG_NAME_INDEX(err_alias_to_undefined)
DIAG_NAME_INDEX(err_align_value_attribute_argument_not_int)
DIAG_NAME_INDEX(err_alignas_attribute_wrong_decl_type)
DIAG_NAME_INDEX(err_alignas_mismatch)
DIAG_NAME_INDEX(err_alignas_missing_on_definition)
DIAG_NAME_INDEX(err_alignas_underaligned)
DIAG_NAME_INDEX(err_aligned_allocation_unavailable)
DIAG_NAME_INDEX(err_aligned_attribute_argument_not_int)
DIAG_NAME_INDEX(err_alignment_dependent_typedef_name)
DIAG_NAME_INDEX(err_alignment_not_power_of_two)
DIAG_NAME_INDEX(err_alignment_too_big)
DIAG_NAME_INDEX(err_alignment_too_small)
DIAG_NAME_INDEX(err_alignof_member_of_incomplete_type)
DIAG_NAME_INDEX(err_allocation_of_abstract_type)
DIAG_NAME_INDEX(err_altivec_empty_initializer)
DIAG_NAME_INDEX(err_ambiguous_base_to_derived_cast)
DIAG_NAME_INDEX(err_ambiguous_delete_operand)
DIAG_NAME_INDEX(err_ambiguous_derived_to_base_conv)
DIAG_NAME_INDEX(err_ambiguous_destructor)
DIAG_NAME_INDEX(err_ambiguous_inherited_constructor)
DIAG_NAME_INDEX(err_ambiguous_member_multiple_subobject_types)
DIAG_NAME_INDEX(err_ambiguous_member_multiple_subobjects)
DIAG_NAME_INDEX(err_ambiguous_memptr_conv)
DIAG_NAME_INDEX(err_ambiguous_reference)
DIAG_NAME_INDEX(err_ambiguous_suitable_delete_member_function_found)
DIAG_NAME_INDEX(err_ambiguous_tag_hiding)
DIAG_NAME_INDEX(err_analyzer_checker_incompatible_analyzer_option)
DIAG_NAME_INDEX(err_analyzer_checker_option_invalid_input)
DIAG_NAME_INDEX(err_analyzer_checker_option_unknown)
DIAG_NAME_INDEX(err_analyzer_config_invalid_input)
DIAG_NAME_INDEX(err_analyzer_config_multiple_values)
DIAG_NAME_INDEX(err_analyzer_config_no_value)
DIAG_NAME_INDEX(err_analyzer_config_unknown)
DIAG_NAME_INDEX(err_analyzer_not_built_with_z3)
DIAG_NAME_INDEX(err_anon_bitfield_has_negative_width)
DIAG_NAME_INDEX(err_anon_bitfield_member_init)
DIAG_NAME_INDEX(err_anon_bitfield_qualifiers)
DIAG_NAME_INDEX(err_anon_type_definition)
DIAG_NAME_INDEX(err_anonymous_enum_bitfield)
DIAG_NAME_INDEX(err_anonymous_property)
DIAG_NAME_INDEX(err_anonymous_record_bad_member)
DIAG_NAME_INDEX(err_anonymous_record_member_redecl)
DIAG_NAME_INDEX(err_anonymous_record_nonpublic_member)
DIAG_NAME_INDEX(err_anonymous_record_with_function)
DIAG_NAME_INDEX(err_anonymous_record_with_static)
DIAG_NAME_INDEX(err_anonymous_record_with_type)
DIAG_NAME_INDEX(err_anonymous_struct_not_member)
DIAG_NAME_INDEX(err_anonymous_union_not_static)
DIAG_NAME_INDEX(err_anonymous_union_with_storage_spec)
DIAG_NAME_INDEX(err_anyx86_interrupt_attribute)
DIAG_NAME_INDEX(err_anyx86_interrupt_called)
DIAG_NAME_INDEX(err_arc_array_param_no_ownership)
DIAG_NAME_INDEX(err_arc_assign_property_ownership)
DIAG_NAME_INDEX(err_arc_atomic_ownership)
DIAG_NAME_INDEX(err_arc_autoreleasing_capture)
DIAG_NAME_INDEX(err_arc_autoreleasing_var)
DIAG_NAME_INDEX(err_arc_bridge_cast_incompatible)
DIAG_NAME_INDEX(err_arc_bridge_cast_wrong_kind)
DIAG_NAME_INDEX(err_arc_bridge_retain)
DIAG_NAME_INDEX(err_arc_cast_requires_bridge)
DIAG_NAME_INDEX(err_arc_collection_forward)
DIAG_NAME_INDEX(err_arc_convesion_of_weak_unavailable)
DIAG_NAME_INDEX(err_arc_gained_method_convention)
DIAG_NAME_INDEX(err_arc_illegal_explicit_message)
DIAG_NAME_INDEX(err_arc_illegal_method_def)
DIAG_NAME_INDEX(err_arc_illegal_selector)
DIAG_NAME_INDEX(err_arc_inconsistent_property_ownership)
DIAG_NAME_INDEX(err_arc_indirect_no_ownership)
DIAG_NAME_INDEX(err_arc_init_method_unrelated_result_type)
DIAG_NAME_INDEX(err_arc_lost_method_convention)
DIAG_NAME_INDEX(err_arc_may_not_respond)
DIAG_NAME_INDEX(err_arc_method_not_found)
DIAG_NAME_INDEX(err_arc_mismatched_cast)
DIAG_NAME_INDEX(err_arc_multiple_method_decl)
DIAG_NAME_INDEX(err_arc_new_array_without_ownership)
DIAG_NAME_INDEX(err_arc_nolifetime_behavior)
DIAG_NAME_INDEX(err_arc_nonlocal_writeback)
DIAG_NAME_INDEX(err_arc_objc_property_default_assign_on_object)
DIAG_NAME_INDEX(err_arc_perform_selector_retains)
DIAG_NAME_INDEX(err_arc_pseudo_dtor_inconstant_quals)
DIAG_NAME_INDEX(err_arc_receiver_forward_class)
DIAG_NAME_INDEX(err_arc_receiver_forward_instance)
DIAG_NAME_INDEX(err_arc_strong_property_ownership)
DIAG_NAME_INDEX(err_arc_thread_ownership)
DIAG_NAME_INDEX(err_arc_typecheck_convert_incompatible_pointer)
DIAG_NAME_INDEX(err_arc_unsupported_on_runtime)
DIAG_NAME_INDEX(err_arc_unsupported_on_toolchain)
DIAG_NAME_INDEX(err_arc_unsupported_weak_class)
DIAG_NAME_INDEX(err_arc_unused_init_message)
DIAG_NAME_INDEX(err_arc_weak_disabled)
DIAG_NAME_INDEX(err_arc_weak_ivar_access)
DIAG_NAME_INDEX(err_arc_weak_no_runtime)
DIAG_NAME_INDEX(err_arc_weak_unavailable_assign)
DIAG_NAME_INDEX(err_arc_weak_unavailable_property)
DIAG_NAME_INDEX(err_arch_unsupported_isa)
DIAG_NAME_INDEX(err_arcmt_nsinvocation_ownership)
DIAG_NAME_INDEX(err_arg_with_address_space)
DIAG_NAME_INDEX(err_argument_invalid_range)
DIAG_NAME_INDEX(err_argument_not_contiguous_bit_field)
DIAG_NAME_INDEX(err_argument_not_multiple)
DIAG_NAME_INDEX(err_argument_not_power_of_2)
DIAG_NAME_INDEX(err_argument_not_shifted_byte)
DIAG_NAME_INDEX(err_argument_not_shifted_byte_or_xxff)
DIAG_NAME_INDEX(err_argument_required_after_attribute)
DIAG_NAME_INDEX(err_arithmetic_nonfragile_interface)
DIAG_NAME_INDEX(err_arm_invalid_coproc)
DIAG_NAME_INDEX(err_arm_invalid_specialreg)
DIAG_NAME_INDEX(err_array_designator_empty_range)
DIAG_NAME_INDEX(err_array_designator_negative)
DIAG_NAME_INDEX(err_array_designator_non_array)
DIAG_NAME_INDEX(err_array_designator_too_large)
DIAG_NAME_INDEX(err_array_element_alignment)
DIAG_NAME_INDEX(err_array_incomplete_or_sizeless_type)
DIAG_NAME_INDEX(err_array_init_different_type)
DIAG_NAME_INDEX(err_array_init_incompat_wide_string_into_wchar)
DIAG_NAME_INDEX(err_array_init_narrow_string_into_wchar)
DIAG_NAME_INDEX(err_array_init_non_constant_array)
DIAG_NAME_INDEX(err_array_init_not_init_list)
DIAG_NAME_INDEX(err_array_init_plain_string_into_char8_t)
DIAG_NAME_INDEX(err_array_init_utf8_string_into_char)
DIAG_NAME_INDEX(err_array_init_wide_string_into_char)
DIAG_NAME_INDEX(err_array_new_needs_size)
DIAG_NAME_INDEX(err_array_of_abstract_type)
DIAG_NAME_INDEX(err_array_section_does_not_specify_contiguous_storage)
DIAG_NAME_INDEX(err_array_section_does_not_specify_length)
DIAG_NAME_INDEX(err_array_size_ambiguous_conversion)
DIAG_NAME_INDEX(err_array_size_explicit_conversion)
DIAG_NAME_INDEX(err_array_size_incomplete_type)
DIAG_NAME_INDEX(err_array_size_non_int)
DIAG_NAME_INDEX(err_array_size_not_integral)
DIAG_NAME_INDEX(err_array_star_in_function_definition)
DIAG_NAME_INDEX(err_array_star_outside_prototype)
DIAG_NAME_INDEX(err_array_static_not_outermost)
DIAG_NAME_INDEX(err_array_static_outside_prototype)
DIAG_NAME_INDEX(err_array_too_large)
DIAG_NAME_INDEX(err_as_qualified_auto_decl)
DIAG_NAME_INDEX(err_asm_bad_register_type)
DIAG_NAME_INDEX(err_asm_duplicate_qual)
DIAG_NAME_INDEX(err_asm_empty)
DIAG_NAME_INDEX(err_asm_empty_symbolic_operand_name)
DIAG_NAME_INDEX(err_asm_incomplete_type)
DIAG_NAME_INDEX(err_asm_input_duplicate_match)
DIAG_NAME_INDEX(err_asm_invalid_escape)
DIAG_NAME_INDEX(err_asm_invalid_global_var_reg)
DIAG_NAME_INDEX(err_asm_invalid_input_constraint)
DIAG_NAME_INDEX(err_asm_invalid_input_size)
DIAG_NAME_INDEX(err_asm_invalid_lvalue_in_input)
DIAG_NAME_INDEX(err_asm_invalid_lvalue_in_output)
DIAG_NAME_INDEX(err_asm_invalid_operand_number)
DIAG_NAME_INDEX(err_asm_invalid_output_constraint)
DIAG_NAME_INDEX(err_asm_invalid_output_size)
DIAG_NAME_INDEX(err_asm_invalid_type)
DIAG_NAME_INDEX(err_asm_invalid_type_in_input)
DIAG_NAME_INDEX(err_asm_naked_parm_ref)
DIAG_NAME_INDEX(err_asm_naked_this_ref)
DIAG_NAME_INDEX(err_asm_non_addr_value_in_memory_constraint)
DIAG_NAME_INDEX(err_asm_operand_wide_string_literal)
DIAG_NAME_INDEX(err_asm_pmf_through_constraint_not_permitted)
DIAG_NAME_INDEX(err_asm_qualifier_ignored)
DIAG_NAME_INDEX(err_asm_register_size_mismatch)
DIAG_NAME_INDEX(err_asm_tying_incompatible_types)
DIAG_NAME_INDEX(err_asm_unexpected_constraint_alternatives)
DIAG_NAME_INDEX(err_asm_unknown_register_name)
DIAG_NAME_INDEX(err_asm_unknown_symbolic_operand_name)
DIAG_NAME_INDEX(err_asm_unterminated_symbolic_operand_name)
DIAG_NAME_INDEX(err_asm_unwind_and_goto)
DIAG_NAME_INDEX(err_assoc_compatible_types)
DIAG_NAME_INDEX(err_assoc_type_incomplete)
DIAG_NAME_INDEX(err_assoc_type_nonobject)
DIAG_NAME_INDEX(err_assoc_type_variably_modified)
DIAG_NAME_INDEX(err_ast_file_invalid)
DIAG_NAME_INDEX(err_ast_file_not_found)
DIAG_NAME_INDEX(err_ast_file_out_of_date)
DIAG_NAME_INDEX(err_at_defs_cxx)
DIAG_NAME_INDEX(err_at_in_class)
DIAG_NAME_INDEX(err_atdef_nonfragile_interface)
DIAG_NAME_INDEX(err_atimport)
DIAG_NAME_INDEX(err_atomic_builtin_bit_int_prohibit)
DIAG_NAME_INDEX(err_atomic_builtin_cannot_be_const)
DIAG_NAME_INDEX(err_atomic_builtin_ext_int_size)
DIAG_NAME_INDEX(err_atomic_builtin_must_be_pointer)
DIAG_NAME_INDEX(err_atomic_builtin_must_be_pointer_intfltptr)
DIAG_NAME_INDEX(err_atomic_builtin_must_be_pointer_intptr)
DIAG_NAME_INDEX(err_atomic_builtin_pointer_size)
DIAG_NAME_INDEX(err_atomic_exclusive_builtin_pointer_size)
DIAG_NAME_INDEX(err_atomic_load_store_uses_lib)
DIAG_NAME_INDEX(err_atomic_op_has_invalid_synch_scope)
DIAG_NAME_INDEX(err_atomic_op_needs_atomic)
DIAG_NAME_INDEX(err_atomic_op_needs_atomic_int)
DIAG_NAME_INDEX(err_atomic_op_needs_atomic_int_or_fp)
DIAG_NAME_INDEX(err_atomic_op_needs_atomic_int_or_ptr)
DIAG_NAME_INDEX(err_atomic_op_needs_atomic_int_ptr_or_fp)
DIAG_NAME_INDEX(err_atomic_op_needs_non_const_atomic)
DIAG_NAME_INDEX(err_atomic_op_needs_non_const_pointer)
DIAG_NAME_INDEX(err_atomic_op_needs_trivial_copy)
DIAG_NAME_INDEX(err_atomic_property_nontrivial_assign_op)
DIAG_NAME_INDEX(err_atomic_specifier_bad_type)
DIAG_NAME_INDEX(err_atprotocol_protocol)
DIAG_NAME_INDEX(err_attr_cond_never_constant_expr)
DIAG_NAME_INDEX(err_attr_objc_ownership_redundant)
DIAG_NAME_INDEX(err_attr_swift_error_no_error_parameter)
DIAG_NAME_INDEX(err_attr_swift_error_return_type)
DIAG_NAME_INDEX(err_attr_tlsmodel_arg)
DIAG_NAME_INDEX(err_attribute_address_function_type)
DIAG_NAME_INDEX(err_attribute_address_multiple_qualifiers)
DIAG_NAME_INDEX(err_attribute_address_space_negative)
DIAG_NAME_INDEX(err_attribute_address_space_too_high)
DIAG_NAME_INDEX(err_attribute_aligned_too_great)
DIAG_NAME_INDEX(err_attribute_argument_invalid)
DIAG_NAME_INDEX(err_attribute_argument_is_zero)
DIAG_NAME_INDEX(err_attribute_argument_n_type)
DIAG_NAME_INDEX(err_attribute_argument_out_of_bounds)
DIAG_NAME_INDEX(err_attribute_argument_out_of_bounds_extra_info)
DIAG_NAME_INDEX(err_attribute_argument_out_of_range)
DIAG_NAME_INDEX(err_attribute_argument_parm_pack_not_supported)
DIAG_NAME_INDEX(err_attribute_argument_type)
DIAG_NAME_INDEX(err_attribute_arm_builtin_alias)
DIAG_NAME_INDEX(err_attribute_arm_feature_sve_bits_unsupported)
DIAG_NAME_INDEX(err_attribute_arm_mve_polymorphism)
DIAG_NAME_INDEX(err_attribute_bad_neon_vector_size)
DIAG_NAME_INDEX(err_attribute_bad_rvv_vector_size)
DIAG_NAME_INDEX(err_attribute_bad_sve_vector_size)
DIAG_NAME_INDEX(err_attribute_bounds_for_function)
DIAG_NAME_INDEX(err_attribute_builtin_alias)
DIAG_NAME_INDEX(err_attribute_cleanup_arg_not_function)
DIAG_NAME_INDEX(err_attribute_cleanup_func_arg_incompatible_type)
DIAG_NAME_INDEX(err_attribute_cleanup_func_must_take_one_arg)
DIAG_NAME_INDEX(err_attribute_dll_ambiguous_default_ctor)
DIAG_NAME_INDEX(err_attribute_dll_deleted)
DIAG_NAME_INDEX(err_attribute_dll_lambda)
DIAG_NAME_INDEX(err_attribute_dll_member_of_dll_class)
DIAG_NAME_INDEX(err_attribute_dll_not_extern)
DIAG_NAME_INDEX(err_attribute_dll_redeclaration)
DIAG_NAME_INDEX(err_attribute_dll_thread_local)
DIAG_NAME_INDEX(err_attribute_dllimport_data_definition)
DIAG_NAME_INDEX(err_attribute_dllimport_function_definition)
DIAG_NAME_INDEX(err_attribute_dllimport_function_specialization_definition)
DIAG_NAME_INDEX(err_attribute_dllimport_static_field_definition)
DIAG_NAME_INDEX(err_attribute_integers_only)
DIAG_NAME_INDEX(err_attribute_invalid_argument)
DIAG_NAME_INDEX(err_attribute_invalid_bitint_vector_type)
DIAG_NAME_INDEX(err_attribute_invalid_implicit_this_argument)
DIAG_NAME_INDEX(err_attribute_invalid_matrix_type)
DIAG_NAME_INDEX(err_attribute_invalid_on_decl)
DIAG_NAME_INDEX(err_attribute_invalid_rvv_type)
DIAG_NAME_INDEX(err_attribute_invalid_size)
DIAG_NAME_INDEX(err_attribute_invalid_sve_type)
DIAG_NAME_INDEX(err_attribute_invalid_vector_type)
DIAG_NAME_INDEX(err_attribute_missing_on_first_decl)
DIAG_NAME_INDEX(err_attribute_multiple_objc_gc)
DIAG_NAME_INDEX(err_attribute_no_builtin_on_defaulted_deleted_function)
DIAG_NAME_INDEX(err_attribute_no_builtin_on_non_definition)
DIAG_NAME_INDEX(err_attribute_no_builtin_wildcard_or_builtin_name)
DIAG_NAME_INDEX(err_attribute_no_member_function)
DIAG_NAME_INDEX(err_attribute_no_member_pointers)
DIAG_NAME_INDEX(err_attribute_not_clinkage)
DIAG_NAME_INDEX(err_attribute_not_import_attr)
DIAG_NAME_INDEX(err_attribute_not_module_attr)
DIAG_NAME_INDEX(err_attribute_not_supported_in_lang)
DIAG_NAME_INDEX(err_attribute_not_supported_on_arch)
DIAG_NAME_INDEX(err_attribute_not_type_attr)
DIAG_NAME_INDEX(err_attribute_only_once_per_parameter)
DIAG_NAME_INDEX(err_attribute_output_parameter)
DIAG_NAME_INDEX(err_attribute_overloadable_mismatch)
DIAG_NAME_INDEX(err_attribute_overloadable_multiple_unmarked_overloads)
DIAG_NAME_INDEX(err_attribute_overloadable_no_prototype)
DIAG_NAME_INDEX(err_attribute_parameter_types)
DIAG_NAME_INDEX(err_attribute_pointers_only)
DIAG_NAME_INDEX(err_attribute_preferred_name_arg_invalid)
DIAG_NAME_INDEX(err_attribute_regparm_invalid_number)
DIAG_NAME_INDEX(err_attribute_regparm_wrong_platform)
DIAG_NAME_INDEX(err_attribute_requires_arguments)
DIAG_NAME_INDEX(err_attribute_requires_opencl_version)
DIAG_NAME_INDEX(err_attribute_requires_positive_integer)
DIAG_NAME_INDEX(err_attribute_riscv_rvv_bits_unsupported)
DIAG_NAME_INDEX(err_attribute_section_invalid_for_target)
DIAG_NAME_INDEX(err_attribute_selectany_non_extern_data)
DIAG_NAME_INDEX(err_attribute_sentinel_less_than_zero)
DIAG_NAME_INDEX(err_attribute_sentinel_not_zero_or_one)
DIAG_NAME_INDEX(err_attribute_size_too_large)
DIAG_NAME_INDEX(err_attribute_sizeless_type)
DIAG_NAME_INDEX(err_attribute_too_few_arguments)
DIAG_NAME_INDEX(err_attribute_too_many_arguments)
DIAG_NAME_INDEX(err_attribute_unsupported)
DIAG_NAME_INDEX(err_attribute_uuid_malformed_guid)
DIAG_NAME_INDEX(err_attribute_vecreturn_only_pod_record)
DIAG_NAME_INDEX(err_attribute_vecreturn_only_vector_member)
DIAG_NAME_INDEX(err_attribute_weak_static)
DIAG_NAME_INDEX(err_attribute_weakref_not_global_context)
DIAG_NAME_INDEX(err_attribute_weakref_not_static)
DIAG_NAME_INDEX(err_attribute_weakref_without_alias)
DIAG_NAME_INDEX(err_attribute_webassembly_funcref)
DIAG_NAME_INDEX(err_attribute_wrong_decl_type)
DIAG_NAME_INDEX(err_attribute_wrong_decl_type_str)
DIAG_NAME_INDEX(err_attribute_wrong_number_arguments)
DIAG_NAME_INDEX(err_attribute_wrong_number_arguments_for)
DIAG_NAME_INDEX(err_attribute_zero_size)
DIAG_NAME_INDEX(err_attributes_are_not_compatible)
DIAG_NAME_INDEX(err_attributes_misplaced)
DIAG_NAME_INDEX(err_attributes_not_allowed)
DIAG_NAME_INDEX(err_auto_bitfield)
DIAG_NAME_INDEX(err_auto_different_deductions)
DIAG_NAME_INDEX(err_auto_expr_deduction_failure)
DIAG_NAME_INDEX(err_auto_expr_init_multiple_expressions)
DIAG_NAME_INDEX(err_auto_expr_init_no_expression)
DIAG_NAME_INDEX(err_auto_expr_init_paren_braces)
DIAG_NAME_INDEX(err_auto_fn_deduction_failure)
DIAG_NAME_INDEX(err_auto_fn_different_deductions)
DIAG_NAME_INDEX(err_auto_fn_no_return_but_not_auto)
DIAG_NAME_INDEX(err_auto_fn_return_init_list)
DIAG_NAME_INDEX(err_auto_fn_return_void_but_not_auto)
DIAG_NAME_INDEX(err_auto_fn_used_before_defined)
DIAG_NAME_INDEX(err_auto_fn_virtual)
DIAG_NAME_INDEX(err_auto_inconsistent_deduction)
DIAG_NAME_INDEX(err_auto_init_list_from_c)
DIAG_NAME_INDEX(err_auto_missing_trailing_return)
DIAG_NAME_INDEX(err_auto_new_ctor_multiple_expressions)
DIAG_NAME_INDEX(err_auto_new_deduction_failure)
DIAG_NAME_INDEX(err_auto_new_requires_ctor_arg)
DIAG_NAME_INDEX(err_auto_non_deduced_not_alone)
DIAG_NAME_INDEX(err_auto_not_allowed)
DIAG_NAME_INDEX(err_auto_not_allowed_var_inst)
DIAG_NAME_INDEX(err_auto_var_deduction_failure)
DIAG_NAME_INDEX(err_auto_var_deduction_failure_from_init_list)
DIAG_NAME_INDEX(err_auto_var_init_multiple_expressions)
DIAG_NAME_INDEX(err_auto_var_init_no_expression)
DIAG_NAME_INDEX(err_auto_var_init_paren_braces)
DIAG_NAME_INDEX(err_auto_var_requires_init)
DIAG_NAME_INDEX(err_auto_variable_cannot_appear_in_own_initializer)
DIAG_NAME_INDEX(err_avail_query_expected_platform_name)
DIAG_NAME_INDEX(err_avail_query_unrecognized_platform_name)
DIAG_NAME_INDEX(err_availability_expected_change)
DIAG_NAME_INDEX(err_availability_expected_platform)
DIAG_NAME_INDEX(err_availability_query_repeated_platform)
DIAG_NAME_INDEX(err_availability_query_repeated_star)
DIAG_NAME_INDEX(err_availability_query_wildcard_required)
DIAG_NAME_INDEX(err_availability_redundant)
DIAG_NAME_INDEX(err_availability_unknown_change)
DIAG_NAME_INDEX(err_avx_calling_convention)
DIAG_NAME_INDEX(err_await_suspend_invalid_return_type)
DIAG_NAME_INDEX(err_bad_cast_incomplete)
DIAG_NAME_INDEX(err_bad_category_property_decl)
DIAG_NAME_INDEX(err_bad_character_encoding)
DIAG_NAME_INDEX(err_bad_const_cast_dest)
DIAG_NAME_INDEX(err_bad_cstyle_cast_overload)
DIAG_NAME_INDEX(err_bad_cxx_cast_addr_space_mismatch)
DIAG_NAME_INDEX(err_bad_cxx_cast_bitfield)
DIAG_NAME_INDEX(err_bad_cxx_cast_generic)
DIAG_NAME_INDEX(err_bad_cxx_cast_member_pointer_size)
DIAG_NAME_INDEX(err_bad_cxx_cast_qualifiers_away)
DIAG_NAME_INDEX(err_bad_cxx_cast_rvalue)
DIAG_NAME_INDEX(err_bad_cxx_cast_scalar_to_vector_different_size)
DIAG_NAME_INDEX(err_bad_cxx_cast_unrelated_class)
DIAG_NAME_INDEX(err_bad_cxx_cast_vector_to_scalar_different_size)
DIAG_NAME_INDEX(err_bad_cxx_cast_vector_to_vector_different_size)
DIAG_NAME_INDEX(err_bad_dynamic_cast_not_class)
DIAG_NAME_INDEX(err_bad_dynamic_cast_not_polymorphic)
DIAG_NAME_INDEX(err_bad_dynamic_cast_not_ptr)
DIAG_NAME_INDEX(err_bad_dynamic_cast_not_ref_or_ptr)
DIAG_NAME_INDEX(err_bad_kernel_param_type)
DIAG_NAME_INDEX(err_bad_lvalue_to_rvalue_cast)
DIAG_NAME_INDEX(err_bad_memptr_lhs)
DIAG_NAME_INDEX(err_bad_memptr_rhs)
DIAG_NAME_INDEX(err_bad_multiversion_option)
DIAG_NAME_INDEX(err_bad_new_type)
DIAG_NAME_INDEX(err_bad_parameter_name)
DIAG_NAME_INDEX(err_bad_parameter_name_template_id)
DIAG_NAME_INDEX(err_bad_property_context)
DIAG_NAME_INDEX(err_bad_property_decl)
DIAG_NAME_INDEX(err_bad_receiver_type)
DIAG_NAME_INDEX(err_bad_reinterpret_cast_overload)
DIAG_NAME_INDEX(err_bad_reinterpret_cast_reference)
DIAG_NAME_INDEX(err_bad_reinterpret_cast_small_int)
DIAG_NAME_INDEX(err_bad_rvalue_to_rvalue_cast)
DIAG_NAME_INDEX(err_bad_static_cast_member_pointer_nonmp)
DIAG_NAME_INDEX(err_bad_static_cast_overload)
DIAG_NAME_INDEX(err_bad_static_cast_pointer_nonpointer)
DIAG_NAME_INDEX(err_bad_string_encoding)
DIAG_NAME_INDEX(err_bad_variable_name)
DIAG_NAME_INDEX(err_base_class_has_flexible_array_member)
DIAG_NAME_INDEX(err_base_clause_on_union)
DIAG_NAME_INDEX(err_base_init_direct_and_virtual)
DIAG_NAME_INDEX(err_base_init_does_not_name_class)
DIAG_NAME_INDEX(err_base_must_be_class)
DIAG_NAME_INDEX(err_base_specifier_attribute)
DIAG_NAME_INDEX(err_binding_cannot_appear_in_own_initializer)
DIAG_NAME_INDEX(err_bit_cast_non_trivially_copyable)
DIAG_NAME_INDEX(err_bit_cast_type_size_mismatch)
DIAG_NAME_INDEX(err_bit_int_bad_size)
DIAG_NAME_INDEX(err_bit_int_max_size)
DIAG_NAME_INDEX(err_bitfield_has_negative_width)
DIAG_NAME_INDEX(err_bitfield_has_zero_width)
DIAG_NAME_INDEX(err_bitfield_too_wide)
DIAG_NAME_INDEX(err_bitfield_width_exceeds_type_width)
DIAG_NAME_INDEX(err_block_decl_ref_not_modifiable_lvalue)
DIAG_NAME_INDEX(err_block_extern_cant_init)
DIAG_NAME_INDEX(err_block_on_nonlocal)
DIAG_NAME_INDEX(err_block_on_vm)
DIAG_NAME_INDEX(err_block_return_missing_expr)
DIAG_NAME_INDEX(err_block_returning_array_function)
DIAG_NAME_INDEX(err_blocks_disable)
DIAG_NAME_INDEX(err_bool_redeclaration)
DIAG_NAME_INDEX(err_bound_member_function)
DIAG_NAME_INDEX(err_box_literal_collection)
DIAG_NAME_INDEX(err_bracket_depth_exceeded)
DIAG_NAME_INDEX(err_brackets_go_after_unqualified_id)
DIAG_NAME_INDEX(err_break_not_in_loop_or_switch)
DIAG_NAME_INDEX(err_btf_type_id_not_const)
DIAG_NAME_INDEX(err_builtin_annotation_first_arg)
DIAG_NAME_INDEX(err_builtin_annotation_second_arg)
DIAG_NAME_INDEX(err_builtin_definition)
DIAG_NAME_INDEX(err_builtin_fn_use)
DIAG_NAME_INDEX(err_builtin_func_cast_more_than_one_arg)
DIAG_NAME_INDEX(err_builtin_invalid_arg_type)
DIAG_NAME_INDEX(err_builtin_launder_invalid_arg)
DIAG_NAME_INDEX(err_builtin_longjmp_invalid_val)
DIAG_NAME_INDEX(err_builtin_longjmp_unsupported)
DIAG_NAME_INDEX(err_builtin_matrix_disabled)
DIAG_NAME_INDEX(err_builtin_matrix_invalid_dimension)
DIAG_NAME_INDEX(err_builtin_matrix_pointer_arg_mismatch)
DIAG_NAME_INDEX(err_builtin_matrix_scalar_unsigned_arg)
DIAG_NAME_INDEX(err_builtin_matrix_store_to_const)
DIAG_NAME_INDEX(err_builtin_matrix_stride_too_small)
DIAG_NAME_INDEX(err_builtin_move_forward_unsupported)
DIAG_NAME_INDEX(err_builtin_needs_feature)
DIAG_NAME_INDEX(err_builtin_operator_new_delete_not_usual)
DIAG_NAME_INDEX(err_builtin_pass_in_regs_non_class)
DIAG_NAME_INDEX(err_builtin_redeclare)
DIAG_NAME_INDEX(err_builtin_requires_language)
DIAG_NAME_INDEX(err_builtin_setjmp_unsupported)
DIAG_NAME_INDEX(err_builtin_target_unsupported)
DIAG_NAME_INDEX(err_builtin_x64_aarch64_only)
DIAG_NAME_INDEX(err_c11_noreturn_misplaced)
DIAG_NAME_INDEX(err_c99_array_usage_cxx)
DIAG_NAME_INDEX(err_call_function_incomplete_return)
DIAG_NAME_INDEX(err_call_incomplete_argument)
DIAG_NAME_INDEX(err_call_incomplete_return)
DIAG_NAME_INDEX(err_callback_attribute_argument_unknown)
DIAG_NAME_INDEX(err_callback_attribute_invalid_callee)
DIAG_NAME_INDEX(err_callback_attribute_multiple)
DIAG_NAME_INDEX(err_callback_attribute_no_callee)
DIAG_NAME_INDEX(err_callback_callee_is_variadic)
DIAG_NAME_INDEX(err_callback_callee_no_function_type)
DIAG_NAME_INDEX(err_callback_implicit_this_not_available)
DIAG_NAME_INDEX(err_called_once_attribute_wrong_type)
DIAG_NAME_INDEX(err_cannot_find_suitable_accessor)
DIAG_NAME_INDEX(err_cannot_form_pointer_to_member_of_reference_type)
DIAG_NAME_INDEX(err_cannot_open_file)
DIAG_NAME_INDEX(err_cannot_pass_non_trivial_c_struct_to_vararg)
DIAG_NAME_INDEX(err_cannot_pass_objc_interface_to_vararg)
DIAG_NAME_INDEX(err_cannot_pass_objc_interface_to_vararg_format)
DIAG_NAME_INDEX(err_cannot_pass_to_vararg)
DIAG_NAME_INDEX(err_cannot_pass_to_vararg_format)
DIAG_NAME_INDEX(err_capture_bad_target)
DIAG_NAME_INDEX(err_capture_binding_openmp)
DIAG_NAME_INDEX(err_capture_block_variable)
DIAG_NAME_INDEX(err_capture_default_first)
DIAG_NAME_INDEX(err_capture_default_non_local)
DIAG_NAME_INDEX(err_capture_does_not_name_variable)
DIAG_NAME_INDEX(err_capture_more_than_once)
DIAG_NAME_INDEX(err_capture_non_automatic_variable)
DIAG_NAME_INDEX(err_capture_of_abstract_type)
DIAG_NAME_INDEX(err_capture_of_incomplete_or_sizeless_type)
DIAG_NAME_INDEX(err_carries_dependency_missing_on_first_decl)
DIAG_NAME_INDEX(err_carries_dependency_param_not_function_decl)
DIAG_NAME_INDEX(err_case_not_in_switch)
DIAG_NAME_INDEX(err_cast_from_randomized_struct)
DIAG_NAME_INDEX(err_cast_pointer_from_non_pointer_int)
DIAG_NAME_INDEX(err_cast_pointer_to_non_pointer_int)
DIAG_NAME_INDEX(err_cast_selector_expr)
DIAG_NAME_INDEX(err_catch_incomplete)
DIAG_NAME_INDEX(err_catch_incomplete_ptr)
DIAG_NAME_INDEX(err_catch_incomplete_ref)
DIAG_NAME_INDEX(err_catch_param_not_objc_type)
DIAG_NAME_INDEX(err_catch_rvalue_ref)
DIAG_NAME_INDEX(err_catch_sizeless)
DIAG_NAME_INDEX(err_catch_variably_modified)
DIAG_NAME_INDEX(err_category_forward_interface)
DIAG_NAME_INDEX(err_category_property)
DIAG_NAME_INDEX(err_cc1_round_trip_fail_then_ok)
DIAG_NAME_INDEX(err_cc1_round_trip_mismatch)
DIAG_NAME_INDEX(err_cc1_round_trip_ok_then_fail)
DIAG_NAME_INDEX(err_cc1_unbounded_vscale_min)
DIAG_NAME_INDEX(err_cconv_change)
DIAG_NAME_INDEX(err_cconv_incomplete_param_type)
DIAG_NAME_INDEX(err_cconv_knr)
DIAG_NAME_INDEX(err_cconv_varargs)
DIAG_NAME_INDEX(err_cfstring_literal_not_string_constant)
DIAG_NAME_INDEX(err_character_not_allowed)
DIAG_NAME_INDEX(err_character_not_allowed_identifier)
DIAG_NAME_INDEX(err_character_too_large)
DIAG_NAME_INDEX(err_circular_inheritance)
DIAG_NAME_INDEX(err_class_extension_after_impl)
DIAG_NAME_INDEX(err_class_marked_final_used_as_base)
DIAG_NAME_INDEX(err_class_on_template_template_param)
DIAG_NAME_INDEX(err_class_property_found)
DIAG_NAME_INDEX(err_class_redeclared_with_different_access)
DIAG_NAME_INDEX(err_class_stub_subclassing_mismatch)
DIAG_NAME_INDEX(err_cmse_pi_are_incompatible)
DIAG_NAME_INDEX(err_cocoa_naming_owned_rule)
DIAG_NAME_INDEX(err_collection_expr_type)
DIAG_NAME_INDEX(err_complex_mode_vector_type)
DIAG_NAME_INDEX(err_compound_literal_with_address_space)
DIAG_NAME_INDEX(err_compound_qualified_function_type)
DIAG_NAME_INDEX(err_concept_decls_may_only_appear_in_global_namespace_scope)
DIAG_NAME_INDEX(err_concept_definition_not_identifier)
DIAG_NAME_INDEX(err_concept_extra_headers)
DIAG_NAME_INDEX(err_concept_legacy_bool_keyword)
DIAG_NAME_INDEX(err_concept_no_associated_constraints)
DIAG_NAME_INDEX(err_concept_no_parameters)
DIAG_NAME_INDEX(err_cond_voidptr_arc)
DIAG_NAME_INDEX(err_conditional_ambiguous)
DIAG_NAME_INDEX(err_conditional_ambiguous_ovl)
DIAG_NAME_INDEX(err_conditional_vector_cond_result_mismatch)
DIAG_NAME_INDEX(err_conditional_vector_element_size)
DIAG_NAME_INDEX(err_conditional_vector_has_void)
DIAG_NAME_INDEX(err_conditional_vector_mismatched)
DIAG_NAME_INDEX(err_conditional_vector_operand_type)
DIAG_NAME_INDEX(err_conditional_vector_size)
DIAG_NAME_INDEX(err_conditional_void_nonvoid)
DIAG_NAME_INDEX(err_config_scalar_return)
DIAG_NAME_INDEX(err_conflict_marker)
DIAG_NAME_INDEX(err_conflicting_aliasing_type)
DIAG_NAME_INDEX(err_conflicting_aligned_options)
DIAG_NAME_INDEX(err_conflicting_codeseg_attribute)
DIAG_NAME_INDEX(err_conflicting_ivar_bitwidth)
DIAG_NAME_INDEX(err_conflicting_ivar_name)
DIAG_NAME_INDEX(err_conflicting_ivar_type)
DIAG_NAME_INDEX(err_conflicting_overriding_cc_attributes)
DIAG_NAME_INDEX(err_conflicting_super_class)
DIAG_NAME_INDEX(err_conflicting_types)
DIAG_NAME_INDEX(err_constant_integer_arg_type)
DIAG_NAME_INDEX(err_consteval_override)
DIAG_NAME_INDEX(err_constexpr_body_invalid_stmt)
DIAG_NAME_INDEX(err_constexpr_body_no_return)
DIAG_NAME_INDEX(err_constexpr_dtor)
DIAG_NAME_INDEX(err_constexpr_dtor_subobject)
DIAG_NAME_INDEX(err_constexpr_if_condition_expression_is_not_constant)
DIAG_NAME_INDEX(err_constexpr_invalid_template_arg)
DIAG_NAME_INDEX(err_constexpr_local_var_non_literal_type)
DIAG_NAME_INDEX(err_constexpr_main)
DIAG_NAME_INDEX(err_constexpr_non_literal_param)
DIAG_NAME_INDEX(err_constexpr_non_literal_return)
DIAG_NAME_INDEX(err_constexpr_redecl_mismatch)
DIAG_NAME_INDEX(err_constexpr_return_missing_expr)
DIAG_NAME_INDEX(err_constexpr_static_mem_var_requires_init)
DIAG_NAME_INDEX(err_constexpr_tag)
DIAG_NAME_INDEX(err_constexpr_var_non_literal)
DIAG_NAME_INDEX(err_constexpr_var_requires_const_destruction)
DIAG_NAME_INDEX(err_constexpr_var_requires_const_init)
DIAG_NAME_INDEX(err_constexpr_virtual)
DIAG_NAME_INDEX(err_constexpr_virtual_base)
DIAG_NAME_INDEX(err_constexpr_vla)
DIAG_NAME_INDEX(err_constexpr_wrong_decl_kind)
DIAG_NAME_INDEX(err_constinit_added_too_late)
DIAG_NAME_INDEX(err_constinit_local_variable)
DIAG_NAME_INDEX(err_constrained_non_templated_function)
DIAG_NAME_INDEX(err_constrained_virtual_method)
DIAG_NAME_INDEX(err_constraint_depends_on_self)
DIAG_NAME_INDEX(err_constructor_bad_name)
DIAG_NAME_INDEX(err_constructor_byvalue_arg)
DIAG_NAME_INDEX(err_constructor_cannot_be)
DIAG_NAME_INDEX(err_constructor_redeclared)
DIAG_NAME_INDEX(err_constructor_return_type)
DIAG_NAME_INDEX(err_continuation_class)
DIAG_NAME_INDEX(err_continue_from_cond_var_init)
DIAG_NAME_INDEX(err_continue_not_in_loop)
DIAG_NAME_INDEX(err_conv_function_not_member)
DIAG_NAME_INDEX(err_conv_function_redeclared)
DIAG_NAME_INDEX(err_conv_function_return_type)
DIAG_NAME_INDEX(err_conv_function_to_array)
DIAG_NAME_INDEX(err_conv_function_to_function)
DIAG_NAME_INDEX(err_conv_function_variadic)
DIAG_NAME_INDEX(err_conv_function_with_complex_decl)
DIAG_NAME_INDEX(err_conv_function_with_params)
DIAG_NAME_INDEX(err_convertvector_incompatible_vector)
DIAG_NAME_INDEX(err_convertvector_non_vector)
DIAG_NAME_INDEX(err_convertvector_non_vector_type)
DIAG_NAME_INDEX(err_copy_capture_with_copy_default)
DIAG_NAME_INDEX(err_coro_invalid_addr_of_label)
DIAG_NAME_INDEX(err_coroutine_handle_missing_member)
DIAG_NAME_INDEX(err_coroutine_invalid_func_context)
DIAG_NAME_INDEX(err_coroutine_objc_method)
DIAG_NAME_INDEX(err_coroutine_outside_function)
DIAG_NAME_INDEX(err_coroutine_promise_final_suspend_requires_nothrow)
DIAG_NAME_INDEX(err_coroutine_promise_get_return_object_on_allocation_failure)
DIAG_NAME_INDEX(err_coroutine_promise_incompatible_return_functions)
DIAG_NAME_INDEX(err_coroutine_promise_new_requires_nothrow)
DIAG_NAME_INDEX(err_coroutine_promise_type_incomplete)
DIAG_NAME_INDEX(err_coroutine_promise_unhandled_exception_required)
DIAG_NAME_INDEX(err_coroutine_type_missing_specialization)
DIAG_NAME_INDEX(err_coroutine_unevaluated_context)
DIAG_NAME_INDEX(err_coroutine_unfound_nothrow_new)
DIAG_NAME_INDEX(err_coroutine_unusable_new)
DIAG_NAME_INDEX(err_coroutine_within_handler)
DIAG_NAME_INDEX(err_covariant_return_ambiguous_derived_to_base_conv)
DIAG_NAME_INDEX(err_covariant_return_inaccessible_base)
DIAG_NAME_INDEX(err_covariant_return_incomplete)
DIAG_NAME_INDEX(err_covariant_return_not_derived)
DIAG_NAME_INDEX(err_covariant_return_type_class_type_more_qualified)
DIAG_NAME_INDEX(err_covariant_return_type_different_qualifications)
DIAG_NAME_INDEX(err_cpu_dispatch_mismatch)
DIAG_NAME_INDEX(err_cpu_specific_multiple_defs)
DIAG_NAME_INDEX(err_cpu_unsupported_isa)
DIAG_NAME_INDEX(err_ctor_dtor_returns_void)
DIAG_NAME_INDEX(err_ctor_init_missing_comma)
DIAG_NAME_INDEX(err_ctu_error_opening)
DIAG_NAME_INDEX(err_cuda_device_builtin_surftex_cls_template)
DIAG_NAME_INDEX(err_cuda_device_builtin_surftex_ref_decl)
DIAG_NAME_INDEX(err_cuda_device_exceptions)
DIAG_NAME_INDEX(err_cuda_extern_shared)
DIAG_NAME_INDEX(err_cuda_host_shared)
DIAG_NAME_INDEX(err_cuda_nonstatic_constdev)
DIAG_NAME_INDEX(err_cuda_ovl_target)
DIAG_NAME_INDEX(err_cuda_unattributed_constexpr_cannot_overload_device)
DIAG_NAME_INDEX(err_cuda_vla)
DIAG_NAME_INDEX(err_current_module_name_mismatch)
DIAG_NAME_INDEX(err_cxx11_attribute_forbids_arguments)
DIAG_NAME_INDEX(err_cxx11_attribute_forbids_ellipsis)
DIAG_NAME_INDEX(err_cxx23_size_t_suffix)
DIAG_NAME_INDEX(err_cyclic_alias)
DIAG_NAME_INDEX(err_dangling_member)
DIAG_NAME_INDEX(err_dealloc_bad_result_type)
DIAG_NAME_INDEX(err_decimal_unsupported)
DIAG_NAME_INDEX(err_decl_attribute_invalid_on_stmt)
DIAG_NAME_INDEX(err_decl_negative_array_size)
DIAG_NAME_INDEX(err_declaration_does_not_declare_param)
DIAG_NAME_INDEX(err_declarator_need_ident)
DIAG_NAME_INDEX(err_declspec_after_virtspec)
DIAG_NAME_INDEX(err_declspec_keyword_has_no_effect)
DIAG_NAME_INDEX(err_declspec_thread_on_thread_variable)
DIAG_NAME_INDEX(err_decltype_auto_cannot_be_combined)
DIAG_NAME_INDEX(err_decltype_auto_compound_type)
DIAG_NAME_INDEX(err_decltype_auto_function_declarator_not_declaration)
DIAG_NAME_INDEX(err_decltype_auto_initializer_list)
DIAG_NAME_INDEX(err_decltype_auto_invalid)
DIAG_NAME_INDEX(err_decltype_in_declarator)
DIAG_NAME_INDEX(err_decomp_decl_ambiguous_base)
DIAG_NAME_INDEX(err_decomp_decl_anon_union_member)
DIAG_NAME_INDEX(err_decomp_decl_constraint)
DIAG_NAME_INDEX(err_decomp_decl_context)
DIAG_NAME_INDEX(err_decomp_decl_inaccessible_base)
DIAG_NAME_INDEX(err_decomp_decl_inaccessible_field)
DIAG_NAME_INDEX(err_decomp_decl_lambda)
DIAG_NAME_INDEX(err_decomp_decl_multiple_bases_with_members)
DIAG_NAME_INDEX(err_decomp_decl_not_alone)
DIAG_NAME_INDEX(err_decomp_decl_parens)
DIAG_NAME_INDEX(err_decomp_decl_requires_init)
DIAG_NAME_INDEX(err_decomp_decl_spec)
DIAG_NAME_INDEX(err_decomp_decl_std_tuple_element_not_specialized)
DIAG_NAME_INDEX(err_decomp_decl_std_tuple_size_not_constant)
DIAG_NAME_INDEX(err_decomp_decl_template)
DIAG_NAME_INDEX(err_decomp_decl_type)
DIAG_NAME_INDEX(err_decomp_decl_unbindable_type)
DIAG_NAME_INDEX(err_decomp_decl_wrong_number_bindings)
DIAG_NAME_INDEX(err_decrement_bool)
DIAG_NAME_INDEX(err_deduced_class_template_compound_type)
DIAG_NAME_INDEX(err_deduced_class_template_ctor_ambiguous)
DIAG_NAME_INDEX(err_deduced_class_template_ctor_no_viable)
DIAG_NAME_INDEX(err_deduced_class_template_deleted)
DIAG_NAME_INDEX(err_deduced_class_template_explicit)
DIAG_NAME_INDEX(err_deduced_class_template_incomplete)
DIAG_NAME_INDEX(err_deduced_non_class_template_specialization_type)
DIAG_NAME_INDEX(err_deduced_non_type_template_arg_type_mismatch)
DIAG_NAME_INDEX(err_deduced_return_type)
DIAG_NAME_INDEX(err_deduced_tst)
DIAG_NAME_INDEX(err_deduction_guide_bad_trailing_return_type)
DIAG_NAME_INDEX(err_deduction_guide_defines_function)
DIAG_NAME_INDEX(err_deduction_guide_invalid_specifier)
DIAG_NAME_INDEX(err_deduction_guide_name_not_class_template)
DIAG_NAME_INDEX(err_deduction_guide_no_trailing_return_type)
DIAG_NAME_INDEX(err_deduction_guide_redeclared)
DIAG_NAME_INDEX(err_deduction_guide_specialized)
DIAG_NAME_INDEX(err_deduction_guide_template_not_deducible)
DIAG_NAME_INDEX(err_deduction_guide_with_complex_decl)
DIAG_NAME_INDEX(err_deduction_guide_wrong_access)
DIAG_NAME_INDEX(err_deduction_guide_wrong_scope)
DIAG_NAME_INDEX(err_deep_exception_specs_differ)
DIAG_NAME_INDEX(err_default_arg_in_partial_spec)
DIAG_NAME_INDEX(err_default_arg_makes_ctor_special)
DIAG_NAME_INDEX(err_default_arg_unparsed)
DIAG_NAME_INDEX(err_default_delete_in_multiple_declaration)
DIAG_NAME_INDEX(err_default_init_const)
DIAG_NAME_INDEX(err_default_member_initializer_cycle)
DIAG_NAME_INDEX(err_default_member_initializer_not_yet_parsed)
DIAG_NAME_INDEX(err_default_not_in_switch)
DIAG_NAME_INDEX(err_default_special_members)
DIAG_NAME_INDEX(err_default_template_template_parameter_not_template)
DIAG_NAME_INDEX(err_defaulted_comparison_cannot_deduce_undeduced_auto)
DIAG_NAME_INDEX(err_defaulted_comparison_deduced_return_type_not_auto)
DIAG_NAME_INDEX(err_defaulted_comparison_non_const)
DIAG_NAME_INDEX(err_defaulted_comparison_not_friend)
DIAG_NAME_INDEX(err_defaulted_comparison_num_args)
DIAG_NAME_INDEX(err_defaulted_comparison_param)
DIAG_NAME_INDEX(err_defaulted_comparison_param_mismatch)
DIAG_NAME_INDEX(err_defaulted_comparison_param_unknown)
DIAG_NAME_INDEX(err_defaulted_comparison_return_type_not_bool)
DIAG_NAME_INDEX(err_defaulted_comparison_template)
DIAG_NAME_INDEX(err_defaulted_copy_assign_not_ref)
DIAG_NAME_INDEX(err_defaulted_special_member_copy_const_param)
DIAG_NAME_INDEX(err_defaulted_special_member_move_const_param)
DIAG_NAME_INDEX(err_defaulted_special_member_params)
DIAG_NAME_INDEX(err_defaulted_special_member_quals)
DIAG_NAME_INDEX(err_defaulted_special_member_return_type)
DIAG_NAME_INDEX(err_defaulted_special_member_variadic)
DIAG_NAME_INDEX(err_defaulted_special_member_volatile_param)
DIAG_NAME_INDEX(err_defined_macro_name)
DIAG_NAME_INDEX(err_definition_of_explicitly_defaulted_member)
DIAG_NAME_INDEX(err_definition_of_implicitly_declared_member)
DIAG_NAME_INDEX(err_delegating_ctor)
DIAG_NAME_INDEX(err_delegating_initializer_alone)
DIAG_NAME_INDEX(err_delete_explicit_conversion)
DIAG_NAME_INDEX(err_delete_incomplete_class_type)
DIAG_NAME_INDEX(err_delete_operand)
DIAG_NAME_INDEX(err_deleted_decl_not_first)
DIAG_NAME_INDEX(err_deleted_function_use)
DIAG_NAME_INDEX(err_deleted_inherited_ctor_use)
DIAG_NAME_INDEX(err_deleted_main)
DIAG_NAME_INDEX(err_deleted_non_function)
DIAG_NAME_INDEX(err_deleted_override)
DIAG_NAME_INDEX(err_delimited_escape_empty)
DIAG_NAME_INDEX(err_delimited_escape_invalid)
DIAG_NAME_INDEX(err_delimited_escape_missing_brace)
DIAG_NAME_INDEX(err_dep_source_scanner_missing_semi_after_at_import)
DIAG_NAME_INDEX(err_dep_source_scanner_unexpected_tokens_at_import)
DIAG_NAME_INDEX(err_dependent_deduced_tst)
DIAG_NAME_INDEX(err_dependent_function_template_spec_no_match)
DIAG_NAME_INDEX(err_dependent_nested_name_spec)
DIAG_NAME_INDEX(err_dependent_non_type_arg_in_partial_spec)
DIAG_NAME_INDEX(err_dependent_tag_decl)
DIAG_NAME_INDEX(err_dependent_typed_non_type_arg_in_partial_spec)
DIAG_NAME_INDEX(err_dereference_incomplete_type)
DIAG_NAME_INDEX(err_designated_init_attr_non_init)
DIAG_NAME_INDEX(err_designated_init_for_non_aggregate)
DIAG_NAME_INDEX(err_designator_for_scalar_or_sizeless_init)
DIAG_NAME_INDEX(err_designator_into_flexible_array_member)
DIAG_NAME_INDEX(err_destroy_attr_on_non_static_var)
DIAG_NAME_INDEX(err_destroying_operator_delete_not_usual)
DIAG_NAME_INDEX(err_destructor_cannot_be)
DIAG_NAME_INDEX(err_destructor_expr_mismatch)
DIAG_NAME_INDEX(err_destructor_expr_nontype)
DIAG_NAME_INDEX(err_destructor_expr_type_mismatch)
DIAG_NAME_INDEX(err_destructor_name)
DIAG_NAME_INDEX(err_destructor_name_nontype)
DIAG_NAME_INDEX(err_destructor_not_member)
DIAG_NAME_INDEX(err_destructor_redeclared)
DIAG_NAME_INDEX(err_destructor_return_type)
DIAG_NAME_INDEX(err_destructor_template)
DIAG_NAME_INDEX(err_destructor_template_id)
DIAG_NAME_INDEX(err_destructor_tilde_identifier)
DIAG_NAME_INDEX(err_destructor_tilde_scope)
DIAG_NAME_INDEX(err_destructor_variadic)
DIAG_NAME_INDEX(err_destructor_with_params)
DIAG_NAME_INDEX(err_diagnose_if_invalid_diagnostic_type)
DIAG_NAME_INDEX(err_diagnose_if_succeeded)
DIAG_NAME_INDEX(err_different_asm_label)
DIAG_NAME_INDEX(err_different_language_linkage)
DIAG_NAME_INDEX(err_different_pass_object_size_params)
DIAG_NAME_INDEX(err_different_return_type_for_overriding_virtual_function)
DIAG_NAME_INDEX(err_digit_separator_not_between_digits)
DIAG_NAME_INDEX(err_dimension_expr_not_constant_integer)
DIAG_NAME_INDEX(err_direct_selector_expression)
DIAG_NAME_INDEX(err_disallowed_duplicate_attribute)
DIAG_NAME_INDEX(err_distant_exception_spec)
DIAG_NAME_INDEX(err_downcast_from_inaccessible_base)
DIAG_NAME_INDEX(err_drv_I_dash_not_supported)
DIAG_NAME_INDEX(err_drv_Xopenmp_target_missing_triple)
DIAG_NAME_INDEX(err_drv_alignment_not_power_of_two)
DIAG_NAME_INDEX(err_drv_amdgpu_ieee_without_no_honor_nans)
DIAG_NAME_INDEX(err_drv_arg_requires_bitcode_input)
DIAG_NAME_INDEX(err_drv_argument_not_allowed_with)
DIAG_NAME_INDEX(err_drv_argument_only_allowed_with)
DIAG_NAME_INDEX(err_drv_bad_offload_arch_combo)
DIAG_NAME_INDEX(err_drv_bad_target_id)
DIAG_NAME_INDEX(err_drv_bitcode_unsupported_on_toolchain)
DIAG_NAME_INDEX(err_drv_cannot_mix_options)
DIAG_NAME_INDEX(err_drv_cannot_open_config_file)
DIAG_NAME_INDEX(err_drv_cannot_open_randomize_layout_seed_file)
DIAG_NAME_INDEX(err_drv_cannot_read_config_file)
DIAG_NAME_INDEX(err_drv_cc_print_options_failure)
DIAG_NAME_INDEX(err_drv_clang_unsupported)
DIAG_NAME_INDEX(err_drv_clang_unsupported_opt_cxx_darwin_i386)
DIAG_NAME_INDEX(err_drv_clang_unsupported_opt_faltivec)
DIAG_NAME_INDEX(err_drv_clang_unsupported_opt_pg_darwin)
DIAG_NAME_INDEX(err_drv_command_failed)
DIAG_NAME_INDEX(err_drv_command_failure)
DIAG_NAME_INDEX(err_drv_command_signalled)
DIAG_NAME_INDEX(err_drv_compilationdatabase)
DIAG_NAME_INDEX(err_drv_config_file_not_found)
DIAG_NAME_INDEX(err_drv_conflicting_deployment_targets)
DIAG_NAME_INDEX(err_drv_cuda_bad_gpu_arch)
DIAG_NAME_INDEX(err_drv_cuda_host_arch)
DIAG_NAME_INDEX(err_drv_cuda_offload_only_emit_bc)
DIAG_NAME_INDEX(err_drv_cuda_version_unsupported)
DIAG_NAME_INDEX(err_drv_darwin_sdk_missing_arclite)
DIAG_NAME_INDEX(err_drv_defsym_invalid_format)
DIAG_NAME_INDEX(err_drv_defsym_invalid_symval)
DIAG_NAME_INDEX(err_drv_duplicate_config)
DIAG_NAME_INDEX(err_drv_dxc_missing_target_profile)
DIAG_NAME_INDEX(err_drv_emit_llvm_link)
DIAG_NAME_INDEX(err_drv_expand_response_file)
DIAG_NAME_INDEX(err_drv_expecting_fopenmp_with_fopenmp_targets)
DIAG_NAME_INDEX(err_drv_extract_api_wrong_kind)
DIAG_NAME_INDEX(err_drv_failed_to_deduce_target_from_arch)
DIAG_NAME_INDEX(err_drv_force_crash)
DIAG_NAME_INDEX(err_drv_gnustep_objc_runtime_incompatible_binary)
DIAG_NAME_INDEX(err_drv_header_unit_extra_inputs)
DIAG_NAME_INDEX(err_drv_hipspv_no_hip_path)
DIAG_NAME_INDEX(err_drv_hlsl_unsupported_target)
DIAG_NAME_INDEX(err_drv_incompatible_omp_arch)
DIAG_NAME_INDEX(err_drv_incompatible_options)
DIAG_NAME_INDEX(err_drv_incompatible_unwindlib)
DIAG_NAME_INDEX(err_drv_invalid_Xarch_argument_with_args)
DIAG_NAME_INDEX(err_drv_invalid_Xopenmp_target_with_args)
DIAG_NAME_INDEX(err_drv_invalid_arch_name)
DIAG_NAME_INDEX(err_drv_invalid_argument_to_option)
DIAG_NAME_INDEX(err_drv_invalid_cf_runtime_abi)
DIAG_NAME_INDEX(err_drv_invalid_darwin_version)
DIAG_NAME_INDEX(err_drv_invalid_diagnotics_hotness_threshold)
DIAG_NAME_INDEX(err_drv_invalid_diagnotics_misexpect_tolerance)
DIAG_NAME_INDEX(err_drv_invalid_directx_shader_module)
DIAG_NAME_INDEX(err_drv_invalid_empty_dxil_validator_version)
DIAG_NAME_INDEX(err_drv_invalid_format_dxil_validator_version)
DIAG_NAME_INDEX(err_drv_invalid_gcc_install_dir)
DIAG_NAME_INDEX(err_drv_invalid_gcc_output_type)
DIAG_NAME_INDEX(err_drv_invalid_int_value)
DIAG_NAME_INDEX(err_drv_invalid_linker_name)
DIAG_NAME_INDEX(err_drv_invalid_malign_branch_EQ)
DIAG_NAME_INDEX(err_drv_invalid_mfloat_abi)
DIAG_NAME_INDEX(err_drv_invalid_mtp)
DIAG_NAME_INDEX(err_drv_invalid_object_mode)
DIAG_NAME_INDEX(err_drv_invalid_omp_target)
DIAG_NAME_INDEX(err_drv_invalid_or_unsupported_offload_target)
DIAG_NAME_INDEX(err_drv_invalid_os_in_arg)
DIAG_NAME_INDEX(err_drv_invalid_output_with_multiple_archs)
DIAG_NAME_INDEX(err_drv_invalid_range_dxil_validator_version)
DIAG_NAME_INDEX(err_drv_invalid_remap_file)
DIAG_NAME_INDEX(err_drv_invalid_riscv_arch_name)
DIAG_NAME_INDEX(err_drv_invalid_riscv_cpu_name_for_target)
DIAG_NAME_INDEX(err_drv_invalid_rtlib_name)
DIAG_NAME_INDEX(err_drv_invalid_stdlib_name)
DIAG_NAME_INDEX(err_drv_invalid_thread_model_for_target)
DIAG_NAME_INDEX(err_drv_invalid_unwindlib_name)
DIAG_NAME_INDEX(err_drv_invalid_value)
DIAG_NAME_INDEX(err_drv_invalid_value_with_suggestion)
DIAG_NAME_INDEX(err_drv_invalid_version_number)
DIAG_NAME_INDEX(err_drv_loongarch_invalid_mfpu_EQ)
DIAG_NAME_INDEX(err_drv_lto_without_lld)
DIAG_NAME_INDEX(err_drv_malformed_sanitizer_coverage_allowlist)
DIAG_NAME_INDEX(err_drv_malformed_sanitizer_coverage_ignorelist)
DIAG_NAME_INDEX(err_drv_malformed_sanitizer_ignorelist)
DIAG_NAME_INDEX(err_drv_malformed_sanitizer_metadata_ignorelist)
DIAG_NAME_INDEX(err_drv_mg_requires_m_or_mm)
DIAG_NAME_INDEX(err_drv_minws_unsupported_input_type)
DIAG_NAME_INDEX(err_drv_missing_arg_mtp)
DIAG_NAME_INDEX(err_drv_missing_argument)
DIAG_NAME_INDEX(err_drv_missing_sanitizer_ignorelist)
DIAG_NAME_INDEX(err_drv_mix_cuda_hip)
DIAG_NAME_INDEX(err_drv_module_header_wrong_kind)
DIAG_NAME_INDEX(err_drv_module_output_with_multiple_arch)
DIAG_NAME_INDEX(err_drv_modules_validate_once_requires_timestamp)
DIAG_NAME_INDEX(err_drv_needs_hvx)
DIAG_NAME_INDEX(err_drv_needs_hvx_version)
DIAG_NAME_INDEX(err_drv_negative_columns)
DIAG_NAME_INDEX(err_drv_no_ast_support)
DIAG_NAME_INDEX(err_drv_no_cuda_installation)
DIAG_NAME_INDEX(err_drv_no_cuda_libdevice)
DIAG_NAME_INDEX(err_drv_no_hip_runtime)
DIAG_NAME_INDEX(err_drv_no_hipspv_device_lib)
DIAG_NAME_INDEX(err_drv_no_input_files)
DIAG_NAME_INDEX(err_drv_no_linker_llvm_support)
DIAG_NAME_INDEX(err_drv_no_module_support)
DIAG_NAME_INDEX(err_drv_no_neon_modifier)
DIAG_NAME_INDEX(err_drv_no_rocm_device_lib)
DIAG_NAME_INDEX(err_drv_no_such_file)
DIAG_NAME_INDEX(err_drv_no_such_file_with_suggestion)
DIAG_NAME_INDEX(err_drv_offload_bad_gpu_arch)
DIAG_NAME_INDEX(err_drv_omp_host_ir_file_not_found)
DIAG_NAME_INDEX(err_drv_omp_host_target_not_supported)
DIAG_NAME_INDEX(err_drv_omp_offload_target_bcruntime_not_found)
DIAG_NAME_INDEX(err_drv_omp_offload_target_cuda_version_not_support)
DIAG_NAME_INDEX(err_drv_omp_offload_target_missingbcruntime)
DIAG_NAME_INDEX(err_drv_only_one_offload_target_supported)
DIAG_NAME_INDEX(err_drv_optimization_remark_format)
DIAG_NAME_INDEX(err_drv_optimization_remark_pattern)
DIAG_NAME_INDEX(err_drv_out_file_argument_with_multiple_sources)
DIAG_NAME_INDEX(err_drv_output_argument_with_multiple_files)
DIAG_NAME_INDEX(err_drv_preamble_format)
DIAG_NAME_INDEX(err_drv_print_header_env_var)
DIAG_NAME_INDEX(err_drv_print_header_env_var_combination)
DIAG_NAME_INDEX(err_drv_print_header_env_var_combination_cc1)
DIAG_NAME_INDEX(err_drv_riscv_unsupported_with_linker_relaxation)
DIAG_NAME_INDEX(err_drv_ropi_incompatible_with_cxx)
DIAG_NAME_INDEX(err_drv_ropi_rwpi_incompatible_with_pic)
DIAG_NAME_INDEX(err_drv_small_columns)
DIAG_NAME_INDEX(err_drv_ssp_missing_offset_argument)
DIAG_NAME_INDEX(err_drv_target_variant_invalid)
DIAG_NAME_INDEX(err_drv_trivial_auto_var_init_stop_after_invalid_value)
DIAG_NAME_INDEX(err_drv_trivial_auto_var_init_stop_after_missing_dependency)
DIAG_NAME_INDEX(err_drv_unable_to_remove_file)
DIAG_NAME_INDEX(err_drv_unable_to_set_working_directory)
DIAG_NAME_INDEX(err_drv_undetermined_gpu_arch)
DIAG_NAME_INDEX(err_drv_unknown_argument)
DIAG_NAME_INDEX(err_drv_unknown_argument_with_suggestion)
DIAG_NAME_INDEX(err_drv_unknown_indirect_jump_opt)
DIAG_NAME_INDEX(err_drv_unknown_language)
DIAG_NAME_INDEX(err_drv_unknown_objc_runtime)
DIAG_NAME_INDEX(err_drv_unknown_stdin_type)
DIAG_NAME_INDEX(err_drv_unknown_stdin_type_clang_cl)
DIAG_NAME_INDEX(err_drv_unknown_target_triple)
DIAG_NAME_INDEX(err_drv_unsupported_embed_bitcode)
DIAG_NAME_INDEX(err_drv_unsupported_fpatchable_function_entry_argument)
DIAG_NAME_INDEX(err_drv_unsupported_indirect_jump_opt)
DIAG_NAME_INDEX(err_drv_unsupported_noabicalls_pic)
DIAG_NAME_INDEX(err_drv_unsupported_opt)
DIAG_NAME_INDEX(err_drv_unsupported_opt_for_language_mode)
DIAG_NAME_INDEX(err_drv_unsupported_opt_for_target)
DIAG_NAME_INDEX(err_drv_unsupported_opt_with_suggestion)
DIAG_NAME_INDEX(err_drv_unsupported_option_argument)
DIAG_NAME_INDEX(err_drv_unsupported_rtlib_for_platform)
DIAG_NAME_INDEX(err_drv_unsupported_static_sanitizer_darwin)
DIAG_NAME_INDEX(err_dtor_expr_without_call)
DIAG_NAME_INDEX(err_dup_implementation_category)
DIAG_NAME_INDEX(err_dup_implementation_class)
DIAG_NAME_INDEX(err_dup_virtual)
DIAG_NAME_INDEX(err_duplicate_base_class)
DIAG_NAME_INDEX(err_duplicate_case)
DIAG_NAME_INDEX(err_duplicate_case_differing_expr)
DIAG_NAME_INDEX(err_duplicate_class_def)
DIAG_NAME_INDEX(err_duplicate_class_virt_specifier)
DIAG_NAME_INDEX(err_duplicate_declspec)
DIAG_NAME_INDEX(err_duplicate_default_assoc)
DIAG_NAME_INDEX(err_duplicate_ivar_declaration)
DIAG_NAME_INDEX(err_duplicate_ivar_use)
DIAG_NAME_INDEX(err_duplicate_mangled_name)
DIAG_NAME_INDEX(err_duplicate_member)
DIAG_NAME_INDEX(err_duplicate_method_decl)
DIAG_NAME_INDEX(err_duplicate_property)
DIAG_NAME_INDEX(err_duplicate_virt_specifier)
DIAG_NAME_INDEX(err_dynamic_and_noexcept_specification)
DIAG_NAME_INDEX(err_dynamic_property_ivar_decl)
DIAG_NAME_INDEX(err_dynamic_var_init)
DIAG_NAME_INDEX(err_early_catch_all)
DIAG_NAME_INDEX(err_ellipsis_first_param)
DIAG_NAME_INDEX(err_ellipsis_in_declarator_not_parameter)
DIAG_NAME_INDEX(err_embedded_directive)
DIAG_NAME_INDEX(err_empty_enum)
DIAG_NAME_INDEX(err_empty_requires_expr)
DIAG_NAME_INDEX(err_empty_scalar_initializer)
DIAG_NAME_INDEX(err_empty_sizeless_initializer)
DIAG_NAME_INDEX(err_enum_invalid_underlying)
DIAG_NAME_INDEX(err_enum_mode_vector_type)
DIAG_NAME_INDEX(err_enum_redeclare_fixed_mismatch)
DIAG_NAME_INDEX(err_enum_redeclare_scoped_mismatch)
DIAG_NAME_INDEX(err_enum_redeclare_type_mismatch)
DIAG_NAME_INDEX(err_enum_template)
DIAG_NAME_INDEX(err_enumerator_does_not_exist)
DIAG_NAME_INDEX(err_enumerator_list_missing_comma)
DIAG_NAME_INDEX(err_enumerator_too_large)
DIAG_NAME_INDEX(err_enumerator_unnamed_no_def)
DIAG_NAME_INDEX(err_enumerator_wrapped)
DIAG_NAME_INDEX(err_escape_too_large)
DIAG_NAME_INDEX(err_event_t_addr_space_qual)
DIAG_NAME_INDEX(err_except_spec_unparsed)
DIAG_NAME_INDEX(err_exception_spec_cycle)
DIAG_NAME_INDEX(err_exception_spec_in_typedef)
DIAG_NAME_INDEX(err_exception_spec_incomplete_type)
DIAG_NAME_INDEX(err_exception_spec_not_parsed)
DIAG_NAME_INDEX(err_exceptions_disabled)
DIAG_NAME_INDEX(err_excess_initializers)
DIAG_NAME_INDEX(err_excess_initializers_for_sizeless_type)
DIAG_NAME_INDEX(err_excess_initializers_in_char_array_initializer)
DIAG_NAME_INDEX(err_expected)
DIAG_NAME_INDEX(err_expected_after)
DIAG_NAME_INDEX(err_expected_allocator_clause)
DIAG_NAME_INDEX(err_expected_allocator_expression)
DIAG_NAME_INDEX(err_expected_begin_assumes)
DIAG_NAME_INDEX(err_expected_begin_declare_variant)
DIAG_NAME_INDEX(err_expected_callable_argument)
DIAG_NAME_INDEX(err_expected_capture)
DIAG_NAME_INDEX(err_expected_case_before_expression)
DIAG_NAME_INDEX(err_expected_catch)
DIAG_NAME_INDEX(err_expected_class_name)
DIAG_NAME_INDEX(err_expected_class_name_not_template)
DIAG_NAME_INDEX(err_expected_class_or_namespace)
DIAG_NAME_INDEX(err_expected_colon_after_setter_name)
DIAG_NAME_INDEX(err_expected_coloncolon_after_super)
DIAG_NAME_INDEX(err_expected_comma_greater)
DIAG_NAME_INDEX(err_expected_comma_or_rsquare)
DIAG_NAME_INDEX(err_expected_either)
DIAG_NAME_INDEX(err_expected_end_declare_target_or_variant)
DIAG_NAME_INDEX(err_expected_end_of_enumerator)
DIAG_NAME_INDEX(err_expected_equal_designator)
DIAG_NAME_INDEX(err_expected_expression)
DIAG_NAME_INDEX(err_expected_external_declaration)
DIAG_NAME_INDEX(err_expected_field_designator)
DIAG_NAME_INDEX(err_expected_fn_body)
DIAG_NAME_INDEX(err_expected_fold_operator)
DIAG_NAME_INDEX(err_expected_id_building_module)
DIAG_NAME_INDEX(err_expected_init_in_condition)
DIAG_NAME_INDEX(err_expected_init_in_condition_lparen)
DIAG_NAME_INDEX(err_expected_kernel_void_return_type)
DIAG_NAME_INDEX(err_expected_lambda_body)
DIAG_NAME_INDEX(err_expected_lbrace_after_base_specifiers)
DIAG_NAME_INDEX(err_expected_lbrace_in_compound_literal)
DIAG_NAME_INDEX(err_expected_less_after)
DIAG_NAME_INDEX(err_expected_lparen_after)
DIAG_NAME_INDEX(err_expected_lparen_after_type)
DIAG_NAME_INDEX(err_expected_member_name_or_semi)
DIAG_NAME_INDEX(err_expected_member_name_or_semi_objcxx_keyword)
DIAG_NAME_INDEX(err_expected_member_or_base_name)
DIAG_NAME_INDEX(err_expected_method_body)
DIAG_NAME_INDEX(err_expected_minus_or_plus)
DIAG_NAME_INDEX(err_expected_namespace_name)
DIAG_NAME_INDEX(err_expected_objc_container)
DIAG_NAME_INDEX(err_expected_parameter_pack)
DIAG_NAME_INDEX(err_expected_parentheses_around_typename)
DIAG_NAME_INDEX(err_expected_property_name)
DIAG_NAME_INDEX(err_expected_punc)
DIAG_NAME_INDEX(err_expected_qualified_after_typename)
DIAG_NAME_INDEX(err_expected_rparen_after)
DIAG_NAME_INDEX(err_expected_selector_for_method)
DIAG_NAME_INDEX(err_expected_semantic_identifier)
DIAG_NAME_INDEX(err_expected_semi_after_attribute_list)
DIAG_NAME_INDEX(err_expected_semi_after_expr)
DIAG_NAME_INDEX(err_expected_semi_after_method_proto)
DIAG_NAME_INDEX(err_expected_semi_after_namespace_name)
DIAG_NAME_INDEX(err_expected_semi_after_static_assert)
DIAG_NAME_INDEX(err_expected_semi_after_stmt)
DIAG_NAME_INDEX(err_expected_semi_decl_list)
DIAG_NAME_INDEX(err_expected_semi_declaration)
DIAG_NAME_INDEX(err_expected_semi_for)
DIAG_NAME_INDEX(err_expected_semi_requirement)
DIAG_NAME_INDEX(err_expected_sequence_or_directive)
DIAG_NAME_INDEX(err_expected_star_this_capture)
DIAG_NAME_INDEX(err_expected_statement)
DIAG_NAME_INDEX(err_expected_string_literal)
DIAG_NAME_INDEX(err_expected_struct_pointer_argument)
DIAG_NAME_INDEX(err_expected_template)
DIAG_NAME_INDEX(err_expected_template_parameter)
DIAG_NAME_INDEX(err_expected_token_instead_of_objcxx_keyword)
DIAG_NAME_INDEX(err_expected_type)
DIAG_NAME_INDEX(err_expected_type_name_after_typename)
DIAG_NAME_INDEX(err_expected_unqualified_id)
DIAG_NAME_INDEX(err_expected_version)
DIAG_NAME_INDEX(err_expected_while)
DIAG_NAME_INDEX(err_experimental_clang_interp_failed)
DIAG_NAME_INDEX(err_explicit_instantiation_ambiguous)
DIAG_NAME_INDEX(err_explicit_instantiation_constexpr)
DIAG_NAME_INDEX(err_explicit_instantiation_data_member_not_instantiated)
DIAG_NAME_INDEX(err_explicit_instantiation_declaration_after_definition)
DIAG_NAME_INDEX(err_explicit_instantiation_dependent)
DIAG_NAME_INDEX(err_explicit_instantiation_duplicate)
DIAG_NAME_INDEX(err_explicit_instantiation_enum)
DIAG_NAME_INDEX(err_explicit_instantiation_in_class)
DIAG_NAME_INDEX(err_explicit_instantiation_inline)
DIAG_NAME_INDEX(err_explicit_instantiation_internal_linkage)
DIAG_NAME_INDEX(err_explicit_instantiation_member_function_not_instantiated)
DIAG_NAME_INDEX(err_explicit_instantiation_must_be_global)
DIAG_NAME_INDEX(err_explicit_instantiation_nontemplate_type)
DIAG_NAME_INDEX(err_explicit_instantiation_not_known)
DIAG_NAME_INDEX(err_explicit_instantiation_of_typedef)
DIAG_NAME_INDEX(err_explicit_instantiation_out_of_scope)
DIAG_NAME_INDEX(err_explicit_instantiation_requires_name)
DIAG_NAME_INDEX(err_explicit_instantiation_storage_class)
DIAG_NAME_INDEX(err_explicit_instantiation_undefined_func_template)
DIAG_NAME_INDEX(err_explicit_instantiation_undefined_member)
DIAG_NAME_INDEX(err_explicit_instantiation_undefined_var_template)
DIAG_NAME_INDEX(err_explicit_instantiation_unqualified_wrong_namespace)
DIAG_NAME_INDEX(err_explicit_instantiation_with_definition)
DIAG_NAME_INDEX(err_explicit_instantiation_without_template_id)
DIAG_NAME_INDEX(err_explicit_non_ctor_or_conv_function)
DIAG_NAME_INDEX(err_explicit_non_function)
DIAG_NAME_INDEX(err_explicit_out_of_class)
DIAG_NAME_INDEX(err_explicit_spec_non_template)
DIAG_NAME_INDEX(err_explicit_specialization_inconsistent_storage_class)
DIAG_NAME_INDEX(err_exponent_has_no_digits)
DIAG_NAME_INDEX(err_export_anon_ns_internal)
DIAG_NAME_INDEX(err_export_empty)
DIAG_NAME_INDEX(err_export_in_private_module_fragment)
DIAG_NAME_INDEX(err_export_inline_not_defined)
DIAG_NAME_INDEX(err_export_internal)
DIAG_NAME_INDEX(err_export_non_namespace_scope_name)
DIAG_NAME_INDEX(err_export_not_in_module_interface)
DIAG_NAME_INDEX(err_export_partition_impl)
DIAG_NAME_INDEX(err_export_using_internal)
DIAG_NAME_INDEX(err_export_within_anonymous_namespace)
DIAG_NAME_INDEX(err_export_within_export)
DIAG_NAME_INDEX(err_expr_not_cce)
DIAG_NAME_INDEX(err_expr_not_ice)
DIAG_NAME_INDEX(err_expr_not_string_literal)
DIAG_NAME_INDEX(err_ext_vector_component_exceeds_length)
DIAG_NAME_INDEX(err_ext_vector_component_name_illegal)
DIAG_NAME_INDEX(err_extdefmap_parsing)
DIAG_NAME_INDEX(err_extern_c_global_conflict)
DIAG_NAME_INDEX(err_extern_def_in_header_unit)
DIAG_NAME_INDEX(err_extern_non_extern)
DIAG_NAME_INDEX(err_external_source_symbol_duplicate_clause)
DIAG_NAME_INDEX(err_external_source_symbol_expected_keyword)
DIAG_NAME_INDEX(err_extract_api_ignores_file_not_found)
DIAG_NAME_INDEX(err_extraneous_closing_brace)
DIAG_NAME_INDEX(err_extraneous_rparen_in_condition)
DIAG_NAME_INDEX(err_extraneous_token_before_semi)
DIAG_NAME_INDEX(err_falloff_nonvoid_block)
DIAG_NAME_INDEX(err_fallthrough_attr_invalid_placement)
DIAG_NAME_INDEX(err_fallthrough_attr_outside_switch)
DIAG_NAME_INDEX(err_fallthrough_attr_wrong_target)
DIAG_NAME_INDEX(err_fe_action_not_available)
DIAG_NAME_INDEX(err_fe_ast_file_modified)
DIAG_NAME_INDEX(err_fe_backend_error_attr)
DIAG_NAME_INDEX(err_fe_backend_frame_larger_than)
DIAG_NAME_INDEX(err_fe_backend_plugin)
DIAG_NAME_INDEX(err_fe_backend_resource_limit)
DIAG_NAME_INDEX(err_fe_backend_unsupported)
DIAG_NAME_INDEX(err_fe_dependency_file_requires_MT)
DIAG_NAME_INDEX(err_fe_error_backend)
DIAG_NAME_INDEX(err_fe_error_opening)
DIAG_NAME_INDEX(err_fe_error_reading)
DIAG_NAME_INDEX(err_fe_error_reading_stdin)
DIAG_NAME_INDEX(err_fe_expected_clang_command)
DIAG_NAME_INDEX(err_fe_expected_compiler_job)
DIAG_NAME_INDEX(err_fe_inline_asm)
DIAG_NAME_INDEX(err_fe_invalid_alignment)
DIAG_NAME_INDEX(err_fe_invalid_code_complete_file)
DIAG_NAME_INDEX(err_fe_invalid_exception_model)
DIAG_NAME_INDEX(err_fe_invalid_plugin_name)
DIAG_NAME_INDEX(err_fe_invalid_source_date_epoch)
DIAG_NAME_INDEX(err_fe_linking_module)
DIAG_NAME_INDEX(err_fe_no_pch_in_dir)
DIAG_NAME_INDEX(err_fe_not_a_pch_file)
DIAG_NAME_INDEX(err_fe_pch_file_overridden)
DIAG_NAME_INDEX(err_fe_pch_malformed)
DIAG_NAME_INDEX(err_fe_pch_malformed_block)
DIAG_NAME_INDEX(err_fe_remap_missing_from_file)
DIAG_NAME_INDEX(err_fe_remap_missing_to_file)
DIAG_NAME_INDEX(err_fe_source_mgr)
DIAG_NAME_INDEX(err_fe_unable_to_create_target)
DIAG_NAME_INDEX(err_fe_unable_to_interface_with_target)
DIAG_NAME_INDEX(err_fe_unable_to_load_basic_block_sections_file)
DIAG_NAME_INDEX(err_fe_unable_to_load_pch)
DIAG_NAME_INDEX(err_fe_unable_to_load_plugin)
DIAG_NAME_INDEX(err_fe_unable_to_open_output)
DIAG_NAME_INDEX(err_fe_unable_to_read_pch_file)
DIAG_NAME_INDEX(err_feature_check_malformed)
DIAG_NAME_INDEX(err_field_declared_as_function)
DIAG_NAME_INDEX(err_field_designator_non_aggr)
DIAG_NAME_INDEX(err_field_designator_nonfield)
DIAG_NAME_INDEX(err_field_designator_unknown)
DIAG_NAME_INDEX(err_field_designator_unknown_suggest)
DIAG_NAME_INDEX(err_field_incomplete_or_sizeless)
DIAG_NAME_INDEX(err_field_instantiates_to_function)
DIAG_NAME_INDEX(err_field_with_address_space)
DIAG_NAME_INDEX(err_file_modified)
DIAG_NAME_INDEX(err_file_too_large)
DIAG_NAME_INDEX(err_filter_expression_integral)
DIAG_NAME_INDEX(err_final_function_overridden)
DIAG_NAME_INDEX(err_first_argument_to_cwsc_block_call)
DIAG_NAME_INDEX(err_first_argument_to_cwsc_builtin_call)
DIAG_NAME_INDEX(err_first_argument_to_cwsc_not_call)
DIAG_NAME_INDEX(err_first_argument_to_cwsc_pdtor_call)
DIAG_NAME_INDEX(err_first_argument_to_va_arg_not_of_type_va_list)
DIAG_NAME_INDEX(err_fixed_point_not_enabled)
DIAG_NAME_INDEX(err_flexible_array_arc_retainable)
DIAG_NAME_INDEX(err_flexible_array_empty_aggregate)
DIAG_NAME_INDEX(err_flexible_array_has_nontrivial_dtor)
DIAG_NAME_INDEX(err_flexible_array_init)
DIAG_NAME_INDEX(err_flexible_array_init_needs_braces)
DIAG_NAME_INDEX(err_flexible_array_not_at_end)
DIAG_NAME_INDEX(err_flexible_array_union)
DIAG_NAME_INDEX(err_flexible_array_virtual_base)
DIAG_NAME_INDEX(err_fold_expression_bad_operand)
DIAG_NAME_INDEX(err_fold_expression_empty)
DIAG_NAME_INDEX(err_fold_expression_limit_exceeded)
DIAG_NAME_INDEX(err_fold_expression_packs_both_sides)
DIAG_NAME_INDEX(err_fold_operator_mismatch)
DIAG_NAME_INDEX(err_for_co_await_not_range_for)
DIAG_NAME_INDEX(err_for_range_decl_must_be_var)
DIAG_NAME_INDEX(err_for_range_deduction_failure)
DIAG_NAME_INDEX(err_for_range_dereference)
DIAG_NAME_INDEX(err_for_range_expected_decl)
DIAG_NAME_INDEX(err_for_range_identifier)
DIAG_NAME_INDEX(err_for_range_incomplete_type)
DIAG_NAME_INDEX(err_for_range_invalid)
DIAG_NAME_INDEX(err_for_range_iter_deduction_failure)
DIAG_NAME_INDEX(err_for_range_storage_class)
DIAG_NAME_INDEX(err_format_attribute_implicit_this_format_string)
DIAG_NAME_INDEX(err_format_attribute_not)
DIAG_NAME_INDEX(err_format_attribute_result_not)
DIAG_NAME_INDEX(err_format_strftime_third_parameter)
DIAG_NAME_INDEX(err_forward_ref_enum)
DIAG_NAME_INDEX(err_forward_superclass)
DIAG_NAME_INDEX(err_found_in_dependent_base)
DIAG_NAME_INDEX(err_found_later_in_class)
DIAG_NAME_INDEX(err_friend_decl_defines_type)
DIAG_NAME_INDEX(err_friend_decl_does_not_match)
DIAG_NAME_INDEX(err_friend_decl_spec)
DIAG_NAME_INDEX(err_friend_decl_with_def_arg_must_be_def)
DIAG_NAME_INDEX(err_friend_decl_with_def_arg_redeclared)
DIAG_NAME_INDEX(err_friend_def_in_local_class)
DIAG_NAME_INDEX(err_friend_explicit_instantiation)
DIAG_NAME_INDEX(err_friend_invalid_in_context)
DIAG_NAME_INDEX(err_friend_is_member)
DIAG_NAME_INDEX(err_friend_not_first_in_declaration)
DIAG_NAME_INDEX(err_func_def_incomplete_result)
DIAG_NAME_INDEX(err_func_def_no_params)
DIAG_NAME_INDEX(err_func_returning_array_function)
DIAG_NAME_INDEX(err_func_returning_qualified_void)
DIAG_NAME_INDEX(err_function_attribute_mismatch)
DIAG_NAME_INDEX(err_function_decl_cmse_ns_call)
DIAG_NAME_INDEX(err_function_declared_typedef)
DIAG_NAME_INDEX(err_function_definition_not_allowed)
DIAG_NAME_INDEX(err_function_is_not_record)
DIAG_NAME_INDEX(err_function_marked_override_not_overriding)
DIAG_NAME_INDEX(err_function_needs_feature)
DIAG_NAME_INDEX(err_function_parameter_pack_without_parameter_packs)
DIAG_NAME_INDEX(err_function_scope_depth_exceeded)
DIAG_NAME_INDEX(err_function_start_invalid_type)
DIAG_NAME_INDEX(err_function_template_partial_spec)
DIAG_NAME_INDEX(err_function_template_spec_ambiguous)
DIAG_NAME_INDEX(err_function_template_spec_no_match)
DIAG_NAME_INDEX(err_gc_weak_property_strong_type)
DIAG_NAME_INDEX(err_generic_sel_multi_match)
DIAG_NAME_INDEX(err_generic_sel_no_match)
DIAG_NAME_INDEX(err_getter_not_found)
DIAG_NAME_INDEX(err_global_asm_qualifier_ignored)
DIAG_NAME_INDEX(err_global_call_not_config)
DIAG_NAME_INDEX(err_global_module_introducer_not_at_start)
DIAG_NAME_INDEX(err_gnu_inline_asm_disabled)
DIAG_NAME_INDEX(err_goto_into_protected_scope)
DIAG_NAME_INDEX(err_goto_ms_asm_label)
DIAG_NAME_INDEX(err_half_const_requires_fp16)
DIAG_NAME_INDEX(err_header_import_not_header_unit)
DIAG_NAME_INDEX(err_header_import_semi_in_macro)
DIAG_NAME_INDEX(err_hex_constant_requires)
DIAG_NAME_INDEX(err_hex_escape_no_digits)
DIAG_NAME_INDEX(err_hidden_visibility_dllexport)
DIAG_NAME_INDEX(err_hip_invalid_args_builtin_mangled_name)
DIAG_NAME_INDEX(err_hlsl_attr_invalid_ast_node)
DIAG_NAME_INDEX(err_hlsl_attr_invalid_type)
DIAG_NAME_INDEX(err_hlsl_attr_unsupported_in_stage)
DIAG_NAME_INDEX(err_hlsl_attribute_param_mismatch)
DIAG_NAME_INDEX(err_hlsl_entry_shader_attr_mismatch)
DIAG_NAME_INDEX(err_hlsl_expected_space)
DIAG_NAME_INDEX(err_hlsl_init_priority_unsupported)
DIAG_NAME_INDEX(err_hlsl_missing_numthreads)
DIAG_NAME_INDEX(err_hlsl_missing_semantic_annotation)
DIAG_NAME_INDEX(err_hlsl_numthreads_argument_oor)
DIAG_NAME_INDEX(err_hlsl_numthreads_invalid)
DIAG_NAME_INDEX(err_hlsl_operator_unsupported)
DIAG_NAME_INDEX(err_hlsl_pointers_unsupported)
DIAG_NAME_INDEX(err_hlsl_separate_attr_arg_and_number)
DIAG_NAME_INDEX(err_hlsl_unsupported_register_number)
DIAG_NAME_INDEX(err_hlsl_unsupported_register_type)
DIAG_NAME_INDEX(err_iboutletcollection_builtintype)
DIAG_NAME_INDEX(err_iboutletcollection_type)
DIAG_NAME_INDEX(err_ice_ambiguous_conversion)
DIAG_NAME_INDEX(err_ice_explicit_conversion)
DIAG_NAME_INDEX(err_ice_incomplete_type)
DIAG_NAME_INDEX(err_ice_not_integral)
DIAG_NAME_INDEX(err_ice_too_large)
DIAG_NAME_INDEX(err_id_after_template_in_nested_name_spec)
DIAG_NAME_INDEX(err_ident_list_in_fn_declaration)
DIAG_NAME_INDEX(err_ifunc_resolver_return)
DIAG_NAME_INDEX(err_illegal_container_subscripting_op)
DIAG_NAME_INDEX(err_illegal_decl_array_of_functions)
DIAG_NAME_INDEX(err_illegal_decl_array_of_references)
DIAG_NAME_INDEX(err_illegal_decl_mempointer_in_nonclass)
DIAG_NAME_INDEX(err_illegal_decl_mempointer_to_reference)
DIAG_NAME_INDEX(err_illegal_decl_mempointer_to_void)
DIAG_NAME_INDEX(err_illegal_decl_pointer_to_reference)
DIAG_NAME_INDEX(err_illegal_decl_reference_to_reference)
DIAG_NAME_INDEX(err_illegal_initializer)
DIAG_NAME_INDEX(err_illegal_initializer_type)
DIAG_NAME_INDEX(err_illegal_message_expr_incomplete_type)
DIAG_NAME_INDEX(err_illegal_qualifiers_on_catch_parm)
DIAG_NAME_INDEX(err_illegal_super_cast)
DIAG_NAME_INDEX(err_illegal_union_or_anon_struct_member)
DIAG_NAME_INDEX(err_illegal_use_of_flt_eval_macro)
DIAG_NAME_INDEX(err_imaginary_not_supported)
DIAG_NAME_INDEX(err_immediate_function_used_before_definition)
DIAG_NAME_INDEX(err_impcast_complex_scalar)
DIAG_NAME_INDEX(err_implementation_of_class_stub)
DIAG_NAME_INDEX(err_implicit_coroutine_std_nothrow_type_not_found)
DIAG_NAME_INDEX(err_implicit_empty_initializer)
DIAG_NAME_INDEX(err_implicit_instantiate_member_undefined)
DIAG_NAME_INDEX(err_implied_comparison_category_type_not_found)
DIAG_NAME_INDEX(err_implied_coroutine_type_not_found)
DIAG_NAME_INDEX(err_implied_std_coroutine_traits_promise_type_not_class)
DIAG_NAME_INDEX(err_implied_std_coroutine_traits_promise_type_not_found)
DIAG_NAME_INDEX(err_implied_std_initializer_list_not_found)
DIAG_NAME_INDEX(err_import_in_wrong_fragment)
DIAG_NAME_INDEX(err_import_not_allowed_here)
DIAG_NAME_INDEX(err_imported_module_modmap_changed)
DIAG_NAME_INDEX(err_imported_module_not_found)
DIAG_NAME_INDEX(err_imported_module_relocated)
DIAG_NAME_INDEX(err_in_class_initializer_bad_type)
DIAG_NAME_INDEX(err_in_class_initializer_literal_type)
DIAG_NAME_INDEX(err_in_class_initializer_non_const)
DIAG_NAME_INDEX(err_in_class_initializer_non_constant)
DIAG_NAME_INDEX(err_in_class_initializer_volatile)
DIAG_NAME_INDEX(err_include_too_large)
DIAG_NAME_INDEX(err_incompatible_exception_specs)
DIAG_NAME_INDEX(err_incompatible_fp_eval_method_options)
DIAG_NAME_INDEX(err_incompatible_qualified_id)
DIAG_NAME_INDEX(err_incompatible_vectors)
DIAG_NAME_INDEX(err_incomplete_array_member_init)
DIAG_NAME_INDEX(err_incomplete_base_class)
DIAG_NAME_INDEX(err_incomplete_enum)
DIAG_NAME_INDEX(err_incomplete_in_exception_spec)
DIAG_NAME_INDEX(err_incomplete_member_access)
DIAG_NAME_INDEX(err_incomplete_nested_name_spec)
DIAG_NAME_INDEX(err_incomplete_object_call)
DIAG_NAME_INDEX(err_incomplete_receiver_type)
DIAG_NAME_INDEX(err_incomplete_synthesized_property)
DIAG_NAME_INDEX(err_incomplete_type)
DIAG_NAME_INDEX(err_incomplete_type_objc_at_encode)
DIAG_NAME_INDEX(err_incomplete_type_used_in_type_trait_expr)
DIAG_NAME_INDEX(err_incomplete_typeid)
DIAG_NAME_INDEX(err_inconsistent_ivar_count)
DIAG_NAME_INDEX(err_incorrect_defaulted_consteval)
DIAG_NAME_INDEX(err_incorrect_defaulted_constexpr)
DIAG_NAME_INDEX(err_incorrect_number_of_vector_initializers)
DIAG_NAME_INDEX(err_increment_decrement_enum)
DIAG_NAME_INDEX(err_indirect_goto_in_protected_scope)
DIAG_NAME_INDEX(err_indirect_goto_without_addrlabel)
DIAG_NAME_INDEX(err_init_capture_deduction_failure)
DIAG_NAME_INDEX(err_init_capture_deduction_failure_from_init_list)
DIAG_NAME_INDEX(err_init_capture_multiple_expressions)
DIAG_NAME_INDEX(err_init_capture_no_expression)
DIAG_NAME_INDEX(err_init_capture_paren_braces)
DIAG_NAME_INDEX(err_init_conversion_failed)
DIAG_NAME_INDEX(err_init_element_not_constant)
DIAG_NAME_INDEX(err_init_for_function_type)
DIAG_NAME_INDEX(err_init_incomplete_type)
DIAG_NAME_INDEX(err_init_list_bad_dest_type)
DIAG_NAME_INDEX(err_init_list_bin_op)
DIAG_NAME_INDEX(err_init_method_bad_return_type)
DIAG_NAME_INDEX(err_init_non_aggr_init_list)
DIAG_NAME_INDEX(err_init_objc_class)
DIAG_NAME_INDEX(err_init_priority_object_attr)
DIAG_NAME_INDEX(err_init_reference_member_uninitialized)
DIAG_NAME_INDEX(err_initializer_overrides_destructed)
DIAG_NAME_INDEX(err_initializer_string_for_char_array_too_long)
DIAG_NAME_INDEX(err_inline_decl_follows_def)
DIAG_NAME_INDEX(err_inline_declaration_block_scope)
DIAG_NAME_INDEX(err_inline_main)
DIAG_NAME_INDEX(err_inline_ms_asm_parsing)
DIAG_NAME_INDEX(err_inline_namespace_alias)
DIAG_NAME_INDEX(err_inline_namespace_mismatch)
DIAG_NAME_INDEX(err_inline_namespace_std)
DIAG_NAME_INDEX(err_inline_nested_namespace_definition)
DIAG_NAME_INDEX(err_inline_non_function)
DIAG_NAME_INDEX(err_int_to_block_pointer)
DIAG_NAME_INDEX(err_integer_literal_too_large)
DIAG_NAME_INDEX(err_integer_sequence_integral_element_type)
DIAG_NAME_INDEX(err_integer_sequence_negative_length)
DIAG_NAME_INDEX(err_introducing_special_friend)
DIAG_NAME_INDEX(err_invalid_asm_cast_lvalue)
DIAG_NAME_INDEX(err_invalid_asm_value_for_constraint)
DIAG_NAME_INDEX(err_invalid_astype_of_different_size)
DIAG_NAME_INDEX(err_invalid_base_in_interface)
DIAG_NAME_INDEX(err_invalid_branch_protection_spec)
DIAG_NAME_INDEX(err_invalid_char_raw_delim)
DIAG_NAME_INDEX(err_invalid_character_to_charify)
DIAG_NAME_INDEX(err_invalid_character_udl)
DIAG_NAME_INDEX(err_invalid_collection_element)
DIAG_NAME_INDEX(err_invalid_complex_spec)
DIAG_NAME_INDEX(err_invalid_consteval_call)
DIAG_NAME_INDEX(err_invalid_consteval_decl_kind)
DIAG_NAME_INDEX(err_invalid_consteval_take_address)
DIAG_NAME_INDEX(err_invalid_constexpr)
DIAG_NAME_INDEX(err_invalid_constexpr_member)
DIAG_NAME_INDEX(err_invalid_constexpr_var_decl)
DIAG_NAME_INDEX(err_invalid_conversion_between_ext_vectors)
DIAG_NAME_INDEX(err_invalid_conversion_between_matrix_and_type)
DIAG_NAME_INDEX(err_invalid_conversion_between_matrixes)
DIAG_NAME_INDEX(err_invalid_conversion_between_vector_and_integer)
DIAG_NAME_INDEX(err_invalid_conversion_between_vector_and_scalar)
DIAG_NAME_INDEX(err_invalid_conversion_between_vectors)
DIAG_NAME_INDEX(err_invalid_cpu_is)
DIAG_NAME_INDEX(err_invalid_cpu_specific_dispatch_value)
DIAG_NAME_INDEX(err_invalid_cpu_supports)
DIAG_NAME_INDEX(err_invalid_cxx_abi)
DIAG_NAME_INDEX(err_invalid_decl_spec_combination)
DIAG_NAME_INDEX(err_invalid_decl_specifier_in_nontype_parm)
DIAG_NAME_INDEX(err_invalid_declaration_in_hlsl_buffer)
DIAG_NAME_INDEX(err_invalid_declarator_global_scope)
DIAG_NAME_INDEX(err_invalid_declarator_in_block)
DIAG_NAME_INDEX(err_invalid_declarator_in_function)
DIAG_NAME_INDEX(err_invalid_declarator_scope)
DIAG_NAME_INDEX(err_invalid_digit)
DIAG_NAME_INDEX(err_invalid_feature_combination)
DIAG_NAME_INDEX(err_invalid_form_pointer_member_function)
DIAG_NAME_INDEX(err_invalid_incomplete_type_use)
DIAG_NAME_INDEX(err_invalid_macos_32bit_deployment_target)
DIAG_NAME_INDEX(err_invalid_mask_type_size)
DIAG_NAME_INDEX(err_invalid_member_in_interface)
DIAG_NAME_INDEX(err_invalid_member_use_in_static_method)
DIAG_NAME_INDEX(err_invalid_module_name)
DIAG_NAME_INDEX(err_invalid_neon_type_code)
DIAG_NAME_INDEX(err_invalid_non_static_member_use)
DIAG_NAME_INDEX(err_invalid_nsnumber_type)
DIAG_NAME_INDEX(err_invalid_numeric_udl)
DIAG_NAME_INDEX(err_invalid_operator_on_type)
DIAG_NAME_INDEX(err_invalid_pcs)
DIAG_NAME_INDEX(err_invalid_pixel_decl_spec_combination)
DIAG_NAME_INDEX(err_invalid_property_name)
DIAG_NAME_INDEX(err_invalid_protocol_qualifiers)
DIAG_NAME_INDEX(err_invalid_qualified_constructor)
DIAG_NAME_INDEX(err_invalid_qualified_destructor)
DIAG_NAME_INDEX(err_invalid_qualified_function_type)
DIAG_NAME_INDEX(err_invalid_receiver_class_message)
DIAG_NAME_INDEX(err_invalid_receiver_to_message_super)
DIAG_NAME_INDEX(err_invalid_reference_qualifier_application)
DIAG_NAME_INDEX(err_invalid_saturation_spec)
DIAG_NAME_INDEX(err_invalid_sign_spec)
DIAG_NAME_INDEX(err_invalid_storage_class_in_func_decl)
DIAG_NAME_INDEX(err_invalid_string_udl)
DIAG_NAME_INDEX(err_invalid_suffix_constant)
DIAG_NAME_INDEX(err_invalid_super_scope)
DIAG_NAME_INDEX(err_invalid_this_use)
DIAG_NAME_INDEX(err_invalid_thread)
DIAG_NAME_INDEX(err_invalid_token_after_declarator_suggest_equal)
DIAG_NAME_INDEX(err_invalid_token_after_toplevel_declarator)
DIAG_NAME_INDEX(err_invalid_type_for_program_scope_var)
DIAG_NAME_INDEX(err_invalid_ucn_name)
DIAG_NAME_INDEX(err_invalid_use_of_array_type)
DIAG_NAME_INDEX(err_invalid_use_of_function_type)
DIAG_NAME_INDEX(err_invalid_utf8)
DIAG_NAME_INDEX(err_invalid_var_template_spec_type)
DIAG_NAME_INDEX(err_invalid_vector_bool_decl_spec)
DIAG_NAME_INDEX(err_invalid_vector_bool_int128_decl_spec)
DIAG_NAME_INDEX(err_invalid_vector_decl_spec_combination)
DIAG_NAME_INDEX(err_invalid_vector_double_decl_spec)
DIAG_NAME_INDEX(err_invalid_vector_float_decl_spec)
DIAG_NAME_INDEX(err_invalid_vector_int128_decl_spec)
DIAG_NAME_INDEX(err_invalid_vector_long_decl_spec)
DIAG_NAME_INDEX(err_invalid_vector_long_double_decl_spec)
DIAG_NAME_INDEX(err_invalid_vector_long_long_decl_spec)
DIAG_NAME_INDEX(err_invalid_vfs_overlay)
DIAG_NAME_INDEX(err_invalid_width_spec)
DIAG_NAME_INDEX(err_ivar_access_using_property_syntax_suggest)
DIAG_NAME_INDEX(err_ivar_in_superclass_use)
DIAG_NAME_INDEX(err_ivar_reference_type)
DIAG_NAME_INDEX(err_ivar_use_in_class_method)
DIAG_NAME_INDEX(err_kern_call_not_global_function)
DIAG_NAME_INDEX(err_kern_is_nonstatic_method)
DIAG_NAME_INDEX(err_kern_type_not_void_return)
DIAG_NAME_INDEX(err_kernel_arg_address_space)
DIAG_NAME_INDEX(err_keyword_as_parameter)
DIAG_NAME_INDEX(err_keyword_misplaced)
DIAG_NAME_INDEX(err_keyword_not_allowed)
DIAG_NAME_INDEX(err_keyword_not_import_attr)
DIAG_NAME_INDEX(err_keyword_not_module_attr)
DIAG_NAME_INDEX(err_keyword_not_supported_on_target)
DIAG_NAME_INDEX(err_l_square_l_square_not_attribute)
DIAG_NAME_INDEX(err_lambda_after_delete)
DIAG_NAME_INDEX(err_lambda_capture_anonymous_var)
DIAG_NAME_INDEX(err_lambda_capture_default_arg)
DIAG_NAME_INDEX(err_lambda_capture_flexarray_type)
DIAG_NAME_INDEX(err_lambda_capture_misplaced_ellipsis)
DIAG_NAME_INDEX(err_lambda_capture_multiple_ellipses)
DIAG_NAME_INDEX(err_lambda_decl_ref_not_modifiable_lvalue)
DIAG_NAME_INDEX(err_lambda_decl_specifier_repeated)
DIAG_NAME_INDEX(err_lambda_impcap)
DIAG_NAME_INDEX(err_lambda_in_constant_expression)
DIAG_NAME_INDEX(err_lambda_in_invalid_context)
DIAG_NAME_INDEX(err_lambda_incomplete_result)
DIAG_NAME_INDEX(err_lambda_return_init_list)
DIAG_NAME_INDEX(err_lambda_template_parameter_list_empty)
DIAG_NAME_INDEX(err_lambda_unevaluated_operand)
DIAG_NAME_INDEX(err_language_linkage_spec_unknown)
DIAG_NAME_INDEX(err_late_asm_label_name)
DIAG_NAME_INDEX(err_lexing_char)
DIAG_NAME_INDEX(err_lexing_numeric)
DIAG_NAME_INDEX(err_lexing_string)
DIAG_NAME_INDEX(err_lifetimebound_ctor_dtor)
DIAG_NAME_INDEX(err_lifetimebound_no_object_param)
DIAG_NAME_INDEX(err_list_init_in_parens)
DIAG_NAME_INDEX(err_literal_operator_bad_param_count)
DIAG_NAME_INDEX(err_literal_operator_default_argument)
DIAG_NAME_INDEX(err_literal_operator_extern_c)
DIAG_NAME_INDEX(err_literal_operator_id_outside_namespace)
DIAG_NAME_INDEX(err_literal_operator_invalid_param)
DIAG_NAME_INDEX(err_literal_operator_outside_namespace)
DIAG_NAME_INDEX(err_literal_operator_param)
DIAG_NAME_INDEX(err_literal_operator_string_not_empty)
DIAG_NAME_INDEX(err_literal_operator_string_prefix)
DIAG_NAME_INDEX(err_literal_operator_template)
DIAG_NAME_INDEX(err_literal_operator_template_with_params)
DIAG_NAME_INDEX(err_loader_uninitialized_cant_init)
DIAG_NAME_INDEX(err_loader_uninitialized_extern_decl)
DIAG_NAME_INDEX(err_loader_uninitialized_redeclaration)
DIAG_NAME_INDEX(err_loader_uninitialized_trivial_ctor)
DIAG_NAME_INDEX(err_local_cant_init)
DIAG_NAME_INDEX(err_loongarch_builtin_requires_la32)
DIAG_NAME_INDEX(err_loongarch_builtin_requires_la64)
DIAG_NAME_INDEX(err_lvalue_reference_bind_to_initlist)
DIAG_NAME_INDEX(err_lvalue_reference_bind_to_temporary)
DIAG_NAME_INDEX(err_lvalue_reference_bind_to_unrelated)
DIAG_NAME_INDEX(err_lvalue_to_rvalue_ref)
DIAG_NAME_INDEX(err_machine_mode)
DIAG_NAME_INDEX(err_main_arg_wrong)
DIAG_NAME_INDEX(err_main_global_variable)
DIAG_NAME_INDEX(err_main_returns_nonint)
DIAG_NAME_INDEX(err_main_surplus_args)
DIAG_NAME_INDEX(err_mainlike_template_decl)
DIAG_NAME_INDEX(err_make_signed_integral_only)
DIAG_NAME_INDEX(err_malformed_std_coroutine_handle)
DIAG_NAME_INDEX(err_malformed_std_coroutine_traits)
DIAG_NAME_INDEX(err_malformed_std_initializer_list)
DIAG_NAME_INDEX(err_malformed_std_nothrow)
DIAG_NAME_INDEX(err_matrix_incomplete_index)
DIAG_NAME_INDEX(err_matrix_index_not_integer)
DIAG_NAME_INDEX(err_matrix_index_outside_range)
DIAG_NAME_INDEX(err_matrix_separate_incomplete_index)
DIAG_NAME_INDEX(err_matrix_subscript_comma)
DIAG_NAME_INDEX(err_maybe_falloff_nonvoid_block)
DIAG_NAME_INDEX(err_mem_init_not_member_or_class)
DIAG_NAME_INDEX(err_mem_init_not_member_or_class_suggest)
DIAG_NAME_INDEX(err_member_call_without_object)
DIAG_NAME_INDEX(err_member_decl_does_not_match)
DIAG_NAME_INDEX(err_member_decl_does_not_match_suggest)
DIAG_NAME_INDEX(err_member_def_does_not_match_ret_type)
DIAG_NAME_INDEX(err_member_def_undefined_record)
DIAG_NAME_INDEX(err_member_extra_qualification)
DIAG_NAME_INDEX(err_member_function_call_bad_cvr)
DIAG_NAME_INDEX(err_member_function_call_bad_ref)
DIAG_NAME_INDEX(err_member_function_call_bad_type)
DIAG_NAME_INDEX(err_member_function_initialization)
DIAG_NAME_INDEX(err_member_name_of_class)
DIAG_NAME_INDEX(err_member_not_yet_instantiated)
DIAG_NAME_INDEX(err_member_qualification)
DIAG_NAME_INDEX(err_member_redeclared)
DIAG_NAME_INDEX(err_member_redeclared_in_instantiation)
DIAG_NAME_INDEX(err_member_reference_needs_call)
DIAG_NAME_INDEX(err_member_with_template_arguments)
DIAG_NAME_INDEX(err_mempointer_in_nonclass_type)
DIAG_NAME_INDEX(err_memptr_conv_via_virtual)
DIAG_NAME_INDEX(err_memptr_incomplete)
DIAG_NAME_INDEX(err_memtag_any2arg_pointer)
DIAG_NAME_INDEX(err_memtag_arg_must_be_integer)
DIAG_NAME_INDEX(err_memtag_arg_must_be_pointer)
DIAG_NAME_INDEX(err_memtag_arg_null_or_pointer)
DIAG_NAME_INDEX(err_messaging_class_with_direct_method)
DIAG_NAME_INDEX(err_messaging_super_with_direct_method)
DIAG_NAME_INDEX(err_messaging_unqualified_id_with_direct_method)
DIAG_NAME_INDEX(err_method_kernel)
DIAG_NAME_INDEX(err_method_not_found_with_typo)
DIAG_NAME_INDEX(err_mips_builtin_requires_dsp)
DIAG_NAME_INDEX(err_mips_builtin_requires_dspr2)
DIAG_NAME_INDEX(err_mips_builtin_requires_msa)
DIAG_NAME_INDEX(err_mips_fp64_req)
DIAG_NAME_INDEX(err_mismatched_code_seg_base)
DIAG_NAME_INDEX(err_mismatched_code_seg_override)
DIAG_NAME_INDEX(err_mismatched_exception_spec)
DIAG_NAME_INDEX(err_mismatched_exception_spec_explicit_instantiation)
DIAG_NAME_INDEX(err_mismatched_ms_inheritance)
DIAG_NAME_INDEX(err_mismatched_owning_module)
DIAG_NAME_INDEX(err_mismatched_uuid)
DIAG_NAME_INDEX(err_mismatched_visibility)
DIAG_NAME_INDEX(err_misplaced_ellipsis_in_declaration)
DIAG_NAME_INDEX(err_misplaced_ivar)
DIAG_NAME_INDEX(err_missing_actual_pipe_type)
DIAG_NAME_INDEX(err_missing_atsign_prefix)
DIAG_NAME_INDEX(err_missing_before_module_end)
DIAG_NAME_INDEX(err_missing_catch_finally)
DIAG_NAME_INDEX(err_missing_comma_before_ellipsis)
DIAG_NAME_INDEX(err_missing_default_ctor)
DIAG_NAME_INDEX(err_missing_dependent_template_keyword)
DIAG_NAME_INDEX(err_missing_end_of_definition)
DIAG_NAME_INDEX(err_missing_exception_specification)
DIAG_NAME_INDEX(err_missing_method_context)
DIAG_NAME_INDEX(err_missing_module)
DIAG_NAME_INDEX(err_missing_module_name)
DIAG_NAME_INDEX(err_missing_open_square_message_send)
DIAG_NAME_INDEX(err_missing_param)
DIAG_NAME_INDEX(err_missing_property_context)
DIAG_NAME_INDEX(err_missing_property_interface)
DIAG_NAME_INDEX(err_missing_property_ivar_decl)
DIAG_NAME_INDEX(err_missing_type_specifier)
DIAG_NAME_INDEX(err_missing_vfs_overlay_file)
DIAG_NAME_INDEX(err_missing_whitespace_digraph)
DIAG_NAME_INDEX(err_mixing_cxx_try_seh_try)
DIAG_NAME_INDEX(err_mmap_config_macro_submodule)
DIAG_NAME_INDEX(err_mmap_conflicting_export_as)
DIAG_NAME_INDEX(err_mmap_duplicate_header_attribute)
DIAG_NAME_INDEX(err_mmap_expected_attribute)
DIAG_NAME_INDEX(err_mmap_expected_config_macro)
DIAG_NAME_INDEX(err_mmap_expected_conflicts_comma)
DIAG_NAME_INDEX(err_mmap_expected_conflicts_message)
DIAG_NAME_INDEX(err_mmap_expected_export_wildcard)
DIAG_NAME_INDEX(err_mmap_expected_feature)
DIAG_NAME_INDEX(err_mmap_expected_header)
DIAG_NAME_INDEX(err_mmap_expected_header_attribute)
DIAG_NAME_INDEX(err_mmap_expected_inferred_member)
DIAG_NAME_INDEX(err_mmap_expected_lbrace)
DIAG_NAME_INDEX(err_mmap_expected_lbrace_wildcard)
DIAG_NAME_INDEX(err_mmap_expected_library_name)
DIAG_NAME_INDEX(err_mmap_expected_member)
DIAG_NAME_INDEX(err_mmap_expected_mmap_file)
DIAG_NAME_INDEX(err_mmap_expected_module)
DIAG_NAME_INDEX(err_mmap_expected_module_name)
DIAG_NAME_INDEX(err_mmap_expected_rbrace)
DIAG_NAME_INDEX(err_mmap_expected_rsquare)
DIAG_NAME_INDEX(err_mmap_explicit_inferred_framework)
DIAG_NAME_INDEX(err_mmap_explicit_top_level)
DIAG_NAME_INDEX(err_mmap_inferred_framework_submodule)
DIAG_NAME_INDEX(err_mmap_inferred_no_umbrella)
DIAG_NAME_INDEX(err_mmap_inferred_redef)
DIAG_NAME_INDEX(err_mmap_invalid_header_attribute_value)
DIAG_NAME_INDEX(err_mmap_missing_exclude_name)
DIAG_NAME_INDEX(err_mmap_missing_module_qualified)
DIAG_NAME_INDEX(err_mmap_missing_module_unqualified)
DIAG_NAME_INDEX(err_mmap_missing_parent_module)
DIAG_NAME_INDEX(err_mmap_module_id)
DIAG_NAME_INDEX(err_mmap_module_redefinition)
DIAG_NAME_INDEX(err_mmap_nested_submodule_id)
DIAG_NAME_INDEX(err_mmap_submodule_export_as)
DIAG_NAME_INDEX(err_mmap_top_level_inferred_submodule)
DIAG_NAME_INDEX(err_mmap_umbrella_clash)
DIAG_NAME_INDEX(err_mmap_unknown_token)
DIAG_NAME_INDEX(err_mmap_use_decl_submodule)
DIAG_NAME_INDEX(err_mode_not_primitive)
DIAG_NAME_INDEX(err_mode_wrong_type)
DIAG_NAME_INDEX(err_modifier_expected_colon)
DIAG_NAME_INDEX(err_module_build_disabled)
DIAG_NAME_INDEX(err_module_build_requires_fmodules)
DIAG_NAME_INDEX(err_module_build_shadowed_submodule)
DIAG_NAME_INDEX(err_module_cannot_create_includes)
DIAG_NAME_INDEX(err_module_cycle)
DIAG_NAME_INDEX(err_module_decl_in_header_unit)
DIAG_NAME_INDEX(err_module_decl_in_module_map_module)
DIAG_NAME_INDEX(err_module_decl_not_at_start)
DIAG_NAME_INDEX(err_module_declaration_missing)
DIAG_NAME_INDEX(err_module_declaration_missing_after_global_module_introducer)
DIAG_NAME_INDEX(err_module_different_modmap)
DIAG_NAME_INDEX(err_module_expected_ident)
DIAG_NAME_INDEX(err_module_expected_semi)
DIAG_NAME_INDEX(err_module_file_conflict)
DIAG_NAME_INDEX(err_module_file_missing_top_level_submodule)
DIAG_NAME_INDEX(err_module_file_not_module)
DIAG_NAME_INDEX(err_module_format_unhandled)
DIAG_NAME_INDEX(err_module_fragment_exported)
DIAG_NAME_INDEX(err_module_header_file_not_found)
DIAG_NAME_INDEX(err_module_header_missing)
DIAG_NAME_INDEX(err_module_import_in_implementation)
DIAG_NAME_INDEX(err_module_import_not_at_top_level_fatal)
DIAG_NAME_INDEX(err_module_interface_implementation_mismatch)
DIAG_NAME_INDEX(err_module_interface_requires_cpp_modules)
DIAG_NAME_INDEX(err_module_map_not_found)
DIAG_NAME_INDEX(err_module_no_size_mtime_for_header)
DIAG_NAME_INDEX(err_module_not_built)
DIAG_NAME_INDEX(err_module_not_defined)
DIAG_NAME_INDEX(err_module_not_found)
DIAG_NAME_INDEX(err_module_odr_violation_definition_data)
DIAG_NAME_INDEX(err_module_odr_violation_different_definitions)
DIAG_NAME_INDEX(err_module_odr_violation_different_instantiations)
DIAG_NAME_INDEX(err_module_odr_violation_enum)
DIAG_NAME_INDEX(err_module_odr_violation_field)
DIAG_NAME_INDEX(err_module_odr_violation_function)
DIAG_NAME_INDEX(err_module_odr_violation_method_params)
DIAG_NAME_INDEX(err_module_odr_violation_mismatch_decl)
DIAG_NAME_INDEX(err_module_odr_violation_mismatch_decl_unknown)
DIAG_NAME_INDEX(err_module_odr_violation_missing_decl)
DIAG_NAME_INDEX(err_module_odr_violation_objc_interface)
DIAG_NAME_INDEX(err_module_odr_violation_objc_method)
DIAG_NAME_INDEX(err_module_odr_violation_objc_property)
DIAG_NAME_INDEX(err_module_odr_violation_record)
DIAG_NAME_INDEX(err_module_odr_violation_referenced_protocols)
DIAG_NAME_INDEX(err_module_odr_violation_template_parameter)
DIAG_NAME_INDEX(err_module_odr_violation_typedef)
DIAG_NAME_INDEX(err_module_odr_violation_variable)
DIAG_NAME_INDEX(err_module_prebuilt)
DIAG_NAME_INDEX(err_module_private_local)
DIAG_NAME_INDEX(err_module_private_local_class)
DIAG_NAME_INDEX(err_module_private_specialization)
DIAG_NAME_INDEX(err_module_rebuild_finalized)
DIAG_NAME_INDEX(err_module_redeclaration)
DIAG_NAME_INDEX(err_module_redefinition)
DIAG_NAME_INDEX(err_module_self_import)
DIAG_NAME_INDEX(err_module_self_import_cxx20)
DIAG_NAME_INDEX(err_module_shadowed)
DIAG_NAME_INDEX(err_module_unable_to_hash_content)
DIAG_NAME_INDEX(err_module_unavailable)
DIAG_NAME_INDEX(err_module_unimported_use)
DIAG_NAME_INDEX(err_module_unimported_use_header)
DIAG_NAME_INDEX(err_module_unimported_use_multiple)
DIAG_NAME_INDEX(err_modules_embed_file_not_found)
DIAG_NAME_INDEX(err_ms___leave_not_in___try)
DIAG_NAME_INDEX(err_ms_asm_bitfield_unsupported)
DIAG_NAME_INDEX(err_ms_attributes_not_enabled)
DIAG_NAME_INDEX(err_ms_declspec_type)
DIAG_NAME_INDEX(err_ms_property_duplicate_accessor)
DIAG_NAME_INDEX(err_ms_property_expected_accessor_name)
DIAG_NAME_INDEX(err_ms_property_expected_comma_or_rparen)
DIAG_NAME_INDEX(err_ms_property_expected_equal)
DIAG_NAME_INDEX(err_ms_property_has_set_accessor)
DIAG_NAME_INDEX(err_ms_property_initializer)
DIAG_NAME_INDEX(err_ms_property_missing_accessor_kind)
DIAG_NAME_INDEX(err_ms_property_no_getter_or_putter)
DIAG_NAME_INDEX(err_ms_property_unknown_accessor)
DIAG_NAME_INDEX(err_ms_va_start_used_in_sysv_function)
DIAG_NAME_INDEX(err_msasm_unable_to_create_target)
DIAG_NAME_INDEX(err_msasm_unsupported_arch)
DIAG_NAME_INDEX(err_msvc_annotation_wide_str)
DIAG_NAME_INDEX(err_mt_message)
DIAG_NAME_INDEX(err_multichar_character_literal)
DIAG_NAME_INDEX(err_multiple_base_initialization)
DIAG_NAME_INDEX(err_multiple_def_index)
DIAG_NAME_INDEX(err_multiple_default_labels_defined)
DIAG_NAME_INDEX(err_multiple_final_overriders)
DIAG_NAME_INDEX(err_multiple_mem_initialization)
DIAG_NAME_INDEX(err_multiple_mem_union_initialization)
DIAG_NAME_INDEX(err_multiple_template_declarators)
DIAG_NAME_INDEX(err_multiversion_after_used)
DIAG_NAME_INDEX(err_multiversion_diff)
DIAG_NAME_INDEX(err_multiversion_disallowed_other_attr)
DIAG_NAME_INDEX(err_multiversion_doesnt_support)
DIAG_NAME_INDEX(err_multiversion_duplicate)
DIAG_NAME_INDEX(err_multiversion_noproto)
DIAG_NAME_INDEX(err_multiversion_not_allowed_on_main)
DIAG_NAME_INDEX(err_multiversion_not_supported)
DIAG_NAME_INDEX(err_multiversion_required_in_redecl)
DIAG_NAME_INDEX(err_multiversion_types_mixed)
DIAG_NAME_INDEX(err_musttail_callconv_mismatch)
DIAG_NAME_INDEX(err_musttail_forbidden_from_this_context)
DIAG_NAME_INDEX(err_musttail_member_mismatch)
DIAG_NAME_INDEX(err_musttail_mismatch)
DIAG_NAME_INDEX(err_musttail_needs_call)
DIAG_NAME_INDEX(err_musttail_needs_prototype)
DIAG_NAME_INDEX(err_musttail_needs_trivial_args)
DIAG_NAME_INDEX(err_musttail_no_variadic)
DIAG_NAME_INDEX(err_musttail_scope)
DIAG_NAME_INDEX(err_musttail_structors_forbidden)
DIAG_NAME_INDEX(err_mutable_const)
DIAG_NAME_INDEX(err_mutable_function)
DIAG_NAME_INDEX(err_mutable_nonmember)
DIAG_NAME_INDEX(err_mutable_reference)
DIAG_NAME_INDEX(err_namespace_nonnamespace_scope)
DIAG_NAME_INDEX(err_need_header_before_placement_new)
DIAG_NAME_INDEX(err_need_header_before_typeid)
DIAG_NAME_INDEX(err_nested_name_member_ref_lookup_ambiguous)
DIAG_NAME_INDEX(err_nested_name_spec_is_not_class)
DIAG_NAME_INDEX(err_nested_name_spec_non_tag)
DIAG_NAME_INDEX(err_nested_non_static_member_use)
DIAG_NAME_INDEX(err_nested_pointer_qualifier_mismatch)
DIAG_NAME_INDEX(err_nested_redefinition)
DIAG_NAME_INDEX(err_new_abi_tag_on_redeclaration)
DIAG_NAME_INDEX(err_new_array_init_args)
DIAG_NAME_INDEX(err_new_array_nonconst)
DIAG_NAME_INDEX(err_new_array_of_auto)
DIAG_NAME_INDEX(err_new_array_size_unknown_from_init)
DIAG_NAME_INDEX(err_new_incomplete_or_sizeless_type)
DIAG_NAME_INDEX(err_no_accessor_for_property)
DIAG_NAME_INDEX(err_no_base_classes)
DIAG_NAME_INDEX(err_no_declarators)
DIAG_NAME_INDEX(err_no_dynamic_cast_with_fno_rtti)
DIAG_NAME_INDEX(err_no_external_assembler)
DIAG_NAME_INDEX(err_no_matching_local_friend)
DIAG_NAME_INDEX(err_no_matching_local_friend_suggest)
DIAG_NAME_INDEX(err_no_matching_param)
DIAG_NAME_INDEX(err_no_member)
DIAG_NAME_INDEX(err_no_member_overloaded_arrow)
DIAG_NAME_INDEX(err_no_member_suggest)
DIAG_NAME_INDEX(err_no_member_template)
DIAG_NAME_INDEX(err_no_member_template_suggest)
DIAG_NAME_INDEX(err_no_nsconstant_string_class)
DIAG_NAME_INDEX(err_no_submodule)
DIAG_NAME_INDEX(err_no_submodule_suggest)
DIAG_NAME_INDEX(err_no_subobject_property_setting)
DIAG_NAME_INDEX(err_no_suitable_delete_member_function_found)
DIAG_NAME_INDEX(err_no_super_class_message)
DIAG_NAME_INDEX(err_no_template)
DIAG_NAME_INDEX(err_no_template_suggest)
DIAG_NAME_INDEX(err_no_typeid_with_fno_rtti)
DIAG_NAME_INDEX(err_no_viable_destructor)
DIAG_NAME_INDEX(err_nogetter_property_compound_assignment)
DIAG_NAME_INDEX(err_nogetter_property_incdec)
DIAG_NAME_INDEX(err_non_asm_stmt_in_naked_function)
DIAG_NAME_INDEX(err_non_bool_atomic_constraint)
DIAG_NAME_INDEX(err_non_c_like_anon_struct_in_typedef)
DIAG_NAME_INDEX(err_non_constant_constraint_expression)
DIAG_NAME_INDEX(err_non_consteval_override)
DIAG_NAME_INDEX(err_non_default_visibility_dllimport)
DIAG_NAME_INDEX(err_non_deleted_override)
DIAG_NAME_INDEX(err_non_designated_init_used)
DIAG_NAME_INDEX(err_non_extern_extern)
DIAG_NAME_INDEX(err_non_first_default_compare_deletes)
DIAG_NAME_INDEX(err_non_first_default_compare_in_class)
DIAG_NAME_INDEX(err_non_local_variable_decl_in_for)
DIAG_NAME_INDEX(err_non_static_static)
DIAG_NAME_INDEX(err_non_template_in_member_template_id_suggest)
DIAG_NAME_INDEX(err_non_template_in_template_id)
DIAG_NAME_INDEX(err_non_template_in_template_id_suggest)
DIAG_NAME_INDEX(err_non_thread_thread)
DIAG_NAME_INDEX(err_non_trivial_c_union_in_invalid_context)
DIAG_NAME_INDEX(err_non_type_template_arg_addr_label_diff)
DIAG_NAME_INDEX(err_non_type_template_arg_subobject)
DIAG_NAME_INDEX(err_non_type_template_arg_unsupported)
DIAG_NAME_INDEX(err_non_type_template_in_nested_name_specifier)
DIAG_NAME_INDEX(err_non_type_template_parm_type_deduction_failure)
DIAG_NAME_INDEX(err_non_variable_decl_in_for)
DIAG_NAME_INDEX(err_non_virtual_pure)
DIAG_NAME_INDEX(err_nonfunction_block_type)
DIAG_NAME_INDEX(err_nonstatic_member_out_of_line)
DIAG_NAME_INDEX(err_nontemporal_builtin_must_be_pointer)
DIAG_NAME_INDEX(err_nontemporal_builtin_must_be_pointer_intfltptr_or_vector)
DIAG_NAME_INDEX(err_noreturn_block_has_return_expr)
DIAG_NAME_INDEX(err_noreturn_lambda_has_return_expr)
DIAG_NAME_INDEX(err_noreturn_non_function)
DIAG_NAME_INDEX(err_nosetter_property_assignment)
DIAG_NAME_INDEX(err_nosetter_property_incdec)
DIAG_NAME_INDEX(err_not_class_template_specialization)
DIAG_NAME_INDEX(err_not_direct_base_or_virtual)
DIAG_NAME_INDEX(err_not_found_by_two_phase_lookup)
DIAG_NAME_INDEX(err_not_integral_type_anon_bitfield)
DIAG_NAME_INDEX(err_not_integral_type_bitfield)
DIAG_NAME_INDEX(err_not_tag_in_scope)
DIAG_NAME_INDEX(err_ns_attribute_wrong_parameter_type)
DIAG_NAME_INDEX(err_nsconsumed_attribute_mismatch)
DIAG_NAME_INDEX(err_nserrordomain_invalid_decl)
DIAG_NAME_INDEX(err_nserrordomain_wrong_type)
DIAG_NAME_INDEX(err_nsnumber_nonliteral_unary)
DIAG_NAME_INDEX(err_nsobject_attribute)
DIAG_NAME_INDEX(err_nsreturns_retained_attribute_mismatch)
DIAG_NAME_INDEX(err_nullability_conflicting)
DIAG_NAME_INDEX(err_nullability_cs_multilevel)
DIAG_NAME_INDEX(err_nullability_nonpointer)
DIAG_NAME_INDEX(err_nullptr_cast)
DIAG_NAME_INDEX(err_objc_array_of_interfaces)
DIAG_NAME_INDEX(err_objc_attr_not_id)
DIAG_NAME_INDEX(err_objc_attr_protocol_requires_definition)
DIAG_NAME_INDEX(err_objc_attr_typedef_not_id)
DIAG_NAME_INDEX(err_objc_attr_typedef_not_void_pointer)
DIAG_NAME_INDEX(err_objc_bridged_related_invalid_class)
DIAG_NAME_INDEX(err_objc_bridged_related_invalid_class_name)
DIAG_NAME_INDEX(err_objc_bridged_related_known_method)
DIAG_NAME_INDEX(err_objc_cf_bridged_not_interface)
DIAG_NAME_INDEX(err_objc_concat_string)
DIAG_NAME_INDEX(err_objc_decls_may_only_appear_in_global_scope)
DIAG_NAME_INDEX(err_objc_direct_duplicate_decl)
DIAG_NAME_INDEX(err_objc_direct_dynamic_property)
DIAG_NAME_INDEX(err_objc_direct_impl_decl_mismatch)
DIAG_NAME_INDEX(err_objc_direct_missing_on_decl)
DIAG_NAME_INDEX(err_objc_direct_on_override)
DIAG_NAME_INDEX(err_objc_direct_on_protocol)
DIAG_NAME_INDEX(err_objc_direct_protocol_conformance)
DIAG_NAME_INDEX(err_objc_directive_only_in_protocol)
DIAG_NAME_INDEX(err_objc_exceptions_disabled)
DIAG_NAME_INDEX(err_objc_expected_equal_for_getter)
DIAG_NAME_INDEX(err_objc_expected_equal_for_setter)
DIAG_NAME_INDEX(err_objc_expected_property_attr)
DIAG_NAME_INDEX(err_objc_expected_selector_for_getter_setter)
DIAG_NAME_INDEX(err_objc_expected_type_parameter)
DIAG_NAME_INDEX(err_objc_for_range_init_stmt)
DIAG_NAME_INDEX(err_objc_illegal_boxed_expression_type)
DIAG_NAME_INDEX(err_objc_illegal_interface_qual)
DIAG_NAME_INDEX(err_objc_illegal_visibility_spec)
DIAG_NAME_INDEX(err_objc_incomplete_boxed_expression_type)
DIAG_NAME_INDEX(err_objc_index_incomplete_class_type)
DIAG_NAME_INDEX(err_objc_indexing_method_result_type)
DIAG_NAME_INDEX(err_objc_kindof_nonobject)
DIAG_NAME_INDEX(err_objc_kindof_wrong_position)
DIAG_NAME_INDEX(err_objc_literal_method_sig)
DIAG_NAME_INDEX(err_objc_method_unsupported_param_ret_type)
DIAG_NAME_INDEX(err_objc_missing_end)
DIAG_NAME_INDEX(err_objc_multiple_subscript_type_conversion)
DIAG_NAME_INDEX(err_objc_non_runtime_protocol_in_protocol_expr)
DIAG_NAME_INDEX(err_objc_non_trivially_copyable_boxed_expression_type)
DIAG_NAME_INDEX(err_objc_ns_bridged_invalid_cfobject)
DIAG_NAME_INDEX(err_objc_object_assignment)
DIAG_NAME_INDEX(err_objc_object_catch)
DIAG_NAME_INDEX(err_objc_override_direct_method)
DIAG_NAME_INDEX(err_objc_parameterized_category_nonclass)
DIAG_NAME_INDEX(err_objc_parameterized_forward_class)
DIAG_NAME_INDEX(err_objc_parameterized_forward_class_first)
DIAG_NAME_INDEX(err_objc_parameterized_implementation)
DIAG_NAME_INDEX(err_objc_postfix_attribute)
DIAG_NAME_INDEX(err_objc_postfix_attribute_hint)
DIAG_NAME_INDEX(err_objc_precise_lifetime_bad_type)
DIAG_NAME_INDEX(err_objc_property_attr_mutually_exclusive)
DIAG_NAME_INDEX(err_objc_property_bitfield)
DIAG_NAME_INDEX(err_objc_property_requires_field_name)
DIAG_NAME_INDEX(err_objc_property_requires_object)
DIAG_NAME_INDEX(err_objc_root_class_subclass)
DIAG_NAME_INDEX(err_objc_runtime_visible_category)
DIAG_NAME_INDEX(err_objc_runtime_visible_subclass)
DIAG_NAME_INDEX(err_objc_subscript_base_type)
DIAG_NAME_INDEX(err_objc_subscript_dic_object_type)
DIAG_NAME_INDEX(err_objc_subscript_index_type)
DIAG_NAME_INDEX(err_objc_subscript_key_type)
DIAG_NAME_INDEX(err_objc_subscript_method_not_found)
DIAG_NAME_INDEX(err_objc_subscript_object_type)
DIAG_NAME_INDEX(err_objc_subscript_pointer)
DIAG_NAME_INDEX(err_objc_subscript_type_conversion)
DIAG_NAME_INDEX(err_objc_synchronized_expects_object)
DIAG_NAME_INDEX(err_objc_throw_expects_object)
DIAG_NAME_INDEX(err_objc_type_arg_does_not_match_bound)
DIAG_NAME_INDEX(err_objc_type_arg_explicit_nullability)
DIAG_NAME_INDEX(err_objc_type_arg_missing)
DIAG_NAME_INDEX(err_objc_type_arg_missing_star)
DIAG_NAME_INDEX(err_objc_type_arg_not_id_compatible)
DIAG_NAME_INDEX(err_objc_type_arg_qualified)
DIAG_NAME_INDEX(err_objc_type_args_after_protocols)
DIAG_NAME_INDEX(err_objc_type_args_and_protocols)
DIAG_NAME_INDEX(err_objc_type_args_non_class)
DIAG_NAME_INDEX(err_objc_type_args_non_parameterized_class)
DIAG_NAME_INDEX(err_objc_type_args_specialized_class)
DIAG_NAME_INDEX(err_objc_type_args_wrong_arity)
DIAG_NAME_INDEX(err_objc_type_param_arity_mismatch)
DIAG_NAME_INDEX(err_objc_type_param_bound_conflict)
DIAG_NAME_INDEX(err_objc_type_param_bound_explicit_nullability)
DIAG_NAME_INDEX(err_objc_type_param_bound_missing)
DIAG_NAME_INDEX(err_objc_type_param_bound_missing_pointer)
DIAG_NAME_INDEX(err_objc_type_param_bound_nonobject)
DIAG_NAME_INDEX(err_objc_type_param_bound_qualified)
DIAG_NAME_INDEX(err_objc_type_param_redecl)
DIAG_NAME_INDEX(err_objc_type_param_variance_conflict)
DIAG_NAME_INDEX(err_objc_unexpected_atend)
DIAG_NAME_INDEX(err_objc_unexpected_attr)
DIAG_NAME_INDEX(err_objc_unknown_at)
DIAG_NAME_INDEX(err_objc_var_decl_inclass)
DIAG_NAME_INDEX(err_objc_variable_sized_type_not_at_end)
DIAG_NAME_INDEX(err_objc_weak_unsupported)
DIAG_NAME_INDEX(err_objc_weak_with_gc)
DIAG_NAME_INDEX(err_objcbridge_related_expected_related_class)
DIAG_NAME_INDEX(err_objcbridge_related_selector_name)
DIAG_NAME_INDEX(err_object_cannot_be_passed_returned_by_value)
DIAG_NAME_INDEX(err_odr_different_num_template_parameters)
DIAG_NAME_INDEX(err_odr_different_template_parameter_kind)
DIAG_NAME_INDEX(err_odr_field_type_inconsistent)
DIAG_NAME_INDEX(err_odr_function_type_inconsistent)
DIAG_NAME_INDEX(err_odr_ivar_type_inconsistent)
DIAG_NAME_INDEX(err_odr_non_type_parameter_type_inconsistent)
DIAG_NAME_INDEX(err_odr_objc_method_num_params_inconsistent)
DIAG_NAME_INDEX(err_odr_objc_method_param_type_inconsistent)
DIAG_NAME_INDEX(err_odr_objc_method_result_type_inconsistent)
DIAG_NAME_INDEX(err_odr_objc_method_variadic_inconsistent)
DIAG_NAME_INDEX(err_odr_objc_property_impl_kind_inconsistent)
DIAG_NAME_INDEX(err_odr_objc_property_type_inconsistent)
DIAG_NAME_INDEX(err_odr_objc_superclass_inconsistent)
DIAG_NAME_INDEX(err_odr_objc_synthesize_ivar_inconsistent)
DIAG_NAME_INDEX(err_odr_parameter_pack_non_pack)
DIAG_NAME_INDEX(err_odr_tag_type_inconsistent)
DIAG_NAME_INDEX(err_odr_variable_multiple_def)
DIAG_NAME_INDEX(err_odr_variable_type_inconsistent)
DIAG_NAME_INDEX(err_offsetof_array_type)
DIAG_NAME_INDEX(err_offsetof_bitfield)
DIAG_NAME_INDEX(err_offsetof_field_of_virtual_base)
DIAG_NAME_INDEX(err_offsetof_incomplete_type)
DIAG_NAME_INDEX(err_offsetof_record_type)
DIAG_NAME_INDEX(err_omp_adjust_arg_multiple_clauses)
DIAG_NAME_INDEX(err_omp_aligned_expected_array_or_ptr)
DIAG_NAME_INDEX(err_omp_allocator_not_in_uses_allocators)
DIAG_NAME_INDEX(err_omp_allocator_used_in_clauses)
DIAG_NAME_INDEX(err_omp_ambiguous_conversion)
DIAG_NAME_INDEX(err_omp_append_args_with_varargs)
DIAG_NAME_INDEX(err_omp_argument_type_isdeviceptr)
DIAG_NAME_INDEX(err_omp_array_section_use)
DIAG_NAME_INDEX(err_omp_array_shaping_use)
DIAG_NAME_INDEX(err_omp_at_least_one_motion_clause_required)
DIAG_NAME_INDEX(err_omp_atomic_capture_not_compound_statement)
DIAG_NAME_INDEX(err_omp_atomic_capture_not_expression_statement)
DIAG_NAME_INDEX(err_omp_atomic_compare)
DIAG_NAME_INDEX(err_omp_atomic_compare_capture)
DIAG_NAME_INDEX(err_omp_atomic_incompatible_mem_order_clause)
DIAG_NAME_INDEX(err_omp_atomic_not_expression_statement)
DIAG_NAME_INDEX(err_omp_atomic_read_not_expression_statement)
DIAG_NAME_INDEX(err_omp_atomic_several_clauses)
DIAG_NAME_INDEX(err_omp_atomic_update_not_expression_statement)
DIAG_NAME_INDEX(err_omp_atomic_write_not_expression_statement)
DIAG_NAME_INDEX(err_omp_begin_declare_target_unexpected_implicit_to_clause)
DIAG_NAME_INDEX(err_omp_bit_fields_forbidden_in_clause)
DIAG_NAME_INDEX(err_omp_cannot_update_with_internal_linkage)
DIAG_NAME_INDEX(err_omp_clause_floating_type_arg)
DIAG_NAME_INDEX(err_omp_clause_not_arithmetic_type_arg)
DIAG_NAME_INDEX(err_omp_clause_requires_dispatch_construct)
DIAG_NAME_INDEX(err_omp_clauses_mutually_exclusive)
DIAG_NAME_INDEX(err_omp_const_list_item)
DIAG_NAME_INDEX(err_omp_const_not_mutable_variable)
DIAG_NAME_INDEX(err_omp_const_variable)
DIAG_NAME_INDEX(err_omp_critical_with_hint)
DIAG_NAME_INDEX(err_omp_decl_in_declare_simd_variant)
DIAG_NAME_INDEX(err_omp_declare_mapper_redefinition)
DIAG_NAME_INDEX(err_omp_declare_mapper_wrong_var)
DIAG_NAME_INDEX(err_omp_declare_reduction_redefinition)
DIAG_NAME_INDEX(err_omp_declare_simd_inbranch_notinbranch)
DIAG_NAME_INDEX(err_omp_declare_target_indirect_device_type)
DIAG_NAME_INDEX(err_omp_declare_target_missing_enter_or_link_clause)
DIAG_NAME_INDEX(err_omp_declare_target_missing_to_or_link_clause)
DIAG_NAME_INDEX(err_omp_declare_target_multiple)
DIAG_NAME_INDEX(err_omp_declare_target_to_and_link)
DIAG_NAME_INDEX(err_omp_declare_target_unexpected_clause)
DIAG_NAME_INDEX(err_omp_declare_target_unexpected_clause_52)
DIAG_NAME_INDEX(err_omp_declare_target_unexpected_enter_clause)
DIAG_NAME_INDEX(err_omp_declare_target_unexpected_to_clause)
DIAG_NAME_INDEX(err_omp_declare_target_wrong_clause_after_implicit_enter)
DIAG_NAME_INDEX(err_omp_declare_target_wrong_clause_after_implicit_to)
DIAG_NAME_INDEX(err_omp_declare_variant_diff)
DIAG_NAME_INDEX(err_omp_declare_variant_doesnt_support)
DIAG_NAME_INDEX(err_omp_declare_variant_duplicate_nested_trait)
DIAG_NAME_INDEX(err_omp_declare_variant_incompat_attributes)
DIAG_NAME_INDEX(err_omp_declare_variant_incompat_types)
DIAG_NAME_INDEX(err_omp_declare_variant_nested_user_condition)
DIAG_NAME_INDEX(err_omp_declare_variant_prototype_required)
DIAG_NAME_INDEX(err_omp_declare_variant_same_base_function)
DIAG_NAME_INDEX(err_omp_declare_variant_user_condition_not_constant)
DIAG_NAME_INDEX(err_omp_declare_variant_wrong_clause)
DIAG_NAME_INDEX(err_omp_defaultmap_no_attr_for_variable)
DIAG_NAME_INDEX(err_omp_depend_clause_thread_simd)
DIAG_NAME_INDEX(err_omp_depend_modifier_not_iterator)
DIAG_NAME_INDEX(err_omp_depend_sink_expected_loop_iteration)
DIAG_NAME_INDEX(err_omp_depend_sink_expected_plus_minus)
DIAG_NAME_INDEX(err_omp_depend_sink_source_with_modifier)
DIAG_NAME_INDEX(err_omp_depend_sink_unexpected_expr)
DIAG_NAME_INDEX(err_omp_depend_zero_length_array_section_not_allowed)
DIAG_NAME_INDEX(err_omp_depobj_expected)
DIAG_NAME_INDEX(err_omp_depobj_single_clause_expected)
DIAG_NAME_INDEX(err_omp_device_ancestor_without_requires_reverse_offload)
DIAG_NAME_INDEX(err_omp_device_type_mismatch)
DIAG_NAME_INDEX(err_omp_directive_before_requires)
DIAG_NAME_INDEX(err_omp_dispatch_statement_call)
DIAG_NAME_INDEX(err_omp_duplicate_map_type_modifier)
DIAG_NAME_INDEX(err_omp_duplicate_motion_modifier)
DIAG_NAME_INDEX(err_omp_expected_access_to_data_field)
DIAG_NAME_INDEX(err_omp_expected_addressable_lvalue_or_array_item)
DIAG_NAME_INDEX(err_omp_expected_array_alloctraits)
DIAG_NAME_INDEX(err_omp_expected_base_var_name)
DIAG_NAME_INDEX(err_omp_expected_clause)
DIAG_NAME_INDEX(err_omp_expected_clause_argument)
DIAG_NAME_INDEX(err_omp_expected_colon)
DIAG_NAME_INDEX(err_omp_expected_context_selector)
DIAG_NAME_INDEX(err_omp_expected_equal_in_iterator)
DIAG_NAME_INDEX(err_omp_expected_identifier_for_critical)
DIAG_NAME_INDEX(err_omp_expected_int_param)
DIAG_NAME_INDEX(err_omp_expected_interop_type)
DIAG_NAME_INDEX(err_omp_expected_named_var_member_or_array_expression)
DIAG_NAME_INDEX(err_omp_expected_omp_depend_t_lvalue)
DIAG_NAME_INDEX(err_omp_expected_predefined_allocator)
DIAG_NAME_INDEX(err_omp_expected_private_copy_for_allocate)
DIAG_NAME_INDEX(err_omp_expected_punc)
DIAG_NAME_INDEX(err_omp_expected_punc_after_interop_mod)
DIAG_NAME_INDEX(err_omp_expected_punc_after_iterator)
DIAG_NAME_INDEX(err_omp_expected_reduction_identifier)
DIAG_NAME_INDEX(err_omp_expected_uniform_param)
DIAG_NAME_INDEX(err_omp_expected_var_arg)
DIAG_NAME_INDEX(err_omp_expected_var_arg_suggest)
DIAG_NAME_INDEX(err_omp_expected_var_name_member_expr)
DIAG_NAME_INDEX(err_omp_expected_var_name_member_expr_or_array_item)
DIAG_NAME_INDEX(err_omp_expected_var_name_member_expr_with_type)
DIAG_NAME_INDEX(err_omp_explicit_conversion)
DIAG_NAME_INDEX(err_omp_firstprivate_incomplete_type)
DIAG_NAME_INDEX(err_omp_flush_order_clause_and_list)
DIAG_NAME_INDEX(err_omp_function_expected)
DIAG_NAME_INDEX(err_omp_function_in_link_clause)
DIAG_NAME_INDEX(err_omp_global_var_arg)
DIAG_NAME_INDEX(err_omp_hint_clause_no_name)
DIAG_NAME_INDEX(err_omp_immediate_directive)
DIAG_NAME_INDEX(err_omp_implied_type_not_found)
DIAG_NAME_INDEX(err_omp_inclusive_exclusive_not_reduction)
DIAG_NAME_INDEX(err_omp_incomplete_type)
DIAG_NAME_INDEX(err_omp_inscan_reduction_expected)
DIAG_NAME_INDEX(err_omp_instantiation_not_supported)
DIAG_NAME_INDEX(err_omp_interop_bad_depend_clause)
DIAG_NAME_INDEX(err_omp_interop_prefer_type)
DIAG_NAME_INDEX(err_omp_interop_type_not_found)
DIAG_NAME_INDEX(err_omp_interop_var_multiple_actions)
DIAG_NAME_INDEX(err_omp_interop_variable_expected)
DIAG_NAME_INDEX(err_omp_interop_variable_wrong_type)
DIAG_NAME_INDEX(err_omp_invalid_dsa)
DIAG_NAME_INDEX(err_omp_invalid_map_this_expr)
DIAG_NAME_INDEX(err_omp_invalid_map_type_for_directive)
DIAG_NAME_INDEX(err_omp_invalid_map_type_modifier_for_directive)
DIAG_NAME_INDEX(err_omp_invalid_mapper)
DIAG_NAME_INDEX(err_omp_invalid_scope)
DIAG_NAME_INDEX(err_omp_invalid_target_decl)
DIAG_NAME_INDEX(err_omp_invariant_dependency)
DIAG_NAME_INDEX(err_omp_invariant_or_linear_dependency)
DIAG_NAME_INDEX(err_omp_iterator_not_integral_or_pointer)
DIAG_NAME_INDEX(err_omp_iterator_step_constant_zero)
DIAG_NAME_INDEX(err_omp_iterator_step_not_integral)
DIAG_NAME_INDEX(err_omp_iterator_use)
DIAG_NAME_INDEX(err_omp_lambda_capture_in_declare_target_not_to)
DIAG_NAME_INDEX(err_omp_lastprivate_conditional_non_scalar)
DIAG_NAME_INDEX(err_omp_lastprivate_incomplete_type)
DIAG_NAME_INDEX(err_omp_lastprivate_loop_var_non_loop_iteration)
DIAG_NAME_INDEX(err_omp_linear_distribute_var_non_loop_iteration)
DIAG_NAME_INDEX(err_omp_linear_expected_int_or_ptr)
DIAG_NAME_INDEX(err_omp_linear_incomplete_type)
DIAG_NAME_INDEX(err_omp_linear_ordered)
DIAG_NAME_INDEX(err_omp_local_var_in_threadprivate_init)
DIAG_NAME_INDEX(err_omp_loop_cannot_use_stmt)
DIAG_NAME_INDEX(err_omp_loop_diff_cxx)
DIAG_NAME_INDEX(err_omp_loop_incr_not_compatible)
DIAG_NAME_INDEX(err_omp_loop_not_canonical_cond)
DIAG_NAME_INDEX(err_omp_loop_not_canonical_incr)
DIAG_NAME_INDEX(err_omp_loop_not_canonical_init)
DIAG_NAME_INDEX(err_omp_loop_var_dsa)
DIAG_NAME_INDEX(err_omp_loop_variable_type)
DIAG_NAME_INDEX(err_omp_map_modifier_not_iterator)
DIAG_NAME_INDEX(err_omp_map_shared_storage)
DIAG_NAME_INDEX(err_omp_map_type_missing)
DIAG_NAME_INDEX(err_omp_map_type_modifier_missing)
DIAG_NAME_INDEX(err_omp_mapper_expected_declarator)
DIAG_NAME_INDEX(err_omp_mapper_illegal_identifier)
DIAG_NAME_INDEX(err_omp_mapper_wrong_type)
DIAG_NAME_INDEX(err_omp_more_one_clause)
DIAG_NAME_INDEX(err_omp_multiple_array_items_in_map_clause)
DIAG_NAME_INDEX(err_omp_negative_expression_in_clause)
DIAG_NAME_INDEX(err_omp_no_clause_for_directive)
DIAG_NAME_INDEX(err_omp_no_dsa_for_variable)
DIAG_NAME_INDEX(err_omp_no_more_if_clause)
DIAG_NAME_INDEX(err_omp_non_lvalue_in_map_or_motion_clauses)
DIAG_NAME_INDEX(err_omp_non_pointer_type_array_shaping_base)
DIAG_NAME_INDEX(err_omp_nonpredefined_allocator_without_traits)
DIAG_NAME_INDEX(err_omp_not_for)
DIAG_NAME_INDEX(err_omp_not_integral)
DIAG_NAME_INDEX(err_omp_not_resolved_reduction_identifier)
DIAG_NAME_INDEX(err_omp_nowait_clause_without_depend)
DIAG_NAME_INDEX(err_omp_once_referenced)
DIAG_NAME_INDEX(err_omp_once_referenced_in_target_update)
DIAG_NAME_INDEX(err_omp_one_defaultmap_each_category)
DIAG_NAME_INDEX(err_omp_ordered_directive_with_param)
DIAG_NAME_INDEX(err_omp_ordered_directive_without_param)
DIAG_NAME_INDEX(err_omp_ordered_simd)
DIAG_NAME_INDEX(err_omp_original_storage_is_shared_and_does_not_contain)
DIAG_NAME_INDEX(err_omp_orphaned_device_directive)
DIAG_NAME_INDEX(err_omp_orphaned_section_directive)
DIAG_NAME_INDEX(err_omp_parallel_reduction_in_task_firstprivate)
DIAG_NAME_INDEX(err_omp_parallel_sections_not_compound_stmt)
DIAG_NAME_INDEX(err_omp_parallel_sections_substmt_not_section)
DIAG_NAME_INDEX(err_omp_param_or_this_in_clause)
DIAG_NAME_INDEX(err_omp_parent_cancel_region_nowait)
DIAG_NAME_INDEX(err_omp_parent_cancel_region_ordered)
DIAG_NAME_INDEX(err_omp_pointer_mapped_along_with_derived_section)
DIAG_NAME_INDEX(err_omp_predefined_allocator_with_traits)
DIAG_NAME_INDEX(err_omp_private_incomplete_type)
DIAG_NAME_INDEX(err_omp_prohibited_region)
DIAG_NAME_INDEX(err_omp_prohibited_region_atomic)
DIAG_NAME_INDEX(err_omp_prohibited_region_critical_same_name)
DIAG_NAME_INDEX(err_omp_prohibited_region_order)
DIAG_NAME_INDEX(err_omp_prohibited_region_simd)
DIAG_NAME_INDEX(err_omp_reduction_id_not_compatible)
DIAG_NAME_INDEX(err_omp_reduction_identifier_mismatch)
DIAG_NAME_INDEX(err_omp_reduction_in_task)
DIAG_NAME_INDEX(err_omp_reduction_incomplete_type)
DIAG_NAME_INDEX(err_omp_reduction_non_addressable_expression)
DIAG_NAME_INDEX(err_omp_reduction_not_inclusive_exclusive)
DIAG_NAME_INDEX(err_omp_reduction_ref_type_arg)
DIAG_NAME_INDEX(err_omp_reduction_task_not_parallel_or_worksharing)
DIAG_NAME_INDEX(err_omp_reduction_vla_unsupported)
DIAG_NAME_INDEX(err_omp_reduction_with_nogroup)
DIAG_NAME_INDEX(err_omp_reduction_wrong_type)
DIAG_NAME_INDEX(err_omp_ref_type_arg)
DIAG_NAME_INDEX(err_omp_region_not_file_context)
DIAG_NAME_INDEX(err_omp_required_access)
DIAG_NAME_INDEX(err_omp_required_clause)
DIAG_NAME_INDEX(err_omp_requires_clause_redeclaration)
DIAG_NAME_INDEX(err_omp_requires_out_inout_depend_type)
DIAG_NAME_INDEX(err_omp_same_pointer_dereferenced)
DIAG_NAME_INDEX(err_omp_scan_single_clause_expected)
DIAG_NAME_INDEX(err_omp_schedule_nonmonotonic_static)
DIAG_NAME_INDEX(err_omp_section_function_type)
DIAG_NAME_INDEX(err_omp_section_incomplete_type)
DIAG_NAME_INDEX(err_omp_section_length_negative)
DIAG_NAME_INDEX(err_omp_section_length_undefined)
DIAG_NAME_INDEX(err_omp_section_not_subset_of_array)
DIAG_NAME_INDEX(err_omp_section_stride_non_positive)
DIAG_NAME_INDEX(err_omp_sections_not_compound_stmt)
DIAG_NAME_INDEX(err_omp_sections_substmt_not_section)
DIAG_NAME_INDEX(err_omp_several_directives_in_region)
DIAG_NAME_INDEX(err_omp_several_mem_order_clauses)
DIAG_NAME_INDEX(err_omp_shaping_dimension_not_positive)
DIAG_NAME_INDEX(err_omp_simd_region_cannot_use_stmt)
DIAG_NAME_INDEX(err_omp_simple_clause_incompatible_with_ordered)
DIAG_NAME_INDEX(err_omp_single_copyprivate_with_nowait)
DIAG_NAME_INDEX(err_omp_single_decl_in_declare_simd_variant)
DIAG_NAME_INDEX(err_omp_sink_and_source_iteration_not_allowd)
DIAG_NAME_INDEX(err_omp_sink_and_source_not_allowed)
DIAG_NAME_INDEX(err_omp_stmt_depends_on_loop_counter)
DIAG_NAME_INDEX(err_omp_target_contains_not_only_teams)
DIAG_NAME_INDEX(err_omp_taskwait_depend_mutexinoutset_not_allowed)
DIAG_NAME_INDEX(err_omp_threadprivate_in_clause)
DIAG_NAME_INDEX(err_omp_threadprivate_in_target)
DIAG_NAME_INDEX(err_omp_threadprivate_incomplete_type)
DIAG_NAME_INDEX(err_omp_typecheck_section_not_integer)
DIAG_NAME_INDEX(err_omp_typecheck_section_value)
DIAG_NAME_INDEX(err_omp_typecheck_shaping_not_integer)
DIAG_NAME_INDEX(err_omp_unexpected_append_op)
DIAG_NAME_INDEX(err_omp_unexpected_call_to_omp_runtime_api)
DIAG_NAME_INDEX(err_omp_unexpected_clause)
DIAG_NAME_INDEX(err_omp_unexpected_clause_value)
DIAG_NAME_INDEX(err_omp_unexpected_directive)
DIAG_NAME_INDEX(err_omp_unexpected_execution_modifier)
DIAG_NAME_INDEX(err_omp_unexpected_schedule_modifier)
DIAG_NAME_INDEX(err_omp_union_type_not_allowed)
DIAG_NAME_INDEX(err_omp_unknown_directive)
DIAG_NAME_INDEX(err_omp_unknown_map_type)
DIAG_NAME_INDEX(err_omp_unknown_map_type_modifier)
DIAG_NAME_INDEX(err_omp_unknown_reduction_identifier_prior_omp_6_0)
DIAG_NAME_INDEX(err_omp_unknown_reduction_identifier_since_omp_6_0)
DIAG_NAME_INDEX(err_omp_unnamed_if_clause)
DIAG_NAME_INDEX(err_omp_unroll_full_variable_trip_count)
DIAG_NAME_INDEX(err_omp_used_in_clause_twice)
DIAG_NAME_INDEX(err_omp_usedeviceptr_not_a_pointer)
DIAG_NAME_INDEX(err_omp_var_expected)
DIAG_NAME_INDEX(err_omp_var_scope)
DIAG_NAME_INDEX(err_omp_var_thread_local)
DIAG_NAME_INDEX(err_omp_var_used)
DIAG_NAME_INDEX(err_omp_variable_in_given_clause_and_dsa)
DIAG_NAME_INDEX(err_omp_variably_modified_type_not_supported)
DIAG_NAME_INDEX(err_omp_variant_ctx_second_match_extension)
DIAG_NAME_INDEX(err_omp_wrong_cancel_region)
DIAG_NAME_INDEX(err_omp_wrong_dependency_iterator_type)
DIAG_NAME_INDEX(err_omp_wrong_device_function_call)
DIAG_NAME_INDEX(err_omp_wrong_dsa)
DIAG_NAME_INDEX(err_omp_wrong_if_directive_name_modifier)
DIAG_NAME_INDEX(err_omp_wrong_inscan_reduction)
DIAG_NAME_INDEX(err_omp_wrong_linear_modifier)
DIAG_NAME_INDEX(err_omp_wrong_linear_modifier_non_reference)
DIAG_NAME_INDEX(err_omp_wrong_ordered_loop_count)
DIAG_NAME_INDEX(err_omp_wrong_simdlen_safelen_values)
DIAG_NAME_INDEX(err_omp_wrong_var_in_declare_reduction)
DIAG_NAME_INDEX(err_only_annotate_after_access_spec)
DIAG_NAME_INDEX(err_only_constructors_take_base_inits)
DIAG_NAME_INDEX(err_only_enums_have_underlying_types)
DIAG_NAME_INDEX(err_opencl_addrspace_scope)
DIAG_NAME_INDEX(err_opencl_atomic_init)
DIAG_NAME_INDEX(err_opencl_bitfields)
DIAG_NAME_INDEX(err_opencl_block_ref_block)
DIAG_NAME_INDEX(err_opencl_block_storage_type)
DIAG_NAME_INDEX(err_opencl_builtin_expected_type)
DIAG_NAME_INDEX(err_opencl_builtin_pipe_arg_num)
DIAG_NAME_INDEX(err_opencl_builtin_pipe_first_arg)
DIAG_NAME_INDEX(err_opencl_builtin_pipe_invalid_access_modifier)
DIAG_NAME_INDEX(err_opencl_builtin_pipe_invalid_arg)
DIAG_NAME_INDEX(err_opencl_builtin_to_addr_invalid_arg)
DIAG_NAME_INDEX(err_opencl_cast_non_zero_to_event_t)
DIAG_NAME_INDEX(err_opencl_cast_to_half)
DIAG_NAME_INDEX(err_opencl_constant_no_init)
DIAG_NAME_INDEX(err_opencl_enqueue_kernel_blocks_no_args)
DIAG_NAME_INDEX(err_opencl_enqueue_kernel_blocks_non_local_void_args)
DIAG_NAME_INDEX(err_opencl_enqueue_kernel_incorrect_args)
DIAG_NAME_INDEX(err_opencl_enqueue_kernel_invalid_local_size_type)
DIAG_NAME_INDEX(err_opencl_enqueue_kernel_local_size_args)
DIAG_NAME_INDEX(err_opencl_ext_vector_component_invalid_length)
DIAG_NAME_INDEX(err_opencl_extension_and_feature_differs)
DIAG_NAME_INDEX(err_opencl_extern_block_declaration)
DIAG_NAME_INDEX(err_opencl_feature_requires)
DIAG_NAME_INDEX(err_opencl_function_pointer)
DIAG_NAME_INDEX(err_opencl_function_variable)
DIAG_NAME_INDEX(err_opencl_global_invalid_addr_space)
DIAG_NAME_INDEX(err_opencl_half_declaration)
DIAG_NAME_INDEX(err_opencl_half_load_store)
DIAG_NAME_INDEX(err_opencl_implicit_vector_conversion)
DIAG_NAME_INDEX(err_opencl_invalid_access_qualifier)
DIAG_NAME_INDEX(err_opencl_invalid_block_declaration)
DIAG_NAME_INDEX(err_opencl_invalid_param)
DIAG_NAME_INDEX(err_opencl_invalid_read_write)
DIAG_NAME_INDEX(err_opencl_invalid_return)
DIAG_NAME_INDEX(err_opencl_invalid_type_array)
DIAG_NAME_INDEX(err_opencl_kernel_attr)
DIAG_NAME_INDEX(err_opencl_logical_exclusive_or)
DIAG_NAME_INDEX(err_opencl_multiple_access_qualifiers)
DIAG_NAME_INDEX(err_opencl_no_main)
DIAG_NAME_INDEX(err_opencl_nonconst_global_sampler)
DIAG_NAME_INDEX(err_opencl_pointer_to_type)
DIAG_NAME_INDEX(err_opencl_ptrptr_kernel_param)
DIAG_NAME_INDEX(err_opencl_requires_extension)
DIAG_NAME_INDEX(err_opencl_scalar_type_rank_greater_than_vector_type)
DIAG_NAME_INDEX(err_opencl_sizeof_alignof_type)
DIAG_NAME_INDEX(err_opencl_taking_address_capture)
DIAG_NAME_INDEX(err_opencl_taking_function_address_parser)
DIAG_NAME_INDEX(err_opencl_ternary_with_block)
DIAG_NAME_INDEX(err_opencl_type_can_only_be_used_as_function_parameter)
DIAG_NAME_INDEX(err_opencl_type_not_found)
DIAG_NAME_INDEX(err_opencl_type_struct_or_union_field)
DIAG_NAME_INDEX(err_opencl_unknown_type_specifier)
DIAG_NAME_INDEX(err_opencl_variadic_function)
DIAG_NAME_INDEX(err_opencl_vla)
DIAG_NAME_INDEX(err_openclcxx_not_supported)
DIAG_NAME_INDEX(err_openclcxx_placement_new)
DIAG_NAME_INDEX(err_openclcxx_virtual_function)
DIAG_NAME_INDEX(err_openmp_default_simd_align_expr)
DIAG_NAME_INDEX(err_openmp_vla_in_task_untied)
DIAG_NAME_INDEX(err_operator_arrow_circular)
DIAG_NAME_INDEX(err_operator_arrow_depth_exceeded)
DIAG_NAME_INDEX(err_operator_delete_dependent_param_type)
DIAG_NAME_INDEX(err_operator_delete_param_type)
DIAG_NAME_INDEX(err_operator_new_default_arg)
DIAG_NAME_INDEX(err_operator_new_delete_declared_in_namespace)
DIAG_NAME_INDEX(err_operator_new_delete_declared_static)
DIAG_NAME_INDEX(err_operator_new_delete_dependent_result_type)
DIAG_NAME_INDEX(err_operator_new_delete_invalid_result_type)
DIAG_NAME_INDEX(err_operator_new_delete_template_too_few_parameters)
DIAG_NAME_INDEX(err_operator_new_delete_too_few_parameters)
DIAG_NAME_INDEX(err_operator_new_dependent_param_type)
DIAG_NAME_INDEX(err_operator_new_param_type)
DIAG_NAME_INDEX(err_operator_overload_default_arg)
DIAG_NAME_INDEX(err_operator_overload_must_be)
DIAG_NAME_INDEX(err_operator_overload_must_be_member)
DIAG_NAME_INDEX(err_operator_overload_needs_class_or_enum)
DIAG_NAME_INDEX(err_operator_overload_post_incdec_must_be_int)
DIAG_NAME_INDEX(err_operator_overload_static)
DIAG_NAME_INDEX(err_operator_overload_variadic)
DIAG_NAME_INDEX(err_opt_not_valid_on_target)
DIAG_NAME_INDEX(err_opt_not_valid_with_opt)
DIAG_NAME_INDEX(err_opt_not_valid_without_opt)
DIAG_NAME_INDEX(err_os_log_argument_too_big)
DIAG_NAME_INDEX(err_os_log_format_not_string_constant)
DIAG_NAME_INDEX(err_out_of_line_constructor_template_id)
DIAG_NAME_INDEX(err_out_of_line_default_deletes)
DIAG_NAME_INDEX(err_out_of_line_qualified_id_type_names_constructor)
DIAG_NAME_INDEX(err_overflow_builtin_bit_int_max_size)
DIAG_NAME_INDEX(err_overflow_builtin_must_be_int)
DIAG_NAME_INDEX(err_overflow_builtin_must_be_ptr_int)
DIAG_NAME_INDEX(err_override_control_interface)
DIAG_NAME_INDEX(err_override_exception_spec)
DIAG_NAME_INDEX(err_ovl_ambiguous_call)
DIAG_NAME_INDEX(err_ovl_ambiguous_conversion_in_cast)
DIAG_NAME_INDEX(err_ovl_ambiguous_init)
DIAG_NAME_INDEX(err_ovl_ambiguous_member_call)
DIAG_NAME_INDEX(err_ovl_ambiguous_object_call)
DIAG_NAME_INDEX(err_ovl_ambiguous_oper_binary)
DIAG_NAME_INDEX(err_ovl_ambiguous_oper_unary)
DIAG_NAME_INDEX(err_ovl_ambiguous_subscript_call)
DIAG_NAME_INDEX(err_ovl_deleted_call)
DIAG_NAME_INDEX(err_ovl_deleted_comparison)
DIAG_NAME_INDEX(err_ovl_deleted_conversion_in_cast)
DIAG_NAME_INDEX(err_ovl_deleted_init)
DIAG_NAME_INDEX(err_ovl_deleted_member_call)
DIAG_NAME_INDEX(err_ovl_deleted_object_call)
DIAG_NAME_INDEX(err_ovl_deleted_oper)
DIAG_NAME_INDEX(err_ovl_deleted_special_init)
DIAG_NAME_INDEX(err_ovl_deleted_special_oper)
DIAG_NAME_INDEX(err_ovl_diff_return_type)
DIAG_NAME_INDEX(err_ovl_no_conversion_in_cast)
DIAG_NAME_INDEX(err_ovl_no_oper)
DIAG_NAME_INDEX(err_ovl_no_viable_conversion_in_cast)
DIAG_NAME_INDEX(err_ovl_no_viable_function_in_call)
DIAG_NAME_INDEX(err_ovl_no_viable_function_in_init)
DIAG_NAME_INDEX(err_ovl_no_viable_literal_operator)
DIAG_NAME_INDEX(err_ovl_no_viable_member_function_in_call)
DIAG_NAME_INDEX(err_ovl_no_viable_object_call)
DIAG_NAME_INDEX(err_ovl_no_viable_oper)
DIAG_NAME_INDEX(err_ovl_no_viable_subscript)
DIAG_NAME_INDEX(err_ovl_rewrite_equalequal_not_bool)
DIAG_NAME_INDEX(err_ovl_static_nonstatic_member)
DIAG_NAME_INDEX(err_ovl_unresolvable)
DIAG_NAME_INDEX(err_ownership_returns_index_mismatch)
DIAG_NAME_INDEX(err_ownership_type)
DIAG_NAME_INDEX(err_pack_expansion_length_conflict)
DIAG_NAME_INDEX(err_pack_expansion_length_conflict_multilevel)
DIAG_NAME_INDEX(err_pack_expansion_length_conflict_partial)
DIAG_NAME_INDEX(err_pack_expansion_member_init)
DIAG_NAME_INDEX(err_pack_expansion_without_parameter_packs)
DIAG_NAME_INDEX(err_param_default_argument)
DIAG_NAME_INDEX(err_param_default_argument_member_template_redecl)
DIAG_NAME_INDEX(err_param_default_argument_missing)
DIAG_NAME_INDEX(err_param_default_argument_missing_name)
DIAG_NAME_INDEX(err_param_default_argument_nonfunc)
DIAG_NAME_INDEX(err_param_default_argument_on_parameter_pack)
DIAG_NAME_INDEX(err_param_default_argument_redefinition)
DIAG_NAME_INDEX(err_param_default_argument_references_local)
DIAG_NAME_INDEX(err_param_default_argument_references_param)
DIAG_NAME_INDEX(err_param_default_argument_references_this)
DIAG_NAME_INDEX(err_param_default_argument_template_redecl)
DIAG_NAME_INDEX(err_param_redefinition)
DIAG_NAME_INDEX(err_param_with_void_type)
DIAG_NAME_INDEX(err_parameter_shadow_capture)
DIAG_NAME_INDEX(err_parameters_retval_cannot_have_fp16_type)
DIAG_NAME_INDEX(err_paren_sizeof_parameter_pack)
DIAG_NAME_INDEX(err_parens_pointer_member_function)
DIAG_NAME_INDEX(err_partial_spec_args_match_primary_template)
DIAG_NAME_INDEX(err_partial_spec_fully_specialized)
DIAG_NAME_INDEX(err_partial_spec_ordering_ambiguous)
DIAG_NAME_INDEX(err_partial_spec_redeclared)
DIAG_NAME_INDEX(err_partial_specialization_friend)
DIAG_NAME_INDEX(err_partition_import_outside_module)
DIAG_NAME_INDEX(err_pascal_string_too_long)
DIAG_NAME_INDEX(err_paste_at_end)
DIAG_NAME_INDEX(err_paste_at_start)
DIAG_NAME_INDEX(err_pch_diagopt_mismatch)
DIAG_NAME_INDEX(err_pch_different_branch)
DIAG_NAME_INDEX(err_pch_langopt_mismatch)
DIAG_NAME_INDEX(err_pch_langopt_value_mismatch)
DIAG_NAME_INDEX(err_pch_macro_def_conflict)
DIAG_NAME_INDEX(err_pch_macro_def_undef)
DIAG_NAME_INDEX(err_pch_modulecache_mismatch)
DIAG_NAME_INDEX(err_pch_pp_detailed_record)
DIAG_NAME_INDEX(err_pch_targetopt_feature_mismatch)
DIAG_NAME_INDEX(err_pch_targetopt_mismatch)
DIAG_NAME_INDEX(err_pch_undef)
DIAG_NAME_INDEX(err_pch_version_too_new)
DIAG_NAME_INDEX(err_pch_version_too_old)
DIAG_NAME_INDEX(err_pch_with_compiler_errors)
DIAG_NAME_INDEX(err_placeholder_constraints_not_satisfied)
DIAG_NAME_INDEX(err_placeholder_expected_auto_or_decltype_auto)
DIAG_NAME_INDEX(err_placeholder_in_source)
DIAG_NAME_INDEX(err_placement_new_non_placement_delete)
DIAG_NAME_INDEX(err_pointer_to_member_call_drops_quals)
DIAG_NAME_INDEX(err_pointer_to_member_oper_value_classify)
DIAG_NAME_INDEX(err_pointer_to_member_type)
DIAG_NAME_INDEX(err_postfix_after_unary_requires_parens)
DIAG_NAME_INDEX(err_pp_arc_cf_code_audited_syntax)
DIAG_NAME_INDEX(err_pp_assume_nonnull_syntax)
DIAG_NAME_INDEX(err_pp_bad_paste)
DIAG_NAME_INDEX(err_pp_colon_without_question)
DIAG_NAME_INDEX(err_pp_directive_required)
DIAG_NAME_INDEX(err_pp_division_by_zero)
DIAG_NAME_INDEX(err_pp_double_begin_of_arc_cf_code_audited)
DIAG_NAME_INDEX(err_pp_double_begin_of_assume_nonnull)
DIAG_NAME_INDEX(err_pp_double_begin_pragma_unsafe_buffer_usage)
DIAG_NAME_INDEX(err_pp_duplicate_name_in_arg_list)
DIAG_NAME_INDEX(err_pp_empty_filename)
DIAG_NAME_INDEX(err_pp_endif_without_if)
DIAG_NAME_INDEX(err_pp_eof_in_arc_cf_code_audited)
DIAG_NAME_INDEX(err_pp_eof_in_assume_nonnull)
DIAG_NAME_INDEX(err_pp_error_opening_file)
DIAG_NAME_INDEX(err_pp_expected_after)
DIAG_NAME_INDEX(err_pp_expected_comma_in_arg_list)
DIAG_NAME_INDEX(err_pp_expected_eol)
DIAG_NAME_INDEX(err_pp_expected_ident_in_arg_list)
DIAG_NAME_INDEX(err_pp_expected_module_name)
DIAG_NAME_INDEX(err_pp_expected_rparen)
DIAG_NAME_INDEX(err_pp_expected_value_in_expr)
DIAG_NAME_INDEX(err_pp_expects_filename)
DIAG_NAME_INDEX(err_pp_expr_bad_token_binop)
DIAG_NAME_INDEX(err_pp_expr_bad_token_lparen)
DIAG_NAME_INDEX(err_pp_expr_bad_token_start_expr)
DIAG_NAME_INDEX(err_pp_file_not_found)
DIAG_NAME_INDEX(err_pp_file_not_found_angled_include_not_fatal)
DIAG_NAME_INDEX(err_pp_file_not_found_typo_not_fatal)
DIAG_NAME_INDEX(err_pp_hash_error)
DIAG_NAME_INDEX(err_pp_identifier_arg_not_identifier)
DIAG_NAME_INDEX(err_pp_illegal_floating_literal)
DIAG_NAME_INDEX(err_pp_import_directive_ms)
DIAG_NAME_INDEX(err_pp_include_in_arc_cf_code_audited)
DIAG_NAME_INDEX(err_pp_include_in_assume_nonnull)
DIAG_NAME_INDEX(err_pp_include_too_deep)
DIAG_NAME_INDEX(err_pp_including_mainfile_in_preamble)
DIAG_NAME_INDEX(err_pp_invalid_directive)
DIAG_NAME_INDEX(err_pp_invalid_poison)
DIAG_NAME_INDEX(err_pp_invalid_tok_in_arg_list)
DIAG_NAME_INDEX(err_pp_invalid_udl)
DIAG_NAME_INDEX(err_pp_line_digit_sequence)
DIAG_NAME_INDEX(err_pp_line_invalid_filename)
DIAG_NAME_INDEX(err_pp_line_requires_integer)
DIAG_NAME_INDEX(err_pp_linemarker_invalid_filename)
DIAG_NAME_INDEX(err_pp_linemarker_invalid_flag)
DIAG_NAME_INDEX(err_pp_linemarker_invalid_pop)
DIAG_NAME_INDEX(err_pp_linemarker_requires_integer)
DIAG_NAME_INDEX(err_pp_macro_not_identifier)
DIAG_NAME_INDEX(err_pp_malformed_ident)
DIAG_NAME_INDEX(err_pp_missing_lparen_in_vaopt_use)
DIAG_NAME_INDEX(err_pp_missing_macro_name)
DIAG_NAME_INDEX(err_pp_missing_rparen_in_macro_def)
DIAG_NAME_INDEX(err_pp_module_begin_no_module_map)
DIAG_NAME_INDEX(err_pp_module_begin_no_submodule)
DIAG_NAME_INDEX(err_pp_module_begin_without_module_end)
DIAG_NAME_INDEX(err_pp_module_begin_wrong_module)
DIAG_NAME_INDEX(err_pp_module_build_missing_end)
DIAG_NAME_INDEX(err_pp_module_end_without_module_begin)
DIAG_NAME_INDEX(err_pp_nested_paren)
DIAG_NAME_INDEX(err_pp_operator_used_as_macro_name)
DIAG_NAME_INDEX(err_pp_pragma_hdrstop_not_seen)
DIAG_NAME_INDEX(err_pp_pragma_unsafe_buffer_usage_syntax)
DIAG_NAME_INDEX(err_pp_remainder_by_zero)
DIAG_NAME_INDEX(err_pp_stringize_not_parameter)
DIAG_NAME_INDEX(err_pp_through_header_not_found)
DIAG_NAME_INDEX(err_pp_through_header_not_seen)
DIAG_NAME_INDEX(err_pp_unclosed_pragma_unsafe_buffer_usage)
DIAG_NAME_INDEX(err_pp_unmatched_end_begin_pragma_unsafe_buffer_usage)
DIAG_NAME_INDEX(err_pp_unmatched_end_of_arc_cf_code_audited)
DIAG_NAME_INDEX(err_pp_unmatched_end_of_assume_nonnull)
DIAG_NAME_INDEX(err_pp_unterminated_conditional)
DIAG_NAME_INDEX(err_pp_used_poisoned_id)
DIAG_NAME_INDEX(err_pp_vaopt_nested_use)
DIAG_NAME_INDEX(err_pp_visibility_non_macro)
DIAG_NAME_INDEX(err_ppc_builtin_requires_abi)
DIAG_NAME_INDEX(err_ppc_invalid_test_data_class_type)
DIAG_NAME_INDEX(err_ppc_invalid_use_mma_type)
DIAG_NAME_INDEX(err_pragma_alloc_text_c_linkage)
DIAG_NAME_INDEX(err_pragma_alloc_text_not_function)
DIAG_NAME_INDEX(err_pragma_attr_attr_no_push)
DIAG_NAME_INDEX(err_pragma_attribute_duplicate_subject)
DIAG_NAME_INDEX(err_pragma_attribute_expected_attribute)
DIAG_NAME_INDEX(err_pragma_attribute_expected_attribute_name)
DIAG_NAME_INDEX(err_pragma_attribute_expected_attribute_syntax)
DIAG_NAME_INDEX(err_pragma_attribute_expected_period)
DIAG_NAME_INDEX(err_pragma_attribute_expected_push_pop_paren)
DIAG_NAME_INDEX(err_pragma_attribute_expected_subject_identifier)
DIAG_NAME_INDEX(err_pragma_attribute_expected_subject_sub_identifier)
DIAG_NAME_INDEX(err_pragma_attribute_extra_tokens_after_attribute)
DIAG_NAME_INDEX(err_pragma_attribute_invalid_argument)
DIAG_NAME_INDEX(err_pragma_attribute_invalid_matchers)
DIAG_NAME_INDEX(err_pragma_attribute_invalid_subject_set_specifier)
DIAG_NAME_INDEX(err_pragma_attribute_matcher_negated_subrule_contradicts_subrule)
DIAG_NAME_INDEX(err_pragma_attribute_matcher_subrule_contradicts_rule)
DIAG_NAME_INDEX(err_pragma_attribute_namespace_on_attribute)
DIAG_NAME_INDEX(err_pragma_attribute_no_pop_eof)
DIAG_NAME_INDEX(err_pragma_attribute_stack_mismatch)
DIAG_NAME_INDEX(err_pragma_attribute_unknown_subject_rule)
DIAG_NAME_INDEX(err_pragma_attribute_unknown_subject_sub_rule)
DIAG_NAME_INDEX(err_pragma_attribute_unsupported_attribute)
DIAG_NAME_INDEX(err_pragma_cannot_end_force_cuda_host_device)
DIAG_NAME_INDEX(err_pragma_clang_section_expected_equal)
DIAG_NAME_INDEX(err_pragma_comment_malformed)
DIAG_NAME_INDEX(err_pragma_comment_unknown_kind)
DIAG_NAME_INDEX(err_pragma_detect_mismatch_malformed)
DIAG_NAME_INDEX(err_pragma_expected_clang_section_name)
DIAG_NAME_INDEX(err_pragma_expected_file_scope)
DIAG_NAME_INDEX(err_pragma_expected_integer)
DIAG_NAME_INDEX(err_pragma_fc_except_requires_precise)
DIAG_NAME_INDEX(err_pragma_fc_noprecise_requires_noexcept)
DIAG_NAME_INDEX(err_pragma_fc_noprecise_requires_nofenv)
DIAG_NAME_INDEX(err_pragma_fc_pp_scope)
DIAG_NAME_INDEX(err_pragma_fenv_requires_precise)
DIAG_NAME_INDEX(err_pragma_file_or_compound_scope)
DIAG_NAME_INDEX(err_pragma_float_control_malformed)
DIAG_NAME_INDEX(err_pragma_fp_invalid_argument)
DIAG_NAME_INDEX(err_pragma_fp_invalid_option)
DIAG_NAME_INDEX(err_pragma_invalid_keyword)
DIAG_NAME_INDEX(err_pragma_loop_compatibility)
DIAG_NAME_INDEX(err_pragma_loop_invalid_argument_type)
DIAG_NAME_INDEX(err_pragma_loop_invalid_argument_value)
DIAG_NAME_INDEX(err_pragma_loop_invalid_option)
DIAG_NAME_INDEX(err_pragma_loop_invalid_vectorize_option)
DIAG_NAME_INDEX(err_pragma_loop_missing_argument)
DIAG_NAME_INDEX(err_pragma_loop_precedes_nonloop)
DIAG_NAME_INDEX(err_pragma_message)
DIAG_NAME_INDEX(err_pragma_message_malformed)
DIAG_NAME_INDEX(err_pragma_misplaced_in_decl)
DIAG_NAME_INDEX(err_pragma_missing_argument)
DIAG_NAME_INDEX(err_pragma_optimize_extra_argument)
DIAG_NAME_INDEX(err_pragma_optimize_invalid_argument)
DIAG_NAME_INDEX(err_pragma_options_align_mac68k_target_unsupported)
DIAG_NAME_INDEX(err_pragma_pack_identifer_not_supported)
DIAG_NAME_INDEX(err_pragma_pack_invalid_alignment)
DIAG_NAME_INDEX(err_pragma_pipeline_invalid_keyword)
DIAG_NAME_INDEX(err_pragma_pointers_to_members_unknown_kind)
DIAG_NAME_INDEX(err_pragma_pop_visibility_mismatch)
DIAG_NAME_INDEX(err_pragma_push_pop_macro_malformed)
DIAG_NAME_INDEX(err_pragma_push_visibility_mismatch)
DIAG_NAME_INDEX(err_pragma_section_invalid_for_target)
DIAG_NAME_INDEX(err_preserve_enum_value_invalid)
DIAG_NAME_INDEX(err_preserve_enum_value_not_const)
DIAG_NAME_INDEX(err_preserve_field_info_not_const)
DIAG_NAME_INDEX(err_preserve_field_info_not_field)
DIAG_NAME_INDEX(err_preserve_type_info_invalid)
DIAG_NAME_INDEX(err_preserve_type_info_not_const)
DIAG_NAME_INDEX(err_private_ivar_access)
DIAG_NAME_INDEX(err_private_module_fragment_expected_semi)
DIAG_NAME_INDEX(err_private_module_fragment_not_module)
DIAG_NAME_INDEX(err_private_module_fragment_not_module_interface)
DIAG_NAME_INDEX(err_private_module_fragment_redefined)
DIAG_NAME_INDEX(err_probability_not_constant_float)
DIAG_NAME_INDEX(err_probability_out_of_range)
DIAG_NAME_INDEX(err_property_accessor_type)
DIAG_NAME_INDEX(err_property_found_suggest)
DIAG_NAME_INDEX(err_property_function_in_objc_container)
DIAG_NAME_INDEX(err_property_implemented)
DIAG_NAME_INDEX(err_property_is_variably_modified)
DIAG_NAME_INDEX(err_property_ivar_type)
DIAG_NAME_INDEX(err_property_method_unavailable)
DIAG_NAME_INDEX(err_property_not_as_forward_class)
DIAG_NAME_INDEX(err_property_not_found)
DIAG_NAME_INDEX(err_property_not_found_forward_class)
DIAG_NAME_INDEX(err_property_not_found_suggest)
DIAG_NAME_INDEX(err_property_setter_ambiguous_use)
DIAG_NAME_INDEX(err_property_type)
DIAG_NAME_INDEX(err_protected_ivar_access)
DIAG_NAME_INDEX(err_protocol_has_circular_dependency)
DIAG_NAME_INDEX(err_protocol_property_mismatch)
DIAG_NAME_INDEX(err_pseudo_dtor_base_not_scalar)
DIAG_NAME_INDEX(err_pseudo_dtor_call_with_args)
DIAG_NAME_INDEX(err_pseudo_dtor_destructor_non_type)
DIAG_NAME_INDEX(err_pseudo_dtor_type_mismatch)
DIAG_NAME_INDEX(err_pure_friend)
DIAG_NAME_INDEX(err_qualified_catch_declarator)
DIAG_NAME_INDEX(err_qualified_friend_def)
DIAG_NAME_INDEX(err_qualified_friend_no_match)
DIAG_NAME_INDEX(err_qualified_function_typeid)
DIAG_NAME_INDEX(err_qualified_member_nonclass)
DIAG_NAME_INDEX(err_qualified_member_of_unrelated)
DIAG_NAME_INDEX(err_qualified_objc_access)
DIAG_NAME_INDEX(err_qualified_objc_catch_parm)
DIAG_NAME_INDEX(err_qualified_param_declarator)
DIAG_NAME_INDEX(err_qualified_typedef_declarator)
DIAG_NAME_INDEX(err_range_on_array_parameter)
DIAG_NAME_INDEX(err_raw_delim_too_long)
DIAG_NAME_INDEX(err_readonly_message_assignment)
DIAG_NAME_INDEX(err_realimag_invalid_type)
DIAG_NAME_INDEX(err_record_with_pointers_kernel_param)
DIAG_NAME_INDEX(err_recursive_default_argument)
DIAG_NAME_INDEX(err_recursive_superclass)
DIAG_NAME_INDEX(err_redeclaration_different_type)
DIAG_NAME_INDEX(err_redeclaration_non_exported)
DIAG_NAME_INDEX(err_redefinition)
DIAG_NAME_INDEX(err_redefinition_different_concept)
DIAG_NAME_INDEX(err_redefinition_different_kind)
DIAG_NAME_INDEX(err_redefinition_different_namespace_alias)
DIAG_NAME_INDEX(err_redefinition_different_type)
DIAG_NAME_INDEX(err_redefinition_different_typedef)
DIAG_NAME_INDEX(err_redefinition_extern_inline)
DIAG_NAME_INDEX(err_redefinition_of_enumerator)
DIAG_NAME_INDEX(err_redefinition_of_label)
DIAG_NAME_INDEX(err_redefinition_variably_modified_typedef)
DIAG_NAME_INDEX(err_ref_array_type)
DIAG_NAME_INDEX(err_ref_bad_target)
DIAG_NAME_INDEX(err_ref_bad_target_global_initializer)
DIAG_NAME_INDEX(err_ref_flexarray_type)
DIAG_NAME_INDEX(err_ref_init_ambiguous)
DIAG_NAME_INDEX(err_ref_non_value)
DIAG_NAME_INDEX(err_ref_qualifier_comparison_operator)
DIAG_NAME_INDEX(err_ref_qualifier_constructor)
DIAG_NAME_INDEX(err_ref_qualifier_destructor)
DIAG_NAME_INDEX(err_ref_qualifier_overload)
DIAG_NAME_INDEX(err_ref_vm_type)
DIAG_NAME_INDEX(err_refactor_code_outside_of_function)
DIAG_NAME_INDEX(err_refactor_extract_prohibited_expression)
DIAG_NAME_INDEX(err_refactor_extract_simple_expression)
DIAG_NAME_INDEX(err_refactor_no_selection)
DIAG_NAME_INDEX(err_refactor_selection_invalid_ast)
DIAG_NAME_INDEX(err_refactor_selection_no_symbol)
DIAG_NAME_INDEX(err_reference_bind_drops_quals)
DIAG_NAME_INDEX(err_reference_bind_failed)
DIAG_NAME_INDEX(err_reference_bind_init_list)
DIAG_NAME_INDEX(err_reference_bind_temporary_addrspace)
DIAG_NAME_INDEX(err_reference_bind_to_bitfield)
DIAG_NAME_INDEX(err_reference_bind_to_matrix_element)
DIAG_NAME_INDEX(err_reference_bind_to_vector_element)
DIAG_NAME_INDEX(err_reference_capture_with_reference_default)
DIAG_NAME_INDEX(err_reference_has_multiple_inits)
DIAG_NAME_INDEX(err_reference_pipe_type)
DIAG_NAME_INDEX(err_reference_to_function_with_unsatisfied_constraints)
DIAG_NAME_INDEX(err_reference_to_local_in_enclosing_context)
DIAG_NAME_INDEX(err_reference_to_void)
DIAG_NAME_INDEX(err_reference_var_requires_init)
DIAG_NAME_INDEX(err_reference_without_init)
DIAG_NAME_INDEX(err_regparm_mismatch)
DIAG_NAME_INDEX(err_relocatable_without_isysroot)
DIAG_NAME_INDEX(err_repeat_attribute)
DIAG_NAME_INDEX(err_require_constant_init_failed)
DIAG_NAME_INDEX(err_requires_clause_inside_parens)
DIAG_NAME_INDEX(err_requires_clause_must_appear_after_trailing_return)
DIAG_NAME_INDEX(err_requires_clause_on_declarator_not_declaring_a_function)
DIAG_NAME_INDEX(err_requires_expr_expected_type_constraint)
DIAG_NAME_INDEX(err_requires_expr_in_simple_requirement)
DIAG_NAME_INDEX(err_requires_expr_local_parameter_default_argument)
DIAG_NAME_INDEX(err_requires_expr_missing_arrow)
DIAG_NAME_INDEX(err_requires_expr_parameter_list_ellipsis)
DIAG_NAME_INDEX(err_requires_expr_parameter_referenced_in_evaluated_context)
DIAG_NAME_INDEX(err_requires_expr_simple_requirement_noexcept)
DIAG_NAME_INDEX(err_restricted_superclass_mismatch)
DIAG_NAME_INDEX(err_ret_local_block)
DIAG_NAME_INDEX(err_rethrow_used_outside_catch)
DIAG_NAME_INDEX(err_return_block_has_expr)
DIAG_NAME_INDEX(err_return_in_captured_stmt)
DIAG_NAME_INDEX(err_return_in_constructor_handler)
DIAG_NAME_INDEX(err_return_in_coroutine)
DIAG_NAME_INDEX(err_return_init_list)
DIAG_NAME_INDEX(err_return_value_with_address_space)
DIAG_NAME_INDEX(err_right_angle_bracket_equal_needs_space)
DIAG_NAME_INDEX(err_riscv_builtin_invalid_lmul)
DIAG_NAME_INDEX(err_riscv_builtin_requires_extension)
DIAG_NAME_INDEX(err_riscv_type_requires_extension)
DIAG_NAME_INDEX(err_root_class_cannot_use_super)
DIAG_NAME_INDEX(err_roptr_cannot_build_shared)
DIAG_NAME_INDEX(err_roptr_requires_data_sections)
DIAG_NAME_INDEX(err_rotation_argument_to_cadd)
DIAG_NAME_INDEX(err_rotation_argument_to_cmla)
DIAG_NAME_INDEX(err_rref_in_exception_spec)
DIAG_NAME_INDEX(err_sampler_argument_required)
DIAG_NAME_INDEX(err_sampler_initializer_not_integer)
DIAG_NAME_INDEX(err_scoped_enum_missing_identifier)
DIAG_NAME_INDEX(err_second_argument_to_cwsc_not_pointer)
DIAG_NAME_INDEX(err_second_parameter_to_va_arg_abstract)
DIAG_NAME_INDEX(err_second_parameter_to_va_arg_incomplete)
DIAG_NAME_INDEX(err_section_conflict)
DIAG_NAME_INDEX(err_seh___except_block)
DIAG_NAME_INDEX(err_seh___except_filter)
DIAG_NAME_INDEX(err_seh___finally_block)
DIAG_NAME_INDEX(err_seh_expected_handler)
DIAG_NAME_INDEX(err_seh_in_a_coroutine_with_cxx_exceptions)
DIAG_NAME_INDEX(err_seh_try_outside_functions)
DIAG_NAME_INDEX(err_seh_try_unsupported)
DIAG_NAME_INDEX(err_selected_explicit_constructor)
DIAG_NAME_INDEX(err_selector_element_const_type)
DIAG_NAME_INDEX(err_selector_element_not_lvalue)
DIAG_NAME_INDEX(err_selector_element_type)
DIAG_NAME_INDEX(err_setter_type_void)
DIAG_NAME_INDEX(err_setting_eval_method_used_in_unsafe_context)
DIAG_NAME_INDEX(err_shared_var_init)
DIAG_NAME_INDEX(err_shift_rhs_only_vector)
DIAG_NAME_INDEX(err_shufflevector_argument_too_large)
DIAG_NAME_INDEX(err_shufflevector_nonconstant_argument)
DIAG_NAME_INDEX(err_single_decl_assign_in_for_range)
DIAG_NAME_INDEX(err_size_t_literal_too_large)
DIAG_NAME_INDEX(err_sizeless_in_exception_spec)
DIAG_NAME_INDEX(err_sizeless_nonlocal)
DIAG_NAME_INDEX(err_sizeof_alignof_function_type)
DIAG_NAME_INDEX(err_sizeof_alignof_incomplete_or_sizeless_type)
DIAG_NAME_INDEX(err_sizeof_alignof_typeof_bitfield)
DIAG_NAME_INDEX(err_sizeof_nonfragile_interface)
DIAG_NAME_INDEX(err_sizeof_pack_no_pack_name)
DIAG_NAME_INDEX(err_sizeof_pack_no_pack_name_suggest)
DIAG_NAME_INDEX(err_sizeof_parameter_pack)
DIAG_NAME_INDEX(err_sls_hardening_arm_not_supported)
DIAG_NAME_INDEX(err_spaceship_argument_narrowing)
DIAG_NAME_INDEX(err_spec_member_not_instantiated)
DIAG_NAME_INDEX(err_specialization_after_instantiation)
DIAG_NAME_INDEX(err_specialization_not_primary_template)
DIAG_NAME_INDEX(err_specialize_member_of_template)
DIAG_NAME_INDEX(err_stack_tagging_requires_hardware_feature)
DIAG_NAME_INDEX(err_standalone_class_nested_name_specifier)
DIAG_NAME_INDEX(err_static_assert_expression_is_not_constant)
DIAG_NAME_INDEX(err_static_assert_failed)
DIAG_NAME_INDEX(err_static_assert_invalid_mem_fn_ret_ty)
DIAG_NAME_INDEX(err_static_assert_invalid_message)
DIAG_NAME_INDEX(err_static_assert_message_constexpr)
DIAG_NAME_INDEX(err_static_assert_missing_member_function)
DIAG_NAME_INDEX(err_static_assert_requirement_failed)
DIAG_NAME_INDEX(err_static_block_func)
DIAG_NAME_INDEX(err_static_data_member_not_allowed_in_anon_struct)
DIAG_NAME_INDEX(err_static_data_member_not_allowed_in_local_class)
DIAG_NAME_INDEX(err_static_data_member_reinitialization)
DIAG_NAME_INDEX(err_static_downcast_via_virtual)
DIAG_NAME_INDEX(err_static_function_scope)
DIAG_NAME_INDEX(err_static_illegal_in_new)
DIAG_NAME_INDEX(err_static_kernel)
DIAG_NAME_INDEX(err_static_lambda)
DIAG_NAME_INDEX(err_static_lambda_captures)
DIAG_NAME_INDEX(err_static_main)
DIAG_NAME_INDEX(err_static_mutable_lambda)
DIAG_NAME_INDEX(err_static_non_static)
DIAG_NAME_INDEX(err_static_not_bitfield)
DIAG_NAME_INDEX(err_static_out_of_line)
DIAG_NAME_INDEX(err_static_overrides_virtual)
DIAG_NAME_INDEX(err_statically_allocated_object)
DIAG_NAME_INDEX(err_std_compare_type_not_supported)
DIAG_NAME_INDEX(err_std_source_location_impl_malformed)
DIAG_NAME_INDEX(err_std_source_location_impl_not_found)
DIAG_NAME_INDEX(err_std_type_trait_not_class_template)
DIAG_NAME_INDEX(err_stmt_expr_in_default_arg)
DIAG_NAME_INDEX(err_stmtexpr_file_scope)
DIAG_NAME_INDEX(err_storage_class_for_static_member)
DIAG_NAME_INDEX(err_storage_spec_on_catch_parm)
DIAG_NAME_INDEX(err_storageclass_invalid_for_member)
DIAG_NAME_INDEX(err_store_value_to_reg)
DIAG_NAME_INDEX(err_string_concat_mixed_suffix)
DIAG_NAME_INDEX(err_strong_property)
DIAG_NAME_INDEX(err_subscript_function_type)
DIAG_NAME_INDEX(err_subscript_incomplete_or_sizeless_type)
DIAG_NAME_INDEX(err_subscript_nonfragile_interface)
DIAG_NAME_INDEX(err_subscript_svbool_t)
DIAG_NAME_INDEX(err_super_in_lambda_unsupported)
DIAG_NAME_INDEX(err_super_in_using_declaration)
DIAG_NAME_INDEX(err_sve_vector_in_non_sve_target)
DIAG_NAME_INDEX(err_swift_abi_parameter_wrong_type)
DIAG_NAME_INDEX(err_swift_async_bad_block_type)
DIAG_NAME_INDEX(err_swift_async_error_no_error_parameter)
DIAG_NAME_INDEX(err_swift_async_error_non_integral)
DIAG_NAME_INDEX(err_swift_async_error_without_swift_async)
DIAG_NAME_INDEX(err_swift_async_no_access)
DIAG_NAME_INDEX(err_swift_error_result_not_after_swift_context)
DIAG_NAME_INDEX(err_swift_indirect_result_not_first)
DIAG_NAME_INDEX(err_swift_param_attr_not_swiftcall)
DIAG_NAME_INDEX(err_switch_explicit_conversion)
DIAG_NAME_INDEX(err_switch_incomplete_class_type)
DIAG_NAME_INDEX(err_switch_into_protected_scope)
DIAG_NAME_INDEX(err_switch_multiple_conversions)
DIAG_NAME_INDEX(err_sycl_special_type_num_init_method)
DIAG_NAME_INDEX(err_synthesize_category_decl)
DIAG_NAME_INDEX(err_synthesize_on_class_property)
DIAG_NAME_INDEX(err_synthesize_variable_sized_ivar)
DIAG_NAME_INDEX(err_synthesized_property_name)
DIAG_NAME_INDEX(err_synthesizing_arc_weak_property_disabled)
DIAG_NAME_INDEX(err_synthesizing_arc_weak_property_no_runtime)
DIAG_NAME_INDEX(err_systemz_invalid_tabort_code)
DIAG_NAME_INDEX(err_tag_definition_of_typedef)
DIAG_NAME_INDEX(err_tag_index_out_of_range)
DIAG_NAME_INDEX(err_tag_reference_conflict)
DIAG_NAME_INDEX(err_tag_reference_non_tag)
DIAG_NAME_INDEX(err_tagless_friend_type_template)
DIAG_NAME_INDEX(err_target_clone_doesnt_match)
DIAG_NAME_INDEX(err_target_clone_must_have_default)
DIAG_NAME_INDEX(err_target_unknown_abi)
DIAG_NAME_INDEX(err_target_unknown_cpu)
DIAG_NAME_INDEX(err_target_unknown_fpmath)
DIAG_NAME_INDEX(err_target_unknown_triple)
DIAG_NAME_INDEX(err_target_unsupported_abi)
DIAG_NAME_INDEX(err_target_unsupported_abi_for_triple)
DIAG_NAME_INDEX(err_target_unsupported_arch)
DIAG_NAME_INDEX(err_target_unsupported_cpu_for_micromips)
DIAG_NAME_INDEX(err_target_unsupported_execute_only)
DIAG_NAME_INDEX(err_target_unsupported_fpmath)
DIAG_NAME_INDEX(err_target_unsupported_mcmse)
DIAG_NAME_INDEX(err_target_unsupported_tp_hard)
DIAG_NAME_INDEX(err_target_unsupported_type)
DIAG_NAME_INDEX(err_target_unsupported_unaligned)
DIAG_NAME_INDEX(err_tcb_conflicting_attributes)
DIAG_NAME_INDEX(err_temp_copy_ambiguous)
DIAG_NAME_INDEX(err_temp_copy_deleted)
DIAG_NAME_INDEX(err_temp_copy_incomplete)
DIAG_NAME_INDEX(err_temp_copy_no_viable)
DIAG_NAME_INDEX(err_template_arg_address_of_non_pointer)
DIAG_NAME_INDEX(err_template_arg_deduced_incomplete_pack)
DIAG_NAME_INDEX(err_template_arg_field)
DIAG_NAME_INDEX(err_template_arg_invalid)
DIAG_NAME_INDEX(err_template_arg_list_constraints_not_satisfied)
DIAG_NAME_INDEX(err_template_arg_list_different_arity)
DIAG_NAME_INDEX(err_template_arg_member_ptr_base_derived_not_supported)
DIAG_NAME_INDEX(err_template_arg_method)
DIAG_NAME_INDEX(err_template_arg_must_be_expr)
DIAG_NAME_INDEX(err_template_arg_must_be_template)
DIAG_NAME_INDEX(err_template_arg_must_be_type)
DIAG_NAME_INDEX(err_template_arg_must_be_type_suggest)
DIAG_NAME_INDEX(err_template_arg_no_ref_bind)
DIAG_NAME_INDEX(err_template_arg_nontype_ambig)
DIAG_NAME_INDEX(err_template_arg_not_address_constant)
DIAG_NAME_INDEX(err_template_arg_not_address_of)
DIAG_NAME_INDEX(err_template_arg_not_convertible)
DIAG_NAME_INDEX(err_template_arg_not_decl_ref)
DIAG_NAME_INDEX(err_template_arg_not_ice)
DIAG_NAME_INDEX(err_template_arg_not_integral_or_enumeral)
DIAG_NAME_INDEX(err_template_arg_not_object_or_func)
DIAG_NAME_INDEX(err_template_arg_not_pointer_to_member_form)
DIAG_NAME_INDEX(err_template_arg_not_valid_template)
DIAG_NAME_INDEX(err_template_arg_object_no_linkage)
DIAG_NAME_INDEX(err_template_arg_overload_type)
DIAG_NAME_INDEX(err_template_arg_ref_bind_ignores_quals)
DIAG_NAME_INDEX(err_template_arg_reference_var)
DIAG_NAME_INDEX(err_template_arg_template_params_mismatch)
DIAG_NAME_INDEX(err_template_arg_thread_local)
DIAG_NAME_INDEX(err_template_arg_untyped_null_constant)
DIAG_NAME_INDEX(err_template_arg_wrongtype_null_constant)
DIAG_NAME_INDEX(err_template_defn_explicit_instantiation)
DIAG_NAME_INDEX(err_template_different_requires_clause)
DIAG_NAME_INDEX(err_template_different_type_constraint)
DIAG_NAME_INDEX(err_template_expansion_into_fixed_list)
DIAG_NAME_INDEX(err_template_id_not_a_type)
DIAG_NAME_INDEX(err_template_inside_local_class)
DIAG_NAME_INDEX(err_template_instantiate_undefined)
DIAG_NAME_INDEX(err_template_instantiate_within_definition)
DIAG_NAME_INDEX(err_template_kernel)
DIAG_NAME_INDEX(err_template_kw_missing)
DIAG_NAME_INDEX(err_template_kw_refers_to_dependent_non_template)
DIAG_NAME_INDEX(err_template_kw_refers_to_non_template)
DIAG_NAME_INDEX(err_template_kw_refers_to_type_template)
DIAG_NAME_INDEX(err_template_linkage)
DIAG_NAME_INDEX(err_template_member)
DIAG_NAME_INDEX(err_template_member_noparams)
DIAG_NAME_INDEX(err_template_missing_args)
DIAG_NAME_INDEX(err_template_nontype_parm_bad_structural_type)
DIAG_NAME_INDEX(err_template_nontype_parm_bad_type)
DIAG_NAME_INDEX(err_template_nontype_parm_different_type)
DIAG_NAME_INDEX(err_template_nontype_parm_incomplete)
DIAG_NAME_INDEX(err_template_nontype_parm_not_literal)
DIAG_NAME_INDEX(err_template_nontype_parm_not_structural)
DIAG_NAME_INDEX(err_template_nontype_parm_rvalue_ref)
DIAG_NAME_INDEX(err_template_outside_namespace_or_class_scope)
DIAG_NAME_INDEX(err_template_param_default_arg_inconsistent_redefinition)
DIAG_NAME_INDEX(err_template_param_default_arg_missing)
DIAG_NAME_INDEX(err_template_param_default_arg_redefinition)
DIAG_NAME_INDEX(err_template_param_different_kind)
DIAG_NAME_INDEX(err_template_param_list_different_arity)
DIAG_NAME_INDEX(err_template_param_list_matches_nontemplate)
DIAG_NAME_INDEX(err_template_param_pack_default_arg)
DIAG_NAME_INDEX(err_template_param_pack_must_be_last_template_parameter)
DIAG_NAME_INDEX(err_template_param_shadow)
DIAG_NAME_INDEX(err_template_parameter_default_friend_template)
DIAG_NAME_INDEX(err_template_parameter_default_template_member)
DIAG_NAME_INDEX(err_template_parameter_pack_non_pack)
DIAG_NAME_INDEX(err_template_qualified_declarator_no_match)
DIAG_NAME_INDEX(err_template_recursion_depth_exceeded)
DIAG_NAME_INDEX(err_template_spec_decl_friend)
DIAG_NAME_INDEX(err_template_spec_decl_function_scope)
DIAG_NAME_INDEX(err_template_spec_default_arg)
DIAG_NAME_INDEX(err_template_spec_extra_headers)
DIAG_NAME_INDEX(err_template_spec_friend)
DIAG_NAME_INDEX(err_template_spec_needs_header)
DIAG_NAME_INDEX(err_template_spec_needs_template_parameters)
DIAG_NAME_INDEX(err_template_spec_redecl_global_scope)
DIAG_NAME_INDEX(err_template_spec_redecl_out_of_scope)
DIAG_NAME_INDEX(err_template_spec_syntax_non_template)
DIAG_NAME_INDEX(err_template_spec_unknown_kind)
DIAG_NAME_INDEX(err_template_tag_noparams)
DIAG_NAME_INDEX(err_template_template_parameter_not_at_least_as_constrained)
DIAG_NAME_INDEX(err_template_template_parm_no_parms)
DIAG_NAME_INDEX(err_template_typedef)
DIAG_NAME_INDEX(err_template_unnamed_class)
DIAG_NAME_INDEX(err_template_variable_noparams)
DIAG_NAME_INDEX(err_templated_invalid_declaration)
DIAG_NAME_INDEX(err_templated_using_directive_declaration)
DIAG_NAME_INDEX(err_tentative_def_incomplete_type)
DIAG_NAME_INDEX(err_test_module_file_extension_format)
DIAG_NAME_INDEX(err_test_module_file_extension_version)
DIAG_NAME_INDEX(err_this_capture)
DIAG_NAME_INDEX(err_this_captured_by_reference)
DIAG_NAME_INDEX(err_this_static_member_func)
DIAG_NAME_INDEX(err_thread_dynamic_init)
DIAG_NAME_INDEX(err_thread_non_global)
DIAG_NAME_INDEX(err_thread_non_thread)
DIAG_NAME_INDEX(err_thread_nontrivial_dtor)
DIAG_NAME_INDEX(err_thread_thread_different_kind)
DIAG_NAME_INDEX(err_thread_unsupported)
DIAG_NAME_INDEX(err_three_way_vector_comparison)
DIAG_NAME_INDEX(err_throw_abstract_type)
DIAG_NAME_INDEX(err_throw_incomplete)
DIAG_NAME_INDEX(err_throw_incomplete_ptr)
DIAG_NAME_INDEX(err_throw_sizeless)
DIAG_NAME_INDEX(err_tls_var_aligned_over_maximum)
DIAG_NAME_INDEX(err_too_few_args_in_macro_invoc)
DIAG_NAME_INDEX(err_too_large_for_fixed_point)
DIAG_NAME_INDEX(err_too_many_args_in_macro_invoc)
DIAG_NAME_INDEX(err_toomany_element_decls)
DIAG_NAME_INDEX(err_trailing_requires_clause_on_deduction_guide)
DIAG_NAME_INDEX(err_trailing_return_in_parens)
DIAG_NAME_INDEX(err_trailing_return_without_auto)
DIAG_NAME_INDEX(err_two_right_angle_brackets_need_space)
DIAG_NAME_INDEX(err_type_attribute_wrong_type)
DIAG_NAME_INDEX(err_type_available_only_in_default_eval_method)
DIAG_NAME_INDEX(err_type_constraint_missing_arguments)
DIAG_NAME_INDEX(err_type_constraint_non_type_concept)
DIAG_NAME_INDEX(err_type_defined_in_alias_template)
DIAG_NAME_INDEX(err_type_defined_in_condition)
DIAG_NAME_INDEX(err_type_defined_in_enum)
DIAG_NAME_INDEX(err_type_defined_in_for_range)
DIAG_NAME_INDEX(err_type_defined_in_param_type)
DIAG_NAME_INDEX(err_type_defined_in_result_type)
DIAG_NAME_INDEX(err_type_defined_in_type_specifier)
DIAG_NAME_INDEX(err_type_mismatch_continuation_class)
DIAG_NAME_INDEX(err_type_pack_element_out_of_bounds)
DIAG_NAME_INDEX(err_type_safety_unknown_flag)
DIAG_NAME_INDEX(err_type_tag_for_datatype_not_ice)
DIAG_NAME_INDEX(err_type_tag_for_datatype_too_large)
DIAG_NAME_INDEX(err_type_trait_arity)
DIAG_NAME_INDEX(err_type_unsupported)
DIAG_NAME_INDEX(err_typecheck_address_of)
DIAG_NAME_INDEX(err_typecheck_addrof_dtor)
DIAG_NAME_INDEX(err_typecheck_addrof_temporary)
DIAG_NAME_INDEX(err_typecheck_ambiguous_condition)
DIAG_NAME_INDEX(err_typecheck_arc_assign_externally_retained)
DIAG_NAME_INDEX(err_typecheck_arc_assign_self)
DIAG_NAME_INDEX(err_typecheck_arc_assign_self_class_method)
DIAG_NAME_INDEX(err_typecheck_arithmetic_incomplete_or_sizeless_type)
DIAG_NAME_INDEX(err_typecheck_arr_assign_enumeration)
DIAG_NAME_INDEX(err_typecheck_array_not_modifiable_lvalue)
DIAG_NAME_INDEX(err_typecheck_assign_const)
DIAG_NAME_INDEX(err_typecheck_bool_condition)
DIAG_NAME_INDEX(err_typecheck_call_different_arg_types)
DIAG_NAME_INDEX(err_typecheck_call_invalid_ordered_compare)
DIAG_NAME_INDEX(err_typecheck_call_invalid_unary_fp)
DIAG_NAME_INDEX(err_typecheck_call_not_function)
DIAG_NAME_INDEX(err_typecheck_call_requires_real_fp)
DIAG_NAME_INDEX(err_typecheck_call_too_few_args)
DIAG_NAME_INDEX(err_typecheck_call_too_few_args_at_least)
DIAG_NAME_INDEX(err_typecheck_call_too_few_args_at_least_one)
DIAG_NAME_INDEX(err_typecheck_call_too_few_args_at_least_suggest)
DIAG_NAME_INDEX(err_typecheck_call_too_few_args_one)
DIAG_NAME_INDEX(err_typecheck_call_too_few_args_suggest)
DIAG_NAME_INDEX(err_typecheck_call_too_many_args)
DIAG_NAME_INDEX(err_typecheck_call_too_many_args_at_most)
DIAG_NAME_INDEX(err_typecheck_call_too_many_args_at_most_one)
DIAG_NAME_INDEX(err_typecheck_call_too_many_args_at_most_suggest)
DIAG_NAME_INDEX(err_typecheck_call_too_many_args_one)
DIAG_NAME_INDEX(err_typecheck_call_too_many_args_suggest)
DIAG_NAME_INDEX(err_typecheck_cast_to_incomplete)
DIAG_NAME_INDEX(err_typecheck_cast_to_union_no_type)
DIAG_NAME_INDEX(err_typecheck_choose_expr_requires_constant)
DIAG_NAME_INDEX(err_typecheck_comparison_of_distinct_blocks)
DIAG_NAME_INDEX(err_typecheck_comparison_of_distinct_pointers)
DIAG_NAME_INDEX(err_typecheck_comparison_of_fptr_to_void)
DIAG_NAME_INDEX(err_typecheck_comparison_of_pointer_integer)
DIAG_NAME_INDEX(err_typecheck_cond_expect_int_float)
DIAG_NAME_INDEX(err_typecheck_cond_expect_nonfloat)
DIAG_NAME_INDEX(err_typecheck_cond_expect_scalar)
DIAG_NAME_INDEX(err_typecheck_cond_incompatible_operands)
DIAG_NAME_INDEX(err_typecheck_cond_incompatible_operands_null)
DIAG_NAME_INDEX(err_typecheck_convert_discards_qualifiers)
DIAG_NAME_INDEX(err_typecheck_convert_incompatible)
DIAG_NAME_INDEX(err_typecheck_convert_incompatible_block_pointer)
DIAG_NAME_INDEX(err_typecheck_convert_incompatible_function_pointer)
DIAG_NAME_INDEX(err_typecheck_convert_incompatible_pointer)
DIAG_NAME_INDEX(err_typecheck_convert_incompatible_pointer_sign)
DIAG_NAME_INDEX(err_typecheck_convert_int_pointer)
DIAG_NAME_INDEX(err_typecheck_convert_pointer_int)
DIAG_NAME_INDEX(err_typecheck_convert_pointer_void_func)
DIAG_NAME_INDEX(err_typecheck_converted_constant_expression)
DIAG_NAME_INDEX(err_typecheck_converted_constant_expression_disallowed)
DIAG_NAME_INDEX(err_typecheck_converted_constant_expression_indirect)
DIAG_NAME_INDEX(err_typecheck_decl_incomplete_type)
DIAG_NAME_INDEX(err_typecheck_deleted_function)
DIAG_NAME_INDEX(err_typecheck_duplicate_vector_components_not_mlvalue)
DIAG_NAME_INDEX(err_typecheck_expect_flt_or_vector)
DIAG_NAME_INDEX(err_typecheck_expect_int)
DIAG_NAME_INDEX(err_typecheck_expect_scalar_operand)
DIAG_NAME_INDEX(err_typecheck_expression_not_modifiable_lvalue)
DIAG_NAME_INDEX(err_typecheck_field_variable_size)
DIAG_NAME_INDEX(err_typecheck_illegal_increment_decrement)
DIAG_NAME_INDEX(err_typecheck_incompatible_address_space)
DIAG_NAME_INDEX(err_typecheck_incompatible_nested_address_space)
DIAG_NAME_INDEX(err_typecheck_incompatible_ownership)
DIAG_NAME_INDEX(err_typecheck_incomplete_array_needs_initializer)
DIAG_NAME_INDEX(err_typecheck_incomplete_tag)
DIAG_NAME_INDEX(err_typecheck_incomplete_type_not_modifiable_lvalue)
DIAG_NAME_INDEX(err_typecheck_indirection_requires_pointer)
DIAG_NAME_INDEX(err_typecheck_indirection_through_void_pointer_cpp)
DIAG_NAME_INDEX(err_typecheck_invalid_lvalue_addrof)
DIAG_NAME_INDEX(err_typecheck_invalid_lvalue_addrof_addrof_function)
DIAG_NAME_INDEX(err_typecheck_invalid_operands)
DIAG_NAME_INDEX(err_typecheck_invalid_restrict_invalid_pointee)
DIAG_NAME_INDEX(err_typecheck_invalid_restrict_not_pointer)
DIAG_NAME_INDEX(err_typecheck_invalid_restrict_not_pointer_noarg)
DIAG_NAME_INDEX(err_typecheck_ivar_variable_size)
DIAG_NAME_INDEX(err_typecheck_logical_vector_expr_gnu_cpp_restrict)
DIAG_NAME_INDEX(err_typecheck_lvalue_casts_not_supported)
DIAG_NAME_INDEX(err_typecheck_member_reference_arrow)
DIAG_NAME_INDEX(err_typecheck_member_reference_ivar)
DIAG_NAME_INDEX(err_typecheck_member_reference_ivar_suggest)
DIAG_NAME_INDEX(err_typecheck_member_reference_struct_union)
DIAG_NAME_INDEX(err_typecheck_member_reference_suggestion)
DIAG_NAME_INDEX(err_typecheck_member_reference_type)
DIAG_NAME_INDEX(err_typecheck_member_reference_unknown)
DIAG_NAME_INDEX(err_typecheck_missing_return_type_incompatible)
DIAG_NAME_INDEX(err_typecheck_negative_array_size)
DIAG_NAME_INDEX(err_typecheck_non_object_not_modifiable_lvalue)
DIAG_NAME_INDEX(err_typecheck_nonviable_condition)
DIAG_NAME_INDEX(err_typecheck_nonviable_condition_incomplete)
DIAG_NAME_INDEX(err_typecheck_op_on_nonoverlapping_address_space_pointers)
DIAG_NAME_INDEX(err_typecheck_ordered_comparison_of_function_pointers)
DIAG_NAME_INDEX(err_typecheck_ordered_comparison_of_pointer_and_zero)
DIAG_NAME_INDEX(err_typecheck_pointer_arith_function_type)
DIAG_NAME_INDEX(err_typecheck_pointer_arith_void_type)
DIAG_NAME_INDEX(err_typecheck_sclass_fscope)
DIAG_NAME_INDEX(err_typecheck_sclass_func)
DIAG_NAME_INDEX(err_typecheck_statement_requires_integer)
DIAG_NAME_INDEX(err_typecheck_statement_requires_scalar)
DIAG_NAME_INDEX(err_typecheck_sub_ptr_compatible)
DIAG_NAME_INDEX(err_typecheck_subscript_not_integer)
DIAG_NAME_INDEX(err_typecheck_subscript_value)
DIAG_NAME_INDEX(err_typecheck_sve_rvv_ambiguous)
DIAG_NAME_INDEX(err_typecheck_sve_rvv_gnu_ambiguous)
DIAG_NAME_INDEX(err_typecheck_three_way_comparison_of_pointer_and_zero)
DIAG_NAME_INDEX(err_typecheck_unary_expr)
DIAG_NAME_INDEX(err_typecheck_vector_lengths_not_equal)
DIAG_NAME_INDEX(err_typecheck_vector_not_convertable)
DIAG_NAME_INDEX(err_typecheck_vector_not_convertable_implict_truncation)
DIAG_NAME_INDEX(err_typecheck_vector_not_convertable_non_scalar)
DIAG_NAME_INDEX(err_typecheck_wasm_table_must_have_zero_length)
DIAG_NAME_INDEX(err_typecheck_zero_array_size)
DIAG_NAME_INDEX(err_typedef_changes_linkage)
DIAG_NAME_INDEX(err_typedef_not_bitfield)
DIAG_NAME_INDEX(err_typedef_not_identifier)
DIAG_NAME_INDEX(err_typename_identifiers_only)
DIAG_NAME_INDEX(err_typename_invalid_constexpr)
DIAG_NAME_INDEX(err_typename_invalid_functionspec)
DIAG_NAME_INDEX(err_typename_invalid_storageclass)
DIAG_NAME_INDEX(err_typename_missing)
DIAG_NAME_INDEX(err_typename_missing_template)
DIAG_NAME_INDEX(err_typename_nested_not_found)
DIAG_NAME_INDEX(err_typename_nested_not_found_enable_if)
DIAG_NAME_INDEX(err_typename_nested_not_found_requirement)
DIAG_NAME_INDEX(err_typename_nested_not_type)
DIAG_NAME_INDEX(err_typename_not_type)
DIAG_NAME_INDEX(err_typename_refers_to_non_type_template)
DIAG_NAME_INDEX(err_typename_refers_to_using_value_decl)
DIAG_NAME_INDEX(err_typename_requires_specqual)
DIAG_NAME_INDEX(err_ucn_control_character)
DIAG_NAME_INDEX(err_ucn_escape_basic_scs)
DIAG_NAME_INDEX(err_ucn_escape_incomplete)
DIAG_NAME_INDEX(err_ucn_escape_invalid)
DIAG_NAME_INDEX(err_unable_to_make_temp)
DIAG_NAME_INDEX(err_unable_to_rename_temp)
DIAG_NAME_INDEX(err_unavailable)
DIAG_NAME_INDEX(err_unavailable_in_arc)
DIAG_NAME_INDEX(err_unavailable_message)
DIAG_NAME_INDEX(err_uncasted_call_of_unknown_any)
DIAG_NAME_INDEX(err_uncasted_send_to_unknown_any_method)
DIAG_NAME_INDEX(err_uncasted_use_of_unknown_any)
DIAG_NAME_INDEX(err_undeclared_boxing_method)
DIAG_NAME_INDEX(err_undeclared_destructor_name)
DIAG_NAME_INDEX(err_undeclared_label_use)
DIAG_NAME_INDEX(err_undeclared_objc_literal_class)
DIAG_NAME_INDEX(err_undeclared_protocol)
DIAG_NAME_INDEX(err_undeclared_protocol_suggest)
DIAG_NAME_INDEX(err_undeclared_use)
DIAG_NAME_INDEX(err_undeclared_use_of_module)
DIAG_NAME_INDEX(err_undeclared_use_of_module_indirect)
DIAG_NAME_INDEX(err_undeclared_use_suggest)
DIAG_NAME_INDEX(err_undeclared_var_use)
DIAG_NAME_INDEX(err_undeclared_var_use_suggest)
DIAG_NAME_INDEX(err_undef_interface)
DIAG_NAME_INDEX(err_undef_interface_suggest)
DIAG_NAME_INDEX(err_undef_superclass)
DIAG_NAME_INDEX(err_undef_superclass_suggest)
DIAG_NAME_INDEX(err_undefined_inline_var)
DIAG_NAME_INDEX(err_undefined_internal_type)
DIAG_NAME_INDEX(err_underlying_type_of_incomplete_enum)
DIAG_NAME_INDEX(err_unevaluated_string_invalid_escape_sequence)
DIAG_NAME_INDEX(err_unevaluated_string_prefix)
DIAG_NAME_INDEX(err_unevaluated_string_udl)
DIAG_NAME_INDEX(err_unexpanded_parameter_pack)
DIAG_NAME_INDEX(err_unexpected_at)
DIAG_NAME_INDEX(err_unexpected_colon_in_nested_name_spec)
DIAG_NAME_INDEX(err_unexpected_friend)
DIAG_NAME_INDEX(err_unexpected_interface)
DIAG_NAME_INDEX(err_unexpected_module_decl)
DIAG_NAME_INDEX(err_unexpected_namespace)
DIAG_NAME_INDEX(err_unexpected_namespace_attributes_alias)
DIAG_NAME_INDEX(err_unexpected_nested_namespace_attribute)
DIAG_NAME_INDEX(err_unexpected_protocol_qualifier)
DIAG_NAME_INDEX(err_unexpected_scope_on_base_decltype)
DIAG_NAME_INDEX(err_unexpected_semi)
DIAG_NAME_INDEX(err_unexpected_template_after_using)
DIAG_NAME_INDEX(err_unexpected_template_in_destructor_name)
DIAG_NAME_INDEX(err_unexpected_template_in_unqualified_id)
DIAG_NAME_INDEX(err_unexpected_token_in_nested_name_spec)
DIAG_NAME_INDEX(err_unexpected_typedef)
DIAG_NAME_INDEX(err_unexpected_typedef_ident)
DIAG_NAME_INDEX(err_unexpected_unqualified_id)
DIAG_NAME_INDEX(err_unimplemented_conversion_with_fixed_point_type)
DIAG_NAME_INDEX(err_uninitialized_member_for_assign)
DIAG_NAME_INDEX(err_uninitialized_member_in_ctor)
DIAG_NAME_INDEX(err_union_as_base_class)
DIAG_NAME_INDEX(err_union_member_of_reference_type)
DIAG_NAME_INDEX(err_unknown_analyzer_checker_or_package)
DIAG_NAME_INDEX(err_unknown_any_addrof)
DIAG_NAME_INDEX(err_unknown_any_addrof_call)
DIAG_NAME_INDEX(err_unknown_any_function)
DIAG_NAME_INDEX(err_unknown_any_var_function_type)
DIAG_NAME_INDEX(err_unknown_hlsl_semantic)
DIAG_NAME_INDEX(err_unknown_nested_typename_suggest)
DIAG_NAME_INDEX(err_unknown_receiver_suggest)
DIAG_NAME_INDEX(err_unknown_template_name)
DIAG_NAME_INDEX(err_unknown_type_or_class_name_suggest)
DIAG_NAME_INDEX(err_unknown_typename)
DIAG_NAME_INDEX(err_unknown_typename_suggest)
DIAG_NAME_INDEX(err_unparenthesized_non_primary_expr_in_requires_clause)
DIAG_NAME_INDEX(err_unqualified_pointer_member_function)
DIAG_NAME_INDEX(err_unspecified_size_with_static)
DIAG_NAME_INDEX(err_unspecified_vla_size_with_static)
DIAG_NAME_INDEX(err_unsupported_abi_for_opt)
DIAG_NAME_INDEX(err_unsupported_ast_node)
DIAG_NAME_INDEX(err_unsupported_bom)
DIAG_NAME_INDEX(err_unsupported_cxx_abi)
DIAG_NAME_INDEX(err_unsupported_module_partition)
DIAG_NAME_INDEX(err_unsupported_placeholder_constraint)
DIAG_NAME_INDEX(err_unsupported_string_concat)
DIAG_NAME_INDEX(err_unsupported_unknown_any_call)
DIAG_NAME_INDEX(err_unsupported_unknown_any_decl)
DIAG_NAME_INDEX(err_unsupported_unknown_any_expr)
DIAG_NAME_INDEX(err_unterm_macro_invoc)
DIAG_NAME_INDEX(err_unterminated___pragma)
DIAG_NAME_INDEX(err_unterminated_block_comment)
DIAG_NAME_INDEX(err_unterminated_raw_string)
DIAG_NAME_INDEX(err_upcast_to_inaccessible_base)
DIAG_NAME_INDEX(err_use_continuation_class)
DIAG_NAME_INDEX(err_use_continuation_class_redeclaration_readwrite)
DIAG_NAME_INDEX(err_use_of_default_argument_to_function_declared_later)
DIAG_NAME_INDEX(err_use_of_empty_using_if_exists)
DIAG_NAME_INDEX(err_use_of_tag_name_without_tag)
DIAG_NAME_INDEX(err_use_of_unaddressable_function)
DIAG_NAME_INDEX(err_use_with_wrong_tag)
DIAG_NAME_INDEX(err_using_attribute_ns_conflict)
DIAG_NAME_INDEX(err_using_decl_can_not_refer_to_class_member)
DIAG_NAME_INDEX(err_using_decl_can_not_refer_to_namespace)
DIAG_NAME_INDEX(err_using_decl_conflict)
DIAG_NAME_INDEX(err_using_decl_conflict_reverse)
DIAG_NAME_INDEX(err_using_decl_constructor)
DIAG_NAME_INDEX(err_using_decl_constructor_not_in_direct_base)
DIAG_NAME_INDEX(err_using_decl_destructor)
DIAG_NAME_INDEX(err_using_decl_friend)
DIAG_NAME_INDEX(err_using_decl_nested_name_specifier_is_current_class)
DIAG_NAME_INDEX(err_using_decl_nested_name_specifier_is_not_base_class)
DIAG_NAME_INDEX(err_using_decl_nested_name_specifier_is_not_class)
DIAG_NAME_INDEX(err_using_decl_redeclaration)
DIAG_NAME_INDEX(err_using_decl_redeclaration_expansion)
DIAG_NAME_INDEX(err_using_decl_template_id)
DIAG_NAME_INDEX(err_using_dependent_value_is_type)
DIAG_NAME_INDEX(err_using_directive_member_suggest)
DIAG_NAME_INDEX(err_using_directive_suggest)
DIAG_NAME_INDEX(err_using_enum_decl_redeclaration)
DIAG_NAME_INDEX(err_using_enum_expect_identifier)
DIAG_NAME_INDEX(err_using_enum_is_dependent)
DIAG_NAME_INDEX(err_using_enum_not_enum)
DIAG_NAME_INDEX(err_using_if_exists_on_ctor)
DIAG_NAME_INDEX(err_using_namespace_in_class)
DIAG_NAME_INDEX(err_using_pack_expansion_empty)
DIAG_NAME_INDEX(err_using_requires_qualname)
DIAG_NAME_INDEX(err_using_typename_non_type)
DIAG_NAME_INDEX(err_uuidof_with_multiple_guids)
DIAG_NAME_INDEX(err_uuidof_without_guid)
DIAG_NAME_INDEX(err_va_arg_in_device)
DIAG_NAME_INDEX(err_va_start_captured_stmt)
DIAG_NAME_INDEX(err_va_start_fixed_function)
DIAG_NAME_INDEX(err_va_start_outside_function)
DIAG_NAME_INDEX(err_va_start_used_in_wrong_abi_function)
DIAG_NAME_INDEX(err_value_init_for_array_type)
DIAG_NAME_INDEX(err_vaopt_paste_at_end)
DIAG_NAME_INDEX(err_vaopt_paste_at_start)
DIAG_NAME_INDEX(err_var_partial_spec_redeclared)
DIAG_NAME_INDEX(err_var_spec_no_template)
DIAG_NAME_INDEX(err_var_spec_no_template_but_method)
DIAG_NAME_INDEX(err_variable_instantiates_to_function)
DIAG_NAME_INDEX(err_variable_object_no_init)
DIAG_NAME_INDEX(err_variably_modified_new_type)
DIAG_NAME_INDEX(err_variably_modified_nontype_template_param)
DIAG_NAME_INDEX(err_variably_modified_template_arg)
DIAG_NAME_INDEX(err_variably_modified_typeid)
DIAG_NAME_INDEX(err_variadic_device_fn)
DIAG_NAME_INDEX(err_vec_builtin_incompatible_vector)
DIAG_NAME_INDEX(err_vec_builtin_non_vector)
DIAG_NAME_INDEX(err_vecstep_non_scalar_vector_type)
DIAG_NAME_INDEX(err_vector_incorrect_num_initializers)
DIAG_NAME_INDEX(err_verify_ambiguous_marker)
DIAG_NAME_INDEX(err_verify_inconsistent_diags)
DIAG_NAME_INDEX(err_verify_invalid_content)
DIAG_NAME_INDEX(err_verify_invalid_no_diags)
DIAG_NAME_INDEX(err_verify_invalid_range)
DIAG_NAME_INDEX(err_verify_missing_end)
DIAG_NAME_INDEX(err_verify_missing_file)
DIAG_NAME_INDEX(err_verify_missing_line)
DIAG_NAME_INDEX(err_verify_missing_regex)
DIAG_NAME_INDEX(err_verify_missing_start)
DIAG_NAME_INDEX(err_verify_no_directives)
DIAG_NAME_INDEX(err_verify_no_such_marker)
DIAG_NAME_INDEX(err_verify_nonconst_addrspace)
DIAG_NAME_INDEX(err_vftable_ambiguous_component)
DIAG_NAME_INDEX(err_virt_specifier_outside_class)
DIAG_NAME_INDEX(err_virtual_in_union)
DIAG_NAME_INDEX(err_virtual_member_function_template)
DIAG_NAME_INDEX(err_virtual_non_function)
DIAG_NAME_INDEX(err_virtual_out_of_class)
DIAG_NAME_INDEX(err_vla_decl_has_extern_linkage)
DIAG_NAME_INDEX(err_vla_decl_has_static_storage)
DIAG_NAME_INDEX(err_vla_decl_in_file_scope)
DIAG_NAME_INDEX(err_vla_in_sfinae)
DIAG_NAME_INDEX(err_vla_unsupported)
DIAG_NAME_INDEX(err_vm_decl_has_extern_linkage)
DIAG_NAME_INDEX(err_vm_decl_in_file_scope)
DIAG_NAME_INDEX(err_vm_func_decl)
DIAG_NAME_INDEX(err_void_only_param)
DIAG_NAME_INDEX(err_void_param_qualified)
DIAG_NAME_INDEX(err_volatile_comparison_operator)
DIAG_NAME_INDEX(err_vsx_builtin_nonconstant_argument)
DIAG_NAME_INDEX(err_wasm_builtin_arg_must_be_integer_type)
DIAG_NAME_INDEX(err_wasm_builtin_arg_must_be_table_type)
DIAG_NAME_INDEX(err_wasm_builtin_arg_must_match_table_element_type)
DIAG_NAME_INDEX(err_wasm_ca_reference)
DIAG_NAME_INDEX(err_wasm_cast_table)
DIAG_NAME_INDEX(err_wasm_funcref_not_wasm)
DIAG_NAME_INDEX(err_wasm_reference_pr)
DIAG_NAME_INDEX(err_wasm_reftype_exception_spec)
DIAG_NAME_INDEX(err_wasm_reftype_multidimensional_array)
DIAG_NAME_INDEX(err_wasm_reftype_tc)
DIAG_NAME_INDEX(err_wasm_table_art)
DIAG_NAME_INDEX(err_wasm_table_as_function_parameter)
DIAG_NAME_INDEX(err_wasm_table_conditional_expression)
DIAG_NAME_INDEX(err_wasm_table_in_function)
DIAG_NAME_INDEX(err_wasm_table_invalid_uett_operand)
DIAG_NAME_INDEX(err_wasm_table_must_be_static)
DIAG_NAME_INDEX(err_wasm_table_pr)
DIAG_NAME_INDEX(err_weak_property)
DIAG_NAME_INDEX(err_while_loop_outside_of_a_function)
DIAG_NAME_INDEX(err_wrong_sampler_addressspace)
DIAG_NAME_INDEX(err_x86_builtin_invalid_rounding)
DIAG_NAME_INDEX(err_x86_builtin_invalid_scale)
DIAG_NAME_INDEX(err_x86_builtin_tile_arg_duplicate)
DIAG_NAME_INDEX(err_zero_version)
DIAG_NAME_INDEX(error_cconv_unsupported)
DIAG_NAME_INDEX(error_duplicate_asm_operand_name)
DIAG_NAME_INDEX(error_inoutput_conflict_with_clobber)
DIAG_NAME_INDEX(error_subscript_overload)
DIAG_NAME_INDEX(escaped_newline_block_comment_end)
DIAG_NAME_INDEX(ext_abstract_pack_declarator_parens)
DIAG_NAME_INDEX(ext_adl_only_template_id)
DIAG_NAME_INDEX(ext_aggregate_init_not_constant)
DIAG_NAME_INDEX(ext_alias_declaration)
DIAG_NAME_INDEX(ext_alias_in_init_statement)
DIAG_NAME_INDEX(ext_alignof_expr)
DIAG_NAME_INDEX(ext_anonymous_record_with_anonymous_type)
DIAG_NAME_INDEX(ext_anonymous_record_with_type)
DIAG_NAME_INDEX(ext_anonymous_struct_union_qualified)
DIAG_NAME_INDEX(ext_anonymous_union)
DIAG_NAME_INDEX(ext_array_init_copy)
DIAG_NAME_INDEX(ext_array_init_parens)
DIAG_NAME_INDEX(ext_array_size_conversion)
DIAG_NAME_INDEX(ext_auto_new_list_init)
DIAG_NAME_INDEX(ext_auto_storage_class)
DIAG_NAME_INDEX(ext_auto_type)
DIAG_NAME_INDEX(ext_auto_type_specifier)
DIAG_NAME_INDEX(ext_bad_cxx_cast_qualifiers_away_incoherent)
DIAG_NAME_INDEX(ext_binary_literal)
DIAG_NAME_INDEX(ext_binary_literal_cxx14)
DIAG_NAME_INDEX(ext_bit_int)
DIAG_NAME_INDEX(ext_bitfield_member_init)
DIAG_NAME_INDEX(ext_c11_anonymous_struct)
DIAG_NAME_INDEX(ext_c11_feature)
DIAG_NAME_INDEX(ext_c2x_bitint_suffix)
DIAG_NAME_INDEX(ext_c2x_pp_directive)
DIAG_NAME_INDEX(ext_c99_array_usage)
DIAG_NAME_INDEX(ext_c99_compound_literal)
DIAG_NAME_INDEX(ext_c99_feature)
DIAG_NAME_INDEX(ext_c99_flexible_array_member)
DIAG_NAME_INDEX(ext_c99_longlong)
DIAG_NAME_INDEX(ext_c99_variable_decl_in_for_loop)
DIAG_NAME_INDEX(ext_c99_whitespace_required_after_macro_name)
DIAG_NAME_INDEX(ext_c_empty_initializer)
DIAG_NAME_INDEX(ext_c_label_end_of_compound_statement)
DIAG_NAME_INDEX(ext_c_nullptr)
DIAG_NAME_INDEX(ext_c_static_assert_no_message)
DIAG_NAME_INDEX(ext_cannot_use_trivial_abi)
DIAG_NAME_INDEX(ext_capture_binding)
DIAG_NAME_INDEX(ext_cast_fn_obj)
DIAG_NAME_INDEX(ext_cce_narrowing)
DIAG_NAME_INDEX(ext_charize_microsoft)
DIAG_NAME_INDEX(ext_clang_c_enum_fixed_underlying_type)
DIAG_NAME_INDEX(ext_clang_diagnose_if)
DIAG_NAME_INDEX(ext_clang_enable_if)
DIAG_NAME_INDEX(ext_comment_paste_microsoft)
DIAG_NAME_INDEX(ext_complex_component_init)
DIAG_NAME_INDEX(ext_consteval_if)
DIAG_NAME_INDEX(ext_constexpr_body_invalid_stmt)
DIAG_NAME_INDEX(ext_constexpr_body_invalid_stmt_cxx20)
DIAG_NAME_INDEX(ext_constexpr_body_invalid_stmt_cxx23)
DIAG_NAME_INDEX(ext_constexpr_body_multiple_return)
DIAG_NAME_INDEX(ext_constexpr_ctor_missing_init)
DIAG_NAME_INDEX(ext_constexpr_function_never_constant_expr)
DIAG_NAME_INDEX(ext_constexpr_function_try_block_cxx20)
DIAG_NAME_INDEX(ext_constexpr_if)
DIAG_NAME_INDEX(ext_constexpr_local_var)
DIAG_NAME_INDEX(ext_constexpr_local_var_no_init)
DIAG_NAME_INDEX(ext_constexpr_on_lambda_cxx17)
DIAG_NAME_INDEX(ext_constexpr_static_var)
DIAG_NAME_INDEX(ext_constexpr_type_definition)
DIAG_NAME_INDEX(ext_constexpr_union_ctor_no_init)
DIAG_NAME_INDEX(ext_constinit_missing)
DIAG_NAME_INDEX(ext_ctrl_z_eof_microsoft)
DIAG_NAME_INDEX(ext_cxx11_attr_placement)
DIAG_NAME_INDEX(ext_cxx11_enum_fixed_underlying_type)
DIAG_NAME_INDEX(ext_cxx11_longlong)
DIAG_NAME_INDEX(ext_cxx14_attr)
DIAG_NAME_INDEX(ext_cxx17_attr)
DIAG_NAME_INDEX(ext_cxx20_attr)
DIAG_NAME_INDEX(ext_cxx23_pp_directive)
DIAG_NAME_INDEX(ext_cxx23_size_t_suffix)
DIAG_NAME_INDEX(ext_cxx_designated_init)
DIAG_NAME_INDEX(ext_cxx_label_end_of_compound_statement)
DIAG_NAME_INDEX(ext_cxx_static_assert_no_message)
DIAG_NAME_INDEX(ext_decl_attrs_on_lambda)
DIAG_NAME_INDEX(ext_decltype_auto_type_specifier)
DIAG_NAME_INDEX(ext_decomp_decl)
DIAG_NAME_INDEX(ext_decomp_decl_cond)
DIAG_NAME_INDEX(ext_decomp_decl_empty)
DIAG_NAME_INDEX(ext_decomp_decl_spec)
DIAG_NAME_INDEX(ext_default_init_const)
DIAG_NAME_INDEX(ext_defaulted_comparison)
DIAG_NAME_INDEX(ext_defaulted_comparison_constexpr_mismatch)
DIAG_NAME_INDEX(ext_defaulted_deleted_function)
DIAG_NAME_INDEX(ext_delete_void_ptr_operand)
DIAG_NAME_INDEX(ext_delimited_escape_sequence)
DIAG_NAME_INDEX(ext_deprecated_string_literal_conversion)
DIAG_NAME_INDEX(ext_designated_init)
DIAG_NAME_INDEX(ext_designated_init_array)
DIAG_NAME_INDEX(ext_designated_init_brace_elision)
DIAG_NAME_INDEX(ext_designated_init_mixed)
DIAG_NAME_INDEX(ext_designated_init_nested)
DIAG_NAME_INDEX(ext_designated_init_reordered)
DIAG_NAME_INDEX(ext_destructor_typedef_name)
DIAG_NAME_INDEX(ext_dollar_in_identifier)
DIAG_NAME_INDEX(ext_dtor_name_ambiguous)
DIAG_NAME_INDEX(ext_dtor_named_in_wrong_scope)
DIAG_NAME_INDEX(ext_duplicate_declspec)
DIAG_NAME_INDEX(ext_dynamic_exception_spec)
DIAG_NAME_INDEX(ext_elaborated_enum_class)
DIAG_NAME_INDEX(ext_ellipsis_exception_spec)
DIAG_NAME_INDEX(ext_embedded_directive)
DIAG_NAME_INDEX(ext_empty_character)
DIAG_NAME_INDEX(ext_empty_fnmacro_arg)
DIAG_NAME_INDEX(ext_empty_struct_union)
DIAG_NAME_INDEX(ext_empty_translation_unit)
DIAG_NAME_INDEX(ext_enum_base_in_type_specifier)
DIAG_NAME_INDEX(ext_enum_friend)
DIAG_NAME_INDEX(ext_enum_too_large)
DIAG_NAME_INDEX(ext_enum_value_not_int)
DIAG_NAME_INDEX(ext_enumerator_increment_too_large)
DIAG_NAME_INDEX(ext_enumerator_list_comma_c)
DIAG_NAME_INDEX(ext_enumerator_list_comma_cxx)
DIAG_NAME_INDEX(ext_enumerator_too_large)
DIAG_NAME_INDEX(ext_equals_this_lambda_capture_cxx20)
DIAG_NAME_INDEX(ext_equivalent_internal_linkage_decl_in_modules)
DIAG_NAME_INDEX(ext_excess_initializers)
DIAG_NAME_INDEX(ext_excess_initializers_for_sizeless_type)
DIAG_NAME_INDEX(ext_excess_initializers_in_char_array_initializer)
DIAG_NAME_INDEX(ext_expected_semi_decl_list)
DIAG_NAME_INDEX(ext_explicit_bool)
DIAG_NAME_INDEX(ext_explicit_conversion_functions)
DIAG_NAME_INDEX(ext_explicit_instantiation_duplicate)
DIAG_NAME_INDEX(ext_explicit_instantiation_without_qualified_id)
DIAG_NAME_INDEX(ext_explicit_specialization_storage_class)
DIAG_NAME_INDEX(ext_expr_not_ice)
DIAG_NAME_INDEX(ext_extern_template)
DIAG_NAME_INDEX(ext_extra_semi)
DIAG_NAME_INDEX(ext_extra_semi_cxx11)
DIAG_NAME_INDEX(ext_flexible_array_empty_aggregate_gnu)
DIAG_NAME_INDEX(ext_flexible_array_empty_aggregate_ms)
DIAG_NAME_INDEX(ext_flexible_array_in_array)
DIAG_NAME_INDEX(ext_flexible_array_in_struct)
DIAG_NAME_INDEX(ext_flexible_array_init)
DIAG_NAME_INDEX(ext_flexible_array_union_gnu)
DIAG_NAME_INDEX(ext_flexible_array_union_ms)
DIAG_NAME_INDEX(ext_fold_expression)
DIAG_NAME_INDEX(ext_for_range)
DIAG_NAME_INDEX(ext_for_range_begin_end_types_differ)
DIAG_NAME_INDEX(ext_for_range_init_stmt)
DIAG_NAME_INDEX(ext_forward_ref_enum)
DIAG_NAME_INDEX(ext_forward_ref_enum_def)
DIAG_NAME_INDEX(ext_found_in_dependent_base)
DIAG_NAME_INDEX(ext_found_later_in_class)
DIAG_NAME_INDEX(ext_freestanding_complex)
DIAG_NAME_INDEX(ext_friend_tag_redecl_outside_namespace)
DIAG_NAME_INDEX(ext_generalized_initializer_lists)
DIAG_NAME_INDEX(ext_generic_with_type_arg)
DIAG_NAME_INDEX(ext_gnu_address_of_label)
DIAG_NAME_INDEX(ext_gnu_anonymous_struct)
DIAG_NAME_INDEX(ext_gnu_array_range)
DIAG_NAME_INDEX(ext_gnu_case_range)
DIAG_NAME_INDEX(ext_gnu_conditional_expr)
DIAG_NAME_INDEX(ext_gnu_indirect_goto)
DIAG_NAME_INDEX(ext_gnu_missing_equal_designator)
DIAG_NAME_INDEX(ext_gnu_old_style_field_designator)
DIAG_NAME_INDEX(ext_gnu_ptr_func_arith)
DIAG_NAME_INDEX(ext_gnu_statement_expr)
DIAG_NAME_INDEX(ext_gnu_statement_expr_macro)
DIAG_NAME_INDEX(ext_gnu_subscript_void_type)
DIAG_NAME_INDEX(ext_gnu_void_ptr)
DIAG_NAME_INDEX(ext_goto_into_protected_scope)
DIAG_NAME_INDEX(ext_hex_constant_invalid)
DIAG_NAME_INDEX(ext_hex_literal_invalid)
DIAG_NAME_INDEX(ext_hlsl_access_specifiers)
DIAG_NAME_INDEX(ext_ident_list_in_param)
DIAG_NAME_INDEX(ext_imaginary_constant)
DIAG_NAME_INDEX(ext_implicit_exception_spec_mismatch)
DIAG_NAME_INDEX(ext_implicit_function_decl_c99)
DIAG_NAME_INDEX(ext_implicit_lib_function_decl)
DIAG_NAME_INDEX(ext_implicit_lib_function_decl_c99)
DIAG_NAME_INDEX(ext_implicit_typename)
DIAG_NAME_INDEX(ext_in_class_initializer_float_type)
DIAG_NAME_INDEX(ext_in_class_initializer_float_type_cxx11)
DIAG_NAME_INDEX(ext_in_class_initializer_non_constant)
DIAG_NAME_INDEX(ext_incomplete_in_exception_spec)
DIAG_NAME_INDEX(ext_increment_bool)
DIAG_NAME_INDEX(ext_init_capture)
DIAG_NAME_INDEX(ext_init_capture_pack)
DIAG_NAME_INDEX(ext_init_from_predefined)
DIAG_NAME_INDEX(ext_init_list_constant_narrowing)
DIAG_NAME_INDEX(ext_init_list_type_narrowing)
DIAG_NAME_INDEX(ext_init_list_variable_narrowing)
DIAG_NAME_INDEX(ext_init_statement)
DIAG_NAME_INDEX(ext_initializer_overrides)
DIAG_NAME_INDEX(ext_initializer_string_for_char_array_too_long)
DIAG_NAME_INDEX(ext_initializer_union_overrides)
DIAG_NAME_INDEX(ext_inline_namespace)
DIAG_NAME_INDEX(ext_inline_nested_namespace_definition)
DIAG_NAME_INDEX(ext_inline_variable)
DIAG_NAME_INDEX(ext_integer_complement_complex)
DIAG_NAME_INDEX(ext_integer_complex)
DIAG_NAME_INDEX(ext_integer_increment_complex)
DIAG_NAME_INDEX(ext_integer_literal_too_large_for_signed)
DIAG_NAME_INDEX(ext_internal_in_extern_inline)
DIAG_NAME_INDEX(ext_internal_in_extern_inline_quiet)
DIAG_NAME_INDEX(ext_keyword_as_ident)
DIAG_NAME_INDEX(ext_lambda_missing_parens)
DIAG_NAME_INDEX(ext_lambda_template_parameter_list)
DIAG_NAME_INDEX(ext_line_comment)
DIAG_NAME_INDEX(ext_main_returns_nonint)
DIAG_NAME_INDEX(ext_main_used)
DIAG_NAME_INDEX(ext_many_braces_around_init)
DIAG_NAME_INDEX(ext_mathematical_notation)
DIAG_NAME_INDEX(ext_member_redeclared)
DIAG_NAME_INDEX(ext_mismatched_exception_spec)
DIAG_NAME_INDEX(ext_mismatched_exception_spec_explicit_instantiation)
DIAG_NAME_INDEX(ext_missing_exception_specification)
DIAG_NAME_INDEX(ext_missing_type_specifier)
DIAG_NAME_INDEX(ext_missing_varargs_arg)
DIAG_NAME_INDEX(ext_missing_whitespace_after_macro_name)
DIAG_NAME_INDEX(ext_mixed_decls_code)
DIAG_NAME_INDEX(ext_module_import_in_extern_c)
DIAG_NAME_INDEX(ext_module_import_not_at_top_level_noop)
DIAG_NAME_INDEX(ext_ms_abstract_keyword)
DIAG_NAME_INDEX(ext_ms_ambiguous_direct_base)
DIAG_NAME_INDEX(ext_ms_anonymous_record)
DIAG_NAME_INDEX(ext_ms_c_enum_fixed_underlying_type)
DIAG_NAME_INDEX(ext_ms_cast_fn_obj)
DIAG_NAME_INDEX(ext_ms_delayed_template_argument)
DIAG_NAME_INDEX(ext_ms_deref_template_argument)
DIAG_NAME_INDEX(ext_ms_explicit_constructor_call)
DIAG_NAME_INDEX(ext_ms_forward_ref_enum)
DIAG_NAME_INDEX(ext_ms_impcast_fn_obj)
DIAG_NAME_INDEX(ext_ms_reserved_user_defined_literal)
DIAG_NAME_INDEX(ext_ms_sealed_keyword)
DIAG_NAME_INDEX(ext_ms_static_assert)
DIAG_NAME_INDEX(ext_ms_template_spec_redecl_out_of_scope)
DIAG_NAME_INDEX(ext_ms_template_type_arg_missing_typename)
DIAG_NAME_INDEX(ext_ms_using_declaration_inaccessible)
DIAG_NAME_INDEX(ext_multi_line_line_comment)
DIAG_NAME_INDEX(ext_multi_using_declaration)
DIAG_NAME_INDEX(ext_mutable_reference)
DIAG_NAME_INDEX(ext_named_variadic_macro)
DIAG_NAME_INDEX(ext_nested_name_member_ref_lookup_ambiguous)
DIAG_NAME_INDEX(ext_nested_name_spec_is_enum)
DIAG_NAME_INDEX(ext_nested_namespace_definition)
DIAG_NAME_INDEX(ext_nested_pointer_qualifier_mismatch)
DIAG_NAME_INDEX(ext_new_paren_array_nonconst)
DIAG_NAME_INDEX(ext_no_declarators)
DIAG_NAME_INDEX(ext_no_named_members_in_struct_union)
DIAG_NAME_INDEX(ext_no_newline_eof)
DIAG_NAME_INDEX(ext_non_c_like_anon_struct_in_typedef)
DIAG_NAME_INDEX(ext_nonclass_type_friend)
DIAG_NAME_INDEX(ext_nonstandard_escape)
DIAG_NAME_INDEX(ext_nonstatic_member_init)
DIAG_NAME_INDEX(ext_noreturn_main)
DIAG_NAME_INDEX(ext_ns_enum_attribute)
DIAG_NAME_INDEX(ext_nullability)
DIAG_NAME_INDEX(ext_offsetof_non_pod_type)
DIAG_NAME_INDEX(ext_offsetof_non_standardlayout_type)
DIAG_NAME_INDEX(ext_old_implicitly_unsigned_long_cxx)
DIAG_NAME_INDEX(ext_omp_attributes)
DIAG_NAME_INDEX(ext_omp_loop_not_canonical_init)
DIAG_NAME_INDEX(ext_on_off_switch_syntax)
DIAG_NAME_INDEX(ext_opencl_double_without_pragma)
DIAG_NAME_INDEX(ext_opencl_ext_vector_type_rgba_selector)
DIAG_NAME_INDEX(ext_operator_new_delete_declared_inline)
DIAG_NAME_INDEX(ext_operator_overload_static)
DIAG_NAME_INDEX(ext_out_of_line_declaration)
DIAG_NAME_INDEX(ext_out_of_line_qualified_id_type_names_constructor)
DIAG_NAME_INDEX(ext_override_control_keyword)
DIAG_NAME_INDEX(ext_override_exception_spec)
DIAG_NAME_INDEX(ext_ovl_ambiguous_oper_binary_reversed)
DIAG_NAME_INDEX(ext_ovl_rewrite_equalequal_not_bool)
DIAG_NAME_INDEX(ext_param_default_argument_redefinition)
DIAG_NAME_INDEX(ext_param_not_declared)
DIAG_NAME_INDEX(ext_param_promoted_not_compatible_with_prototype)
DIAG_NAME_INDEX(ext_parameter_name_omitted_c2x)
DIAG_NAME_INDEX(ext_partial_spec_not_more_specialized_than_primary)
DIAG_NAME_INDEX(ext_partial_specs_not_deducible)
DIAG_NAME_INDEX(ext_paste_comma)
DIAG_NAME_INDEX(ext_plain_complex)
DIAG_NAME_INDEX(ext_pointer_to_const_ref_member_on_rvalue)
DIAG_NAME_INDEX(ext_pp_bad_paste_ms)
DIAG_NAME_INDEX(ext_pp_bad_vaargs_use)
DIAG_NAME_INDEX(ext_pp_bad_vaopt_use)
DIAG_NAME_INDEX(ext_pp_comma_expr)
DIAG_NAME_INDEX(ext_pp_extra_tokens_at_eol)
DIAG_NAME_INDEX(ext_pp_gnu_line_directive)
DIAG_NAME_INDEX(ext_pp_ident_directive)
DIAG_NAME_INDEX(ext_pp_import_directive)
DIAG_NAME_INDEX(ext_pp_include_next_directive)
DIAG_NAME_INDEX(ext_pp_include_search_ms)
DIAG_NAME_INDEX(ext_pp_line_too_big)
DIAG_NAME_INDEX(ext_pp_line_zero)
DIAG_NAME_INDEX(ext_pp_macro_redef)
DIAG_NAME_INDEX(ext_pp_opencl_variadic_macros)
DIAG_NAME_INDEX(ext_pp_operator_used_as_macro_name)
DIAG_NAME_INDEX(ext_pp_redef_builtin_macro)
DIAG_NAME_INDEX(ext_pp_undef_builtin_macro)
DIAG_NAME_INDEX(ext_pp_warning_directive)
DIAG_NAME_INDEX(ext_pragma_syntax_eod)
DIAG_NAME_INDEX(ext_predef_outside_function)
DIAG_NAME_INDEX(ext_pseudo_dtor_on_void)
DIAG_NAME_INDEX(ext_pure_function_definition)
DIAG_NAME_INDEX(ext_qualified_dtor_named_in_lexical_scope)
DIAG_NAME_INDEX(ext_redefinition_of_typedef)
DIAG_NAME_INDEX(ext_ref_qualifier)
DIAG_NAME_INDEX(ext_register_storage_class)
DIAG_NAME_INDEX(ext_reserved_user_defined_literal)
DIAG_NAME_INDEX(ext_retained_language_linkage)
DIAG_NAME_INDEX(ext_return_has_expr)
DIAG_NAME_INDEX(ext_return_has_void_expr)
DIAG_NAME_INDEX(ext_return_missing_expr)
DIAG_NAME_INDEX(ext_rvalue_reference)
DIAG_NAME_INDEX(ext_rvalue_to_reference_access_ctor)
DIAG_NAME_INDEX(ext_rvalue_to_reference_temp_copy_no_viable)
DIAG_NAME_INDEX(ext_scoped_enum)
DIAG_NAME_INDEX(ext_sizeof_alignof_function_type)
DIAG_NAME_INDEX(ext_sizeof_alignof_void_type)
DIAG_NAME_INDEX(ext_standalone_specifier)
DIAG_NAME_INDEX(ext_star_this_lambda_capture_cxx17)
DIAG_NAME_INDEX(ext_static_data_member_in_union)
DIAG_NAME_INDEX(ext_static_non_static)
DIAG_NAME_INDEX(ext_static_out_of_line)
DIAG_NAME_INDEX(ext_stdc_pragma_ignored)
DIAG_NAME_INDEX(ext_string_literal_operator_template)
DIAG_NAME_INDEX(ext_string_too_long)
DIAG_NAME_INDEX(ext_subscript_non_lvalue)
DIAG_NAME_INDEX(ext_subscript_overload)
DIAG_NAME_INDEX(ext_template_arg_extra_parens)
DIAG_NAME_INDEX(ext_template_arg_local_type)
DIAG_NAME_INDEX(ext_template_arg_object_internal)
DIAG_NAME_INDEX(ext_template_arg_unnamed_type)
DIAG_NAME_INDEX(ext_template_outside_of_template)
DIAG_NAME_INDEX(ext_template_param_shadow)
DIAG_NAME_INDEX(ext_template_parameter_default_in_function_template)
DIAG_NAME_INDEX(ext_template_template_param_typename)
DIAG_NAME_INDEX(ext_thread_before)
DIAG_NAME_INDEX(ext_token_used)
DIAG_NAME_INDEX(ext_type_defined_in_offsetof)
DIAG_NAME_INDEX(ext_typecheck_addrof_temporary)
DIAG_NAME_INDEX(ext_typecheck_addrof_void)
DIAG_NAME_INDEX(ext_typecheck_base_super)
DIAG_NAME_INDEX(ext_typecheck_cast_nonscalar)
DIAG_NAME_INDEX(ext_typecheck_cast_to_union)
DIAG_NAME_INDEX(ext_typecheck_compare_complete_incomplete_pointers)
DIAG_NAME_INDEX(ext_typecheck_comparison_of_distinct_pointers)
DIAG_NAME_INDEX(ext_typecheck_comparison_of_fptr_to_void)
DIAG_NAME_INDEX(ext_typecheck_comparison_of_pointer_integer)
DIAG_NAME_INDEX(ext_typecheck_cond_incompatible_operands)
DIAG_NAME_INDEX(ext_typecheck_cond_incompatible_pointers)
DIAG_NAME_INDEX(ext_typecheck_cond_one_void)
DIAG_NAME_INDEX(ext_typecheck_cond_pointer_integer_mismatch)
DIAG_NAME_INDEX(ext_typecheck_convert_discards_qualifiers)
DIAG_NAME_INDEX(ext_typecheck_convert_incompatible_function_pointer)
DIAG_NAME_INDEX(ext_typecheck_convert_incompatible_pointer)
DIAG_NAME_INDEX(ext_typecheck_convert_incompatible_pointer_sign)
DIAG_NAME_INDEX(ext_typecheck_convert_int_pointer)
DIAG_NAME_INDEX(ext_typecheck_convert_pointer_int)
DIAG_NAME_INDEX(ext_typecheck_convert_pointer_void_func)
DIAG_NAME_INDEX(ext_typecheck_decl_incomplete_type)
DIAG_NAME_INDEX(ext_typecheck_indirection_through_void_pointer)
DIAG_NAME_INDEX(ext_typecheck_ordered_comparison_of_function_pointers)
DIAG_NAME_INDEX(ext_typecheck_ordered_comparison_of_pointer_and_zero)
DIAG_NAME_INDEX(ext_typecheck_ordered_comparison_of_pointer_integer)
DIAG_NAME_INDEX(ext_typecheck_zero_array_size)
DIAG_NAME_INDEX(ext_typedef_without_a_name)
DIAG_NAME_INDEX(ext_typename_missing)
DIAG_NAME_INDEX(ext_typename_outside_of_template)
DIAG_NAME_INDEX(ext_undeclared_unqual_id_with_dependent_base)
DIAG_NAME_INDEX(ext_undefined_internal_type)
DIAG_NAME_INDEX(ext_unelaborated_friend_type)
DIAG_NAME_INDEX(ext_unicode_whitespace)
DIAG_NAME_INDEX(ext_union_member_of_reference_type)
DIAG_NAME_INDEX(ext_unknown_escape)
DIAG_NAME_INDEX(ext_unqualified_base_class)
DIAG_NAME_INDEX(ext_unterminated_char_or_string)
DIAG_NAME_INDEX(ext_use_out_of_scope_declaration)
DIAG_NAME_INDEX(ext_using_attribute_ns)
DIAG_NAME_INDEX(ext_using_decl_scoped_enumerator)
DIAG_NAME_INDEX(ext_using_declaration_pack)
DIAG_NAME_INDEX(ext_using_enum_declaration)
DIAG_NAME_INDEX(ext_using_undefined_std)
DIAG_NAME_INDEX(ext_variable_sized_type_in_struct)
DIAG_NAME_INDEX(ext_variable_template)
DIAG_NAME_INDEX(ext_variadic_macro)
DIAG_NAME_INDEX(ext_variadic_main)
DIAG_NAME_INDEX(ext_variadic_templates)
DIAG_NAME_INDEX(ext_vla)
DIAG_NAME_INDEX(ext_vla_folded_to_constant)
DIAG_NAME_INDEX(ext_warn_duplicate_declspec)
DIAG_NAME_INDEX(ext_warn_gnu_final)
DIAG_NAME_INDEX(ext_wchar_t_sign_spec)
DIAG_NAME_INDEX(fatal_too_many_errors)
DIAG_NAME_INDEX(note_access_constrained_by_path)
DIAG_NAME_INDEX(note_access_natural)
DIAG_NAME_INDEX(note_access_protected_restricted_ctordtor)
DIAG_NAME_INDEX(note_access_protected_restricted_noobject)
DIAG_NAME_INDEX(note_access_protected_restricted_object)
DIAG_NAME_INDEX(note_add_deprecation_attr)
DIAG_NAME_INDEX(note_add_synthesize_directive)
DIAG_NAME_INDEX(note_additional_parens_for_variable_declaration)
DIAG_NAME_INDEX(note_addrof_ovl_candidate_disabled_by_enable_if_attr)
DIAG_NAME_INDEX(note_alias_mangled_name_alternative)
DIAG_NAME_INDEX(note_alias_requires_mangled_name)
DIAG_NAME_INDEX(note_alignas_on_declaration)
DIAG_NAME_INDEX(note_allocated_here)
DIAG_NAME_INDEX(note_also_found)
DIAG_NAME_INDEX(note_ambig_member_ref_object_type)
DIAG_NAME_INDEX(note_ambig_member_ref_scope)
DIAG_NAME_INDEX(note_ambiguous_atomic_constraints)
DIAG_NAME_INDEX(note_ambiguous_atomic_constraints_similar_expression)
DIAG_NAME_INDEX(note_ambiguous_candidate)
DIAG_NAME_INDEX(note_ambiguous_inherited_constructor_using)
DIAG_NAME_INDEX(note_ambiguous_member_found)
DIAG_NAME_INDEX(note_ambiguous_member_type_found)
DIAG_NAME_INDEX(note_ambiguous_type_conversion)
DIAG_NAME_INDEX(note_anonymous_namespace)
DIAG_NAME_INDEX(note_arc_bridge)
DIAG_NAME_INDEX(note_arc_bridge_retained)
DIAG_NAME_INDEX(note_arc_bridge_transfer)
DIAG_NAME_INDEX(note_arc_cstyle_bridge)
DIAG_NAME_INDEX(note_arc_cstyle_bridge_retained)
DIAG_NAME_INDEX(note_arc_cstyle_bridge_transfer)
DIAG_NAME_INDEX(note_arc_field_with_ownership)
DIAG_NAME_INDEX(note_arc_forbidden_type)
DIAG_NAME_INDEX(note_arc_gained_method_convention)
DIAG_NAME_INDEX(note_arc_init_returns_unrelated)
DIAG_NAME_INDEX(note_arc_lost_method_convention)
DIAG_NAME_INDEX(note_arc_retain_cycle_owner)
DIAG_NAME_INDEX(note_arc_weak_also_accessed_here)
DIAG_NAME_INDEX(note_arc_weak_disabled)
DIAG_NAME_INDEX(note_arc_weak_no_runtime)
DIAG_NAME_INDEX(note_array_declared_here)
DIAG_NAME_INDEX(note_array_init_plain_string_into_char8_t)
DIAG_NAME_INDEX(note_array_size_conversion)
DIAG_NAME_INDEX(note_asm_input_duplicate_first)
DIAG_NAME_INDEX(note_asm_missing_constraint_modifier)
DIAG_NAME_INDEX(note_assign_lhs_incomplete)
DIAG_NAME_INDEX(note_atomic_constraint_evaluated_to_false)
DIAG_NAME_INDEX(note_atomic_constraint_evaluated_to_false_elaborated)
DIAG_NAME_INDEX(note_atomic_property_fixup_suggest)
DIAG_NAME_INDEX(note_attribute)
DIAG_NAME_INDEX(note_attribute_has_no_effect_on_compile_time_if_here)
DIAG_NAME_INDEX(note_attribute_has_no_effect_on_infinite_loop_here)
DIAG_NAME_INDEX(note_attribute_overloadable_prev_overload)
DIAG_NAME_INDEX(note_auto_readonly_iboutlet_fixup_suggest)
DIAG_NAME_INDEX(note_availability_specified_here)
DIAG_NAME_INDEX(note_await_ready_no_bool_conversion)
DIAG_NAME_INDEX(note_bad_memaccess_silence)
DIAG_NAME_INDEX(note_base_class_specified_here)
DIAG_NAME_INDEX(note_bitfield_decl)
DIAG_NAME_INDEX(note_block_var_fixit_add_initialization)
DIAG_NAME_INDEX(note_bracket_depth)
DIAG_NAME_INDEX(note_building_builtin_dump_struct_call)
DIAG_NAME_INDEX(note_called_by)
DIAG_NAME_INDEX(note_called_once_gets_called_twice)
DIAG_NAME_INDEX(note_callee_decl)
DIAG_NAME_INDEX(note_callee_static_array)
DIAG_NAME_INDEX(note_cannot_use_trivial_abi_reason)
DIAG_NAME_INDEX(note_carries_dependency_missing_first_decl)
DIAG_NAME_INDEX(note_cast_operand_to_int)
DIAG_NAME_INDEX(note_cast_to_void)
DIAG_NAME_INDEX(note_cat_conform_to_noescape_prot)
DIAG_NAME_INDEX(note_cc1_round_trip_generated)
DIAG_NAME_INDEX(note_cc1_round_trip_original)
DIAG_NAME_INDEX(note_change_bitfield_sign)
DIAG_NAME_INDEX(note_change_calling_conv_fixit)
DIAG_NAME_INDEX(note_checking_constraints_for_class_spec_id_here)
DIAG_NAME_INDEX(note_checking_constraints_for_function_here)
DIAG_NAME_INDEX(note_checking_constraints_for_template_id_here)
DIAG_NAME_INDEX(note_checking_constraints_for_var_spec_id_here)
DIAG_NAME_INDEX(note_class_declared)
DIAG_NAME_INDEX(note_cocoa_naming_declare_family)
DIAG_NAME_INDEX(note_collapse_loop_count)
DIAG_NAME_INDEX(note_comparison_synthesized_at)
DIAG_NAME_INDEX(note_compat_assoc)
DIAG_NAME_INDEX(note_compound_token_split_second_token_here)
DIAG_NAME_INDEX(note_concatenated_string_literal_silence)
DIAG_NAME_INDEX(note_concept_specialization_constraint_evaluated_to_false)
DIAG_NAME_INDEX(note_concept_specialization_here)
DIAG_NAME_INDEX(note_condition_assign_silence)
DIAG_NAME_INDEX(note_condition_assign_to_comparison)
DIAG_NAME_INDEX(note_condition_or_assign_to_comparison)
DIAG_NAME_INDEX(note_conflicting_attribute)
DIAG_NAME_INDEX(note_conflicting_prototype)
DIAG_NAME_INDEX(note_conflicting_try_here)
DIAG_NAME_INDEX(note_consteval_address_accessible)
DIAG_NAME_INDEX(note_constexpr_access_deleted_object)
DIAG_NAME_INDEX(note_constexpr_access_inactive_union_member)
DIAG_NAME_INDEX(note_constexpr_access_mutable)
DIAG_NAME_INDEX(note_constexpr_access_null)
DIAG_NAME_INDEX(note_constexpr_access_past_end)
DIAG_NAME_INDEX(note_constexpr_access_static_temporary)
DIAG_NAME_INDEX(note_constexpr_access_uninit)
DIAG_NAME_INDEX(note_constexpr_access_unreadable_object)
DIAG_NAME_INDEX(note_constexpr_access_unsized_array)
DIAG_NAME_INDEX(note_constexpr_access_volatile_obj)
DIAG_NAME_INDEX(note_constexpr_access_volatile_type)
DIAG_NAME_INDEX(note_constexpr_alignment_adjust)
DIAG_NAME_INDEX(note_constexpr_alignment_compute)
DIAG_NAME_INDEX(note_constexpr_alignment_too_big)
DIAG_NAME_INDEX(note_constexpr_array_index)
DIAG_NAME_INDEX(note_constexpr_baa_insufficient_alignment)
DIAG_NAME_INDEX(note_constexpr_baa_value_insufficient_alignment)
DIAG_NAME_INDEX(note_constexpr_bit_cast_indet_dest)
DIAG_NAME_INDEX(note_constexpr_bit_cast_invalid_subtype)
DIAG_NAME_INDEX(note_constexpr_bit_cast_invalid_type)
DIAG_NAME_INDEX(note_constexpr_bit_cast_unrepresentable_value)
DIAG_NAME_INDEX(note_constexpr_bit_cast_unsupported_bitfield)
DIAG_NAME_INDEX(note_constexpr_bit_cast_unsupported_type)
DIAG_NAME_INDEX(note_constexpr_body_previous_return)
DIAG_NAME_INDEX(note_constexpr_call_here)
DIAG_NAME_INDEX(note_constexpr_call_limit_exceeded)
DIAG_NAME_INDEX(note_constexpr_calls_suppressed)
DIAG_NAME_INDEX(note_constexpr_compare_virtual_mem_ptr)
DIAG_NAME_INDEX(note_constexpr_conditional_never_const)
DIAG_NAME_INDEX(note_constexpr_construct_complex_elem)
DIAG_NAME_INDEX(note_constexpr_ctor_missing_init)
DIAG_NAME_INDEX(note_constexpr_deallocate_null)
DIAG_NAME_INDEX(note_constexpr_delete_base_nonvirt_dtor)
DIAG_NAME_INDEX(note_constexpr_delete_not_heap_alloc)
DIAG_NAME_INDEX(note_constexpr_delete_subobject)
DIAG_NAME_INDEX(note_constexpr_depth_limit_exceeded)
DIAG_NAME_INDEX(note_constexpr_destroy_complex_elem)
DIAG_NAME_INDEX(note_constexpr_destroy_out_of_lifetime)
DIAG_NAME_INDEX(note_constexpr_double_delete)
DIAG_NAME_INDEX(note_constexpr_double_destroy)
DIAG_NAME_INDEX(note_constexpr_dtor_subobject)
DIAG_NAME_INDEX(note_constexpr_dynamic_alloc)
DIAG_NAME_INDEX(note_constexpr_dynamic_alloc_here)
DIAG_NAME_INDEX(note_constexpr_dynamic_cast_to_reference_failed)
DIAG_NAME_INDEX(note_constexpr_dynamic_rounding)
DIAG_NAME_INDEX(note_constexpr_float_arithmetic)
DIAG_NAME_INDEX(note_constexpr_float_arithmetic_strict)
DIAG_NAME_INDEX(note_constexpr_function_param_value_unknown)
DIAG_NAME_INDEX(note_constexpr_heap_alloc_limit_exceeded)
DIAG_NAME_INDEX(note_constexpr_inherited_ctor_call_here)
DIAG_NAME_INDEX(note_constexpr_invalid_alignment)
DIAG_NAME_INDEX(note_constexpr_invalid_cast)
DIAG_NAME_INDEX(note_constexpr_invalid_downcast)
DIAG_NAME_INDEX(note_constexpr_invalid_function)
DIAG_NAME_INDEX(note_constexpr_invalid_inhctor)
DIAG_NAME_INDEX(note_constexpr_invalid_template_arg)
DIAG_NAME_INDEX(note_constexpr_invalid_void_star_cast)
DIAG_NAME_INDEX(note_constexpr_large_shift)
DIAG_NAME_INDEX(note_constexpr_lifetime_ended)
DIAG_NAME_INDEX(note_constexpr_literal_comparison)
DIAG_NAME_INDEX(note_constexpr_lshift_discards)
DIAG_NAME_INDEX(note_constexpr_lshift_of_negative)
DIAG_NAME_INDEX(note_constexpr_ltor_incomplete_type)
DIAG_NAME_INDEX(note_constexpr_ltor_non_const_int)
DIAG_NAME_INDEX(note_constexpr_ltor_non_constexpr)
DIAG_NAME_INDEX(note_constexpr_ltor_non_integral)
DIAG_NAME_INDEX(note_constexpr_mem_pointer_weak_comparison)
DIAG_NAME_INDEX(note_constexpr_memchr_unsupported)
DIAG_NAME_INDEX(note_constexpr_memcmp_unsupported)
DIAG_NAME_INDEX(note_constexpr_memcpy_incomplete_type)
DIAG_NAME_INDEX(note_constexpr_memcpy_nontrivial)
DIAG_NAME_INDEX(note_constexpr_memcpy_null)
DIAG_NAME_INDEX(note_constexpr_memcpy_overlap)
DIAG_NAME_INDEX(note_constexpr_memcpy_type_pun)
DIAG_NAME_INDEX(note_constexpr_memcpy_unsupported)
DIAG_NAME_INDEX(note_constexpr_memory_leak)
DIAG_NAME_INDEX(note_constexpr_modify_const_type)
DIAG_NAME_INDEX(note_constexpr_modify_global)
DIAG_NAME_INDEX(note_constexpr_negative_shift)
DIAG_NAME_INDEX(note_constexpr_new)
DIAG_NAME_INDEX(note_constexpr_new_delete_mismatch)
DIAG_NAME_INDEX(note_constexpr_new_negative)
DIAG_NAME_INDEX(note_constexpr_new_non_replaceable)
DIAG_NAME_INDEX(note_constexpr_new_not_complete_object_type)
DIAG_NAME_INDEX(note_constexpr_new_placement)
DIAG_NAME_INDEX(note_constexpr_new_too_large)
DIAG_NAME_INDEX(note_constexpr_new_too_small)
DIAG_NAME_INDEX(note_constexpr_new_untyped)
DIAG_NAME_INDEX(note_constexpr_no_return)
DIAG_NAME_INDEX(note_constexpr_non_global)
DIAG_NAME_INDEX(note_constexpr_nonliteral)
DIAG_NAME_INDEX(note_constexpr_not_static)
DIAG_NAME_INDEX(note_constexpr_null_callee)
DIAG_NAME_INDEX(note_constexpr_null_subobject)
DIAG_NAME_INDEX(note_constexpr_operator_new_bad_size)
DIAG_NAME_INDEX(note_constexpr_overflow)
DIAG_NAME_INDEX(note_constexpr_past_end)
DIAG_NAME_INDEX(note_constexpr_past_end_subobject)
DIAG_NAME_INDEX(note_constexpr_placement_new_wrong_type)
DIAG_NAME_INDEX(note_constexpr_pointer_comparison_base_classes)
DIAG_NAME_INDEX(note_constexpr_pointer_comparison_base_field)
DIAG_NAME_INDEX(note_constexpr_pointer_comparison_differing_access)
DIAG_NAME_INDEX(note_constexpr_pointer_comparison_past_end)
DIAG_NAME_INDEX(note_constexpr_pointer_comparison_unspecified)
DIAG_NAME_INDEX(note_constexpr_pointer_comparison_zero_sized)
DIAG_NAME_INDEX(note_constexpr_pointer_constant_comparison)
DIAG_NAME_INDEX(note_constexpr_pointer_subtraction_not_same_array)
DIAG_NAME_INDEX(note_constexpr_pointer_subtraction_zero_size)
DIAG_NAME_INDEX(note_constexpr_pointer_weak_comparison)
DIAG_NAME_INDEX(note_constexpr_polymorphic_unknown_dynamic_type)
DIAG_NAME_INDEX(note_constexpr_pseudo_destructor)
DIAG_NAME_INDEX(note_constexpr_pure_virtual_call)
DIAG_NAME_INDEX(note_constexpr_static_local)
DIAG_NAME_INDEX(note_constexpr_step_limit_exceeded)
DIAG_NAME_INDEX(note_constexpr_stmt_expr_unsupported)
DIAG_NAME_INDEX(note_constexpr_subobject_declared_here)
DIAG_NAME_INDEX(note_constexpr_temporary_here)
DIAG_NAME_INDEX(note_constexpr_this)
DIAG_NAME_INDEX(note_constexpr_typeid_polymorphic)
DIAG_NAME_INDEX(note_constexpr_uninitialized)
DIAG_NAME_INDEX(note_constexpr_uninitialized_base)
DIAG_NAME_INDEX(note_constexpr_union_member_change_during_init)
DIAG_NAME_INDEX(note_constexpr_unsized_array_indexed)
DIAG_NAME_INDEX(note_constexpr_unsupported_destruction)
DIAG_NAME_INDEX(note_constexpr_unsupported_flexible_array)
DIAG_NAME_INDEX(note_constexpr_unsupported_layout)
DIAG_NAME_INDEX(note_constexpr_unsupported_temporary_nontrivial_dtor)
DIAG_NAME_INDEX(note_constexpr_unsupported_unsized_array)
DIAG_NAME_INDEX(note_constexpr_use_uninit_reference)
DIAG_NAME_INDEX(note_constexpr_var_init_non_constant)
DIAG_NAME_INDEX(note_constexpr_var_init_unknown)
DIAG_NAME_INDEX(note_constexpr_var_init_weak)
DIAG_NAME_INDEX(note_constexpr_virtual_base)
DIAG_NAME_INDEX(note_constexpr_virtual_base_here)
DIAG_NAME_INDEX(note_constexpr_virtual_call)
DIAG_NAME_INDEX(note_constexpr_void_comparison)
DIAG_NAME_INDEX(note_constexpr_volatile_here)
DIAG_NAME_INDEX(note_constinit_missing_here)
DIAG_NAME_INDEX(note_constinit_specified_here)
DIAG_NAME_INDEX(note_constraint_normalization_here)
DIAG_NAME_INDEX(note_constraint_references_error)
DIAG_NAME_INDEX(note_constraint_substitution_here)
DIAG_NAME_INDEX(note_conv_function_declared_at)
DIAG_NAME_INDEX(note_convert_inline_to_static)
DIAG_NAME_INDEX(note_coroutine_function_declare_noexcept)
DIAG_NAME_INDEX(note_coroutine_promise_call_implicitly_required)
DIAG_NAME_INDEX(note_coroutine_promise_implicit_await_transform_required_here)
DIAG_NAME_INDEX(note_coroutine_promise_suspend_implicitly_required)
DIAG_NAME_INDEX(note_covariant_thunk)
DIAG_NAME_INDEX(note_cuda_conflicting_device_function_declared_here)
DIAG_NAME_INDEX(note_cuda_const_var_unpromoted)
DIAG_NAME_INDEX(note_cuda_device_builtin_surftex_cls_should_have_match_arg)
DIAG_NAME_INDEX(note_cuda_device_builtin_surftex_cls_should_have_n_args)
DIAG_NAME_INDEX(note_cuda_device_builtin_surftex_should_be_template_class)
DIAG_NAME_INDEX(note_cuda_host_var)
DIAG_NAME_INDEX(note_cuda_ovl_candidate_target_mismatch)
DIAG_NAME_INDEX(note_cxx20_compat_utf8_string_remove_u8)
DIAG_NAME_INDEX(note_decl_hiding_tag_type)
DIAG_NAME_INDEX(note_decl_unguarded_availability_silence)
DIAG_NAME_INDEX(note_declaration_not_a_prototype)
DIAG_NAME_INDEX(note_declare_parameter_strong)
DIAG_NAME_INDEX(note_declared_at)
DIAG_NAME_INDEX(note_declared_coroutine_here)
DIAG_NAME_INDEX(note_declared_nonnull)
DIAG_NAME_INDEX(note_declared_required_constant_init_here)
DIAG_NAME_INDEX(note_deduced_template_arg_substitution_here)
DIAG_NAME_INDEX(note_deduction_guide_access)
DIAG_NAME_INDEX(note_deduction_guide_template_access)
DIAG_NAME_INDEX(note_default_arg_instantiation_here)
DIAG_NAME_INDEX(note_default_argument_declared_here)
DIAG_NAME_INDEX(note_default_function_arg_instantiation_here)
DIAG_NAME_INDEX(note_default_member_initializer_not_yet_parsed)
DIAG_NAME_INDEX(note_defaulted_comparison_ambiguous)
DIAG_NAME_INDEX(note_defaulted_comparison_calls_deleted)
DIAG_NAME_INDEX(note_defaulted_comparison_cannot_deduce)
DIAG_NAME_INDEX(note_defaulted_comparison_cannot_deduce_callee)
DIAG_NAME_INDEX(note_defaulted_comparison_cannot_deduce_undeduced_auto)
DIAG_NAME_INDEX(note_defaulted_comparison_inaccessible)
DIAG_NAME_INDEX(note_defaulted_comparison_no_viable_function)
DIAG_NAME_INDEX(note_defaulted_comparison_no_viable_function_synthesized)
DIAG_NAME_INDEX(note_defaulted_comparison_not_constexpr)
DIAG_NAME_INDEX(note_defaulted_comparison_not_constexpr_here)
DIAG_NAME_INDEX(note_defaulted_comparison_not_rewritten_callee)
DIAG_NAME_INDEX(note_defaulted_comparison_not_rewritten_conversion)
DIAG_NAME_INDEX(note_defaulted_comparison_reference_member)
DIAG_NAME_INDEX(note_defaulted_comparison_union)
DIAG_NAME_INDEX(note_defined_here)
DIAG_NAME_INDEX(note_delete_conversion)
DIAG_NAME_INDEX(note_delete_non_virtual)
DIAG_NAME_INDEX(note_deleted_assign_field)
DIAG_NAME_INDEX(note_deleted_copy_ctor_rvalue_reference)
DIAG_NAME_INDEX(note_deleted_copy_user_declared_move)
DIAG_NAME_INDEX(note_deleted_default_ctor_all_const)
DIAG_NAME_INDEX(note_deleted_default_ctor_uninit_field)
DIAG_NAME_INDEX(note_deleted_dtor_no_operator_delete)
DIAG_NAME_INDEX(note_deleted_special_member_class_subobject)
DIAG_NAME_INDEX(note_deleted_type_mismatch)
DIAG_NAME_INDEX(note_dependent_function_template_spec_discard_reason)
DIAG_NAME_INDEX(note_dependent_member_use)
DIAG_NAME_INDEX(note_dependent_non_type_default_arg_in_partial_spec)
DIAG_NAME_INDEX(note_deprecated_this_capture)
DIAG_NAME_INDEX(note_designated_init_mixed)
DIAG_NAME_INDEX(note_destructor_nontype_here)
DIAG_NAME_INDEX(note_destructor_type_here)
DIAG_NAME_INDEX(note_direct_member_here)
DIAG_NAME_INDEX(note_direct_method_declared_at)
DIAG_NAME_INDEX(note_doc_block_command_previous)
DIAG_NAME_INDEX(note_doc_block_command_previous_alias)
DIAG_NAME_INDEX(note_doc_html_end_tag)
DIAG_NAME_INDEX(note_doc_html_tag_started_here)
DIAG_NAME_INDEX(note_doc_param_name_suggestion)
DIAG_NAME_INDEX(note_doc_param_previous)
DIAG_NAME_INDEX(note_doc_tparam_name_suggestion)
DIAG_NAME_INDEX(note_doc_tparam_previous)
DIAG_NAME_INDEX(note_drv_address_sanitizer_debug_runtime)
DIAG_NAME_INDEX(note_drv_available_multilibs)
DIAG_NAME_INDEX(note_drv_command_failed_diag_msg)
DIAG_NAME_INDEX(note_drv_config_file_searched_in)
DIAG_NAME_INDEX(note_drv_t_option_is_global)
DIAG_NAME_INDEX(note_drv_use_standard)
DIAG_NAME_INDEX(note_drv_verify_prefix_spelling)
DIAG_NAME_INDEX(note_due_to_dllexported_class)
DIAG_NAME_INDEX(note_duplicate_asm_operand_name)
DIAG_NAME_INDEX(note_duplicate_case_prev)
DIAG_NAME_INDEX(note_duplicate_element)
DIAG_NAME_INDEX(note_empty_body_on_separate_line)
DIAG_NAME_INDEX(note_empty_parens_default_ctor)
DIAG_NAME_INDEX(note_empty_parens_function_call)
DIAG_NAME_INDEX(note_empty_parens_zero_initialize)
DIAG_NAME_INDEX(note_empty_using_if_exists_here)
DIAG_NAME_INDEX(note_enforce_read_only_placement)
DIAG_NAME_INDEX(note_enters_block_captures_cxx_obj)
DIAG_NAME_INDEX(note_enters_block_captures_non_trivial_c_struct)
DIAG_NAME_INDEX(note_enters_block_captures_strong)
DIAG_NAME_INDEX(note_enters_block_captures_weak)
DIAG_NAME_INDEX(note_enters_compound_literal_scope)
DIAG_NAME_INDEX(note_enters_statement_expression)
DIAG_NAME_INDEX(note_entity_declared_at)
DIAG_NAME_INDEX(note_enum_specialized_here)
DIAG_NAME_INDEX(note_equality_comparison_silence)
DIAG_NAME_INDEX(note_equality_comparison_to_assign)
DIAG_NAME_INDEX(note_equivalent_internal_linkage_decl)
DIAG_NAME_INDEX(note_evaluate_comparison_first)
DIAG_NAME_INDEX(note_evaluating_exception_spec_here)
DIAG_NAME_INDEX(note_exception_spec_deprecated)
DIAG_NAME_INDEX(note_exits___block)
DIAG_NAME_INDEX(note_exits_block_captures_cxx_obj)
DIAG_NAME_INDEX(note_exits_block_captures_non_trivial_c_struct)
DIAG_NAME_INDEX(note_exits_block_captures_strong)
DIAG_NAME_INDEX(note_exits_block_captures_weak)
DIAG_NAME_INDEX(note_exits_cleanup)
DIAG_NAME_INDEX(note_exits_compound_literal_scope)
DIAG_NAME_INDEX(note_exits_cxx_catch)
DIAG_NAME_INDEX(note_exits_cxx_try)
DIAG_NAME_INDEX(note_exits_dtor)
DIAG_NAME_INDEX(note_exits_objc_autoreleasepool)
DIAG_NAME_INDEX(note_exits_objc_catch)
DIAG_NAME_INDEX(note_exits_objc_finally)
DIAG_NAME_INDEX(note_exits_objc_strong)
DIAG_NAME_INDEX(note_exits_objc_synchronized)
DIAG_NAME_INDEX(note_exits_objc_try)
DIAG_NAME_INDEX(note_exits_objc_weak)
DIAG_NAME_INDEX(note_exits_seh_except)
DIAG_NAME_INDEX(note_exits_seh_finally)
DIAG_NAME_INDEX(note_exits_seh_try)
DIAG_NAME_INDEX(note_exits_temporary_dtor)
DIAG_NAME_INDEX(note_explicit_ctor_deduction_guide_here)
DIAG_NAME_INDEX(note_explicit_instantiation_candidate)
DIAG_NAME_INDEX(note_explicit_instantiation_definition_here)
DIAG_NAME_INDEX(note_explicit_instantiation_here)
DIAG_NAME_INDEX(note_explicit_template_arg_substitution_here)
DIAG_NAME_INDEX(note_explicit_template_spec_does_not_need_header)
DIAG_NAME_INDEX(note_export)
DIAG_NAME_INDEX(note_expr_divide_by_zero)
DIAG_NAME_INDEX(note_expr_evaluates_to)
DIAG_NAME_INDEX(note_expr_requirement_constraints_not_satisfied)
DIAG_NAME_INDEX(note_expr_requirement_constraints_not_satisfied_simple)
DIAG_NAME_INDEX(note_expr_requirement_expr_substitution_error)
DIAG_NAME_INDEX(note_expr_requirement_expr_unknown_substitution_error)
DIAG_NAME_INDEX(note_expr_requirement_noexcept_not_met)
DIAG_NAME_INDEX(note_expr_requirement_type_requirement_substitution_error)
DIAG_NAME_INDEX(note_expr_requirement_type_requirement_unknown_substitution_error)
DIAG_NAME_INDEX(note_extern_c_begins_here)
DIAG_NAME_INDEX(note_extern_c_global_conflict)
DIAG_NAME_INDEX(note_extra_comma_message_arg)
DIAG_NAME_INDEX(note_fallthrough_insert_semi_fixit)
DIAG_NAME_INDEX(note_fe_backend_frame_larger_than)
DIAG_NAME_INDEX(note_fe_backend_invalid_loc)
DIAG_NAME_INDEX(note_fe_backend_plugin)
DIAG_NAME_INDEX(note_fe_backend_resource_limit)
DIAG_NAME_INDEX(note_fe_inline_asm)
DIAG_NAME_INDEX(note_fe_inline_asm_here)
DIAG_NAME_INDEX(note_fe_linking_module)
DIAG_NAME_INDEX(note_fe_source_mgr)
DIAG_NAME_INDEX(note_field_designator_found)
DIAG_NAME_INDEX(note_file_misc_sloc_usage)
DIAG_NAME_INDEX(note_file_sloc_usage)
DIAG_NAME_INDEX(note_final_dtor_non_final_class_silence)
DIAG_NAME_INDEX(note_final_overrider)
DIAG_NAME_INDEX(note_first_module_difference)
DIAG_NAME_INDEX(note_fixit_applied)
DIAG_NAME_INDEX(note_fixit_failed)
DIAG_NAME_INDEX(note_fixit_in_macro)
DIAG_NAME_INDEX(note_fixit_unfixed_error)
DIAG_NAME_INDEX(note_flexible_array_member)
DIAG_NAME_INDEX(note_for_range_begin_end)
DIAG_NAME_INDEX(note_for_range_invalid_iterator)
DIAG_NAME_INDEX(note_for_range_member_begin_end_ignored)
DIAG_NAME_INDEX(note_force_empty_selector_name)
DIAG_NAME_INDEX(note_format_fix_specifier)
DIAG_NAME_INDEX(note_format_security_fixit)
DIAG_NAME_INDEX(note_format_string_defined)
DIAG_NAME_INDEX(note_forward_class)
DIAG_NAME_INDEX(note_forward_class_conversion)
DIAG_NAME_INDEX(note_forward_declaration)
DIAG_NAME_INDEX(note_forward_template_decl)
DIAG_NAME_INDEX(note_found_mutex_near_match)
DIAG_NAME_INDEX(note_from_diagnose_if)
DIAG_NAME_INDEX(note_function_style_cast_add_parentheses)
DIAG_NAME_INDEX(note_function_suggestion)
DIAG_NAME_INDEX(note_function_template_deduction_instantiation_here)
DIAG_NAME_INDEX(note_function_template_spec_here)
DIAG_NAME_INDEX(note_function_template_spec_matched)
DIAG_NAME_INDEX(note_function_to_function_call)
DIAG_NAME_INDEX(note_function_warning_silence)
DIAG_NAME_INDEX(note_getter_unavailable)
DIAG_NAME_INDEX(note_global_module_introducer_missing)
DIAG_NAME_INDEX(note_goto_ms_asm_label)
DIAG_NAME_INDEX(note_guarded_by_declared_here)
DIAG_NAME_INDEX(note_header_guard)
DIAG_NAME_INDEX(note_hidden_overloaded_virtual_declared_here)
DIAG_NAME_INDEX(note_hidden_tag)
DIAG_NAME_INDEX(note_hiding_object)
DIAG_NAME_INDEX(note_ice_conversion_here)
DIAG_NAME_INDEX(note_illegal_field_declared_here)
DIAG_NAME_INDEX(note_immediate_function_reason)
DIAG_NAME_INDEX(note_implementation_declared)
DIAG_NAME_INDEX(note_implemented_by_class)
DIAG_NAME_INDEX(note_implicit_delete_this_in_destructor_here)
DIAG_NAME_INDEX(note_implicit_member_target_infer_collision)
DIAG_NAME_INDEX(note_implicit_param_decl)
DIAG_NAME_INDEX(note_implicit_top_level_module_import_here)
DIAG_NAME_INDEX(note_implicitly_deleted)
DIAG_NAME_INDEX(note_imported_by_pch_module_not_found)
DIAG_NAME_INDEX(note_in_binding_decl_init)
DIAG_NAME_INDEX(note_in_class_initializer_float_type_cxx11)
DIAG_NAME_INDEX(note_in_declaration_of_implicit_equality_comparison)
DIAG_NAME_INDEX(note_in_declaration_of_implicit_special_member)
DIAG_NAME_INDEX(note_in_for_range)
DIAG_NAME_INDEX(note_in_omitted_aggregate_initializer)
DIAG_NAME_INDEX(note_in_reference_temporary_list_initializer)
DIAG_NAME_INDEX(note_include_header_or_declare)
DIAG_NAME_INDEX(note_incompatible_analyzer_plugin_api)
DIAG_NAME_INDEX(note_incomplete_class_and_qualified_id)
DIAG_NAME_INDEX(note_indirect_goto_target)
DIAG_NAME_INDEX(note_indirection_through_null)
DIAG_NAME_INDEX(note_inequality_comparison_to_or_assign)
DIAG_NAME_INDEX(note_init_list_at_beginning_of_macro_argument)
DIAG_NAME_INDEX(note_init_list_narrowing_silence)
DIAG_NAME_INDEX(note_init_with_default_member_initializer)
DIAG_NAME_INDEX(note_initializer_out_of_order)
DIAG_NAME_INDEX(note_insert_break_fixit)
DIAG_NAME_INDEX(note_insert_fallthrough_fixit)
DIAG_NAME_INDEX(note_inst_declaration_hint)
DIAG_NAME_INDEX(note_instantiation_contexts_suppressed)
DIAG_NAME_INDEX(note_instantiation_required_here)
DIAG_NAME_INDEX(note_invalid_consteval_initializer)
DIAG_NAME_INDEX(note_invalid_consteval_initializer_here)
DIAG_NAME_INDEX(note_invalid_subexpr_in_const_expr)
DIAG_NAME_INDEX(note_invalid_ucn_name_candidate)
DIAG_NAME_INDEX(note_invalid_ucn_name_loose_matching)
DIAG_NAME_INDEX(note_it_delegates_to)
DIAG_NAME_INDEX(note_ivar_decl)
DIAG_NAME_INDEX(note_lambda_capture_initializer)
DIAG_NAME_INDEX(note_lambda_decl)
DIAG_NAME_INDEX(note_lambda_default_capture_fixit)
DIAG_NAME_INDEX(note_lambda_substitution_here)
DIAG_NAME_INDEX(note_lambda_this_capture_fixit)
DIAG_NAME_INDEX(note_lambda_to_block_conv)
DIAG_NAME_INDEX(note_lambda_variable_capture_fixit)
DIAG_NAME_INDEX(note_lifetime_extending_member_declared_here)
DIAG_NAME_INDEX(note_local_decl_close_match)
DIAG_NAME_INDEX(note_local_decl_close_param_match)
DIAG_NAME_INDEX(note_local_var_initializer)
DIAG_NAME_INDEX(note_lock_exclusive_and_shared)
DIAG_NAME_INDEX(note_locked_here)
DIAG_NAME_INDEX(note_logical_instead_of_bitwise_change_operator)
DIAG_NAME_INDEX(note_logical_instead_of_bitwise_remove_constant)
DIAG_NAME_INDEX(note_logical_not_fix)
DIAG_NAME_INDEX(note_logical_not_silence_with_parens)
DIAG_NAME_INDEX(note_loop_iteration_here)
DIAG_NAME_INDEX(note_macro_expansion_here)
DIAG_NAME_INDEX(note_macro_here)
DIAG_NAME_INDEX(note_main_change_return_type)
DIAG_NAME_INDEX(note_main_remove_noreturn)
DIAG_NAME_INDEX(note_matching)
DIAG_NAME_INDEX(note_max_tokens_total_override)
DIAG_NAME_INDEX(note_meant_to_use_typename)
DIAG_NAME_INDEX(note_member_declared_at)
DIAG_NAME_INDEX(note_member_declared_here)
DIAG_NAME_INDEX(note_member_def_close_const_match)
DIAG_NAME_INDEX(note_member_def_close_match)
DIAG_NAME_INDEX(note_member_def_close_param_match)
DIAG_NAME_INDEX(note_member_first_declared_here)
DIAG_NAME_INDEX(note_member_reference_arrow_from_operator_arrow)
DIAG_NAME_INDEX(note_member_synthesized_at)
DIAG_NAME_INDEX(note_memsize_comparison_cast_silence)
DIAG_NAME_INDEX(note_memsize_comparison_paren)
DIAG_NAME_INDEX(note_method_declared_at)
DIAG_NAME_INDEX(note_method_return_type_change)
DIAG_NAME_INDEX(note_method_sent_forward_class)
DIAG_NAME_INDEX(note_misaligned_member_used_here)
DIAG_NAME_INDEX(note_misplaced_ellipsis_vararg_add_comma)
DIAG_NAME_INDEX(note_misplaced_ellipsis_vararg_add_ellipsis)
DIAG_NAME_INDEX(note_misplaced_ellipsis_vararg_existing_ellipsis)
DIAG_NAME_INDEX(note_missing_end_of_definition_before)
DIAG_NAME_INDEX(note_missing_selector_name)
DIAG_NAME_INDEX(note_mmap_add_framework_keyword)
DIAG_NAME_INDEX(note_mmap_lbrace_match)
DIAG_NAME_INDEX(note_mmap_lsquare_match)
DIAG_NAME_INDEX(note_mmap_prev_definition)
DIAG_NAME_INDEX(note_mmap_rename_top_level_private_module)
DIAG_NAME_INDEX(note_module_cache_path)
DIAG_NAME_INDEX(note_module_def_undef_here)
DIAG_NAME_INDEX(note_module_file_conflict)
DIAG_NAME_INDEX(note_module_file_imported_by)
DIAG_NAME_INDEX(note_module_import_here)
DIAG_NAME_INDEX(note_module_import_not_at_top_level)
DIAG_NAME_INDEX(note_module_odr_violation_definition_data)
DIAG_NAME_INDEX(note_module_odr_violation_different_definitions)
DIAG_NAME_INDEX(note_module_odr_violation_enum)
DIAG_NAME_INDEX(note_module_odr_violation_field)
DIAG_NAME_INDEX(note_module_odr_violation_function)
DIAG_NAME_INDEX(note_module_odr_violation_method_params)
DIAG_NAME_INDEX(note_module_odr_violation_mismatch_decl)
DIAG_NAME_INDEX(note_module_odr_violation_mismatch_decl_unknown)
DIAG_NAME_INDEX(note_module_odr_violation_no_possible_decls)
DIAG_NAME_INDEX(note_module_odr_violation_objc_interface)
DIAG_NAME_INDEX(note_module_odr_violation_objc_method)
DIAG_NAME_INDEX(note_module_odr_violation_objc_property)
DIAG_NAME_INDEX(note_module_odr_violation_possible_decl)
DIAG_NAME_INDEX(note_module_odr_violation_record)
DIAG_NAME_INDEX(note_module_odr_violation_referenced_protocols)
DIAG_NAME_INDEX(note_module_odr_violation_template_parameter)
DIAG_NAME_INDEX(note_module_odr_violation_typedef)
DIAG_NAME_INDEX(note_module_odr_violation_variable)
DIAG_NAME_INDEX(note_mt_message)
DIAG_NAME_INDEX(note_multiversioning_caused_here)
DIAG_NAME_INDEX(note_musttail_callconv_mismatch)
DIAG_NAME_INDEX(note_musttail_callee_defined_here)
DIAG_NAME_INDEX(note_musttail_fix_non_prototype)
DIAG_NAME_INDEX(note_musttail_mismatch)
DIAG_NAME_INDEX(note_musttail_structors_forbidden)
DIAG_NAME_INDEX(note_namespace_defined_here)
DIAG_NAME_INDEX(note_neon_vector_initializer_non_portable)
DIAG_NAME_INDEX(note_neon_vector_initializer_non_portable_q)
DIAG_NAME_INDEX(note_nested_requirement_here)
DIAG_NAME_INDEX(note_nested_requirement_substitution_error)
DIAG_NAME_INDEX(note_nested_requirement_unknown_substitution_error)
DIAG_NAME_INDEX(note_next_field_declaration)
DIAG_NAME_INDEX(note_next_ivar_declaration)
DIAG_NAME_INDEX(note_non_c_like_anon_struct)
DIAG_NAME_INDEX(note_non_deducible_parameter)
DIAG_NAME_INDEX(note_non_instantiated_member_here)
DIAG_NAME_INDEX(note_non_literal_base_class)
DIAG_NAME_INDEX(note_non_literal_field)
DIAG_NAME_INDEX(note_non_literal_incomplete)
DIAG_NAME_INDEX(note_non_literal_lambda)
DIAG_NAME_INDEX(note_non_literal_no_constexpr_ctors)
DIAG_NAME_INDEX(note_non_literal_non_constexpr_dtor)
DIAG_NAME_INDEX(note_non_literal_nontrivial_dtor)
DIAG_NAME_INDEX(note_non_literal_user_provided_dtor)
DIAG_NAME_INDEX(note_non_literal_virtual_base)
DIAG_NAME_INDEX(note_non_null_attribute_failed)
DIAG_NAME_INDEX(note_non_template_in_template_id_found)
DIAG_NAME_INDEX(note_non_trivial_c_union)
DIAG_NAME_INDEX(note_non_usual_function_declared_here)
DIAG_NAME_INDEX(note_nontemplate_decl_here)
DIAG_NAME_INDEX(note_nontrivial_default_arg)
DIAG_NAME_INDEX(note_nontrivial_default_member_init)
DIAG_NAME_INDEX(note_nontrivial_field)
DIAG_NAME_INDEX(note_nontrivial_has_virtual)
DIAG_NAME_INDEX(note_nontrivial_no_copy)
DIAG_NAME_INDEX(note_nontrivial_no_def_ctor)
DIAG_NAME_INDEX(note_nontrivial_objc_ownership)
DIAG_NAME_INDEX(note_nontrivial_param_type)
DIAG_NAME_INDEX(note_nontrivial_subobject)
DIAG_NAME_INDEX(note_nontrivial_user_provided)
DIAG_NAME_INDEX(note_nontrivial_variadic)
DIAG_NAME_INDEX(note_nontrivial_virtual_dtor)
DIAG_NAME_INDEX(note_not_found_by_two_phase_lookup)
DIAG_NAME_INDEX(note_not_module_interface_add_export)
DIAG_NAME_INDEX(note_not_structural_mutable_field)
DIAG_NAME_INDEX(note_not_structural_non_public)
DIAG_NAME_INDEX(note_not_structural_rvalue_ref_field)
DIAG_NAME_INDEX(note_not_structural_subobject)
DIAG_NAME_INDEX(note_nsdictionary_duplicate_key_here)
DIAG_NAME_INDEX(note_nullability_fix_it)
DIAG_NAME_INDEX(note_nullability_here)
DIAG_NAME_INDEX(note_nullability_type_specifier)
DIAG_NAME_INDEX(note_objc_circular_container_declared_here)
DIAG_NAME_INDEX(note_objc_container_start)
DIAG_NAME_INDEX(note_objc_designated_init_marked_here)
DIAG_NAME_INDEX(note_objc_literal_comparison_isequal)
DIAG_NAME_INDEX(note_objc_literal_method_param)
DIAG_NAME_INDEX(note_objc_literal_method_return)
DIAG_NAME_INDEX(note_objc_needs_superclass)
DIAG_NAME_INDEX(note_objc_type_param_here)
DIAG_NAME_INDEX(note_objc_unsafe_perform_selector_method_declared_here)
DIAG_NAME_INDEX(note_odr_base)
DIAG_NAME_INDEX(note_odr_enumerator)
DIAG_NAME_INDEX(note_odr_field)
DIAG_NAME_INDEX(note_odr_field_name)
DIAG_NAME_INDEX(note_odr_friend)
DIAG_NAME_INDEX(note_odr_missing_base)
DIAG_NAME_INDEX(note_odr_missing_enumerator)
DIAG_NAME_INDEX(note_odr_missing_field)
DIAG_NAME_INDEX(note_odr_missing_friend)
DIAG_NAME_INDEX(note_odr_number_of_bases)
DIAG_NAME_INDEX(note_odr_objc_method_here)
DIAG_NAME_INDEX(note_odr_objc_missing_superclass)
DIAG_NAME_INDEX(note_odr_objc_property_impl_kind)
DIAG_NAME_INDEX(note_odr_objc_superclass)
DIAG_NAME_INDEX(note_odr_objc_synthesize_ivar_here)
DIAG_NAME_INDEX(note_odr_parameter_pack_non_pack)
DIAG_NAME_INDEX(note_odr_tag_kind_here)
DIAG_NAME_INDEX(note_odr_template_parameter_here)
DIAG_NAME_INDEX(note_odr_template_parameter_list)
DIAG_NAME_INDEX(note_odr_value_here)
DIAG_NAME_INDEX(note_odr_virtual_base)
DIAG_NAME_INDEX(note_omp_assumption_clause_continue_here)
DIAG_NAME_INDEX(note_omp_atomic_capture)
DIAG_NAME_INDEX(note_omp_atomic_compare)
DIAG_NAME_INDEX(note_omp_atomic_read_write)
DIAG_NAME_INDEX(note_omp_atomic_update)
DIAG_NAME_INDEX(note_omp_collapse_ordered_expr)
DIAG_NAME_INDEX(note_omp_conversion_here)
DIAG_NAME_INDEX(note_omp_critical_hint_here)
DIAG_NAME_INDEX(note_omp_critical_no_hint)
DIAG_NAME_INDEX(note_omp_ctx_compatible_set_and_selector_for_property)
DIAG_NAME_INDEX(note_omp_ctx_compatible_set_for_selector)
DIAG_NAME_INDEX(note_omp_declare_variant_ctx_continue_here)
DIAG_NAME_INDEX(note_omp_declare_variant_ctx_is_a)
DIAG_NAME_INDEX(note_omp_declare_variant_ctx_options)
DIAG_NAME_INDEX(note_omp_declare_variant_ctx_try)
DIAG_NAME_INDEX(note_omp_declare_variant_ctx_used_here)
DIAG_NAME_INDEX(note_omp_default_dsa_none)
DIAG_NAME_INDEX(note_omp_defaultmap_attr_none)
DIAG_NAME_INDEX(note_omp_directive_here)
DIAG_NAME_INDEX(note_omp_exits_structured_block)
DIAG_NAME_INDEX(note_omp_explicit_dsa)
DIAG_NAME_INDEX(note_omp_flush_order_clause_here)
DIAG_NAME_INDEX(note_omp_implicit_dsa)
DIAG_NAME_INDEX(note_omp_invalid_length_on_this_ptr_mapping)
DIAG_NAME_INDEX(note_omp_invalid_lower_bound_on_this_ptr_mapping)
DIAG_NAME_INDEX(note_omp_invalid_subscript_on_this_ptr_map)
DIAG_NAME_INDEX(note_omp_loop_cond_requres_compatible_incr)
DIAG_NAME_INDEX(note_omp_marked_declare_variant_here)
DIAG_NAME_INDEX(note_omp_marked_device_type_here)
DIAG_NAME_INDEX(note_omp_nested_statement_here)
DIAG_NAME_INDEX(note_omp_nested_teams_construct_here)
DIAG_NAME_INDEX(note_omp_nowait_clause_here)
DIAG_NAME_INDEX(note_omp_ordered_param)
DIAG_NAME_INDEX(note_omp_predefined_allocator)
DIAG_NAME_INDEX(note_omp_predetermined_dsa)
DIAG_NAME_INDEX(note_omp_previous_allocator)
DIAG_NAME_INDEX(note_omp_previous_clause)
DIAG_NAME_INDEX(note_omp_previous_critical_region)
DIAG_NAME_INDEX(note_omp_previous_directive)
DIAG_NAME_INDEX(note_omp_previous_inscan_reduction)
DIAG_NAME_INDEX(note_omp_previous_mem_order_clause)
DIAG_NAME_INDEX(note_omp_previous_named_if_clause)
DIAG_NAME_INDEX(note_omp_previous_reduction_identifier)
DIAG_NAME_INDEX(note_omp_protected_structured_block)
DIAG_NAME_INDEX(note_omp_referenced)
DIAG_NAME_INDEX(note_omp_requires_encountered_directive)
DIAG_NAME_INDEX(note_omp_requires_previous_clause)
DIAG_NAME_INDEX(note_omp_task_predetermined_firstprivate_here)
DIAG_NAME_INDEX(note_opencl_typedef_access_qualifier)
DIAG_NAME_INDEX(note_operator_arrow_depth)
DIAG_NAME_INDEX(note_operator_arrow_here)
DIAG_NAME_INDEX(note_operator_arrows_suppressed)
DIAG_NAME_INDEX(note_overridden_marked_noescape)
DIAG_NAME_INDEX(note_overridden_method)
DIAG_NAME_INDEX(note_overridden_virtual_function)
DIAG_NAME_INDEX(note_ovl_ambiguous_eqeq_reversed_self_non_const)
DIAG_NAME_INDEX(note_ovl_ambiguous_oper_binary_reversed_candidate)
DIAG_NAME_INDEX(note_ovl_ambiguous_oper_binary_reversed_self)
DIAG_NAME_INDEX(note_ovl_ambiguous_oper_binary_selected_candidate)
DIAG_NAME_INDEX(note_ovl_builtin_candidate)
DIAG_NAME_INDEX(note_ovl_candidate)
DIAG_NAME_INDEX(note_ovl_candidate_arity)
DIAG_NAME_INDEX(note_ovl_candidate_arity_one)
DIAG_NAME_INDEX(note_ovl_candidate_bad_addrspace)
DIAG_NAME_INDEX(note_ovl_candidate_bad_addrspace_this)
DIAG_NAME_INDEX(note_ovl_candidate_bad_arc_conv)
DIAG_NAME_INDEX(note_ovl_candidate_bad_base_to_derived_conv)
DIAG_NAME_INDEX(note_ovl_candidate_bad_conv)
DIAG_NAME_INDEX(note_ovl_candidate_bad_conv_incomplete)
DIAG_NAME_INDEX(note_ovl_candidate_bad_cvr)
DIAG_NAME_INDEX(note_ovl_candidate_bad_cvr_this)
DIAG_NAME_INDEX(note_ovl_candidate_bad_deduction)
DIAG_NAME_INDEX(note_ovl_candidate_bad_gc)
DIAG_NAME_INDEX(note_ovl_candidate_bad_list_argument)
DIAG_NAME_INDEX(note_ovl_candidate_bad_overload)
DIAG_NAME_INDEX(note_ovl_candidate_bad_ownership)
DIAG_NAME_INDEX(note_ovl_candidate_bad_target)
DIAG_NAME_INDEX(note_ovl_candidate_bad_value_category)
DIAG_NAME_INDEX(note_ovl_candidate_constraints_not_satisfied)
DIAG_NAME_INDEX(note_ovl_candidate_deduced_mismatch)
DIAG_NAME_INDEX(note_ovl_candidate_deleted)
DIAG_NAME_INDEX(note_ovl_candidate_disabled_by_enable_if)
DIAG_NAME_INDEX(note_ovl_candidate_disabled_by_function_cond_attr)
DIAG_NAME_INDEX(note_ovl_candidate_disabled_by_requirement)
DIAG_NAME_INDEX(note_ovl_candidate_explicit)
DIAG_NAME_INDEX(note_ovl_candidate_explicit_arg_mismatch_named)
DIAG_NAME_INDEX(note_ovl_candidate_explicit_arg_mismatch_unnamed)
DIAG_NAME_INDEX(note_ovl_candidate_has_pass_object_size_params)
DIAG_NAME_INDEX(note_ovl_candidate_illegal_constructor)
DIAG_NAME_INDEX(note_ovl_candidate_illegal_constructor_adrspace_mismatch)
DIAG_NAME_INDEX(note_ovl_candidate_incomplete_deduction)
DIAG_NAME_INDEX(note_ovl_candidate_incomplete_deduction_pack)
DIAG_NAME_INDEX(note_ovl_candidate_inconsistent_deduction)
DIAG_NAME_INDEX(note_ovl_candidate_inconsistent_deduction_types)
DIAG_NAME_INDEX(note_ovl_candidate_inherited_constructor)
DIAG_NAME_INDEX(note_ovl_candidate_inherited_constructor_slice)
DIAG_NAME_INDEX(note_ovl_candidate_instantiation_depth)
DIAG_NAME_INDEX(note_ovl_candidate_non_deduced_mismatch)
DIAG_NAME_INDEX(note_ovl_candidate_non_deduced_mismatch_qualified)
DIAG_NAME_INDEX(note_ovl_candidate_substitution_failure)
DIAG_NAME_INDEX(note_ovl_candidate_underqualified)
DIAG_NAME_INDEX(note_ovl_candidate_unsatisfied_constraints)
DIAG_NAME_INDEX(note_ovl_surrogate_cand)
DIAG_NAME_INDEX(note_ovl_surrogate_constraints_not_satisfied)
DIAG_NAME_INDEX(note_ovl_too_many_candidates)
DIAG_NAME_INDEX(note_ownership_returns_index_mismatch)
DIAG_NAME_INDEX(note_parameter_here)
DIAG_NAME_INDEX(note_parameter_mapping_substitution_here)
DIAG_NAME_INDEX(note_parameter_named_here)
DIAG_NAME_INDEX(note_parameter_pack_here)
DIAG_NAME_INDEX(note_parameter_type)
DIAG_NAME_INDEX(note_partial_availability_specified_here)
DIAG_NAME_INDEX(note_partial_spec_match)
DIAG_NAME_INDEX(note_partial_spec_not_more_specialized_than_primary)
DIAG_NAME_INDEX(note_pch_rebuild_required)
DIAG_NAME_INDEX(note_pch_required_by)
DIAG_NAME_INDEX(note_performs_forbidden_arc_conversion)
DIAG_NAME_INDEX(note_pointer_declared_here)
DIAG_NAME_INDEX(note_possibility)
DIAG_NAME_INDEX(note_possible_target_of_call)
DIAG_NAME_INDEX(note_pp_ambiguous_macro_chosen)
DIAG_NAME_INDEX(note_pp_ambiguous_macro_other)
DIAG_NAME_INDEX(note_pp_framework_without_header)
DIAG_NAME_INDEX(note_pp_macro_annotation)
DIAG_NAME_INDEX(note_pp_module_begin_here)
DIAG_NAME_INDEX(note_pragma_attribute_applied_decl_here)
DIAG_NAME_INDEX(note_pragma_attribute_namespace_on_attribute)
DIAG_NAME_INDEX(note_pragma_attribute_region_ends_here)
DIAG_NAME_INDEX(note_pragma_attribute_use_attribute_kw)
DIAG_NAME_INDEX(note_pragma_entered_here)
DIAG_NAME_INDEX(note_pragma_loop_invalid_vectorize_option)
DIAG_NAME_INDEX(note_pragma_pack_here)
DIAG_NAME_INDEX(note_pragma_pack_pop_instead_reset)
DIAG_NAME_INDEX(note_precedence_bitwise_first)
DIAG_NAME_INDEX(note_precedence_conditional_first)
DIAG_NAME_INDEX(note_precedence_silence)
DIAG_NAME_INDEX(note_prev_module_declaration)
DIAG_NAME_INDEX(note_prev_module_definition)
DIAG_NAME_INDEX(note_prev_module_definition_from_ast_file)
DIAG_NAME_INDEX(note_prev_partial_spec_here)
DIAG_NAME_INDEX(note_previous_access_declaration)
DIAG_NAME_INDEX(note_previous_attribute)
DIAG_NAME_INDEX(note_previous_builtin_declaration)
DIAG_NAME_INDEX(note_previous_decl)
DIAG_NAME_INDEX(note_previous_declaration)
DIAG_NAME_INDEX(note_previous_declaration_as)
DIAG_NAME_INDEX(note_previous_default_assoc)
DIAG_NAME_INDEX(note_previous_definition)
DIAG_NAME_INDEX(note_previous_exception_handler)
DIAG_NAME_INDEX(note_previous_explicit_instantiation)
DIAG_NAME_INDEX(note_previous_field_init)
DIAG_NAME_INDEX(note_previous_implicit_declaration)
DIAG_NAME_INDEX(note_previous_initializer)
DIAG_NAME_INDEX(note_previous_ms_inheritance)
DIAG_NAME_INDEX(note_previous_namespace_alias)
DIAG_NAME_INDEX(note_previous_statement)
DIAG_NAME_INDEX(note_previous_template_specialization)
DIAG_NAME_INDEX(note_previous_use)
DIAG_NAME_INDEX(note_previous_uuid)
DIAG_NAME_INDEX(note_printf_c_str)
DIAG_NAME_INDEX(note_prior_template_arg_substitution)
DIAG_NAME_INDEX(note_private_extern)
DIAG_NAME_INDEX(note_private_module_fragment)
DIAG_NAME_INDEX(note_private_top_level_defined)
DIAG_NAME_INDEX(note_property_attribute)
DIAG_NAME_INDEX(note_property_declare)
DIAG_NAME_INDEX(note_property_synthesize)
DIAG_NAME_INDEX(note_protected_by___block)
DIAG_NAME_INDEX(note_protected_by_cleanup)
DIAG_NAME_INDEX(note_protected_by_consteval_if)
DIAG_NAME_INDEX(note_protected_by_constexpr_if)
DIAG_NAME_INDEX(note_protected_by_cxx_catch)
DIAG_NAME_INDEX(note_protected_by_cxx_try)
DIAG_NAME_INDEX(note_protected_by_if_available)
DIAG_NAME_INDEX(note_protected_by_non_trivial_c_struct_init)
DIAG_NAME_INDEX(note_protected_by_objc_autoreleasepool)
DIAG_NAME_INDEX(note_protected_by_objc_catch)
DIAG_NAME_INDEX(note_protected_by_objc_fast_enumeration)
DIAG_NAME_INDEX(note_protected_by_objc_finally)
DIAG_NAME_INDEX(note_protected_by_objc_strong_init)
DIAG_NAME_INDEX(note_protected_by_objc_synchronized)
DIAG_NAME_INDEX(note_protected_by_objc_try)
DIAG_NAME_INDEX(note_protected_by_objc_weak_init)
DIAG_NAME_INDEX(note_protected_by_seh_except)
DIAG_NAME_INDEX(note_protected_by_seh_finally)
DIAG_NAME_INDEX(note_protected_by_seh_try)
DIAG_NAME_INDEX(note_protected_by_variable_init)
DIAG_NAME_INDEX(note_protected_by_variable_non_pod)
DIAG_NAME_INDEX(note_protected_by_variable_nontriv_destructor)
DIAG_NAME_INDEX(note_protected_by_vla)
DIAG_NAME_INDEX(note_protected_by_vla_type_alias)
DIAG_NAME_INDEX(note_protected_by_vla_typedef)
DIAG_NAME_INDEX(note_protocol_decl)
DIAG_NAME_INDEX(note_protocol_decl_undefined)
DIAG_NAME_INDEX(note_protocol_method)
DIAG_NAME_INDEX(note_protocol_property_declare)
DIAG_NAME_INDEX(note_pure_qualified_call_kext)
DIAG_NAME_INDEX(note_pure_virtual_function)
DIAG_NAME_INDEX(note_raii_guard_add_name)
DIAG_NAME_INDEX(note_receiver_class_declared)
DIAG_NAME_INDEX(note_receiver_expr_here)
DIAG_NAME_INDEX(note_receiver_is_id)
DIAG_NAME_INDEX(note_recursive_default_argument_used_here)
DIAG_NAME_INDEX(note_redefinition_include_same_file)
DIAG_NAME_INDEX(note_redefinition_modules_same_file)
DIAG_NAME_INDEX(note_ref_or_ptr_member_declared_here)
DIAG_NAME_INDEX(note_refconst_member_not_initialized)
DIAG_NAME_INDEX(note_reference_is_return_value)
DIAG_NAME_INDEX(note_referenced_type_template)
DIAG_NAME_INDEX(note_reinterpret_updowncast_use_static)
DIAG_NAME_INDEX(note_related_result_type_explicit)
DIAG_NAME_INDEX(note_related_result_type_family)
DIAG_NAME_INDEX(note_related_result_type_inferred)
DIAG_NAME_INDEX(note_related_result_type_overridden)
DIAG_NAME_INDEX(note_remove_abs)
DIAG_NAME_INDEX(note_remove_max_call)
DIAG_NAME_INDEX(note_remove_move)
DIAG_NAME_INDEX(note_remove_parens_for_variable_declaration)
DIAG_NAME_INDEX(note_replace_abs_function)
DIAG_NAME_INDEX(note_replace_equals_default_to_delete)
DIAG_NAME_INDEX(note_rewriting_operator_as_spaceship)
DIAG_NAME_INDEX(note_riscv_repeated_interrupt_attribute)
DIAG_NAME_INDEX(note_safe_buffer_usage_suggestions_disabled)
DIAG_NAME_INDEX(note_second_module_difference)
DIAG_NAME_INDEX(note_sentinel_here)
DIAG_NAME_INDEX(note_shadow_field)
DIAG_NAME_INDEX(note_silence_aligned_allocation_unavailable)
DIAG_NAME_INDEX(note_single_arg_concept_specialization_constraint_evaluated_to_false)
DIAG_NAME_INDEX(note_specialized_decl)
DIAG_NAME_INDEX(note_specialized_entity)
DIAG_NAME_INDEX(note_static_for_internal_linkage)
DIAG_NAME_INDEX(note_string_plus_scalar_silence)
DIAG_NAME_INDEX(note_strlcpycat_wrong_size)
DIAG_NAME_INDEX(note_strncat_wrong_size)
DIAG_NAME_INDEX(note_struct_class_suggestion)
DIAG_NAME_INDEX(note_substituted_constraint_expr_is_ill_formed)
DIAG_NAME_INDEX(note_suggest_disabling_all_checkers)
DIAG_NAME_INDEX(note_suggest_parens_for_macro)
DIAG_NAME_INDEX(note_suppress_ctad_maybe_unsupported)
DIAG_NAME_INDEX(note_suppressed_class_declare)
DIAG_NAME_INDEX(note_surrounding_namespace_ends_here)
DIAG_NAME_INDEX(note_surrounding_namespace_starts_here)
DIAG_NAME_INDEX(note_suspicious_bzero_size_silence)
DIAG_NAME_INDEX(note_suspicious_sizeof_memset_silence)
DIAG_NAME_INDEX(note_switch_conversion)
DIAG_NAME_INDEX(note_tail_call_required)
DIAG_NAME_INDEX(note_template_arg_internal_object)
DIAG_NAME_INDEX(note_template_arg_refers_here)
DIAG_NAME_INDEX(note_template_arg_refers_here_func)
DIAG_NAME_INDEX(note_template_class_explicit_specialization_was_here)
DIAG_NAME_INDEX(note_template_class_instantiation_here)
DIAG_NAME_INDEX(note_template_class_instantiation_was_here)
DIAG_NAME_INDEX(note_template_decl_here)
DIAG_NAME_INDEX(note_template_declared_here)
DIAG_NAME_INDEX(note_template_default_arg_checking)
DIAG_NAME_INDEX(note_template_enum_def_here)
DIAG_NAME_INDEX(note_template_exception_spec_instantiation_here)
DIAG_NAME_INDEX(note_template_kw_refers_to_non_template)
DIAG_NAME_INDEX(note_template_member_class_here)
DIAG_NAME_INDEX(note_template_member_function_here)
DIAG_NAME_INDEX(note_template_nontype_parm_different_type)
DIAG_NAME_INDEX(note_template_nontype_parm_prev_declaration)
DIAG_NAME_INDEX(note_template_nsdmi_here)
DIAG_NAME_INDEX(note_template_param_different_kind)
DIAG_NAME_INDEX(note_template_param_here)
DIAG_NAME_INDEX(note_template_param_list_different_arity)
DIAG_NAME_INDEX(note_template_param_prev_default_arg)
DIAG_NAME_INDEX(note_template_param_prev_default_arg_in_other_module)
DIAG_NAME_INDEX(note_template_parameter_pack_here)
DIAG_NAME_INDEX(note_template_parameter_pack_non_pack)
DIAG_NAME_INDEX(note_template_prev_declaration)
DIAG_NAME_INDEX(note_template_recursion_depth)
DIAG_NAME_INDEX(note_template_requirement_instantiation_here)
DIAG_NAME_INDEX(note_template_requirement_params_instantiation_here)
DIAG_NAME_INDEX(note_template_static_data_member_def_here)
DIAG_NAME_INDEX(note_template_type_alias_instantiation_here)
DIAG_NAME_INDEX(note_template_unnamed_type_here)
DIAG_NAME_INDEX(note_template_variable_def_here)
DIAG_NAME_INDEX(note_thread_warning_in_fun)
DIAG_NAME_INDEX(note_throw_in_dtor)
DIAG_NAME_INDEX(note_throw_in_function)
DIAG_NAME_INDEX(note_throw_underaligned_obj)
DIAG_NAME_INDEX(note_total_sloc_usage)
DIAG_NAME_INDEX(note_transparent_union_first_field_size_align)
DIAG_NAME_INDEX(note_type_being_defined)
DIAG_NAME_INDEX(note_type_incomplete)
DIAG_NAME_INDEX(note_type_requirement_substitution_error)
DIAG_NAME_INDEX(note_type_requirement_unknown_substitution_error)
DIAG_NAME_INDEX(note_typecheck_assign_const)
DIAG_NAME_INDEX(note_typecheck_invalid_operands_converted)
DIAG_NAME_INDEX(note_typecheck_member_reference_suggestion)
DIAG_NAME_INDEX(note_typedef_for_linkage_here)
DIAG_NAME_INDEX(note_typename_member_refers_here)
DIAG_NAME_INDEX(note_typename_refers_here)
DIAG_NAME_INDEX(note_ucn_four_not_eight)
DIAG_NAME_INDEX(note_unguarded_available_silence)
DIAG_NAME_INDEX(note_unimplemented_constexpr_lambda_feature_ast)
DIAG_NAME_INDEX(note_uninit_fixit_remove_cond)
DIAG_NAME_INDEX(note_uninit_in_this_constructor)
DIAG_NAME_INDEX(note_uninit_reference_member)
DIAG_NAME_INDEX(note_uninit_var_use)
DIAG_NAME_INDEX(note_unlocked_here)
DIAG_NAME_INDEX(note_unparenthesized_non_primary_expr_in_requires_clause)
DIAG_NAME_INDEX(note_unreachable_entity)
DIAG_NAME_INDEX(note_unreachable_silence)
DIAG_NAME_INDEX(note_unsafe_buffer_operation)
DIAG_NAME_INDEX(note_unsafe_buffer_variable_fixit_group)
DIAG_NAME_INDEX(note_use_dashdash)
DIAG_NAME_INDEX(note_use_ifdef_guards)
DIAG_NAME_INDEX(note_use_non_reference_type)
DIAG_NAME_INDEX(note_use_reference_type)
DIAG_NAME_INDEX(note_use_thread_local)
DIAG_NAME_INDEX(note_use_type_or_non_reference)
DIAG_NAME_INDEX(note_used_here)
DIAG_NAME_INDEX(note_used_in_initialization_here)
DIAG_NAME_INDEX(note_user_declared_ctor)
DIAG_NAME_INDEX(note_using)
DIAG_NAME_INDEX(note_using_decl)
DIAG_NAME_INDEX(note_using_decl_class_member_workaround)
DIAG_NAME_INDEX(note_using_decl_conflict)
DIAG_NAME_INDEX(note_using_decl_target)
DIAG_NAME_INDEX(note_using_enum_decl)
DIAG_NAME_INDEX(note_using_value_decl_missing_typename)
DIAG_NAME_INDEX(note_valid_options)
DIAG_NAME_INDEX(note_value_initialization_here)
DIAG_NAME_INDEX(note_var_declared_here)
DIAG_NAME_INDEX(note_var_explicitly_captured_here)
DIAG_NAME_INDEX(note_var_fixit_add_initialization)
DIAG_NAME_INDEX(note_var_prev_partial_spec_here)
DIAG_NAME_INDEX(note_vbase_moved_here)
DIAG_NAME_INDEX(note_verify_ambiguous_marker)
DIAG_NAME_INDEX(note_vla_unsupported)
DIAG_NAME_INDEX(note_which_delegates_to)
DIAG_NAME_INDEX(note_while_in_implementation)
DIAG_NAME_INDEX(note_widen_bitfield)
DIAG_NAME_INDEX(note_within_field_of_type)
DIAG_NAME_INDEX(note_xor_used_as_pow_silence)
DIAG_NAME_INDEX(null_in_char_or_string)
DIAG_NAME_INDEX(null_in_file)
DIAG_NAME_INDEX(override_keyword_hides_virtual_member_function)
DIAG_NAME_INDEX(override_keyword_only_allowed_on_virtual_member_functions)
DIAG_NAME_INDEX(pp_disabled_macro_expansion)
DIAG_NAME_INDEX(pp_err_elif_after_else)
DIAG_NAME_INDEX(pp_err_elif_without_if)
DIAG_NAME_INDEX(pp_err_else_after_else)
DIAG_NAME_INDEX(pp_err_else_without_if)
DIAG_NAME_INDEX(pp_hash_warning)
DIAG_NAME_INDEX(pp_include_macros_out_of_predefines)
DIAG_NAME_INDEX(pp_include_next_absolute_path)
DIAG_NAME_INDEX(pp_include_next_in_primary)
DIAG_NAME_INDEX(pp_invalid_string_literal)
DIAG_NAME_INDEX(pp_macro_not_used)
DIAG_NAME_INDEX(pp_nonportable_path)
DIAG_NAME_INDEX(pp_nonportable_system_path)
DIAG_NAME_INDEX(pp_out_of_date_dependency)
DIAG_NAME_INDEX(pp_poisoning_existing_macro)
DIAG_NAME_INDEX(pp_pragma_once_in_main_file)
DIAG_NAME_INDEX(pp_pragma_sysheader_in_main_file)
DIAG_NAME_INDEX(remark_cc1_round_trip_generated)
DIAG_NAME_INDEX(remark_fe_backend_optimization_remark)
DIAG_NAME_INDEX(remark_fe_backend_optimization_remark_analysis)
DIAG_NAME_INDEX(remark_fe_backend_optimization_remark_analysis_aliasing)
DIAG_NAME_INDEX(remark_fe_backend_optimization_remark_analysis_fpcommute)
DIAG_NAME_INDEX(remark_fe_backend_optimization_remark_missed)
DIAG_NAME_INDEX(remark_fe_backend_plugin)
DIAG_NAME_INDEX(remark_module_build)
DIAG_NAME_INDEX(remark_module_build_done)
DIAG_NAME_INDEX(remark_module_import)
DIAG_NAME_INDEX(remark_module_lock)
DIAG_NAME_INDEX(remark_module_lock_failure)
DIAG_NAME_INDEX(remark_module_lock_timeout)
DIAG_NAME_INDEX(remark_pp_include_directive_modular_translation)
DIAG_NAME_INDEX(remark_pp_search_path_usage)
DIAG_NAME_INDEX(remark_sanitize_address_insert_extra_padding_accepted)
DIAG_NAME_INDEX(remark_sanitize_address_insert_extra_padding_rejected)
DIAG_NAME_INDEX(remark_sloc_usage)
DIAG_NAME_INDEX(trigraph_converted)
DIAG_NAME_INDEX(trigraph_ends_block_comment)
DIAG_NAME_INDEX(trigraph_ignored)
DIAG_NAME_INDEX(trigraph_ignored_block_comment)
DIAG_NAME_INDEX(warn_O4_is_O3)
DIAG_NAME_INDEX(warn_abs_too_small)
DIAG_NAME_INDEX(warn_abstract_final_class)
DIAG_NAME_INDEX(warn_abstract_vbase_init_ignored)
DIAG_NAME_INDEX(warn_access_decl_deprecated)
DIAG_NAME_INDEX(warn_accessor_property_type_mismatch)
DIAG_NAME_INDEX(warn_acquire_requires_negative_cap)
DIAG_NAME_INDEX(warn_acquired_before)
DIAG_NAME_INDEX(warn_acquired_before_after_cycle)
DIAG_NAME_INDEX(warn_addition_in_bitshift)
DIAG_NAME_INDEX(warn_address_of_reference_bool_conversion)
DIAG_NAME_INDEX(warn_address_of_reference_null_compare)
DIAG_NAME_INDEX(warn_alias_to_weak_alias)
DIAG_NAME_INDEX(warn_alias_with_section)
DIAG_NAME_INDEX(warn_aligned_attr_underaligned)
DIAG_NAME_INDEX(warn_alignment_builtin_useless)
DIAG_NAME_INDEX(warn_alignment_not_power_of_two)
DIAG_NAME_INDEX(warn_alloca)
DIAG_NAME_INDEX(warn_alloca_align_alignof)
DIAG_NAME_INDEX(warn_always_inline_coroutine)
DIAG_NAME_INDEX(warn_ambiguous_suitable_delete_function_found)
DIAG_NAME_INDEX(warn_analyzer_deprecated_option)
DIAG_NAME_INDEX(warn_analyzer_deprecated_option_with_alternative)
DIAG_NAME_INDEX(warn_anyx86_interrupt_regsave)
DIAG_NAME_INDEX(warn_arc_bridge_cast_nonarc)
DIAG_NAME_INDEX(warn_arc_lifetime_result_type)
DIAG_NAME_INDEX(warn_arc_literal_assign)
DIAG_NAME_INDEX(warn_arc_object_memaccess)
DIAG_NAME_INDEX(warn_arc_perform_selector_leaks)
DIAG_NAME_INDEX(warn_arc_possible_repeated_use_of_weak)
DIAG_NAME_INDEX(warn_arc_repeated_use_of_weak)
DIAG_NAME_INDEX(warn_arc_retain_cycle)
DIAG_NAME_INDEX(warn_arc_retained_assign)
DIAG_NAME_INDEX(warn_arc_retained_property_assign)
DIAG_NAME_INDEX(warn_arc_strong_pointer_objc_pointer)
DIAG_NAME_INDEX(warn_arcmt_nsalloc_realloc)
DIAG_NAME_INDEX(warn_argument_invalid_range)
DIAG_NAME_INDEX(warn_argument_undefined_behaviour)
DIAG_NAME_INDEX(warn_arith_conv_enum_float)
DIAG_NAME_INDEX(warn_arith_conv_enum_float_cxx20)
DIAG_NAME_INDEX(warn_arith_conv_mixed_anon_enum_types)
DIAG_NAME_INDEX(warn_arith_conv_mixed_anon_enum_types_cxx20)
DIAG_NAME_INDEX(warn_arith_conv_mixed_enum_types)
DIAG_NAME_INDEX(warn_arith_conv_mixed_enum_types_cxx20)
DIAG_NAME_INDEX(warn_arm_interrupt_calling_convention)
DIAG_NAME_INDEX(warn_array_index_exceeds_bounds)
DIAG_NAME_INDEX(warn_array_index_exceeds_max_addressable_bounds)
DIAG_NAME_INDEX(warn_array_index_precedes_bounds)
DIAG_NAME_INDEX(warn_asm_label_on_auto_decl)
DIAG_NAME_INDEX(warn_asm_mismatched_size_modifier)
DIAG_NAME_INDEX(warn_assume_aligned_too_great)
DIAG_NAME_INDEX(warn_assume_attribute_string_unknown)
DIAG_NAME_INDEX(warn_assume_attribute_string_unknown_suggested)
DIAG_NAME_INDEX(warn_assume_side_effects)
DIAG_NAME_INDEX(warn_at_available_unchecked_use)
DIAG_NAME_INDEX(warn_atimport_in_framework_header)
DIAG_NAME_INDEX(warn_atl_uuid_deprecated)
DIAG_NAME_INDEX(warn_atomic_implicit_seq_cst)
DIAG_NAME_INDEX(warn_atomic_member_access)
DIAG_NAME_INDEX(warn_atomic_op_has_invalid_memory_order)
DIAG_NAME_INDEX(warn_atomic_op_misaligned)
DIAG_NAME_INDEX(warn_atomic_op_oversized)
DIAG_NAME_INDEX(warn_atomic_property_rule)
DIAG_NAME_INDEX(warn_attr_abi_tag_namespace)
DIAG_NAME_INDEX(warn_attr_on_unconsumable_class)
DIAG_NAME_INDEX(warn_attr_swift_name_decl_kind)
DIAG_NAME_INDEX(warn_attr_swift_name_decl_missing_params)
DIAG_NAME_INDEX(warn_attr_swift_name_function)
DIAG_NAME_INDEX(warn_attr_swift_name_getter_parameters)
DIAG_NAME_INDEX(warn_attr_swift_name_invalid_identifier)
DIAG_NAME_INDEX(warn_attr_swift_name_missing_parameters)
DIAG_NAME_INDEX(warn_attr_swift_name_multiple_selfs)
DIAG_NAME_INDEX(warn_attr_swift_name_num_params)
DIAG_NAME_INDEX(warn_attr_swift_name_setter_parameters)
DIAG_NAME_INDEX(warn_attr_swift_name_subscript_getter_newValue)
DIAG_NAME_INDEX(warn_attr_swift_name_subscript_invalid_parameter)
DIAG_NAME_INDEX(warn_attr_swift_name_subscript_setter_multiple_newValues)
DIAG_NAME_INDEX(warn_attr_swift_name_subscript_setter_no_newValue)
DIAG_NAME_INDEX(warn_attribute_address_multiple_identical_qualifiers)
DIAG_NAME_INDEX(warn_attribute_after_definition_ignored)
DIAG_NAME_INDEX(warn_attribute_argument_n_negative)
DIAG_NAME_INDEX(warn_attribute_cmse_entry_static)
DIAG_NAME_INDEX(warn_attribute_dll_instantiated_base_class)
DIAG_NAME_INDEX(warn_attribute_dll_redeclaration)
DIAG_NAME_INDEX(warn_attribute_dllexport_explicit_instantiation_decl)
DIAG_NAME_INDEX(warn_attribute_dllexport_explicit_instantiation_def)
DIAG_NAME_INDEX(warn_attribute_dllimport_static_field_definition)
DIAG_NAME_INDEX(warn_attribute_has_no_effect_on_compile_time_if)
DIAG_NAME_INDEX(warn_attribute_has_no_effect_on_infinite_loop)
DIAG_NAME_INDEX(warn_attribute_iboutlet)
DIAG_NAME_INDEX(warn_attribute_ignored)
DIAG_NAME_INDEX(warn_attribute_ignored_for_field_of_type)
DIAG_NAME_INDEX(warn_attribute_ignored_no_calls_in_stmt)
DIAG_NAME_INDEX(warn_attribute_ignored_non_function_pointer)
DIAG_NAME_INDEX(warn_attribute_ignored_on_inline)
DIAG_NAME_INDEX(warn_attribute_ignored_on_non_definition)
DIAG_NAME_INDEX(warn_attribute_invalid_on_definition)
DIAG_NAME_INDEX(warn_attribute_no_builtin_invalid_builtin_name)
DIAG_NAME_INDEX(warn_attribute_no_decl)
DIAG_NAME_INDEX(warn_attribute_nonnull_no_pointers)
DIAG_NAME_INDEX(warn_attribute_nonnull_parm_no_args)
DIAG_NAME_INDEX(warn_attribute_not_on_decl)
DIAG_NAME_INDEX(warn_attribute_on_function_definition)
DIAG_NAME_INDEX(warn_attribute_packed_for_bitfield)
DIAG_NAME_INDEX(warn_attribute_pointer_or_reference_only)
DIAG_NAME_INDEX(warn_attribute_pointers_only)
DIAG_NAME_INDEX(warn_attribute_precede_definition)
DIAG_NAME_INDEX(warn_attribute_protected_visibility)
DIAG_NAME_INDEX(warn_attribute_return_pointers_only)
DIAG_NAME_INDEX(warn_attribute_return_pointers_refs_only)
DIAG_NAME_INDEX(warn_attribute_section_drectve)
DIAG_NAME_INDEX(warn_attribute_section_on_redeclaration)
DIAG_NAME_INDEX(warn_attribute_sentinel_named_arguments)
DIAG_NAME_INDEX(warn_attribute_sentinel_not_variadic)
DIAG_NAME_INDEX(warn_attribute_type_not_supported)
DIAG_NAME_INDEX(warn_attribute_type_not_supported_global)
DIAG_NAME_INDEX(warn_attribute_unknown_visibility)
DIAG_NAME_INDEX(warn_attribute_void_function_method)
DIAG_NAME_INDEX(warn_attribute_weak_on_field)
DIAG_NAME_INDEX(warn_attribute_weak_on_local)
DIAG_NAME_INDEX(warn_attribute_wrong_decl_type)
DIAG_NAME_INDEX(warn_attribute_wrong_decl_type_str)
DIAG_NAME_INDEX(warn_attributes_likelihood_ifstmt_conflict)
DIAG_NAME_INDEX(warn_auto_implicit_atomic_property)
DIAG_NAME_INDEX(warn_auto_readonly_iboutlet_property)
DIAG_NAME_INDEX(warn_auto_storage_class)
DIAG_NAME_INDEX(warn_auto_synthesizing_protocol_property)
DIAG_NAME_INDEX(warn_auto_var_is_id)
DIAG_NAME_INDEX(warn_autosynthesis_property_in_superclass)
DIAG_NAME_INDEX(warn_autosynthesis_property_ivar_match)
DIAG_NAME_INDEX(warn_availability_and_unavailable)
DIAG_NAME_INDEX(warn_availability_fuchsia_unavailable_minor)
DIAG_NAME_INDEX(warn_availability_on_static_initializer)
DIAG_NAME_INDEX(warn_availability_swift_unavailable_deprecated_only)
DIAG_NAME_INDEX(warn_availability_unknown_platform)
DIAG_NAME_INDEX(warn_availability_version_ordering)
DIAG_NAME_INDEX(warn_avx_calling_convention)
DIAG_NAME_INDEX(warn_bad_character_encoding)
DIAG_NAME_INDEX(warn_bad_cxx_cast_nested_pointer_addr_space)
DIAG_NAME_INDEX(warn_bad_function_cast)
DIAG_NAME_INDEX(warn_bad_receiver_type)
DIAG_NAME_INDEX(warn_bad_string_encoding)
DIAG_NAME_INDEX(warn_base_class_is_uninit)
DIAG_NAME_INDEX(warn_bind_ref_member_to_parameter)
DIAG_NAME_INDEX(warn_binding_null_to_reference)
DIAG_NAME_INDEX(warn_bitfield_too_small_for_enum)
DIAG_NAME_INDEX(warn_bitfield_width_exceeds_type_width)
DIAG_NAME_INDEX(warn_bitwise_instead_of_logical)
DIAG_NAME_INDEX(warn_bitwise_negation_bool)
DIAG_NAME_INDEX(warn_bitwise_op_in_bitwise_op)
DIAG_NAME_INDEX(warn_block_capture_autoreleasing)
DIAG_NAME_INDEX(warn_block_literal_attributes_on_omitted_return_type)
DIAG_NAME_INDEX(warn_block_literal_qualifiers_on_omitted_return_type)
DIAG_NAME_INDEX(warn_bool_switch_condition)
DIAG_NAME_INDEX(warn_braces_around_init)
DIAG_NAME_INDEX(warn_break_binds_to_switch)
DIAG_NAME_INDEX(warn_builtin_chk_overflow)
DIAG_NAME_INDEX(warn_builtin_unknown)
DIAG_NAME_INDEX(warn_c17_compat_ellipsis_only_parameter)
DIAG_NAME_INDEX(warn_c17_compat_static_assert_no_message)
DIAG_NAME_INDEX(warn_c2x_compat_bitint_suffix)
DIAG_NAME_INDEX(warn_c2x_compat_digit_separator)
DIAG_NAME_INDEX(warn_c2x_compat_empty_initializer)
DIAG_NAME_INDEX(warn_c2x_compat_keyword)
DIAG_NAME_INDEX(warn_c2x_compat_label_end_of_compound_statement)
DIAG_NAME_INDEX(warn_c2x_compat_literal_ucn_control_character)
DIAG_NAME_INDEX(warn_c2x_compat_literal_ucn_escape_basic_scs)
DIAG_NAME_INDEX(warn_c2x_compat_pp_directive)
DIAG_NAME_INDEX(warn_c2x_compat_warning_directive)
DIAG_NAME_INDEX(warn_c2x_keyword)
DIAG_NAME_INDEX(warn_c99_compat_unicode_id)
DIAG_NAME_INDEX(warn_c99_compat_unicode_literal)
DIAG_NAME_INDEX(warn_c99_keyword)
DIAG_NAME_INDEX(warn_c_kext)
DIAG_NAME_INDEX(warn_call_to_pure_virtual_member_function_from_ctor_dtor)
DIAG_NAME_INDEX(warn_call_wrong_number_of_arguments)
DIAG_NAME_INDEX(warn_called_once_gets_called_twice)
DIAG_NAME_INDEX(warn_called_once_never_called)
DIAG_NAME_INDEX(warn_called_once_never_called_when)
DIAG_NAME_INDEX(warn_cannot_pass_non_pod_arg_to_vararg)
DIAG_NAME_INDEX(warn_cannot_resolve_lock)
DIAG_NAME_INDEX(warn_case_empty_range)
DIAG_NAME_INDEX(warn_case_value_overflow)
DIAG_NAME_INDEX(warn_cast_align)
DIAG_NAME_INDEX(warn_cast_calling_conv)
DIAG_NAME_INDEX(warn_cast_function_type)
DIAG_NAME_INDEX(warn_cast_function_type_strict)
DIAG_NAME_INDEX(warn_cast_nonnull_to_bool)
DIAG_NAME_INDEX(warn_cast_pointer_from_sel)
DIAG_NAME_INDEX(warn_cast_qual)
DIAG_NAME_INDEX(warn_cast_qual2)
DIAG_NAME_INDEX(warn_category_method_impl_match)
DIAG_NAME_INDEX(warn_cconv_knr)
DIAG_NAME_INDEX(warn_cconv_unsupported)
DIAG_NAME_INDEX(warn_cdtor_function_try_handler_mem_expr)
DIAG_NAME_INDEX(warn_cfstring_truncated)
DIAG_NAME_INDEX(warn_char_constant_too_large)
DIAG_NAME_INDEX(warn_class_method_not_found)
DIAG_NAME_INDEX(warn_class_method_not_found_with_typo)
DIAG_NAME_INDEX(warn_clause_expected_string)
DIAG_NAME_INDEX(warn_cleanup_ext)
DIAG_NAME_INDEX(warn_cmse_nonsecure_union)
DIAG_NAME_INDEX(warn_cocoa_naming_owned_rule)
DIAG_NAME_INDEX(warn_collection_expr_type)
DIAG_NAME_INDEX(warn_comma_operator)
DIAG_NAME_INDEX(warn_comparison_always)
DIAG_NAME_INDEX(warn_comparison_bitwise_always)
DIAG_NAME_INDEX(warn_comparison_bitwise_or)
DIAG_NAME_INDEX(warn_comparison_mixed_enum_types)
DIAG_NAME_INDEX(warn_comparison_mixed_enum_types_cxx20)
DIAG_NAME_INDEX(warn_comparison_of_mixed_enum_types_switch)
DIAG_NAME_INDEX(warn_completion_handler_called_twice)
DIAG_NAME_INDEX(warn_completion_handler_never_called)
DIAG_NAME_INDEX(warn_completion_handler_never_called_when)
DIAG_NAME_INDEX(warn_compound_token_split_by_macro)
DIAG_NAME_INDEX(warn_compound_token_split_by_whitespace)
DIAG_NAME_INDEX(warn_concatenated_literal_array_init)
DIAG_NAME_INDEX(warn_concatenated_nsarray_literal)
DIAG_NAME_INDEX(warn_condition_is_assignment)
DIAG_NAME_INDEX(warn_condition_is_idiomatic_assignment)
DIAG_NAME_INDEX(warn_conditional_mixed_enum_types)
DIAG_NAME_INDEX(warn_conditional_mixed_enum_types_cxx20)
DIAG_NAME_INDEX(warn_conflicting_nullability_attr_overriding_param_types)
DIAG_NAME_INDEX(warn_conflicting_nullability_attr_overriding_ret_types)
DIAG_NAME_INDEX(warn_conflicting_overriding_param_modifiers)
DIAG_NAME_INDEX(warn_conflicting_overriding_param_types)
DIAG_NAME_INDEX(warn_conflicting_overriding_ret_type_modifiers)
DIAG_NAME_INDEX(warn_conflicting_overriding_ret_types)
DIAG_NAME_INDEX(warn_conflicting_overriding_variadic)
DIAG_NAME_INDEX(warn_conflicting_param_modifiers)
DIAG_NAME_INDEX(warn_conflicting_param_types)
DIAG_NAME_INDEX(warn_conflicting_ret_type_modifiers)
DIAG_NAME_INDEX(warn_conflicting_ret_types)
DIAG_NAME_INDEX(warn_conflicting_variadic)
DIAG_NAME_INDEX(warn_consteval_if_always_true)
DIAG_NAME_INDEX(warn_constexpr_unscoped_enum_out_of_range)
DIAG_NAME_INDEX(warn_conv_to_base_not_used)
DIAG_NAME_INDEX(warn_conv_to_self_not_used)
DIAG_NAME_INDEX(warn_conv_to_void_not_used)
DIAG_NAME_INDEX(warn_coroutine_handle_address_invalid_return_type)
DIAG_NAME_INDEX(warn_coroutine_promise_unhandled_exception_required_with_exceptions)
DIAG_NAME_INDEX(warn_correct_comment_command_name)
DIAG_NAME_INDEX(warn_cstruct_memaccess)
DIAG_NAME_INDEX(warn_cstyle_param)
DIAG_NAME_INDEX(warn_ctad_maybe_unsupported)
DIAG_NAME_INDEX(warn_ctor_parm_shadows_field)
DIAG_NAME_INDEX(warn_ctu_incompat_triple)
DIAG_NAME_INDEX(warn_cuda_attr_lambda_position)
DIAG_NAME_INDEX(warn_cxx11_compat_binary_literal)
DIAG_NAME_INDEX(warn_cxx11_compat_constexpr_body_invalid_stmt)
DIAG_NAME_INDEX(warn_cxx11_compat_constexpr_body_multiple_return)
DIAG_NAME_INDEX(warn_cxx11_compat_constexpr_body_no_return)
DIAG_NAME_INDEX(warn_cxx11_compat_constexpr_local_var)
DIAG_NAME_INDEX(warn_cxx11_compat_constexpr_type_definition)
DIAG_NAME_INDEX(warn_cxx11_compat_decltype_auto_type_specifier)
DIAG_NAME_INDEX(warn_cxx11_compat_deduced_return_type)
DIAG_NAME_INDEX(warn_cxx11_compat_digit_separator)
DIAG_NAME_INDEX(warn_cxx11_compat_generic_lambda)
DIAG_NAME_INDEX(warn_cxx11_compat_init_capture)
DIAG_NAME_INDEX(warn_cxx11_compat_reserved_user_defined_literal)
DIAG_NAME_INDEX(warn_cxx11_compat_user_defined_literal)
DIAG_NAME_INDEX(warn_cxx11_compat_variable_template)
DIAG_NAME_INDEX(warn_cxx11_gnu_attribute_on_type)
DIAG_NAME_INDEX(warn_cxx11_keyword)
DIAG_NAME_INDEX(warn_cxx11_right_shift_in_template_arg)
DIAG_NAME_INDEX(warn_cxx14_compat_class_template_argument_deduction)
DIAG_NAME_INDEX(warn_cxx14_compat_constexpr_if)
DIAG_NAME_INDEX(warn_cxx14_compat_constexpr_not_const)
DIAG_NAME_INDEX(warn_cxx14_compat_constexpr_on_lambda)
DIAG_NAME_INDEX(warn_cxx14_compat_decomp_decl)
DIAG_NAME_INDEX(warn_cxx14_compat_fold_expression)
DIAG_NAME_INDEX(warn_cxx14_compat_init_statement)
DIAG_NAME_INDEX(warn_cxx14_compat_inline_variable)
DIAG_NAME_INDEX(warn_cxx14_compat_nested_namespace_definition)
DIAG_NAME_INDEX(warn_cxx14_compat_ns_enum_attribute)
DIAG_NAME_INDEX(warn_cxx14_compat_star_this_lambda_capture)
DIAG_NAME_INDEX(warn_cxx14_compat_static_assert_no_message)
DIAG_NAME_INDEX(warn_cxx14_compat_template_nontype_parm_auto_type)
DIAG_NAME_INDEX(warn_cxx14_compat_template_template_param_typename)
DIAG_NAME_INDEX(warn_cxx14_compat_u8_character_literal)
DIAG_NAME_INDEX(warn_cxx14_compat_using_attribute_ns)
DIAG_NAME_INDEX(warn_cxx17_compat_adl_only_template_id)
DIAG_NAME_INDEX(warn_cxx17_compat_aggregate_init_paren_list)
DIAG_NAME_INDEX(warn_cxx17_compat_bitfield_member_init)
DIAG_NAME_INDEX(warn_cxx17_compat_capture_binding)
DIAG_NAME_INDEX(warn_cxx17_compat_constexpr_body_invalid_stmt)
DIAG_NAME_INDEX(warn_cxx17_compat_constexpr_ctor_missing_init)
DIAG_NAME_INDEX(warn_cxx17_compat_constexpr_function_try_block)
DIAG_NAME_INDEX(warn_cxx17_compat_constexpr_local_var_no_init)
DIAG_NAME_INDEX(warn_cxx17_compat_constexpr_union_ctor_no_init)
DIAG_NAME_INDEX(warn_cxx17_compat_constexpr_virtual)
DIAG_NAME_INDEX(warn_cxx17_compat_decomp_decl_spec)
DIAG_NAME_INDEX(warn_cxx17_compat_defaulted_comparison)
DIAG_NAME_INDEX(warn_cxx17_compat_defaulted_method_type_mismatch)
DIAG_NAME_INDEX(warn_cxx17_compat_designated_init)
DIAG_NAME_INDEX(warn_cxx17_compat_equals_this_lambda_capture)
DIAG_NAME_INDEX(warn_cxx17_compat_exception_spec_in_signature)
DIAG_NAME_INDEX(warn_cxx17_compat_explicit_bool)
DIAG_NAME_INDEX(warn_cxx17_compat_for_range_init_stmt)
DIAG_NAME_INDEX(warn_cxx17_compat_implicit_typename)
DIAG_NAME_INDEX(warn_cxx17_compat_init_capture_pack)
DIAG_NAME_INDEX(warn_cxx17_compat_inline_nested_namespace_definition)
DIAG_NAME_INDEX(warn_cxx17_compat_lambda_def_ctor_assign)
DIAG_NAME_INDEX(warn_cxx17_compat_lambda_template_parameter_list)
DIAG_NAME_INDEX(warn_cxx17_compat_missing_varargs_arg)
DIAG_NAME_INDEX(warn_cxx17_compat_multi_using_declaration)
DIAG_NAME_INDEX(warn_cxx17_compat_pointer_to_const_ref_member_on_rvalue)
DIAG_NAME_INDEX(warn_cxx17_compat_spaceship)
DIAG_NAME_INDEX(warn_cxx17_compat_template_nontype_parm_type)
DIAG_NAME_INDEX(warn_cxx17_compat_unicode_type)
DIAG_NAME_INDEX(warn_cxx17_compat_using_decl_class_member_enumerator)
DIAG_NAME_INDEX(warn_cxx17_compat_using_decl_non_member_enumerator)
DIAG_NAME_INDEX(warn_cxx17_compat_using_decl_scoped_enumerator)
DIAG_NAME_INDEX(warn_cxx17_compat_using_declaration_pack)
DIAG_NAME_INDEX(warn_cxx17_compat_using_enum_declaration)
DIAG_NAME_INDEX(warn_cxx17_hex_literal)
DIAG_NAME_INDEX(warn_cxx20_alias_in_init_statement)
DIAG_NAME_INDEX(warn_cxx20_compat_aggregate_init_with_ctors)
DIAG_NAME_INDEX(warn_cxx20_compat_auto_expr)
DIAG_NAME_INDEX(warn_cxx20_compat_consteval)
DIAG_NAME_INDEX(warn_cxx20_compat_consteval_if)
DIAG_NAME_INDEX(warn_cxx20_compat_constexpr_body_invalid_stmt)
DIAG_NAME_INDEX(warn_cxx20_compat_constexpr_var)
DIAG_NAME_INDEX(warn_cxx20_compat_constinit)
DIAG_NAME_INDEX(warn_cxx20_compat_decl_attrs_on_lambda)
DIAG_NAME_INDEX(warn_cxx20_compat_explicit_bool)
DIAG_NAME_INDEX(warn_cxx20_compat_label_end_of_compound_statement)
DIAG_NAME_INDEX(warn_cxx20_compat_operator_overload_static)
DIAG_NAME_INDEX(warn_cxx20_compat_size_t_suffix)
DIAG_NAME_INDEX(warn_cxx20_compat_spaceship)
DIAG_NAME_INDEX(warn_cxx20_compat_static_lambda)
DIAG_NAME_INDEX(warn_cxx20_compat_use_of_unaddressable_function)
DIAG_NAME_INDEX(warn_cxx20_compat_utf8_string)
DIAG_NAME_INDEX(warn_cxx20_keyword)
DIAG_NAME_INDEX(warn_cxx23_compat_defaulted_comparison_constexpr_mismatch)
DIAG_NAME_INDEX(warn_cxx23_compat_pp_directive)
DIAG_NAME_INDEX(warn_cxx23_compat_warning_directive)
DIAG_NAME_INDEX(warn_cxx23_delimited_escape_sequence)
DIAG_NAME_INDEX(warn_cxx98_compat_alias_declaration)
DIAG_NAME_INDEX(warn_cxx98_compat_alignas)
DIAG_NAME_INDEX(warn_cxx98_compat_alignof)
DIAG_NAME_INDEX(warn_cxx98_compat_array_size_conversion)
DIAG_NAME_INDEX(warn_cxx98_compat_attribute)
DIAG_NAME_INDEX(warn_cxx98_compat_auto_type_specifier)
DIAG_NAME_INDEX(warn_cxx98_compat_cast_fn_obj)
DIAG_NAME_INDEX(warn_cxx98_compat_constexpr)
DIAG_NAME_INDEX(warn_cxx98_compat_ctor_list_init)
DIAG_NAME_INDEX(warn_cxx98_compat_decltype)
DIAG_NAME_INDEX(warn_cxx98_compat_defaulted_deleted_function)
DIAG_NAME_INDEX(warn_cxx98_compat_delegating_ctor)
DIAG_NAME_INDEX(warn_cxx98_compat_empty_fnmacro_arg)
DIAG_NAME_INDEX(warn_cxx98_compat_empty_scalar_initializer)
DIAG_NAME_INDEX(warn_cxx98_compat_empty_sizeless_initializer)
DIAG_NAME_INDEX(warn_cxx98_compat_enum_fixed_underlying_type)
DIAG_NAME_INDEX(warn_cxx98_compat_enum_friend)
DIAG_NAME_INDEX(warn_cxx98_compat_enum_nested_name_spec)
DIAG_NAME_INDEX(warn_cxx98_compat_enumerator_list_comma)
DIAG_NAME_INDEX(warn_cxx98_compat_explicit_conversion_functions)
DIAG_NAME_INDEX(warn_cxx98_compat_extern_template)
DIAG_NAME_INDEX(warn_cxx98_compat_for_range)
DIAG_NAME_INDEX(warn_cxx98_compat_friend_is_member)
DIAG_NAME_INDEX(warn_cxx98_compat_generalized_initializer_lists)
DIAG_NAME_INDEX(warn_cxx98_compat_goto_into_protected_scope)
DIAG_NAME_INDEX(warn_cxx98_compat_indirect_goto_in_protected_scope)
DIAG_NAME_INDEX(warn_cxx98_compat_initializer_list_init)
DIAG_NAME_INDEX(warn_cxx98_compat_inline_namespace)
DIAG_NAME_INDEX(warn_cxx98_compat_lambda)
DIAG_NAME_INDEX(warn_cxx98_compat_less_colon_colon)
DIAG_NAME_INDEX(warn_cxx98_compat_literal_operator)
DIAG_NAME_INDEX(warn_cxx98_compat_literal_ucn_control_character)
DIAG_NAME_INDEX(warn_cxx98_compat_literal_ucn_escape_basic_scs)
DIAG_NAME_INDEX(warn_cxx98_compat_longlong)
DIAG_NAME_INDEX(warn_cxx98_compat_no_newline_eof)
DIAG_NAME_INDEX(warn_cxx98_compat_noexcept_decl)
DIAG_NAME_INDEX(warn_cxx98_compat_noexcept_expr)
DIAG_NAME_INDEX(warn_cxx98_compat_non_static_member_use)
DIAG_NAME_INDEX(warn_cxx98_compat_nonclass_type_friend)
DIAG_NAME_INDEX(warn_cxx98_compat_nonstatic_member_init)
DIAG_NAME_INDEX(warn_cxx98_compat_nontrivial_union_or_anon_struct_member)
DIAG_NAME_INDEX(warn_cxx98_compat_nullptr)
DIAG_NAME_INDEX(warn_cxx98_compat_override_control_keyword)
DIAG_NAME_INDEX(warn_cxx98_compat_pass_non_pod_arg_to_vararg)
DIAG_NAME_INDEX(warn_cxx98_compat_pp_line_too_big)
DIAG_NAME_INDEX(warn_cxx98_compat_raw_string_literal)
DIAG_NAME_INDEX(warn_cxx98_compat_ref_qualifier)
DIAG_NAME_INDEX(warn_cxx98_compat_reference_list_init)
DIAG_NAME_INDEX(warn_cxx98_compat_rvalue_reference)
DIAG_NAME_INDEX(warn_cxx98_compat_scoped_enum)
DIAG_NAME_INDEX(warn_cxx98_compat_sfinae_access_control)
DIAG_NAME_INDEX(warn_cxx98_compat_static_assert)
DIAG_NAME_INDEX(warn_cxx98_compat_static_data_member_in_union)
DIAG_NAME_INDEX(warn_cxx98_compat_switch_into_protected_scope)
DIAG_NAME_INDEX(warn_cxx98_compat_temp_copy)
DIAG_NAME_INDEX(warn_cxx98_compat_template_arg_extra_parens)
DIAG_NAME_INDEX(warn_cxx98_compat_template_arg_local_type)
DIAG_NAME_INDEX(warn_cxx98_compat_template_arg_null)
DIAG_NAME_INDEX(warn_cxx98_compat_template_arg_object_internal)
DIAG_NAME_INDEX(warn_cxx98_compat_template_arg_unnamed_type)
DIAG_NAME_INDEX(warn_cxx98_compat_template_outside_of_template)
DIAG_NAME_INDEX(warn_cxx98_compat_template_parameter_default_in_function_template)
DIAG_NAME_INDEX(warn_cxx98_compat_top_level_semi)
DIAG_NAME_INDEX(warn_cxx98_compat_trailing_return_type)
DIAG_NAME_INDEX(warn_cxx98_compat_two_right_angle_brackets)
DIAG_NAME_INDEX(warn_cxx98_compat_typename_outside_of_template)
DIAG_NAME_INDEX(warn_cxx98_compat_unelaborated_friend_type)
DIAG_NAME_INDEX(warn_cxx98_compat_unicode_literal)
DIAG_NAME_INDEX(warn_cxx98_compat_unicode_type)
DIAG_NAME_INDEX(warn_cxx98_compat_using_decl_constructor)
DIAG_NAME_INDEX(warn_cxx98_compat_variadic_macro)
DIAG_NAME_INDEX(warn_cxx98_compat_variadic_templates)
DIAG_NAME_INDEX(warn_cxx_ms_struct)
DIAG_NAME_INDEX(warn_dangling_else)
DIAG_NAME_INDEX(warn_dangling_lifetime_pointer)
DIAG_NAME_INDEX(warn_dangling_lifetime_pointer_member)
DIAG_NAME_INDEX(warn_dangling_member)
DIAG_NAME_INDEX(warn_dangling_variable)
DIAG_NAME_INDEX(warn_dealloc_in_category)
DIAG_NAME_INDEX(warn_debug_compression_unavailable)
DIAG_NAME_INDEX(warn_decl_in_param_list)
DIAG_NAME_INDEX(warn_decl_shadow)
DIAG_NAME_INDEX(warn_decl_shadow_uncaptured_local)
DIAG_NAME_INDEX(warn_declspec_allocator_nonpointer)
DIAG_NAME_INDEX(warn_declspec_attribute_ignored)
DIAG_NAME_INDEX(warn_deep_exception_specs_differ)
DIAG_NAME_INDEX(warn_def_missing_case)
DIAG_NAME_INDEX(warn_default_atomic_custom_getter_setter)
DIAG_NAME_INDEX(warn_defaulted_comparison_deleted)
DIAG_NAME_INDEX(warn_defaulted_method_deleted)
DIAG_NAME_INDEX(warn_defined_in_function_type_macro)
DIAG_NAME_INDEX(warn_defined_in_object_type_macro)
DIAG_NAME_INDEX(warn_delegating_ctor_cycle)
DIAG_NAME_INDEX(warn_delete_abstract_non_virtual_dtor)
DIAG_NAME_INDEX(warn_delete_array_type)
DIAG_NAME_INDEX(warn_delete_incomplete)
DIAG_NAME_INDEX(warn_delete_non_virtual_dtor)
DIAG_NAME_INDEX(warn_delimited_ucn_empty)
DIAG_NAME_INDEX(warn_delimited_ucn_incomplete)
DIAG_NAME_INDEX(warn_depr_array_comparison)
DIAG_NAME_INDEX(warn_deprecated)
DIAG_NAME_INDEX(warn_deprecated_altivec_src_compat)
DIAG_NAME_INDEX(warn_deprecated_anonymous_namespace)
DIAG_NAME_INDEX(warn_deprecated_builtin)
DIAG_NAME_INDEX(warn_deprecated_comma_subscript)
DIAG_NAME_INDEX(warn_deprecated_copy)
DIAG_NAME_INDEX(warn_deprecated_copy_with_dtor)
DIAG_NAME_INDEX(warn_deprecated_copy_with_user_provided_copy)
DIAG_NAME_INDEX(warn_deprecated_copy_with_user_provided_dtor)
DIAG_NAME_INDEX(warn_deprecated_def)
DIAG_NAME_INDEX(warn_deprecated_for_co_await)
DIAG_NAME_INDEX(warn_deprecated_fwdclass_message)
DIAG_NAME_INDEX(warn_deprecated_ignored_on_using)
DIAG_NAME_INDEX(warn_deprecated_increment_decrement_volatile)
DIAG_NAME_INDEX(warn_deprecated_lax_vec_conv_all)
DIAG_NAME_INDEX(warn_deprecated_literal_operator_id)
DIAG_NAME_INDEX(warn_deprecated_message)
DIAG_NAME_INDEX(warn_deprecated_noreturn_spelling)
DIAG_NAME_INDEX(warn_deprecated_redundant_constexpr_static_def)
DIAG_NAME_INDEX(warn_deprecated_register)
DIAG_NAME_INDEX(warn_deprecated_simple_assign_volatile)
DIAG_NAME_INDEX(warn_deprecated_string_literal_conversion)
DIAG_NAME_INDEX(warn_deprecated_this_capture)
DIAG_NAME_INDEX(warn_deprecated_volatile_param)
DIAG_NAME_INDEX(warn_deprecated_volatile_return)
DIAG_NAME_INDEX(warn_deprecated_volatile_structured_binding)
DIAG_NAME_INDEX(warn_dereference_of_noderef_type)
DIAG_NAME_INDEX(warn_dereference_of_noderef_type_no_decl)
DIAG_NAME_INDEX(warn_diagnose_if_succeeded)
DIAG_NAME_INDEX(warn_direct_initialize_call)
DIAG_NAME_INDEX(warn_direct_ivar_access)
DIAG_NAME_INDEX(warn_direct_super_initialize_call)
DIAG_NAME_INDEX(warn_dispatch_body_ignored)
DIAG_NAME_INDEX(warn_division_sizeof_array)
DIAG_NAME_INDEX(warn_division_sizeof_ptr)
DIAG_NAME_INDEX(warn_dllimport_dropped_from_inline_function)
DIAG_NAME_INDEX(warn_doc_api_container_decl_mismatch)
DIAG_NAME_INDEX(warn_doc_block_command_duplicate)
DIAG_NAME_INDEX(warn_doc_block_command_empty_paragraph)
DIAG_NAME_INDEX(warn_doc_container_decl_mismatch)
DIAG_NAME_INDEX(warn_doc_deprecated_not_sync)
DIAG_NAME_INDEX(warn_doc_function_method_decl_mismatch)
DIAG_NAME_INDEX(warn_doc_html_end_forbidden)
DIAG_NAME_INDEX(warn_doc_html_end_unbalanced)
DIAG_NAME_INDEX(warn_doc_html_missing_end_tag)
DIAG_NAME_INDEX(warn_doc_html_start_end_mismatch)
DIAG_NAME_INDEX(warn_doc_html_start_tag_expected_ident_or_greater)
DIAG_NAME_INDEX(warn_doc_html_start_tag_expected_quoted_string)
DIAG_NAME_INDEX(warn_doc_inline_command_not_enough_arguments)
DIAG_NAME_INDEX(warn_doc_param_duplicate)
DIAG_NAME_INDEX(warn_doc_param_invalid_direction)
DIAG_NAME_INDEX(warn_doc_param_not_attached_to_a_function_decl)
DIAG_NAME_INDEX(warn_doc_param_not_found)
DIAG_NAME_INDEX(warn_doc_param_spaces_in_direction)
DIAG_NAME_INDEX(warn_doc_returns_attached_to_a_void_function)
DIAG_NAME_INDEX(warn_doc_returns_not_attached_to_a_function_decl)
DIAG_NAME_INDEX(warn_doc_tparam_duplicate)
DIAG_NAME_INDEX(warn_doc_tparam_not_attached_to_a_template_decl)
DIAG_NAME_INDEX(warn_doc_tparam_not_found)
DIAG_NAME_INDEX(warn_double_const_requires_fp64)
DIAG_NAME_INDEX(warn_double_lock)
DIAG_NAME_INDEX(warn_drv_assuming_mfloat_abi_is)
DIAG_NAME_INDEX(warn_drv_avr_family_linking_stdlibs_not_implemented)
DIAG_NAME_INDEX(warn_drv_avr_libc_not_found)
DIAG_NAME_INDEX(warn_drv_avr_linker_section_addresses_not_implemented)
DIAG_NAME_INDEX(warn_drv_avr_mcu_not_specified)
DIAG_NAME_INDEX(warn_drv_avr_stdlib_not_linked)
DIAG_NAME_INDEX(warn_drv_clang_unsupported)
DIAG_NAME_INDEX(warn_drv_darwin_sdk_invalid_settings)
DIAG_NAME_INDEX(warn_drv_deprecated_arg)
DIAG_NAME_INDEX(warn_drv_diagnostics_hotness_requires_pgo)
DIAG_NAME_INDEX(warn_drv_diagnostics_misexpect_requires_pgo)
DIAG_NAME_INDEX(warn_drv_disabling_vptr_no_rtti_default)
DIAG_NAME_INDEX(warn_drv_dwarf_version_limited_by_target)
DIAG_NAME_INDEX(warn_drv_dxc_missing_dxv)
DIAG_NAME_INDEX(warn_drv_empty_joined_argument)
DIAG_NAME_INDEX(warn_drv_fine_grained_bitfield_accesses_ignored)
DIAG_NAME_INDEX(warn_drv_fjmc_for_elf_only)
DIAG_NAME_INDEX(warn_drv_fuse_ld_path)
DIAG_NAME_INDEX(warn_drv_global_isel_incomplete)
DIAG_NAME_INDEX(warn_drv_global_isel_incomplete_opt)
DIAG_NAME_INDEX(warn_drv_input_file_unused)
DIAG_NAME_INDEX(warn_drv_input_file_unused_by_cpp)
DIAG_NAME_INDEX(warn_drv_invalid_arch_name_with_suggestion)
DIAG_NAME_INDEX(warn_drv_jmc_requires_debuginfo)
DIAG_NAME_INDEX(warn_drv_libstdcxx_not_found)
DIAG_NAME_INDEX(warn_drv_loongarch_conflicting_implied_val)
DIAG_NAME_INDEX(warn_drv_missing_multilib)
DIAG_NAME_INDEX(warn_drv_missing_plugin_arg)
DIAG_NAME_INDEX(warn_drv_missing_plugin_name)
DIAG_NAME_INDEX(warn_drv_moutline_atomics_unsupported_opt)
DIAG_NAME_INDEX(warn_drv_moutline_unsupported_opt)
DIAG_NAME_INDEX(warn_drv_msp430_hwmult_mismatch)
DIAG_NAME_INDEX(warn_drv_msp430_hwmult_no_device)
DIAG_NAME_INDEX(warn_drv_msp430_hwmult_unsupported)
DIAG_NAME_INDEX(warn_drv_msvc_not_found)
DIAG_NAME_INDEX(warn_drv_multi_gpu_arch)
DIAG_NAME_INDEX(warn_drv_needs_hvx)
DIAG_NAME_INDEX(warn_drv_new_cuda_version)
DIAG_NAME_INDEX(warn_drv_no_floating_point_registers)
DIAG_NAME_INDEX(warn_drv_object_size_disabled_O0)
DIAG_NAME_INDEX(warn_drv_omp_offload_target_duplicate)
DIAG_NAME_INDEX(warn_drv_optimization_value)
DIAG_NAME_INDEX(warn_drv_overriding_flag_option)
DIAG_NAME_INDEX(warn_drv_partially_supported_cuda_version)
DIAG_NAME_INDEX(warn_drv_pch_not_first_include)
DIAG_NAME_INDEX(warn_drv_potentially_misspelled_joined_argument)
DIAG_NAME_INDEX(warn_drv_preprocessed_input_file_unused)
DIAG_NAME_INDEX(warn_drv_ps_force_pic)
DIAG_NAME_INDEX(warn_drv_sarif_format_unstable)
DIAG_NAME_INDEX(warn_drv_treating_input_as_cxx)
DIAG_NAME_INDEX(warn_drv_unable_to_find_directory_expected)
DIAG_NAME_INDEX(warn_drv_unknown_argument_clang_cl)
DIAG_NAME_INDEX(warn_drv_unknown_argument_clang_cl_with_suggestion)
DIAG_NAME_INDEX(warn_drv_unsupported_debug_info_opt_for_target)
DIAG_NAME_INDEX(warn_drv_unsupported_diag_option_for_flang)
DIAG_NAME_INDEX(warn_drv_unsupported_float_abi_by_lib)
DIAG_NAME_INDEX(warn_drv_unsupported_gpopt)
DIAG_NAME_INDEX(warn_drv_unsupported_longcalls)
DIAG_NAME_INDEX(warn_drv_unsupported_opt_for_target)
DIAG_NAME_INDEX(warn_drv_unsupported_option_for_flang)
DIAG_NAME_INDEX(warn_drv_unsupported_option_for_offload_arch_req_feature)
DIAG_NAME_INDEX(warn_drv_unsupported_option_for_processor)
DIAG_NAME_INDEX(warn_drv_unsupported_option_for_target)
DIAG_NAME_INDEX(warn_drv_unsupported_pic_with_mabicalls)
DIAG_NAME_INDEX(warn_drv_unsupported_sdata)
DIAG_NAME_INDEX(warn_drv_unused_argument)
DIAG_NAME_INDEX(warn_drv_unused_x)
DIAG_NAME_INDEX(warn_drv_yc_multiple_inputs_clang_cl)
DIAG_NAME_INDEX(warn_drv_ycyu_different_arg_clang_cl)
DIAG_NAME_INDEX(warn_dup_category_def)
DIAG_NAME_INDEX(warn_duplicate_attribute)
DIAG_NAME_INDEX(warn_duplicate_attribute_exact)
DIAG_NAME_INDEX(warn_duplicate_codeseg_attribute)
DIAG_NAME_INDEX(warn_duplicate_declspec)
DIAG_NAME_INDEX(warn_duplicate_enum_values)
DIAG_NAME_INDEX(warn_duplicate_method_decl)
DIAG_NAME_INDEX(warn_duplicate_module_file_extension)
DIAG_NAME_INDEX(warn_duplicate_protocol_def)
DIAG_NAME_INDEX(warn_dyn_class_memaccess)
DIAG_NAME_INDEX(warn_eagerly_load_for_standard_cplusplus_modules)
DIAG_NAME_INDEX(warn_empty_for_body)
DIAG_NAME_INDEX(warn_empty_format_string)
DIAG_NAME_INDEX(warn_empty_if_body)
DIAG_NAME_INDEX(warn_empty_init_statement)
DIAG_NAME_INDEX(warn_empty_parens_are_function_decl)
DIAG_NAME_INDEX(warn_empty_range_based_for_body)
DIAG_NAME_INDEX(warn_empty_switch_body)
DIAG_NAME_INDEX(warn_empty_while_body)
DIAG_NAME_INDEX(warn_enum_constant_in_bool_context)
DIAG_NAME_INDEX(warn_enum_value_overflow)
DIAG_NAME_INDEX(warn_equality_with_extra_parens)
DIAG_NAME_INDEX(warn_exception_caught_by_earlier_handler)
DIAG_NAME_INDEX(warn_exception_spec_deprecated)
DIAG_NAME_INDEX(warn_exit_time_destructor)
DIAG_NAME_INDEX(warn_expected_consistent_version_separator)
DIAG_NAME_INDEX(warn_expected_qualified_after_typename)
DIAG_NAME_INDEX(warn_expecting_lock_held_on_loop)
DIAG_NAME_INDEX(warn_expecting_locked)
DIAG_NAME_INDEX(warn_experimental_header_unit)
DIAG_NAME_INDEX(warn_explicit_instantiation_after_specialization)
DIAG_NAME_INDEX(warn_explicit_instantiation_inline_0x)
DIAG_NAME_INDEX(warn_explicit_instantiation_must_be_global_0x)
DIAG_NAME_INDEX(warn_explicit_instantiation_out_of_scope_0x)
DIAG_NAME_INDEX(warn_explicit_instantiation_unqualified_wrong_namespace_0x)
DIAG_NAME_INDEX(warn_ext_c2x_attributes)
DIAG_NAME_INDEX(warn_ext_cxx11_attributes)
DIAG_NAME_INDEX(warn_ext_int_deprecated)
DIAG_NAME_INDEX(warn_extern_init)
DIAG_NAME_INDEX(warn_extra_semi_after_mem_fn_def)
DIAG_NAME_INDEX(warn_falloff_nonvoid_coroutine)
DIAG_NAME_INDEX(warn_falloff_nonvoid_function)
DIAG_NAME_INDEX(warn_falloff_nonvoid_lambda)
DIAG_NAME_INDEX(warn_falloff_noreturn_function)
DIAG_NAME_INDEX(warn_fe_backend_frame_larger_than)
DIAG_NAME_INDEX(warn_fe_backend_invalid_feature_flag)
DIAG_NAME_INDEX(warn_fe_backend_optimization_failure)
DIAG_NAME_INDEX(warn_fe_backend_plugin)
DIAG_NAME_INDEX(warn_fe_backend_readonly_feature_flag)
DIAG_NAME_INDEX(warn_fe_backend_resource_limit)
DIAG_NAME_INDEX(warn_fe_backend_unsupported)
DIAG_NAME_INDEX(warn_fe_backend_unsupported_fp_exceptions)
DIAG_NAME_INDEX(warn_fe_backend_unsupported_fp_rounding)
DIAG_NAME_INDEX(warn_fe_backend_warning_attr)
DIAG_NAME_INDEX(warn_fe_cc_log_diagnostics_failure)
DIAG_NAME_INDEX(warn_fe_cc_print_header_failure)
DIAG_NAME_INDEX(warn_fe_frame_larger_than)
DIAG_NAME_INDEX(warn_fe_inline_asm)
DIAG_NAME_INDEX(warn_fe_linking_module)
DIAG_NAME_INDEX(warn_fe_macro_contains_embedded_newline)
DIAG_NAME_INDEX(warn_fe_override_module)
DIAG_NAME_INDEX(warn_fe_serialized_diag_failure)
DIAG_NAME_INDEX(warn_fe_serialized_diag_failure_during_finalisation)
DIAG_NAME_INDEX(warn_fe_serialized_diag_merge_failure)
DIAG_NAME_INDEX(warn_fe_source_mgr)
DIAG_NAME_INDEX(warn_fe_unable_to_open_stats_file)
DIAG_NAME_INDEX(warn_field_is_uninit)
DIAG_NAME_INDEX(warn_final_dtor_non_final_class)
DIAG_NAME_INDEX(warn_fixedpoint_constant_overflow)
DIAG_NAME_INDEX(warn_fixit_no_changes)
DIAG_NAME_INDEX(warn_flag_enum_constant_out_of_range)
DIAG_NAME_INDEX(warn_float_compare_literal)
DIAG_NAME_INDEX(warn_float_overflow)
DIAG_NAME_INDEX(warn_float_underflow)
DIAG_NAME_INDEX(warn_floatingpoint_eq)
DIAG_NAME_INDEX(warn_for_range_begin_end_types_differ)
DIAG_NAME_INDEX(warn_for_range_const_ref_binds_temp_built_from_ref)
DIAG_NAME_INDEX(warn_for_range_copy)
DIAG_NAME_INDEX(warn_for_range_ref_binds_ret_temp)
DIAG_NAME_INDEX(warn_format_P_no_precision)
DIAG_NAME_INDEX(warn_format_argument_needs_cast)
DIAG_NAME_INDEX(warn_format_argument_needs_cast_pedantic)
DIAG_NAME_INDEX(warn_format_bool_as_character)
DIAG_NAME_INDEX(warn_format_conversion_argument_type_mismatch)
DIAG_NAME_INDEX(warn_format_conversion_argument_type_mismatch_confusion)
DIAG_NAME_INDEX(warn_format_conversion_argument_type_mismatch_pedantic)
DIAG_NAME_INDEX(warn_format_invalid_annotation)
DIAG_NAME_INDEX(warn_format_invalid_conversion)
DIAG_NAME_INDEX(warn_format_invalid_positional_specifier)
DIAG_NAME_INDEX(warn_format_mix_positional_nonpositional_args)
DIAG_NAME_INDEX(warn_format_non_standard)
DIAG_NAME_INDEX(warn_format_non_standard_conversion_spec)
DIAG_NAME_INDEX(warn_format_non_standard_positional_arg)
DIAG_NAME_INDEX(warn_format_nonliteral)
DIAG_NAME_INDEX(warn_format_nonliteral_noargs)
DIAG_NAME_INDEX(warn_format_nonsensical_length)
DIAG_NAME_INDEX(warn_format_string_is_wide_literal)
DIAG_NAME_INDEX(warn_format_zero_positional_specifier)
DIAG_NAME_INDEX(warn_fortify_scanf_overflow)
DIAG_NAME_INDEX(warn_fortify_source_format_overflow)
DIAG_NAME_INDEX(warn_fortify_source_overflow)
DIAG_NAME_INDEX(warn_fortify_source_size_mismatch)
DIAG_NAME_INDEX(warn_fortify_strlen_overflow)
DIAG_NAME_INDEX(warn_forward_class_redefinition)
DIAG_NAME_INDEX(warn_four_char_character_literal)
DIAG_NAME_INDEX(warn_frame_address)
DIAG_NAME_INDEX(warn_framework_include_private_from_public)
DIAG_NAME_INDEX(warn_free_nonheap_object)
DIAG_NAME_INDEX(warn_fun_excludes_mutex)
DIAG_NAME_INDEX(warn_fun_requires_lock)
DIAG_NAME_INDEX(warn_fun_requires_lock_precise)
DIAG_NAME_INDEX(warn_fun_requires_negative_cap)
DIAG_NAME_INDEX(warn_func_template_missing)
DIAG_NAME_INDEX(warn_function_attribute_ignored_in_stmt)
DIAG_NAME_INDEX(warn_function_def_in_objc_container)
DIAG_NAME_INDEX(warn_function_stmt_attribute_precedence)
DIAG_NAME_INDEX(warn_gc_attribute_weak_on_local)
DIAG_NAME_INDEX(warn_gcc_attribute_location)
DIAG_NAME_INDEX(warn_gcc_ignores_type_attr)
DIAG_NAME_INDEX(warn_gcc_requires_variadic_function)
DIAG_NAME_INDEX(warn_gcc_variable_decl_in_for_loop)
DIAG_NAME_INDEX(warn_global_constructor)
DIAG_NAME_INDEX(warn_global_destructor)
DIAG_NAME_INDEX(warn_gnu_inline_attribute_requires_inline)
DIAG_NAME_INDEX(warn_gnu_inline_cplusplus_without_extern)
DIAG_NAME_INDEX(warn_gnu_null_ptr_arith)
DIAG_NAME_INDEX(warn_guarded_pass_by_reference)
DIAG_NAME_INDEX(warn_has_warning_invalid_option)
DIAG_NAME_INDEX(warn_header_guard)
DIAG_NAME_INDEX(warn_hip_omp_target_directives)
DIAG_NAME_INDEX(warn_iboutlet_object_type)
DIAG_NAME_INDEX(warn_iboutletcollection_property_assign)
DIAG_NAME_INDEX(warn_identity_field_assign)
DIAG_NAME_INDEX(warn_ignored_clang_option)
DIAG_NAME_INDEX(warn_ignored_gcc_optimization)
DIAG_NAME_INDEX(warn_ignored_hip_only_option)
DIAG_NAME_INDEX(warn_ignored_ms_inheritance)
DIAG_NAME_INDEX(warn_ignored_objc_externally_retained)
DIAG_NAME_INDEX(warn_ignoring_fdiscard_for_bitcode)
DIAG_NAME_INDEX(warn_ignoring_ftabstop_value)
DIAG_NAME_INDEX(warn_ignoring_verify_debuginfo_preserve_export)
DIAG_NAME_INDEX(warn_imp_cast_drops_unaligned)
DIAG_NAME_INDEX(warn_impcast_bitfield_precision_constant)
DIAG_NAME_INDEX(warn_impcast_bool_to_null_pointer)
DIAG_NAME_INDEX(warn_impcast_complex_scalar)
DIAG_NAME_INDEX(warn_impcast_constant_value_to_objc_bool)
DIAG_NAME_INDEX(warn_impcast_different_enum_types)
DIAG_NAME_INDEX(warn_impcast_double_promotion)
DIAG_NAME_INDEX(warn_impcast_fixed_point_range)
DIAG_NAME_INDEX(warn_impcast_float_integer)
DIAG_NAME_INDEX(warn_impcast_float_precision)
DIAG_NAME_INDEX(warn_impcast_float_result_precision)
DIAG_NAME_INDEX(warn_impcast_float_to_integer)
DIAG_NAME_INDEX(warn_impcast_float_to_integer_out_of_range)
DIAG_NAME_INDEX(warn_impcast_float_to_integer_zero)
DIAG_NAME_INDEX(warn_impcast_float_to_objc_signed_char_bool)
DIAG_NAME_INDEX(warn_impcast_floating_point_to_bool)
DIAG_NAME_INDEX(warn_impcast_high_order_zero_bits)
DIAG_NAME_INDEX(warn_impcast_int_to_objc_signed_char_bool)
DIAG_NAME_INDEX(warn_impcast_integer_64_32)
DIAG_NAME_INDEX(warn_impcast_integer_float_precision)
DIAG_NAME_INDEX(warn_impcast_integer_float_precision_constant)
DIAG_NAME_INDEX(warn_impcast_integer_precision)
DIAG_NAME_INDEX(warn_impcast_integer_precision_constant)
DIAG_NAME_INDEX(warn_impcast_integer_sign)
DIAG_NAME_INDEX(warn_impcast_integer_sign_conditional)
DIAG_NAME_INDEX(warn_impcast_literal_float_to_integer)
DIAG_NAME_INDEX(warn_impcast_literal_float_to_integer_out_of_range)
DIAG_NAME_INDEX(warn_impcast_nonnegative_result)
DIAG_NAME_INDEX(warn_impcast_null_pointer_to_integer)
DIAG_NAME_INDEX(warn_impcast_objective_c_literal_to_bool)
DIAG_NAME_INDEX(warn_impcast_pointer_to_bool)
DIAG_NAME_INDEX(warn_impcast_single_bit_bitield_precision_constant)
DIAG_NAME_INDEX(warn_impcast_string_literal_to_bool)
DIAG_NAME_INDEX(warn_impcast_vector_scalar)
DIAG_NAME_INDEX(warn_impl_required_for_class_property)
DIAG_NAME_INDEX(warn_impl_required_in_category_for_class_property)
DIAG_NAME_INDEX(warn_implements_nscopying)
DIAG_NAME_INDEX(warn_implicit_atomic_property)
DIAG_NAME_INDEX(warn_implicit_decl_no_jmp_buf)
DIAG_NAME_INDEX(warn_implicit_decl_requires_sysheader)
DIAG_NAME_INDEX(warn_implicit_function_decl)
DIAG_NAME_INDEX(warn_implicitly_retains_self)
DIAG_NAME_INDEX(warn_import_on_definition)
DIAG_NAME_INDEX(warn_inaccessible_base_class)
DIAG_NAME_INDEX(warn_incompatible_analyzer_plugin_api)
DIAG_NAME_INDEX(warn_incompatible_branch_protection_option)
DIAG_NAME_INDEX(warn_incompatible_exception_specs)
DIAG_NAME_INDEX(warn_incompatible_qualified_id)
DIAG_NAME_INDEX(warn_incompatible_sysroot)
DIAG_NAME_INDEX(warn_incompatible_vectors)
DIAG_NAME_INDEX(warn_incomplete_encoded_type)
DIAG_NAME_INDEX(warn_inconsistent_array_form)
DIAG_NAME_INDEX(warn_inconsistent_destructor_marked_not_override_overriding)
DIAG_NAME_INDEX(warn_inconsistent_function_marked_not_override_overriding)
DIAG_NAME_INDEX(warn_increment_bool)
DIAG_NAME_INDEX(warn_independentclass_attribute)
DIAG_NAME_INDEX(warn_indirection_through_null)
DIAG_NAME_INDEX(warn_infinite_recursive_function)
DIAG_NAME_INDEX(warn_init_list_constant_narrowing)
DIAG_NAME_INDEX(warn_init_list_type_narrowing)
DIAG_NAME_INDEX(warn_init_list_variable_narrowing)
DIAG_NAME_INDEX(warn_init_ptr_member_to_parameter_addr)
DIAG_NAME_INDEX(warn_initializer_out_of_order)
DIAG_NAME_INDEX(warn_initializer_overrides)
DIAG_NAME_INDEX(warn_inline_namespace_reopened_noninline)
DIAG_NAME_INDEX(warn_inst_method_not_found)
DIAG_NAME_INDEX(warn_instance_method_not_found_with_typo)
DIAG_NAME_INDEX(warn_instance_method_on_class_found)
DIAG_NAME_INDEX(warn_int_to_pointer_cast)
DIAG_NAME_INDEX(warn_int_to_void_pointer_cast)
DIAG_NAME_INDEX(warn_integer_constant_overflow)
DIAG_NAME_INDEX(warn_integer_constants_in_conditional_always_true)
DIAG_NAME_INDEX(warn_internal_linkage_local_storage)
DIAG_NAME_INDEX(warn_interrupt_attribute_invalid)
DIAG_NAME_INDEX(warn_invalid_asm_cast_lvalue)
DIAG_NAME_INDEX(warn_invalid_initializer_from_system_header)
DIAG_NAME_INDEX(warn_invalid_ios_deployment_target)
DIAG_NAME_INDEX(warn_invalid_utf8_in_comment)
DIAG_NAME_INDEX(warn_is_constant_evaluated_always_true_constexpr)
DIAG_NAME_INDEX(warn_ivar_use_hidden)
DIAG_NAME_INDEX(warn_ivars_in_interface)
DIAG_NAME_INDEX(warn_jump_out_of_seh_finally)
DIAG_NAME_INDEX(warn_kern_is_inline)
DIAG_NAME_INDEX(warn_kern_is_method)
DIAG_NAME_INDEX(warn_left_shift_always)
DIAG_NAME_INDEX(warn_left_shift_in_bool_context)
DIAG_NAME_INDEX(warn_lock_exclusive_and_shared)
DIAG_NAME_INDEX(warn_lock_some_predecessors)
DIAG_NAME_INDEX(warn_logical_and_in_logical_or)
DIAG_NAME_INDEX(warn_logical_instead_of_bitwise)
DIAG_NAME_INDEX(warn_logical_not_on_lhs_of_check)
DIAG_NAME_INDEX(warn_loop_ctrl_binds_to_inner)
DIAG_NAME_INDEX(warn_loop_state_mismatch)
DIAG_NAME_INDEX(warn_main_one_arg)
DIAG_NAME_INDEX(warn_main_redefined)
DIAG_NAME_INDEX(warn_main_returns_bool_literal)
DIAG_NAME_INDEX(warn_max_tokens)
DIAG_NAME_INDEX(warn_max_tokens_total)
DIAG_NAME_INDEX(warn_max_unsigned_zero)
DIAG_NAME_INDEX(warn_maybe_capture_bad_target_this_ptr)
DIAG_NAME_INDEX(warn_maybe_falloff_nonvoid_coroutine)
DIAG_NAME_INDEX(warn_maybe_falloff_nonvoid_function)
DIAG_NAME_INDEX(warn_maybe_falloff_nonvoid_lambda)
DIAG_NAME_INDEX(warn_maybe_uninit_var)
DIAG_NAME_INDEX(warn_maynot_respond)
DIAG_NAME_INDEX(warn_member_extra_qualification)
DIAG_NAME_INDEX(warn_memsize_comparison)
DIAG_NAME_INDEX(warn_messaging_unqualified_id)
DIAG_NAME_INDEX(warn_method_param_declaration)
DIAG_NAME_INDEX(warn_method_param_redefinition)
DIAG_NAME_INDEX(warn_microsoft_dependent_exists)
DIAG_NAME_INDEX(warn_microsoft_qualifiers_ignored)
DIAG_NAME_INDEX(warn_mig_server_routine_does_not_return_kern_return_t)
DIAG_NAME_INDEX(warn_misleading_indentation)
DIAG_NAME_INDEX(warn_mismatched_availability)
DIAG_NAME_INDEX(warn_mismatched_availability_override)
DIAG_NAME_INDEX(warn_mismatched_availability_override_unavail)
DIAG_NAME_INDEX(warn_mismatched_delete_new)
DIAG_NAME_INDEX(warn_mismatched_import)
DIAG_NAME_INDEX(warn_mismatched_nullability_attr)
DIAG_NAME_INDEX(warn_mismatched_section)
DIAG_NAME_INDEX(warn_misplaced_ellipsis_vararg)
DIAG_NAME_INDEX(warn_missing_braces)
DIAG_NAME_INDEX(warn_missing_case)
DIAG_NAME_INDEX(warn_missing_case_for_condition)
DIAG_NAME_INDEX(warn_missing_dependent_template_keyword)
DIAG_NAME_INDEX(warn_missing_explicit_synthesis)
DIAG_NAME_INDEX(warn_missing_field_initializers)
DIAG_NAME_INDEX(warn_missing_format_string)
DIAG_NAME_INDEX(warn_missing_method_return_type)
DIAG_NAME_INDEX(warn_missing_prototype)
DIAG_NAME_INDEX(warn_missing_sdksettings_for_availability_checking)
DIAG_NAME_INDEX(warn_missing_selector_name)
DIAG_NAME_INDEX(warn_missing_sentinel)
DIAG_NAME_INDEX(warn_missing_submodule)
DIAG_NAME_INDEX(warn_missing_sysroot)
DIAG_NAME_INDEX(warn_missing_type_specifier)
DIAG_NAME_INDEX(warn_missing_variable_declarations)
DIAG_NAME_INDEX(warn_missing_whitespace_after_macro_name)
DIAG_NAME_INDEX(warn_mixed_decls_code)
DIAG_NAME_INDEX(warn_mixed_sign_comparison)
DIAG_NAME_INDEX(warn_mmap_incomplete_framework_module_declaration)
DIAG_NAME_INDEX(warn_mmap_mismatched_private_module_name)
DIAG_NAME_INDEX(warn_mmap_mismatched_private_submodule)
DIAG_NAME_INDEX(warn_mmap_redundant_export_as)
DIAG_NAME_INDEX(warn_mmap_umbrella_dir_not_found)
DIAG_NAME_INDEX(warn_mmap_unknown_attribute)
DIAG_NAME_INDEX(warn_modifying_shadowing_decl)
DIAG_NAME_INDEX(warn_module_config_macro_undef)
DIAG_NAME_INDEX(warn_module_config_mismatch)
DIAG_NAME_INDEX(warn_module_conflict)
DIAG_NAME_INDEX(warn_module_system_bit_conflict)
DIAG_NAME_INDEX(warn_module_uses_date_time)
DIAG_NAME_INDEX(warn_mt_message)
DIAG_NAME_INDEX(warn_multichar_character_literal)
DIAG_NAME_INDEX(warn_multiple_method_decl)
DIAG_NAME_INDEX(warn_multiple_selectors)
DIAG_NAME_INDEX(warn_multiversion_duplicate_entries)
DIAG_NAME_INDEX(warn_namespace_member_extra_qualification)
DIAG_NAME_INDEX(warn_neon_vector_initializer_non_portable)
DIAG_NAME_INDEX(warn_nested_block_comment)
DIAG_NAME_INDEX(warn_new_dangling_initializer_list)
DIAG_NAME_INDEX(warn_new_dangling_reference)
DIAG_NAME_INDEX(warn_no_autosynthesis_property)
DIAG_NAME_INDEX(warn_no_autosynthesis_shared_ivar_property)
DIAG_NAME_INDEX(warn_no_constructor_for_refconst)
DIAG_NAME_INDEX(warn_no_dynamic_cast_with_rtti_disabled)
DIAG_NAME_INDEX(warn_no_newline_eof)
DIAG_NAME_INDEX(warn_no_priv_submodule_use_toplevel)
DIAG_NAME_INDEX(warn_no_support_for_eval_method_source_on_m32)
DIAG_NAME_INDEX(warn_no_typeid_with_rtti_disabled)
DIAG_NAME_INDEX(warn_no_underlying_type_specified_for_enum_bitfield)
DIAG_NAME_INDEX(warn_no_unlock)
DIAG_NAME_INDEX(warn_nocf_check_attribute_ignored)
DIAG_NAME_INDEX(warn_noderef_on_non_pointer_or_array)
DIAG_NAME_INDEX(warn_noderef_to_dereferenceable_pointer)
DIAG_NAME_INDEX(warn_non_aligned_allocation_function)
DIAG_NAME_INDEX(warn_non_contravariant_overriding_param_types)
DIAG_NAME_INDEX(warn_non_contravariant_param_types)
DIAG_NAME_INDEX(warn_non_covariant_overriding_ret_types)
DIAG_NAME_INDEX(warn_non_covariant_ret_types)
DIAG_NAME_INDEX(warn_non_literal_null_pointer)
DIAG_NAME_INDEX(warn_non_modular_include_in_framework_module)
DIAG_NAME_INDEX(warn_non_modular_include_in_module)
DIAG_NAME_INDEX(warn_non_pod_vararg_with_format_string)
DIAG_NAME_INDEX(warn_non_prototype_changes_behavior)
DIAG_NAME_INDEX(warn_non_virtual_dtor)
DIAG_NAME_INDEX(warn_nonnull_expr_compare)
DIAG_NAME_INDEX(warn_noreturn_function_has_return_expr)
DIAG_NAME_INDEX(warn_not_a_doxygen_trailing_member_comment)
DIAG_NAME_INDEX(warn_not_compound_assign)
DIAG_NAME_INDEX(warn_not_enough_argument)
DIAG_NAME_INDEX(warn_not_in_enum)
DIAG_NAME_INDEX(warn_not_in_enum_assignment)
DIAG_NAME_INDEX(warn_not_xl_compatible)
DIAG_NAME_INDEX(warn_nothrow_attribute_ignored)
DIAG_NAME_INDEX(warn_npot_ms_struct)
DIAG_NAME_INDEX(warn_ns_attribute_wrong_parameter_type)
DIAG_NAME_INDEX(warn_ns_attribute_wrong_return_type)
DIAG_NAME_INDEX(warn_nsconsumed_attribute_mismatch)
DIAG_NAME_INDEX(warn_nsdictionary_duplicate_key)
DIAG_NAME_INDEX(warn_nsobject_attribute)
DIAG_NAME_INDEX(warn_nsreturns_retained_attribute_mismatch)
DIAG_NAME_INDEX(warn_null_arg)
DIAG_NAME_INDEX(warn_null_in_arithmetic_operation)
DIAG_NAME_INDEX(warn_null_in_comparison_operation)
DIAG_NAME_INDEX(warn_null_pointer_compare)
DIAG_NAME_INDEX(warn_null_resettable_setter)
DIAG_NAME_INDEX(warn_null_ret)
DIAG_NAME_INDEX(warn_null_statement)
DIAG_NAME_INDEX(warn_nullability_declspec)
DIAG_NAME_INDEX(warn_nullability_duplicate)
DIAG_NAME_INDEX(warn_nullability_inferred_on_nested_type)
DIAG_NAME_INDEX(warn_nullability_lost)
DIAG_NAME_INDEX(warn_nullability_missing)
DIAG_NAME_INDEX(warn_nullability_missing_array)
DIAG_NAME_INDEX(warn_objc_boxing_invalid_utf8_string)
DIAG_NAME_INDEX(warn_objc_cdirective_format_string)
DIAG_NAME_INDEX(warn_objc_circular_container)
DIAG_NAME_INDEX(warn_objc_collection_literal_element)
DIAG_NAME_INDEX(warn_objc_designated_init_missing_super_call)
DIAG_NAME_INDEX(warn_objc_designated_init_non_designated_init_call)
DIAG_NAME_INDEX(warn_objc_designated_init_non_super_designated_init_call)
DIAG_NAME_INDEX(warn_objc_direct_ignored)
DIAG_NAME_INDEX(warn_objc_direct_property_ignored)
DIAG_NAME_INDEX(warn_objc_implementation_missing_designated_init_override)
DIAG_NAME_INDEX(warn_objc_invalid_bridge)
DIAG_NAME_INDEX(warn_objc_invalid_bridge_to_cf)
DIAG_NAME_INDEX(warn_objc_isa_assign)
DIAG_NAME_INDEX(warn_objc_isa_use)
DIAG_NAME_INDEX(warn_objc_literal_comparison)
DIAG_NAME_INDEX(warn_objc_missing_super_call)
DIAG_NAME_INDEX(warn_objc_pointer_cxx_catch_fragile)
DIAG_NAME_INDEX(warn_objc_pointer_masking)
DIAG_NAME_INDEX(warn_objc_pointer_masking_performSelector)
DIAG_NAME_INDEX(warn_objc_precise_lifetime_meaningless)
DIAG_NAME_INDEX(warn_objc_property_assign_on_object)
DIAG_NAME_INDEX(warn_objc_property_copy_missing_on_block)
DIAG_NAME_INDEX(warn_objc_property_default_assign_on_object)
DIAG_NAME_INDEX(warn_objc_property_no_assignment_attribute)
DIAG_NAME_INDEX(warn_objc_property_retain_of_block)
DIAG_NAME_INDEX(warn_objc_protocol_qualifier_missing_id)
DIAG_NAME_INDEX(warn_objc_readonly_property_has_setter)
DIAG_NAME_INDEX(warn_objc_redundant_literal_use)
DIAG_NAME_INDEX(warn_objc_redundant_qualified_class_type)
DIAG_NAME_INDEX(warn_objc_requires_super_protocol)
DIAG_NAME_INDEX(warn_objc_root_class_missing)
DIAG_NAME_INDEX(warn_objc_secondary_init_missing_init_call)
DIAG_NAME_INDEX(warn_objc_secondary_init_super_init_call)
DIAG_NAME_INDEX(warn_objc_string_literal_comparison)
DIAG_NAME_INDEX(warn_objc_unsafe_perform_selector)
DIAG_NAME_INDEX(warn_odr_different_num_template_parameters)
DIAG_NAME_INDEX(warn_odr_different_template_parameter_kind)
DIAG_NAME_INDEX(warn_odr_field_type_inconsistent)
DIAG_NAME_INDEX(warn_odr_function_type_inconsistent)
DIAG_NAME_INDEX(warn_odr_ivar_type_inconsistent)
DIAG_NAME_INDEX(warn_odr_non_type_parameter_type_inconsistent)
DIAG_NAME_INDEX(warn_odr_objc_method_num_params_inconsistent)
DIAG_NAME_INDEX(warn_odr_objc_method_param_type_inconsistent)
DIAG_NAME_INDEX(warn_odr_objc_method_result_type_inconsistent)
DIAG_NAME_INDEX(warn_odr_objc_method_variadic_inconsistent)
DIAG_NAME_INDEX(warn_odr_objc_property_impl_kind_inconsistent)
DIAG_NAME_INDEX(warn_odr_objc_property_type_inconsistent)
DIAG_NAME_INDEX(warn_odr_objc_superclass_inconsistent)
DIAG_NAME_INDEX(warn_odr_objc_synthesize_ivar_inconsistent)
DIAG_NAME_INDEX(warn_odr_parameter_pack_non_pack)
DIAG_NAME_INDEX(warn_odr_tag_type_inconsistent)
DIAG_NAME_INDEX(warn_odr_variable_multiple_def)
DIAG_NAME_INDEX(warn_odr_variable_type_inconsistent)
DIAG_NAME_INDEX(warn_old_implicitly_unsigned_long)
DIAG_NAME_INDEX(warn_old_implicitly_unsigned_long_cxx)
DIAG_NAME_INDEX(warn_old_style_cast)
DIAG_NAME_INDEX(warn_omp51_compat_attributes)
DIAG_NAME_INDEX(warn_omp_alignment_not_power_of_two)
DIAG_NAME_INDEX(warn_omp_allocate_thread_on_task_target_directive)
DIAG_NAME_INDEX(warn_omp_ctx_incompatible_property_for_selector)
DIAG_NAME_INDEX(warn_omp_ctx_incompatible_score_for_property)
DIAG_NAME_INDEX(warn_omp_ctx_incompatible_selector_for_set)
DIAG_NAME_INDEX(warn_omp_ctx_selector_without_properties)
DIAG_NAME_INDEX(warn_omp_declare_target_after_first_use)
DIAG_NAME_INDEX(warn_omp_declare_variant_after_emitted)
DIAG_NAME_INDEX(warn_omp_declare_variant_after_used)
DIAG_NAME_INDEX(warn_omp_declare_variant_ctx_mutiple_use)
DIAG_NAME_INDEX(warn_omp_declare_variant_ctx_not_a_property)
DIAG_NAME_INDEX(warn_omp_declare_variant_ctx_not_a_selector)
DIAG_NAME_INDEX(warn_omp_declare_variant_ctx_not_a_set)
DIAG_NAME_INDEX(warn_omp_declare_variant_expected)
DIAG_NAME_INDEX(warn_omp_declare_variant_marked_as_declare_variant)
DIAG_NAME_INDEX(warn_omp_declare_variant_score_not_constant)
DIAG_NAME_INDEX(warn_omp_declare_variant_string_literal_or_identifier)
DIAG_NAME_INDEX(warn_omp_depend_in_ordered_deprecated)
DIAG_NAME_INDEX(warn_omp_extra_tokens_at_eol)
DIAG_NAME_INDEX(warn_omp_linear_step_zero)
DIAG_NAME_INDEX(warn_omp_loop_64_bit_var)
DIAG_NAME_INDEX(warn_omp_minus_in_reduction_deprecated)
DIAG_NAME_INDEX(warn_omp_more_one_device_type_clause)
DIAG_NAME_INDEX(warn_omp_more_one_interop_type)
DIAG_NAME_INDEX(warn_omp_more_one_omp_all_memory)
DIAG_NAME_INDEX(warn_omp_nesting_simd)
DIAG_NAME_INDEX(warn_omp_non_trivial_type_mapped)
DIAG_NAME_INDEX(warn_omp_not_in_target_context)
DIAG_NAME_INDEX(warn_omp_section_is_char)
DIAG_NAME_INDEX(warn_omp_unknown_assumption_clause_missing_id)
DIAG_NAME_INDEX(warn_omp_unknown_assumption_clause_without_args)
DIAG_NAME_INDEX(warn_omp_unterminated_declare_target)
DIAG_NAME_INDEX(warn_omp_used_different_allocator)
DIAG_NAME_INDEX(warn_on_superclass_use)
DIAG_NAME_INDEX(warn_opencl_attr_deprecated_ignored)
DIAG_NAME_INDEX(warn_opencl_generic_address_space_arg)
DIAG_NAME_INDEX(warn_opencl_unsupported_core_feature)
DIAG_NAME_INDEX(warn_operator_new_returns_null)
DIAG_NAME_INDEX(warn_option_invalid_ocl_version)
DIAG_NAME_INDEX(warn_os_log_format_narg)
DIAG_NAME_INDEX(warn_out_of_range_compare)
DIAG_NAME_INDEX(warn_overaligned_type)
DIAG_NAME_INDEX(warn_overloaded_shift_in_comparison)
DIAG_NAME_INDEX(warn_overloaded_virtual)
DIAG_NAME_INDEX(warn_overriding_method_missing_noescape)
DIAG_NAME_INDEX(warn_padded_struct_anon_field)
DIAG_NAME_INDEX(warn_padded_struct_field)
DIAG_NAME_INDEX(warn_padded_struct_size)
DIAG_NAME_INDEX(warn_param_mismatched_alignment)
DIAG_NAME_INDEX(warn_param_return_typestate_mismatch)
DIAG_NAME_INDEX(warn_param_typestate_mismatch)
DIAG_NAME_INDEX(warn_parameter_size)
DIAG_NAME_INDEX(warn_parens_disambiguated_as_function_declaration)
DIAG_NAME_INDEX(warn_parens_disambiguated_as_variable_declaration)
DIAG_NAME_INDEX(warn_pass_class_arg_to_vararg)
DIAG_NAME_INDEX(warn_pessimizing_move_on_initialization)
DIAG_NAME_INDEX(warn_pessimizing_move_on_return)
DIAG_NAME_INDEX(warn_pointer_abs)
DIAG_NAME_INDEX(warn_pointer_arith_null_ptr)
DIAG_NAME_INDEX(warn_pointer_compare)
DIAG_NAME_INDEX(warn_pointer_indirection_from_incompatible_type)
DIAG_NAME_INDEX(warn_pointer_sub_null_ptr)
DIAG_NAME_INDEX(warn_pointer_to_enum_cast)
DIAG_NAME_INDEX(warn_pointer_to_int_cast)
DIAG_NAME_INDEX(warn_poison_system_directories)
DIAG_NAME_INDEX(warn_potentially_direct_selector_expression)
DIAG_NAME_INDEX(warn_pp_ambiguous_macro)
DIAG_NAME_INDEX(warn_pp_convert_to_positive)
DIAG_NAME_INDEX(warn_pp_date_time)
DIAG_NAME_INDEX(warn_pp_expr_overflow)
DIAG_NAME_INDEX(warn_pp_hdrstop_filename_ignored)
DIAG_NAME_INDEX(warn_pp_invalid_directive)
DIAG_NAME_INDEX(warn_pp_line_decimal)
DIAG_NAME_INDEX(warn_pp_macro_def_mismatch_with_pch)
DIAG_NAME_INDEX(warn_pp_macro_hides_keyword)
DIAG_NAME_INDEX(warn_pp_macro_is_reserved_id)
DIAG_NAME_INDEX(warn_pp_objc_macro_redef_ignored)
DIAG_NAME_INDEX(warn_pp_undef_identifier)
DIAG_NAME_INDEX(warn_pp_undef_prefix)
DIAG_NAME_INDEX(warn_pragma_align_expected_equal)
DIAG_NAME_INDEX(warn_pragma_align_invalid_option)
DIAG_NAME_INDEX(warn_pragma_align_not_xl_compatible)
DIAG_NAME_INDEX(warn_pragma_attribute_unused)
DIAG_NAME_INDEX(warn_pragma_comment_ignored)
DIAG_NAME_INDEX(warn_pragma_debug_dependent_argument)
DIAG_NAME_INDEX(warn_pragma_debug_missing_argument)
DIAG_NAME_INDEX(warn_pragma_debug_missing_command)
DIAG_NAME_INDEX(warn_pragma_debug_unexpected_argument)
DIAG_NAME_INDEX(warn_pragma_debug_unexpected_command)
DIAG_NAME_INDEX(warn_pragma_debug_unknown_module)
DIAG_NAME_INDEX(warn_pragma_deprecated_macro_use)
DIAG_NAME_INDEX(warn_pragma_diagnostic_cannot_pop)
DIAG_NAME_INDEX(warn_pragma_diagnostic_invalid)
DIAG_NAME_INDEX(warn_pragma_diagnostic_invalid_option)
DIAG_NAME_INDEX(warn_pragma_diagnostic_invalid_token)
DIAG_NAME_INDEX(warn_pragma_diagnostic_unknown_warning)
DIAG_NAME_INDEX(warn_pragma_exec_charset_expected)
DIAG_NAME_INDEX(warn_pragma_exec_charset_push_invalid)
DIAG_NAME_INDEX(warn_pragma_exec_charset_spec_invalid)
DIAG_NAME_INDEX(warn_pragma_expected_action_or_r_paren)
DIAG_NAME_INDEX(warn_pragma_expected_colon)
DIAG_NAME_INDEX(warn_pragma_expected_colon_r_paren)
DIAG_NAME_INDEX(warn_pragma_expected_comma)
DIAG_NAME_INDEX(warn_pragma_expected_identifier)
DIAG_NAME_INDEX(warn_pragma_expected_init_seg)
DIAG_NAME_INDEX(warn_pragma_expected_integer)
DIAG_NAME_INDEX(warn_pragma_expected_lparen)
DIAG_NAME_INDEX(warn_pragma_expected_non_wide_string)
DIAG_NAME_INDEX(warn_pragma_expected_predicate)
DIAG_NAME_INDEX(warn_pragma_expected_punc)
DIAG_NAME_INDEX(warn_pragma_expected_rparen)
DIAG_NAME_INDEX(warn_pragma_expected_section_label_or_name)
DIAG_NAME_INDEX(warn_pragma_expected_section_name)
DIAG_NAME_INDEX(warn_pragma_expected_section_push_pop_or_name)
DIAG_NAME_INDEX(warn_pragma_expected_string)
DIAG_NAME_INDEX(warn_pragma_extension_is_core)
DIAG_NAME_INDEX(warn_pragma_extra_tokens_at_eol)
DIAG_NAME_INDEX(warn_pragma_final_macro)
DIAG_NAME_INDEX(warn_pragma_force_cuda_host_device_bad_arg)
DIAG_NAME_INDEX(warn_pragma_fp_ignored)
DIAG_NAME_INDEX(warn_pragma_ignored)
DIAG_NAME_INDEX(warn_pragma_include_alias_expected)
DIAG_NAME_INDEX(warn_pragma_include_alias_expected_filename)
DIAG_NAME_INDEX(warn_pragma_include_alias_mismatch_angle)
DIAG_NAME_INDEX(warn_pragma_include_alias_mismatch_quote)
DIAG_NAME_INDEX(warn_pragma_init_seg_unsupported_target)
DIAG_NAME_INDEX(warn_pragma_intrinsic_builtin)
DIAG_NAME_INDEX(warn_pragma_invalid_action)
DIAG_NAME_INDEX(warn_pragma_invalid_argument)
DIAG_NAME_INDEX(warn_pragma_invalid_specific_action)
DIAG_NAME_INDEX(warn_pragma_message)
DIAG_NAME_INDEX(warn_pragma_missing_argument)
DIAG_NAME_INDEX(warn_pragma_ms_fenv_access)
DIAG_NAME_INDEX(warn_pragma_ms_struct)
DIAG_NAME_INDEX(warn_pragma_omp_ignored)
DIAG_NAME_INDEX(warn_pragma_options_align_reset_failed)
DIAG_NAME_INDEX(warn_pragma_options_expected_align)
DIAG_NAME_INDEX(warn_pragma_pack_invalid_alignment)
DIAG_NAME_INDEX(warn_pragma_pack_malformed)
DIAG_NAME_INDEX(warn_pragma_pack_modified_after_include)
DIAG_NAME_INDEX(warn_pragma_pack_no_pop_eof)
DIAG_NAME_INDEX(warn_pragma_pack_non_default_at_include)
DIAG_NAME_INDEX(warn_pragma_pack_pop_identifier_and_alignment)
DIAG_NAME_INDEX(warn_pragma_pack_show)
DIAG_NAME_INDEX(warn_pragma_pop_failed)
DIAG_NAME_INDEX(warn_pragma_pop_macro_no_push)
DIAG_NAME_INDEX(warn_pragma_restrict_expansion_macro_use)
DIAG_NAME_INDEX(warn_pragma_unknown_extension)
DIAG_NAME_INDEX(warn_pragma_unroll_cuda_value_in_parens)
DIAG_NAME_INDEX(warn_pragma_unsupported_action)
DIAG_NAME_INDEX(warn_pragma_unsupported_extension)
DIAG_NAME_INDEX(warn_pragma_unused_expected_var)
DIAG_NAME_INDEX(warn_pragma_unused_expected_var_arg)
DIAG_NAME_INDEX(warn_pragma_unused_undeclared_var)
DIAG_NAME_INDEX(warn_pragma_warning_expected)
DIAG_NAME_INDEX(warn_pragma_warning_expected_number)
DIAG_NAME_INDEX(warn_pragma_warning_push_level)
DIAG_NAME_INDEX(warn_pragma_warning_spec_invalid)
DIAG_NAME_INDEX(warn_pre_c2x_compat_attributes)
DIAG_NAME_INDEX(warn_precedence_bitwise_conditional)
DIAG_NAME_INDEX(warn_precedence_bitwise_rel)
DIAG_NAME_INDEX(warn_precedence_conditional)
DIAG_NAME_INDEX(warn_printf_ObjCflags_without_ObjCConversion)
DIAG_NAME_INDEX(warn_printf_asterisk_missing_arg)
DIAG_NAME_INDEX(warn_printf_asterisk_wrong_type)
DIAG_NAME_INDEX(warn_printf_data_arg_not_used)
DIAG_NAME_INDEX(warn_printf_empty_objc_flag)
DIAG_NAME_INDEX(warn_printf_format_string_contains_null_char)
DIAG_NAME_INDEX(warn_printf_format_string_not_null_terminated)
DIAG_NAME_INDEX(warn_printf_ignored_flag)
DIAG_NAME_INDEX(warn_printf_incomplete_specifier)
DIAG_NAME_INDEX(warn_printf_insufficient_data_args)
DIAG_NAME_INDEX(warn_printf_invalid_objc_flag)
DIAG_NAME_INDEX(warn_printf_narg_not_supported)
DIAG_NAME_INDEX(warn_printf_nonsensical_flag)
DIAG_NAME_INDEX(warn_printf_nonsensical_optional_amount)
DIAG_NAME_INDEX(warn_printf_positional_arg_exceeds_data_args)
DIAG_NAME_INDEX(warn_private_extern)
DIAG_NAME_INDEX(warn_profile_data_misexpect)
DIAG_NAME_INDEX(warn_profile_data_missing)
DIAG_NAME_INDEX(warn_profile_data_out_of_date)
DIAG_NAME_INDEX(warn_profile_data_unprofiled)
DIAG_NAME_INDEX(warn_property_access_suggest)
DIAG_NAME_INDEX(warn_property_attr_mismatch)
DIAG_NAME_INDEX(warn_property_attribute)
DIAG_NAME_INDEX(warn_property_getter_owning_mismatch)
DIAG_NAME_INDEX(warn_property_implicitly_mismatched)
DIAG_NAME_INDEX(warn_property_method_deprecated)
DIAG_NAME_INDEX(warn_property_redecl_getter_mismatch)
DIAG_NAME_INDEX(warn_property_types_are_incompatible)
DIAG_NAME_INDEX(warn_protocol_property_mismatch)
DIAG_NAME_INDEX(warn_pt_guarded_pass_by_reference)
DIAG_NAME_INDEX(warn_ptr_arith_exceeds_bounds)
DIAG_NAME_INDEX(warn_ptr_arith_exceeds_max_addressable_bounds)
DIAG_NAME_INDEX(warn_ptr_arith_precedes_bounds)
DIAG_NAME_INDEX(warn_ptr_independentclass_attribute)
DIAG_NAME_INDEX(warn_qual_return_type)
DIAG_NAME_INDEX(warn_quoted_include_in_framework_header)
DIAG_NAME_INDEX(warn_reading_std_cxx_module_by_implicit_paths)
DIAG_NAME_INDEX(warn_readonly_property)
DIAG_NAME_INDEX(warn_receiver_forward_class)
DIAG_NAME_INDEX(warn_receiver_forward_instance)
DIAG_NAME_INDEX(warn_redecl_library_builtin)
DIAG_NAME_INDEX(warn_redeclaration_without_attribute_prev_attribute_ignored)
DIAG_NAME_INDEX(warn_redeclaration_without_import_attribute)
DIAG_NAME_INDEX(warn_redefine_extname_not_applied)
DIAG_NAME_INDEX(warn_redefinition_in_param_list)
DIAG_NAME_INDEX(warn_redundant_loop_iteration)
DIAG_NAME_INDEX(warn_redundant_move_on_return)
DIAG_NAME_INDEX(warn_redundant_parens_around_declarator)
DIAG_NAME_INDEX(warn_reference_field_is_uninit)
DIAG_NAME_INDEX(warn_register_objc_catch_parm)
DIAG_NAME_INDEX(warn_reinterpret_different_from_static)
DIAG_NAME_INDEX(warn_related_result_type_compatibility_class)
DIAG_NAME_INDEX(warn_related_result_type_compatibility_protocol)
DIAG_NAME_INDEX(warn_remainder_division_by_zero)
DIAG_NAME_INDEX(warn_require_const_init_added_too_late)
DIAG_NAME_INDEX(warn_reserved_extern_symbol)
DIAG_NAME_INDEX(warn_reserved_module_name)
DIAG_NAME_INDEX(warn_ret_addr_label)
DIAG_NAME_INDEX(warn_ret_local_temp_addr_ref)
DIAG_NAME_INDEX(warn_ret_stack_addr_ref)
DIAG_NAME_INDEX(warn_return_missing_expr)
DIAG_NAME_INDEX(warn_return_typestate_for_unconsumable_type)
DIAG_NAME_INDEX(warn_return_typestate_mismatch)
DIAG_NAME_INDEX(warn_return_value_size)
DIAG_NAME_INDEX(warn_return_value_udt)
DIAG_NAME_INDEX(warn_return_value_udt_incomplete)
DIAG_NAME_INDEX(warn_riscv_repeated_interrupt_attribute)
DIAG_NAME_INDEX(warn_root_inst_method_not_found)
DIAG_NAME_INDEX(warn_sampler_initializer_invalid_bits)
DIAG_NAME_INDEX(warn_scanf_nonzero_width)
DIAG_NAME_INDEX(warn_scanf_scanlist_incomplete)
DIAG_NAME_INDEX(warn_second_arg_of_va_start_not_last_named_param)
DIAG_NAME_INDEX(warn_second_parameter_to_va_arg_never_compatible)
DIAG_NAME_INDEX(warn_second_parameter_to_va_arg_not_pod)
DIAG_NAME_INDEX(warn_second_parameter_to_va_arg_ownership_qualified)
DIAG_NAME_INDEX(warn_self_assignment_builtin)
DIAG_NAME_INDEX(warn_self_assignment_overloaded)
DIAG_NAME_INDEX(warn_self_move)
DIAG_NAME_INDEX(warn_semicolon_before_method_body)
DIAG_NAME_INDEX(warn_setter_getter_impl_required)
DIAG_NAME_INDEX(warn_setter_getter_impl_required_in_category)
DIAG_NAME_INDEX(warn_shadow_field)
DIAG_NAME_INDEX(warn_shift_gt_typewidth)
DIAG_NAME_INDEX(warn_shift_lhs_negative)
DIAG_NAME_INDEX(warn_shift_negative)
DIAG_NAME_INDEX(warn_shift_result_gt_typewidth)
DIAG_NAME_INDEX(warn_shift_result_sets_sign_bit)
DIAG_NAME_INDEX(warn_side_effects_typeid)
DIAG_NAME_INDEX(warn_side_effects_unevaluated_context)
DIAG_NAME_INDEX(warn_signed_bitfield_enum_conversion)
DIAG_NAME_INDEX(warn_sizeof_array_decay)
DIAG_NAME_INDEX(warn_sizeof_array_param)
DIAG_NAME_INDEX(warn_sizeof_pointer_expr_memaccess)
DIAG_NAME_INDEX(warn_sizeof_pointer_expr_memaccess_note)
DIAG_NAME_INDEX(warn_sizeof_pointer_type_memaccess)
DIAG_NAME_INDEX(warn_slash_u_filename)
DIAG_NAME_INDEX(warn_slh_does_not_support_asm_goto)
DIAG_NAME_INDEX(warn_some_initializers_out_of_order)
DIAG_NAME_INDEX(warn_sometimes_uninit_var)
DIAG_NAME_INDEX(warn_splice_in_doxygen_comment)
DIAG_NAME_INDEX(warn_stack_clash_protection_inline_asm)
DIAG_NAME_INDEX(warn_stack_exhausted)
DIAG_NAME_INDEX(warn_standalone_specifier)
DIAG_NAME_INDEX(warn_static_array_too_small)
DIAG_NAME_INDEX(warn_static_assert_message_constexpr)
DIAG_NAME_INDEX(warn_static_inline_explicit_inst_ignored)
DIAG_NAME_INDEX(warn_static_local_in_extern_inline)
DIAG_NAME_INDEX(warn_static_main)
DIAG_NAME_INDEX(warn_static_self_reference_in_init)
DIAG_NAME_INDEX(warn_stdc_fenv_round_not_supported)
DIAG_NAME_INDEX(warn_stdc_unknown_rounding_mode)
DIAG_NAME_INDEX(warn_strict_multiple_method_decl)
DIAG_NAME_INDEX(warn_strict_potentially_direct_selector_expression)
DIAG_NAME_INDEX(warn_strict_prototypes)
DIAG_NAME_INDEX(warn_strict_uses_without_prototype)
DIAG_NAME_INDEX(warn_string_plus_char)
DIAG_NAME_INDEX(warn_string_plus_int)
DIAG_NAME_INDEX(warn_stringcompare)
DIAG_NAME_INDEX(warn_strlcpycat_wrong_size)
DIAG_NAME_INDEX(warn_strncat_large_size)
DIAG_NAME_INDEX(warn_strncat_src_size)
DIAG_NAME_INDEX(warn_strncat_wrong_size)
DIAG_NAME_INDEX(warn_struct_class_previous_tag_mismatch)
DIAG_NAME_INDEX(warn_struct_class_tag_mismatch)
DIAG_NAME_INDEX(warn_sub_ptr_zero_size_types)
DIAG_NAME_INDEX(warn_subscript_is_char)
DIAG_NAME_INDEX(warn_suggest_destructor_marked_not_override_overriding)
DIAG_NAME_INDEX(warn_suggest_function_marked_not_override_overriding)
DIAG_NAME_INDEX(warn_suggest_noreturn_block)
DIAG_NAME_INDEX(warn_suggest_noreturn_function)
DIAG_NAME_INDEX(warn_superclass_variable_sized_type_not_at_end)
DIAG_NAME_INDEX(warn_suspicious_bzero_size)
DIAG_NAME_INDEX(warn_suspicious_sizeof_memset)
DIAG_NAME_INDEX(warn_sycl_kernel_invalid_template_param_type)
DIAG_NAME_INDEX(warn_sycl_kernel_num_of_function_params)
DIAG_NAME_INDEX(warn_sycl_kernel_num_of_template_params)
DIAG_NAME_INDEX(warn_sycl_kernel_return_type)
DIAG_NAME_INDEX(warn_sync_fetch_and_nand_semantics_change)
DIAG_NAME_INDEX(warn_sync_op_misaligned)
DIAG_NAME_INDEX(warn_taking_address_of_packed_member)
DIAG_NAME_INDEX(warn_target_clone_duplicate_options)
DIAG_NAME_INDEX(warn_target_clone_mixed_values)
DIAG_NAME_INDEX(warn_target_clone_no_impact_options)
DIAG_NAME_INDEX(warn_target_override_arm64ec)
DIAG_NAME_INDEX(warn_target_unrecognized_env)
DIAG_NAME_INDEX(warn_target_unsupported_abs2008)
DIAG_NAME_INDEX(warn_target_unsupported_abslegacy)
DIAG_NAME_INDEX(warn_target_unsupported_branch_protection_attribute)
DIAG_NAME_INDEX(warn_target_unsupported_compact_branches)
DIAG_NAME_INDEX(warn_target_unsupported_extension)
DIAG_NAME_INDEX(warn_target_unsupported_nan2008)
DIAG_NAME_INDEX(warn_target_unsupported_nanlegacy)
DIAG_NAME_INDEX(warn_tautological_bool_compare)
DIAG_NAME_INDEX(warn_tautological_compare_objc_bool)
DIAG_NAME_INDEX(warn_tautological_compare_value_range)
DIAG_NAME_INDEX(warn_tautological_constant_compare)
DIAG_NAME_INDEX(warn_tautological_overlap_comparison)
DIAG_NAME_INDEX(warn_tcb_enforcement_violation)
DIAG_NAME_INDEX(warn_template_arg_negative)
DIAG_NAME_INDEX(warn_template_arg_too_large)
DIAG_NAME_INDEX(warn_template_export_unsupported)
DIAG_NAME_INDEX(warn_template_qualified_friend_ignored)
DIAG_NAME_INDEX(warn_template_qualified_friend_unsupported)
DIAG_NAME_INDEX(warn_template_spec_extra_headers)
DIAG_NAME_INDEX(warn_tentative_incomplete_array)
DIAG_NAME_INDEX(warn_this_bool_conversion)
DIAG_NAME_INDEX(warn_this_null_compare)
DIAG_NAME_INDEX(warn_thread_attribute_argument_not_lockable)
DIAG_NAME_INDEX(warn_thread_attribute_decl_not_lockable)
DIAG_NAME_INDEX(warn_thread_attribute_decl_not_pointer)
DIAG_NAME_INDEX(warn_thread_attribute_ignored)
DIAG_NAME_INDEX(warn_thread_attribute_not_on_capability_member)
DIAG_NAME_INDEX(warn_thread_attribute_not_on_non_static_member)
DIAG_NAME_INDEX(warn_thread_safety_beta)
DIAG_NAME_INDEX(warn_thread_safety_verbose)
DIAG_NAME_INDEX(warn_throw_in_noexcept_func)
DIAG_NAME_INDEX(warn_throw_underaligned_obj)
DIAG_NAME_INDEX(warn_transparent_union_attribute_field_size_align)
DIAG_NAME_INDEX(warn_transparent_union_attribute_floating)
DIAG_NAME_INDEX(warn_transparent_union_attribute_not_definition)
DIAG_NAME_INDEX(warn_transparent_union_attribute_zero_fields)
DIAG_NAME_INDEX(warn_type_attribute_deprecated_on_decl)
DIAG_NAME_INDEX(warn_type_attribute_wrong_type)
DIAG_NAME_INDEX(warn_type_safety_null_pointer_required)
DIAG_NAME_INDEX(warn_type_safety_type_mismatch)
DIAG_NAME_INDEX(warn_type_tag_for_datatype_wrong_kind)
DIAG_NAME_INDEX(warn_typecheck_convert_incompatible_function_pointer_strict)
DIAG_NAME_INDEX(warn_typecheck_function_qualifiers_ignored)
DIAG_NAME_INDEX(warn_typecheck_function_qualifiers_unspecified)
DIAG_NAME_INDEX(warn_typecheck_ordered_comparison_of_function_pointers)
DIAG_NAME_INDEX(warn_typecheck_reference_qualifiers)
DIAG_NAME_INDEX(warn_typecheck_vector_element_sizes_not_equal)
DIAG_NAME_INDEX(warn_ucn_escape_incomplete)
DIAG_NAME_INDEX(warn_ucn_escape_no_digits)
DIAG_NAME_INDEX(warn_ucn_escape_surrogate)
DIAG_NAME_INDEX(warn_ucn_not_valid_in_c89)
DIAG_NAME_INDEX(warn_ucn_not_valid_in_c89_literal)
DIAG_NAME_INDEX(warn_unaligned_access)
DIAG_NAME_INDEX(warn_unannotated_fallthrough)
DIAG_NAME_INDEX(warn_unannotated_fallthrough_per_function)
DIAG_NAME_INDEX(warn_unavailable_def)
DIAG_NAME_INDEX(warn_unavailable_fwdclass_message)
DIAG_NAME_INDEX(warn_uncovered_module_header)
DIAG_NAME_INDEX(warn_undeclared_selector)
DIAG_NAME_INDEX(warn_undeclared_selector_with_typo)
DIAG_NAME_INDEX(warn_undef_interface)
DIAG_NAME_INDEX(warn_undef_interface_suggest)
DIAG_NAME_INDEX(warn_undef_method_impl)
DIAG_NAME_INDEX(warn_undef_protocolref)
DIAG_NAME_INDEX(warn_undefined_inline)
DIAG_NAME_INDEX(warn_undefined_internal)
DIAG_NAME_INDEX(warn_undefined_reinterpret_cast)
DIAG_NAME_INDEX(warn_unevaluated_string_prefix)
DIAG_NAME_INDEX(warn_unguarded_availability)
DIAG_NAME_INDEX(warn_unguarded_availability_new)
DIAG_NAME_INDEX(warn_unhandled_ms_attribute_ignored)
DIAG_NAME_INDEX(warn_unimplemented_protocol_method)
DIAG_NAME_INDEX(warn_unimplemented_selector)
DIAG_NAME_INDEX(warn_uninit_byref_blockvar_captured_by_block)
DIAG_NAME_INDEX(warn_uninit_const_reference)
DIAG_NAME_INDEX(warn_uninit_self_reference_in_init)
DIAG_NAME_INDEX(warn_uninit_self_reference_in_reference_init)
DIAG_NAME_INDEX(warn_uninit_var)
DIAG_NAME_INDEX(warn_unknown_attribute_ignored)
DIAG_NAME_INDEX(warn_unknown_comment_command_name)
DIAG_NAME_INDEX(warn_unknown_declare_variant_isa_trait)
DIAG_NAME_INDEX(warn_unknown_diag_option)
DIAG_NAME_INDEX(warn_unknown_sanitizer_ignored)
DIAG_NAME_INDEX(warn_unknown_warning_specifier)
DIAG_NAME_INDEX(warn_unlock_but_no_lock)
DIAG_NAME_INDEX(warn_unlock_kind_mismatch)
DIAG_NAME_INDEX(warn_unnecessary_packed)
DIAG_NAME_INDEX(warn_unneeded_internal_decl)
DIAG_NAME_INDEX(warn_unneeded_member_function)
DIAG_NAME_INDEX(warn_unneeded_static_internal_decl)
DIAG_NAME_INDEX(warn_unpacked_field)
DIAG_NAME_INDEX(warn_unqualified_call_to_std_cast_function)
DIAG_NAME_INDEX(warn_unreachable)
DIAG_NAME_INDEX(warn_unreachable_association)
DIAG_NAME_INDEX(warn_unreachable_break)
DIAG_NAME_INDEX(warn_unreachable_default)
DIAG_NAME_INDEX(warn_unreachable_fallthrough_attr)
DIAG_NAME_INDEX(warn_unreachable_loop_increment)
DIAG_NAME_INDEX(warn_unreachable_return)
DIAG_NAME_INDEX(warn_unsafe_buffer_operation)
DIAG_NAME_INDEX(warn_unsafe_buffer_variable)
DIAG_NAME_INDEX(warn_unsequenced_mod_mod)
DIAG_NAME_INDEX(warn_unsequenced_mod_use)
DIAG_NAME_INDEX(warn_unsigned_abs)
DIAG_NAME_INDEX(warn_unsigned_always_true_comparison)
DIAG_NAME_INDEX(warn_unsigned_bitfield_assigned_signed_enum)
DIAG_NAME_INDEX(warn_unsigned_char_always_true_comparison)
DIAG_NAME_INDEX(warn_unsigned_enum_always_true_comparison)
DIAG_NAME_INDEX(warn_unsupported_branch_protection)
DIAG_NAME_INDEX(warn_unsupported_branch_protection_spec)
DIAG_NAME_INDEX(warn_unsupported_lifetime_extension)
DIAG_NAME_INDEX(warn_unsupported_target_attribute)
DIAG_NAME_INDEX(warn_unused_but_set_parameter)
DIAG_NAME_INDEX(warn_unused_but_set_variable)
DIAG_NAME_INDEX(warn_unused_call)
DIAG_NAME_INDEX(warn_unused_comma_left_operand)
DIAG_NAME_INDEX(warn_unused_comparison)
DIAG_NAME_INDEX(warn_unused_const_variable)
DIAG_NAME_INDEX(warn_unused_constructor)
DIAG_NAME_INDEX(warn_unused_constructor_msg)
DIAG_NAME_INDEX(warn_unused_container_subscript_expr)
DIAG_NAME_INDEX(warn_unused_exception_param)
DIAG_NAME_INDEX(warn_unused_expr)
DIAG_NAME_INDEX(warn_unused_function)
DIAG_NAME_INDEX(warn_unused_label)
DIAG_NAME_INDEX(warn_unused_lambda_capture)
DIAG_NAME_INDEX(warn_unused_local_typedef)
DIAG_NAME_INDEX(warn_unused_member_function)
DIAG_NAME_INDEX(warn_unused_parameter)
DIAG_NAME_INDEX(warn_unused_private_field)
DIAG_NAME_INDEX(warn_unused_property_backing_ivar)
DIAG_NAME_INDEX(warn_unused_property_expr)
DIAG_NAME_INDEX(warn_unused_result)
DIAG_NAME_INDEX(warn_unused_result_msg)
DIAG_NAME_INDEX(warn_unused_result_typedef_unsupported_spelling)
DIAG_NAME_INDEX(warn_unused_template)
DIAG_NAME_INDEX(warn_unused_variable)
DIAG_NAME_INDEX(warn_unused_voidptr)
DIAG_NAME_INDEX(warn_unused_volatile)
DIAG_NAME_INDEX(warn_use_in_invalid_state)
DIAG_NAME_INDEX(warn_use_of_private_header_outside_module)
DIAG_NAME_INDEX(warn_use_of_temp_in_invalid_state)
DIAG_NAME_INDEX(warn_used_but_marked_unused)
DIAG_NAME_INDEX(warn_user_literal_reserved)
DIAG_NAME_INDEX(warn_using_directive_in_header)
DIAG_NAME_INDEX(warn_utf8_symbol_homoglyph)
DIAG_NAME_INDEX(warn_utf8_symbol_zero_width)
DIAG_NAME_INDEX(warn_va_start_type_is_undefined)
DIAG_NAME_INDEX(warn_var_decl_not_read_only)
DIAG_NAME_INDEX(warn_var_deref_requires_any_lock)
DIAG_NAME_INDEX(warn_var_deref_requires_lock)
DIAG_NAME_INDEX(warn_var_deref_requires_lock_precise)
DIAG_NAME_INDEX(warn_var_template_missing)
DIAG_NAME_INDEX(warn_variable_requires_any_lock)
DIAG_NAME_INDEX(warn_variable_requires_lock)
DIAG_NAME_INDEX(warn_variable_requires_lock_precise)
DIAG_NAME_INDEX(warn_variable_sized_ivar_visibility)
DIAG_NAME_INDEX(warn_variables_not_in_loop_body)
DIAG_NAME_INDEX(warn_vbase_moved_multiple_times)
DIAG_NAME_INDEX(warn_vector_long_decl_spec_combination)
DIAG_NAME_INDEX(warn_vector_mode_deprecated)
DIAG_NAME_INDEX(warn_verbatim_block_end_without_start)
DIAG_NAME_INDEX(warn_vla_used)
DIAG_NAME_INDEX(warn_void_pointer_to_enum_cast)
DIAG_NAME_INDEX(warn_void_pointer_to_int_cast)
DIAG_NAME_INDEX(warn_wasm_dynamic_exception_spec_ignored)
DIAG_NAME_INDEX(warn_weak_identifier_undeclared)
DIAG_NAME_INDEX(warn_weak_import)
DIAG_NAME_INDEX(warn_weak_template_vtable)
DIAG_NAME_INDEX(warn_weak_vtable)
DIAG_NAME_INDEX(warn_wrong_absolute_value_type)
DIAG_NAME_INDEX(warn_wrong_clang_attr_namespace)
DIAG_NAME_INDEX(warn_xor_used_as_pow)
DIAG_NAME_INDEX(warn_xor_used_as_pow_base)
DIAG_NAME_INDEX(warn_xor_used_as_pow_base_extra)
DIAG_NAME_INDEX(warn_zero_as_null_pointer_constant)
DIAG_NAME_INDEX(warn_zero_size_struct_union_compat)
DIAG_NAME_INDEX(warn_zero_size_struct_union_in_extern_c)
