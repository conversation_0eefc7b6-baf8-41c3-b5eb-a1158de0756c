#ifdef DRIVERSTART
__DRIVERSTART = DIAG_START_DRIVER,
#undef DRIVERSTART
#endif

DIAG(err_aix_unsupported_tls_model, CLASS_ERROR, (unsigned)diag::Severity::Error, "TLS model '%0' is not yet supported on AIX", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_analyzer_checker_incompatible_analyzer_option, CLASS_ERROR, (unsigned)diag::Severity::Error, "checker cannot be enabled with analyzer option '%0' == %1", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_analyzer_checker_option_invalid_input, CLASS_ERROR, (unsigned)diag::Severity::Error, "invalid input for checker option '%0', that expects %1", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_analyzer_checker_option_unknown, CLASS_ERROR, (unsigned)diag::Severity::Error, "checker '%0' has no option called '%1'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_analyzer_config_invalid_input, CLASS_ERROR, (unsigned)diag::Severity::Error, "invalid input for analyzer-config option '%0', that expects %1 value", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_analyzer_config_multiple_values, CLASS_ERROR, (unsigned)diag::Severity::Error, "analyzer-config option '%0' should contain only one '='", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_analyzer_config_no_value, CLASS_ERROR, (unsigned)diag::Severity::Error, "analyzer-config option '%0' has a key but no value", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_analyzer_config_unknown, CLASS_ERROR, (unsigned)diag::Severity::Error, "unknown analyzer-config '%0'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_analyzer_not_built_with_z3, CLASS_ERROR, (unsigned)diag::Severity::Error, "analyzer constraint manager 'z3' is only available if LLVM was built with -DLLVM_ENABLE_Z3_SOLVER=ON", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_arc_unsupported_on_runtime, CLASS_ERROR, (unsigned)diag::Severity::Error, "-fobjc-arc is not supported on platforms using the legacy runtime", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_arc_unsupported_on_toolchain, CLASS_ERROR, (unsigned)diag::Severity::Error, "-fobjc-arc is not supported on versions of OS X prior to 10.6", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_arch_unsupported_isa, CLASS_ERROR, (unsigned)diag::Severity::Error, "architecture '%0' does not support '%1' execution mode", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_cc1_round_trip_fail_then_ok, CLASS_ERROR, (unsigned)diag::Severity::Error, "original arguments parse failed, then succeeded in round-trip", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_cc1_round_trip_mismatch, CLASS_ERROR, (unsigned)diag::Severity::Error, "generated arguments do not match in round-trip", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_cc1_round_trip_ok_then_fail, CLASS_ERROR, (unsigned)diag::Severity::Error, "generated arguments parse failed in round-trip", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_cc1_unbounded_vscale_min, CLASS_ERROR, (unsigned)diag::Severity::Error, "minimum vscale must be an unsigned integer greater than 0", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_cmse_pi_are_incompatible, CLASS_ERROR, (unsigned)diag::Severity::Error, "cmse is not compatible with %select{RWPI|ROPI}0", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_cpu_unsupported_isa, CLASS_ERROR, (unsigned)diag::Severity::Error, "CPU '%0' does not support '%1' execution mode", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_I_dash_not_supported, CLASS_ERROR, (unsigned)diag::Severity::Error, "'%0' not supported, please use -iquote instead", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_Xopenmp_target_missing_triple, CLASS_ERROR, (unsigned)diag::Severity::Error, "cannot deduce implicit triple value for -Xopenmp-target, specify triple using -Xopenmp-target=<triple>", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_alignment_not_power_of_two, CLASS_ERROR, (unsigned)diag::Severity::Error, "alignment is not a power of 2 in '%0'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_amdgpu_ieee_without_no_honor_nans, CLASS_ERROR, (unsigned)diag::Severity::Error, "invalid argument '-mno-amdgpu-ieee' only allowed with relaxed NaN handling", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_arg_requires_bitcode_input, CLASS_ERROR, (unsigned)diag::Severity::Error, "option '%0' requires input to be LLVM bitcode", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_argument_not_allowed_with, CLASS_ERROR, (unsigned)diag::Severity::Error, "invalid argument '%0' not allowed with '%1'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_argument_only_allowed_with, CLASS_ERROR, (unsigned)diag::Severity::Error, "invalid argument '%0' only allowed with '%1'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_bad_offload_arch_combo, CLASS_ERROR, (unsigned)diag::Severity::Error, "invalid offload arch combinations: '%0' and '%1' (for a specific processor, a feature should either exist in all offload archs, or not exist in any offload archs)", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_bad_target_id, CLASS_ERROR, (unsigned)diag::Severity::Error, "invalid target ID '%0'; format is a processor name followed by an optional colon-delimited list of features followed by an enable/disable sign (e.g., 'gfx908:sramecc+:xnack-')", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_bitcode_unsupported_on_toolchain, CLASS_ERROR, (unsigned)diag::Severity::Error, "-fembed-bitcode is not supported on versions of iOS prior to 6.0", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_cannot_mix_options, CLASS_ERROR, (unsigned)diag::Severity::Error, "cannot specify '%1' along with '%0'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_cannot_open_config_file, CLASS_ERROR, (unsigned)diag::Severity::Error, "configuration file '%0' cannot be opened: %1", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_cannot_open_randomize_layout_seed_file, CLASS_ERROR, (unsigned)diag::Severity::Error, "cannot read randomize layout seed file '%0'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_cannot_read_config_file, CLASS_ERROR, (unsigned)diag::Severity::Error, "cannot read configuration file '%0': %1", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_cc_print_options_failure, CLASS_ERROR, (unsigned)diag::Severity::Error, "unable to open CC_PRINT_OPTIONS file: %0", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_clang_unsupported, CLASS_ERROR, (unsigned)diag::Severity::Error, "the clang compiler does not support '%0'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_clang_unsupported_opt_cxx_darwin_i386, CLASS_ERROR, (unsigned)diag::Severity::Error, "the clang compiler does not support '%0' for C++ on Darwin/i386", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_clang_unsupported_opt_faltivec, CLASS_ERROR, (unsigned)diag::Severity::Error, "the clang compiler does not support '%0', %1", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_clang_unsupported_opt_pg_darwin, CLASS_ERROR, (unsigned)diag::Severity::Error, "the clang compiler does not support -pg option on %select{Darwin|versions of OS X 10.9 and later}0", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_command_failed, CLASS_ERROR, (unsigned)diag::Severity::Error, "%0 command failed with exit code %1 (use -v to see invocation)", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_command_failure, CLASS_ERROR, (unsigned)diag::Severity::Error, "unable to execute command: %0", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_command_signalled, CLASS_ERROR, (unsigned)diag::Severity::Error, "%0 command failed due to signal (use -v to see invocation)", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_compilationdatabase, CLASS_ERROR, (unsigned)diag::Severity::Error, "compilation database '%0' could not be opened: %1", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_config_file_not_found, CLASS_ERROR, (unsigned)diag::Severity::Error, "configuration file '%0' cannot be found", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_conflicting_deployment_targets, CLASS_ERROR, (unsigned)diag::Severity::Error, "conflicting deployment targets, both '%0' and '%1' are present in environment", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_cuda_bad_gpu_arch, CLASS_ERROR, (unsigned)diag::Severity::Error, "unsupported CUDA gpu architecture: %0", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_cuda_host_arch, CLASS_ERROR, (unsigned)diag::Severity::Error, "unsupported architecture '%0' for host compilation", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_cuda_offload_only_emit_bc, CLASS_ERROR, (unsigned)diag::Severity::Error, "CUDA offload target is supported only along with --emit-llvm", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_cuda_version_unsupported, CLASS_ERROR, (unsigned)diag::Severity::Error, "GPU arch %0 is supported by CUDA versions between %1 and %2 (inclusive), but installation at %3 is %4; use '--cuda-path' to specify a different CUDA install, pass a different GPU arch with '--cuda-gpu-arch', or pass '--no-cuda-version-check'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_darwin_sdk_missing_arclite, CLASS_ERROR, (unsigned)diag::Severity::Error, "SDK does not contain 'libarclite' at the path '%0'; try increasing the minimum deployment target", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_defsym_invalid_format, CLASS_ERROR, (unsigned)diag::Severity::Error, "defsym must be of the form: sym=value: %0", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_defsym_invalid_symval, CLASS_ERROR, (unsigned)diag::Severity::Error, "value is not an integer: %0", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_duplicate_config, CLASS_ERROR, (unsigned)diag::Severity::Error, "no more than one option '--config' is allowed", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_dxc_missing_target_profile, CLASS_ERROR, (unsigned)diag::Severity::Error, "target profile option (-T) is missing", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_emit_llvm_link, CLASS_ERROR, (unsigned)diag::Severity::Error, "-emit-llvm cannot be used when linking", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_expand_response_file, CLASS_ERROR, (unsigned)diag::Severity::Error, "failed to expand response file: %0", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_expecting_fopenmp_with_fopenmp_targets, CLASS_ERROR, (unsigned)diag::Severity::Error, "'-fopenmp-targets' must be used in conjunction with a '-fopenmp' option compatible with offloading; e.g., '-fopenmp=libomp' or '-fopenmp=libiomp5'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_extract_api_wrong_kind, CLASS_ERROR, (unsigned)diag::Severity::Error, "header file '%0' input '%1' does not match the type of prior input in api extraction; use '-x %2' to override", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_failed_to_deduce_target_from_arch, CLASS_ERROR, (unsigned)diag::Severity::Error, "failed to deduce triple for target architecture '%0'; specify the triple using '-fopenmp-targets' and '-Xopenmp-target' instead.", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_force_crash, CLASS_ERROR, (unsigned)diag::Severity::Error, "failing because %select{environment variable 'FORCE_CLANG_DIAGNOSTICS_CRASH' is set|'-gen-reproducer' is used}0", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_gnustep_objc_runtime_incompatible_binary, CLASS_ERROR, (unsigned)diag::Severity::Error, "GNUstep Objective-C runtime version %0 incompatible with target binary format", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_header_unit_extra_inputs, CLASS_ERROR, (unsigned)diag::Severity::Error, "multiple inputs are not valid for header units (first extra '%0')", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_hipspv_no_hip_path, CLASS_ERROR, (unsigned)diag::Severity::Error, "'--hip-path' must be specified when offloading to SPIR-V%select{| unless %1 is given}0.", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_hlsl_unsupported_target, CLASS_ERROR, (unsigned)diag::Severity::Error, "HLSL code generation is unsupported for target '%0'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_incompatible_omp_arch, CLASS_ERROR, (unsigned)diag::Severity::Error, "OpenMP target architecture '%0' pointer size is incompatible with host '%1'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_incompatible_options, CLASS_ERROR, (unsigned)diag::Severity::Error, "the combination of '%0' and '%1' is incompatible", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_incompatible_unwindlib, CLASS_ERROR, (unsigned)diag::Severity::Error, "--rtlib=libgcc requires --unwindlib=libgcc", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_invalid_Xarch_argument_with_args, CLASS_ERROR, (unsigned)diag::Severity::Error, "invalid Xarch argument: '%0', options requiring arguments are unsupported", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_invalid_Xopenmp_target_with_args, CLASS_ERROR, (unsigned)diag::Severity::Error, "invalid -Xopenmp-target argument: '%0', options requiring arguments are unsupported", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_invalid_arch_name, CLASS_ERROR, (unsigned)diag::Severity::Error, "invalid arch name '%0'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_invalid_argument_to_option, CLASS_ERROR, (unsigned)diag::Severity::Error, "invalid argument '%0' to -%1", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_invalid_cf_runtime_abi, CLASS_ERROR, (unsigned)diag::Severity::Error, "invalid CoreFoundation Runtime ABI '%0'; must be one of 'objc', 'standalone', 'swift', 'swift-5.0', 'swift-4.2', 'swift-4.1'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_invalid_darwin_version, CLASS_ERROR, (unsigned)diag::Severity::Error, "invalid Darwin version number: %0", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_invalid_diagnotics_hotness_threshold, CLASS_ERROR, (unsigned)diag::Severity::Error, "invalid argument in '%0', only integer or 'auto' is supported", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_invalid_diagnotics_misexpect_tolerance, CLASS_ERROR, (unsigned)diag::Severity::Error, "invalid argument in '%0', only integers are supported", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_invalid_directx_shader_module, CLASS_ERROR, (unsigned)diag::Severity::Error, "invalid profile : %0", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_invalid_empty_dxil_validator_version, CLASS_ERROR, (unsigned)diag::Severity::Error, "invalid validator version : %0\nIf validator major version is 0, minor version must also be 0.", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_invalid_format_dxil_validator_version, CLASS_ERROR, (unsigned)diag::Severity::Error, "invalid validator version : %0\nFormat of validator version is \"<major>.<minor>\" (ex:\"1.4\").", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_invalid_gcc_install_dir, CLASS_ERROR, (unsigned)diag::Severity::Error, "'%0' does not contain a GCC installation", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_invalid_gcc_output_type, CLASS_ERROR, (unsigned)diag::Severity::Error, "invalid output type '%0' for use with gcc tool", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_invalid_int_value, CLASS_ERROR, (unsigned)diag::Severity::Error, "invalid integral value '%1' in '%0'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_invalid_linker_name, CLASS_ERROR, (unsigned)diag::Severity::Error, "invalid linker name in argument '%0'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_invalid_malign_branch_EQ, CLASS_ERROR, (unsigned)diag::Severity::Error, "invalid argument '%0' to -malign-branch=; each element must be one of: %1", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_invalid_mfloat_abi, CLASS_ERROR, (unsigned)diag::Severity::Error, "invalid float ABI '%0'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_invalid_mtp, CLASS_ERROR, (unsigned)diag::Severity::Error, "invalid thread pointer reading mode '%0'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_invalid_object_mode, CLASS_ERROR, (unsigned)diag::Severity::Error, "OBJECT_MODE setting %0 is not recognized and is not a valid setting", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_invalid_omp_target, CLASS_ERROR, (unsigned)diag::Severity::Error, "OpenMP target is invalid: '%0'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_invalid_or_unsupported_offload_target, CLASS_ERROR, (unsigned)diag::Severity::Error, "invalid or unsupported offload target: '%0'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_invalid_os_in_arg, CLASS_ERROR, (unsigned)diag::Severity::Error, "invalid OS value '%0' in '%1'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_invalid_output_with_multiple_archs, CLASS_ERROR, (unsigned)diag::Severity::Error, "cannot use '%0' output with multiple -arch options", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_invalid_range_dxil_validator_version, CLASS_ERROR, (unsigned)diag::Severity::Error, "invalid validator version : %0\nValidator version must be less than or equal to current internal version.", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_invalid_remap_file, CLASS_ERROR, (unsigned)diag::Severity::Error, "invalid option '%0' not of the form <from-file>;<to-file>", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_invalid_riscv_arch_name, CLASS_ERROR, (unsigned)diag::Severity::Error, "invalid arch name '%0', %1", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_invalid_riscv_cpu_name_for_target, CLASS_ERROR, (unsigned)diag::Severity::Error, "cpu '%0' does not support rv%select{32|64}1", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_invalid_rtlib_name, CLASS_ERROR, (unsigned)diag::Severity::Error, "invalid runtime library name in argument '%0'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_invalid_stdlib_name, CLASS_ERROR, (unsigned)diag::Severity::Error, "invalid library name in argument '%0'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_invalid_thread_model_for_target, CLASS_ERROR, (unsigned)diag::Severity::Error, "invalid thread model '%0' in '%1' for this target", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_invalid_unwindlib_name, CLASS_ERROR, (unsigned)diag::Severity::Error, "invalid unwind library name in argument '%0'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_invalid_value, CLASS_ERROR, (unsigned)diag::Severity::Error, "invalid value '%1' in '%0'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_invalid_value_with_suggestion, CLASS_ERROR, (unsigned)diag::Severity::Error, "invalid value '%1' in '%0', expected one of: %2", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_invalid_version_number, CLASS_ERROR, (unsigned)diag::Severity::Error, "invalid version number in '%0'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_loongarch_invalid_mfpu_EQ, CLASS_ERROR, (unsigned)diag::Severity::Error, "invalid argument '%0' to -mfpu=; must be one of: 64, 32, none, 0 (alias for none)", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_lto_without_lld, CLASS_ERROR, (unsigned)diag::Severity::Error, "LTO requires -fuse-ld=lld", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_malformed_sanitizer_coverage_allowlist, CLASS_ERROR, (unsigned)diag::Severity::Error, "malformed sanitizer coverage allowlist: '%0'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_malformed_sanitizer_coverage_ignorelist, CLASS_ERROR, (unsigned)diag::Severity::Error, "malformed sanitizer coverage ignorelist: '%0'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_malformed_sanitizer_ignorelist, CLASS_ERROR, (unsigned)diag::Severity::Error, "malformed sanitizer ignorelist: '%0'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_malformed_sanitizer_metadata_ignorelist, CLASS_ERROR, (unsigned)diag::Severity::Error, "malformed sanitizer metadata ignorelist: '%0'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_mg_requires_m_or_mm, CLASS_ERROR, (unsigned)diag::Severity::Error, "option '-MG' requires '-M' or '-MM'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_minws_unsupported_input_type, CLASS_ERROR, (unsigned)diag::Severity::Error, "'-fminimize-whitespace' invalid for input of type %0", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_missing_arg_mtp, CLASS_ERROR, (unsigned)diag::Severity::Error, "missing argument to '%0'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_missing_argument, CLASS_ERROR, (unsigned)diag::Severity::Error, "argument to '%0' is missing (expected %1 value%s1)", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_missing_sanitizer_ignorelist, CLASS_ERROR, (unsigned)diag::Severity::Error, "missing sanitizer ignorelist: '%0'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_mix_cuda_hip, CLASS_ERROR, (unsigned)diag::Severity::Error, "mixed CUDA and HIP compilation is not supported", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_module_header_wrong_kind, CLASS_ERROR, (unsigned)diag::Severity::Error, "header file '%0' input type '%1' does not match type of prior input in module compilation; use '-x %2' to override", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_module_output_with_multiple_arch, CLASS_ERROR, (unsigned)diag::Severity::Error, "option '-fmodule-output' can't be used with multiple arch options", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_modules_validate_once_requires_timestamp, CLASS_ERROR, (unsigned)diag::Severity::Error, "option '-fmodules-validate-once-per-build-session' requires '-fbuild-session-timestamp=<seconds since Epoch>' or '-fbuild-session-file=<file>'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_needs_hvx, CLASS_ERROR, (unsigned)diag::Severity::Error, "%0 requires HVX, use -mhvx/-mhvx= to enable it", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_needs_hvx_version, CLASS_ERROR, (unsigned)diag::Severity::Error, "%0 is not supported on HVX %1", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_negative_columns, CLASS_ERROR, (unsigned)diag::Severity::Error, "invalid value '%1' in '%0', value must be 'none' or a positive integer", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_no_ast_support, CLASS_ERROR, (unsigned)diag::Severity::Error, "'%0': unable to use AST files with this tool", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_no_cuda_installation, CLASS_ERROR, (unsigned)diag::Severity::Error, "cannot find CUDA installation; provide its path via '--cuda-path', or pass '-nocudainc' to build without CUDA includes", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_no_cuda_libdevice, CLASS_ERROR, (unsigned)diag::Severity::Error, "cannot find libdevice for %0; provide path to different CUDA installation via '--cuda-path', or pass '-nocudalib' to build without linking with libdevice", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_no_hip_runtime, CLASS_ERROR, (unsigned)diag::Severity::Error, "cannot find HIP runtime; provide its path via '--rocm-path', or pass '-nogpuinc' to build without HIP runtime", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_no_hipspv_device_lib, CLASS_ERROR, (unsigned)diag::Severity::Error, "cannot find HIP device library%select{| for %1}0; provide its path via '--hip-path' or '--hip-device-lib-path', or pass '-nogpulib' to build without HIP device library", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_no_input_files, CLASS_ERROR, (unsigned)diag::Severity::Error, "no input files", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_no_linker_llvm_support, CLASS_ERROR, (unsigned)diag::Severity::Error, "'%0': unable to pass LLVM bit-code files to linker", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_no_module_support, CLASS_ERROR, (unsigned)diag::Severity::Error, "'%0': unable to use module files with this tool", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_no_neon_modifier, CLASS_ERROR, (unsigned)diag::Severity::Error, "[no]neon is not accepted as modifier, please use [no]simd instead", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_no_rocm_device_lib, CLASS_ERROR, (unsigned)diag::Severity::Error, "cannot find ROCm device library%select{| for %1|for ABI version %1}0; provide its path via '--rocm-path' or '--rocm-device-lib-path', or pass '-nogpulib' to build without ROCm device library", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_no_such_file, CLASS_ERROR, (unsigned)diag::Severity::Error, "no such file or directory: '%0'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_no_such_file_with_suggestion, CLASS_ERROR, (unsigned)diag::Severity::Error, "no such file or directory: '%0'; did you mean '%1'?", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_offload_bad_gpu_arch, CLASS_ERROR, (unsigned)diag::Severity::Error, "unsupported %0 gpu architecture: %1", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_omp_host_ir_file_not_found, CLASS_ERROR, (unsigned)diag::Severity::Error, "provided host compiler IR file '%0' is required to generate code for OpenMP target regions but cannot be found", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_omp_host_target_not_supported, CLASS_ERROR, (unsigned)diag::Severity::Error, "target '%0' is not a supported OpenMP host target", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_omp_offload_target_bcruntime_not_found, CLASS_ERROR, (unsigned)diag::Severity::Error, "bitcode library '%0' does not exist", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_omp_offload_target_cuda_version_not_support, CLASS_ERROR, (unsigned)diag::Severity::Error, "NVPTX target requires CUDA 9.2 or above; CUDA %0 detected", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_omp_offload_target_missingbcruntime, CLASS_ERROR, (unsigned)diag::Severity::Error, "no library '%0' found in the default clang lib directory or in LIBRARY_PATH; use '--libomptarget-%1-bc-path' to specify %1 bitcode library", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_only_one_offload_target_supported, CLASS_ERROR, (unsigned)diag::Severity::Error, "only one offload target is supported", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_optimization_remark_format, CLASS_ERROR, (unsigned)diag::Severity::Error, "unknown remark serializer format: '%0'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_optimization_remark_pattern, CLASS_ERROR, (unsigned)diag::Severity::Error, "in pattern '%1': %0", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_out_file_argument_with_multiple_sources, CLASS_ERROR, (unsigned)diag::Severity::Error, "cannot specify '%0%1' when compiling multiple source files", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_output_argument_with_multiple_files, CLASS_ERROR, (unsigned)diag::Severity::Error, "cannot specify -o when generating multiple output files", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_preamble_format, CLASS_ERROR, (unsigned)diag::Severity::Error, "incorrect format for -preamble-bytes=N,END", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_print_header_env_var, CLASS_ERROR, (unsigned)diag::Severity::Error, "environment variable CC_PRINT_HEADERS_%select{FORMAT|FILTERING}0 has invalid value %1", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_print_header_env_var_combination, CLASS_ERROR, (unsigned)diag::Severity::Error, "unsupported combination: CC_PRINT_HEADERS_FORMAT=%0 and CC_PRINT_HEADERS_FILTERING=%1", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_print_header_env_var_combination_cc1, CLASS_ERROR, (unsigned)diag::Severity::Error, "unsupported combination: -header-include-format=%0 and -header-include-filtering=%1", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_riscv_unsupported_with_linker_relaxation, CLASS_ERROR, (unsigned)diag::Severity::Error, "%0 is unsupported with RISC-V linker relaxation (-mrelax)", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_ropi_incompatible_with_cxx, CLASS_ERROR, (unsigned)diag::Severity::Error, "ROPI is not compatible with c++", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_ropi_rwpi_incompatible_with_pic, CLASS_ERROR, (unsigned)diag::Severity::Error, "embedded and GOT-based position independence are incompatible", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_small_columns, CLASS_ERROR, (unsigned)diag::Severity::Error, "invalid value '%1' in '%0', value must be '%2' or greater", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_ssp_missing_offset_argument, CLASS_ERROR, (unsigned)diag::Severity::Error, "'%0' is used without '-mstack-protector-guard-offset', and there is no default", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_target_variant_invalid, CLASS_ERROR, (unsigned)diag::Severity::Error, "unsupported '%0' value '%1'; use 'ios-macabi' instead", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_trivial_auto_var_init_stop_after_invalid_value, CLASS_ERROR, (unsigned)diag::Severity::Error, "'-ftrivial-auto-var-init-stop-after=*' only accepts positive integers", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_trivial_auto_var_init_stop_after_missing_dependency, CLASS_ERROR, (unsigned)diag::Severity::Error, "'-ftrivial-auto-var-init-stop-after=*' is used without '-ftrivial-auto-var-init=zero' or '-ftrivial-auto-var-init=pattern'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_unable_to_remove_file, CLASS_ERROR, (unsigned)diag::Severity::Error, "unable to remove file: %0", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_unable_to_set_working_directory, CLASS_ERROR, (unsigned)diag::Severity::Error, "unable to set working directory: %0", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_undetermined_gpu_arch, CLASS_ERROR, (unsigned)diag::Severity::Error, "cannot determine %0 architecture: %1; consider passing it via '%2'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_unknown_argument, CLASS_ERROR, (unsigned)diag::Severity::Error, "unknown argument: '%0'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_unknown_argument_with_suggestion, CLASS_ERROR, (unsigned)diag::Severity::Error, "unknown argument '%0'; did you mean '%1'?", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_unknown_indirect_jump_opt, CLASS_ERROR, (unsigned)diag::Severity::Error, "unknown '-mindirect-jump=' option '%0'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_unknown_language, CLASS_ERROR, (unsigned)diag::Severity::Error, "language not recognized: '%0'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_unknown_objc_runtime, CLASS_ERROR, (unsigned)diag::Severity::Error, "unknown or ill-formed Objective-C runtime '%0'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_unknown_stdin_type, CLASS_ERROR, (unsigned)diag::Severity::Error, "-E or -x required when input is from standard input", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_unknown_stdin_type_clang_cl, CLASS_ERROR, (unsigned)diag::Severity::Error, "use /Tc or /Tp to set input type for standard input", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_unknown_target_triple, CLASS_ERROR, (unsigned)diag::Severity::Error, "unknown target triple '%0'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_unsupported_embed_bitcode, CLASS_ERROR, (unsigned)diag::Severity::Error, "%0 is not supported with -fembed-bitcode", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_unsupported_fpatchable_function_entry_argument, CLASS_ERROR, (unsigned)diag::Severity::Error, "the second argument of '-fpatchable-function-entry' must be smaller than the first argument", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_unsupported_indirect_jump_opt, CLASS_ERROR, (unsigned)diag::Severity::Error, "'-mindirect-jump=%0' is unsupported with the '%1' architecture", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_unsupported_noabicalls_pic, CLASS_ERROR, (unsigned)diag::Severity::Error, "position-independent code requires '-mabicalls'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_unsupported_opt, CLASS_ERROR, (unsigned)diag::Severity::Error, "unsupported option '%0'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_unsupported_opt_for_language_mode, CLASS_ERROR, (unsigned)diag::Severity::Error, "unsupported option '%0' for language mode '%1'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_unsupported_opt_for_target, CLASS_ERROR, (unsigned)diag::Severity::Error, "unsupported option '%0' for target '%1'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_unsupported_opt_with_suggestion, CLASS_ERROR, (unsigned)diag::Severity::Error, "unsupported option '%0'; did you mean '%1'?", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_unsupported_option_argument, CLASS_ERROR, (unsigned)diag::Severity::Error, "unsupported argument '%1' to option '%0'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_unsupported_rtlib_for_platform, CLASS_ERROR, (unsigned)diag::Severity::Error, "unsupported runtime library '%0' for platform '%1'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_drv_unsupported_static_sanitizer_darwin, CLASS_ERROR, (unsigned)diag::Severity::Error, "static %0 runtime is not supported on darwin", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_invalid_cxx_abi, CLASS_ERROR, (unsigned)diag::Severity::Error, "invalid C++ ABI name '%0'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_invalid_macos_32bit_deployment_target, CLASS_ERROR, (unsigned)diag::Severity::Error, "32-bit targets are not supported when building for Mac Catalyst", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_no_external_assembler, CLASS_ERROR, (unsigned)diag::Severity::Error, "there is no external assembler that can be used on this platform", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_objc_weak_unsupported, CLASS_ERROR, (unsigned)diag::Severity::Error, "-fobjc-weak is not supported on the current deployment target", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_objc_weak_with_gc, CLASS_ERROR, (unsigned)diag::Severity::Error, "-fobjc-weak is not supported in Objective-C garbage collection", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_roptr_cannot_build_shared, CLASS_ERROR, (unsigned)diag::Severity::Error, "-mxcoff-roptr is not supported with -shared", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_roptr_requires_data_sections, CLASS_ERROR, (unsigned)diag::Severity::Error, "-mxcoff-roptr is supported only with -fdata-sections", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_sls_hardening_arm_not_supported, CLASS_ERROR, (unsigned)diag::Severity::Error, "-mharden-sls is only supported on armv7-a or later", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_stack_tagging_requires_hardware_feature, CLASS_ERROR, (unsigned)diag::Severity::Error, "'-fsanitize=memtag-stack' requires hardware support (+memtag). For Armv8 or Armv9, try compiling with -march=armv8a+memtag or -march=armv9a+memtag", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_target_unsupported_arch, CLASS_ERROR, (unsigned)diag::Severity::Error, "the target architecture '%0' is not supported by the target '%1'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_test_module_file_extension_format, CLASS_ERROR, (unsigned)diag::Severity::Error, "-ftest-module-file-extension argument '%0' is not of the required form 'blockname:major:minor:hashed:user info'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_unsupported_cxx_abi, CLASS_ERROR, (unsigned)diag::Severity::Error, "C++ ABI '%0' is not supported on target triple '%1'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(note_cc1_round_trip_generated, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "generated arguments #%0 in round-trip: %1", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_cc1_round_trip_original, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "original arguments in round-trip: %0", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_drv_address_sanitizer_debug_runtime, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "AddressSanitizer doesn't support linking with debug runtime libraries yet", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_drv_available_multilibs, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "available multilibs are:%0", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_drv_command_failed_diag_msg, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "diagnostic msg: %0", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_drv_config_file_searched_in, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "was searched for in the directory: %0", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_drv_t_option_is_global, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "the last '/TC' or '/TP' option takes precedence over earlier instances", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_drv_use_standard, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "use '%0'%select{| or '%3'|, '%3', or '%4'|, '%3', '%4', or '%5'}2 for '%1' standard", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_drv_verify_prefix_spelling, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "-verify prefixes must start with a letter and contain only alphanumeric characters, hyphens, and underscores", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_use_dashdash, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "use '--' to treat subsequent arguments as filenames", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(remark_cc1_round_trip_generated, CLASS_REMARK, (unsigned)diag::Severity::Ignored, "generated arguments #%0 in round-trip: %1", 756, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_O4_is_O3, CLASS_WARNING, (unsigned)diag::Severity::Warning, "-O4 is equivalent to -O3", 210, SFINAE_Suppress, false, false, true, false, 30)
DIAG(warn_analyzer_deprecated_option, CLASS_WARNING, (unsigned)diag::Severity::Warning, "analyzer option '%0' is deprecated. This flag will be removed in %1, and passing this option will be an error.", 239, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_analyzer_deprecated_option_with_alternative, CLASS_WARNING, (unsigned)diag::Severity::Warning, "analyzer option '%0' is deprecated. This flag will be removed in %1, and passing this option will be an error. Use '%2' instead.", 239, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_c_kext, CLASS_WARNING, (unsigned)diag::Severity::Warning, "ignoring -fapple-kext which is valid for C++ and Objective-C++ only", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_debug_compression_unavailable, CLASS_WARNING, (unsigned)diag::Severity::Warning, "cannot compress debug sections (%0 not enabled)", 200, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_drv_assuming_mfloat_abi_is, CLASS_WARNING, (unsigned)diag::Severity::Warning, "unknown platform, assuming -mfloat-abi=%0", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_drv_avr_family_linking_stdlibs_not_implemented, CLASS_WARNING, (unsigned)diag::Severity::Warning, "support for linking stdlibs for microcontroller '%0' is not implemented", 62, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_drv_avr_libc_not_found, CLASS_WARNING, (unsigned)diag::Severity::Warning, "no avr-libc installation can be found on the system, cannot link standard libraries", 62, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_drv_avr_linker_section_addresses_not_implemented, CLASS_WARNING, (unsigned)diag::Severity::Warning, "support for passing the data section address to the linker for microcontroller '%0' is not implemented", 62, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_drv_avr_mcu_not_specified, CLASS_WARNING, (unsigned)diag::Severity::Warning, "no target microcontroller specified on command line, cannot link standard libraries, please pass -mmcu=<mcu name>", 62, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_drv_avr_stdlib_not_linked, CLASS_WARNING, (unsigned)diag::Severity::Warning, "standard library not linked and so no interrupt vector table or compiler runtime routines will be linked", 62, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_drv_clang_unsupported, CLASS_WARNING, (unsigned)diag::Severity::Warning, "the clang compiler does not support '%0'", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_drv_darwin_sdk_invalid_settings, CLASS_WARNING, (unsigned)diag::Severity::Warning, "SDK settings were ignored as 'SDKSettings.json' could not be parsed", 197, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_drv_deprecated_arg, CLASS_WARNING, (unsigned)diag::Severity::Warning, "argument '%0' is deprecated, use '%1' instead", 210, SFINAE_Suppress, false, false, true, false, 30)
DIAG(warn_drv_diagnostics_hotness_requires_pgo, CLASS_WARNING, (unsigned)diag::Severity::Warning, "argument '%0' requires profile-guided optimization information", 939, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_drv_diagnostics_misexpect_requires_pgo, CLASS_WARNING, (unsigned)diag::Severity::Warning, "argument '%0' requires profile-guided optimization information", 939, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_drv_disabling_vptr_no_rtti_default, CLASS_WARNING, (unsigned)diag::Severity::Warning, "implicitly disabling vptr sanitizer because rtti wasn't enabled", 57, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_drv_dwarf_version_limited_by_target, CLASS_WARNING, (unsigned)diag::Severity::Warning, "debug information option '%0' is not supported; requires DWARF-%2 but target '%1' only provides DWARF-%3", 932, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_drv_dxc_missing_dxv, CLASS_WARNING, (unsigned)diag::Severity::Warning, "dxv not found. Resulting DXIL will not be validated or signed for use in release environments.", 268, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_drv_empty_joined_argument, CLASS_WARNING, (unsigned)diag::Severity::Warning, "joined argument expects additional value: '%0'", 939, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_drv_fine_grained_bitfield_accesses_ignored, CLASS_WARNING, (unsigned)diag::Severity::Warning, "option '-ffine-grained-bitfield-accesses' cannot be enabled together with a sanitizer; flag ignored", 649, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_drv_fjmc_for_elf_only, CLASS_WARNING, (unsigned)diag::Severity::Warning, "-fjmc works only for ELF; option ignored", 649, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_drv_fuse_ld_path, CLASS_WARNING, (unsigned)diag::Severity::Ignored, "'-fuse-ld=' taking a path is deprecated; use '--ld-path=' instead", 337, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_drv_global_isel_incomplete, CLASS_WARNING, (unsigned)diag::Severity::Warning, "-fglobal-isel support for the '%0' architecture is incomplete", 343, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_drv_global_isel_incomplete_opt, CLASS_WARNING, (unsigned)diag::Severity::Warning, "-fglobal-isel support is incomplete for this architecture at the current optimization level", 343, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_drv_input_file_unused, CLASS_WARNING, (unsigned)diag::Severity::Warning, "%0: '%1' input unused%select{ when '%3' is present|}2", 939, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_drv_input_file_unused_by_cpp, CLASS_WARNING, (unsigned)diag::Severity::Warning, "%0: '%1' input unused in cpp mode", 939, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_drv_invalid_arch_name_with_suggestion, CLASS_WARNING, (unsigned)diag::Severity::Warning, "ignoring invalid /arch: argument '%0'; for %select{64|32}1-bit expected one of %2", 939, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_drv_jmc_requires_debuginfo, CLASS_WARNING, (unsigned)diag::Severity::Warning, "%0 requires debug info. Use %1 or debug options that enable debugger's stepping function; option ignored", 649, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_drv_libstdcxx_not_found, CLASS_WARNING, (unsigned)diag::Severity::Warning, "include path for libstdc++ headers not found; pass '-stdlib=libc++' on the command line to use the libc++ standard library instead", 812, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_drv_loongarch_conflicting_implied_val, CLASS_WARNING, (unsigned)diag::Severity::Warning, "ignoring '%0' as it conflicts with that implied by '%1' (%2)", 649, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_drv_missing_multilib, CLASS_WARNING, (unsigned)diag::Severity::Warning, "no multilib found matching flags: %0", 537, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_drv_missing_plugin_arg, CLASS_WARNING, (unsigned)diag::Severity::Warning, "missing plugin argument for plugin %0 in %1", 446, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_drv_missing_plugin_name, CLASS_WARNING, (unsigned)diag::Severity::Warning, "missing plugin name in %0", 446, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_drv_moutline_atomics_unsupported_opt, CLASS_WARNING, (unsigned)diag::Severity::Warning, "'%0' does not support '-%1'; flag ignored", 649, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_drv_moutline_unsupported_opt, CLASS_WARNING, (unsigned)diag::Severity::Warning, "'%0' does not support '-moutline'; flag ignored", 649, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_drv_msp430_hwmult_mismatch, CLASS_WARNING, (unsigned)diag::Severity::Warning, "the given MCU supports %0 hardware multiply, but '-mhwmult' is set to %1", 446, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_drv_msp430_hwmult_no_device, CLASS_WARNING, (unsigned)diag::Severity::Warning, "no MCU device specified, but '-mhwmult' is set to 'auto', assuming no hardware multiply; use '-mmcu' to specify an MSP430 device, or '-mhwmult' to set the hardware multiply type explicitly", 446, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_drv_msp430_hwmult_unsupported, CLASS_WARNING, (unsigned)diag::Severity::Warning, "the given MCU does not support hardware multiply, but '-mhwmult' is set to %0", 446, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_drv_msvc_not_found, CLASS_WARNING, (unsigned)diag::Severity::Warning, "unable to find a Visual Studio installation; try running Clang from a developer command prompt", 559, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_drv_multi_gpu_arch, CLASS_WARNING, (unsigned)diag::Severity::Warning, "multiple %0 architectures are detected: %1; only the first one is used for '%2'", 560, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_drv_needs_hvx, CLASS_WARNING, (unsigned)diag::Severity::Warning, "%0 requires HVX, use -mhvx/-mhvx= to enable it", 649, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_drv_new_cuda_version, CLASS_WARNING, (unsigned)diag::Severity::Warning, "CUDA version%0 is newer than the latest%select{| partially}1 supported version %2", 904, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_drv_no_floating_point_registers, CLASS_WARNING, (unsigned)diag::Severity::Warning, "'%0': selected processor lacks floating point registers", 923, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_drv_object_size_disabled_O0, CLASS_WARNING, (unsigned)diag::Severity::Warning, "the object size sanitizer has no effect at -O0, but is explicitly enabled: %0", 446, SFINAE_Suppress, true, false, true, false, 0)
DIAG(warn_drv_omp_offload_target_duplicate, CLASS_WARNING, (unsigned)diag::Severity::Warning, "OpenMP offloading target '%0' is similar to target '%1' already specified; will be ignored", 648, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_drv_optimization_value, CLASS_WARNING, (unsigned)diag::Severity::Warning, "optimization level '%0' is not supported; using '%1%2' instead", 446, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_drv_overriding_flag_option, CLASS_WARNING, (unsigned)diag::Severity::Warning, "overriding '%0' option with '%1'", 661, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_drv_partially_supported_cuda_version, CLASS_WARNING, (unsigned)diag::Severity::Warning, "CUDA version %0 is only partially supported", 904, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_drv_pch_not_first_include, CLASS_WARNING, (unsigned)diag::Severity::Warning, "precompiled header '%0' was ignored because '%1' is not first '-include'", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_drv_potentially_misspelled_joined_argument, CLASS_WARNING, (unsigned)diag::Severity::Warning, "joined argument treated as '%0'; did you mean '%1'?", 901, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_drv_preprocessed_input_file_unused, CLASS_WARNING, (unsigned)diag::Severity::Warning, "%0: previously preprocessed input%select{ unused when '%2' is present|}1", 939, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_drv_ps_force_pic, CLASS_WARNING, (unsigned)diag::Severity::Warning, "option '%0' was ignored by the %1 toolchain, using '-fPIC'", 649, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_drv_sarif_format_unstable, CLASS_WARNING, (unsigned)diag::Severity::Warning, "diagnostic formatting in SARIF mode is currently unstable", 759, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_drv_treating_input_as_cxx, CLASS_WARNING, (unsigned)diag::Severity::Warning, "treating '%0' input as '%1' when in C++ mode, this behavior is deprecated", 210, SFINAE_Suppress, false, false, true, false, 30)
DIAG(warn_drv_unable_to_find_directory_expected, CLASS_WARNING, (unsigned)diag::Severity::Ignored, "unable to find %0 directory, expected to be in '%1' found via %2", 454, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_drv_unknown_argument_clang_cl, CLASS_WARNING, (unsigned)diag::Severity::Warning, "unknown argument ignored in clang-cl: '%0'", 901, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_drv_unknown_argument_clang_cl_with_suggestion, CLASS_WARNING, (unsigned)diag::Severity::Warning, "unknown argument ignored in clang-cl '%0'; did you mean '%1'?", 901, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_drv_unsupported_debug_info_opt_for_target, CLASS_WARNING, (unsigned)diag::Severity::Warning, "debug information option '%0' is not supported for target '%1'", 932, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_drv_unsupported_diag_option_for_flang, CLASS_WARNING, (unsigned)diag::Severity::Warning, "The warning option '-%0' is not supported", 649, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_drv_unsupported_float_abi_by_lib, CLASS_WARNING, (unsigned)diag::Severity::Warning, "float ABI '%0' is not supported by current library", 923, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_drv_unsupported_gpopt, CLASS_WARNING, (unsigned)diag::Severity::Warning, "ignoring '-mgpopt' option as it cannot be used with %select{|the implicit usage of }0-mabicalls", 930, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_drv_unsupported_longcalls, CLASS_WARNING, (unsigned)diag::Severity::Warning, "ignoring '-mlong-calls' option as it is not currently supported with %select{|the implicit usage of }0-mabicalls", 649, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_drv_unsupported_opt_for_target, CLASS_WARNING, (unsigned)diag::Severity::Warning, "optimization flag '%0' is not supported for target '%1'", 386, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_drv_unsupported_option_for_flang, CLASS_WARNING, (unsigned)diag::Severity::Warning, "the argument '%0' is not supported for option '%1'. Mapping to '%1%2'", 649, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_drv_unsupported_option_for_offload_arch_req_feature, CLASS_WARNING, (unsigned)diag::Severity::Warning, "ignoring '%0' option for offload arch '%1' as it is not currently supported there. Use it with an offload arch containing '%2' instead", 649, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_drv_unsupported_option_for_processor, CLASS_WARNING, (unsigned)diag::Severity::Warning, "ignoring '%0' option as it is not currently supported for processor '%1'", 649, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_drv_unsupported_option_for_target, CLASS_WARNING, (unsigned)diag::Severity::Warning, "ignoring '%0' option as it is not currently supported for target '%1'", 649, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_drv_unsupported_pic_with_mabicalls, CLASS_WARNING, (unsigned)diag::Severity::Warning, "ignoring '%0' option as it cannot be used with %select{implicit usage of|}1 -mabicalls and the N64 ABI", 649, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_drv_unsupported_sdata, CLASS_WARNING, (unsigned)diag::Severity::Warning, "ignoring '-msmall-data-limit=' with -mcmodel=large for -fpic or RV64", 649, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_drv_unused_argument, CLASS_WARNING, (unsigned)diag::Severity::Warning, "argument unused during compilation: '%0'", 939, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_drv_unused_x, CLASS_WARNING, (unsigned)diag::Severity::Warning, "'-x %0' after last input file has no effect", 939, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_drv_yc_multiple_inputs_clang_cl, CLASS_WARNING, (unsigned)diag::Severity::Warning, "support for '/Yc' with more than one source file not implemented yet; flag ignored", 157, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_drv_ycyu_different_arg_clang_cl, CLASS_WARNING, (unsigned)diag::Severity::Warning, "support for '/Yc' and '/Yu' with different filenames not implemented yet; flags ignored", 157, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_ignored_clang_option, CLASS_WARNING, (unsigned)diag::Severity::Warning, "the flag '%0' has been deprecated and will be ignored", 939, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_ignored_gcc_optimization, CLASS_WARNING, (unsigned)diag::Severity::Warning, "optimization flag '%0' is not supported", 386, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_ignoring_fdiscard_for_bitcode, CLASS_WARNING, (unsigned)diag::Severity::Warning, "ignoring -fdiscard-value-names for LLVM Bitcode", 939, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_ignoring_ftabstop_value, CLASS_WARNING, (unsigned)diag::Severity::Warning, "ignoring invalid -ftabstop value '%0', using default value %1", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_ignoring_verify_debuginfo_preserve_export, CLASS_WARNING, (unsigned)diag::Severity::Warning, "ignoring -fverify-debuginfo-preserve-export=%0 because -fverify-debuginfo-preserve wasn't enabled", 939, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_incompatible_sysroot, CLASS_WARNING, (unsigned)diag::Severity::Warning, "using sysroot for '%0' but targeting '%1'", 420, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_invalid_ios_deployment_target, CLASS_WARNING, (unsigned)diag::Severity::Error, "invalid iOS deployment version '%0', iOS 10 is the maximum deployment target for 32-bit targets", 450, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_missing_sysroot, CLASS_WARNING, (unsigned)diag::Severity::Warning, "no such sysroot directory: '%0'", 543, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_slash_u_filename, CLASS_WARNING, (unsigned)diag::Severity::Warning, "'/U%0' treated as the '/U' option", 797, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_target_override_arm64ec, CLASS_WARNING, (unsigned)diag::Severity::Warning, "/arm64EC has been overridden by specified target: %0; option ignored", 649, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_target_unsupported_abs2008, CLASS_WARNING, (unsigned)diag::Severity::Warning, "ignoring '-mabs=2008' option because the '%0' architecture does not support it", 924, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_target_unsupported_abslegacy, CLASS_WARNING, (unsigned)diag::Severity::Warning, "ignoring '-mabs=legacy' option because the '%0' architecture does not support it", 924, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_target_unsupported_compact_branches, CLASS_WARNING, (unsigned)diag::Severity::Warning, "ignoring '-mcompact-branches=' option because the '%0' architecture does not support it", 926, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_target_unsupported_extension, CLASS_WARNING, (unsigned)diag::Severity::Warning, "ignoring extension '%0' because the '%1' architecture does not support it", 446, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_target_unsupported_nan2008, CLASS_WARNING, (unsigned)diag::Severity::Warning, "ignoring '-mnan=2008' option because the '%0' architecture does not support it", 931, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_target_unsupported_nanlegacy, CLASS_WARNING, (unsigned)diag::Severity::Warning, "ignoring '-mnan=legacy' option because the '%0' architecture does not support it", 931, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_unsupported_branch_protection, CLASS_WARNING, (unsigned)diag::Severity::Warning, "invalid branch protection option '%0' in '%1'", 81, SFINAE_Suppress, false, false, true, false, 0)
