BUILTIN(__builtin_arm_mve_asrl, "", "n")
BUILTIN(__builtin_arm_mve_lsll, "", "n")
BUILTIN(__builtin_arm_mve_sqrshr, "", "n")
BUILTIN(__builtin_arm_mve_sqrshrl, "", "n")
BUILTIN(__builtin_arm_mve_sqrshrl_sat48, "", "n")
BUILTIN(__builtin_arm_mve_sqshl, "", "n")
BUILTIN(__builtin_arm_mve_sqshll, "", "n")
BUILTIN(__builtin_arm_mve_srshr, "", "n")
BUILTIN(__builtin_arm_mve_srshrl, "", "n")
BUILTIN(__builtin_arm_mve_uqrshl, "", "n")
BUILTIN(__builtin_arm_mve_uqrshll, "", "n")
BUILTIN(__builtin_arm_mve_uqrshll_sat48, "", "n")
BUILTIN(__builtin_arm_mve_uqshl, "", "n")
BUILTIN(__builtin_arm_mve_uqshll, "", "n")
BUILTIN(__builtin_arm_mve_urshr, "", "n")
BUILTIN(__builtin_arm_mve_urshrl, "", "n")
BUILTIN(__builtin_arm_mve_vabavq_p_s16, "", "n")
BUILTIN(__builtin_arm_mve_vabavq_p_s32, "", "n")
BUILTIN(__builtin_arm_mve_vabavq_p_s8, "", "n")
BUILTIN(__builtin_arm_mve_vabavq_p_u16, "", "n")
BUILTIN(__builtin_arm_mve_vabavq_p_u32, "", "n")
BUILTIN(__builtin_arm_mve_vabavq_p_u8, "", "n")
BUILTIN(__builtin_arm_mve_vabavq_s16, "", "n")
BUILTIN(__builtin_arm_mve_vabavq_s32, "", "n")
BUILTIN(__builtin_arm_mve_vabavq_s8, "", "n")
BUILTIN(__builtin_arm_mve_vabavq_u16, "", "n")
BUILTIN(__builtin_arm_mve_vabavq_u32, "", "n")
BUILTIN(__builtin_arm_mve_vabavq_u8, "", "n")
BUILTIN(__builtin_arm_mve_vabdq_f16, "", "n")
BUILTIN(__builtin_arm_mve_vabdq_f32, "", "n")
BUILTIN(__builtin_arm_mve_vabdq_m_f16, "", "n")
BUILTIN(__builtin_arm_mve_vabdq_m_f32, "", "n")
BUILTIN(__builtin_arm_mve_vabdq_m_s16, "", "n")
BUILTIN(__builtin_arm_mve_vabdq_m_s32, "", "n")
BUILTIN(__builtin_arm_mve_vabdq_m_s8, "", "n")
BUILTIN(__builtin_arm_mve_vabdq_m_u16, "", "n")
BUILTIN(__builtin_arm_mve_vabdq_m_u32, "", "n")
BUILTIN(__builtin_arm_mve_vabdq_m_u8, "", "n")
BUILTIN(__builtin_arm_mve_vabdq_s16, "", "n")
BUILTIN(__builtin_arm_mve_vabdq_s32, "", "n")
BUILTIN(__builtin_arm_mve_vabdq_s8, "", "n")
BUILTIN(__builtin_arm_mve_vabdq_u16, "", "n")
BUILTIN(__builtin_arm_mve_vabdq_u32, "", "n")
BUILTIN(__builtin_arm_mve_vabdq_u8, "", "n")
BUILTIN(__builtin_arm_mve_vabdq_x_f16, "", "n")
BUILTIN(__builtin_arm_mve_vabdq_x_f32, "", "n")
BUILTIN(__builtin_arm_mve_vabdq_x_s16, "", "n")
BUILTIN(__builtin_arm_mve_vabdq_x_s32, "", "n")
BUILTIN(__builtin_arm_mve_vabdq_x_s8, "", "n")
BUILTIN(__builtin_arm_mve_vabdq_x_u16, "", "n")
BUILTIN(__builtin_arm_mve_vabdq_x_u32, "", "n")
BUILTIN(__builtin_arm_mve_vabdq_x_u8, "", "n")
BUILTIN(__builtin_arm_mve_vabsq_f16, "", "n")
BUILTIN(__builtin_arm_mve_vabsq_f32, "", "n")
BUILTIN(__builtin_arm_mve_vabsq_m_f16, "", "n")
BUILTIN(__builtin_arm_mve_vabsq_m_f32, "", "n")
BUILTIN(__builtin_arm_mve_vabsq_m_s16, "", "n")
BUILTIN(__builtin_arm_mve_vabsq_m_s32, "", "n")
BUILTIN(__builtin_arm_mve_vabsq_m_s8, "", "n")
BUILTIN(__builtin_arm_mve_vabsq_s16, "", "n")
BUILTIN(__builtin_arm_mve_vabsq_s32, "", "n")
BUILTIN(__builtin_arm_mve_vabsq_s8, "", "n")
BUILTIN(__builtin_arm_mve_vabsq_x_f16, "", "n")
BUILTIN(__builtin_arm_mve_vabsq_x_f32, "", "n")
BUILTIN(__builtin_arm_mve_vabsq_x_s16, "", "n")
BUILTIN(__builtin_arm_mve_vabsq_x_s32, "", "n")
BUILTIN(__builtin_arm_mve_vabsq_x_s8, "", "n")
BUILTIN(__builtin_arm_mve_vadciq_m_s32, "", "n")
BUILTIN(__builtin_arm_mve_vadciq_m_u32, "", "n")
BUILTIN(__builtin_arm_mve_vadciq_s32, "", "n")
BUILTIN(__builtin_arm_mve_vadciq_u32, "", "n")
BUILTIN(__builtin_arm_mve_vadcq_m_s32, "", "n")
BUILTIN(__builtin_arm_mve_vadcq_m_u32, "", "n")
BUILTIN(__builtin_arm_mve_vadcq_s32, "", "n")
BUILTIN(__builtin_arm_mve_vadcq_u32, "", "n")
BUILTIN(__builtin_arm_mve_vaddlvaq_p_s32, "", "n")
BUILTIN(__builtin_arm_mve_vaddlvaq_p_u32, "", "n")
BUILTIN(__builtin_arm_mve_vaddlvaq_s32, "", "n")
BUILTIN(__builtin_arm_mve_vaddlvaq_u32, "", "n")
BUILTIN(__builtin_arm_mve_vaddlvq_p_s32, "", "n")
BUILTIN(__builtin_arm_mve_vaddlvq_p_u32, "", "n")
BUILTIN(__builtin_arm_mve_vaddlvq_s32, "", "n")
BUILTIN(__builtin_arm_mve_vaddlvq_u32, "", "n")
BUILTIN(__builtin_arm_mve_vaddq_f16, "", "n")
BUILTIN(__builtin_arm_mve_vaddq_f32, "", "n")
BUILTIN(__builtin_arm_mve_vaddq_m_f16, "", "n")
BUILTIN(__builtin_arm_mve_vaddq_m_f32, "", "n")
BUILTIN(__builtin_arm_mve_vaddq_m_n_f16, "", "n")
BUILTIN(__builtin_arm_mve_vaddq_m_n_f32, "", "n")
BUILTIN(__builtin_arm_mve_vaddq_m_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vaddq_m_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vaddq_m_n_s8, "", "n")
BUILTIN(__builtin_arm_mve_vaddq_m_n_u16, "", "n")
BUILTIN(__builtin_arm_mve_vaddq_m_n_u32, "", "n")
BUILTIN(__builtin_arm_mve_vaddq_m_n_u8, "", "n")
BUILTIN(__builtin_arm_mve_vaddq_m_s16, "", "n")
BUILTIN(__builtin_arm_mve_vaddq_m_s32, "", "n")
BUILTIN(__builtin_arm_mve_vaddq_m_s8, "", "n")
BUILTIN(__builtin_arm_mve_vaddq_m_u16, "", "n")
BUILTIN(__builtin_arm_mve_vaddq_m_u32, "", "n")
BUILTIN(__builtin_arm_mve_vaddq_m_u8, "", "n")
BUILTIN(__builtin_arm_mve_vaddq_n_f16, "", "n")
BUILTIN(__builtin_arm_mve_vaddq_n_f32, "", "n")
BUILTIN(__builtin_arm_mve_vaddq_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vaddq_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vaddq_n_s8, "", "n")
BUILTIN(__builtin_arm_mve_vaddq_n_u16, "", "n")
BUILTIN(__builtin_arm_mve_vaddq_n_u32, "", "n")
BUILTIN(__builtin_arm_mve_vaddq_n_u8, "", "n")
BUILTIN(__builtin_arm_mve_vaddq_s16, "", "n")
BUILTIN(__builtin_arm_mve_vaddq_s32, "", "n")
BUILTIN(__builtin_arm_mve_vaddq_s8, "", "n")
BUILTIN(__builtin_arm_mve_vaddq_u16, "", "n")
BUILTIN(__builtin_arm_mve_vaddq_u32, "", "n")
BUILTIN(__builtin_arm_mve_vaddq_u8, "", "n")
BUILTIN(__builtin_arm_mve_vaddq_x_f16, "", "n")
BUILTIN(__builtin_arm_mve_vaddq_x_f32, "", "n")
BUILTIN(__builtin_arm_mve_vaddq_x_n_f16, "", "n")
BUILTIN(__builtin_arm_mve_vaddq_x_n_f32, "", "n")
BUILTIN(__builtin_arm_mve_vaddq_x_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vaddq_x_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vaddq_x_n_s8, "", "n")
BUILTIN(__builtin_arm_mve_vaddq_x_n_u16, "", "n")
BUILTIN(__builtin_arm_mve_vaddq_x_n_u32, "", "n")
BUILTIN(__builtin_arm_mve_vaddq_x_n_u8, "", "n")
BUILTIN(__builtin_arm_mve_vaddq_x_s16, "", "n")
BUILTIN(__builtin_arm_mve_vaddq_x_s32, "", "n")
BUILTIN(__builtin_arm_mve_vaddq_x_s8, "", "n")
BUILTIN(__builtin_arm_mve_vaddq_x_u16, "", "n")
BUILTIN(__builtin_arm_mve_vaddq_x_u32, "", "n")
BUILTIN(__builtin_arm_mve_vaddq_x_u8, "", "n")
BUILTIN(__builtin_arm_mve_vaddvaq_p_s16, "", "n")
BUILTIN(__builtin_arm_mve_vaddvaq_p_s32, "", "n")
BUILTIN(__builtin_arm_mve_vaddvaq_p_s8, "", "n")
BUILTIN(__builtin_arm_mve_vaddvaq_p_u16, "", "n")
BUILTIN(__builtin_arm_mve_vaddvaq_p_u32, "", "n")
BUILTIN(__builtin_arm_mve_vaddvaq_p_u8, "", "n")
BUILTIN(__builtin_arm_mve_vaddvaq_s16, "", "n")
BUILTIN(__builtin_arm_mve_vaddvaq_s32, "", "n")
BUILTIN(__builtin_arm_mve_vaddvaq_s8, "", "n")
BUILTIN(__builtin_arm_mve_vaddvaq_u16, "", "n")
BUILTIN(__builtin_arm_mve_vaddvaq_u32, "", "n")
BUILTIN(__builtin_arm_mve_vaddvaq_u8, "", "n")
BUILTIN(__builtin_arm_mve_vaddvq_p_s16, "", "n")
BUILTIN(__builtin_arm_mve_vaddvq_p_s32, "", "n")
BUILTIN(__builtin_arm_mve_vaddvq_p_s8, "", "n")
BUILTIN(__builtin_arm_mve_vaddvq_p_u16, "", "n")
BUILTIN(__builtin_arm_mve_vaddvq_p_u32, "", "n")
BUILTIN(__builtin_arm_mve_vaddvq_p_u8, "", "n")
BUILTIN(__builtin_arm_mve_vaddvq_s16, "", "n")
BUILTIN(__builtin_arm_mve_vaddvq_s32, "", "n")
BUILTIN(__builtin_arm_mve_vaddvq_s8, "", "n")
BUILTIN(__builtin_arm_mve_vaddvq_u16, "", "n")
BUILTIN(__builtin_arm_mve_vaddvq_u32, "", "n")
BUILTIN(__builtin_arm_mve_vaddvq_u8, "", "n")
BUILTIN(__builtin_arm_mve_vandq_f16, "", "n")
BUILTIN(__builtin_arm_mve_vandq_f32, "", "n")
BUILTIN(__builtin_arm_mve_vandq_m_f16, "", "n")
BUILTIN(__builtin_arm_mve_vandq_m_f32, "", "n")
BUILTIN(__builtin_arm_mve_vandq_m_s16, "", "n")
BUILTIN(__builtin_arm_mve_vandq_m_s32, "", "n")
BUILTIN(__builtin_arm_mve_vandq_m_s8, "", "n")
BUILTIN(__builtin_arm_mve_vandq_m_u16, "", "n")
BUILTIN(__builtin_arm_mve_vandq_m_u32, "", "n")
BUILTIN(__builtin_arm_mve_vandq_m_u8, "", "n")
BUILTIN(__builtin_arm_mve_vandq_s16, "", "n")
BUILTIN(__builtin_arm_mve_vandq_s32, "", "n")
BUILTIN(__builtin_arm_mve_vandq_s8, "", "n")
BUILTIN(__builtin_arm_mve_vandq_u16, "", "n")
BUILTIN(__builtin_arm_mve_vandq_u32, "", "n")
BUILTIN(__builtin_arm_mve_vandq_u8, "", "n")
BUILTIN(__builtin_arm_mve_vandq_x_f16, "", "n")
BUILTIN(__builtin_arm_mve_vandq_x_f32, "", "n")
BUILTIN(__builtin_arm_mve_vandq_x_s16, "", "n")
BUILTIN(__builtin_arm_mve_vandq_x_s32, "", "n")
BUILTIN(__builtin_arm_mve_vandq_x_s8, "", "n")
BUILTIN(__builtin_arm_mve_vandq_x_u16, "", "n")
BUILTIN(__builtin_arm_mve_vandq_x_u32, "", "n")
BUILTIN(__builtin_arm_mve_vandq_x_u8, "", "n")
BUILTIN(__builtin_arm_mve_vbicq_f16, "", "n")
BUILTIN(__builtin_arm_mve_vbicq_f32, "", "n")
BUILTIN(__builtin_arm_mve_vbicq_m_f16, "", "n")
BUILTIN(__builtin_arm_mve_vbicq_m_f32, "", "n")
BUILTIN(__builtin_arm_mve_vbicq_m_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vbicq_m_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vbicq_m_n_u16, "", "n")
BUILTIN(__builtin_arm_mve_vbicq_m_n_u32, "", "n")
BUILTIN(__builtin_arm_mve_vbicq_m_s16, "", "n")
BUILTIN(__builtin_arm_mve_vbicq_m_s32, "", "n")
BUILTIN(__builtin_arm_mve_vbicq_m_s8, "", "n")
BUILTIN(__builtin_arm_mve_vbicq_m_u16, "", "n")
BUILTIN(__builtin_arm_mve_vbicq_m_u32, "", "n")
BUILTIN(__builtin_arm_mve_vbicq_m_u8, "", "n")
BUILTIN(__builtin_arm_mve_vbicq_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vbicq_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vbicq_n_u16, "", "n")
BUILTIN(__builtin_arm_mve_vbicq_n_u32, "", "n")
BUILTIN(__builtin_arm_mve_vbicq_s16, "", "n")
BUILTIN(__builtin_arm_mve_vbicq_s32, "", "n")
BUILTIN(__builtin_arm_mve_vbicq_s8, "", "n")
BUILTIN(__builtin_arm_mve_vbicq_u16, "", "n")
BUILTIN(__builtin_arm_mve_vbicq_u32, "", "n")
BUILTIN(__builtin_arm_mve_vbicq_u8, "", "n")
BUILTIN(__builtin_arm_mve_vbicq_x_f16, "", "n")
BUILTIN(__builtin_arm_mve_vbicq_x_f32, "", "n")
BUILTIN(__builtin_arm_mve_vbicq_x_s16, "", "n")
BUILTIN(__builtin_arm_mve_vbicq_x_s32, "", "n")
BUILTIN(__builtin_arm_mve_vbicq_x_s8, "", "n")
BUILTIN(__builtin_arm_mve_vbicq_x_u16, "", "n")
BUILTIN(__builtin_arm_mve_vbicq_x_u32, "", "n")
BUILTIN(__builtin_arm_mve_vbicq_x_u8, "", "n")
BUILTIN(__builtin_arm_mve_vbrsrq_m_n_f16, "", "n")
BUILTIN(__builtin_arm_mve_vbrsrq_m_n_f32, "", "n")
BUILTIN(__builtin_arm_mve_vbrsrq_m_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vbrsrq_m_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vbrsrq_m_n_s8, "", "n")
BUILTIN(__builtin_arm_mve_vbrsrq_m_n_u16, "", "n")
BUILTIN(__builtin_arm_mve_vbrsrq_m_n_u32, "", "n")
BUILTIN(__builtin_arm_mve_vbrsrq_m_n_u8, "", "n")
BUILTIN(__builtin_arm_mve_vbrsrq_n_f16, "", "n")
BUILTIN(__builtin_arm_mve_vbrsrq_n_f32, "", "n")
BUILTIN(__builtin_arm_mve_vbrsrq_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vbrsrq_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vbrsrq_n_s8, "", "n")
BUILTIN(__builtin_arm_mve_vbrsrq_n_u16, "", "n")
BUILTIN(__builtin_arm_mve_vbrsrq_n_u32, "", "n")
BUILTIN(__builtin_arm_mve_vbrsrq_n_u8, "", "n")
BUILTIN(__builtin_arm_mve_vbrsrq_x_n_f16, "", "n")
BUILTIN(__builtin_arm_mve_vbrsrq_x_n_f32, "", "n")
BUILTIN(__builtin_arm_mve_vbrsrq_x_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vbrsrq_x_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vbrsrq_x_n_s8, "", "n")
BUILTIN(__builtin_arm_mve_vbrsrq_x_n_u16, "", "n")
BUILTIN(__builtin_arm_mve_vbrsrq_x_n_u32, "", "n")
BUILTIN(__builtin_arm_mve_vbrsrq_x_n_u8, "", "n")
BUILTIN(__builtin_arm_mve_vcaddq_rot270_f16, "", "n")
BUILTIN(__builtin_arm_mve_vcaddq_rot270_f32, "", "n")
BUILTIN(__builtin_arm_mve_vcaddq_rot270_m_f16, "", "n")
BUILTIN(__builtin_arm_mve_vcaddq_rot270_m_f32, "", "n")
BUILTIN(__builtin_arm_mve_vcaddq_rot270_m_s16, "", "n")
BUILTIN(__builtin_arm_mve_vcaddq_rot270_m_s32, "", "n")
BUILTIN(__builtin_arm_mve_vcaddq_rot270_m_s8, "", "n")
BUILTIN(__builtin_arm_mve_vcaddq_rot270_m_u16, "", "n")
BUILTIN(__builtin_arm_mve_vcaddq_rot270_m_u32, "", "n")
BUILTIN(__builtin_arm_mve_vcaddq_rot270_m_u8, "", "n")
BUILTIN(__builtin_arm_mve_vcaddq_rot270_s16, "", "n")
BUILTIN(__builtin_arm_mve_vcaddq_rot270_s32, "", "n")
BUILTIN(__builtin_arm_mve_vcaddq_rot270_s8, "", "n")
BUILTIN(__builtin_arm_mve_vcaddq_rot270_u16, "", "n")
BUILTIN(__builtin_arm_mve_vcaddq_rot270_u32, "", "n")
BUILTIN(__builtin_arm_mve_vcaddq_rot270_u8, "", "n")
BUILTIN(__builtin_arm_mve_vcaddq_rot270_x_f16, "", "n")
BUILTIN(__builtin_arm_mve_vcaddq_rot270_x_f32, "", "n")
BUILTIN(__builtin_arm_mve_vcaddq_rot270_x_s16, "", "n")
BUILTIN(__builtin_arm_mve_vcaddq_rot270_x_s32, "", "n")
BUILTIN(__builtin_arm_mve_vcaddq_rot270_x_s8, "", "n")
BUILTIN(__builtin_arm_mve_vcaddq_rot270_x_u16, "", "n")
BUILTIN(__builtin_arm_mve_vcaddq_rot270_x_u32, "", "n")
BUILTIN(__builtin_arm_mve_vcaddq_rot270_x_u8, "", "n")
BUILTIN(__builtin_arm_mve_vcaddq_rot90_f16, "", "n")
BUILTIN(__builtin_arm_mve_vcaddq_rot90_f32, "", "n")
BUILTIN(__builtin_arm_mve_vcaddq_rot90_m_f16, "", "n")
BUILTIN(__builtin_arm_mve_vcaddq_rot90_m_f32, "", "n")
BUILTIN(__builtin_arm_mve_vcaddq_rot90_m_s16, "", "n")
BUILTIN(__builtin_arm_mve_vcaddq_rot90_m_s32, "", "n")
BUILTIN(__builtin_arm_mve_vcaddq_rot90_m_s8, "", "n")
BUILTIN(__builtin_arm_mve_vcaddq_rot90_m_u16, "", "n")
BUILTIN(__builtin_arm_mve_vcaddq_rot90_m_u32, "", "n")
BUILTIN(__builtin_arm_mve_vcaddq_rot90_m_u8, "", "n")
BUILTIN(__builtin_arm_mve_vcaddq_rot90_s16, "", "n")
BUILTIN(__builtin_arm_mve_vcaddq_rot90_s32, "", "n")
BUILTIN(__builtin_arm_mve_vcaddq_rot90_s8, "", "n")
BUILTIN(__builtin_arm_mve_vcaddq_rot90_u16, "", "n")
BUILTIN(__builtin_arm_mve_vcaddq_rot90_u32, "", "n")
BUILTIN(__builtin_arm_mve_vcaddq_rot90_u8, "", "n")
BUILTIN(__builtin_arm_mve_vcaddq_rot90_x_f16, "", "n")
BUILTIN(__builtin_arm_mve_vcaddq_rot90_x_f32, "", "n")
BUILTIN(__builtin_arm_mve_vcaddq_rot90_x_s16, "", "n")
BUILTIN(__builtin_arm_mve_vcaddq_rot90_x_s32, "", "n")
BUILTIN(__builtin_arm_mve_vcaddq_rot90_x_s8, "", "n")
BUILTIN(__builtin_arm_mve_vcaddq_rot90_x_u16, "", "n")
BUILTIN(__builtin_arm_mve_vcaddq_rot90_x_u32, "", "n")
BUILTIN(__builtin_arm_mve_vcaddq_rot90_x_u8, "", "n")
BUILTIN(__builtin_arm_mve_vclsq_m_s16, "", "n")
BUILTIN(__builtin_arm_mve_vclsq_m_s32, "", "n")
BUILTIN(__builtin_arm_mve_vclsq_m_s8, "", "n")
BUILTIN(__builtin_arm_mve_vclsq_s16, "", "n")
BUILTIN(__builtin_arm_mve_vclsq_s32, "", "n")
BUILTIN(__builtin_arm_mve_vclsq_s8, "", "n")
BUILTIN(__builtin_arm_mve_vclsq_x_s16, "", "n")
BUILTIN(__builtin_arm_mve_vclsq_x_s32, "", "n")
BUILTIN(__builtin_arm_mve_vclsq_x_s8, "", "n")
BUILTIN(__builtin_arm_mve_vclzq_m_s16, "", "n")
BUILTIN(__builtin_arm_mve_vclzq_m_s32, "", "n")
BUILTIN(__builtin_arm_mve_vclzq_m_s8, "", "n")
BUILTIN(__builtin_arm_mve_vclzq_m_u16, "", "n")
BUILTIN(__builtin_arm_mve_vclzq_m_u32, "", "n")
BUILTIN(__builtin_arm_mve_vclzq_m_u8, "", "n")
BUILTIN(__builtin_arm_mve_vclzq_s16, "", "n")
BUILTIN(__builtin_arm_mve_vclzq_s32, "", "n")
BUILTIN(__builtin_arm_mve_vclzq_s8, "", "n")
BUILTIN(__builtin_arm_mve_vclzq_u16, "", "n")
BUILTIN(__builtin_arm_mve_vclzq_u32, "", "n")
BUILTIN(__builtin_arm_mve_vclzq_u8, "", "n")
BUILTIN(__builtin_arm_mve_vclzq_x_s16, "", "n")
BUILTIN(__builtin_arm_mve_vclzq_x_s32, "", "n")
BUILTIN(__builtin_arm_mve_vclzq_x_s8, "", "n")
BUILTIN(__builtin_arm_mve_vclzq_x_u16, "", "n")
BUILTIN(__builtin_arm_mve_vclzq_x_u32, "", "n")
BUILTIN(__builtin_arm_mve_vclzq_x_u8, "", "n")
BUILTIN(__builtin_arm_mve_vcmlaq_f16, "", "n")
BUILTIN(__builtin_arm_mve_vcmlaq_f32, "", "n")
BUILTIN(__builtin_arm_mve_vcmlaq_m_f16, "", "n")
BUILTIN(__builtin_arm_mve_vcmlaq_m_f32, "", "n")
BUILTIN(__builtin_arm_mve_vcmlaq_rot180_f16, "", "n")
BUILTIN(__builtin_arm_mve_vcmlaq_rot180_f32, "", "n")
BUILTIN(__builtin_arm_mve_vcmlaq_rot180_m_f16, "", "n")
BUILTIN(__builtin_arm_mve_vcmlaq_rot180_m_f32, "", "n")
BUILTIN(__builtin_arm_mve_vcmlaq_rot270_f16, "", "n")
BUILTIN(__builtin_arm_mve_vcmlaq_rot270_f32, "", "n")
BUILTIN(__builtin_arm_mve_vcmlaq_rot270_m_f16, "", "n")
BUILTIN(__builtin_arm_mve_vcmlaq_rot270_m_f32, "", "n")
BUILTIN(__builtin_arm_mve_vcmlaq_rot90_f16, "", "n")
BUILTIN(__builtin_arm_mve_vcmlaq_rot90_f32, "", "n")
BUILTIN(__builtin_arm_mve_vcmlaq_rot90_m_f16, "", "n")
BUILTIN(__builtin_arm_mve_vcmlaq_rot90_m_f32, "", "n")
BUILTIN(__builtin_arm_mve_vcmpcsq_m_n_u16, "", "n")
BUILTIN(__builtin_arm_mve_vcmpcsq_m_n_u32, "", "n")
BUILTIN(__builtin_arm_mve_vcmpcsq_m_n_u8, "", "n")
BUILTIN(__builtin_arm_mve_vcmpcsq_m_u16, "", "n")
BUILTIN(__builtin_arm_mve_vcmpcsq_m_u32, "", "n")
BUILTIN(__builtin_arm_mve_vcmpcsq_m_u8, "", "n")
BUILTIN(__builtin_arm_mve_vcmpcsq_n_u16, "", "n")
BUILTIN(__builtin_arm_mve_vcmpcsq_n_u32, "", "n")
BUILTIN(__builtin_arm_mve_vcmpcsq_n_u8, "", "n")
BUILTIN(__builtin_arm_mve_vcmpcsq_u16, "", "n")
BUILTIN(__builtin_arm_mve_vcmpcsq_u32, "", "n")
BUILTIN(__builtin_arm_mve_vcmpcsq_u8, "", "n")
BUILTIN(__builtin_arm_mve_vcmpeqq_f16, "", "n")
BUILTIN(__builtin_arm_mve_vcmpeqq_f32, "", "n")
BUILTIN(__builtin_arm_mve_vcmpeqq_m_f16, "", "n")
BUILTIN(__builtin_arm_mve_vcmpeqq_m_f32, "", "n")
BUILTIN(__builtin_arm_mve_vcmpeqq_m_n_f16, "", "n")
BUILTIN(__builtin_arm_mve_vcmpeqq_m_n_f32, "", "n")
BUILTIN(__builtin_arm_mve_vcmpeqq_m_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vcmpeqq_m_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vcmpeqq_m_n_s8, "", "n")
BUILTIN(__builtin_arm_mve_vcmpeqq_m_n_u16, "", "n")
BUILTIN(__builtin_arm_mve_vcmpeqq_m_n_u32, "", "n")
BUILTIN(__builtin_arm_mve_vcmpeqq_m_n_u8, "", "n")
BUILTIN(__builtin_arm_mve_vcmpeqq_m_s16, "", "n")
BUILTIN(__builtin_arm_mve_vcmpeqq_m_s32, "", "n")
BUILTIN(__builtin_arm_mve_vcmpeqq_m_s8, "", "n")
BUILTIN(__builtin_arm_mve_vcmpeqq_m_u16, "", "n")
BUILTIN(__builtin_arm_mve_vcmpeqq_m_u32, "", "n")
BUILTIN(__builtin_arm_mve_vcmpeqq_m_u8, "", "n")
BUILTIN(__builtin_arm_mve_vcmpeqq_n_f16, "", "n")
BUILTIN(__builtin_arm_mve_vcmpeqq_n_f32, "", "n")
BUILTIN(__builtin_arm_mve_vcmpeqq_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vcmpeqq_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vcmpeqq_n_s8, "", "n")
BUILTIN(__builtin_arm_mve_vcmpeqq_n_u16, "", "n")
BUILTIN(__builtin_arm_mve_vcmpeqq_n_u32, "", "n")
BUILTIN(__builtin_arm_mve_vcmpeqq_n_u8, "", "n")
BUILTIN(__builtin_arm_mve_vcmpeqq_s16, "", "n")
BUILTIN(__builtin_arm_mve_vcmpeqq_s32, "", "n")
BUILTIN(__builtin_arm_mve_vcmpeqq_s8, "", "n")
BUILTIN(__builtin_arm_mve_vcmpeqq_u16, "", "n")
BUILTIN(__builtin_arm_mve_vcmpeqq_u32, "", "n")
BUILTIN(__builtin_arm_mve_vcmpeqq_u8, "", "n")
BUILTIN(__builtin_arm_mve_vcmpgeq_f16, "", "n")
BUILTIN(__builtin_arm_mve_vcmpgeq_f32, "", "n")
BUILTIN(__builtin_arm_mve_vcmpgeq_m_f16, "", "n")
BUILTIN(__builtin_arm_mve_vcmpgeq_m_f32, "", "n")
BUILTIN(__builtin_arm_mve_vcmpgeq_m_n_f16, "", "n")
BUILTIN(__builtin_arm_mve_vcmpgeq_m_n_f32, "", "n")
BUILTIN(__builtin_arm_mve_vcmpgeq_m_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vcmpgeq_m_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vcmpgeq_m_n_s8, "", "n")
BUILTIN(__builtin_arm_mve_vcmpgeq_m_s16, "", "n")
BUILTIN(__builtin_arm_mve_vcmpgeq_m_s32, "", "n")
BUILTIN(__builtin_arm_mve_vcmpgeq_m_s8, "", "n")
BUILTIN(__builtin_arm_mve_vcmpgeq_n_f16, "", "n")
BUILTIN(__builtin_arm_mve_vcmpgeq_n_f32, "", "n")
BUILTIN(__builtin_arm_mve_vcmpgeq_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vcmpgeq_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vcmpgeq_n_s8, "", "n")
BUILTIN(__builtin_arm_mve_vcmpgeq_s16, "", "n")
BUILTIN(__builtin_arm_mve_vcmpgeq_s32, "", "n")
BUILTIN(__builtin_arm_mve_vcmpgeq_s8, "", "n")
BUILTIN(__builtin_arm_mve_vcmpgtq_f16, "", "n")
BUILTIN(__builtin_arm_mve_vcmpgtq_f32, "", "n")
BUILTIN(__builtin_arm_mve_vcmpgtq_m_f16, "", "n")
BUILTIN(__builtin_arm_mve_vcmpgtq_m_f32, "", "n")
BUILTIN(__builtin_arm_mve_vcmpgtq_m_n_f16, "", "n")
BUILTIN(__builtin_arm_mve_vcmpgtq_m_n_f32, "", "n")
BUILTIN(__builtin_arm_mve_vcmpgtq_m_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vcmpgtq_m_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vcmpgtq_m_n_s8, "", "n")
BUILTIN(__builtin_arm_mve_vcmpgtq_m_s16, "", "n")
BUILTIN(__builtin_arm_mve_vcmpgtq_m_s32, "", "n")
BUILTIN(__builtin_arm_mve_vcmpgtq_m_s8, "", "n")
BUILTIN(__builtin_arm_mve_vcmpgtq_n_f16, "", "n")
BUILTIN(__builtin_arm_mve_vcmpgtq_n_f32, "", "n")
BUILTIN(__builtin_arm_mve_vcmpgtq_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vcmpgtq_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vcmpgtq_n_s8, "", "n")
BUILTIN(__builtin_arm_mve_vcmpgtq_s16, "", "n")
BUILTIN(__builtin_arm_mve_vcmpgtq_s32, "", "n")
BUILTIN(__builtin_arm_mve_vcmpgtq_s8, "", "n")
BUILTIN(__builtin_arm_mve_vcmphiq_m_n_u16, "", "n")
BUILTIN(__builtin_arm_mve_vcmphiq_m_n_u32, "", "n")
BUILTIN(__builtin_arm_mve_vcmphiq_m_n_u8, "", "n")
BUILTIN(__builtin_arm_mve_vcmphiq_m_u16, "", "n")
BUILTIN(__builtin_arm_mve_vcmphiq_m_u32, "", "n")
BUILTIN(__builtin_arm_mve_vcmphiq_m_u8, "", "n")
BUILTIN(__builtin_arm_mve_vcmphiq_n_u16, "", "n")
BUILTIN(__builtin_arm_mve_vcmphiq_n_u32, "", "n")
BUILTIN(__builtin_arm_mve_vcmphiq_n_u8, "", "n")
BUILTIN(__builtin_arm_mve_vcmphiq_u16, "", "n")
BUILTIN(__builtin_arm_mve_vcmphiq_u32, "", "n")
BUILTIN(__builtin_arm_mve_vcmphiq_u8, "", "n")
BUILTIN(__builtin_arm_mve_vcmpleq_f16, "", "n")
BUILTIN(__builtin_arm_mve_vcmpleq_f32, "", "n")
BUILTIN(__builtin_arm_mve_vcmpleq_m_f16, "", "n")
BUILTIN(__builtin_arm_mve_vcmpleq_m_f32, "", "n")
BUILTIN(__builtin_arm_mve_vcmpleq_m_n_f16, "", "n")
BUILTIN(__builtin_arm_mve_vcmpleq_m_n_f32, "", "n")
BUILTIN(__builtin_arm_mve_vcmpleq_m_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vcmpleq_m_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vcmpleq_m_n_s8, "", "n")
BUILTIN(__builtin_arm_mve_vcmpleq_m_s16, "", "n")
BUILTIN(__builtin_arm_mve_vcmpleq_m_s32, "", "n")
BUILTIN(__builtin_arm_mve_vcmpleq_m_s8, "", "n")
BUILTIN(__builtin_arm_mve_vcmpleq_n_f16, "", "n")
BUILTIN(__builtin_arm_mve_vcmpleq_n_f32, "", "n")
BUILTIN(__builtin_arm_mve_vcmpleq_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vcmpleq_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vcmpleq_n_s8, "", "n")
BUILTIN(__builtin_arm_mve_vcmpleq_s16, "", "n")
BUILTIN(__builtin_arm_mve_vcmpleq_s32, "", "n")
BUILTIN(__builtin_arm_mve_vcmpleq_s8, "", "n")
BUILTIN(__builtin_arm_mve_vcmpltq_f16, "", "n")
BUILTIN(__builtin_arm_mve_vcmpltq_f32, "", "n")
BUILTIN(__builtin_arm_mve_vcmpltq_m_f16, "", "n")
BUILTIN(__builtin_arm_mve_vcmpltq_m_f32, "", "n")
BUILTIN(__builtin_arm_mve_vcmpltq_m_n_f16, "", "n")
BUILTIN(__builtin_arm_mve_vcmpltq_m_n_f32, "", "n")
BUILTIN(__builtin_arm_mve_vcmpltq_m_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vcmpltq_m_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vcmpltq_m_n_s8, "", "n")
BUILTIN(__builtin_arm_mve_vcmpltq_m_s16, "", "n")
BUILTIN(__builtin_arm_mve_vcmpltq_m_s32, "", "n")
BUILTIN(__builtin_arm_mve_vcmpltq_m_s8, "", "n")
BUILTIN(__builtin_arm_mve_vcmpltq_n_f16, "", "n")
BUILTIN(__builtin_arm_mve_vcmpltq_n_f32, "", "n")
BUILTIN(__builtin_arm_mve_vcmpltq_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vcmpltq_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vcmpltq_n_s8, "", "n")
BUILTIN(__builtin_arm_mve_vcmpltq_s16, "", "n")
BUILTIN(__builtin_arm_mve_vcmpltq_s32, "", "n")
BUILTIN(__builtin_arm_mve_vcmpltq_s8, "", "n")
BUILTIN(__builtin_arm_mve_vcmpneq_f16, "", "n")
BUILTIN(__builtin_arm_mve_vcmpneq_f32, "", "n")
BUILTIN(__builtin_arm_mve_vcmpneq_m_f16, "", "n")
BUILTIN(__builtin_arm_mve_vcmpneq_m_f32, "", "n")
BUILTIN(__builtin_arm_mve_vcmpneq_m_n_f16, "", "n")
BUILTIN(__builtin_arm_mve_vcmpneq_m_n_f32, "", "n")
BUILTIN(__builtin_arm_mve_vcmpneq_m_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vcmpneq_m_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vcmpneq_m_n_s8, "", "n")
BUILTIN(__builtin_arm_mve_vcmpneq_m_n_u16, "", "n")
BUILTIN(__builtin_arm_mve_vcmpneq_m_n_u32, "", "n")
BUILTIN(__builtin_arm_mve_vcmpneq_m_n_u8, "", "n")
BUILTIN(__builtin_arm_mve_vcmpneq_m_s16, "", "n")
BUILTIN(__builtin_arm_mve_vcmpneq_m_s32, "", "n")
BUILTIN(__builtin_arm_mve_vcmpneq_m_s8, "", "n")
BUILTIN(__builtin_arm_mve_vcmpneq_m_u16, "", "n")
BUILTIN(__builtin_arm_mve_vcmpneq_m_u32, "", "n")
BUILTIN(__builtin_arm_mve_vcmpneq_m_u8, "", "n")
BUILTIN(__builtin_arm_mve_vcmpneq_n_f16, "", "n")
BUILTIN(__builtin_arm_mve_vcmpneq_n_f32, "", "n")
BUILTIN(__builtin_arm_mve_vcmpneq_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vcmpneq_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vcmpneq_n_s8, "", "n")
BUILTIN(__builtin_arm_mve_vcmpneq_n_u16, "", "n")
BUILTIN(__builtin_arm_mve_vcmpneq_n_u32, "", "n")
BUILTIN(__builtin_arm_mve_vcmpneq_n_u8, "", "n")
BUILTIN(__builtin_arm_mve_vcmpneq_s16, "", "n")
BUILTIN(__builtin_arm_mve_vcmpneq_s32, "", "n")
BUILTIN(__builtin_arm_mve_vcmpneq_s8, "", "n")
BUILTIN(__builtin_arm_mve_vcmpneq_u16, "", "n")
BUILTIN(__builtin_arm_mve_vcmpneq_u32, "", "n")
BUILTIN(__builtin_arm_mve_vcmpneq_u8, "", "n")
BUILTIN(__builtin_arm_mve_vcmulq_f16, "", "n")
BUILTIN(__builtin_arm_mve_vcmulq_f32, "", "n")
BUILTIN(__builtin_arm_mve_vcmulq_m_f16, "", "n")
BUILTIN(__builtin_arm_mve_vcmulq_m_f32, "", "n")
BUILTIN(__builtin_arm_mve_vcmulq_rot180_f16, "", "n")
BUILTIN(__builtin_arm_mve_vcmulq_rot180_f32, "", "n")
BUILTIN(__builtin_arm_mve_vcmulq_rot180_m_f16, "", "n")
BUILTIN(__builtin_arm_mve_vcmulq_rot180_m_f32, "", "n")
BUILTIN(__builtin_arm_mve_vcmulq_rot180_x_f16, "", "n")
BUILTIN(__builtin_arm_mve_vcmulq_rot180_x_f32, "", "n")
BUILTIN(__builtin_arm_mve_vcmulq_rot270_f16, "", "n")
BUILTIN(__builtin_arm_mve_vcmulq_rot270_f32, "", "n")
BUILTIN(__builtin_arm_mve_vcmulq_rot270_m_f16, "", "n")
BUILTIN(__builtin_arm_mve_vcmulq_rot270_m_f32, "", "n")
BUILTIN(__builtin_arm_mve_vcmulq_rot270_x_f16, "", "n")
BUILTIN(__builtin_arm_mve_vcmulq_rot270_x_f32, "", "n")
BUILTIN(__builtin_arm_mve_vcmulq_rot90_f16, "", "n")
BUILTIN(__builtin_arm_mve_vcmulq_rot90_f32, "", "n")
BUILTIN(__builtin_arm_mve_vcmulq_rot90_m_f16, "", "n")
BUILTIN(__builtin_arm_mve_vcmulq_rot90_m_f32, "", "n")
BUILTIN(__builtin_arm_mve_vcmulq_rot90_x_f16, "", "n")
BUILTIN(__builtin_arm_mve_vcmulq_rot90_x_f32, "", "n")
BUILTIN(__builtin_arm_mve_vcmulq_x_f16, "", "n")
BUILTIN(__builtin_arm_mve_vcmulq_x_f32, "", "n")
BUILTIN(__builtin_arm_mve_vcreateq_f16, "", "n")
BUILTIN(__builtin_arm_mve_vcreateq_f32, "", "n")
BUILTIN(__builtin_arm_mve_vcreateq_s16, "", "n")
BUILTIN(__builtin_arm_mve_vcreateq_s32, "", "n")
BUILTIN(__builtin_arm_mve_vcreateq_s64, "", "n")
BUILTIN(__builtin_arm_mve_vcreateq_s8, "", "n")
BUILTIN(__builtin_arm_mve_vcreateq_u16, "", "n")
BUILTIN(__builtin_arm_mve_vcreateq_u32, "", "n")
BUILTIN(__builtin_arm_mve_vcreateq_u64, "", "n")
BUILTIN(__builtin_arm_mve_vcreateq_u8, "", "n")
BUILTIN(__builtin_arm_mve_vctp16q, "", "n")
BUILTIN(__builtin_arm_mve_vctp16q_m, "", "n")
BUILTIN(__builtin_arm_mve_vctp32q, "", "n")
BUILTIN(__builtin_arm_mve_vctp32q_m, "", "n")
BUILTIN(__builtin_arm_mve_vctp64q, "", "n")
BUILTIN(__builtin_arm_mve_vctp64q_m, "", "n")
BUILTIN(__builtin_arm_mve_vctp8q, "", "n")
BUILTIN(__builtin_arm_mve_vctp8q_m, "", "n")
BUILTIN(__builtin_arm_mve_vcvtaq_m_s16_f16, "", "n")
BUILTIN(__builtin_arm_mve_vcvtaq_m_s32_f32, "", "n")
BUILTIN(__builtin_arm_mve_vcvtaq_m_u16_f16, "", "n")
BUILTIN(__builtin_arm_mve_vcvtaq_m_u32_f32, "", "n")
BUILTIN(__builtin_arm_mve_vcvtaq_s16_f16, "", "n")
BUILTIN(__builtin_arm_mve_vcvtaq_s32_f32, "", "n")
BUILTIN(__builtin_arm_mve_vcvtaq_u16_f16, "", "n")
BUILTIN(__builtin_arm_mve_vcvtaq_u32_f32, "", "n")
BUILTIN(__builtin_arm_mve_vcvtaq_x_s16_f16, "", "n")
BUILTIN(__builtin_arm_mve_vcvtaq_x_s32_f32, "", "n")
BUILTIN(__builtin_arm_mve_vcvtaq_x_u16_f16, "", "n")
BUILTIN(__builtin_arm_mve_vcvtaq_x_u32_f32, "", "n")
BUILTIN(__builtin_arm_mve_vcvtbq_f16_f32, "", "n")
BUILTIN(__builtin_arm_mve_vcvtbq_f32_f16, "", "n")
BUILTIN(__builtin_arm_mve_vcvtbq_m_f16_f32, "", "n")
BUILTIN(__builtin_arm_mve_vcvtbq_m_f32_f16, "", "n")
BUILTIN(__builtin_arm_mve_vcvtbq_x_f32_f16, "", "n")
BUILTIN(__builtin_arm_mve_vcvtmq_m_s16_f16, "", "n")
BUILTIN(__builtin_arm_mve_vcvtmq_m_s32_f32, "", "n")
BUILTIN(__builtin_arm_mve_vcvtmq_m_u16_f16, "", "n")
BUILTIN(__builtin_arm_mve_vcvtmq_m_u32_f32, "", "n")
BUILTIN(__builtin_arm_mve_vcvtmq_s16_f16, "", "n")
BUILTIN(__builtin_arm_mve_vcvtmq_s32_f32, "", "n")
BUILTIN(__builtin_arm_mve_vcvtmq_u16_f16, "", "n")
BUILTIN(__builtin_arm_mve_vcvtmq_u32_f32, "", "n")
BUILTIN(__builtin_arm_mve_vcvtmq_x_s16_f16, "", "n")
BUILTIN(__builtin_arm_mve_vcvtmq_x_s32_f32, "", "n")
BUILTIN(__builtin_arm_mve_vcvtmq_x_u16_f16, "", "n")
BUILTIN(__builtin_arm_mve_vcvtmq_x_u32_f32, "", "n")
BUILTIN(__builtin_arm_mve_vcvtnq_m_s16_f16, "", "n")
BUILTIN(__builtin_arm_mve_vcvtnq_m_s32_f32, "", "n")
BUILTIN(__builtin_arm_mve_vcvtnq_m_u16_f16, "", "n")
BUILTIN(__builtin_arm_mve_vcvtnq_m_u32_f32, "", "n")
BUILTIN(__builtin_arm_mve_vcvtnq_s16_f16, "", "n")
BUILTIN(__builtin_arm_mve_vcvtnq_s32_f32, "", "n")
BUILTIN(__builtin_arm_mve_vcvtnq_u16_f16, "", "n")
BUILTIN(__builtin_arm_mve_vcvtnq_u32_f32, "", "n")
BUILTIN(__builtin_arm_mve_vcvtnq_x_s16_f16, "", "n")
BUILTIN(__builtin_arm_mve_vcvtnq_x_s32_f32, "", "n")
BUILTIN(__builtin_arm_mve_vcvtnq_x_u16_f16, "", "n")
BUILTIN(__builtin_arm_mve_vcvtnq_x_u32_f32, "", "n")
BUILTIN(__builtin_arm_mve_vcvtpq_m_s16_f16, "", "n")
BUILTIN(__builtin_arm_mve_vcvtpq_m_s32_f32, "", "n")
BUILTIN(__builtin_arm_mve_vcvtpq_m_u16_f16, "", "n")
BUILTIN(__builtin_arm_mve_vcvtpq_m_u32_f32, "", "n")
BUILTIN(__builtin_arm_mve_vcvtpq_s16_f16, "", "n")
BUILTIN(__builtin_arm_mve_vcvtpq_s32_f32, "", "n")
BUILTIN(__builtin_arm_mve_vcvtpq_u16_f16, "", "n")
BUILTIN(__builtin_arm_mve_vcvtpq_u32_f32, "", "n")
BUILTIN(__builtin_arm_mve_vcvtpq_x_s16_f16, "", "n")
BUILTIN(__builtin_arm_mve_vcvtpq_x_s32_f32, "", "n")
BUILTIN(__builtin_arm_mve_vcvtpq_x_u16_f16, "", "n")
BUILTIN(__builtin_arm_mve_vcvtpq_x_u32_f32, "", "n")
BUILTIN(__builtin_arm_mve_vcvtq_f16_s16, "", "n")
BUILTIN(__builtin_arm_mve_vcvtq_f16_u16, "", "n")
BUILTIN(__builtin_arm_mve_vcvtq_f32_s32, "", "n")
BUILTIN(__builtin_arm_mve_vcvtq_f32_u32, "", "n")
BUILTIN(__builtin_arm_mve_vcvtq_m_f16_s16, "", "n")
BUILTIN(__builtin_arm_mve_vcvtq_m_f16_u16, "", "n")
BUILTIN(__builtin_arm_mve_vcvtq_m_f32_s32, "", "n")
BUILTIN(__builtin_arm_mve_vcvtq_m_f32_u32, "", "n")
BUILTIN(__builtin_arm_mve_vcvtq_m_n_f16_s16, "", "n")
BUILTIN(__builtin_arm_mve_vcvtq_m_n_f16_u16, "", "n")
BUILTIN(__builtin_arm_mve_vcvtq_m_n_f32_s32, "", "n")
BUILTIN(__builtin_arm_mve_vcvtq_m_n_f32_u32, "", "n")
BUILTIN(__builtin_arm_mve_vcvtq_m_n_s16_f16, "", "n")
BUILTIN(__builtin_arm_mve_vcvtq_m_n_s32_f32, "", "n")
BUILTIN(__builtin_arm_mve_vcvtq_m_n_u16_f16, "", "n")
BUILTIN(__builtin_arm_mve_vcvtq_m_n_u32_f32, "", "n")
BUILTIN(__builtin_arm_mve_vcvtq_m_s16_f16, "", "n")
BUILTIN(__builtin_arm_mve_vcvtq_m_s32_f32, "", "n")
BUILTIN(__builtin_arm_mve_vcvtq_m_u16_f16, "", "n")
BUILTIN(__builtin_arm_mve_vcvtq_m_u32_f32, "", "n")
BUILTIN(__builtin_arm_mve_vcvtq_n_f16_s16, "", "n")
BUILTIN(__builtin_arm_mve_vcvtq_n_f16_u16, "", "n")
BUILTIN(__builtin_arm_mve_vcvtq_n_f32_s32, "", "n")
BUILTIN(__builtin_arm_mve_vcvtq_n_f32_u32, "", "n")
BUILTIN(__builtin_arm_mve_vcvtq_n_s16_f16, "", "n")
BUILTIN(__builtin_arm_mve_vcvtq_n_s32_f32, "", "n")
BUILTIN(__builtin_arm_mve_vcvtq_n_u16_f16, "", "n")
BUILTIN(__builtin_arm_mve_vcvtq_n_u32_f32, "", "n")
BUILTIN(__builtin_arm_mve_vcvtq_s16_f16, "", "n")
BUILTIN(__builtin_arm_mve_vcvtq_s32_f32, "", "n")
BUILTIN(__builtin_arm_mve_vcvtq_u16_f16, "", "n")
BUILTIN(__builtin_arm_mve_vcvtq_u32_f32, "", "n")
BUILTIN(__builtin_arm_mve_vcvtq_x_f16_s16, "", "n")
BUILTIN(__builtin_arm_mve_vcvtq_x_f16_u16, "", "n")
BUILTIN(__builtin_arm_mve_vcvtq_x_f32_s32, "", "n")
BUILTIN(__builtin_arm_mve_vcvtq_x_f32_u32, "", "n")
BUILTIN(__builtin_arm_mve_vcvtq_x_n_f16_s16, "", "n")
BUILTIN(__builtin_arm_mve_vcvtq_x_n_f16_u16, "", "n")
BUILTIN(__builtin_arm_mve_vcvtq_x_n_f32_s32, "", "n")
BUILTIN(__builtin_arm_mve_vcvtq_x_n_f32_u32, "", "n")
BUILTIN(__builtin_arm_mve_vcvtq_x_n_s16_f16, "", "n")
BUILTIN(__builtin_arm_mve_vcvtq_x_n_s32_f32, "", "n")
BUILTIN(__builtin_arm_mve_vcvtq_x_n_u16_f16, "", "n")
BUILTIN(__builtin_arm_mve_vcvtq_x_n_u32_f32, "", "n")
BUILTIN(__builtin_arm_mve_vcvtq_x_s16_f16, "", "n")
BUILTIN(__builtin_arm_mve_vcvtq_x_s32_f32, "", "n")
BUILTIN(__builtin_arm_mve_vcvtq_x_u16_f16, "", "n")
BUILTIN(__builtin_arm_mve_vcvtq_x_u32_f32, "", "n")
BUILTIN(__builtin_arm_mve_vcvttq_f16_f32, "", "n")
BUILTIN(__builtin_arm_mve_vcvttq_f32_f16, "", "n")
BUILTIN(__builtin_arm_mve_vcvttq_m_f16_f32, "", "n")
BUILTIN(__builtin_arm_mve_vcvttq_m_f32_f16, "", "n")
BUILTIN(__builtin_arm_mve_vcvttq_x_f32_f16, "", "n")
BUILTIN(__builtin_arm_mve_vddupq_m_n_u16, "", "n")
BUILTIN(__builtin_arm_mve_vddupq_m_n_u32, "", "n")
BUILTIN(__builtin_arm_mve_vddupq_m_n_u8, "", "n")
BUILTIN(__builtin_arm_mve_vddupq_m_wb_u16, "", "n")
BUILTIN(__builtin_arm_mve_vddupq_m_wb_u32, "", "n")
BUILTIN(__builtin_arm_mve_vddupq_m_wb_u8, "", "n")
BUILTIN(__builtin_arm_mve_vddupq_n_u16, "", "n")
BUILTIN(__builtin_arm_mve_vddupq_n_u32, "", "n")
BUILTIN(__builtin_arm_mve_vddupq_n_u8, "", "n")
BUILTIN(__builtin_arm_mve_vddupq_wb_u16, "", "n")
BUILTIN(__builtin_arm_mve_vddupq_wb_u32, "", "n")
BUILTIN(__builtin_arm_mve_vddupq_wb_u8, "", "n")
BUILTIN(__builtin_arm_mve_vddupq_x_n_u16, "", "n")
BUILTIN(__builtin_arm_mve_vddupq_x_n_u32, "", "n")
BUILTIN(__builtin_arm_mve_vddupq_x_n_u8, "", "n")
BUILTIN(__builtin_arm_mve_vddupq_x_wb_u16, "", "n")
BUILTIN(__builtin_arm_mve_vddupq_x_wb_u32, "", "n")
BUILTIN(__builtin_arm_mve_vddupq_x_wb_u8, "", "n")
BUILTIN(__builtin_arm_mve_vdupq_m_n_f16, "", "n")
BUILTIN(__builtin_arm_mve_vdupq_m_n_f32, "", "n")
BUILTIN(__builtin_arm_mve_vdupq_m_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vdupq_m_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vdupq_m_n_s8, "", "n")
BUILTIN(__builtin_arm_mve_vdupq_m_n_u16, "", "n")
BUILTIN(__builtin_arm_mve_vdupq_m_n_u32, "", "n")
BUILTIN(__builtin_arm_mve_vdupq_m_n_u8, "", "n")
BUILTIN(__builtin_arm_mve_vdupq_n_f16, "", "n")
BUILTIN(__builtin_arm_mve_vdupq_n_f32, "", "n")
BUILTIN(__builtin_arm_mve_vdupq_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vdupq_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vdupq_n_s8, "", "n")
BUILTIN(__builtin_arm_mve_vdupq_n_u16, "", "n")
BUILTIN(__builtin_arm_mve_vdupq_n_u32, "", "n")
BUILTIN(__builtin_arm_mve_vdupq_n_u8, "", "n")
BUILTIN(__builtin_arm_mve_vdupq_x_n_f16, "", "n")
BUILTIN(__builtin_arm_mve_vdupq_x_n_f32, "", "n")
BUILTIN(__builtin_arm_mve_vdupq_x_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vdupq_x_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vdupq_x_n_s8, "", "n")
BUILTIN(__builtin_arm_mve_vdupq_x_n_u16, "", "n")
BUILTIN(__builtin_arm_mve_vdupq_x_n_u32, "", "n")
BUILTIN(__builtin_arm_mve_vdupq_x_n_u8, "", "n")
BUILTIN(__builtin_arm_mve_vdwdupq_m_n_u16, "", "n")
BUILTIN(__builtin_arm_mve_vdwdupq_m_n_u32, "", "n")
BUILTIN(__builtin_arm_mve_vdwdupq_m_n_u8, "", "n")
BUILTIN(__builtin_arm_mve_vdwdupq_m_wb_u16, "", "n")
BUILTIN(__builtin_arm_mve_vdwdupq_m_wb_u32, "", "n")
BUILTIN(__builtin_arm_mve_vdwdupq_m_wb_u8, "", "n")
BUILTIN(__builtin_arm_mve_vdwdupq_n_u16, "", "n")
BUILTIN(__builtin_arm_mve_vdwdupq_n_u32, "", "n")
BUILTIN(__builtin_arm_mve_vdwdupq_n_u8, "", "n")
BUILTIN(__builtin_arm_mve_vdwdupq_wb_u16, "", "n")
BUILTIN(__builtin_arm_mve_vdwdupq_wb_u32, "", "n")
BUILTIN(__builtin_arm_mve_vdwdupq_wb_u8, "", "n")
BUILTIN(__builtin_arm_mve_vdwdupq_x_n_u16, "", "n")
BUILTIN(__builtin_arm_mve_vdwdupq_x_n_u32, "", "n")
BUILTIN(__builtin_arm_mve_vdwdupq_x_n_u8, "", "n")
BUILTIN(__builtin_arm_mve_vdwdupq_x_wb_u16, "", "n")
BUILTIN(__builtin_arm_mve_vdwdupq_x_wb_u32, "", "n")
BUILTIN(__builtin_arm_mve_vdwdupq_x_wb_u8, "", "n")
BUILTIN(__builtin_arm_mve_veorq_f16, "", "n")
BUILTIN(__builtin_arm_mve_veorq_f32, "", "n")
BUILTIN(__builtin_arm_mve_veorq_m_f16, "", "n")
BUILTIN(__builtin_arm_mve_veorq_m_f32, "", "n")
BUILTIN(__builtin_arm_mve_veorq_m_s16, "", "n")
BUILTIN(__builtin_arm_mve_veorq_m_s32, "", "n")
BUILTIN(__builtin_arm_mve_veorq_m_s8, "", "n")
BUILTIN(__builtin_arm_mve_veorq_m_u16, "", "n")
BUILTIN(__builtin_arm_mve_veorq_m_u32, "", "n")
BUILTIN(__builtin_arm_mve_veorq_m_u8, "", "n")
BUILTIN(__builtin_arm_mve_veorq_s16, "", "n")
BUILTIN(__builtin_arm_mve_veorq_s32, "", "n")
BUILTIN(__builtin_arm_mve_veorq_s8, "", "n")
BUILTIN(__builtin_arm_mve_veorq_u16, "", "n")
BUILTIN(__builtin_arm_mve_veorq_u32, "", "n")
BUILTIN(__builtin_arm_mve_veorq_u8, "", "n")
BUILTIN(__builtin_arm_mve_veorq_x_f16, "", "n")
BUILTIN(__builtin_arm_mve_veorq_x_f32, "", "n")
BUILTIN(__builtin_arm_mve_veorq_x_s16, "", "n")
BUILTIN(__builtin_arm_mve_veorq_x_s32, "", "n")
BUILTIN(__builtin_arm_mve_veorq_x_s8, "", "n")
BUILTIN(__builtin_arm_mve_veorq_x_u16, "", "n")
BUILTIN(__builtin_arm_mve_veorq_x_u32, "", "n")
BUILTIN(__builtin_arm_mve_veorq_x_u8, "", "n")
BUILTIN(__builtin_arm_mve_vfmaq_f16, "", "n")
BUILTIN(__builtin_arm_mve_vfmaq_f32, "", "n")
BUILTIN(__builtin_arm_mve_vfmaq_m_f16, "", "n")
BUILTIN(__builtin_arm_mve_vfmaq_m_f32, "", "n")
BUILTIN(__builtin_arm_mve_vfmaq_m_n_f16, "", "n")
BUILTIN(__builtin_arm_mve_vfmaq_m_n_f32, "", "n")
BUILTIN(__builtin_arm_mve_vfmaq_n_f16, "", "n")
BUILTIN(__builtin_arm_mve_vfmaq_n_f32, "", "n")
BUILTIN(__builtin_arm_mve_vfmasq_m_n_f16, "", "n")
BUILTIN(__builtin_arm_mve_vfmasq_m_n_f32, "", "n")
BUILTIN(__builtin_arm_mve_vfmasq_n_f16, "", "n")
BUILTIN(__builtin_arm_mve_vfmasq_n_f32, "", "n")
BUILTIN(__builtin_arm_mve_vfmsq_f16, "", "n")
BUILTIN(__builtin_arm_mve_vfmsq_f32, "", "n")
BUILTIN(__builtin_arm_mve_vfmsq_m_f16, "", "n")
BUILTIN(__builtin_arm_mve_vfmsq_m_f32, "", "n")
BUILTIN(__builtin_arm_mve_vgetq_lane_f16, "", "n")
BUILTIN(__builtin_arm_mve_vgetq_lane_f32, "", "n")
BUILTIN(__builtin_arm_mve_vgetq_lane_s16, "", "n")
BUILTIN(__builtin_arm_mve_vgetq_lane_s32, "", "n")
BUILTIN(__builtin_arm_mve_vgetq_lane_s64, "", "n")
BUILTIN(__builtin_arm_mve_vgetq_lane_s8, "", "n")
BUILTIN(__builtin_arm_mve_vgetq_lane_u16, "", "n")
BUILTIN(__builtin_arm_mve_vgetq_lane_u32, "", "n")
BUILTIN(__builtin_arm_mve_vgetq_lane_u64, "", "n")
BUILTIN(__builtin_arm_mve_vgetq_lane_u8, "", "n")
BUILTIN(__builtin_arm_mve_vhaddq_m_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vhaddq_m_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vhaddq_m_n_s8, "", "n")
BUILTIN(__builtin_arm_mve_vhaddq_m_n_u16, "", "n")
BUILTIN(__builtin_arm_mve_vhaddq_m_n_u32, "", "n")
BUILTIN(__builtin_arm_mve_vhaddq_m_n_u8, "", "n")
BUILTIN(__builtin_arm_mve_vhaddq_m_s16, "", "n")
BUILTIN(__builtin_arm_mve_vhaddq_m_s32, "", "n")
BUILTIN(__builtin_arm_mve_vhaddq_m_s8, "", "n")
BUILTIN(__builtin_arm_mve_vhaddq_m_u16, "", "n")
BUILTIN(__builtin_arm_mve_vhaddq_m_u32, "", "n")
BUILTIN(__builtin_arm_mve_vhaddq_m_u8, "", "n")
BUILTIN(__builtin_arm_mve_vhaddq_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vhaddq_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vhaddq_n_s8, "", "n")
BUILTIN(__builtin_arm_mve_vhaddq_n_u16, "", "n")
BUILTIN(__builtin_arm_mve_vhaddq_n_u32, "", "n")
BUILTIN(__builtin_arm_mve_vhaddq_n_u8, "", "n")
BUILTIN(__builtin_arm_mve_vhaddq_s16, "", "n")
BUILTIN(__builtin_arm_mve_vhaddq_s32, "", "n")
BUILTIN(__builtin_arm_mve_vhaddq_s8, "", "n")
BUILTIN(__builtin_arm_mve_vhaddq_u16, "", "n")
BUILTIN(__builtin_arm_mve_vhaddq_u32, "", "n")
BUILTIN(__builtin_arm_mve_vhaddq_u8, "", "n")
BUILTIN(__builtin_arm_mve_vhaddq_x_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vhaddq_x_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vhaddq_x_n_s8, "", "n")
BUILTIN(__builtin_arm_mve_vhaddq_x_n_u16, "", "n")
BUILTIN(__builtin_arm_mve_vhaddq_x_n_u32, "", "n")
BUILTIN(__builtin_arm_mve_vhaddq_x_n_u8, "", "n")
BUILTIN(__builtin_arm_mve_vhaddq_x_s16, "", "n")
BUILTIN(__builtin_arm_mve_vhaddq_x_s32, "", "n")
BUILTIN(__builtin_arm_mve_vhaddq_x_s8, "", "n")
BUILTIN(__builtin_arm_mve_vhaddq_x_u16, "", "n")
BUILTIN(__builtin_arm_mve_vhaddq_x_u32, "", "n")
BUILTIN(__builtin_arm_mve_vhaddq_x_u8, "", "n")
BUILTIN(__builtin_arm_mve_vhcaddq_rot270_m_s16, "", "n")
BUILTIN(__builtin_arm_mve_vhcaddq_rot270_m_s32, "", "n")
BUILTIN(__builtin_arm_mve_vhcaddq_rot270_m_s8, "", "n")
BUILTIN(__builtin_arm_mve_vhcaddq_rot270_s16, "", "n")
BUILTIN(__builtin_arm_mve_vhcaddq_rot270_s32, "", "n")
BUILTIN(__builtin_arm_mve_vhcaddq_rot270_s8, "", "n")
BUILTIN(__builtin_arm_mve_vhcaddq_rot270_x_s16, "", "n")
BUILTIN(__builtin_arm_mve_vhcaddq_rot270_x_s32, "", "n")
BUILTIN(__builtin_arm_mve_vhcaddq_rot270_x_s8, "", "n")
BUILTIN(__builtin_arm_mve_vhcaddq_rot90_m_s16, "", "n")
BUILTIN(__builtin_arm_mve_vhcaddq_rot90_m_s32, "", "n")
BUILTIN(__builtin_arm_mve_vhcaddq_rot90_m_s8, "", "n")
BUILTIN(__builtin_arm_mve_vhcaddq_rot90_s16, "", "n")
BUILTIN(__builtin_arm_mve_vhcaddq_rot90_s32, "", "n")
BUILTIN(__builtin_arm_mve_vhcaddq_rot90_s8, "", "n")
BUILTIN(__builtin_arm_mve_vhcaddq_rot90_x_s16, "", "n")
BUILTIN(__builtin_arm_mve_vhcaddq_rot90_x_s32, "", "n")
BUILTIN(__builtin_arm_mve_vhcaddq_rot90_x_s8, "", "n")
BUILTIN(__builtin_arm_mve_vhsubq_m_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vhsubq_m_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vhsubq_m_n_s8, "", "n")
BUILTIN(__builtin_arm_mve_vhsubq_m_n_u16, "", "n")
BUILTIN(__builtin_arm_mve_vhsubq_m_n_u32, "", "n")
BUILTIN(__builtin_arm_mve_vhsubq_m_n_u8, "", "n")
BUILTIN(__builtin_arm_mve_vhsubq_m_s16, "", "n")
BUILTIN(__builtin_arm_mve_vhsubq_m_s32, "", "n")
BUILTIN(__builtin_arm_mve_vhsubq_m_s8, "", "n")
BUILTIN(__builtin_arm_mve_vhsubq_m_u16, "", "n")
BUILTIN(__builtin_arm_mve_vhsubq_m_u32, "", "n")
BUILTIN(__builtin_arm_mve_vhsubq_m_u8, "", "n")
BUILTIN(__builtin_arm_mve_vhsubq_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vhsubq_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vhsubq_n_s8, "", "n")
BUILTIN(__builtin_arm_mve_vhsubq_n_u16, "", "n")
BUILTIN(__builtin_arm_mve_vhsubq_n_u32, "", "n")
BUILTIN(__builtin_arm_mve_vhsubq_n_u8, "", "n")
BUILTIN(__builtin_arm_mve_vhsubq_s16, "", "n")
BUILTIN(__builtin_arm_mve_vhsubq_s32, "", "n")
BUILTIN(__builtin_arm_mve_vhsubq_s8, "", "n")
BUILTIN(__builtin_arm_mve_vhsubq_u16, "", "n")
BUILTIN(__builtin_arm_mve_vhsubq_u32, "", "n")
BUILTIN(__builtin_arm_mve_vhsubq_u8, "", "n")
BUILTIN(__builtin_arm_mve_vhsubq_x_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vhsubq_x_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vhsubq_x_n_s8, "", "n")
BUILTIN(__builtin_arm_mve_vhsubq_x_n_u16, "", "n")
BUILTIN(__builtin_arm_mve_vhsubq_x_n_u32, "", "n")
BUILTIN(__builtin_arm_mve_vhsubq_x_n_u8, "", "n")
BUILTIN(__builtin_arm_mve_vhsubq_x_s16, "", "n")
BUILTIN(__builtin_arm_mve_vhsubq_x_s32, "", "n")
BUILTIN(__builtin_arm_mve_vhsubq_x_s8, "", "n")
BUILTIN(__builtin_arm_mve_vhsubq_x_u16, "", "n")
BUILTIN(__builtin_arm_mve_vhsubq_x_u32, "", "n")
BUILTIN(__builtin_arm_mve_vhsubq_x_u8, "", "n")
BUILTIN(__builtin_arm_mve_vidupq_m_n_u16, "", "n")
BUILTIN(__builtin_arm_mve_vidupq_m_n_u32, "", "n")
BUILTIN(__builtin_arm_mve_vidupq_m_n_u8, "", "n")
BUILTIN(__builtin_arm_mve_vidupq_m_wb_u16, "", "n")
BUILTIN(__builtin_arm_mve_vidupq_m_wb_u32, "", "n")
BUILTIN(__builtin_arm_mve_vidupq_m_wb_u8, "", "n")
BUILTIN(__builtin_arm_mve_vidupq_n_u16, "", "n")
BUILTIN(__builtin_arm_mve_vidupq_n_u32, "", "n")
BUILTIN(__builtin_arm_mve_vidupq_n_u8, "", "n")
BUILTIN(__builtin_arm_mve_vidupq_wb_u16, "", "n")
BUILTIN(__builtin_arm_mve_vidupq_wb_u32, "", "n")
BUILTIN(__builtin_arm_mve_vidupq_wb_u8, "", "n")
BUILTIN(__builtin_arm_mve_vidupq_x_n_u16, "", "n")
BUILTIN(__builtin_arm_mve_vidupq_x_n_u32, "", "n")
BUILTIN(__builtin_arm_mve_vidupq_x_n_u8, "", "n")
BUILTIN(__builtin_arm_mve_vidupq_x_wb_u16, "", "n")
BUILTIN(__builtin_arm_mve_vidupq_x_wb_u32, "", "n")
BUILTIN(__builtin_arm_mve_vidupq_x_wb_u8, "", "n")
BUILTIN(__builtin_arm_mve_viwdupq_m_n_u16, "", "n")
BUILTIN(__builtin_arm_mve_viwdupq_m_n_u32, "", "n")
BUILTIN(__builtin_arm_mve_viwdupq_m_n_u8, "", "n")
BUILTIN(__builtin_arm_mve_viwdupq_m_wb_u16, "", "n")
BUILTIN(__builtin_arm_mve_viwdupq_m_wb_u32, "", "n")
BUILTIN(__builtin_arm_mve_viwdupq_m_wb_u8, "", "n")
BUILTIN(__builtin_arm_mve_viwdupq_n_u16, "", "n")
BUILTIN(__builtin_arm_mve_viwdupq_n_u32, "", "n")
BUILTIN(__builtin_arm_mve_viwdupq_n_u8, "", "n")
BUILTIN(__builtin_arm_mve_viwdupq_wb_u16, "", "n")
BUILTIN(__builtin_arm_mve_viwdupq_wb_u32, "", "n")
BUILTIN(__builtin_arm_mve_viwdupq_wb_u8, "", "n")
BUILTIN(__builtin_arm_mve_viwdupq_x_n_u16, "", "n")
BUILTIN(__builtin_arm_mve_viwdupq_x_n_u32, "", "n")
BUILTIN(__builtin_arm_mve_viwdupq_x_n_u8, "", "n")
BUILTIN(__builtin_arm_mve_viwdupq_x_wb_u16, "", "n")
BUILTIN(__builtin_arm_mve_viwdupq_x_wb_u32, "", "n")
BUILTIN(__builtin_arm_mve_viwdupq_x_wb_u8, "", "n")
BUILTIN(__builtin_arm_mve_vld1q_f16, "", "n")
BUILTIN(__builtin_arm_mve_vld1q_f32, "", "n")
BUILTIN(__builtin_arm_mve_vld1q_s16, "", "n")
BUILTIN(__builtin_arm_mve_vld1q_s32, "", "n")
BUILTIN(__builtin_arm_mve_vld1q_s8, "", "n")
BUILTIN(__builtin_arm_mve_vld1q_u16, "", "n")
BUILTIN(__builtin_arm_mve_vld1q_u32, "", "n")
BUILTIN(__builtin_arm_mve_vld1q_u8, "", "n")
BUILTIN(__builtin_arm_mve_vld1q_z_f16, "", "n")
BUILTIN(__builtin_arm_mve_vld1q_z_f32, "", "n")
BUILTIN(__builtin_arm_mve_vld1q_z_s16, "", "n")
BUILTIN(__builtin_arm_mve_vld1q_z_s32, "", "n")
BUILTIN(__builtin_arm_mve_vld1q_z_s8, "", "n")
BUILTIN(__builtin_arm_mve_vld1q_z_u16, "", "n")
BUILTIN(__builtin_arm_mve_vld1q_z_u32, "", "n")
BUILTIN(__builtin_arm_mve_vld1q_z_u8, "", "n")
BUILTIN(__builtin_arm_mve_vld2q_f16, "", "n")
BUILTIN(__builtin_arm_mve_vld2q_f32, "", "n")
BUILTIN(__builtin_arm_mve_vld2q_s16, "", "n")
BUILTIN(__builtin_arm_mve_vld2q_s32, "", "n")
BUILTIN(__builtin_arm_mve_vld2q_s8, "", "n")
BUILTIN(__builtin_arm_mve_vld2q_u16, "", "n")
BUILTIN(__builtin_arm_mve_vld2q_u32, "", "n")
BUILTIN(__builtin_arm_mve_vld2q_u8, "", "n")
BUILTIN(__builtin_arm_mve_vld4q_f16, "", "n")
BUILTIN(__builtin_arm_mve_vld4q_f32, "", "n")
BUILTIN(__builtin_arm_mve_vld4q_s16, "", "n")
BUILTIN(__builtin_arm_mve_vld4q_s32, "", "n")
BUILTIN(__builtin_arm_mve_vld4q_s8, "", "n")
BUILTIN(__builtin_arm_mve_vld4q_u16, "", "n")
BUILTIN(__builtin_arm_mve_vld4q_u32, "", "n")
BUILTIN(__builtin_arm_mve_vld4q_u8, "", "n")
BUILTIN(__builtin_arm_mve_vldrbq_gather_offset_s16, "", "n")
BUILTIN(__builtin_arm_mve_vldrbq_gather_offset_s32, "", "n")
BUILTIN(__builtin_arm_mve_vldrbq_gather_offset_s8, "", "n")
BUILTIN(__builtin_arm_mve_vldrbq_gather_offset_u16, "", "n")
BUILTIN(__builtin_arm_mve_vldrbq_gather_offset_u32, "", "n")
BUILTIN(__builtin_arm_mve_vldrbq_gather_offset_u8, "", "n")
BUILTIN(__builtin_arm_mve_vldrbq_gather_offset_z_s16, "", "n")
BUILTIN(__builtin_arm_mve_vldrbq_gather_offset_z_s32, "", "n")
BUILTIN(__builtin_arm_mve_vldrbq_gather_offset_z_s8, "", "n")
BUILTIN(__builtin_arm_mve_vldrbq_gather_offset_z_u16, "", "n")
BUILTIN(__builtin_arm_mve_vldrbq_gather_offset_z_u32, "", "n")
BUILTIN(__builtin_arm_mve_vldrbq_gather_offset_z_u8, "", "n")
BUILTIN(__builtin_arm_mve_vldrbq_s16, "", "n")
BUILTIN(__builtin_arm_mve_vldrbq_s32, "", "n")
BUILTIN(__builtin_arm_mve_vldrbq_s8, "", "n")
BUILTIN(__builtin_arm_mve_vldrbq_u16, "", "n")
BUILTIN(__builtin_arm_mve_vldrbq_u32, "", "n")
BUILTIN(__builtin_arm_mve_vldrbq_u8, "", "n")
BUILTIN(__builtin_arm_mve_vldrbq_z_s16, "", "n")
BUILTIN(__builtin_arm_mve_vldrbq_z_s32, "", "n")
BUILTIN(__builtin_arm_mve_vldrbq_z_s8, "", "n")
BUILTIN(__builtin_arm_mve_vldrbq_z_u16, "", "n")
BUILTIN(__builtin_arm_mve_vldrbq_z_u32, "", "n")
BUILTIN(__builtin_arm_mve_vldrbq_z_u8, "", "n")
BUILTIN(__builtin_arm_mve_vldrdq_gather_base_s64, "", "n")
BUILTIN(__builtin_arm_mve_vldrdq_gather_base_u64, "", "n")
BUILTIN(__builtin_arm_mve_vldrdq_gather_base_wb_s64, "", "n")
BUILTIN(__builtin_arm_mve_vldrdq_gather_base_wb_u64, "", "n")
BUILTIN(__builtin_arm_mve_vldrdq_gather_base_wb_z_s64, "", "n")
BUILTIN(__builtin_arm_mve_vldrdq_gather_base_wb_z_u64, "", "n")
BUILTIN(__builtin_arm_mve_vldrdq_gather_base_z_s64, "", "n")
BUILTIN(__builtin_arm_mve_vldrdq_gather_base_z_u64, "", "n")
BUILTIN(__builtin_arm_mve_vldrdq_gather_offset_s64, "", "n")
BUILTIN(__builtin_arm_mve_vldrdq_gather_offset_u64, "", "n")
BUILTIN(__builtin_arm_mve_vldrdq_gather_offset_z_s64, "", "n")
BUILTIN(__builtin_arm_mve_vldrdq_gather_offset_z_u64, "", "n")
BUILTIN(__builtin_arm_mve_vldrdq_gather_shifted_offset_s64, "", "n")
BUILTIN(__builtin_arm_mve_vldrdq_gather_shifted_offset_u64, "", "n")
BUILTIN(__builtin_arm_mve_vldrdq_gather_shifted_offset_z_s64, "", "n")
BUILTIN(__builtin_arm_mve_vldrdq_gather_shifted_offset_z_u64, "", "n")
BUILTIN(__builtin_arm_mve_vldrhq_f16, "", "n")
BUILTIN(__builtin_arm_mve_vldrhq_gather_offset_f16, "", "n")
BUILTIN(__builtin_arm_mve_vldrhq_gather_offset_s16, "", "n")
BUILTIN(__builtin_arm_mve_vldrhq_gather_offset_s32, "", "n")
BUILTIN(__builtin_arm_mve_vldrhq_gather_offset_u16, "", "n")
BUILTIN(__builtin_arm_mve_vldrhq_gather_offset_u32, "", "n")
BUILTIN(__builtin_arm_mve_vldrhq_gather_offset_z_f16, "", "n")
BUILTIN(__builtin_arm_mve_vldrhq_gather_offset_z_s16, "", "n")
BUILTIN(__builtin_arm_mve_vldrhq_gather_offset_z_s32, "", "n")
BUILTIN(__builtin_arm_mve_vldrhq_gather_offset_z_u16, "", "n")
BUILTIN(__builtin_arm_mve_vldrhq_gather_offset_z_u32, "", "n")
BUILTIN(__builtin_arm_mve_vldrhq_gather_shifted_offset_f16, "", "n")
BUILTIN(__builtin_arm_mve_vldrhq_gather_shifted_offset_s16, "", "n")
BUILTIN(__builtin_arm_mve_vldrhq_gather_shifted_offset_s32, "", "n")
BUILTIN(__builtin_arm_mve_vldrhq_gather_shifted_offset_u16, "", "n")
BUILTIN(__builtin_arm_mve_vldrhq_gather_shifted_offset_u32, "", "n")
BUILTIN(__builtin_arm_mve_vldrhq_gather_shifted_offset_z_f16, "", "n")
BUILTIN(__builtin_arm_mve_vldrhq_gather_shifted_offset_z_s16, "", "n")
BUILTIN(__builtin_arm_mve_vldrhq_gather_shifted_offset_z_s32, "", "n")
BUILTIN(__builtin_arm_mve_vldrhq_gather_shifted_offset_z_u16, "", "n")
BUILTIN(__builtin_arm_mve_vldrhq_gather_shifted_offset_z_u32, "", "n")
BUILTIN(__builtin_arm_mve_vldrhq_s16, "", "n")
BUILTIN(__builtin_arm_mve_vldrhq_s32, "", "n")
BUILTIN(__builtin_arm_mve_vldrhq_u16, "", "n")
BUILTIN(__builtin_arm_mve_vldrhq_u32, "", "n")
BUILTIN(__builtin_arm_mve_vldrhq_z_f16, "", "n")
BUILTIN(__builtin_arm_mve_vldrhq_z_s16, "", "n")
BUILTIN(__builtin_arm_mve_vldrhq_z_s32, "", "n")
BUILTIN(__builtin_arm_mve_vldrhq_z_u16, "", "n")
BUILTIN(__builtin_arm_mve_vldrhq_z_u32, "", "n")
BUILTIN(__builtin_arm_mve_vldrwq_f32, "", "n")
BUILTIN(__builtin_arm_mve_vldrwq_gather_base_f32, "", "n")
BUILTIN(__builtin_arm_mve_vldrwq_gather_base_s32, "", "n")
BUILTIN(__builtin_arm_mve_vldrwq_gather_base_u32, "", "n")
BUILTIN(__builtin_arm_mve_vldrwq_gather_base_wb_f32, "", "n")
BUILTIN(__builtin_arm_mve_vldrwq_gather_base_wb_s32, "", "n")
BUILTIN(__builtin_arm_mve_vldrwq_gather_base_wb_u32, "", "n")
BUILTIN(__builtin_arm_mve_vldrwq_gather_base_wb_z_f32, "", "n")
BUILTIN(__builtin_arm_mve_vldrwq_gather_base_wb_z_s32, "", "n")
BUILTIN(__builtin_arm_mve_vldrwq_gather_base_wb_z_u32, "", "n")
BUILTIN(__builtin_arm_mve_vldrwq_gather_base_z_f32, "", "n")
BUILTIN(__builtin_arm_mve_vldrwq_gather_base_z_s32, "", "n")
BUILTIN(__builtin_arm_mve_vldrwq_gather_base_z_u32, "", "n")
BUILTIN(__builtin_arm_mve_vldrwq_gather_offset_f32, "", "n")
BUILTIN(__builtin_arm_mve_vldrwq_gather_offset_s32, "", "n")
BUILTIN(__builtin_arm_mve_vldrwq_gather_offset_u32, "", "n")
BUILTIN(__builtin_arm_mve_vldrwq_gather_offset_z_f32, "", "n")
BUILTIN(__builtin_arm_mve_vldrwq_gather_offset_z_s32, "", "n")
BUILTIN(__builtin_arm_mve_vldrwq_gather_offset_z_u32, "", "n")
BUILTIN(__builtin_arm_mve_vldrwq_gather_shifted_offset_f32, "", "n")
BUILTIN(__builtin_arm_mve_vldrwq_gather_shifted_offset_s32, "", "n")
BUILTIN(__builtin_arm_mve_vldrwq_gather_shifted_offset_u32, "", "n")
BUILTIN(__builtin_arm_mve_vldrwq_gather_shifted_offset_z_f32, "", "n")
BUILTIN(__builtin_arm_mve_vldrwq_gather_shifted_offset_z_s32, "", "n")
BUILTIN(__builtin_arm_mve_vldrwq_gather_shifted_offset_z_u32, "", "n")
BUILTIN(__builtin_arm_mve_vldrwq_s32, "", "n")
BUILTIN(__builtin_arm_mve_vldrwq_u32, "", "n")
BUILTIN(__builtin_arm_mve_vldrwq_z_f32, "", "n")
BUILTIN(__builtin_arm_mve_vldrwq_z_s32, "", "n")
BUILTIN(__builtin_arm_mve_vldrwq_z_u32, "", "n")
BUILTIN(__builtin_arm_mve_vmaxaq_m_s16, "", "n")
BUILTIN(__builtin_arm_mve_vmaxaq_m_s32, "", "n")
BUILTIN(__builtin_arm_mve_vmaxaq_m_s8, "", "n")
BUILTIN(__builtin_arm_mve_vmaxaq_s16, "", "n")
BUILTIN(__builtin_arm_mve_vmaxaq_s32, "", "n")
BUILTIN(__builtin_arm_mve_vmaxaq_s8, "", "n")
BUILTIN(__builtin_arm_mve_vmaxavq_p_s16, "", "n")
BUILTIN(__builtin_arm_mve_vmaxavq_p_s32, "", "n")
BUILTIN(__builtin_arm_mve_vmaxavq_p_s8, "", "n")
BUILTIN(__builtin_arm_mve_vmaxavq_s16, "", "n")
BUILTIN(__builtin_arm_mve_vmaxavq_s32, "", "n")
BUILTIN(__builtin_arm_mve_vmaxavq_s8, "", "n")
BUILTIN(__builtin_arm_mve_vmaxnmaq_f16, "", "n")
BUILTIN(__builtin_arm_mve_vmaxnmaq_f32, "", "n")
BUILTIN(__builtin_arm_mve_vmaxnmaq_m_f16, "", "n")
BUILTIN(__builtin_arm_mve_vmaxnmaq_m_f32, "", "n")
BUILTIN(__builtin_arm_mve_vmaxnmavq_f16, "", "n")
BUILTIN(__builtin_arm_mve_vmaxnmavq_f32, "", "n")
BUILTIN(__builtin_arm_mve_vmaxnmavq_p_f16, "", "n")
BUILTIN(__builtin_arm_mve_vmaxnmavq_p_f32, "", "n")
BUILTIN(__builtin_arm_mve_vmaxnmq_f16, "", "n")
BUILTIN(__builtin_arm_mve_vmaxnmq_f32, "", "n")
BUILTIN(__builtin_arm_mve_vmaxnmq_m_f16, "", "n")
BUILTIN(__builtin_arm_mve_vmaxnmq_m_f32, "", "n")
BUILTIN(__builtin_arm_mve_vmaxnmq_x_f16, "", "n")
BUILTIN(__builtin_arm_mve_vmaxnmq_x_f32, "", "n")
BUILTIN(__builtin_arm_mve_vmaxnmvq_f16, "", "n")
BUILTIN(__builtin_arm_mve_vmaxnmvq_f32, "", "n")
BUILTIN(__builtin_arm_mve_vmaxnmvq_p_f16, "", "n")
BUILTIN(__builtin_arm_mve_vmaxnmvq_p_f32, "", "n")
BUILTIN(__builtin_arm_mve_vmaxq_m_s16, "", "n")
BUILTIN(__builtin_arm_mve_vmaxq_m_s32, "", "n")
BUILTIN(__builtin_arm_mve_vmaxq_m_s8, "", "n")
BUILTIN(__builtin_arm_mve_vmaxq_m_u16, "", "n")
BUILTIN(__builtin_arm_mve_vmaxq_m_u32, "", "n")
BUILTIN(__builtin_arm_mve_vmaxq_m_u8, "", "n")
BUILTIN(__builtin_arm_mve_vmaxq_s16, "", "n")
BUILTIN(__builtin_arm_mve_vmaxq_s32, "", "n")
BUILTIN(__builtin_arm_mve_vmaxq_s8, "", "n")
BUILTIN(__builtin_arm_mve_vmaxq_u16, "", "n")
BUILTIN(__builtin_arm_mve_vmaxq_u32, "", "n")
BUILTIN(__builtin_arm_mve_vmaxq_u8, "", "n")
BUILTIN(__builtin_arm_mve_vmaxq_x_s16, "", "n")
BUILTIN(__builtin_arm_mve_vmaxq_x_s32, "", "n")
BUILTIN(__builtin_arm_mve_vmaxq_x_s8, "", "n")
BUILTIN(__builtin_arm_mve_vmaxq_x_u16, "", "n")
BUILTIN(__builtin_arm_mve_vmaxq_x_u32, "", "n")
BUILTIN(__builtin_arm_mve_vmaxq_x_u8, "", "n")
BUILTIN(__builtin_arm_mve_vmaxvq_p_s16, "", "n")
BUILTIN(__builtin_arm_mve_vmaxvq_p_s32, "", "n")
BUILTIN(__builtin_arm_mve_vmaxvq_p_s8, "", "n")
BUILTIN(__builtin_arm_mve_vmaxvq_p_u16, "", "n")
BUILTIN(__builtin_arm_mve_vmaxvq_p_u32, "", "n")
BUILTIN(__builtin_arm_mve_vmaxvq_p_u8, "", "n")
BUILTIN(__builtin_arm_mve_vmaxvq_s16, "", "n")
BUILTIN(__builtin_arm_mve_vmaxvq_s32, "", "n")
BUILTIN(__builtin_arm_mve_vmaxvq_s8, "", "n")
BUILTIN(__builtin_arm_mve_vmaxvq_u16, "", "n")
BUILTIN(__builtin_arm_mve_vmaxvq_u32, "", "n")
BUILTIN(__builtin_arm_mve_vmaxvq_u8, "", "n")
BUILTIN(__builtin_arm_mve_vminaq_m_s16, "", "n")
BUILTIN(__builtin_arm_mve_vminaq_m_s32, "", "n")
BUILTIN(__builtin_arm_mve_vminaq_m_s8, "", "n")
BUILTIN(__builtin_arm_mve_vminaq_s16, "", "n")
BUILTIN(__builtin_arm_mve_vminaq_s32, "", "n")
BUILTIN(__builtin_arm_mve_vminaq_s8, "", "n")
BUILTIN(__builtin_arm_mve_vminavq_p_s16, "", "n")
BUILTIN(__builtin_arm_mve_vminavq_p_s32, "", "n")
BUILTIN(__builtin_arm_mve_vminavq_p_s8, "", "n")
BUILTIN(__builtin_arm_mve_vminavq_s16, "", "n")
BUILTIN(__builtin_arm_mve_vminavq_s32, "", "n")
BUILTIN(__builtin_arm_mve_vminavq_s8, "", "n")
BUILTIN(__builtin_arm_mve_vminnmaq_f16, "", "n")
BUILTIN(__builtin_arm_mve_vminnmaq_f32, "", "n")
BUILTIN(__builtin_arm_mve_vminnmaq_m_f16, "", "n")
BUILTIN(__builtin_arm_mve_vminnmaq_m_f32, "", "n")
BUILTIN(__builtin_arm_mve_vminnmavq_f16, "", "n")
BUILTIN(__builtin_arm_mve_vminnmavq_f32, "", "n")
BUILTIN(__builtin_arm_mve_vminnmavq_p_f16, "", "n")
BUILTIN(__builtin_arm_mve_vminnmavq_p_f32, "", "n")
BUILTIN(__builtin_arm_mve_vminnmq_f16, "", "n")
BUILTIN(__builtin_arm_mve_vminnmq_f32, "", "n")
BUILTIN(__builtin_arm_mve_vminnmq_m_f16, "", "n")
BUILTIN(__builtin_arm_mve_vminnmq_m_f32, "", "n")
BUILTIN(__builtin_arm_mve_vminnmq_x_f16, "", "n")
BUILTIN(__builtin_arm_mve_vminnmq_x_f32, "", "n")
BUILTIN(__builtin_arm_mve_vminnmvq_f16, "", "n")
BUILTIN(__builtin_arm_mve_vminnmvq_f32, "", "n")
BUILTIN(__builtin_arm_mve_vminnmvq_p_f16, "", "n")
BUILTIN(__builtin_arm_mve_vminnmvq_p_f32, "", "n")
BUILTIN(__builtin_arm_mve_vminq_m_s16, "", "n")
BUILTIN(__builtin_arm_mve_vminq_m_s32, "", "n")
BUILTIN(__builtin_arm_mve_vminq_m_s8, "", "n")
BUILTIN(__builtin_arm_mve_vminq_m_u16, "", "n")
BUILTIN(__builtin_arm_mve_vminq_m_u32, "", "n")
BUILTIN(__builtin_arm_mve_vminq_m_u8, "", "n")
BUILTIN(__builtin_arm_mve_vminq_s16, "", "n")
BUILTIN(__builtin_arm_mve_vminq_s32, "", "n")
BUILTIN(__builtin_arm_mve_vminq_s8, "", "n")
BUILTIN(__builtin_arm_mve_vminq_u16, "", "n")
BUILTIN(__builtin_arm_mve_vminq_u32, "", "n")
BUILTIN(__builtin_arm_mve_vminq_u8, "", "n")
BUILTIN(__builtin_arm_mve_vminq_x_s16, "", "n")
BUILTIN(__builtin_arm_mve_vminq_x_s32, "", "n")
BUILTIN(__builtin_arm_mve_vminq_x_s8, "", "n")
BUILTIN(__builtin_arm_mve_vminq_x_u16, "", "n")
BUILTIN(__builtin_arm_mve_vminq_x_u32, "", "n")
BUILTIN(__builtin_arm_mve_vminq_x_u8, "", "n")
BUILTIN(__builtin_arm_mve_vminvq_p_s16, "", "n")
BUILTIN(__builtin_arm_mve_vminvq_p_s32, "", "n")
BUILTIN(__builtin_arm_mve_vminvq_p_s8, "", "n")
BUILTIN(__builtin_arm_mve_vminvq_p_u16, "", "n")
BUILTIN(__builtin_arm_mve_vminvq_p_u32, "", "n")
BUILTIN(__builtin_arm_mve_vminvq_p_u8, "", "n")
BUILTIN(__builtin_arm_mve_vminvq_s16, "", "n")
BUILTIN(__builtin_arm_mve_vminvq_s32, "", "n")
BUILTIN(__builtin_arm_mve_vminvq_s8, "", "n")
BUILTIN(__builtin_arm_mve_vminvq_u16, "", "n")
BUILTIN(__builtin_arm_mve_vminvq_u32, "", "n")
BUILTIN(__builtin_arm_mve_vminvq_u8, "", "n")
BUILTIN(__builtin_arm_mve_vmladavaq_p_s16, "", "n")
BUILTIN(__builtin_arm_mve_vmladavaq_p_s32, "", "n")
BUILTIN(__builtin_arm_mve_vmladavaq_p_s8, "", "n")
BUILTIN(__builtin_arm_mve_vmladavaq_p_u16, "", "n")
BUILTIN(__builtin_arm_mve_vmladavaq_p_u32, "", "n")
BUILTIN(__builtin_arm_mve_vmladavaq_p_u8, "", "n")
BUILTIN(__builtin_arm_mve_vmladavaq_s16, "", "n")
BUILTIN(__builtin_arm_mve_vmladavaq_s32, "", "n")
BUILTIN(__builtin_arm_mve_vmladavaq_s8, "", "n")
BUILTIN(__builtin_arm_mve_vmladavaq_u16, "", "n")
BUILTIN(__builtin_arm_mve_vmladavaq_u32, "", "n")
BUILTIN(__builtin_arm_mve_vmladavaq_u8, "", "n")
BUILTIN(__builtin_arm_mve_vmladavaxq_p_s16, "", "n")
BUILTIN(__builtin_arm_mve_vmladavaxq_p_s32, "", "n")
BUILTIN(__builtin_arm_mve_vmladavaxq_p_s8, "", "n")
BUILTIN(__builtin_arm_mve_vmladavaxq_s16, "", "n")
BUILTIN(__builtin_arm_mve_vmladavaxq_s32, "", "n")
BUILTIN(__builtin_arm_mve_vmladavaxq_s8, "", "n")
BUILTIN(__builtin_arm_mve_vmladavq_p_s16, "", "n")
BUILTIN(__builtin_arm_mve_vmladavq_p_s32, "", "n")
BUILTIN(__builtin_arm_mve_vmladavq_p_s8, "", "n")
BUILTIN(__builtin_arm_mve_vmladavq_p_u16, "", "n")
BUILTIN(__builtin_arm_mve_vmladavq_p_u32, "", "n")
BUILTIN(__builtin_arm_mve_vmladavq_p_u8, "", "n")
BUILTIN(__builtin_arm_mve_vmladavq_s16, "", "n")
BUILTIN(__builtin_arm_mve_vmladavq_s32, "", "n")
BUILTIN(__builtin_arm_mve_vmladavq_s8, "", "n")
BUILTIN(__builtin_arm_mve_vmladavq_u16, "", "n")
BUILTIN(__builtin_arm_mve_vmladavq_u32, "", "n")
BUILTIN(__builtin_arm_mve_vmladavq_u8, "", "n")
BUILTIN(__builtin_arm_mve_vmladavxq_p_s16, "", "n")
BUILTIN(__builtin_arm_mve_vmladavxq_p_s32, "", "n")
BUILTIN(__builtin_arm_mve_vmladavxq_p_s8, "", "n")
BUILTIN(__builtin_arm_mve_vmladavxq_s16, "", "n")
BUILTIN(__builtin_arm_mve_vmladavxq_s32, "", "n")
BUILTIN(__builtin_arm_mve_vmladavxq_s8, "", "n")
BUILTIN(__builtin_arm_mve_vmlaldavaq_p_s16, "", "n")
BUILTIN(__builtin_arm_mve_vmlaldavaq_p_s32, "", "n")
BUILTIN(__builtin_arm_mve_vmlaldavaq_p_u16, "", "n")
BUILTIN(__builtin_arm_mve_vmlaldavaq_p_u32, "", "n")
BUILTIN(__builtin_arm_mve_vmlaldavaq_s16, "", "n")
BUILTIN(__builtin_arm_mve_vmlaldavaq_s32, "", "n")
BUILTIN(__builtin_arm_mve_vmlaldavaq_u16, "", "n")
BUILTIN(__builtin_arm_mve_vmlaldavaq_u32, "", "n")
BUILTIN(__builtin_arm_mve_vmlaldavaxq_p_s16, "", "n")
BUILTIN(__builtin_arm_mve_vmlaldavaxq_p_s32, "", "n")
BUILTIN(__builtin_arm_mve_vmlaldavaxq_s16, "", "n")
BUILTIN(__builtin_arm_mve_vmlaldavaxq_s32, "", "n")
BUILTIN(__builtin_arm_mve_vmlaldavq_p_s16, "", "n")
BUILTIN(__builtin_arm_mve_vmlaldavq_p_s32, "", "n")
BUILTIN(__builtin_arm_mve_vmlaldavq_p_u16, "", "n")
BUILTIN(__builtin_arm_mve_vmlaldavq_p_u32, "", "n")
BUILTIN(__builtin_arm_mve_vmlaldavq_s16, "", "n")
BUILTIN(__builtin_arm_mve_vmlaldavq_s32, "", "n")
BUILTIN(__builtin_arm_mve_vmlaldavq_u16, "", "n")
BUILTIN(__builtin_arm_mve_vmlaldavq_u32, "", "n")
BUILTIN(__builtin_arm_mve_vmlaldavxq_p_s16, "", "n")
BUILTIN(__builtin_arm_mve_vmlaldavxq_p_s32, "", "n")
BUILTIN(__builtin_arm_mve_vmlaldavxq_s16, "", "n")
BUILTIN(__builtin_arm_mve_vmlaldavxq_s32, "", "n")
BUILTIN(__builtin_arm_mve_vmlaq_m_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vmlaq_m_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vmlaq_m_n_s8, "", "n")
BUILTIN(__builtin_arm_mve_vmlaq_m_n_u16, "", "n")
BUILTIN(__builtin_arm_mve_vmlaq_m_n_u32, "", "n")
BUILTIN(__builtin_arm_mve_vmlaq_m_n_u8, "", "n")
BUILTIN(__builtin_arm_mve_vmlaq_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vmlaq_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vmlaq_n_s8, "", "n")
BUILTIN(__builtin_arm_mve_vmlaq_n_u16, "", "n")
BUILTIN(__builtin_arm_mve_vmlaq_n_u32, "", "n")
BUILTIN(__builtin_arm_mve_vmlaq_n_u8, "", "n")
BUILTIN(__builtin_arm_mve_vmlasq_m_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vmlasq_m_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vmlasq_m_n_s8, "", "n")
BUILTIN(__builtin_arm_mve_vmlasq_m_n_u16, "", "n")
BUILTIN(__builtin_arm_mve_vmlasq_m_n_u32, "", "n")
BUILTIN(__builtin_arm_mve_vmlasq_m_n_u8, "", "n")
BUILTIN(__builtin_arm_mve_vmlasq_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vmlasq_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vmlasq_n_s8, "", "n")
BUILTIN(__builtin_arm_mve_vmlasq_n_u16, "", "n")
BUILTIN(__builtin_arm_mve_vmlasq_n_u32, "", "n")
BUILTIN(__builtin_arm_mve_vmlasq_n_u8, "", "n")
BUILTIN(__builtin_arm_mve_vmlsdavaq_p_s16, "", "n")
BUILTIN(__builtin_arm_mve_vmlsdavaq_p_s32, "", "n")
BUILTIN(__builtin_arm_mve_vmlsdavaq_p_s8, "", "n")
BUILTIN(__builtin_arm_mve_vmlsdavaq_s16, "", "n")
BUILTIN(__builtin_arm_mve_vmlsdavaq_s32, "", "n")
BUILTIN(__builtin_arm_mve_vmlsdavaq_s8, "", "n")
BUILTIN(__builtin_arm_mve_vmlsdavaxq_p_s16, "", "n")
BUILTIN(__builtin_arm_mve_vmlsdavaxq_p_s32, "", "n")
BUILTIN(__builtin_arm_mve_vmlsdavaxq_p_s8, "", "n")
BUILTIN(__builtin_arm_mve_vmlsdavaxq_s16, "", "n")
BUILTIN(__builtin_arm_mve_vmlsdavaxq_s32, "", "n")
BUILTIN(__builtin_arm_mve_vmlsdavaxq_s8, "", "n")
BUILTIN(__builtin_arm_mve_vmlsdavq_p_s16, "", "n")
BUILTIN(__builtin_arm_mve_vmlsdavq_p_s32, "", "n")
BUILTIN(__builtin_arm_mve_vmlsdavq_p_s8, "", "n")
BUILTIN(__builtin_arm_mve_vmlsdavq_s16, "", "n")
BUILTIN(__builtin_arm_mve_vmlsdavq_s32, "", "n")
BUILTIN(__builtin_arm_mve_vmlsdavq_s8, "", "n")
BUILTIN(__builtin_arm_mve_vmlsdavxq_p_s16, "", "n")
BUILTIN(__builtin_arm_mve_vmlsdavxq_p_s32, "", "n")
BUILTIN(__builtin_arm_mve_vmlsdavxq_p_s8, "", "n")
BUILTIN(__builtin_arm_mve_vmlsdavxq_s16, "", "n")
BUILTIN(__builtin_arm_mve_vmlsdavxq_s32, "", "n")
BUILTIN(__builtin_arm_mve_vmlsdavxq_s8, "", "n")
BUILTIN(__builtin_arm_mve_vmlsldavaq_p_s16, "", "n")
BUILTIN(__builtin_arm_mve_vmlsldavaq_p_s32, "", "n")
BUILTIN(__builtin_arm_mve_vmlsldavaq_s16, "", "n")
BUILTIN(__builtin_arm_mve_vmlsldavaq_s32, "", "n")
BUILTIN(__builtin_arm_mve_vmlsldavaxq_p_s16, "", "n")
BUILTIN(__builtin_arm_mve_vmlsldavaxq_p_s32, "", "n")
BUILTIN(__builtin_arm_mve_vmlsldavaxq_s16, "", "n")
BUILTIN(__builtin_arm_mve_vmlsldavaxq_s32, "", "n")
BUILTIN(__builtin_arm_mve_vmlsldavq_p_s16, "", "n")
BUILTIN(__builtin_arm_mve_vmlsldavq_p_s32, "", "n")
BUILTIN(__builtin_arm_mve_vmlsldavq_s16, "", "n")
BUILTIN(__builtin_arm_mve_vmlsldavq_s32, "", "n")
BUILTIN(__builtin_arm_mve_vmlsldavxq_p_s16, "", "n")
BUILTIN(__builtin_arm_mve_vmlsldavxq_p_s32, "", "n")
BUILTIN(__builtin_arm_mve_vmlsldavxq_s16, "", "n")
BUILTIN(__builtin_arm_mve_vmlsldavxq_s32, "", "n")
BUILTIN(__builtin_arm_mve_vmovlbq_m_s16, "", "n")
BUILTIN(__builtin_arm_mve_vmovlbq_m_s8, "", "n")
BUILTIN(__builtin_arm_mve_vmovlbq_m_u16, "", "n")
BUILTIN(__builtin_arm_mve_vmovlbq_m_u8, "", "n")
BUILTIN(__builtin_arm_mve_vmovlbq_s16, "", "n")
BUILTIN(__builtin_arm_mve_vmovlbq_s8, "", "n")
BUILTIN(__builtin_arm_mve_vmovlbq_u16, "", "n")
BUILTIN(__builtin_arm_mve_vmovlbq_u8, "", "n")
BUILTIN(__builtin_arm_mve_vmovlbq_x_s16, "", "n")
BUILTIN(__builtin_arm_mve_vmovlbq_x_s8, "", "n")
BUILTIN(__builtin_arm_mve_vmovlbq_x_u16, "", "n")
BUILTIN(__builtin_arm_mve_vmovlbq_x_u8, "", "n")
BUILTIN(__builtin_arm_mve_vmovltq_m_s16, "", "n")
BUILTIN(__builtin_arm_mve_vmovltq_m_s8, "", "n")
BUILTIN(__builtin_arm_mve_vmovltq_m_u16, "", "n")
BUILTIN(__builtin_arm_mve_vmovltq_m_u8, "", "n")
BUILTIN(__builtin_arm_mve_vmovltq_s16, "", "n")
BUILTIN(__builtin_arm_mve_vmovltq_s8, "", "n")
BUILTIN(__builtin_arm_mve_vmovltq_u16, "", "n")
BUILTIN(__builtin_arm_mve_vmovltq_u8, "", "n")
BUILTIN(__builtin_arm_mve_vmovltq_x_s16, "", "n")
BUILTIN(__builtin_arm_mve_vmovltq_x_s8, "", "n")
BUILTIN(__builtin_arm_mve_vmovltq_x_u16, "", "n")
BUILTIN(__builtin_arm_mve_vmovltq_x_u8, "", "n")
BUILTIN(__builtin_arm_mve_vmovnbq_m_s16, "", "n")
BUILTIN(__builtin_arm_mve_vmovnbq_m_s32, "", "n")
BUILTIN(__builtin_arm_mve_vmovnbq_m_u16, "", "n")
BUILTIN(__builtin_arm_mve_vmovnbq_m_u32, "", "n")
BUILTIN(__builtin_arm_mve_vmovnbq_s16, "", "n")
BUILTIN(__builtin_arm_mve_vmovnbq_s32, "", "n")
BUILTIN(__builtin_arm_mve_vmovnbq_u16, "", "n")
BUILTIN(__builtin_arm_mve_vmovnbq_u32, "", "n")
BUILTIN(__builtin_arm_mve_vmovntq_m_s16, "", "n")
BUILTIN(__builtin_arm_mve_vmovntq_m_s32, "", "n")
BUILTIN(__builtin_arm_mve_vmovntq_m_u16, "", "n")
BUILTIN(__builtin_arm_mve_vmovntq_m_u32, "", "n")
BUILTIN(__builtin_arm_mve_vmovntq_s16, "", "n")
BUILTIN(__builtin_arm_mve_vmovntq_s32, "", "n")
BUILTIN(__builtin_arm_mve_vmovntq_u16, "", "n")
BUILTIN(__builtin_arm_mve_vmovntq_u32, "", "n")
BUILTIN(__builtin_arm_mve_vmulhq_m_s16, "", "n")
BUILTIN(__builtin_arm_mve_vmulhq_m_s32, "", "n")
BUILTIN(__builtin_arm_mve_vmulhq_m_s8, "", "n")
BUILTIN(__builtin_arm_mve_vmulhq_m_u16, "", "n")
BUILTIN(__builtin_arm_mve_vmulhq_m_u32, "", "n")
BUILTIN(__builtin_arm_mve_vmulhq_m_u8, "", "n")
BUILTIN(__builtin_arm_mve_vmulhq_s16, "", "n")
BUILTIN(__builtin_arm_mve_vmulhq_s32, "", "n")
BUILTIN(__builtin_arm_mve_vmulhq_s8, "", "n")
BUILTIN(__builtin_arm_mve_vmulhq_u16, "", "n")
BUILTIN(__builtin_arm_mve_vmulhq_u32, "", "n")
BUILTIN(__builtin_arm_mve_vmulhq_u8, "", "n")
BUILTIN(__builtin_arm_mve_vmulhq_x_s16, "", "n")
BUILTIN(__builtin_arm_mve_vmulhq_x_s32, "", "n")
BUILTIN(__builtin_arm_mve_vmulhq_x_s8, "", "n")
BUILTIN(__builtin_arm_mve_vmulhq_x_u16, "", "n")
BUILTIN(__builtin_arm_mve_vmulhq_x_u32, "", "n")
BUILTIN(__builtin_arm_mve_vmulhq_x_u8, "", "n")
BUILTIN(__builtin_arm_mve_vmullbq_int_m_s16, "", "n")
BUILTIN(__builtin_arm_mve_vmullbq_int_m_s32, "", "n")
BUILTIN(__builtin_arm_mve_vmullbq_int_m_s8, "", "n")
BUILTIN(__builtin_arm_mve_vmullbq_int_m_u16, "", "n")
BUILTIN(__builtin_arm_mve_vmullbq_int_m_u32, "", "n")
BUILTIN(__builtin_arm_mve_vmullbq_int_m_u8, "", "n")
BUILTIN(__builtin_arm_mve_vmullbq_int_s16, "", "n")
BUILTIN(__builtin_arm_mve_vmullbq_int_s32, "", "n")
BUILTIN(__builtin_arm_mve_vmullbq_int_s8, "", "n")
BUILTIN(__builtin_arm_mve_vmullbq_int_u16, "", "n")
BUILTIN(__builtin_arm_mve_vmullbq_int_u32, "", "n")
BUILTIN(__builtin_arm_mve_vmullbq_int_u8, "", "n")
BUILTIN(__builtin_arm_mve_vmullbq_int_x_s16, "", "n")
BUILTIN(__builtin_arm_mve_vmullbq_int_x_s32, "", "n")
BUILTIN(__builtin_arm_mve_vmullbq_int_x_s8, "", "n")
BUILTIN(__builtin_arm_mve_vmullbq_int_x_u16, "", "n")
BUILTIN(__builtin_arm_mve_vmullbq_int_x_u32, "", "n")
BUILTIN(__builtin_arm_mve_vmullbq_int_x_u8, "", "n")
BUILTIN(__builtin_arm_mve_vmullbq_poly_m_p16, "", "n")
BUILTIN(__builtin_arm_mve_vmullbq_poly_m_p8, "", "n")
BUILTIN(__builtin_arm_mve_vmullbq_poly_p16, "", "n")
BUILTIN(__builtin_arm_mve_vmullbq_poly_p8, "", "n")
BUILTIN(__builtin_arm_mve_vmullbq_poly_x_p16, "", "n")
BUILTIN(__builtin_arm_mve_vmullbq_poly_x_p8, "", "n")
BUILTIN(__builtin_arm_mve_vmulltq_int_m_s16, "", "n")
BUILTIN(__builtin_arm_mve_vmulltq_int_m_s32, "", "n")
BUILTIN(__builtin_arm_mve_vmulltq_int_m_s8, "", "n")
BUILTIN(__builtin_arm_mve_vmulltq_int_m_u16, "", "n")
BUILTIN(__builtin_arm_mve_vmulltq_int_m_u32, "", "n")
BUILTIN(__builtin_arm_mve_vmulltq_int_m_u8, "", "n")
BUILTIN(__builtin_arm_mve_vmulltq_int_s16, "", "n")
BUILTIN(__builtin_arm_mve_vmulltq_int_s32, "", "n")
BUILTIN(__builtin_arm_mve_vmulltq_int_s8, "", "n")
BUILTIN(__builtin_arm_mve_vmulltq_int_u16, "", "n")
BUILTIN(__builtin_arm_mve_vmulltq_int_u32, "", "n")
BUILTIN(__builtin_arm_mve_vmulltq_int_u8, "", "n")
BUILTIN(__builtin_arm_mve_vmulltq_int_x_s16, "", "n")
BUILTIN(__builtin_arm_mve_vmulltq_int_x_s32, "", "n")
BUILTIN(__builtin_arm_mve_vmulltq_int_x_s8, "", "n")
BUILTIN(__builtin_arm_mve_vmulltq_int_x_u16, "", "n")
BUILTIN(__builtin_arm_mve_vmulltq_int_x_u32, "", "n")
BUILTIN(__builtin_arm_mve_vmulltq_int_x_u8, "", "n")
BUILTIN(__builtin_arm_mve_vmulltq_poly_m_p16, "", "n")
BUILTIN(__builtin_arm_mve_vmulltq_poly_m_p8, "", "n")
BUILTIN(__builtin_arm_mve_vmulltq_poly_p16, "", "n")
BUILTIN(__builtin_arm_mve_vmulltq_poly_p8, "", "n")
BUILTIN(__builtin_arm_mve_vmulltq_poly_x_p16, "", "n")
BUILTIN(__builtin_arm_mve_vmulltq_poly_x_p8, "", "n")
BUILTIN(__builtin_arm_mve_vmulq_f16, "", "n")
BUILTIN(__builtin_arm_mve_vmulq_f32, "", "n")
BUILTIN(__builtin_arm_mve_vmulq_m_f16, "", "n")
BUILTIN(__builtin_arm_mve_vmulq_m_f32, "", "n")
BUILTIN(__builtin_arm_mve_vmulq_m_n_f16, "", "n")
BUILTIN(__builtin_arm_mve_vmulq_m_n_f32, "", "n")
BUILTIN(__builtin_arm_mve_vmulq_m_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vmulq_m_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vmulq_m_n_s8, "", "n")
BUILTIN(__builtin_arm_mve_vmulq_m_n_u16, "", "n")
BUILTIN(__builtin_arm_mve_vmulq_m_n_u32, "", "n")
BUILTIN(__builtin_arm_mve_vmulq_m_n_u8, "", "n")
BUILTIN(__builtin_arm_mve_vmulq_m_s16, "", "n")
BUILTIN(__builtin_arm_mve_vmulq_m_s32, "", "n")
BUILTIN(__builtin_arm_mve_vmulq_m_s8, "", "n")
BUILTIN(__builtin_arm_mve_vmulq_m_u16, "", "n")
BUILTIN(__builtin_arm_mve_vmulq_m_u32, "", "n")
BUILTIN(__builtin_arm_mve_vmulq_m_u8, "", "n")
BUILTIN(__builtin_arm_mve_vmulq_n_f16, "", "n")
BUILTIN(__builtin_arm_mve_vmulq_n_f32, "", "n")
BUILTIN(__builtin_arm_mve_vmulq_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vmulq_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vmulq_n_s8, "", "n")
BUILTIN(__builtin_arm_mve_vmulq_n_u16, "", "n")
BUILTIN(__builtin_arm_mve_vmulq_n_u32, "", "n")
BUILTIN(__builtin_arm_mve_vmulq_n_u8, "", "n")
BUILTIN(__builtin_arm_mve_vmulq_s16, "", "n")
BUILTIN(__builtin_arm_mve_vmulq_s32, "", "n")
BUILTIN(__builtin_arm_mve_vmulq_s8, "", "n")
BUILTIN(__builtin_arm_mve_vmulq_u16, "", "n")
BUILTIN(__builtin_arm_mve_vmulq_u32, "", "n")
BUILTIN(__builtin_arm_mve_vmulq_u8, "", "n")
BUILTIN(__builtin_arm_mve_vmulq_x_f16, "", "n")
BUILTIN(__builtin_arm_mve_vmulq_x_f32, "", "n")
BUILTIN(__builtin_arm_mve_vmulq_x_n_f16, "", "n")
BUILTIN(__builtin_arm_mve_vmulq_x_n_f32, "", "n")
BUILTIN(__builtin_arm_mve_vmulq_x_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vmulq_x_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vmulq_x_n_s8, "", "n")
BUILTIN(__builtin_arm_mve_vmulq_x_n_u16, "", "n")
BUILTIN(__builtin_arm_mve_vmulq_x_n_u32, "", "n")
BUILTIN(__builtin_arm_mve_vmulq_x_n_u8, "", "n")
BUILTIN(__builtin_arm_mve_vmulq_x_s16, "", "n")
BUILTIN(__builtin_arm_mve_vmulq_x_s32, "", "n")
BUILTIN(__builtin_arm_mve_vmulq_x_s8, "", "n")
BUILTIN(__builtin_arm_mve_vmulq_x_u16, "", "n")
BUILTIN(__builtin_arm_mve_vmulq_x_u32, "", "n")
BUILTIN(__builtin_arm_mve_vmulq_x_u8, "", "n")
BUILTIN(__builtin_arm_mve_vmvnq_m_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vmvnq_m_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vmvnq_m_n_u16, "", "n")
BUILTIN(__builtin_arm_mve_vmvnq_m_n_u32, "", "n")
BUILTIN(__builtin_arm_mve_vmvnq_m_s16, "", "n")
BUILTIN(__builtin_arm_mve_vmvnq_m_s32, "", "n")
BUILTIN(__builtin_arm_mve_vmvnq_m_s8, "", "n")
BUILTIN(__builtin_arm_mve_vmvnq_m_u16, "", "n")
BUILTIN(__builtin_arm_mve_vmvnq_m_u32, "", "n")
BUILTIN(__builtin_arm_mve_vmvnq_m_u8, "", "n")
BUILTIN(__builtin_arm_mve_vmvnq_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vmvnq_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vmvnq_n_u16, "", "n")
BUILTIN(__builtin_arm_mve_vmvnq_n_u32, "", "n")
BUILTIN(__builtin_arm_mve_vmvnq_s16, "", "n")
BUILTIN(__builtin_arm_mve_vmvnq_s32, "", "n")
BUILTIN(__builtin_arm_mve_vmvnq_s8, "", "n")
BUILTIN(__builtin_arm_mve_vmvnq_u16, "", "n")
BUILTIN(__builtin_arm_mve_vmvnq_u32, "", "n")
BUILTIN(__builtin_arm_mve_vmvnq_u8, "", "n")
BUILTIN(__builtin_arm_mve_vmvnq_x_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vmvnq_x_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vmvnq_x_n_u16, "", "n")
BUILTIN(__builtin_arm_mve_vmvnq_x_n_u32, "", "n")
BUILTIN(__builtin_arm_mve_vmvnq_x_s16, "", "n")
BUILTIN(__builtin_arm_mve_vmvnq_x_s32, "", "n")
BUILTIN(__builtin_arm_mve_vmvnq_x_s8, "", "n")
BUILTIN(__builtin_arm_mve_vmvnq_x_u16, "", "n")
BUILTIN(__builtin_arm_mve_vmvnq_x_u32, "", "n")
BUILTIN(__builtin_arm_mve_vmvnq_x_u8, "", "n")
BUILTIN(__builtin_arm_mve_vnegq_f16, "", "n")
BUILTIN(__builtin_arm_mve_vnegq_f32, "", "n")
BUILTIN(__builtin_arm_mve_vnegq_m_f16, "", "n")
BUILTIN(__builtin_arm_mve_vnegq_m_f32, "", "n")
BUILTIN(__builtin_arm_mve_vnegq_m_s16, "", "n")
BUILTIN(__builtin_arm_mve_vnegq_m_s32, "", "n")
BUILTIN(__builtin_arm_mve_vnegq_m_s8, "", "n")
BUILTIN(__builtin_arm_mve_vnegq_s16, "", "n")
BUILTIN(__builtin_arm_mve_vnegq_s32, "", "n")
BUILTIN(__builtin_arm_mve_vnegq_s8, "", "n")
BUILTIN(__builtin_arm_mve_vnegq_x_f16, "", "n")
BUILTIN(__builtin_arm_mve_vnegq_x_f32, "", "n")
BUILTIN(__builtin_arm_mve_vnegq_x_s16, "", "n")
BUILTIN(__builtin_arm_mve_vnegq_x_s32, "", "n")
BUILTIN(__builtin_arm_mve_vnegq_x_s8, "", "n")
BUILTIN(__builtin_arm_mve_vornq_f16, "", "n")
BUILTIN(__builtin_arm_mve_vornq_f32, "", "n")
BUILTIN(__builtin_arm_mve_vornq_m_f16, "", "n")
BUILTIN(__builtin_arm_mve_vornq_m_f32, "", "n")
BUILTIN(__builtin_arm_mve_vornq_m_s16, "", "n")
BUILTIN(__builtin_arm_mve_vornq_m_s32, "", "n")
BUILTIN(__builtin_arm_mve_vornq_m_s8, "", "n")
BUILTIN(__builtin_arm_mve_vornq_m_u16, "", "n")
BUILTIN(__builtin_arm_mve_vornq_m_u32, "", "n")
BUILTIN(__builtin_arm_mve_vornq_m_u8, "", "n")
BUILTIN(__builtin_arm_mve_vornq_s16, "", "n")
BUILTIN(__builtin_arm_mve_vornq_s32, "", "n")
BUILTIN(__builtin_arm_mve_vornq_s8, "", "n")
BUILTIN(__builtin_arm_mve_vornq_u16, "", "n")
BUILTIN(__builtin_arm_mve_vornq_u32, "", "n")
BUILTIN(__builtin_arm_mve_vornq_u8, "", "n")
BUILTIN(__builtin_arm_mve_vornq_x_f16, "", "n")
BUILTIN(__builtin_arm_mve_vornq_x_f32, "", "n")
BUILTIN(__builtin_arm_mve_vornq_x_s16, "", "n")
BUILTIN(__builtin_arm_mve_vornq_x_s32, "", "n")
BUILTIN(__builtin_arm_mve_vornq_x_s8, "", "n")
BUILTIN(__builtin_arm_mve_vornq_x_u16, "", "n")
BUILTIN(__builtin_arm_mve_vornq_x_u32, "", "n")
BUILTIN(__builtin_arm_mve_vornq_x_u8, "", "n")
BUILTIN(__builtin_arm_mve_vorrq_f16, "", "n")
BUILTIN(__builtin_arm_mve_vorrq_f32, "", "n")
BUILTIN(__builtin_arm_mve_vorrq_m_f16, "", "n")
BUILTIN(__builtin_arm_mve_vorrq_m_f32, "", "n")
BUILTIN(__builtin_arm_mve_vorrq_m_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vorrq_m_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vorrq_m_n_u16, "", "n")
BUILTIN(__builtin_arm_mve_vorrq_m_n_u32, "", "n")
BUILTIN(__builtin_arm_mve_vorrq_m_s16, "", "n")
BUILTIN(__builtin_arm_mve_vorrq_m_s32, "", "n")
BUILTIN(__builtin_arm_mve_vorrq_m_s8, "", "n")
BUILTIN(__builtin_arm_mve_vorrq_m_u16, "", "n")
BUILTIN(__builtin_arm_mve_vorrq_m_u32, "", "n")
BUILTIN(__builtin_arm_mve_vorrq_m_u8, "", "n")
BUILTIN(__builtin_arm_mve_vorrq_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vorrq_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vorrq_n_u16, "", "n")
BUILTIN(__builtin_arm_mve_vorrq_n_u32, "", "n")
BUILTIN(__builtin_arm_mve_vorrq_s16, "", "n")
BUILTIN(__builtin_arm_mve_vorrq_s32, "", "n")
BUILTIN(__builtin_arm_mve_vorrq_s8, "", "n")
BUILTIN(__builtin_arm_mve_vorrq_u16, "", "n")
BUILTIN(__builtin_arm_mve_vorrq_u32, "", "n")
BUILTIN(__builtin_arm_mve_vorrq_u8, "", "n")
BUILTIN(__builtin_arm_mve_vorrq_x_f16, "", "n")
BUILTIN(__builtin_arm_mve_vorrq_x_f32, "", "n")
BUILTIN(__builtin_arm_mve_vorrq_x_s16, "", "n")
BUILTIN(__builtin_arm_mve_vorrq_x_s32, "", "n")
BUILTIN(__builtin_arm_mve_vorrq_x_s8, "", "n")
BUILTIN(__builtin_arm_mve_vorrq_x_u16, "", "n")
BUILTIN(__builtin_arm_mve_vorrq_x_u32, "", "n")
BUILTIN(__builtin_arm_mve_vorrq_x_u8, "", "n")
BUILTIN(__builtin_arm_mve_vpnot, "", "n")
BUILTIN(__builtin_arm_mve_vpselq_f16, "", "n")
BUILTIN(__builtin_arm_mve_vpselq_f32, "", "n")
BUILTIN(__builtin_arm_mve_vpselq_s16, "", "n")
BUILTIN(__builtin_arm_mve_vpselq_s32, "", "n")
BUILTIN(__builtin_arm_mve_vpselq_s64, "", "n")
BUILTIN(__builtin_arm_mve_vpselq_s8, "", "n")
BUILTIN(__builtin_arm_mve_vpselq_u16, "", "n")
BUILTIN(__builtin_arm_mve_vpselq_u32, "", "n")
BUILTIN(__builtin_arm_mve_vpselq_u64, "", "n")
BUILTIN(__builtin_arm_mve_vpselq_u8, "", "n")
BUILTIN(__builtin_arm_mve_vqabsq_m_s16, "", "n")
BUILTIN(__builtin_arm_mve_vqabsq_m_s32, "", "n")
BUILTIN(__builtin_arm_mve_vqabsq_m_s8, "", "n")
BUILTIN(__builtin_arm_mve_vqabsq_s16, "", "n")
BUILTIN(__builtin_arm_mve_vqabsq_s32, "", "n")
BUILTIN(__builtin_arm_mve_vqabsq_s8, "", "n")
BUILTIN(__builtin_arm_mve_vqaddq_m_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vqaddq_m_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vqaddq_m_n_s8, "", "n")
BUILTIN(__builtin_arm_mve_vqaddq_m_n_u16, "", "n")
BUILTIN(__builtin_arm_mve_vqaddq_m_n_u32, "", "n")
BUILTIN(__builtin_arm_mve_vqaddq_m_n_u8, "", "n")
BUILTIN(__builtin_arm_mve_vqaddq_m_s16, "", "n")
BUILTIN(__builtin_arm_mve_vqaddq_m_s32, "", "n")
BUILTIN(__builtin_arm_mve_vqaddq_m_s8, "", "n")
BUILTIN(__builtin_arm_mve_vqaddq_m_u16, "", "n")
BUILTIN(__builtin_arm_mve_vqaddq_m_u32, "", "n")
BUILTIN(__builtin_arm_mve_vqaddq_m_u8, "", "n")
BUILTIN(__builtin_arm_mve_vqaddq_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vqaddq_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vqaddq_n_s8, "", "n")
BUILTIN(__builtin_arm_mve_vqaddq_n_u16, "", "n")
BUILTIN(__builtin_arm_mve_vqaddq_n_u32, "", "n")
BUILTIN(__builtin_arm_mve_vqaddq_n_u8, "", "n")
BUILTIN(__builtin_arm_mve_vqaddq_s16, "", "n")
BUILTIN(__builtin_arm_mve_vqaddq_s32, "", "n")
BUILTIN(__builtin_arm_mve_vqaddq_s8, "", "n")
BUILTIN(__builtin_arm_mve_vqaddq_u16, "", "n")
BUILTIN(__builtin_arm_mve_vqaddq_u32, "", "n")
BUILTIN(__builtin_arm_mve_vqaddq_u8, "", "n")
BUILTIN(__builtin_arm_mve_vqdmladhq_m_s16, "", "n")
BUILTIN(__builtin_arm_mve_vqdmladhq_m_s32, "", "n")
BUILTIN(__builtin_arm_mve_vqdmladhq_m_s8, "", "n")
BUILTIN(__builtin_arm_mve_vqdmladhq_s16, "", "n")
BUILTIN(__builtin_arm_mve_vqdmladhq_s32, "", "n")
BUILTIN(__builtin_arm_mve_vqdmladhq_s8, "", "n")
BUILTIN(__builtin_arm_mve_vqdmladhxq_m_s16, "", "n")
BUILTIN(__builtin_arm_mve_vqdmladhxq_m_s32, "", "n")
BUILTIN(__builtin_arm_mve_vqdmladhxq_m_s8, "", "n")
BUILTIN(__builtin_arm_mve_vqdmladhxq_s16, "", "n")
BUILTIN(__builtin_arm_mve_vqdmladhxq_s32, "", "n")
BUILTIN(__builtin_arm_mve_vqdmladhxq_s8, "", "n")
BUILTIN(__builtin_arm_mve_vqdmlahq_m_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vqdmlahq_m_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vqdmlahq_m_n_s8, "", "n")
BUILTIN(__builtin_arm_mve_vqdmlahq_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vqdmlahq_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vqdmlahq_n_s8, "", "n")
BUILTIN(__builtin_arm_mve_vqdmlashq_m_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vqdmlashq_m_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vqdmlashq_m_n_s8, "", "n")
BUILTIN(__builtin_arm_mve_vqdmlashq_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vqdmlashq_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vqdmlashq_n_s8, "", "n")
BUILTIN(__builtin_arm_mve_vqdmlsdhq_m_s16, "", "n")
BUILTIN(__builtin_arm_mve_vqdmlsdhq_m_s32, "", "n")
BUILTIN(__builtin_arm_mve_vqdmlsdhq_m_s8, "", "n")
BUILTIN(__builtin_arm_mve_vqdmlsdhq_s16, "", "n")
BUILTIN(__builtin_arm_mve_vqdmlsdhq_s32, "", "n")
BUILTIN(__builtin_arm_mve_vqdmlsdhq_s8, "", "n")
BUILTIN(__builtin_arm_mve_vqdmlsdhxq_m_s16, "", "n")
BUILTIN(__builtin_arm_mve_vqdmlsdhxq_m_s32, "", "n")
BUILTIN(__builtin_arm_mve_vqdmlsdhxq_m_s8, "", "n")
BUILTIN(__builtin_arm_mve_vqdmlsdhxq_s16, "", "n")
BUILTIN(__builtin_arm_mve_vqdmlsdhxq_s32, "", "n")
BUILTIN(__builtin_arm_mve_vqdmlsdhxq_s8, "", "n")
BUILTIN(__builtin_arm_mve_vqdmulhq_m_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vqdmulhq_m_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vqdmulhq_m_n_s8, "", "n")
BUILTIN(__builtin_arm_mve_vqdmulhq_m_s16, "", "n")
BUILTIN(__builtin_arm_mve_vqdmulhq_m_s32, "", "n")
BUILTIN(__builtin_arm_mve_vqdmulhq_m_s8, "", "n")
BUILTIN(__builtin_arm_mve_vqdmulhq_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vqdmulhq_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vqdmulhq_n_s8, "", "n")
BUILTIN(__builtin_arm_mve_vqdmulhq_s16, "", "n")
BUILTIN(__builtin_arm_mve_vqdmulhq_s32, "", "n")
BUILTIN(__builtin_arm_mve_vqdmulhq_s8, "", "n")
BUILTIN(__builtin_arm_mve_vqdmullbq_m_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vqdmullbq_m_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vqdmullbq_m_s16, "", "n")
BUILTIN(__builtin_arm_mve_vqdmullbq_m_s32, "", "n")
BUILTIN(__builtin_arm_mve_vqdmullbq_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vqdmullbq_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vqdmullbq_s16, "", "n")
BUILTIN(__builtin_arm_mve_vqdmullbq_s32, "", "n")
BUILTIN(__builtin_arm_mve_vqdmulltq_m_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vqdmulltq_m_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vqdmulltq_m_s16, "", "n")
BUILTIN(__builtin_arm_mve_vqdmulltq_m_s32, "", "n")
BUILTIN(__builtin_arm_mve_vqdmulltq_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vqdmulltq_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vqdmulltq_s16, "", "n")
BUILTIN(__builtin_arm_mve_vqdmulltq_s32, "", "n")
BUILTIN(__builtin_arm_mve_vqmovnbq_m_s16, "", "n")
BUILTIN(__builtin_arm_mve_vqmovnbq_m_s32, "", "n")
BUILTIN(__builtin_arm_mve_vqmovnbq_m_u16, "", "n")
BUILTIN(__builtin_arm_mve_vqmovnbq_m_u32, "", "n")
BUILTIN(__builtin_arm_mve_vqmovnbq_s16, "", "n")
BUILTIN(__builtin_arm_mve_vqmovnbq_s32, "", "n")
BUILTIN(__builtin_arm_mve_vqmovnbq_u16, "", "n")
BUILTIN(__builtin_arm_mve_vqmovnbq_u32, "", "n")
BUILTIN(__builtin_arm_mve_vqmovntq_m_s16, "", "n")
BUILTIN(__builtin_arm_mve_vqmovntq_m_s32, "", "n")
BUILTIN(__builtin_arm_mve_vqmovntq_m_u16, "", "n")
BUILTIN(__builtin_arm_mve_vqmovntq_m_u32, "", "n")
BUILTIN(__builtin_arm_mve_vqmovntq_s16, "", "n")
BUILTIN(__builtin_arm_mve_vqmovntq_s32, "", "n")
BUILTIN(__builtin_arm_mve_vqmovntq_u16, "", "n")
BUILTIN(__builtin_arm_mve_vqmovntq_u32, "", "n")
BUILTIN(__builtin_arm_mve_vqmovunbq_m_s16, "", "n")
BUILTIN(__builtin_arm_mve_vqmovunbq_m_s32, "", "n")
BUILTIN(__builtin_arm_mve_vqmovunbq_s16, "", "n")
BUILTIN(__builtin_arm_mve_vqmovunbq_s32, "", "n")
BUILTIN(__builtin_arm_mve_vqmovuntq_m_s16, "", "n")
BUILTIN(__builtin_arm_mve_vqmovuntq_m_s32, "", "n")
BUILTIN(__builtin_arm_mve_vqmovuntq_s16, "", "n")
BUILTIN(__builtin_arm_mve_vqmovuntq_s32, "", "n")
BUILTIN(__builtin_arm_mve_vqnegq_m_s16, "", "n")
BUILTIN(__builtin_arm_mve_vqnegq_m_s32, "", "n")
BUILTIN(__builtin_arm_mve_vqnegq_m_s8, "", "n")
BUILTIN(__builtin_arm_mve_vqnegq_s16, "", "n")
BUILTIN(__builtin_arm_mve_vqnegq_s32, "", "n")
BUILTIN(__builtin_arm_mve_vqnegq_s8, "", "n")
BUILTIN(__builtin_arm_mve_vqrdmladhq_m_s16, "", "n")
BUILTIN(__builtin_arm_mve_vqrdmladhq_m_s32, "", "n")
BUILTIN(__builtin_arm_mve_vqrdmladhq_m_s8, "", "n")
BUILTIN(__builtin_arm_mve_vqrdmladhq_s16, "", "n")
BUILTIN(__builtin_arm_mve_vqrdmladhq_s32, "", "n")
BUILTIN(__builtin_arm_mve_vqrdmladhq_s8, "", "n")
BUILTIN(__builtin_arm_mve_vqrdmladhxq_m_s16, "", "n")
BUILTIN(__builtin_arm_mve_vqrdmladhxq_m_s32, "", "n")
BUILTIN(__builtin_arm_mve_vqrdmladhxq_m_s8, "", "n")
BUILTIN(__builtin_arm_mve_vqrdmladhxq_s16, "", "n")
BUILTIN(__builtin_arm_mve_vqrdmladhxq_s32, "", "n")
BUILTIN(__builtin_arm_mve_vqrdmladhxq_s8, "", "n")
BUILTIN(__builtin_arm_mve_vqrdmlahq_m_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vqrdmlahq_m_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vqrdmlahq_m_n_s8, "", "n")
BUILTIN(__builtin_arm_mve_vqrdmlahq_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vqrdmlahq_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vqrdmlahq_n_s8, "", "n")
BUILTIN(__builtin_arm_mve_vqrdmlashq_m_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vqrdmlashq_m_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vqrdmlashq_m_n_s8, "", "n")
BUILTIN(__builtin_arm_mve_vqrdmlashq_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vqrdmlashq_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vqrdmlashq_n_s8, "", "n")
BUILTIN(__builtin_arm_mve_vqrdmlsdhq_m_s16, "", "n")
BUILTIN(__builtin_arm_mve_vqrdmlsdhq_m_s32, "", "n")
BUILTIN(__builtin_arm_mve_vqrdmlsdhq_m_s8, "", "n")
BUILTIN(__builtin_arm_mve_vqrdmlsdhq_s16, "", "n")
BUILTIN(__builtin_arm_mve_vqrdmlsdhq_s32, "", "n")
BUILTIN(__builtin_arm_mve_vqrdmlsdhq_s8, "", "n")
BUILTIN(__builtin_arm_mve_vqrdmlsdhxq_m_s16, "", "n")
BUILTIN(__builtin_arm_mve_vqrdmlsdhxq_m_s32, "", "n")
BUILTIN(__builtin_arm_mve_vqrdmlsdhxq_m_s8, "", "n")
BUILTIN(__builtin_arm_mve_vqrdmlsdhxq_s16, "", "n")
BUILTIN(__builtin_arm_mve_vqrdmlsdhxq_s32, "", "n")
BUILTIN(__builtin_arm_mve_vqrdmlsdhxq_s8, "", "n")
BUILTIN(__builtin_arm_mve_vqrdmulhq_m_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vqrdmulhq_m_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vqrdmulhq_m_n_s8, "", "n")
BUILTIN(__builtin_arm_mve_vqrdmulhq_m_s16, "", "n")
BUILTIN(__builtin_arm_mve_vqrdmulhq_m_s32, "", "n")
BUILTIN(__builtin_arm_mve_vqrdmulhq_m_s8, "", "n")
BUILTIN(__builtin_arm_mve_vqrdmulhq_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vqrdmulhq_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vqrdmulhq_n_s8, "", "n")
BUILTIN(__builtin_arm_mve_vqrdmulhq_s16, "", "n")
BUILTIN(__builtin_arm_mve_vqrdmulhq_s32, "", "n")
BUILTIN(__builtin_arm_mve_vqrdmulhq_s8, "", "n")
BUILTIN(__builtin_arm_mve_vqrshlq_m_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vqrshlq_m_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vqrshlq_m_n_s8, "", "n")
BUILTIN(__builtin_arm_mve_vqrshlq_m_n_u16, "", "n")
BUILTIN(__builtin_arm_mve_vqrshlq_m_n_u32, "", "n")
BUILTIN(__builtin_arm_mve_vqrshlq_m_n_u8, "", "n")
BUILTIN(__builtin_arm_mve_vqrshlq_m_s16, "", "n")
BUILTIN(__builtin_arm_mve_vqrshlq_m_s32, "", "n")
BUILTIN(__builtin_arm_mve_vqrshlq_m_s8, "", "n")
BUILTIN(__builtin_arm_mve_vqrshlq_m_u16, "", "n")
BUILTIN(__builtin_arm_mve_vqrshlq_m_u32, "", "n")
BUILTIN(__builtin_arm_mve_vqrshlq_m_u8, "", "n")
BUILTIN(__builtin_arm_mve_vqrshlq_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vqrshlq_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vqrshlq_n_s8, "", "n")
BUILTIN(__builtin_arm_mve_vqrshlq_n_u16, "", "n")
BUILTIN(__builtin_arm_mve_vqrshlq_n_u32, "", "n")
BUILTIN(__builtin_arm_mve_vqrshlq_n_u8, "", "n")
BUILTIN(__builtin_arm_mve_vqrshlq_s16, "", "n")
BUILTIN(__builtin_arm_mve_vqrshlq_s32, "", "n")
BUILTIN(__builtin_arm_mve_vqrshlq_s8, "", "n")
BUILTIN(__builtin_arm_mve_vqrshlq_u16, "", "n")
BUILTIN(__builtin_arm_mve_vqrshlq_u32, "", "n")
BUILTIN(__builtin_arm_mve_vqrshlq_u8, "", "n")
BUILTIN(__builtin_arm_mve_vqrshrnbq_m_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vqrshrnbq_m_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vqrshrnbq_m_n_u16, "", "n")
BUILTIN(__builtin_arm_mve_vqrshrnbq_m_n_u32, "", "n")
BUILTIN(__builtin_arm_mve_vqrshrnbq_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vqrshrnbq_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vqrshrnbq_n_u16, "", "n")
BUILTIN(__builtin_arm_mve_vqrshrnbq_n_u32, "", "n")
BUILTIN(__builtin_arm_mve_vqrshrntq_m_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vqrshrntq_m_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vqrshrntq_m_n_u16, "", "n")
BUILTIN(__builtin_arm_mve_vqrshrntq_m_n_u32, "", "n")
BUILTIN(__builtin_arm_mve_vqrshrntq_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vqrshrntq_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vqrshrntq_n_u16, "", "n")
BUILTIN(__builtin_arm_mve_vqrshrntq_n_u32, "", "n")
BUILTIN(__builtin_arm_mve_vqrshrunbq_m_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vqrshrunbq_m_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vqrshrunbq_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vqrshrunbq_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vqrshruntq_m_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vqrshruntq_m_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vqrshruntq_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vqrshruntq_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vqshlq_m_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vqshlq_m_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vqshlq_m_n_s8, "", "n")
BUILTIN(__builtin_arm_mve_vqshlq_m_n_u16, "", "n")
BUILTIN(__builtin_arm_mve_vqshlq_m_n_u32, "", "n")
BUILTIN(__builtin_arm_mve_vqshlq_m_n_u8, "", "n")
BUILTIN(__builtin_arm_mve_vqshlq_m_r_s16, "", "n")
BUILTIN(__builtin_arm_mve_vqshlq_m_r_s32, "", "n")
BUILTIN(__builtin_arm_mve_vqshlq_m_r_s8, "", "n")
BUILTIN(__builtin_arm_mve_vqshlq_m_r_u16, "", "n")
BUILTIN(__builtin_arm_mve_vqshlq_m_r_u32, "", "n")
BUILTIN(__builtin_arm_mve_vqshlq_m_r_u8, "", "n")
BUILTIN(__builtin_arm_mve_vqshlq_m_s16, "", "n")
BUILTIN(__builtin_arm_mve_vqshlq_m_s32, "", "n")
BUILTIN(__builtin_arm_mve_vqshlq_m_s8, "", "n")
BUILTIN(__builtin_arm_mve_vqshlq_m_u16, "", "n")
BUILTIN(__builtin_arm_mve_vqshlq_m_u32, "", "n")
BUILTIN(__builtin_arm_mve_vqshlq_m_u8, "", "n")
BUILTIN(__builtin_arm_mve_vqshlq_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vqshlq_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vqshlq_n_s8, "", "n")
BUILTIN(__builtin_arm_mve_vqshlq_n_u16, "", "n")
BUILTIN(__builtin_arm_mve_vqshlq_n_u32, "", "n")
BUILTIN(__builtin_arm_mve_vqshlq_n_u8, "", "n")
BUILTIN(__builtin_arm_mve_vqshlq_r_s16, "", "n")
BUILTIN(__builtin_arm_mve_vqshlq_r_s32, "", "n")
BUILTIN(__builtin_arm_mve_vqshlq_r_s8, "", "n")
BUILTIN(__builtin_arm_mve_vqshlq_r_u16, "", "n")
BUILTIN(__builtin_arm_mve_vqshlq_r_u32, "", "n")
BUILTIN(__builtin_arm_mve_vqshlq_r_u8, "", "n")
BUILTIN(__builtin_arm_mve_vqshlq_s16, "", "n")
BUILTIN(__builtin_arm_mve_vqshlq_s32, "", "n")
BUILTIN(__builtin_arm_mve_vqshlq_s8, "", "n")
BUILTIN(__builtin_arm_mve_vqshlq_u16, "", "n")
BUILTIN(__builtin_arm_mve_vqshlq_u32, "", "n")
BUILTIN(__builtin_arm_mve_vqshlq_u8, "", "n")
BUILTIN(__builtin_arm_mve_vqshluq_m_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vqshluq_m_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vqshluq_m_n_s8, "", "n")
BUILTIN(__builtin_arm_mve_vqshluq_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vqshluq_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vqshluq_n_s8, "", "n")
BUILTIN(__builtin_arm_mve_vqshrnbq_m_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vqshrnbq_m_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vqshrnbq_m_n_u16, "", "n")
BUILTIN(__builtin_arm_mve_vqshrnbq_m_n_u32, "", "n")
BUILTIN(__builtin_arm_mve_vqshrnbq_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vqshrnbq_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vqshrnbq_n_u16, "", "n")
BUILTIN(__builtin_arm_mve_vqshrnbq_n_u32, "", "n")
BUILTIN(__builtin_arm_mve_vqshrntq_m_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vqshrntq_m_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vqshrntq_m_n_u16, "", "n")
BUILTIN(__builtin_arm_mve_vqshrntq_m_n_u32, "", "n")
BUILTIN(__builtin_arm_mve_vqshrntq_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vqshrntq_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vqshrntq_n_u16, "", "n")
BUILTIN(__builtin_arm_mve_vqshrntq_n_u32, "", "n")
BUILTIN(__builtin_arm_mve_vqshrunbq_m_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vqshrunbq_m_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vqshrunbq_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vqshrunbq_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vqshruntq_m_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vqshruntq_m_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vqshruntq_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vqshruntq_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vqsubq_m_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vqsubq_m_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vqsubq_m_n_s8, "", "n")
BUILTIN(__builtin_arm_mve_vqsubq_m_n_u16, "", "n")
BUILTIN(__builtin_arm_mve_vqsubq_m_n_u32, "", "n")
BUILTIN(__builtin_arm_mve_vqsubq_m_n_u8, "", "n")
BUILTIN(__builtin_arm_mve_vqsubq_m_s16, "", "n")
BUILTIN(__builtin_arm_mve_vqsubq_m_s32, "", "n")
BUILTIN(__builtin_arm_mve_vqsubq_m_s8, "", "n")
BUILTIN(__builtin_arm_mve_vqsubq_m_u16, "", "n")
BUILTIN(__builtin_arm_mve_vqsubq_m_u32, "", "n")
BUILTIN(__builtin_arm_mve_vqsubq_m_u8, "", "n")
BUILTIN(__builtin_arm_mve_vqsubq_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vqsubq_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vqsubq_n_s8, "", "n")
BUILTIN(__builtin_arm_mve_vqsubq_n_u16, "", "n")
BUILTIN(__builtin_arm_mve_vqsubq_n_u32, "", "n")
BUILTIN(__builtin_arm_mve_vqsubq_n_u8, "", "n")
BUILTIN(__builtin_arm_mve_vqsubq_s16, "", "n")
BUILTIN(__builtin_arm_mve_vqsubq_s32, "", "n")
BUILTIN(__builtin_arm_mve_vqsubq_s8, "", "n")
BUILTIN(__builtin_arm_mve_vqsubq_u16, "", "n")
BUILTIN(__builtin_arm_mve_vqsubq_u32, "", "n")
BUILTIN(__builtin_arm_mve_vqsubq_u8, "", "n")
BUILTIN(__builtin_arm_mve_vreinterpretq_f16_f32, "", "n")
BUILTIN(__builtin_arm_mve_vreinterpretq_f16_s16, "", "n")
BUILTIN(__builtin_arm_mve_vreinterpretq_f16_s32, "", "n")
BUILTIN(__builtin_arm_mve_vreinterpretq_f16_s64, "", "n")
BUILTIN(__builtin_arm_mve_vreinterpretq_f16_s8, "", "n")
BUILTIN(__builtin_arm_mve_vreinterpretq_f16_u16, "", "n")
BUILTIN(__builtin_arm_mve_vreinterpretq_f16_u32, "", "n")
BUILTIN(__builtin_arm_mve_vreinterpretq_f16_u64, "", "n")
BUILTIN(__builtin_arm_mve_vreinterpretq_f16_u8, "", "n")
BUILTIN(__builtin_arm_mve_vreinterpretq_f32_f16, "", "n")
BUILTIN(__builtin_arm_mve_vreinterpretq_f32_s16, "", "n")
BUILTIN(__builtin_arm_mve_vreinterpretq_f32_s32, "", "n")
BUILTIN(__builtin_arm_mve_vreinterpretq_f32_s64, "", "n")
BUILTIN(__builtin_arm_mve_vreinterpretq_f32_s8, "", "n")
BUILTIN(__builtin_arm_mve_vreinterpretq_f32_u16, "", "n")
BUILTIN(__builtin_arm_mve_vreinterpretq_f32_u32, "", "n")
BUILTIN(__builtin_arm_mve_vreinterpretq_f32_u64, "", "n")
BUILTIN(__builtin_arm_mve_vreinterpretq_f32_u8, "", "n")
BUILTIN(__builtin_arm_mve_vreinterpretq_s16_f16, "", "n")
BUILTIN(__builtin_arm_mve_vreinterpretq_s16_f32, "", "n")
BUILTIN(__builtin_arm_mve_vreinterpretq_s16_s32, "", "n")
BUILTIN(__builtin_arm_mve_vreinterpretq_s16_s64, "", "n")
BUILTIN(__builtin_arm_mve_vreinterpretq_s16_s8, "", "n")
BUILTIN(__builtin_arm_mve_vreinterpretq_s16_u16, "", "n")
BUILTIN(__builtin_arm_mve_vreinterpretq_s16_u32, "", "n")
BUILTIN(__builtin_arm_mve_vreinterpretq_s16_u64, "", "n")
BUILTIN(__builtin_arm_mve_vreinterpretq_s16_u8, "", "n")
BUILTIN(__builtin_arm_mve_vreinterpretq_s32_f16, "", "n")
BUILTIN(__builtin_arm_mve_vreinterpretq_s32_f32, "", "n")
BUILTIN(__builtin_arm_mve_vreinterpretq_s32_s16, "", "n")
BUILTIN(__builtin_arm_mve_vreinterpretq_s32_s64, "", "n")
BUILTIN(__builtin_arm_mve_vreinterpretq_s32_s8, "", "n")
BUILTIN(__builtin_arm_mve_vreinterpretq_s32_u16, "", "n")
BUILTIN(__builtin_arm_mve_vreinterpretq_s32_u32, "", "n")
BUILTIN(__builtin_arm_mve_vreinterpretq_s32_u64, "", "n")
BUILTIN(__builtin_arm_mve_vreinterpretq_s32_u8, "", "n")
BUILTIN(__builtin_arm_mve_vreinterpretq_s64_f16, "", "n")
BUILTIN(__builtin_arm_mve_vreinterpretq_s64_f32, "", "n")
BUILTIN(__builtin_arm_mve_vreinterpretq_s64_s16, "", "n")
BUILTIN(__builtin_arm_mve_vreinterpretq_s64_s32, "", "n")
BUILTIN(__builtin_arm_mve_vreinterpretq_s64_s8, "", "n")
BUILTIN(__builtin_arm_mve_vreinterpretq_s64_u16, "", "n")
BUILTIN(__builtin_arm_mve_vreinterpretq_s64_u32, "", "n")
BUILTIN(__builtin_arm_mve_vreinterpretq_s64_u64, "", "n")
BUILTIN(__builtin_arm_mve_vreinterpretq_s64_u8, "", "n")
BUILTIN(__builtin_arm_mve_vreinterpretq_s8_f16, "", "n")
BUILTIN(__builtin_arm_mve_vreinterpretq_s8_f32, "", "n")
BUILTIN(__builtin_arm_mve_vreinterpretq_s8_s16, "", "n")
BUILTIN(__builtin_arm_mve_vreinterpretq_s8_s32, "", "n")
BUILTIN(__builtin_arm_mve_vreinterpretq_s8_s64, "", "n")
BUILTIN(__builtin_arm_mve_vreinterpretq_s8_u16, "", "n")
BUILTIN(__builtin_arm_mve_vreinterpretq_s8_u32, "", "n")
BUILTIN(__builtin_arm_mve_vreinterpretq_s8_u64, "", "n")
BUILTIN(__builtin_arm_mve_vreinterpretq_s8_u8, "", "n")
BUILTIN(__builtin_arm_mve_vreinterpretq_u16_f16, "", "n")
BUILTIN(__builtin_arm_mve_vreinterpretq_u16_f32, "", "n")
BUILTIN(__builtin_arm_mve_vreinterpretq_u16_s16, "", "n")
BUILTIN(__builtin_arm_mve_vreinterpretq_u16_s32, "", "n")
BUILTIN(__builtin_arm_mve_vreinterpretq_u16_s64, "", "n")
BUILTIN(__builtin_arm_mve_vreinterpretq_u16_s8, "", "n")
BUILTIN(__builtin_arm_mve_vreinterpretq_u16_u32, "", "n")
BUILTIN(__builtin_arm_mve_vreinterpretq_u16_u64, "", "n")
BUILTIN(__builtin_arm_mve_vreinterpretq_u16_u8, "", "n")
BUILTIN(__builtin_arm_mve_vreinterpretq_u32_f16, "", "n")
BUILTIN(__builtin_arm_mve_vreinterpretq_u32_f32, "", "n")
BUILTIN(__builtin_arm_mve_vreinterpretq_u32_s16, "", "n")
BUILTIN(__builtin_arm_mve_vreinterpretq_u32_s32, "", "n")
BUILTIN(__builtin_arm_mve_vreinterpretq_u32_s64, "", "n")
BUILTIN(__builtin_arm_mve_vreinterpretq_u32_s8, "", "n")
BUILTIN(__builtin_arm_mve_vreinterpretq_u32_u16, "", "n")
BUILTIN(__builtin_arm_mve_vreinterpretq_u32_u64, "", "n")
BUILTIN(__builtin_arm_mve_vreinterpretq_u32_u8, "", "n")
BUILTIN(__builtin_arm_mve_vreinterpretq_u64_f16, "", "n")
BUILTIN(__builtin_arm_mve_vreinterpretq_u64_f32, "", "n")
BUILTIN(__builtin_arm_mve_vreinterpretq_u64_s16, "", "n")
BUILTIN(__builtin_arm_mve_vreinterpretq_u64_s32, "", "n")
BUILTIN(__builtin_arm_mve_vreinterpretq_u64_s64, "", "n")
BUILTIN(__builtin_arm_mve_vreinterpretq_u64_s8, "", "n")
BUILTIN(__builtin_arm_mve_vreinterpretq_u64_u16, "", "n")
BUILTIN(__builtin_arm_mve_vreinterpretq_u64_u32, "", "n")
BUILTIN(__builtin_arm_mve_vreinterpretq_u64_u8, "", "n")
BUILTIN(__builtin_arm_mve_vreinterpretq_u8_f16, "", "n")
BUILTIN(__builtin_arm_mve_vreinterpretq_u8_f32, "", "n")
BUILTIN(__builtin_arm_mve_vreinterpretq_u8_s16, "", "n")
BUILTIN(__builtin_arm_mve_vreinterpretq_u8_s32, "", "n")
BUILTIN(__builtin_arm_mve_vreinterpretq_u8_s64, "", "n")
BUILTIN(__builtin_arm_mve_vreinterpretq_u8_s8, "", "n")
BUILTIN(__builtin_arm_mve_vreinterpretq_u8_u16, "", "n")
BUILTIN(__builtin_arm_mve_vreinterpretq_u8_u32, "", "n")
BUILTIN(__builtin_arm_mve_vreinterpretq_u8_u64, "", "n")
BUILTIN(__builtin_arm_mve_vrev16q_m_s8, "", "n")
BUILTIN(__builtin_arm_mve_vrev16q_m_u8, "", "n")
BUILTIN(__builtin_arm_mve_vrev16q_s8, "", "n")
BUILTIN(__builtin_arm_mve_vrev16q_u8, "", "n")
BUILTIN(__builtin_arm_mve_vrev16q_x_s8, "", "n")
BUILTIN(__builtin_arm_mve_vrev16q_x_u8, "", "n")
BUILTIN(__builtin_arm_mve_vrev32q_f16, "", "n")
BUILTIN(__builtin_arm_mve_vrev32q_m_f16, "", "n")
BUILTIN(__builtin_arm_mve_vrev32q_m_s16, "", "n")
BUILTIN(__builtin_arm_mve_vrev32q_m_s8, "", "n")
BUILTIN(__builtin_arm_mve_vrev32q_m_u16, "", "n")
BUILTIN(__builtin_arm_mve_vrev32q_m_u8, "", "n")
BUILTIN(__builtin_arm_mve_vrev32q_s16, "", "n")
BUILTIN(__builtin_arm_mve_vrev32q_s8, "", "n")
BUILTIN(__builtin_arm_mve_vrev32q_u16, "", "n")
BUILTIN(__builtin_arm_mve_vrev32q_u8, "", "n")
BUILTIN(__builtin_arm_mve_vrev32q_x_f16, "", "n")
BUILTIN(__builtin_arm_mve_vrev32q_x_s16, "", "n")
BUILTIN(__builtin_arm_mve_vrev32q_x_s8, "", "n")
BUILTIN(__builtin_arm_mve_vrev32q_x_u16, "", "n")
BUILTIN(__builtin_arm_mve_vrev32q_x_u8, "", "n")
BUILTIN(__builtin_arm_mve_vrev64q_f16, "", "n")
BUILTIN(__builtin_arm_mve_vrev64q_f32, "", "n")
BUILTIN(__builtin_arm_mve_vrev64q_m_f16, "", "n")
BUILTIN(__builtin_arm_mve_vrev64q_m_f32, "", "n")
BUILTIN(__builtin_arm_mve_vrev64q_m_s16, "", "n")
BUILTIN(__builtin_arm_mve_vrev64q_m_s32, "", "n")
BUILTIN(__builtin_arm_mve_vrev64q_m_s8, "", "n")
BUILTIN(__builtin_arm_mve_vrev64q_m_u16, "", "n")
BUILTIN(__builtin_arm_mve_vrev64q_m_u32, "", "n")
BUILTIN(__builtin_arm_mve_vrev64q_m_u8, "", "n")
BUILTIN(__builtin_arm_mve_vrev64q_s16, "", "n")
BUILTIN(__builtin_arm_mve_vrev64q_s32, "", "n")
BUILTIN(__builtin_arm_mve_vrev64q_s8, "", "n")
BUILTIN(__builtin_arm_mve_vrev64q_u16, "", "n")
BUILTIN(__builtin_arm_mve_vrev64q_u32, "", "n")
BUILTIN(__builtin_arm_mve_vrev64q_u8, "", "n")
BUILTIN(__builtin_arm_mve_vrev64q_x_f16, "", "n")
BUILTIN(__builtin_arm_mve_vrev64q_x_f32, "", "n")
BUILTIN(__builtin_arm_mve_vrev64q_x_s16, "", "n")
BUILTIN(__builtin_arm_mve_vrev64q_x_s32, "", "n")
BUILTIN(__builtin_arm_mve_vrev64q_x_s8, "", "n")
BUILTIN(__builtin_arm_mve_vrev64q_x_u16, "", "n")
BUILTIN(__builtin_arm_mve_vrev64q_x_u32, "", "n")
BUILTIN(__builtin_arm_mve_vrev64q_x_u8, "", "n")
BUILTIN(__builtin_arm_mve_vrhaddq_m_s16, "", "n")
BUILTIN(__builtin_arm_mve_vrhaddq_m_s32, "", "n")
BUILTIN(__builtin_arm_mve_vrhaddq_m_s8, "", "n")
BUILTIN(__builtin_arm_mve_vrhaddq_m_u16, "", "n")
BUILTIN(__builtin_arm_mve_vrhaddq_m_u32, "", "n")
BUILTIN(__builtin_arm_mve_vrhaddq_m_u8, "", "n")
BUILTIN(__builtin_arm_mve_vrhaddq_s16, "", "n")
BUILTIN(__builtin_arm_mve_vrhaddq_s32, "", "n")
BUILTIN(__builtin_arm_mve_vrhaddq_s8, "", "n")
BUILTIN(__builtin_arm_mve_vrhaddq_u16, "", "n")
BUILTIN(__builtin_arm_mve_vrhaddq_u32, "", "n")
BUILTIN(__builtin_arm_mve_vrhaddq_u8, "", "n")
BUILTIN(__builtin_arm_mve_vrhaddq_x_s16, "", "n")
BUILTIN(__builtin_arm_mve_vrhaddq_x_s32, "", "n")
BUILTIN(__builtin_arm_mve_vrhaddq_x_s8, "", "n")
BUILTIN(__builtin_arm_mve_vrhaddq_x_u16, "", "n")
BUILTIN(__builtin_arm_mve_vrhaddq_x_u32, "", "n")
BUILTIN(__builtin_arm_mve_vrhaddq_x_u8, "", "n")
BUILTIN(__builtin_arm_mve_vrmlaldavhaq_p_s32, "", "n")
BUILTIN(__builtin_arm_mve_vrmlaldavhaq_p_u32, "", "n")
BUILTIN(__builtin_arm_mve_vrmlaldavhaq_s32, "", "n")
BUILTIN(__builtin_arm_mve_vrmlaldavhaq_u32, "", "n")
BUILTIN(__builtin_arm_mve_vrmlaldavhaxq_p_s32, "", "n")
BUILTIN(__builtin_arm_mve_vrmlaldavhaxq_s32, "", "n")
BUILTIN(__builtin_arm_mve_vrmlaldavhq_p_s32, "", "n")
BUILTIN(__builtin_arm_mve_vrmlaldavhq_p_u32, "", "n")
BUILTIN(__builtin_arm_mve_vrmlaldavhq_s32, "", "n")
BUILTIN(__builtin_arm_mve_vrmlaldavhq_u32, "", "n")
BUILTIN(__builtin_arm_mve_vrmlaldavhxq_p_s32, "", "n")
BUILTIN(__builtin_arm_mve_vrmlaldavhxq_s32, "", "n")
BUILTIN(__builtin_arm_mve_vrmlsldavhaq_p_s32, "", "n")
BUILTIN(__builtin_arm_mve_vrmlsldavhaq_s32, "", "n")
BUILTIN(__builtin_arm_mve_vrmlsldavhaxq_p_s32, "", "n")
BUILTIN(__builtin_arm_mve_vrmlsldavhaxq_s32, "", "n")
BUILTIN(__builtin_arm_mve_vrmlsldavhq_p_s32, "", "n")
BUILTIN(__builtin_arm_mve_vrmlsldavhq_s32, "", "n")
BUILTIN(__builtin_arm_mve_vrmlsldavhxq_p_s32, "", "n")
BUILTIN(__builtin_arm_mve_vrmlsldavhxq_s32, "", "n")
BUILTIN(__builtin_arm_mve_vrmulhq_m_s16, "", "n")
BUILTIN(__builtin_arm_mve_vrmulhq_m_s32, "", "n")
BUILTIN(__builtin_arm_mve_vrmulhq_m_s8, "", "n")
BUILTIN(__builtin_arm_mve_vrmulhq_m_u16, "", "n")
BUILTIN(__builtin_arm_mve_vrmulhq_m_u32, "", "n")
BUILTIN(__builtin_arm_mve_vrmulhq_m_u8, "", "n")
BUILTIN(__builtin_arm_mve_vrmulhq_s16, "", "n")
BUILTIN(__builtin_arm_mve_vrmulhq_s32, "", "n")
BUILTIN(__builtin_arm_mve_vrmulhq_s8, "", "n")
BUILTIN(__builtin_arm_mve_vrmulhq_u16, "", "n")
BUILTIN(__builtin_arm_mve_vrmulhq_u32, "", "n")
BUILTIN(__builtin_arm_mve_vrmulhq_u8, "", "n")
BUILTIN(__builtin_arm_mve_vrmulhq_x_s16, "", "n")
BUILTIN(__builtin_arm_mve_vrmulhq_x_s32, "", "n")
BUILTIN(__builtin_arm_mve_vrmulhq_x_s8, "", "n")
BUILTIN(__builtin_arm_mve_vrmulhq_x_u16, "", "n")
BUILTIN(__builtin_arm_mve_vrmulhq_x_u32, "", "n")
BUILTIN(__builtin_arm_mve_vrmulhq_x_u8, "", "n")
BUILTIN(__builtin_arm_mve_vrndaq_f16, "", "n")
BUILTIN(__builtin_arm_mve_vrndaq_f32, "", "n")
BUILTIN(__builtin_arm_mve_vrndaq_m_f16, "", "n")
BUILTIN(__builtin_arm_mve_vrndaq_m_f32, "", "n")
BUILTIN(__builtin_arm_mve_vrndaq_x_f16, "", "n")
BUILTIN(__builtin_arm_mve_vrndaq_x_f32, "", "n")
BUILTIN(__builtin_arm_mve_vrndmq_f16, "", "n")
BUILTIN(__builtin_arm_mve_vrndmq_f32, "", "n")
BUILTIN(__builtin_arm_mve_vrndmq_m_f16, "", "n")
BUILTIN(__builtin_arm_mve_vrndmq_m_f32, "", "n")
BUILTIN(__builtin_arm_mve_vrndmq_x_f16, "", "n")
BUILTIN(__builtin_arm_mve_vrndmq_x_f32, "", "n")
BUILTIN(__builtin_arm_mve_vrndnq_f16, "", "n")
BUILTIN(__builtin_arm_mve_vrndnq_f32, "", "n")
BUILTIN(__builtin_arm_mve_vrndnq_m_f16, "", "n")
BUILTIN(__builtin_arm_mve_vrndnq_m_f32, "", "n")
BUILTIN(__builtin_arm_mve_vrndnq_x_f16, "", "n")
BUILTIN(__builtin_arm_mve_vrndnq_x_f32, "", "n")
BUILTIN(__builtin_arm_mve_vrndpq_f16, "", "n")
BUILTIN(__builtin_arm_mve_vrndpq_f32, "", "n")
BUILTIN(__builtin_arm_mve_vrndpq_m_f16, "", "n")
BUILTIN(__builtin_arm_mve_vrndpq_m_f32, "", "n")
BUILTIN(__builtin_arm_mve_vrndpq_x_f16, "", "n")
BUILTIN(__builtin_arm_mve_vrndpq_x_f32, "", "n")
BUILTIN(__builtin_arm_mve_vrndq_f16, "", "n")
BUILTIN(__builtin_arm_mve_vrndq_f32, "", "n")
BUILTIN(__builtin_arm_mve_vrndq_m_f16, "", "n")
BUILTIN(__builtin_arm_mve_vrndq_m_f32, "", "n")
BUILTIN(__builtin_arm_mve_vrndq_x_f16, "", "n")
BUILTIN(__builtin_arm_mve_vrndq_x_f32, "", "n")
BUILTIN(__builtin_arm_mve_vrndxq_f16, "", "n")
BUILTIN(__builtin_arm_mve_vrndxq_f32, "", "n")
BUILTIN(__builtin_arm_mve_vrndxq_m_f16, "", "n")
BUILTIN(__builtin_arm_mve_vrndxq_m_f32, "", "n")
BUILTIN(__builtin_arm_mve_vrndxq_x_f16, "", "n")
BUILTIN(__builtin_arm_mve_vrndxq_x_f32, "", "n")
BUILTIN(__builtin_arm_mve_vrshlq_m_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vrshlq_m_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vrshlq_m_n_s8, "", "n")
BUILTIN(__builtin_arm_mve_vrshlq_m_n_u16, "", "n")
BUILTIN(__builtin_arm_mve_vrshlq_m_n_u32, "", "n")
BUILTIN(__builtin_arm_mve_vrshlq_m_n_u8, "", "n")
BUILTIN(__builtin_arm_mve_vrshlq_m_s16, "", "n")
BUILTIN(__builtin_arm_mve_vrshlq_m_s32, "", "n")
BUILTIN(__builtin_arm_mve_vrshlq_m_s8, "", "n")
BUILTIN(__builtin_arm_mve_vrshlq_m_u16, "", "n")
BUILTIN(__builtin_arm_mve_vrshlq_m_u32, "", "n")
BUILTIN(__builtin_arm_mve_vrshlq_m_u8, "", "n")
BUILTIN(__builtin_arm_mve_vrshlq_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vrshlq_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vrshlq_n_s8, "", "n")
BUILTIN(__builtin_arm_mve_vrshlq_n_u16, "", "n")
BUILTIN(__builtin_arm_mve_vrshlq_n_u32, "", "n")
BUILTIN(__builtin_arm_mve_vrshlq_n_u8, "", "n")
BUILTIN(__builtin_arm_mve_vrshlq_s16, "", "n")
BUILTIN(__builtin_arm_mve_vrshlq_s32, "", "n")
BUILTIN(__builtin_arm_mve_vrshlq_s8, "", "n")
BUILTIN(__builtin_arm_mve_vrshlq_u16, "", "n")
BUILTIN(__builtin_arm_mve_vrshlq_u32, "", "n")
BUILTIN(__builtin_arm_mve_vrshlq_u8, "", "n")
BUILTIN(__builtin_arm_mve_vrshlq_x_s16, "", "n")
BUILTIN(__builtin_arm_mve_vrshlq_x_s32, "", "n")
BUILTIN(__builtin_arm_mve_vrshlq_x_s8, "", "n")
BUILTIN(__builtin_arm_mve_vrshlq_x_u16, "", "n")
BUILTIN(__builtin_arm_mve_vrshlq_x_u32, "", "n")
BUILTIN(__builtin_arm_mve_vrshlq_x_u8, "", "n")
BUILTIN(__builtin_arm_mve_vrshrnbq_m_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vrshrnbq_m_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vrshrnbq_m_n_u16, "", "n")
BUILTIN(__builtin_arm_mve_vrshrnbq_m_n_u32, "", "n")
BUILTIN(__builtin_arm_mve_vrshrnbq_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vrshrnbq_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vrshrnbq_n_u16, "", "n")
BUILTIN(__builtin_arm_mve_vrshrnbq_n_u32, "", "n")
BUILTIN(__builtin_arm_mve_vrshrntq_m_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vrshrntq_m_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vrshrntq_m_n_u16, "", "n")
BUILTIN(__builtin_arm_mve_vrshrntq_m_n_u32, "", "n")
BUILTIN(__builtin_arm_mve_vrshrntq_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vrshrntq_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vrshrntq_n_u16, "", "n")
BUILTIN(__builtin_arm_mve_vrshrntq_n_u32, "", "n")
BUILTIN(__builtin_arm_mve_vrshrq_m_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vrshrq_m_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vrshrq_m_n_s8, "", "n")
BUILTIN(__builtin_arm_mve_vrshrq_m_n_u16, "", "n")
BUILTIN(__builtin_arm_mve_vrshrq_m_n_u32, "", "n")
BUILTIN(__builtin_arm_mve_vrshrq_m_n_u8, "", "n")
BUILTIN(__builtin_arm_mve_vrshrq_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vrshrq_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vrshrq_n_s8, "", "n")
BUILTIN(__builtin_arm_mve_vrshrq_n_u16, "", "n")
BUILTIN(__builtin_arm_mve_vrshrq_n_u32, "", "n")
BUILTIN(__builtin_arm_mve_vrshrq_n_u8, "", "n")
BUILTIN(__builtin_arm_mve_vrshrq_x_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vrshrq_x_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vrshrq_x_n_s8, "", "n")
BUILTIN(__builtin_arm_mve_vrshrq_x_n_u16, "", "n")
BUILTIN(__builtin_arm_mve_vrshrq_x_n_u32, "", "n")
BUILTIN(__builtin_arm_mve_vrshrq_x_n_u8, "", "n")
BUILTIN(__builtin_arm_mve_vsbciq_m_s32, "", "n")
BUILTIN(__builtin_arm_mve_vsbciq_m_u32, "", "n")
BUILTIN(__builtin_arm_mve_vsbciq_s32, "", "n")
BUILTIN(__builtin_arm_mve_vsbciq_u32, "", "n")
BUILTIN(__builtin_arm_mve_vsbcq_m_s32, "", "n")
BUILTIN(__builtin_arm_mve_vsbcq_m_u32, "", "n")
BUILTIN(__builtin_arm_mve_vsbcq_s32, "", "n")
BUILTIN(__builtin_arm_mve_vsbcq_u32, "", "n")
BUILTIN(__builtin_arm_mve_vsetq_lane_f16, "", "n")
BUILTIN(__builtin_arm_mve_vsetq_lane_f32, "", "n")
BUILTIN(__builtin_arm_mve_vsetq_lane_s16, "", "n")
BUILTIN(__builtin_arm_mve_vsetq_lane_s32, "", "n")
BUILTIN(__builtin_arm_mve_vsetq_lane_s64, "", "n")
BUILTIN(__builtin_arm_mve_vsetq_lane_s8, "", "n")
BUILTIN(__builtin_arm_mve_vsetq_lane_u16, "", "n")
BUILTIN(__builtin_arm_mve_vsetq_lane_u32, "", "n")
BUILTIN(__builtin_arm_mve_vsetq_lane_u64, "", "n")
BUILTIN(__builtin_arm_mve_vsetq_lane_u8, "", "n")
BUILTIN(__builtin_arm_mve_vshlcq_m_s16, "", "n")
BUILTIN(__builtin_arm_mve_vshlcq_m_s32, "", "n")
BUILTIN(__builtin_arm_mve_vshlcq_m_s8, "", "n")
BUILTIN(__builtin_arm_mve_vshlcq_m_u16, "", "n")
BUILTIN(__builtin_arm_mve_vshlcq_m_u32, "", "n")
BUILTIN(__builtin_arm_mve_vshlcq_m_u8, "", "n")
BUILTIN(__builtin_arm_mve_vshlcq_s16, "", "n")
BUILTIN(__builtin_arm_mve_vshlcq_s32, "", "n")
BUILTIN(__builtin_arm_mve_vshlcq_s8, "", "n")
BUILTIN(__builtin_arm_mve_vshlcq_u16, "", "n")
BUILTIN(__builtin_arm_mve_vshlcq_u32, "", "n")
BUILTIN(__builtin_arm_mve_vshlcq_u8, "", "n")
BUILTIN(__builtin_arm_mve_vshllbq_m_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vshllbq_m_n_s8, "", "n")
BUILTIN(__builtin_arm_mve_vshllbq_m_n_u16, "", "n")
BUILTIN(__builtin_arm_mve_vshllbq_m_n_u8, "", "n")
BUILTIN(__builtin_arm_mve_vshllbq_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vshllbq_n_s8, "", "n")
BUILTIN(__builtin_arm_mve_vshllbq_n_u16, "", "n")
BUILTIN(__builtin_arm_mve_vshllbq_n_u8, "", "n")
BUILTIN(__builtin_arm_mve_vshllbq_x_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vshllbq_x_n_s8, "", "n")
BUILTIN(__builtin_arm_mve_vshllbq_x_n_u16, "", "n")
BUILTIN(__builtin_arm_mve_vshllbq_x_n_u8, "", "n")
BUILTIN(__builtin_arm_mve_vshlltq_m_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vshlltq_m_n_s8, "", "n")
BUILTIN(__builtin_arm_mve_vshlltq_m_n_u16, "", "n")
BUILTIN(__builtin_arm_mve_vshlltq_m_n_u8, "", "n")
BUILTIN(__builtin_arm_mve_vshlltq_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vshlltq_n_s8, "", "n")
BUILTIN(__builtin_arm_mve_vshlltq_n_u16, "", "n")
BUILTIN(__builtin_arm_mve_vshlltq_n_u8, "", "n")
BUILTIN(__builtin_arm_mve_vshlltq_x_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vshlltq_x_n_s8, "", "n")
BUILTIN(__builtin_arm_mve_vshlltq_x_n_u16, "", "n")
BUILTIN(__builtin_arm_mve_vshlltq_x_n_u8, "", "n")
BUILTIN(__builtin_arm_mve_vshlq_m_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vshlq_m_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vshlq_m_n_s8, "", "n")
BUILTIN(__builtin_arm_mve_vshlq_m_n_u16, "", "n")
BUILTIN(__builtin_arm_mve_vshlq_m_n_u32, "", "n")
BUILTIN(__builtin_arm_mve_vshlq_m_n_u8, "", "n")
BUILTIN(__builtin_arm_mve_vshlq_m_r_s16, "", "n")
BUILTIN(__builtin_arm_mve_vshlq_m_r_s32, "", "n")
BUILTIN(__builtin_arm_mve_vshlq_m_r_s8, "", "n")
BUILTIN(__builtin_arm_mve_vshlq_m_r_u16, "", "n")
BUILTIN(__builtin_arm_mve_vshlq_m_r_u32, "", "n")
BUILTIN(__builtin_arm_mve_vshlq_m_r_u8, "", "n")
BUILTIN(__builtin_arm_mve_vshlq_m_s16, "", "n")
BUILTIN(__builtin_arm_mve_vshlq_m_s32, "", "n")
BUILTIN(__builtin_arm_mve_vshlq_m_s8, "", "n")
BUILTIN(__builtin_arm_mve_vshlq_m_u16, "", "n")
BUILTIN(__builtin_arm_mve_vshlq_m_u32, "", "n")
BUILTIN(__builtin_arm_mve_vshlq_m_u8, "", "n")
BUILTIN(__builtin_arm_mve_vshlq_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vshlq_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vshlq_n_s8, "", "n")
BUILTIN(__builtin_arm_mve_vshlq_n_u16, "", "n")
BUILTIN(__builtin_arm_mve_vshlq_n_u32, "", "n")
BUILTIN(__builtin_arm_mve_vshlq_n_u8, "", "n")
BUILTIN(__builtin_arm_mve_vshlq_r_s16, "", "n")
BUILTIN(__builtin_arm_mve_vshlq_r_s32, "", "n")
BUILTIN(__builtin_arm_mve_vshlq_r_s8, "", "n")
BUILTIN(__builtin_arm_mve_vshlq_r_u16, "", "n")
BUILTIN(__builtin_arm_mve_vshlq_r_u32, "", "n")
BUILTIN(__builtin_arm_mve_vshlq_r_u8, "", "n")
BUILTIN(__builtin_arm_mve_vshlq_s16, "", "n")
BUILTIN(__builtin_arm_mve_vshlq_s32, "", "n")
BUILTIN(__builtin_arm_mve_vshlq_s8, "", "n")
BUILTIN(__builtin_arm_mve_vshlq_u16, "", "n")
BUILTIN(__builtin_arm_mve_vshlq_u32, "", "n")
BUILTIN(__builtin_arm_mve_vshlq_u8, "", "n")
BUILTIN(__builtin_arm_mve_vshlq_x_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vshlq_x_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vshlq_x_n_s8, "", "n")
BUILTIN(__builtin_arm_mve_vshlq_x_n_u16, "", "n")
BUILTIN(__builtin_arm_mve_vshlq_x_n_u32, "", "n")
BUILTIN(__builtin_arm_mve_vshlq_x_n_u8, "", "n")
BUILTIN(__builtin_arm_mve_vshlq_x_s16, "", "n")
BUILTIN(__builtin_arm_mve_vshlq_x_s32, "", "n")
BUILTIN(__builtin_arm_mve_vshlq_x_s8, "", "n")
BUILTIN(__builtin_arm_mve_vshlq_x_u16, "", "n")
BUILTIN(__builtin_arm_mve_vshlq_x_u32, "", "n")
BUILTIN(__builtin_arm_mve_vshlq_x_u8, "", "n")
BUILTIN(__builtin_arm_mve_vshrnbq_m_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vshrnbq_m_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vshrnbq_m_n_u16, "", "n")
BUILTIN(__builtin_arm_mve_vshrnbq_m_n_u32, "", "n")
BUILTIN(__builtin_arm_mve_vshrnbq_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vshrnbq_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vshrnbq_n_u16, "", "n")
BUILTIN(__builtin_arm_mve_vshrnbq_n_u32, "", "n")
BUILTIN(__builtin_arm_mve_vshrntq_m_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vshrntq_m_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vshrntq_m_n_u16, "", "n")
BUILTIN(__builtin_arm_mve_vshrntq_m_n_u32, "", "n")
BUILTIN(__builtin_arm_mve_vshrntq_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vshrntq_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vshrntq_n_u16, "", "n")
BUILTIN(__builtin_arm_mve_vshrntq_n_u32, "", "n")
BUILTIN(__builtin_arm_mve_vshrq_m_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vshrq_m_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vshrq_m_n_s8, "", "n")
BUILTIN(__builtin_arm_mve_vshrq_m_n_u16, "", "n")
BUILTIN(__builtin_arm_mve_vshrq_m_n_u32, "", "n")
BUILTIN(__builtin_arm_mve_vshrq_m_n_u8, "", "n")
BUILTIN(__builtin_arm_mve_vshrq_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vshrq_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vshrq_n_s8, "", "n")
BUILTIN(__builtin_arm_mve_vshrq_n_u16, "", "n")
BUILTIN(__builtin_arm_mve_vshrq_n_u32, "", "n")
BUILTIN(__builtin_arm_mve_vshrq_n_u8, "", "n")
BUILTIN(__builtin_arm_mve_vshrq_x_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vshrq_x_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vshrq_x_n_s8, "", "n")
BUILTIN(__builtin_arm_mve_vshrq_x_n_u16, "", "n")
BUILTIN(__builtin_arm_mve_vshrq_x_n_u32, "", "n")
BUILTIN(__builtin_arm_mve_vshrq_x_n_u8, "", "n")
BUILTIN(__builtin_arm_mve_vsliq_m_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vsliq_m_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vsliq_m_n_s8, "", "n")
BUILTIN(__builtin_arm_mve_vsliq_m_n_u16, "", "n")
BUILTIN(__builtin_arm_mve_vsliq_m_n_u32, "", "n")
BUILTIN(__builtin_arm_mve_vsliq_m_n_u8, "", "n")
BUILTIN(__builtin_arm_mve_vsliq_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vsliq_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vsliq_n_s8, "", "n")
BUILTIN(__builtin_arm_mve_vsliq_n_u16, "", "n")
BUILTIN(__builtin_arm_mve_vsliq_n_u32, "", "n")
BUILTIN(__builtin_arm_mve_vsliq_n_u8, "", "n")
BUILTIN(__builtin_arm_mve_vsriq_m_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vsriq_m_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vsriq_m_n_s8, "", "n")
BUILTIN(__builtin_arm_mve_vsriq_m_n_u16, "", "n")
BUILTIN(__builtin_arm_mve_vsriq_m_n_u32, "", "n")
BUILTIN(__builtin_arm_mve_vsriq_m_n_u8, "", "n")
BUILTIN(__builtin_arm_mve_vsriq_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vsriq_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vsriq_n_s8, "", "n")
BUILTIN(__builtin_arm_mve_vsriq_n_u16, "", "n")
BUILTIN(__builtin_arm_mve_vsriq_n_u32, "", "n")
BUILTIN(__builtin_arm_mve_vsriq_n_u8, "", "n")
BUILTIN(__builtin_arm_mve_vst1q_f16, "", "n")
BUILTIN(__builtin_arm_mve_vst1q_f32, "", "n")
BUILTIN(__builtin_arm_mve_vst1q_p_f16, "", "n")
BUILTIN(__builtin_arm_mve_vst1q_p_f32, "", "n")
BUILTIN(__builtin_arm_mve_vst1q_p_s16, "", "n")
BUILTIN(__builtin_arm_mve_vst1q_p_s32, "", "n")
BUILTIN(__builtin_arm_mve_vst1q_p_s8, "", "n")
BUILTIN(__builtin_arm_mve_vst1q_p_u16, "", "n")
BUILTIN(__builtin_arm_mve_vst1q_p_u32, "", "n")
BUILTIN(__builtin_arm_mve_vst1q_p_u8, "", "n")
BUILTIN(__builtin_arm_mve_vst1q_s16, "", "n")
BUILTIN(__builtin_arm_mve_vst1q_s32, "", "n")
BUILTIN(__builtin_arm_mve_vst1q_s8, "", "n")
BUILTIN(__builtin_arm_mve_vst1q_u16, "", "n")
BUILTIN(__builtin_arm_mve_vst1q_u32, "", "n")
BUILTIN(__builtin_arm_mve_vst1q_u8, "", "n")
BUILTIN(__builtin_arm_mve_vst2q_f16, "", "n")
BUILTIN(__builtin_arm_mve_vst2q_f32, "", "n")
BUILTIN(__builtin_arm_mve_vst2q_s16, "", "n")
BUILTIN(__builtin_arm_mve_vst2q_s32, "", "n")
BUILTIN(__builtin_arm_mve_vst2q_s8, "", "n")
BUILTIN(__builtin_arm_mve_vst2q_u16, "", "n")
BUILTIN(__builtin_arm_mve_vst2q_u32, "", "n")
BUILTIN(__builtin_arm_mve_vst2q_u8, "", "n")
BUILTIN(__builtin_arm_mve_vst4q_f16, "", "n")
BUILTIN(__builtin_arm_mve_vst4q_f32, "", "n")
BUILTIN(__builtin_arm_mve_vst4q_s16, "", "n")
BUILTIN(__builtin_arm_mve_vst4q_s32, "", "n")
BUILTIN(__builtin_arm_mve_vst4q_s8, "", "n")
BUILTIN(__builtin_arm_mve_vst4q_u16, "", "n")
BUILTIN(__builtin_arm_mve_vst4q_u32, "", "n")
BUILTIN(__builtin_arm_mve_vst4q_u8, "", "n")
BUILTIN(__builtin_arm_mve_vstrbq_p_s16, "", "n")
BUILTIN(__builtin_arm_mve_vstrbq_p_s32, "", "n")
BUILTIN(__builtin_arm_mve_vstrbq_p_s8, "", "n")
BUILTIN(__builtin_arm_mve_vstrbq_p_u16, "", "n")
BUILTIN(__builtin_arm_mve_vstrbq_p_u32, "", "n")
BUILTIN(__builtin_arm_mve_vstrbq_p_u8, "", "n")
BUILTIN(__builtin_arm_mve_vstrbq_s16, "", "n")
BUILTIN(__builtin_arm_mve_vstrbq_s32, "", "n")
BUILTIN(__builtin_arm_mve_vstrbq_s8, "", "n")
BUILTIN(__builtin_arm_mve_vstrbq_scatter_offset_p_s16, "", "n")
BUILTIN(__builtin_arm_mve_vstrbq_scatter_offset_p_s32, "", "n")
BUILTIN(__builtin_arm_mve_vstrbq_scatter_offset_p_s8, "", "n")
BUILTIN(__builtin_arm_mve_vstrbq_scatter_offset_p_u16, "", "n")
BUILTIN(__builtin_arm_mve_vstrbq_scatter_offset_p_u32, "", "n")
BUILTIN(__builtin_arm_mve_vstrbq_scatter_offset_p_u8, "", "n")
BUILTIN(__builtin_arm_mve_vstrbq_scatter_offset_s16, "", "n")
BUILTIN(__builtin_arm_mve_vstrbq_scatter_offset_s32, "", "n")
BUILTIN(__builtin_arm_mve_vstrbq_scatter_offset_s8, "", "n")
BUILTIN(__builtin_arm_mve_vstrbq_scatter_offset_u16, "", "n")
BUILTIN(__builtin_arm_mve_vstrbq_scatter_offset_u32, "", "n")
BUILTIN(__builtin_arm_mve_vstrbq_scatter_offset_u8, "", "n")
BUILTIN(__builtin_arm_mve_vstrbq_u16, "", "n")
BUILTIN(__builtin_arm_mve_vstrbq_u32, "", "n")
BUILTIN(__builtin_arm_mve_vstrbq_u8, "", "n")
BUILTIN(__builtin_arm_mve_vstrdq_scatter_base_p_s64, "", "n")
BUILTIN(__builtin_arm_mve_vstrdq_scatter_base_p_u64, "", "n")
BUILTIN(__builtin_arm_mve_vstrdq_scatter_base_s64, "", "n")
BUILTIN(__builtin_arm_mve_vstrdq_scatter_base_u64, "", "n")
BUILTIN(__builtin_arm_mve_vstrdq_scatter_base_wb_p_s64, "", "n")
BUILTIN(__builtin_arm_mve_vstrdq_scatter_base_wb_p_u64, "", "n")
BUILTIN(__builtin_arm_mve_vstrdq_scatter_base_wb_s64, "", "n")
BUILTIN(__builtin_arm_mve_vstrdq_scatter_base_wb_u64, "", "n")
BUILTIN(__builtin_arm_mve_vstrdq_scatter_offset_p_s64, "", "n")
BUILTIN(__builtin_arm_mve_vstrdq_scatter_offset_p_u64, "", "n")
BUILTIN(__builtin_arm_mve_vstrdq_scatter_offset_s64, "", "n")
BUILTIN(__builtin_arm_mve_vstrdq_scatter_offset_u64, "", "n")
BUILTIN(__builtin_arm_mve_vstrdq_scatter_shifted_offset_p_s64, "", "n")
BUILTIN(__builtin_arm_mve_vstrdq_scatter_shifted_offset_p_u64, "", "n")
BUILTIN(__builtin_arm_mve_vstrdq_scatter_shifted_offset_s64, "", "n")
BUILTIN(__builtin_arm_mve_vstrdq_scatter_shifted_offset_u64, "", "n")
BUILTIN(__builtin_arm_mve_vstrhq_f16, "", "n")
BUILTIN(__builtin_arm_mve_vstrhq_p_f16, "", "n")
BUILTIN(__builtin_arm_mve_vstrhq_p_s16, "", "n")
BUILTIN(__builtin_arm_mve_vstrhq_p_s32, "", "n")
BUILTIN(__builtin_arm_mve_vstrhq_p_u16, "", "n")
BUILTIN(__builtin_arm_mve_vstrhq_p_u32, "", "n")
BUILTIN(__builtin_arm_mve_vstrhq_s16, "", "n")
BUILTIN(__builtin_arm_mve_vstrhq_s32, "", "n")
BUILTIN(__builtin_arm_mve_vstrhq_scatter_offset_f16, "", "n")
BUILTIN(__builtin_arm_mve_vstrhq_scatter_offset_p_f16, "", "n")
BUILTIN(__builtin_arm_mve_vstrhq_scatter_offset_p_s16, "", "n")
BUILTIN(__builtin_arm_mve_vstrhq_scatter_offset_p_s32, "", "n")
BUILTIN(__builtin_arm_mve_vstrhq_scatter_offset_p_u16, "", "n")
BUILTIN(__builtin_arm_mve_vstrhq_scatter_offset_p_u32, "", "n")
BUILTIN(__builtin_arm_mve_vstrhq_scatter_offset_s16, "", "n")
BUILTIN(__builtin_arm_mve_vstrhq_scatter_offset_s32, "", "n")
BUILTIN(__builtin_arm_mve_vstrhq_scatter_offset_u16, "", "n")
BUILTIN(__builtin_arm_mve_vstrhq_scatter_offset_u32, "", "n")
BUILTIN(__builtin_arm_mve_vstrhq_scatter_shifted_offset_f16, "", "n")
BUILTIN(__builtin_arm_mve_vstrhq_scatter_shifted_offset_p_f16, "", "n")
BUILTIN(__builtin_arm_mve_vstrhq_scatter_shifted_offset_p_s16, "", "n")
BUILTIN(__builtin_arm_mve_vstrhq_scatter_shifted_offset_p_s32, "", "n")
BUILTIN(__builtin_arm_mve_vstrhq_scatter_shifted_offset_p_u16, "", "n")
BUILTIN(__builtin_arm_mve_vstrhq_scatter_shifted_offset_p_u32, "", "n")
BUILTIN(__builtin_arm_mve_vstrhq_scatter_shifted_offset_s16, "", "n")
BUILTIN(__builtin_arm_mve_vstrhq_scatter_shifted_offset_s32, "", "n")
BUILTIN(__builtin_arm_mve_vstrhq_scatter_shifted_offset_u16, "", "n")
BUILTIN(__builtin_arm_mve_vstrhq_scatter_shifted_offset_u32, "", "n")
BUILTIN(__builtin_arm_mve_vstrhq_u16, "", "n")
BUILTIN(__builtin_arm_mve_vstrhq_u32, "", "n")
BUILTIN(__builtin_arm_mve_vstrwq_f32, "", "n")
BUILTIN(__builtin_arm_mve_vstrwq_p_f32, "", "n")
BUILTIN(__builtin_arm_mve_vstrwq_p_s32, "", "n")
BUILTIN(__builtin_arm_mve_vstrwq_p_u32, "", "n")
BUILTIN(__builtin_arm_mve_vstrwq_s32, "", "n")
BUILTIN(__builtin_arm_mve_vstrwq_scatter_base_f32, "", "n")
BUILTIN(__builtin_arm_mve_vstrwq_scatter_base_p_f32, "", "n")
BUILTIN(__builtin_arm_mve_vstrwq_scatter_base_p_s32, "", "n")
BUILTIN(__builtin_arm_mve_vstrwq_scatter_base_p_u32, "", "n")
BUILTIN(__builtin_arm_mve_vstrwq_scatter_base_s32, "", "n")
BUILTIN(__builtin_arm_mve_vstrwq_scatter_base_u32, "", "n")
BUILTIN(__builtin_arm_mve_vstrwq_scatter_base_wb_f32, "", "n")
BUILTIN(__builtin_arm_mve_vstrwq_scatter_base_wb_p_f32, "", "n")
BUILTIN(__builtin_arm_mve_vstrwq_scatter_base_wb_p_s32, "", "n")
BUILTIN(__builtin_arm_mve_vstrwq_scatter_base_wb_p_u32, "", "n")
BUILTIN(__builtin_arm_mve_vstrwq_scatter_base_wb_s32, "", "n")
BUILTIN(__builtin_arm_mve_vstrwq_scatter_base_wb_u32, "", "n")
BUILTIN(__builtin_arm_mve_vstrwq_scatter_offset_f32, "", "n")
BUILTIN(__builtin_arm_mve_vstrwq_scatter_offset_p_f32, "", "n")
BUILTIN(__builtin_arm_mve_vstrwq_scatter_offset_p_s32, "", "n")
BUILTIN(__builtin_arm_mve_vstrwq_scatter_offset_p_u32, "", "n")
BUILTIN(__builtin_arm_mve_vstrwq_scatter_offset_s32, "", "n")
BUILTIN(__builtin_arm_mve_vstrwq_scatter_offset_u32, "", "n")
BUILTIN(__builtin_arm_mve_vstrwq_scatter_shifted_offset_f32, "", "n")
BUILTIN(__builtin_arm_mve_vstrwq_scatter_shifted_offset_p_f32, "", "n")
BUILTIN(__builtin_arm_mve_vstrwq_scatter_shifted_offset_p_s32, "", "n")
BUILTIN(__builtin_arm_mve_vstrwq_scatter_shifted_offset_p_u32, "", "n")
BUILTIN(__builtin_arm_mve_vstrwq_scatter_shifted_offset_s32, "", "n")
BUILTIN(__builtin_arm_mve_vstrwq_scatter_shifted_offset_u32, "", "n")
BUILTIN(__builtin_arm_mve_vstrwq_u32, "", "n")
BUILTIN(__builtin_arm_mve_vsubq_f16, "", "n")
BUILTIN(__builtin_arm_mve_vsubq_f32, "", "n")
BUILTIN(__builtin_arm_mve_vsubq_m_f16, "", "n")
BUILTIN(__builtin_arm_mve_vsubq_m_f32, "", "n")
BUILTIN(__builtin_arm_mve_vsubq_m_n_f16, "", "n")
BUILTIN(__builtin_arm_mve_vsubq_m_n_f32, "", "n")
BUILTIN(__builtin_arm_mve_vsubq_m_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vsubq_m_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vsubq_m_n_s8, "", "n")
BUILTIN(__builtin_arm_mve_vsubq_m_n_u16, "", "n")
BUILTIN(__builtin_arm_mve_vsubq_m_n_u32, "", "n")
BUILTIN(__builtin_arm_mve_vsubq_m_n_u8, "", "n")
BUILTIN(__builtin_arm_mve_vsubq_m_s16, "", "n")
BUILTIN(__builtin_arm_mve_vsubq_m_s32, "", "n")
BUILTIN(__builtin_arm_mve_vsubq_m_s8, "", "n")
BUILTIN(__builtin_arm_mve_vsubq_m_u16, "", "n")
BUILTIN(__builtin_arm_mve_vsubq_m_u32, "", "n")
BUILTIN(__builtin_arm_mve_vsubq_m_u8, "", "n")
BUILTIN(__builtin_arm_mve_vsubq_n_f16, "", "n")
BUILTIN(__builtin_arm_mve_vsubq_n_f32, "", "n")
BUILTIN(__builtin_arm_mve_vsubq_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vsubq_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vsubq_n_s8, "", "n")
BUILTIN(__builtin_arm_mve_vsubq_n_u16, "", "n")
BUILTIN(__builtin_arm_mve_vsubq_n_u32, "", "n")
BUILTIN(__builtin_arm_mve_vsubq_n_u8, "", "n")
BUILTIN(__builtin_arm_mve_vsubq_s16, "", "n")
BUILTIN(__builtin_arm_mve_vsubq_s32, "", "n")
BUILTIN(__builtin_arm_mve_vsubq_s8, "", "n")
BUILTIN(__builtin_arm_mve_vsubq_u16, "", "n")
BUILTIN(__builtin_arm_mve_vsubq_u32, "", "n")
BUILTIN(__builtin_arm_mve_vsubq_u8, "", "n")
BUILTIN(__builtin_arm_mve_vsubq_x_f16, "", "n")
BUILTIN(__builtin_arm_mve_vsubq_x_f32, "", "n")
BUILTIN(__builtin_arm_mve_vsubq_x_n_f16, "", "n")
BUILTIN(__builtin_arm_mve_vsubq_x_n_f32, "", "n")
BUILTIN(__builtin_arm_mve_vsubq_x_n_s16, "", "n")
BUILTIN(__builtin_arm_mve_vsubq_x_n_s32, "", "n")
BUILTIN(__builtin_arm_mve_vsubq_x_n_s8, "", "n")
BUILTIN(__builtin_arm_mve_vsubq_x_n_u16, "", "n")
BUILTIN(__builtin_arm_mve_vsubq_x_n_u32, "", "n")
BUILTIN(__builtin_arm_mve_vsubq_x_n_u8, "", "n")
BUILTIN(__builtin_arm_mve_vsubq_x_s16, "", "n")
BUILTIN(__builtin_arm_mve_vsubq_x_s32, "", "n")
BUILTIN(__builtin_arm_mve_vsubq_x_s8, "", "n")
BUILTIN(__builtin_arm_mve_vsubq_x_u16, "", "n")
BUILTIN(__builtin_arm_mve_vsubq_x_u32, "", "n")
BUILTIN(__builtin_arm_mve_vsubq_x_u8, "", "n")
BUILTIN(__builtin_arm_mve_vuninitializedq_f16, "", "n")
BUILTIN(__builtin_arm_mve_vuninitializedq_f32, "", "n")
BUILTIN(__builtin_arm_mve_vuninitializedq_polymorphic_f16, "", "n")
BUILTIN(__builtin_arm_mve_vuninitializedq_polymorphic_f32, "", "n")
BUILTIN(__builtin_arm_mve_vuninitializedq_polymorphic_s16, "", "n")
BUILTIN(__builtin_arm_mve_vuninitializedq_polymorphic_s32, "", "n")
BUILTIN(__builtin_arm_mve_vuninitializedq_polymorphic_s64, "", "n")
BUILTIN(__builtin_arm_mve_vuninitializedq_polymorphic_s8, "", "n")
BUILTIN(__builtin_arm_mve_vuninitializedq_polymorphic_u16, "", "n")
BUILTIN(__builtin_arm_mve_vuninitializedq_polymorphic_u32, "", "n")
BUILTIN(__builtin_arm_mve_vuninitializedq_polymorphic_u64, "", "n")
BUILTIN(__builtin_arm_mve_vuninitializedq_polymorphic_u8, "", "n")
BUILTIN(__builtin_arm_mve_vuninitializedq_s16, "", "n")
BUILTIN(__builtin_arm_mve_vuninitializedq_s32, "", "n")
BUILTIN(__builtin_arm_mve_vuninitializedq_s64, "", "n")
BUILTIN(__builtin_arm_mve_vuninitializedq_s8, "", "n")
BUILTIN(__builtin_arm_mve_vuninitializedq_u16, "", "n")
BUILTIN(__builtin_arm_mve_vuninitializedq_u32, "", "n")
BUILTIN(__builtin_arm_mve_vuninitializedq_u64, "", "n")
BUILTIN(__builtin_arm_mve_vuninitializedq_u8, "", "n")
BUILTIN(__builtin_arm_mve_vabavq_p, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vabavq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vabdq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vabdq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vabdq_x, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vabsq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vabsq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vabsq_x, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vadciq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vadciq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vadcq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vadcq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vaddlvaq_p, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vaddlvaq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vaddlvq_p, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vaddlvq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vaddq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vaddq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vaddq_x, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vaddvaq_p, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vaddvaq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vaddvq_p, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vaddvq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vandq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vandq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vandq_x, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vbicq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vbicq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vbicq_m_n, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vbicq_x, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vbrsrq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vbrsrq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vbrsrq_x, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vcaddq_rot270, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vcaddq_rot270_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vcaddq_rot270_x, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vcaddq_rot90, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vcaddq_rot90_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vcaddq_rot90_x, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vclsq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vclsq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vclsq_x, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vclzq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vclzq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vclzq_x, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vcmlaq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vcmlaq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vcmlaq_rot180, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vcmlaq_rot180_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vcmlaq_rot270, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vcmlaq_rot270_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vcmlaq_rot90, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vcmlaq_rot90_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vcmpcsq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vcmpcsq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vcmpeqq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vcmpeqq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vcmpgeq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vcmpgeq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vcmpgtq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vcmpgtq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vcmphiq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vcmphiq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vcmpleq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vcmpleq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vcmpltq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vcmpltq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vcmpneq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vcmpneq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vcmulq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vcmulq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vcmulq_rot180, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vcmulq_rot180_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vcmulq_rot180_x, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vcmulq_rot270, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vcmulq_rot270_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vcmulq_rot270_x, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vcmulq_rot90, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vcmulq_rot90_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vcmulq_rot90_x, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vcmulq_x, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vcvtaq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vcvtmq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vcvtnq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vcvtpq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vcvtq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vcvtq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vcvtq_m_n, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vcvtq_n, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vcvtq_x, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vcvtq_x_n, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vddupq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vddupq_u16, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vddupq_u32, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vddupq_u8, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vddupq_x_u16, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vddupq_x_u32, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vddupq_x_u8, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vdupq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vdwdupq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vdwdupq_u16, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vdwdupq_u32, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vdwdupq_u8, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vdwdupq_x_u16, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vdwdupq_x_u32, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vdwdupq_x_u8, "vi.", "nt")
BUILTIN(__builtin_arm_mve_veorq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_veorq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_veorq_x, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vfmaq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vfmaq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vfmasq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vfmasq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vfmsq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vfmsq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vgetq_lane, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vhaddq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vhaddq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vhaddq_x, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vhcaddq_rot270_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vhcaddq_rot270, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vhcaddq_rot270_x, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vhcaddq_rot90_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vhcaddq_rot90, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vhcaddq_rot90_x, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vhsubq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vhsubq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vhsubq_x, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vidupq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vidupq_u16, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vidupq_u32, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vidupq_u8, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vidupq_x_u16, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vidupq_x_u32, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vidupq_x_u8, "vi.", "nt")
BUILTIN(__builtin_arm_mve_viwdupq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_viwdupq_u16, "vi.", "nt")
BUILTIN(__builtin_arm_mve_viwdupq_u32, "vi.", "nt")
BUILTIN(__builtin_arm_mve_viwdupq_u8, "vi.", "nt")
BUILTIN(__builtin_arm_mve_viwdupq_x_u16, "vi.", "nt")
BUILTIN(__builtin_arm_mve_viwdupq_x_u32, "vi.", "nt")
BUILTIN(__builtin_arm_mve_viwdupq_x_u8, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vld1q, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vld1q_z, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vld2q, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vld4q, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vldrbq_gather_offset, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vldrbq_gather_offset_z, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vldrdq_gather_offset, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vldrdq_gather_offset_z, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vldrdq_gather_shifted_offset, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vldrdq_gather_shifted_offset_z, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vldrhq_gather_offset, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vldrhq_gather_offset_z, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vldrhq_gather_shifted_offset, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vldrhq_gather_shifted_offset_z, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vldrwq_gather_offset, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vldrwq_gather_offset_z, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vldrwq_gather_shifted_offset, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vldrwq_gather_shifted_offset_z, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vmaxaq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vmaxaq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vmaxavq_p, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vmaxavq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vmaxnmaq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vmaxnmaq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vmaxnmavq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vmaxnmavq_p, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vmaxnmq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vmaxnmq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vmaxnmq_x, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vmaxnmvq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vmaxnmvq_p, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vmaxq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vmaxq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vmaxq_x, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vmaxvq_p, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vmaxvq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vminaq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vminaq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vminavq_p, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vminavq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vminnmaq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vminnmaq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vminnmavq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vminnmavq_p, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vminnmq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vminnmq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vminnmq_x, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vminnmvq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vminnmvq_p, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vminq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vminq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vminq_x, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vminvq_p, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vminvq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vmladavaq_p, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vmladavaq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vmladavaxq_p, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vmladavaxq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vmladavq_p, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vmladavq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vmladavxq_p, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vmladavxq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vmlaldavaq_p, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vmlaldavaq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vmlaldavaxq_p, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vmlaldavaxq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vmlaldavq_p, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vmlaldavq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vmlaldavxq_p, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vmlaldavxq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vmlaq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vmlaq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vmlasq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vmlasq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vmlsdavaq_p, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vmlsdavaq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vmlsdavaxq_p, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vmlsdavaxq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vmlsdavq_p, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vmlsdavq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vmlsdavxq_p, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vmlsdavxq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vmlsldavaq_p, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vmlsldavaq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vmlsldavaxq_p, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vmlsldavaxq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vmlsldavq_p, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vmlsldavq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vmlsldavxq_p, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vmlsldavxq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vmovlbq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vmovlbq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vmovlbq_x, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vmovltq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vmovltq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vmovltq_x, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vmovnbq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vmovnbq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vmovntq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vmovntq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vmulhq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vmulhq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vmulhq_x, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vmullbq_int_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vmullbq_int, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vmullbq_int_x, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vmullbq_poly_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vmullbq_poly, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vmullbq_poly_x, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vmulltq_int_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vmulltq_int, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vmulltq_int_x, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vmulltq_poly_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vmulltq_poly, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vmulltq_poly_x, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vmulq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vmulq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vmulq_x, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vmvnq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vmvnq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vmvnq_x, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vnegq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vnegq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vnegq_x, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vornq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vornq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vornq_x, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vorrq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vorrq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vorrq_m_n, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vorrq_x, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vpselq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vqabsq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vqabsq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vqaddq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vqaddq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vqdmladhq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vqdmladhq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vqdmladhxq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vqdmladhxq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vqdmlahq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vqdmlahq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vqdmlashq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vqdmlashq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vqdmlsdhq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vqdmlsdhq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vqdmlsdhxq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vqdmlsdhxq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vqdmulhq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vqdmulhq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vqdmullbq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vqdmullbq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vqdmulltq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vqdmulltq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vqmovnbq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vqmovnbq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vqmovntq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vqmovntq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vqmovunbq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vqmovunbq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vqmovuntq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vqmovuntq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vqnegq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vqnegq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vqrdmladhq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vqrdmladhq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vqrdmladhxq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vqrdmladhxq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vqrdmlahq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vqrdmlahq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vqrdmlashq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vqrdmlashq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vqrdmlsdhq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vqrdmlsdhq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vqrdmlsdhxq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vqrdmlsdhxq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vqrdmulhq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vqrdmulhq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vqrshlq_m_n, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vqrshlq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vqrshlq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vqrshrnbq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vqrshrnbq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vqrshrntq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vqrshrntq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vqrshrunbq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vqrshrunbq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vqrshruntq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vqrshruntq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vqshlq_m_n, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vqshlq_m_r, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vqshlq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vqshlq_n, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vqshlq_r, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vqshlq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vqshluq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vqshluq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vqshrnbq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vqshrnbq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vqshrntq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vqshrntq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vqshrunbq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vqshrunbq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vqshruntq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vqshruntq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vqsubq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vqsubq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vreinterpretq_f16, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vreinterpretq_f32, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vreinterpretq_s16, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vreinterpretq_s32, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vreinterpretq_s64, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vreinterpretq_s8, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vreinterpretq_u16, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vreinterpretq_u32, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vreinterpretq_u64, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vreinterpretq_u8, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vrev16q_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vrev16q, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vrev16q_x, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vrev32q, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vrev32q_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vrev32q_x, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vrev64q, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vrev64q_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vrev64q_x, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vrhaddq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vrhaddq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vrhaddq_x, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vrmlaldavhaq_p, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vrmlaldavhaq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vrmlaldavhaxq_p, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vrmlaldavhaxq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vrmlaldavhq_p, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vrmlaldavhq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vrmlaldavhxq_p, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vrmlaldavhxq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vrmlsldavhaq_p, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vrmlsldavhaq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vrmlsldavhaxq_p, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vrmlsldavhaxq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vrmlsldavhq_p, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vrmlsldavhq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vrmlsldavhxq_p, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vrmlsldavhxq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vrmulhq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vrmulhq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vrmulhq_x, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vrndaq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vrndaq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vrndaq_x, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vrndmq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vrndmq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vrndmq_x, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vrndnq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vrndnq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vrndnq_x, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vrndpq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vrndpq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vrndpq_x, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vrndq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vrndq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vrndq_x, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vrndxq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vrndxq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vrndxq_x, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vrshlq_m_n, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vrshlq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vrshlq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vrshlq_x, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vrshrnbq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vrshrnbq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vrshrntq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vrshrntq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vrshrq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vrshrq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vrshrq_x, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vsbciq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vsbciq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vsbcq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vsbcq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vsetq_lane, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vshlcq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vshlcq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vshllbq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vshllbq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vshllbq_x, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vshlltq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vshlltq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vshlltq_x, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vshlq_m_n, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vshlq_m_r, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vshlq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vshlq_n, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vshlq_r, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vshlq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vshlq_x_n, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vshlq_x, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vshrnbq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vshrnbq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vshrntq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vshrntq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vshrq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vshrq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vshrq_x, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vsliq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vsliq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vsriq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vsriq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vst1q, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vst1q_p, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vst2q, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vst4q, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vstrbq_p, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vstrbq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vstrbq_scatter_offset_p, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vstrbq_scatter_offset, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vstrdq_scatter_base_p, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vstrdq_scatter_base, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vstrdq_scatter_base_wb_p, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vstrdq_scatter_base_wb, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vstrdq_scatter_offset_p, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vstrdq_scatter_offset, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vstrdq_scatter_shifted_offset_p, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vstrdq_scatter_shifted_offset, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vstrhq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vstrhq_p, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vstrhq_scatter_offset, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vstrhq_scatter_offset_p, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vstrhq_scatter_shifted_offset, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vstrhq_scatter_shifted_offset_p, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vstrwq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vstrwq_p, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vstrwq_scatter_base, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vstrwq_scatter_base_p, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vstrwq_scatter_base_wb, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vstrwq_scatter_base_wb_p, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vstrwq_scatter_offset, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vstrwq_scatter_offset_p, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vstrwq_scatter_shifted_offset, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vstrwq_scatter_shifted_offset_p, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vsubq, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vsubq_m, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vsubq_x, "vi.", "nt")
BUILTIN(__builtin_arm_mve_vuninitializedq, "vi.", "ntu")
