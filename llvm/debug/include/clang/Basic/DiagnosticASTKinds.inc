#ifdef ASTSTART
__ASTSTART = DIAG_START_AST,
#undef ASTSTART
#endif

DIAG(err_asm_empty_symbolic_operand_name, CLASS_ERROR, (unsigned)diag::Severity::Error, "empty symbolic operand name in inline assembly string", 0, SFINAE_SubstitutionFailure, false, true, true, false, 12)
DIAG(err_asm_invalid_escape, CLASS_ERROR, (unsigned)diag::Severity::Error, "invalid %% escape in inline assembly string", 0, SFINAE_SubstitutionFailure, false, true, true, false, 12)
DIAG(err_asm_invalid_operand_number, CLASS_ERROR, (unsigned)diag::Severity::Error, "invalid operand number in inline asm string", 0, SFINAE_SubstitutionFailure, false, true, true, false, 12)
DIAG(err_asm_unknown_symbolic_operand_name, CLASS_ERROR, (unsigned)diag::Severity::<PERSON>rror, "unknown symbolic operand name in inline assembly string", 0, SFINAE_SubstitutionFailure, false, true, true, false, 12)
DIAG(err_asm_unterminated_symbolic_operand_name, CLASS_ERROR, (unsigned)diag::Severity::Error, "unterminated symbolic operand name in inline assembly string", 0, SFINAE_SubstitutionFailure, false, true, true, false, 12)
DIAG(err_experimental_clang_interp_failed, CLASS_ERROR, (unsigned)diag::Severity::Error, "the experimental clang interpreter failed to evaluate an expression", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_module_odr_violation_definition_data, CLASS_ERROR, (unsigned)diag::Severity::Error, "%q0 has different definitions in different modules; first difference is %select{definition in module '%2'|defined here}1 found %select{%4 base %plural{1:class|:classes}4|%4 virtual base %plural{1:class|:classes}4|%ordinal4 base class with type %5|%ordinal4 %select{non-virtual|virtual}5 base class %6|%ordinal4 base class %5 with %select{public|protected|private|no}6 access specifier}3", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_module_odr_violation_different_definitions, CLASS_ERROR, (unsigned)diag::Severity::Error, "%q0 has different definitions in different modules; %select{definition in module '%2' is here|defined here}1", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_module_odr_violation_enum, CLASS_ERROR, (unsigned)diag::Severity::Error, "%q0 has different definitions in different modules; %select{definition in module '%2'|defined here}1 first difference is %select{enum that is %select{not scoped|scoped}4|enum scoped with keyword %select{struct|class}4|enum %select{without|with}4 specified type|enum with specified type %4|enum with %4 element%s4|%ordinal4 element has name %5|%ordinal4 element %5 %select{has|does not have}6 an initializer|%ordinal4 element %5 has an initializer|}3", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_module_odr_violation_field, CLASS_ERROR, (unsigned)diag::Severity::Error, "%q0 has different definitions in different modules; first difference is %select{definition in module '%2'|defined here}1 found %select{field %4|field %4 with type %5|%select{non-|}5bitfield %4|bitfield %4 with one width expression|%select{non-|}5mutable field %4|field %4 with %select{no|an}5 initializer|field %4 with an initializer}3", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_module_odr_violation_function, CLASS_ERROR, (unsigned)diag::Severity::Error, "%q0 has different definitions in different modules; %select{definition in module '%2'|defined here}1 first difference is %select{return type is %4|%ordinal4 parameter with name %5|%ordinal4 parameter with type %5%select{| decayed from %7}6|%ordinal4 parameter with%select{out|}5 a default argument|%ordinal4 parameter with a default argument|function body}3", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_module_odr_violation_method_params, CLASS_ERROR, (unsigned)diag::Severity::Error, "%q0 has different definitions in different modules; first difference is %select{definition in module '%2'|defined here}1 found %select{%select{method %5|constructor|destructor}4 that has %6 parameter%s6|%select{method %5|constructor|destructor}4 with %ordinal6 parameter of type %7%select{| decayed from %9}8|%select{method %5|constructor|destructor}4 with %ordinal6 parameter named %7}3", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_module_odr_violation_mismatch_decl, CLASS_ERROR, (unsigned)diag::Severity::Error, "%q0 has different definitions in different modules; first difference is %select{definition in module '%2'|defined here}1 found %select{end of class|public access specifier|private access specifier|protected access specifier|static assert|field|method|type alias|typedef|data member|friend declaration|function template|method|instance variable|property}3", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_module_odr_violation_mismatch_decl_unknown, CLASS_ERROR, (unsigned)diag::Severity::Error, "%q0 %select{with definition in module '%2'|defined here}1 has different definitions in different modules; first difference is this %select{||||static assert|field|method|type alias|typedef|data member|friend declaration|function template|method|instance variable|property|unexpected decl}3", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_module_odr_violation_objc_interface, CLASS_ERROR, (unsigned)diag::Severity::Error, "%0 has different definitions in different modules; first difference is %select{definition in module '%2'|defined here}1 found %select{%select{no super class|super class with type %5}4|instance variable '%4' access control is %select{|@private|@protected|@public|@package}5}3", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_module_odr_violation_objc_method, CLASS_ERROR, (unsigned)diag::Severity::Error, "%q0 has different definitions in different modules; first difference is %select{definition in module '%2'|defined here}1 found %select{method %4 with return type %5|%select{class|instance}5 method %4|%select{no|'required'|'optional'}4 method control|method %4 with %select{no designated initializer|designated initializer}5|%select{regular|direct}5 method %4|method %4}3", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_module_odr_violation_objc_property, CLASS_ERROR, (unsigned)diag::Severity::Error, "%q0 has different definitions in different modules; first difference is %select{definition in module '%2'|defined here}1 found %select{property %4|property %4 with type %5|%select{no|'required'|'optional'}4 property control|property %4 with %select{default |}6'%select{none|readonly|getter|assign|readwrite|retain|copy|nonatomic|setter|atomic|weak|strong|unsafe_unretained|nullability|null_resettable|class|direct}5' attribute}3", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_module_odr_violation_record, CLASS_ERROR, (unsigned)diag::Severity::Error, "%q0 has different definitions in different modules; first difference is %select{definition in module '%2'|defined here}1 found %select{static assert with condition|static assert with message|static assert with %select{|no }4message|%select{method %5|constructor|destructor}4|%select{method %5|constructor|destructor}4 is %select{not deleted|deleted}6|%select{method %5|constructor|destructor}4 is %select{not defaulted|defaulted}6|%select{method %5|constructor|destructor}4 is %select{|pure }6%select{not virtual|virtual}7|%select{method %5|constructor|destructor}4 is %select{not static|static}6|%select{method %5|constructor|destructor}4 is %select{not volatile|volatile}6|%select{method %5|constructor|destructor}4 is %select{not const|const}6|%select{method %5|constructor|destructor}4 is %select{not inline|inline}6|%select{method %5|constructor|destructor}4 with %ordinal6 parameter with%select{out|}7 a default argument|%select{method %5|constructor|destructor}4 with %ordinal6 parameter with a default argument|%select{method %5|constructor|destructor}4 with %select{no |}6template arguments|%select{method %5|constructor|destructor}4 with %6 template argument%s6|%select{method %5|constructor|destructor}4 with %6 for %ordinal7 template argument|%select{method %5|constructor|destructor}4 with %select{no body|body}6|%select{method %5|constructor|destructor}4 with body|friend %select{class|function}4|friend %4|friend function %4|function template %4 with %5 template parameter%s5|function template %4 with %ordinal5 template parameter being a %select{type|non-type|template}6 template parameter|function template %4 with %ordinal5 template parameter %select{with no name|named %7}6|function template %4 with %ordinal5 template parameter with %select{no |}6default argument|function template %4 with %ordinal5 template parameter with default argument %6|function template %4 with %ordinal5 template parameter with one type|function template %4 with %ordinal5 template parameter %select{not |}6being a template parameter pack|}3", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_module_odr_violation_referenced_protocols, CLASS_ERROR, (unsigned)diag::Severity::Error, "%q0 has different definitions in different modules; first difference is %select{definition in module '%2'|defined here}1 found %select{%4 referenced %plural{1:protocol|:protocols}4|%ordinal4 referenced protocol with name %5}3", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_module_odr_violation_template_parameter, CLASS_ERROR, (unsigned)diag::Severity::Error, "%q0 has different definitions in different modules; first difference is %select{definition in module '%2'|defined here}1 found %select{unnamed template parameter|template parameter %5|template parameter with %select{no |}4default argument|template parameter with default argument}3", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_module_odr_violation_typedef, CLASS_ERROR, (unsigned)diag::Severity::Error, "%q0 has different definitions in different modules; first difference is %select{definition in module '%2'|defined here}1 found %select{%select{typedef|type alias}4 name %5|%select{typedef|type alias}4 %5 with underlying type %6}3", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_module_odr_violation_variable, CLASS_ERROR, (unsigned)diag::Severity::Error, "%q0 has different definitions in different modules; first difference is %select{definition in module '%2'|defined here}1 found %select{data member with name %4|data member %4 with type %5|data member %4 with%select{out|}5 an initializer|data member %4 with an initializer|data member %4 %select{is constexpr|is not constexpr}5}3", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_odr_different_num_template_parameters, CLASS_ERROR, (unsigned)diag::Severity::Error, "template parameter lists have a different number of parameters (%0 vs %1)", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_odr_different_template_parameter_kind, CLASS_ERROR, (unsigned)diag::Severity::Error, "template parameter has different kinds in different translation units", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_odr_field_type_inconsistent, CLASS_ERROR, (unsigned)diag::Severity::Error, "field %0 declared with incompatible types in different translation units (%1 vs. %2)", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_odr_function_type_inconsistent, CLASS_ERROR, (unsigned)diag::Severity::Error, "external function %0 declared with incompatible types in different translation units (%1 vs. %2)", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_odr_ivar_type_inconsistent, CLASS_ERROR, (unsigned)diag::Severity::Error, "instance variable %0 declared with incompatible types in different translation units (%1 vs. %2)", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_odr_non_type_parameter_type_inconsistent, CLASS_ERROR, (unsigned)diag::Severity::Error, "non-type template parameter declared with incompatible types in different translation units (%0 vs. %1)", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_odr_objc_method_num_params_inconsistent, CLASS_ERROR, (unsigned)diag::Severity::Error, "%select{class|instance}0 method %1 has a different number of parameters in different translation units (%2 vs. %3)", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_odr_objc_method_param_type_inconsistent, CLASS_ERROR, (unsigned)diag::Severity::Error, "%select{class|instance}0 method %1 has a parameter with a different types in different translation units (%2 vs. %3)", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_odr_objc_method_result_type_inconsistent, CLASS_ERROR, (unsigned)diag::Severity::Error, "%select{class|instance}0 method %1 has incompatible result types in different translation units (%2 vs. %3)", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_odr_objc_method_variadic_inconsistent, CLASS_ERROR, (unsigned)diag::Severity::Error, "%select{class|instance}0 method %1 is variadic in one translation unit and not variadic in another", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_odr_objc_property_impl_kind_inconsistent, CLASS_ERROR, (unsigned)diag::Severity::Error, "property %0 is implemented with %select{@synthesize|@dynamic}1 in one translation but %select{@dynamic|@synthesize}1 in another translation unit", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_odr_objc_property_type_inconsistent, CLASS_ERROR, (unsigned)diag::Severity::Error, "property %0 declared with incompatible types in different translation units (%1 vs. %2)", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_odr_objc_superclass_inconsistent, CLASS_ERROR, (unsigned)diag::Severity::Error, "class %0 has incompatible superclasses", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_odr_objc_synthesize_ivar_inconsistent, CLASS_ERROR, (unsigned)diag::Severity::Error, "property %0 is synthesized to different ivars in different translation units (%1 vs. %2)", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_odr_parameter_pack_non_pack, CLASS_ERROR, (unsigned)diag::Severity::Error, "parameter kind mismatch; parameter is %select{not a|a}0 parameter pack", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_odr_tag_type_inconsistent, CLASS_ERROR, (unsigned)diag::Severity::Error, "type %0 has incompatible definitions in different translation units", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_odr_variable_multiple_def, CLASS_ERROR, (unsigned)diag::Severity::Error, "external variable %0 defined in multiple translation units", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_odr_variable_type_inconsistent, CLASS_ERROR, (unsigned)diag::Severity::Error, "external variable %0 declared with incompatible types in different translation units (%1 vs. %2)", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_unsupported_ast_node, CLASS_ERROR, (unsigned)diag::Severity::Error, "cannot import unsupported AST node %0", 0, SFINAE_SubstitutionFailure, false, true, true, false, 0)
DIAG(err_vftable_ambiguous_component, CLASS_ERROR, (unsigned)diag::Severity::Error, "ambiguous vftable component for %0 introduced via covariant thunks; this is an inherent limitation of the ABI", 0, SFINAE_SubstitutionFailure, false, true, true, false, 26)
DIAG(note_consteval_address_accessible, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "%select{pointer|reference}0 to a consteval declaration is not a constant expression", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_access_deleted_object, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "%select{read of|read of|assignment to|increment of|decrement of|member call on|dynamic_cast of|typeid applied to|construction of|destruction of}0 heap allocated object that has been deleted", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_access_inactive_union_member, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "%select{read of|read of|assignment to|increment of|decrement of|member call on|dynamic_cast of|typeid applied to|construction of subobject of|destruction of}0 member %1 of union with %select{active member %3|no active member}2 is not allowed in a constant expression", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_access_mutable, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "%select{read of|read of|assignment to|increment of|decrement of|member call on|dynamic_cast of|typeid applied to|construction of|destruction of}0 mutable member %1 is not allowed in a constant expression", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_access_null, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "%select{read of|read of|assignment to|increment of|decrement of|member call on|dynamic_cast of|typeid applied to|construction of|destruction of}0 dereferenced null pointer is not allowed in a constant expression", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_access_past_end, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "%select{read of|read of|assignment to|increment of|decrement of|member call on|dynamic_cast of|typeid applied to|construction of|destruction of}0 dereferenced one-past-the-end pointer is not allowed in a constant expression", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_access_static_temporary, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "%select{read of|read of|assignment to|increment of|decrement of|member call on|dynamic_cast of|typeid applied to|reconstruction of|destruction of}0 temporary is not allowed in a constant expression outside the expression that created the temporary", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_access_uninit, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "%select{read of|read of|assignment to|increment of|decrement of|member call on|dynamic_cast of|typeid applied to|construction of subobject of|destruction of}0 %select{object outside its lifetime|uninitialized object}1 is not allowed in a constant expression", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_access_unreadable_object, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "%select{read of|read of|assignment to|increment of|decrement of|member call on|dynamic_cast of|typeid applied to|construction of|destruction of}0 object '%1' whose value is not known", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_access_unsized_array, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "%select{read of|read of|assignment to|increment of|decrement of|member call on|dynamic_cast of|typeid applied to|construction of|destruction of}0 element of array without known bound is not allowed in a constant expression", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_access_volatile_obj, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "%select{read of|read of|assignment to|increment of|decrement of|<ERROR>|<ERROR>|<ERROR>|<ERROR>}0 volatile %select{temporary|object %2|member %2}1 is not allowed in a constant expression", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_access_volatile_type, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "%select{read of|read of|assignment to|increment of|decrement of|<ERROR>|<ERROR>|<ERROR>|<ERROR>}0 volatile-qualified type %1 is not allowed in a constant expression", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_alignment_adjust, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "cannot constant evaluate the result of adjusting alignment to %0", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_alignment_compute, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "cannot constant evaluate whether run-time alignment is at least %0", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_alignment_too_big, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "requested alignment must be %0 or less for type %1; %2 is invalid", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_array_index, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "cannot refer to element %0 of %select{array of %2 element%plural{1:|:s}2|non-array object}1 in a constant expression", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_baa_insufficient_alignment, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "%select{alignment of|offset of the aligned pointer from}0 the base pointee object (%1 %plural{1:byte|:bytes}1) is %select{less than|not a multiple of}0 the asserted %2 %plural{1:byte|:bytes}2", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_baa_value_insufficient_alignment, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "value of the aligned pointer (%0) is not a multiple of the asserted %1 %plural{1:byte|:bytes}1", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_bit_cast_indet_dest, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "indeterminate value can only initialize an object of type 'unsigned char'%select{, 'char',|}1 or 'std::byte'; %0 is invalid", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_bit_cast_invalid_subtype, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "invalid type %0 is a %select{member|base}1 of %2", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_bit_cast_invalid_type, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "bit_cast %select{from|to}0 a %select{|type with a }1%select{union|pointer|member pointer|volatile|reference}2 %select{type|member}1 is not allowed in a constant expression", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_bit_cast_unrepresentable_value, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "value %1 cannot be represented in type %0", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_bit_cast_unsupported_bitfield, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "constexpr bit_cast involving bit-field is not yet supported", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_bit_cast_unsupported_type, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "constexpr bit_cast involving type %0 is not yet supported", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_call_here, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "in call to '%0'", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_call_limit_exceeded, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "constexpr evaluation hit maximum call limit", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_calls_suppressed, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "(skipping %0 call%s0 in backtrace; use -fconstexpr-backtrace-limit=0 to see all)", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_compare_virtual_mem_ptr, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "comparison of pointer to virtual member function %0 has unspecified value", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_conditional_never_const, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "both arms of conditional operator are unable to produce a constant expression", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_construct_complex_elem, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "construction of individual component of complex number is not yet supported in constant expressions", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_deallocate_null, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "'std::allocator<...>::deallocate' used to delete a null pointer", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_delete_base_nonvirt_dtor, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "delete of object with dynamic type %1 through pointer to base class type %0 with non-virtual destructor", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_delete_not_heap_alloc, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "delete of pointer '%0' that does not point to a heap-allocated object", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_delete_subobject, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "delete of pointer%select{ to subobject|}1 '%0' %select{|that does not point to complete object}1", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_depth_limit_exceeded, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "constexpr evaluation exceeded maximum depth of %0 calls", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_destroy_complex_elem, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "destruction of individual component of complex number is not yet supported in constant expressions", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_destroy_out_of_lifetime, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "destroying object '%0' whose lifetime has already ended", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_double_delete, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "delete of pointer that has already been deleted", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_double_destroy, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "destruction of object that is already being destroyed", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_dynamic_alloc, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "%select{pointer|reference}0 to %select{|subobject of }1heap-allocated object is not a constant expression", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_dynamic_alloc_here, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "heap allocation performed here", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_dynamic_cast_to_reference_failed, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "reference dynamic_cast failed: %select{static type %1 of operand is a non-public base class of dynamic type %2|dynamic type %2 of operand does not have a base class of type %3|%3 is an ambiguous base class of dynamic type %2 of operand|%3 is a non-public base class of dynamic type %2 of operand}0", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_dynamic_rounding, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "cannot evaluate this expression if rounding mode is dynamic", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_float_arithmetic, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "floating point arithmetic produces %select{an infinity|a NaN}0", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_float_arithmetic_strict, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "compile time floating point arithmetic suppressed in strict evaluation modes", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_function_param_value_unknown, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "function parameter %0 with unknown value cannot be used in a constant expression", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_heap_alloc_limit_exceeded, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "constexpr evaluation hit maximum heap allocation limit", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_inherited_ctor_call_here, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "in implicit initialization for inherited constructor of %0", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_invalid_alignment, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "requested alignment %0 is not a positive power of two", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_invalid_cast, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "%select{reinterpret_cast|dynamic_cast|%select{this conversion|cast that performs the conversions of a reinterpret_cast}1|cast from %1}0 is not allowed in a constant expression%select{| in C++ standards before C++20||}0", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_invalid_downcast, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "cannot cast object of dynamic type %0 to type %1", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_invalid_function, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "%select{non-constexpr|undefined}0 %select{function|constructor}1 %2 cannot be used in a constant expression", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_invalid_inhctor, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "constructor inherited from base class %0 cannot be used in a constant expression; derived class cannot be implicitly initialized", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_invalid_void_star_cast, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "cast from %0 is not allowed in a constant expression %select{in C++ standards before C++2c|because the pointed object type %2 is not similar to the target type %3}1", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_large_shift, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "shift count %0 >= width of type %1 (%2 bit%s2)", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_lifetime_ended, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "%select{read of|read of|assignment to|increment of|decrement of|member call on|dynamic_cast of|typeid applied to|construction of|destruction of}0 %select{temporary|variable}1 whose %plural{8:storage duration|:lifetime}0 has ended", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_literal_comparison, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "comparison of addresses of literals has unspecified value", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_lshift_discards, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "signed left shift discards bits", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_lshift_of_negative, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "left shift of negative value %0", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_ltor_incomplete_type, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "read of incomplete type %0 is not allowed in a constant expression", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_ltor_non_const_int, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "read of non-const variable %0 is not allowed in a constant expression", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_ltor_non_constexpr, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "read of non-constexpr variable %0 is not allowed in a constant expression", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_ltor_non_integral, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "read of variable %0 of non-integral, non-enumeration type %1 is not allowed in a constant expression", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_mem_pointer_weak_comparison, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "comparison against pointer to weak member %q0 can only be performed at runtime", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_memchr_unsupported, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "constant evaluation of %0 on array of type %1 is not supported; only arrays of narrow character types can be searched", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_memcmp_unsupported, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "constant evaluation of %0 between arrays of types %1 and %2 is not supported; only arrays of narrow character types can be compared", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_memcpy_incomplete_type, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "cannot constant evaluate '%select{memcpy|memmove}0' between objects of incomplete type %1", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_memcpy_nontrivial, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "cannot constant evaluate '%select{memcpy|memmove}0' between objects of non-trivially-copyable type %1", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_memcpy_null, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "%select{source|destination}2 of '%select{%select{memcpy|wmemcpy}1|%select{memmove|wmemmove}1}0' is %3", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_memcpy_overlap, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "'%select{memcpy|wmemcpy}0' between overlapping memory regions", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_memcpy_type_pun, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "cannot constant evaluate '%select{memcpy|memmove}0' from object of type %1 to object of type %2", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_memcpy_unsupported, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "'%select{%select{memcpy|wmemcpy}1|%select{memmove|wmemmove}1}0' not supported: %select{size to copy (%4) is not a multiple of size of element type %3 (%5)|source is not a contiguous array of at least %4 elements of type %3|destination is not a contiguous array of at least %4 elements of type %3}2", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_memory_leak, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "allocation performed here was not deallocated%plural{0:|: (along with %0 other memory leak%s0)}0", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_modify_const_type, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "modification of object of const-qualified type %0 is not allowed in a constant expression", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_modify_global, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "a constant expression cannot modify an object that is visible outside that expression", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_negative_shift, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "negative shift count %0", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_new, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "dynamic memory allocation is not permitted in constant expressions until C++20", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_new_delete_mismatch, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "%plural{2:'delete' used to delete pointer to object allocated with 'std::allocator<...>::allocate'|:%select{non-array delete|array delete|'std::allocator<...>::deallocate'}0 used to delete pointer to %select{array object of type %2|non-array object of type %2|object allocated with 'new'}0}1", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_new_negative, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "cannot allocate array; evaluated array bound %0 is negative", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_new_non_replaceable, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "call to %select{placement|class-specific}0 %1", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_new_not_complete_object_type, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "cannot allocate memory of %select{incomplete|function}0 type %1", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_new_placement, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "this placement new expression is not yet supported in constant expressions", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_new_too_large, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "cannot allocate array; evaluated array bound %0 is too large", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_new_too_small, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "cannot allocate array; evaluated array bound %0 is too small to hold %1 explicitly initialized elements", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_new_untyped, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "cannot allocate untyped memory in a constant expression; use 'std::allocator<T>::allocate' to allocate memory of type 'T'", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_no_return, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "control reached end of constexpr function", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_non_global, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "%select{pointer|reference}0 to %select{|subobject of }1%select{temporary|%3}2 is not a constant expression", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_nonliteral, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "non-literal type %0 cannot be used in a constant expression", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_not_static, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "address of non-static constexpr variable %0 may differ on each invocation of the enclosing function; add 'static' to give it a constant address", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_null_callee, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "'%0' evaluates to a null function pointer", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_null_subobject, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "cannot %select{access base class of|access derived class of|access field of|access array element of|perform pointer arithmetic on|access real component of|access imaginary component of}0 null pointer", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_operator_new_bad_size, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "allocated size %0 is not a multiple of size %1 of element type %2", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_overflow, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "value %0 is outside the range of representable values of type %1", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_past_end, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "dereferenced pointer past the end of %select{|subobject of }0%select{temporary|%2}1 is not a constant expression", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_past_end_subobject, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "cannot %select{access base class of|access derived class of|access field of|access array element of|ERROR|access real component of|access imaginary component of}0 pointer past the end of object", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_placement_new_wrong_type, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "placement new would change type of storage from %0 to %1", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_pointer_comparison_base_classes, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "comparison of addresses of subobjects of different base classes has unspecified value", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_pointer_comparison_base_field, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "comparison of address of base class subobject %0 of class %1 to field %2 has unspecified value", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_pointer_comparison_differing_access, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "comparison of address of fields %0 and %2 of %4 with differing access specifiers (%1 vs %3) has unspecified value", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_pointer_comparison_past_end, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "comparison against pointer '%0' that points past the end of a complete object has unspecified value", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_pointer_comparison_unspecified, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "comparison between '%0' and '%1' has unspecified value", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_pointer_comparison_zero_sized, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "comparison of pointers '%0' and '%1' to unrelated zero-sized objects", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_pointer_constant_comparison, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "comparison of numeric address '%0' with pointer '%1' can only be performed at runtime", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_pointer_subtraction_not_same_array, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "subtracted pointers are not elements of the same array", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_pointer_subtraction_zero_size, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "subtraction of pointers to type %0 of zero size", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_pointer_weak_comparison, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "comparison against address of weak declaration '%0' can only be performed at runtime", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_polymorphic_unknown_dynamic_type, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "%select{|||||virtual function called on|dynamic_cast applied to|typeid applied to|construction of|destruction of}0 object '%1' whose dynamic type is not constant", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_pseudo_destructor, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "pseudo-destructor call is not permitted in constant expressions until C++20", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_pure_virtual_call, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "pure virtual function %q0 called", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_static_local, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "control flows through the definition of a %select{static|thread_local}0 variable", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_step_limit_exceeded, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "constexpr evaluation hit maximum step limit; possible infinite loop?", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_stmt_expr_unsupported, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "this use of statement expressions is not supported in a constant expression", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_subobject_declared_here, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "subobject declared here", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_temporary_here, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "temporary created here", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_this, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "%select{|implicit }0use of 'this' pointer is only allowed within the evaluation of a call to a 'constexpr' member function", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_typeid_polymorphic, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "typeid applied to expression of polymorphic type %0 is not allowed in a constant expression in C++ standards before C++20", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_uninitialized, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "subobject %select{of type |}0%1 is not initialized", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_uninitialized_base, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "constructor of base class %0 is not called", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_union_member_change_during_init, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "assignment would change active union member during the initialization of a different member of the same union", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_unsized_array_indexed, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "indexing of array without known bound is not allowed in a constant expression", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_unsupported_destruction, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "non-trivial destruction of type %0 in a constant expression is not supported", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_unsupported_flexible_array, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "flexible array initialization is not yet supported", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_unsupported_layout, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "type %0 has unexpected layout", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_unsupported_temporary_nontrivial_dtor, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "non-trivial destruction of lifetime-extended temporary with type %0 used in the result of a constant expression is not yet supported", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_unsupported_unsized_array, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "array-to-pointer decay of array member without known bound is not supported", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_use_uninit_reference, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "use of reference outside its lifetime is not allowed in a constant expression", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_var_init_non_constant, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "initializer of %0 is not a constant expression", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_var_init_unknown, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "initializer of %0 is unknown", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_var_init_weak, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "initializer of weak variable %0 is not considered constant because it may be different at runtime", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_virtual_base, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "cannot construct object of type %0 with virtual base class in a constant expression", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_virtual_call, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "cannot evaluate call to virtual function in a constant expression in C++ standards before C++20", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_void_comparison, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "comparison between unequal pointers to void has unspecified result", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_constexpr_volatile_here, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "volatile %select{temporary created|object declared|member declared}0 here", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_covariant_thunk, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "covariant thunk required by %0", 0, SFINAE_Suppress, false, false, true, false, 26)
DIAG(note_expr_divide_by_zero, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "division by zero", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_first_module_difference, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "in first definition, possible difference is here", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_module_odr_violation_definition_data, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "but in '%0' found %select{%2 base %plural{1:class|:classes}2|%2 virtual base %plural{1:class|:classes}2|%ordinal2 base class with different type %3|%ordinal2 %select{non-virtual|virtual}3 base class %4|%ordinal2 base class %3 with %select{public|protected|private|no}4 access specifier}1", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_module_odr_violation_different_definitions, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "definition in module '%0' is here", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_module_odr_violation_enum, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "but in '%0' found %select{enum that is %select{not scoped|scoped}2|enum scoped with keyword %select{struct|class}2|enum %select{without|with}2 specified type|enum with specified type %2|enum with %2 element%s2|%ordinal2 element has name %3|%ordinal2 element %3 %select{has|does not have}4 an initializer|%ordinal2 element %3 has different initializer|}1", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_module_odr_violation_field, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "but in %select{'%1'|definition here}0 found %select{field %3|field %3 with type %4|%select{non-|}4bitfield %3|bitfield %3 with different width expression|%select{non-|}4mutable field %3|field %3 with %select{no|an}4 initializer|field %3 with a different initializer}2", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_module_odr_violation_function, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "but in '%0' found %select{different return type %2|%ordinal2 parameter with name %3|%ordinal2 parameter with type %3%select{| decayed from %5}4|%ordinal2 parameter with%select{out|}3 a default argument|%ordinal2 parameter with a different default argument|a different body}1", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_module_odr_violation_method_params, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "but in %select{'%1'|definition here}0 found %select{%select{method %4|constructor|destructor}3 that has %5 parameter%s5|%select{method %4|constructor|destructor}3 with %ordinal5 parameter of type %6%select{| decayed from %8}7|%select{method %4|constructor|destructor}3 with %ordinal5 parameter named %6}2", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_module_odr_violation_mismatch_decl, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "but in %select{'%1'|definition here}0 found %select{end of class|public access specifier|private access specifier|protected access specifier|static assert|field|method|type alias|typedef|data member|friend declaration|function template|method|instance variable|property}2", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_module_odr_violation_mismatch_decl_unknown, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "but in %select{'%1'|definition here}0 found %select{||||different static assert|different field|different method|different type alias|different typedef|different data member|different friend declaration|different function template|different method|different instance variable|different property|another unexpected decl}2", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_module_odr_violation_objc_interface, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "but in %select{'%1'|definition here}0 found %select{%select{no super class|super class with type %4}3|instance variable '%3' access control is %select{|@private|@protected|@public|@package}4}2", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_module_odr_violation_objc_method, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "but in %select{'%1'|definition here}0 found %select{method %3 with different return type %4|method %3 as %select{class|instance}4 method|%select{no|'required'|'optional'}3 method control|method %3 with %select{no designated initializer|designated initializer}4|%select{regular|direct}4 method %3|different method %3}2", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_module_odr_violation_objc_property, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "but in %select{'%1'|definition here}0 found %select{property %3|property %3 with type %4|%select{no|'required'|'optional'}3 property control|property %3 with different '%select{none|readonly|getter|assign|readwrite|retain|copy|nonatomic|setter|atomic|weak|strong|unsafe_unretained|nullability|null_resettable|class|direct}4' attribute}2", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_module_odr_violation_record, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "but in '%0' found %select{static assert with different condition|static assert with different message|static assert with %select{|no }2message|%select{method %3|constructor|destructor}2|%select{method %3|constructor|destructor}2 is %select{not deleted|deleted}4|%select{method %3|constructor|destructor}2 is %select{not defaulted|defaulted}4|%select{method %3|constructor|destructor}2 is %select{|pure }4%select{not virtual|virtual}5|%select{method %3|constructor|destructor}2 is %select{not static|static}4|%select{method %3|constructor|destructor}2 is %select{not volatile|volatile}4|%select{method %3|constructor|destructor}2 is %select{not const|const}4|%select{method %3|constructor|destructor}2 is %select{not inline|inline}4|%select{method %3|constructor|destructor}2 with %ordinal4 parameter with%select{out|}5 a default argument|%select{method %3|constructor|destructor}2 with %ordinal4 parameter with a different default argument|%select{method %3|constructor|destructor}2 with %select{no |}4template arguments|%select{method %3|constructor|destructor}2 with %4 template argument%s4|%select{method %3|constructor|destructor}2 with %4 for %ordinal5 template argument|%select{method %3|constructor|destructor}2 with %select{no body|body}4|%select{method %3|constructor|destructor}2 with different body|friend %select{class|function}2|friend %2|friend function %2|function template %2 with %3 template parameter%s3|function template %2 with %ordinal3 template paramter being a %select{type|non-type|template}4 template parameter|function template %2 with %ordinal3 template parameter %select{with no name|named %5}4|function template %2 with %ordinal3 template parameter with %select{no |}4default argument|function template %2 with %ordinal3 template parameter with default argument %4|function template %2 with %ordinal3 template parameter with different type|function template %2 with %ordinal3 template parameter %select{not |}4being a template parameter pack|}1", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_module_odr_violation_referenced_protocols, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "but in %select{'%1'|definition here}0 found %select{%3 referenced %plural{1:protocol|:protocols}3|%ordinal3 referenced protocol with different name %4}2", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_module_odr_violation_template_parameter, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "but in '%0' found %select{unnamed template parameter %2|template parameter %3|template parameter with %select{no |}2default argument|template parameter with different default argument}1", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_module_odr_violation_typedef, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "but in '%0' found %select{%select{typedef|type alias}2 name %3|%select{typedef|type alias}2 %3 with different underlying type %4}1", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_module_odr_violation_variable, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "but in '%0' found %select{data member with name %2|data member %2 with different type %3|data member %2 with%select{out|}3 an initializer|data member %2 with a different initializer|data member %2 %select{is constexpr|is not constexpr}3}1", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_non_null_attribute_failed, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "null passed to a callee that requires a non-null argument", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_odr_base, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "class has base type %0", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_odr_enumerator, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "enumerator %0 with value %1 here", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_odr_field, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "field %0 has type %1 here", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_odr_field_name, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "field has name %0 here", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_odr_friend, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "friend declared here", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_odr_missing_base, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "no corresponding base class here", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_odr_missing_enumerator, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "no corresponding enumerator here", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_odr_missing_field, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "no corresponding field here", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_odr_missing_friend, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "no corresponding friend here", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_odr_number_of_bases, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "class has %0 base %plural{1:class|:classes}0", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_odr_objc_method_here, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "%select{class|instance}0 method %1 also declared here", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_odr_objc_missing_superclass, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "no corresponding superclass here", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_odr_objc_property_impl_kind, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "property %0 is implemented with %select{@synthesize|@dynamic}1 here", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_odr_objc_superclass, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "inherits from superclass %0 here", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_odr_objc_synthesize_ivar_here, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "property is synthesized to ivar %0 here", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_odr_parameter_pack_non_pack, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "%select{parameter|parameter pack}0 declared here", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_odr_tag_kind_here, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "%0 is a %select{struct|interface|union|class|enum}1 here", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_odr_template_parameter_here, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "template parameter declared here", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_odr_template_parameter_list, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "template parameter list also declared here", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_odr_value_here, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "declared here with type %0", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_odr_virtual_base, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "%select{non-virtual|virtual}0 derivation here", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_second_module_difference, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "in second definition, possible difference is here", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(note_unimplemented_constexpr_lambda_feature_ast, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "unimplemented constexpr lambda feature: %0 (coming soon!)", 0, SFINAE_Suppress, false, false, true, false, 0)
DIAG(remark_sanitize_address_insert_extra_padding_accepted, CLASS_REMARK, (unsigned)diag::Severity::Ignored, "-fsanitize-address-field-padding applied to %0", 758, SFINAE_Suppress, false, true, true, false, 0)
DIAG(remark_sanitize_address_insert_extra_padding_rejected, CLASS_REMARK, (unsigned)diag::Severity::Ignored, "-fsanitize-address-field-padding ignored for %0 because it %select{is not C++|is packed|is a union|is trivially copyable|has trivial destructor|is standard layout|is in a ignorelisted file|is ignorelisted}1", 758, SFINAE_Suppress, false, true, true, false, 0)
DIAG(warn_constexpr_unscoped_enum_out_of_range, CLASS_WARNING, (unsigned)diag::Severity::Error, "integer value %0 is outside the valid range of values [%1, %2] for the enumeration type %3", 285, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_fixedpoint_constant_overflow, CLASS_WARNING, (unsigned)diag::Severity::Warning, "overflow in expression; result is %0 with type %1", 308, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_integer_constant_overflow, CLASS_WARNING, (unsigned)diag::Severity::Warning, "overflow in expression; result is %0 with type %1", 444, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_is_constant_evaluated_always_true_constexpr, CLASS_WARNING, (unsigned)diag::Severity::Warning, "'%0' will always evaluate to 'true' in a manifestly constant-evaluated expression", 174, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_npot_ms_struct, CLASS_WARNING, (unsigned)diag::Severity::Error, "ms_struct may not produce Microsoft-compatible layouts with fundamental data types with sizes that aren't a power of two", 416, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_odr_different_num_template_parameters, CLASS_WARNING, (unsigned)diag::Severity::Warning, "template parameter lists have a different number of parameters (%0 vs %1)", 639, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_odr_different_template_parameter_kind, CLASS_WARNING, (unsigned)diag::Severity::Warning, "template parameter has different kinds in different translation units", 639, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_odr_field_type_inconsistent, CLASS_WARNING, (unsigned)diag::Severity::Warning, "field %0 declared with incompatible types in different translation units (%1 vs. %2)", 639, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_odr_function_type_inconsistent, CLASS_WARNING, (unsigned)diag::Severity::Warning, "external function %0 declared with incompatible types in different translation units (%1 vs. %2)", 639, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_odr_ivar_type_inconsistent, CLASS_WARNING, (unsigned)diag::Severity::Warning, "instance variable %0 declared with incompatible types in different translation units (%1 vs. %2)", 639, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_odr_non_type_parameter_type_inconsistent, CLASS_WARNING, (unsigned)diag::Severity::Warning, "non-type template parameter declared with incompatible types in different translation units (%0 vs. %1)", 639, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_odr_objc_method_num_params_inconsistent, CLASS_WARNING, (unsigned)diag::Severity::Warning, "%select{class|instance}0 method %1 has a different number of parameters in different translation units (%2 vs. %3)", 639, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_odr_objc_method_param_type_inconsistent, CLASS_WARNING, (unsigned)diag::Severity::Warning, "%select{class|instance}0 method %1 has a parameter with a different types in different translation units (%2 vs. %3)", 639, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_odr_objc_method_result_type_inconsistent, CLASS_WARNING, (unsigned)diag::Severity::Warning, "%select{class|instance}0 method %1 has incompatible result types in different translation units (%2 vs. %3)", 639, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_odr_objc_method_variadic_inconsistent, CLASS_WARNING, (unsigned)diag::Severity::Warning, "%select{class|instance}0 method %1 is variadic in one translation unit and not variadic in another", 639, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_odr_objc_property_impl_kind_inconsistent, CLASS_WARNING, (unsigned)diag::Severity::Warning, "property %0 is implemented with %select{@synthesize|@dynamic}1 in one translation but %select{@dynamic|@synthesize}1 in another translation unit", 639, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_odr_objc_property_type_inconsistent, CLASS_WARNING, (unsigned)diag::Severity::Warning, "property %0 declared with incompatible types in different translation units (%1 vs. %2)", 639, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_odr_objc_superclass_inconsistent, CLASS_WARNING, (unsigned)diag::Severity::Warning, "class %0 has incompatible superclasses", 639, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_odr_objc_synthesize_ivar_inconsistent, CLASS_WARNING, (unsigned)diag::Severity::Warning, "property %0 is synthesized to different ivars in different translation units (%1 vs. %2)", 639, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_odr_parameter_pack_non_pack, CLASS_WARNING, (unsigned)diag::Severity::Warning, "parameter kind mismatch; parameter is %select{not a|a}0 parameter pack", 639, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_odr_tag_type_inconsistent, CLASS_WARNING, (unsigned)diag::Severity::Warning, "type %0 has incompatible definitions in different translation units", 639, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_odr_variable_multiple_def, CLASS_WARNING, (unsigned)diag::Severity::Warning, "external variable %0 defined in multiple translation units", 639, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_odr_variable_type_inconsistent, CLASS_WARNING, (unsigned)diag::Severity::Warning, "external variable %0 declared with incompatible types in different translation units (%1 vs. %2)", 639, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_padded_struct_anon_field, CLASS_WARNING, (unsigned)diag::Severity::Ignored, "padding %select{struct|interface|class}0 %1 with %2 %select{byte|bit}3%s2 to align anonymous bit-field", 664, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_padded_struct_field, CLASS_WARNING, (unsigned)diag::Severity::Ignored, "padding %select{struct|interface|class}0 %1 with %2 %select{byte|bit}3%s2 to align %4", 664, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_padded_struct_size, CLASS_WARNING, (unsigned)diag::Severity::Ignored, "padding size of %0 with %1 %select{byte|bit}2%s1 to alignment boundary", 664, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_unaligned_access, CLASS_WARNING, (unsigned)diag::Severity::Ignored, "field %1 within %0 is less aligned than %2 and is usually due to %0 being packed, which can lead to unaligned accesses", 878, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_unnecessary_packed, CLASS_WARNING, (unsigned)diag::Severity::Ignored, "packed attribute is unnecessary for %0", 662, SFINAE_Suppress, false, false, true, false, 0)
DIAG(warn_unpacked_field, CLASS_WARNING, (unsigned)diag::Severity::Ignored, "not packing field %0 as it is non-POD for the purposes of layout", 663, SFINAE_Suppress, false, false, true, false, 0)
