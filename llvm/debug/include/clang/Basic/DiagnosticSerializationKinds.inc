#ifdef SERIALIZATIONSTART
__SERIALIZATIONSTART = DIAG_START_SERIALIZATION,
#undef SERIALIZATIONSTART
#endif

DIAG(err_ast_file_invalid, CLASS_ERROR, (unsigned)diag::Severity::Fatal, "file '%1' is not a valid precompiled %select{PCH|module|AST}0 file", 0, SFINAE_SubstitutionFailure, false, true, true, false, 13)
DIAG(err_ast_file_not_found, CLASS_ERROR, (unsigned)diag::Severity::Fatal, "%select{PCH|module|AST}0 file '%1' not found%select{|: %3}2", 0, SFINAE_SubstitutionFailure, false, true, true, false, 13)
DIAG(err_ast_file_out_of_date, CLASS_ERROR, (unsigned)diag::Severity::Fatal, "%select{PCH|module|AST}0 file '%1' is out of date and needs to be rebuilt%select{|: %3}2", 0, SFINAE_SubstitutionFailure, false, true, true, false, 13)
DIAG(err_fe_ast_file_modified, CLASS_ERROR, (unsigned)diag::Severity::Fatal, "file '%0' has been modified since the %select{precompiled header|module file|AST file}1 '%2' was built: %select{size|mtime|content}3 changed%select{| (was %5, now %6)}4", 0, SFINAE_SubstitutionFailure, false, true, true, false, 13)
DIAG(err_fe_not_a_pch_file, CLASS_ERROR, (unsigned)diag::Severity::Error, "input is not a PCH file: '%0'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 13)
DIAG(err_fe_pch_file_overridden, CLASS_ERROR, (unsigned)diag::Severity::Error, "file '%0' from the precompiled header has been overridden", 0, SFINAE_SubstitutionFailure, false, true, true, false, 13)
DIAG(err_fe_pch_malformed, CLASS_ERROR, (unsigned)diag::Severity::Fatal, "malformed or corrupted AST file: '%0'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 13)
DIAG(err_fe_pch_malformed_block, CLASS_ERROR, (unsigned)diag::Severity::Fatal, "malformed block record in PCH file: '%0'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 13)
DIAG(err_fe_unable_to_read_pch_file, CLASS_ERROR, (unsigned)diag::Severity::Error, "unable to read PCH file %0: '%1'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 13)
DIAG(err_imported_module_modmap_changed, CLASS_ERROR, (unsigned)diag::Severity::Fatal, "module '%0' %select{in|imported by}4 AST file '%1' found in a different module map file (%2) than when the importing AST file was built (%3)", 0, SFINAE_SubstitutionFailure, false, true, true, false, 13)
DIAG(err_imported_module_not_found, CLASS_ERROR, (unsigned)diag::Severity::Fatal, "module '%0' in AST file '%1' %select{(imported by AST file '%2') |}4is not defined in any loaded module map file; maybe you need to load '%3'?", 0, SFINAE_SubstitutionFailure, false, true, true, false, 13)
DIAG(err_imported_module_relocated, CLASS_ERROR, (unsigned)diag::Severity::Fatal, "module '%0' was built in directory '%1' but now resides in directory '%2'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 13)
DIAG(err_module_different_modmap, CLASS_ERROR, (unsigned)diag::Severity::Error, "module '%0' %select{uses|does not use}1 additional module map '%2'%select{| not}1 used when the module was built", 0, SFINAE_SubstitutionFailure, false, true, true, false, 13)
DIAG(err_module_file_conflict, CLASS_ERROR, (unsigned)diag::Severity::Fatal, "module '%0' is defined in both '%1' and '%2'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 13)
DIAG(err_module_file_missing_top_level_submodule, CLASS_ERROR, (unsigned)diag::Severity::Fatal, "module file '%0' is missing its top-level submodule", 0, SFINAE_SubstitutionFailure, false, true, true, false, 13)
DIAG(err_module_file_not_module, CLASS_ERROR, (unsigned)diag::Severity::Fatal, "AST file '%0' was not built as a module", 0, SFINAE_SubstitutionFailure, false, true, true, false, 13)
DIAG(err_module_no_size_mtime_for_header, CLASS_ERROR, (unsigned)diag::Severity::Error, "cannot emit module %0: %select{size|mtime}1 must be explicitly specified for missing header file \"%2\"", 0, SFINAE_SubstitutionFailure, false, true, true, false, 21)
DIAG(err_module_odr_violation_different_instantiations, CLASS_ERROR, (unsigned)diag::Severity::Error, "instantiation of %q0 is different in different modules", 0, SFINAE_SubstitutionFailure, false, true, true, false, 13)
DIAG(err_module_odr_violation_missing_decl, CLASS_ERROR, (unsigned)diag::Severity::Error, "%q0 from module '%1' is not present in definition of %q2%select{ in module '%4'| provided earlier}3", 0, SFINAE_Report, false, true, true, false, 13)
DIAG(err_module_unable_to_hash_content, CLASS_ERROR, (unsigned)diag::Severity::Error, "failed to hash content for '%0' because memory buffer cannot be retrieved", 0, SFINAE_SubstitutionFailure, false, true, true, false, 21)
DIAG(err_pch_diagopt_mismatch, CLASS_ERROR, (unsigned)diag::Severity::Error, "%0 is currently enabled, but was not in the PCH file", 0, SFINAE_SubstitutionFailure, false, true, true, false, 13)
DIAG(err_pch_different_branch, CLASS_ERROR, (unsigned)diag::Severity::Error, "PCH file built from a different branch (%0) than the compiler (%1)", 0, SFINAE_SubstitutionFailure, false, true, true, false, 13)
DIAG(err_pch_langopt_mismatch, CLASS_ERROR, (unsigned)diag::Severity::Error, "%0 was %select{disabled|enabled}1 in PCH file but is currently %select{disabled|enabled}2", 0, SFINAE_SubstitutionFailure, false, true, true, false, 13)
DIAG(err_pch_langopt_value_mismatch, CLASS_ERROR, (unsigned)diag::Severity::Error, "%0 differs in PCH file vs. current file", 0, SFINAE_SubstitutionFailure, false, true, true, false, 13)
DIAG(err_pch_macro_def_conflict, CLASS_ERROR, (unsigned)diag::Severity::Error, "definition of macro '%0' differs between the precompiled header ('%1') and the command line ('%2')", 0, SFINAE_SubstitutionFailure, false, true, true, false, 13)
DIAG(err_pch_macro_def_undef, CLASS_ERROR, (unsigned)diag::Severity::Error, "macro '%0' was %select{defined|undef'd}1 in the precompiled header but %select{undef'd|defined}1 on the command line", 0, SFINAE_SubstitutionFailure, false, true, true, false, 13)
DIAG(err_pch_modulecache_mismatch, CLASS_ERROR, (unsigned)diag::Severity::Error, "PCH was compiled with module cache path '%0', but the path is currently '%1'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 13)
DIAG(err_pch_pp_detailed_record, CLASS_ERROR, (unsigned)diag::Severity::Error, "%select{command line contains|precompiled header was built with}0 '-detailed-preprocessing-record' but %select{precompiled header was not built with it|it is not present on the command line}0", 0, SFINAE_SubstitutionFailure, false, true, true, false, 13)
DIAG(err_pch_targetopt_feature_mismatch, CLASS_ERROR, (unsigned)diag::Severity::Error, "%select{AST file was|current translation unit is}0 compiled with the target feature '%1' but the %select{current translation unit is|AST file was}0 not", 0, SFINAE_SubstitutionFailure, false, true, true, false, 13)
DIAG(err_pch_targetopt_mismatch, CLASS_ERROR, (unsigned)diag::Severity::Error, "PCH file was compiled for the %0 '%1' but the current translation unit is being compiled for target '%2'", 0, SFINAE_SubstitutionFailure, false, true, true, false, 13)
DIAG(err_pch_undef, CLASS_ERROR, (unsigned)diag::Severity::Error, "%select{command line contains|precompiled header was built with}0 '-undef' but %select{precompiled header was not built with it|it is not present on the command line}0", 0, SFINAE_SubstitutionFailure, false, true, true, false, 13)
DIAG(err_pch_version_too_new, CLASS_ERROR, (unsigned)diag::Severity::Error, "PCH file uses a newer PCH format that cannot be read", 0, SFINAE_SubstitutionFailure, false, true, true, false, 13)
DIAG(err_pch_version_too_old, CLASS_ERROR, (unsigned)diag::Severity::Error, "PCH file uses an older PCH format that is no longer supported", 0, SFINAE_SubstitutionFailure, false, true, true, false, 13)
DIAG(err_pch_with_compiler_errors, CLASS_ERROR, (unsigned)diag::Severity::Error, "PCH file contains compiler errors", 0, SFINAE_SubstitutionFailure, false, true, true, false, 13)
DIAG(note_imported_by_pch_module_not_found, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "consider adding '%0' to the header search path", 0, SFINAE_Suppress, false, false, true, false, 13)
DIAG(note_module_cache_path, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "after modifying system headers, please delete the module cache at '%0'", 0, SFINAE_Suppress, false, false, true, false, 13)
DIAG(note_module_file_conflict, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "this is generally caused by modules with the same name found in multiple paths", 0, SFINAE_Suppress, false, false, true, false, 13)
DIAG(note_module_file_imported_by, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "imported by %select{|module '%2' in }1'%0'", 0, SFINAE_Suppress, false, false, true, false, 13)
DIAG(note_module_odr_violation_no_possible_decls, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "definition has no member %0", 0, SFINAE_Suppress, false, false, true, false, 13)
DIAG(note_module_odr_violation_possible_decl, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "declaration of %0 does not match", 0, SFINAE_Suppress, false, false, true, false, 13)
DIAG(note_pch_rebuild_required, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "please rebuild precompiled header '%0'", 0, SFINAE_Suppress, false, false, true, false, 13)
DIAG(note_pch_required_by, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "'%0' required by '%1'", 0, SFINAE_Suppress, false, false, true, false, 13)
DIAG(remark_module_import, CLASS_REMARK, (unsigned)diag::Severity::Ignored, "importing module '%0'%select{| into '%3'}2 from '%1'", 550, SFINAE_Suppress, false, true, true, false, 13)
DIAG(warn_duplicate_module_file_extension, CLASS_WARNING, (unsigned)diag::Severity::Warning, "duplicate module file extension block name '%0'", 549, SFINAE_Suppress, false, false, true, false, 13)
DIAG(warn_eagerly_load_for_standard_cplusplus_modules, CLASS_WARNING, (unsigned)diag::Severity::Warning, "the form '-fmodule-file=<BMI-path>' is deprecated for standard C++ named modules;consider to use '-fmodule-file=<module-name>=<BMI-path>' instead", 271, SFINAE_Suppress, false, false, true, false, 13)
DIAG(warn_module_system_bit_conflict, CLASS_WARNING, (unsigned)diag::Severity::Warning, "module file '%0' was validated as a system module and is now being imported as a non-system module; any difference in diagnostic options will be ignored", 547, SFINAE_Suppress, false, false, true, false, 13)
DIAG(warn_module_uses_date_time, CLASS_WARNING, (unsigned)diag::Severity::Warning, "%select{precompiled header|module}0 uses __DATE__ or __TIME__", 672, SFINAE_Suppress, false, false, true, false, 21)
DIAG(warn_reading_std_cxx_module_by_implicit_paths, CLASS_WARNING, (unsigned)diag::Severity::Warning, "it is deprecated to read module '%0' implicitly; it is going to be removed in clang 18; consider to specify the dependencies explicitly", 726, SFINAE_Suppress, false, false, true, false, 13)
