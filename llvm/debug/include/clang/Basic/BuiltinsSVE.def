//===--- BuiltinsSVE.def - SVE Builtin function database --------*- C++ -*-===//
//
//  Part of the LLVM Project, under the Apache License v2.0 with LLVM Exceptions.
//  See https://llvm.org/LICENSE.txt for license information.
//  SPDX-License-Identifier: Apache-2.0 WITH LLVM-exception
//
//===----------------------------------------------------------------------===//
//
// This file defines the SVE-specific builtin function database.  Users of
// this file must define the BUILTIN macro to make use of this information.
//
//===----------------------------------------------------------------------===//

// The format of this database matches clang/Basic/Builtins.def.

#define GET_SVE_BUILTINS
#include "clang/Basic/arm_sve_builtins.inc"
#include "clang/Basic/BuiltinsAArch64NeonSVEBridge.def"
#undef GET_SVE_BUILTINS

#undef BUILTIN
#undef TARGET_BUILTIN
