/*===- TableGen'erated file -------------------------------------*- C++ -*-===*\
|*                                                                            *|
|* List of all attributes that <PERSON><PERSON> recognizes                               *|
|*                                                                            *|
|* Automatically generated file, do not edit!                                 *|
|*                                                                            *|
\*===----------------------------------------------------------------------===*/

#ifndef TYPE_ATTR
#define TYPE_ATTR(NAME) ATTR(NAME)
#endif

#ifndef STMT_ATTR
#define STMT_ATTR(NAME) ATTR(NAME)
#endif

#ifndef DECL_OR_STMT_ATTR
#define DECL_OR_STMT_ATTR(NAME) INHERITABLE_ATTR(NAME)
#endif

#ifndef INHERITABLE_ATTR
#define INHERITABLE_ATTR(NAME) ATTR(NAME)
#endif

#ifndef DECL_OR_TYPE_ATTR
#define DECL_OR_TYPE_ATTR(NAME) INHERITABLE_ATTR(NAME)
#endif

#ifndef INHERITABLE_PARAM_ATTR
#define INHERITABLE_PARAM_ATTR(NAME) INHERITABLE_ATTR(NAME)
#endif

#ifndef PARAMETER_ABI_ATTR
#define PARAMETER_ABI_ATTR(NAME) INHERITABLE_PARAM_ATTR(NAME)
#endif

#ifndef HLSL_ANNOTATION_ATTR
#define HLSL_ANNOTATION_ATTR(NAME) INHERITABLE_ATTR(NAME)
#endif

#ifndef PRAGMA_SPELLING_ATTR
#define PRAGMA_SPELLING_ATTR(NAME) 
#endif

TYPE_ATTR(AddressSpace)
TYPE_ATTR(AnnotateType)
TYPE_ATTR(ArmMveStrictPolymorphism)
TYPE_ATTR(ArmStreaming)
TYPE_ATTR(BTFTypeTag)
TYPE_ATTR(CmseNSCall)
TYPE_ATTR(HLSLGroupSharedAddressSpace)
TYPE_ATTR(NoDeref)
TYPE_ATTR(ObjCGC)
TYPE_ATTR(ObjCInertUnsafeUnretained)
TYPE_ATTR(ObjCKindOf)
TYPE_ATTR(OpenCLConstantAddressSpace)
TYPE_ATTR(OpenCLGenericAddressSpace)
TYPE_ATTR(OpenCLGlobalAddressSpace)
TYPE_ATTR(OpenCLGlobalDeviceAddressSpace)
TYPE_ATTR(OpenCLGlobalHostAddressSpace)
TYPE_ATTR(OpenCLLocalAddressSpace)
TYPE_ATTR(OpenCLPrivateAddressSpace)
TYPE_ATTR(Ptr32)
TYPE_ATTR(Ptr64)
TYPE_ATTR(SPtr)
TYPE_ATTR(TypeNonNull)
TYPE_ATTR(TypeNullUnspecified)
TYPE_ATTR(TypeNullable)
TYPE_ATTR(TypeNullableResult)
TYPE_ATTR(UPtr)
TYPE_ATTR(WebAssemblyFuncref)
STMT_ATTR(FallThrough)
STMT_ATTR(Likely)
STMT_ATTR(MustTail)
STMT_ATTR(OpenCLUnrollHint)
STMT_ATTR(Suppress)
STMT_ATTR(Unlikely)
DECL_OR_STMT_ATTR(AlwaysInline)
DECL_OR_STMT_ATTR(NoInline)
DECL_OR_STMT_ATTR(NoMerge)
DECL_OR_TYPE_ATTR(AArch64SVEPcs)
DECL_OR_TYPE_ATTR(AArch64VectorPcs)
DECL_OR_TYPE_ATTR(AMDGPUKernelCall)
DECL_OR_TYPE_ATTR(AcquireHandle)
DECL_OR_TYPE_ATTR(AnyX86NoCfCheck)
DECL_OR_TYPE_ATTR(CDecl)
DECL_OR_TYPE_ATTR(FastCall)
DECL_OR_TYPE_ATTR(IntelOclBicc)
DECL_OR_TYPE_ATTR(LifetimeBound)
DECL_OR_TYPE_ATTR(MSABI)
DECL_OR_TYPE_ATTR(NSReturnsRetained)
DECL_OR_TYPE_ATTR(ObjCOwnership)
DECL_OR_TYPE_ATTR(Pascal)
DECL_OR_TYPE_ATTR(Pcs)
DECL_OR_TYPE_ATTR(PreserveAll)
DECL_OR_TYPE_ATTR(PreserveMost)
DECL_OR_TYPE_ATTR(RegCall)
DECL_OR_TYPE_ATTR(StdCall)
DECL_OR_TYPE_ATTR(SwiftAsyncCall)
DECL_OR_TYPE_ATTR(SwiftCall)
DECL_OR_TYPE_ATTR(SysVABI)
DECL_OR_TYPE_ATTR(ThisCall)
DECL_OR_TYPE_ATTR(VectorCall)
PARAMETER_ABI_ATTR(SwiftAsyncContext)
PARAMETER_ABI_ATTR(SwiftContext)
PARAMETER_ABI_ATTR(SwiftErrorResult)
PARAMETER_ABI_ATTR(SwiftIndirectResult)
INHERITABLE_PARAM_ATTR(Annotate)
INHERITABLE_PARAM_ATTR(CFConsumed)
INHERITABLE_PARAM_ATTR(CarriesDependency)
INHERITABLE_PARAM_ATTR(NSConsumed)
INHERITABLE_PARAM_ATTR(NonNull)
INHERITABLE_PARAM_ATTR(OSConsumed)
INHERITABLE_PARAM_ATTR(PassObjectSize)
INHERITABLE_PARAM_ATTR(ReleaseHandle)
INHERITABLE_PARAM_ATTR(UseHandle)
HLSL_ANNOTATION_ATTR(HLSLSV_DispatchThreadID)
HLSL_ANNOTATION_ATTR(HLSLSV_GroupIndex)
INHERITABLE_ATTR(AMDGPUFlatWorkGroupSize)
INHERITABLE_ATTR(AMDGPUNumSGPR)
INHERITABLE_ATTR(AMDGPUNumVGPR)
INHERITABLE_ATTR(AMDGPUWavesPerEU)
INHERITABLE_ATTR(ARMInterrupt)
INHERITABLE_ATTR(AVRInterrupt)
INHERITABLE_ATTR(AVRSignal)
INHERITABLE_ATTR(AcquireCapability)
INHERITABLE_ATTR(AcquiredAfter)
INHERITABLE_ATTR(AcquiredBefore)
INHERITABLE_ATTR(AlignMac68k)
INHERITABLE_ATTR(AlignNatural)
INHERITABLE_ATTR(Aligned)
INHERITABLE_ATTR(AllocAlign)
INHERITABLE_ATTR(AllocSize)
INHERITABLE_ATTR(AlwaysDestroy)
INHERITABLE_ATTR(AnalyzerNoReturn)
INHERITABLE_ATTR(AnyX86Interrupt)
INHERITABLE_ATTR(AnyX86NoCallerSavedRegisters)
INHERITABLE_ATTR(ArcWeakrefUnavailable)
INHERITABLE_ATTR(ArgumentWithTypeTag)
INHERITABLE_ATTR(ArmBuiltinAlias)
INHERITABLE_ATTR(Artificial)
INHERITABLE_ATTR(AsmLabel)
INHERITABLE_ATTR(AssertCapability)
INHERITABLE_ATTR(AssertExclusiveLock)
INHERITABLE_ATTR(AssertSharedLock)
INHERITABLE_ATTR(AssumeAligned)
INHERITABLE_ATTR(Assumption)
INHERITABLE_ATTR(Availability)
INHERITABLE_ATTR(AvailableOnlyInDefaultEvalMethod)
INHERITABLE_ATTR(BPFPreserveAccessIndex)
INHERITABLE_ATTR(BTFDeclTag)
INHERITABLE_ATTR(Blocks)
INHERITABLE_ATTR(Builtin)
INHERITABLE_ATTR(C11NoReturn)
INHERITABLE_ATTR(CFAuditedTransfer)
INHERITABLE_ATTR(CFGuard)
INHERITABLE_ATTR(CFICanonicalJumpTable)
INHERITABLE_ATTR(CFReturnsNotRetained)
INHERITABLE_ATTR(CFReturnsRetained)
INHERITABLE_ATTR(CFUnknownTransfer)
INHERITABLE_ATTR(CPUDispatch)
INHERITABLE_ATTR(CPUSpecific)
INHERITABLE_ATTR(CUDAConstant)
INHERITABLE_ATTR(CUDADevice)
INHERITABLE_ATTR(CUDADeviceBuiltinSurfaceType)
INHERITABLE_ATTR(CUDADeviceBuiltinTextureType)
INHERITABLE_ATTR(CUDAGlobal)
INHERITABLE_ATTR(CUDAHost)
INHERITABLE_ATTR(CUDAInvalidTarget)
INHERITABLE_ATTR(CUDALaunchBounds)
INHERITABLE_ATTR(CUDAShared)
INHERITABLE_ATTR(CXX11NoReturn)
INHERITABLE_ATTR(CallableWhen)
INHERITABLE_ATTR(Callback)
INHERITABLE_ATTR(Capability)
INHERITABLE_ATTR(CapturedRecord)
INHERITABLE_ATTR(Cleanup)
INHERITABLE_ATTR(CmseNSEntry)
INHERITABLE_ATTR(CodeSeg)
INHERITABLE_ATTR(Cold)
INHERITABLE_ATTR(Common)
INHERITABLE_ATTR(Const)
INHERITABLE_ATTR(ConstInit)
INHERITABLE_ATTR(Constructor)
INHERITABLE_ATTR(Consumable)
INHERITABLE_ATTR(ConsumableAutoCast)
INHERITABLE_ATTR(ConsumableSetOnRead)
INHERITABLE_ATTR(Convergent)
INHERITABLE_ATTR(DLLExport)
INHERITABLE_ATTR(DLLExportStaticLocal)
INHERITABLE_ATTR(DLLImport)
INHERITABLE_ATTR(DLLImportStaticLocal)
INHERITABLE_ATTR(Deprecated)
INHERITABLE_ATTR(Destructor)
INHERITABLE_ATTR(DiagnoseAsBuiltin)
INHERITABLE_ATTR(DiagnoseIf)
INHERITABLE_ATTR(DisableSanitizerInstrumentation)
INHERITABLE_ATTR(DisableTailCalls)
INHERITABLE_ATTR(EmptyBases)
INHERITABLE_ATTR(EnableIf)
INHERITABLE_ATTR(EnforceTCB)
INHERITABLE_ATTR(EnforceTCBLeaf)
INHERITABLE_ATTR(EnumExtensibility)
INHERITABLE_ATTR(Error)
INHERITABLE_ATTR(ExcludeFromExplicitInstantiation)
INHERITABLE_ATTR(ExclusiveTrylockFunction)
INHERITABLE_ATTR(ExternalSourceSymbol)
INHERITABLE_ATTR(Final)
INHERITABLE_ATTR(FlagEnum)
INHERITABLE_ATTR(Flatten)
INHERITABLE_ATTR(Format)
INHERITABLE_ATTR(FormatArg)
INHERITABLE_ATTR(FunctionReturnThunks)
INHERITABLE_ATTR(GNUInline)
INHERITABLE_ATTR(GuardedBy)
INHERITABLE_ATTR(GuardedVar)
INHERITABLE_ATTR(HIPManaged)
INHERITABLE_ATTR(HLSLNumThreads)
INHERITABLE_ATTR(HLSLResource)
INHERITABLE_ATTR(HLSLResourceBinding)
INHERITABLE_ATTR(HLSLShader)
INHERITABLE_ATTR(Hot)
INHERITABLE_ATTR(IBAction)
INHERITABLE_ATTR(IBOutlet)
INHERITABLE_ATTR(IBOutletCollection)
INHERITABLE_ATTR(InitPriority)
INHERITABLE_ATTR(InternalLinkage)
INHERITABLE_ATTR(LTOVisibilityPublic)
INHERITABLE_ATTR(LayoutVersion)
INHERITABLE_ATTR(Leaf)
INHERITABLE_ATTR(LockReturned)
INHERITABLE_ATTR(LocksExcluded)
INHERITABLE_ATTR(M68kInterrupt)
INHERITABLE_ATTR(MIGServerRoutine)
INHERITABLE_ATTR(MSAllocator)
INHERITABLE_ATTR(MSInheritance)
INHERITABLE_ATTR(MSNoVTable)
INHERITABLE_ATTR(MSP430Interrupt)
INHERITABLE_ATTR(MSStruct)
INHERITABLE_ATTR(MSVtorDisp)
INHERITABLE_ATTR(MaxFieldAlignment)
INHERITABLE_ATTR(MayAlias)
INHERITABLE_ATTR(MaybeUndef)
INHERITABLE_ATTR(MicroMips)
INHERITABLE_ATTR(MinSize)
INHERITABLE_ATTR(MinVectorWidth)
INHERITABLE_ATTR(Mips16)
INHERITABLE_ATTR(MipsInterrupt)
INHERITABLE_ATTR(MipsLongCall)
INHERITABLE_ATTR(MipsShortCall)
INHERITABLE_ATTR(NSConsumesSelf)
INHERITABLE_ATTR(NSErrorDomain)
INHERITABLE_ATTR(NSReturnsAutoreleased)
INHERITABLE_ATTR(NSReturnsNotRetained)
INHERITABLE_ATTR(NVPTXKernel)
INHERITABLE_ATTR(Naked)
INHERITABLE_ATTR(NoAlias)
INHERITABLE_ATTR(NoCommon)
INHERITABLE_ATTR(NoDebug)
INHERITABLE_ATTR(NoDestroy)
INHERITABLE_ATTR(NoDuplicate)
INHERITABLE_ATTR(NoInstrumentFunction)
INHERITABLE_ATTR(NoMicroMips)
INHERITABLE_ATTR(NoMips16)
INHERITABLE_ATTR(NoProfileFunction)
INHERITABLE_ATTR(NoRandomizeLayout)
INHERITABLE_ATTR(NoReturn)
INHERITABLE_ATTR(NoSanitize)
INHERITABLE_ATTR(NoSpeculativeLoadHardening)
INHERITABLE_ATTR(NoSplitStack)
INHERITABLE_ATTR(NoStackProtector)
INHERITABLE_ATTR(NoThreadSafetyAnalysis)
INHERITABLE_ATTR(NoThrow)
INHERITABLE_ATTR(NoUniqueAddress)
INHERITABLE_ATTR(NoUwtable)
INHERITABLE_ATTR(NotTailCalled)
INHERITABLE_ATTR(OMPAllocateDecl)
INHERITABLE_ATTR(OMPCaptureNoInit)
INHERITABLE_ATTR(OMPDeclareTargetDecl)
INHERITABLE_ATTR(OMPDeclareVariant)
INHERITABLE_ATTR(OMPThreadPrivateDecl)
INHERITABLE_ATTR(OSConsumesThis)
INHERITABLE_ATTR(OSReturnsNotRetained)
INHERITABLE_ATTR(OSReturnsRetained)
INHERITABLE_ATTR(OSReturnsRetainedOnNonZero)
INHERITABLE_ATTR(OSReturnsRetainedOnZero)
INHERITABLE_ATTR(ObjCBridge)
INHERITABLE_ATTR(ObjCBridgeMutable)
INHERITABLE_ATTR(ObjCBridgeRelated)
INHERITABLE_ATTR(ObjCException)
INHERITABLE_ATTR(ObjCExplicitProtocolImpl)
INHERITABLE_ATTR(ObjCExternallyRetained)
INHERITABLE_ATTR(ObjCIndependentClass)
INHERITABLE_ATTR(ObjCMethodFamily)
INHERITABLE_ATTR(ObjCNSObject)
INHERITABLE_ATTR(ObjCPreciseLifetime)
INHERITABLE_ATTR(ObjCRequiresPropertyDefs)
INHERITABLE_ATTR(ObjCRequiresSuper)
INHERITABLE_ATTR(ObjCReturnsInnerPointer)
INHERITABLE_ATTR(ObjCRootClass)
INHERITABLE_ATTR(ObjCSubclassingRestricted)
INHERITABLE_ATTR(OpenCLIntelReqdSubGroupSize)
INHERITABLE_ATTR(OpenCLKernel)
INHERITABLE_ATTR(OptimizeNone)
INHERITABLE_ATTR(Override)
INHERITABLE_ATTR(Owner)
INHERITABLE_ATTR(Ownership)
INHERITABLE_ATTR(Packed)
INHERITABLE_ATTR(ParamTypestate)
INHERITABLE_ATTR(PatchableFunctionEntry)
INHERITABLE_ATTR(Pointer)
INHERITABLE_ATTR(PragmaClangBSSSection)
INHERITABLE_ATTR(PragmaClangDataSection)
INHERITABLE_ATTR(PragmaClangRelroSection)
INHERITABLE_ATTR(PragmaClangRodataSection)
INHERITABLE_ATTR(PragmaClangTextSection)
INHERITABLE_ATTR(PreferredName)
INHERITABLE_ATTR(PtGuardedBy)
INHERITABLE_ATTR(PtGuardedVar)
INHERITABLE_ATTR(Pure)
INHERITABLE_ATTR(RISCVInterrupt)
INHERITABLE_ATTR(RandomizeLayout)
INHERITABLE_ATTR(ReadOnlyPlacement)
INHERITABLE_ATTR(Reinitializes)
INHERITABLE_ATTR(ReleaseCapability)
INHERITABLE_ATTR(ReqdWorkGroupSize)
INHERITABLE_ATTR(RequiresCapability)
INHERITABLE_ATTR(Restrict)
INHERITABLE_ATTR(Retain)
INHERITABLE_ATTR(ReturnTypestate)
INHERITABLE_ATTR(ReturnsNonNull)
INHERITABLE_ATTR(ReturnsTwice)
INHERITABLE_ATTR(SYCLKernel)
INHERITABLE_ATTR(SYCLSpecialClass)
INHERITABLE_ATTR(ScopedLockable)
INHERITABLE_ATTR(Section)
INHERITABLE_ATTR(SelectAny)
INHERITABLE_ATTR(Sentinel)
INHERITABLE_ATTR(SetTypestate)
INHERITABLE_ATTR(SharedTrylockFunction)
INHERITABLE_ATTR(SpeculativeLoadHardening)
INHERITABLE_ATTR(StandaloneDebug)
INHERITABLE_ATTR(StrictFP)
INHERITABLE_ATTR(StrictGuardStackCheck)
INHERITABLE_ATTR(SwiftAsync)
INHERITABLE_ATTR(SwiftAsyncError)
INHERITABLE_ATTR(SwiftAsyncName)
INHERITABLE_ATTR(SwiftAttr)
INHERITABLE_ATTR(SwiftBridge)
INHERITABLE_ATTR(SwiftBridgedTypedef)
INHERITABLE_ATTR(SwiftError)
INHERITABLE_ATTR(SwiftName)
INHERITABLE_ATTR(SwiftNewType)
INHERITABLE_ATTR(SwiftPrivate)
INHERITABLE_ATTR(TLSModel)
INHERITABLE_ATTR(Target)
INHERITABLE_ATTR(TargetClones)
INHERITABLE_ATTR(TargetVersion)
INHERITABLE_ATTR(TestTypestate)
INHERITABLE_ATTR(TransparentUnion)
INHERITABLE_ATTR(TrivialABI)
INHERITABLE_ATTR(TryAcquireCapability)
INHERITABLE_ATTR(TypeTagForDatatype)
INHERITABLE_ATTR(TypeVisibility)
INHERITABLE_ATTR(Unavailable)
INHERITABLE_ATTR(Uninitialized)
INHERITABLE_ATTR(UnsafeBufferUsage)
INHERITABLE_ATTR(Unused)
INHERITABLE_ATTR(Used)
INHERITABLE_ATTR(UsingIfExists)
INHERITABLE_ATTR(Uuid)
INHERITABLE_ATTR(VecReturn)
INHERITABLE_ATTR(VecTypeHint)
INHERITABLE_ATTR(Visibility)
INHERITABLE_ATTR(WarnUnused)
INHERITABLE_ATTR(WarnUnusedResult)
INHERITABLE_ATTR(Weak)
INHERITABLE_ATTR(WeakImport)
INHERITABLE_ATTR(WeakRef)
INHERITABLE_ATTR(WebAssemblyExportName)
INHERITABLE_ATTR(WebAssemblyImportModule)
INHERITABLE_ATTR(WebAssemblyImportName)
INHERITABLE_ATTR(WorkGroupSizeHint)
INHERITABLE_ATTR(X86ForceAlignArgPointer)
INHERITABLE_ATTR(XRayInstrument)
INHERITABLE_ATTR(XRayLogArgs)
INHERITABLE_ATTR(ZeroCallUsedRegs)
ATTR(AbiTag)
ATTR(Alias)
ATTR(AlignValue)
ATTR(BuiltinAlias)
ATTR(CalledOnce)
ATTR(IFunc)
ATTR(InitSeg)
ATTR(LoaderUninitialized)
ATTR(LoopHint)
ATTR(Mode)
ATTR(NoBuiltin)
ATTR(NoEscape)
ATTR(OMPCaptureKind)
ATTR(OMPDeclareSimdDecl)
ATTR(OMPReferencedVar)
ATTR(ObjCBoxable)
ATTR(ObjCClassStub)
ATTR(ObjCDesignatedInitializer)
ATTR(ObjCDirect)
ATTR(ObjCDirectMembers)
ATTR(ObjCNonLazyClass)
ATTR(ObjCNonRuntimeProtocol)
ATTR(ObjCRuntimeName)
ATTR(ObjCRuntimeVisible)
ATTR(OpenCLAccess)
ATTR(Overloadable)
ATTR(RenderScriptKernel)
ATTR(SwiftObjCMembers)
ATTR(Thread)
PRAGMA_SPELLING_ATTR(InitSeg)
PRAGMA_SPELLING_ATTR(LoopHint)
PRAGMA_SPELLING_ATTR(OMPDeclareSimdDecl)
PRAGMA_SPELLING_ATTR(OMPDeclareTargetDecl)
PRAGMA_SPELLING_ATTR(OMPDeclareVariant)
#ifdef ATTR_RANGE
ATTR_RANGE(Attr, AddressSpace, Thread)
ATTR_RANGE(TypeAttr, AddressSpace, WebAssemblyFuncref)
ATTR_RANGE(StmtAttr, FallThrough, Unlikely)
ATTR_RANGE(DeclOrStmtAttr, AlwaysInline, NoMerge)
ATTR_RANGE(InheritableAttr, AlwaysInline, ZeroCallUsedRegs)
ATTR_RANGE(DeclOrTypeAttr, AArch64SVEPcs, VectorCall)
ATTR_RANGE(InheritableParamAttr, SwiftAsyncContext, UseHandle)
ATTR_RANGE(ParameterABIAttr, SwiftAsyncContext, SwiftIndirectResult)
ATTR_RANGE(HLSLAnnotationAttr, HLSLSV_DispatchThreadID, HLSLSV_GroupIndex)
#undef ATTR_RANGE
#endif
#undef ATTR
#undef TYPE_ATTR
#undef STMT_ATTR
#undef DECL_OR_STMT_ATTR
#undef INHERITABLE_ATTR
#undef DECL_OR_TYPE_ATTR
#undef INHERITABLE_PARAM_ATTR
#undef PARAMETER_ABI_ATTR
#undef HLSL_ANNOTATION_ATTR
#undef PRAGMA_SPELLING_ATTR
