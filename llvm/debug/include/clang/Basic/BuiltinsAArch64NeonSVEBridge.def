#ifdef GET_SVE_BUILTINS
TARGET_BUILTIN(__builtin_sve_svget_neonq_s8, "V16Scq16Sc", "n", "sve")
TARGET_BUILTIN(__builtin_sve_svget_neonq_s16, "V8sq8s", "n", "sve")
TARGET_BUILTIN(__builtin_sve_svget_neonq_s32, "V4iq4i", "n", "sve")
TARGET_BUILTIN(__builtin_sve_svget_neonq_s64, "V2Wiq2Wi", "n", "sve")
TARGET_BUILTIN(__builtin_sve_svget_neonq_u8, "V16Ucq16Uc", "n", "sve")
TARGET_BUILTIN(__builtin_sve_svget_neonq_u16, "V16Usq16Us", "n", "sve")
TARGET_BUILTIN(__builtin_sve_svget_neonq_u32, "V4Uiq4Ui", "n", "sve")
TARGET_BUILTIN(__builtin_sve_svget_neonq_u64, "V2UWiq2UWi", "n", "sve")
TARGET_BUILTIN(__builtin_sve_svget_neonq_f16, "V8hq8h", "n", "sve")
TARGET_BUILTIN(__builtin_sve_svget_neonq_f32, "V4fq4f", "n", "sve")
TARGET_BUILTIN(__builtin_sve_svget_neonq_f64, "V2dq2d", "n", "sve")
TARGET_BUILTIN(__builtin_sve_svget_neonq_bf16, "V8yq8y", "n", "sve,bf16")
TARGET_BUILTIN(__builtin_sve_svset_neonq_s8, "q16Scq16ScV16Sc", "n", "sve")
TARGET_BUILTIN(__builtin_sve_svset_neonq_s16, "q8sq8sV8s", "n", "sve")
TARGET_BUILTIN(__builtin_sve_svset_neonq_s32, "q4iq4iV4i", "n", "sve")
TARGET_BUILTIN(__builtin_sve_svset_neonq_s64, "q2Wiq2WiV2Wi", "n", "sve")
TARGET_BUILTIN(__builtin_sve_svset_neonq_u8, "q16Ucq16UcV16Uc", "n", "sve")
TARGET_BUILTIN(__builtin_sve_svset_neonq_u16, "q8Usq8UsV8s", "n", "sve")
TARGET_BUILTIN(__builtin_sve_svset_neonq_u32, "q4Uiq4UiV4Ui", "n", "sve")
TARGET_BUILTIN(__builtin_sve_svset_neonq_u64, "q2UWiq2UWiV2UWi", "n", "sve")
TARGET_BUILTIN(__builtin_sve_svset_neonq_f16, "q8hq8hV8h", "n", "sve")
TARGET_BUILTIN(__builtin_sve_svset_neonq_f32, "q4fq4fV4f", "n", "sve")
TARGET_BUILTIN(__builtin_sve_svset_neonq_f64, "q2dq2dV2d", "n", "sve")
TARGET_BUILTIN(__builtin_sve_svset_neonq_bf16, "q8yq8yV8y", "n", "sve,bf16")
TARGET_BUILTIN(__builtin_sve_svdup_neonq_s8, "q16ScV16Sc", "n", "sve")
TARGET_BUILTIN(__builtin_sve_svdup_neonq_s16, "q8sV8s", "n", "sve")
TARGET_BUILTIN(__builtin_sve_svdup_neonq_s32, "q4iV4i", "n", "sve")
TARGET_BUILTIN(__builtin_sve_svdup_neonq_s64, "q4iV4i", "n", "sve")
TARGET_BUILTIN(__builtin_sve_svdup_neonq_u8, "q16UcV16Uc", "n", "sve")
TARGET_BUILTIN(__builtin_sve_svdup_neonq_u16, "q8UsV8Us", "n", "sve")
TARGET_BUILTIN(__builtin_sve_svdup_neonq_u32, "q4UiV4Ui", "n", "sve")
TARGET_BUILTIN(__builtin_sve_svdup_neonq_u64, "q2UWiV2UWi", "n", "sve")
TARGET_BUILTIN(__builtin_sve_svdup_neonq_f16, "q8hV8h", "n", "sve")
TARGET_BUILTIN(__builtin_sve_svdup_neonq_f32, "q4fV4f", "n", "sve")
TARGET_BUILTIN(__builtin_sve_svdup_neonq_f64, "q2dV2d", "n", "sve")
TARGET_BUILTIN(__builtin_sve_svdup_neonq_bf16, "q8yV8y", "n", "sve,bf16")
#endif

