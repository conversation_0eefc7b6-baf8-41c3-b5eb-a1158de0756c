#ifdef DECL_SIGNATURE_TABLE
PrototypeDescriptor(2, 0, 8),
PrototypeDescriptor(4, 0, 4),
PrototypeDescriptor(4, 0, 4),
PrototypeDescriptor(4, 0, 4),
PrototypeDescriptor(2, 0, 8),
PrototypeDescriptor(4, 0, 4),
PrototypeDescriptor(4, 0, 4),
PrototypeDescriptor(1, 0, 8),
PrototypeDescriptor(2, 0, 8),
PrototypeDescriptor(4, 0, 4),
PrototypeDescriptor(2, 0, 8),
PrototypeDescriptor(1, 0, 32),
PrototypeDescriptor(2, 0, 8),
PrototypeDescriptor(4, 0, 4),
PrototypeDescriptor(2, 0, 8),
PrototypeDescriptor(1, 0, 8),
PrototypeDescriptor(3, 0, 0),
PrototypeDescriptor(4, 0, 4),
PrototypeDescriptor(4, 0, 4),
PrototypeDescriptor(4, 0, 4),
PrototypeDescriptor(4, 0, 4),
PrototypeDescriptor(3, 0, 0),
PrototypeDescriptor(4, 0, 4),
PrototypeDescriptor(4, 0, 4),
PrototypeDescriptor(4, 0, 4),
PrototypeDescriptor(1, 0, 8),
PrototypeDescriptor(3, 0, 0),
PrototypeDescriptor(4, 0, 4),
PrototypeDescriptor(4, 0, 4),
PrototypeDescriptor(2, 0, 8),
PrototypeDescriptor(4, 0, 4),
PrototypeDescriptor(3, 0, 0),
PrototypeDescriptor(4, 0, 4),
PrototypeDescriptor(4, 0, 4),
PrototypeDescriptor(2, 0, 8),
PrototypeDescriptor(2, 0, 8),
PrototypeDescriptor(3, 0, 0),
PrototypeDescriptor(4, 0, 4),
PrototypeDescriptor(4, 0, 4),
PrototypeDescriptor(2, 0, 8),
PrototypeDescriptor(1, 0, 32),
PrototypeDescriptor(3, 0, 0),
PrototypeDescriptor(4, 0, 4),
PrototypeDescriptor(4, 0, 4),
PrototypeDescriptor(2, 0, 8),
PrototypeDescriptor(1, 0, 8),
PrototypeDescriptor(3, 0, 0),
PrototypeDescriptor(4, 0, 4),
PrototypeDescriptor(2, 1, 8),
PrototypeDescriptor(2, 0, 8),
PrototypeDescriptor(4, 0, 4),
PrototypeDescriptor(3, 0, 0),
PrototypeDescriptor(4, 0, 4),
PrototypeDescriptor(2, 1, 8),
PrototypeDescriptor(2, 0, 8),
PrototypeDescriptor(2, 0, 8),
PrototypeDescriptor(3, 0, 0),
PrototypeDescriptor(4, 0, 4),
PrototypeDescriptor(2, 1, 8),
PrototypeDescriptor(2, 0, 8),
PrototypeDescriptor(1, 0, 32),
PrototypeDescriptor(3, 0, 0),
PrototypeDescriptor(4, 0, 4),
PrototypeDescriptor(2, 1, 8),
PrototypeDescriptor(2, 0, 8),
PrototypeDescriptor(1, 0, 8),
PrototypeDescriptor(3, 0, 0),
PrototypeDescriptor(4, 0, 4),
PrototypeDescriptor(2, 0, 8),
PrototypeDescriptor(2, 0, 8),
PrototypeDescriptor(4, 0, 4),
PrototypeDescriptor(3, 0, 0),
PrototypeDescriptor(4, 0, 4),
PrototypeDescriptor(2, 0, 8),
PrototypeDescriptor(2, 0, 8),
PrototypeDescriptor(2, 0, 8),
PrototypeDescriptor(3, 0, 0),
PrototypeDescriptor(4, 0, 4),
PrototypeDescriptor(2, 0, 8),
PrototypeDescriptor(2, 0, 8),
PrototypeDescriptor(1, 0, 32),
PrototypeDescriptor(3, 0, 0),
PrototypeDescriptor(4, 0, 4),
PrototypeDescriptor(2, 0, 8),
PrototypeDescriptor(2, 0, 8),
PrototypeDescriptor(1, 0, 8),
PrototypeDescriptor(2, 1, 8),
PrototypeDescriptor(4, 0, 4),
PrototypeDescriptor(2, 1, 8),
PrototypeDescriptor(2, 0, 8),
PrototypeDescriptor(4, 0, 4),
PrototypeDescriptor(2, 1, 8),
PrototypeDescriptor(4, 0, 4),
PrototypeDescriptor(2, 1, 8),
PrototypeDescriptor(2, 0, 8),
PrototypeDescriptor(2, 0, 8),
PrototypeDescriptor(2, 1, 8),
PrototypeDescriptor(4, 0, 4),
PrototypeDescriptor(2, 1, 8),
PrototypeDescriptor(2, 0, 8),
PrototypeDescriptor(1, 0, 32),
PrototypeDescriptor(2, 1, 8),
PrototypeDescriptor(4, 0, 4),
PrototypeDescriptor(2, 1, 8),
PrototypeDescriptor(2, 0, 8),
PrototypeDescriptor(1, 0, 8),
PrototypeDescriptor(2, 0, 8),
PrototypeDescriptor(4, 0, 4),
PrototypeDescriptor(2, 0, 8),
PrototypeDescriptor(2, 0, 8),
PrototypeDescriptor(4, 0, 4),
PrototypeDescriptor(2, 0, 8),
PrototypeDescriptor(4, 0, 4),
PrototypeDescriptor(2, 0, 8),
PrototypeDescriptor(2, 0, 8),
PrototypeDescriptor(2, 0, 8),
PrototypeDescriptor(2, 0, 8),
PrototypeDescriptor(4, 0, 4),
PrototypeDescriptor(2, 0, 8),
PrototypeDescriptor(2, 0, 8),
PrototypeDescriptor(1, 0, 32),
PrototypeDescriptor(2, 0, 8),
PrototypeDescriptor(4, 0, 4),
PrototypeDescriptor(2, 0, 8),
PrototypeDescriptor(2, 0, 8),
PrototypeDescriptor(1, 0, 8),
#endif
#ifdef DECL_INTRINSIC_RECORDS
{"sf_vc_v_x","sf_vc_v_x",4,0,0,4,1,0,2,7,127,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_xv_se","sf_vc_xv_se",41,0,0,5,1,0,2,7,127,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_x_se_u8mf8","sf_vc_x_se_u8mf8",21,0,0,5,0,0,2,1,8,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_x_se_u8mf4","sf_vc_x_se_u8mf4",21,0,0,5,0,0,2,1,8,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_x_se_u8mf2","sf_vc_x_se_u8mf2",21,0,0,5,0,0,2,1,8,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_x_se_u8m1","sf_vc_x_se_u8m1",21,0,0,5,0,0,2,1,8,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_x_se_u8m2","sf_vc_x_se_u8m2",21,0,0,5,0,0,2,1,8,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_x_se_u8m4","sf_vc_x_se_u8m4",21,0,0,5,0,0,2,1,8,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_x_se_u8m8","sf_vc_x_se_u8m8",21,0,0,5,0,0,2,1,8,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_x_se_u16mf4","sf_vc_x_se_u16mf4",21,0,0,5,0,0,2,2,8,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_x_se_u16mf2","sf_vc_x_se_u16mf2",21,0,0,5,0,0,2,2,8,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_x_se_u16m1","sf_vc_x_se_u16m1",21,0,0,5,0,0,2,2,8,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_x_se_u16m2","sf_vc_x_se_u16m2",21,0,0,5,0,0,2,2,8,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_x_se_u16m4","sf_vc_x_se_u16m4",21,0,0,5,0,0,2,2,8,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_x_se_u16m8","sf_vc_x_se_u16m8",21,0,0,5,0,0,2,2,8,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_x_se_u32mf2","sf_vc_x_se_u32mf2",21,0,0,5,0,0,2,4,8,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_x_se_u32m1","sf_vc_x_se_u32m1",21,0,0,5,0,0,2,4,8,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_x_se_u32m2","sf_vc_x_se_u32m2",21,0,0,5,0,0,2,4,8,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_x_se_u32m4","sf_vc_x_se_u32m4",21,0,0,5,0,0,2,4,8,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_x_se_u32m8","sf_vc_x_se_u32m8",21,0,0,5,0,0,2,4,8,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_x_se_u64m1","sf_vc_x_se_u64m1",21,0,0,5,0,0,3,8,8,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_x_se_u64m2","sf_vc_x_se_u64m2",21,0,0,5,0,0,3,8,8,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_x_se_u64m4","sf_vc_x_se_u64m4",21,0,0,5,0,0,3,8,8,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_x_se_u64m8","sf_vc_x_se_u64m8",21,0,0,5,0,0,3,8,8,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_i_se_u8mf8","sf_vc_i_se_u8mf8",16,0,0,5,0,0,2,1,8,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_i_se_u8mf4","sf_vc_i_se_u8mf4",16,0,0,5,0,0,2,1,8,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_i_se_u8mf2","sf_vc_i_se_u8mf2",16,0,0,5,0,0,2,1,8,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_i_se_u8m1","sf_vc_i_se_u8m1",16,0,0,5,0,0,2,1,8,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_i_se_u8m2","sf_vc_i_se_u8m2",16,0,0,5,0,0,2,1,8,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_i_se_u8m4","sf_vc_i_se_u8m4",16,0,0,5,0,0,2,1,8,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_i_se_u8m8","sf_vc_i_se_u8m8",16,0,0,5,0,0,2,1,8,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_i_se_u16mf4","sf_vc_i_se_u16mf4",16,0,0,5,0,0,2,2,8,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_i_se_u16mf2","sf_vc_i_se_u16mf2",16,0,0,5,0,0,2,2,8,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_i_se_u16m1","sf_vc_i_se_u16m1",16,0,0,5,0,0,2,2,8,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_i_se_u16m2","sf_vc_i_se_u16m2",16,0,0,5,0,0,2,2,8,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_i_se_u16m4","sf_vc_i_se_u16m4",16,0,0,5,0,0,2,2,8,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_i_se_u16m8","sf_vc_i_se_u16m8",16,0,0,5,0,0,2,2,8,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_i_se_u32mf2","sf_vc_i_se_u32mf2",16,0,0,5,0,0,2,4,8,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_i_se_u32m1","sf_vc_i_se_u32m1",16,0,0,5,0,0,2,4,8,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_i_se_u32m2","sf_vc_i_se_u32m2",16,0,0,5,0,0,2,4,8,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_i_se_u32m4","sf_vc_i_se_u32m4",16,0,0,5,0,0,2,4,8,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_i_se_u32m8","sf_vc_i_se_u32m8",16,0,0,5,0,0,2,4,8,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_i_se_u64m1","sf_vc_i_se_u64m1",16,0,0,5,0,0,2,8,8,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_i_se_u64m2","sf_vc_i_se_u64m2",16,0,0,5,0,0,2,8,8,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_i_se_u64m4","sf_vc_i_se_u64m4",16,0,0,5,0,0,2,8,8,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_i_se_u64m8","sf_vc_i_se_u64m8",16,0,0,5,0,0,2,8,8,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_xv_se","sf_vc_xv_se",41,0,0,5,1,0,3,8,127,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_iv_se","sf_vc_iv_se",26,0,0,5,1,0,2,7,127,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_iv_se","sf_vc_iv_se",26,0,0,5,1,0,2,8,127,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_vv_se","sf_vc_vv_se",31,0,0,5,1,0,2,7,127,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_vv_se","sf_vc_vv_se",31,0,0,5,1,0,2,8,127,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_fv_se","sf_vc_fv_se",36,0,0,5,1,0,2,6,127,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_fv_se","sf_vc_fv_se",36,0,0,5,1,0,2,8,127,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_xvv_se","sf_vc_xvv_se",81,0,0,5,1,0,2,7,127,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_xvv_se","sf_vc_xvv_se",81,0,0,5,1,0,3,8,127,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_ivv_se","sf_vc_ivv_se",66,0,0,5,1,0,2,7,127,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_ivv_se","sf_vc_ivv_se",66,0,0,5,1,0,2,8,127,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_vvv_se","sf_vc_vvv_se",71,0,0,5,1,0,2,7,127,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_vvv_se","sf_vc_vvv_se",71,0,0,5,1,0,2,8,127,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_fvv_se","sf_vc_fvv_se",76,0,0,5,1,0,2,6,127,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_fvv_se","sf_vc_fvv_se",76,0,0,5,1,0,2,8,127,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_v_x_se","sf_vc_v_x_se",4,0,0,4,1,0,2,7,127,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_v_x","sf_vc_v_x",4,0,0,4,1,0,3,8,127,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_v_x_se","sf_vc_v_x_se",4,0,0,4,1,0,3,8,127,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_v_i","sf_vc_v_i",0,0,0,4,1,0,2,7,127,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_v_i_se","sf_vc_v_i_se",0,0,0,4,1,0,2,7,127,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_v_i","sf_vc_v_i",0,0,0,4,1,0,2,8,127,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_v_i_se","sf_vc_v_i_se",0,0,0,4,1,0,2,8,127,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_v_xv","sf_vc_v_xv",12,0,0,4,1,0,2,7,127,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_v_xv_se","sf_vc_v_xv_se",12,0,0,4,1,0,2,7,127,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_v_xv","sf_vc_v_xv",12,0,0,4,1,0,3,8,127,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_v_xv_se","sf_vc_v_xv_se",12,0,0,4,1,0,3,8,127,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_v_iv","sf_vc_v_iv",109,0,0,4,1,0,2,7,127,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_v_iv_se","sf_vc_v_iv_se",109,0,0,4,1,0,2,7,127,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_v_iv","sf_vc_v_iv",109,0,0,4,1,0,2,8,127,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_v_iv_se","sf_vc_v_iv_se",109,0,0,4,1,0,2,8,127,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_v_vv","sf_vc_v_vv",106,0,0,4,1,0,2,7,127,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_v_vv_se","sf_vc_v_vv_se",106,0,0,4,1,0,2,7,127,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_v_vv","sf_vc_v_vv",106,0,0,4,1,0,2,8,127,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_v_vv_se","sf_vc_v_vv_se",106,0,0,4,1,0,2,8,127,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_v_fv","sf_vc_v_fv",8,0,0,4,1,0,2,6,127,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_v_fv_se","sf_vc_v_fv_se",8,0,0,4,1,0,2,6,127,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_v_fv","sf_vc_v_fv",8,0,0,4,1,0,2,8,127,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_v_fv_se","sf_vc_v_fv_se",8,0,0,4,1,0,2,8,127,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_v_xvv","sf_vc_v_xvv",121,0,0,5,1,0,2,7,127,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_v_xvv_se","sf_vc_v_xvv_se",121,0,0,5,1,0,2,7,127,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_v_xvv","sf_vc_v_xvv",121,0,0,5,1,0,3,8,127,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_v_xvv_se","sf_vc_v_xvv_se",121,0,0,5,1,0,3,8,127,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_v_ivv","sf_vc_v_ivv",106,0,0,5,1,0,2,7,127,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_v_ivv_se","sf_vc_v_ivv_se",106,0,0,5,1,0,2,7,127,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_v_ivv","sf_vc_v_ivv",106,0,0,5,1,0,2,8,127,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_v_ivv_se","sf_vc_v_ivv_se",106,0,0,5,1,0,2,8,127,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_v_vvv","sf_vc_v_vvv",111,0,0,5,1,0,2,7,127,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_v_vvv_se","sf_vc_v_vvv_se",111,0,0,5,1,0,2,7,127,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_v_vvv","sf_vc_v_vvv",111,0,0,5,1,0,2,8,127,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_v_vvv_se","sf_vc_v_vvv_se",111,0,0,5,1,0,2,8,127,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_v_fvv","sf_vc_v_fvv",116,0,0,5,1,0,2,6,127,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_v_fvv_se","sf_vc_v_fvv_se",116,0,0,5,1,0,2,6,127,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_v_fvv","sf_vc_v_fvv",116,0,0,5,1,0,2,8,127,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_v_fvv_se","sf_vc_v_fvv_se",116,0,0,5,1,0,2,8,127,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_xvw_se","sf_vc_xvw_se",61,0,0,5,1,0,2,7,63,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_ivw_se","sf_vc_ivw_se",46,0,0,5,1,0,2,7,63,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_vvw_se","sf_vc_vvw_se",51,0,0,5,1,0,2,7,63,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_fvw_se","sf_vc_fvw_se",56,0,0,5,1,0,2,6,63,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_v_xvw","sf_vc_v_xvw",101,0,0,5,1,0,2,7,63,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_v_xvw_se","sf_vc_v_xvw_se",101,0,0,5,1,0,2,7,63,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_v_ivw","sf_vc_v_ivw",86,0,0,5,1,0,2,7,63,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_v_ivw_se","sf_vc_v_ivw_se",86,0,0,5,1,0,2,7,63,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_v_vvw","sf_vc_v_vvw",91,0,0,5,1,0,2,7,63,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_v_vvw_se","sf_vc_v_vvw_se",91,0,0,5,1,0,2,7,63,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_v_fvw","sf_vc_v_fvw",96,0,0,5,1,0,2,6,63,1,0,1,1,1,1,0,0,0,2,},
{"sf_vc_v_fvw_se","sf_vc_v_fvw_se",96,0,0,5,1,0,2,6,63,1,0,1,1,1,1,0,0,0,2,},
#endif
