#if defined(TARGET_BUILTIN) && !defined(RISCVV_BUILTIN)
#define RISCVV_BUILTIN(ID, TYPE, ATTRS) TARGET_BUILTIN(ID, TYPE, ATTRS, "zve32x")
#endif
RISCVV_BUILTIN(__builtin_rvv_vadd_vv,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vadd_vv_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vadd_vv_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vadd_vv_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vadd_vv_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vadd_vv_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfcvt_x_f_v_rm,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfcvt_x_f_v_rm_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfcvt_x_f_v_rm_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfcvt_x_f_v_rm_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfcvt_x_f_v_rm_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfcvt_x_f_v_rm_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwaddu_vv,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwaddu_vv_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwaddu_vv_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwaddu_vv_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwaddu_vv_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwaddu_vv_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwaddu_wv,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwaddu_wv_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwaddu_wv_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwaddu_wv_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwaddu_wv_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwaddu_wv_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vle8_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vle8_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vle8_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vle8_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vle8_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vle8_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vle8ff_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vle8ff_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vle8ff_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vle8ff_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vle8ff_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vle8ff_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlse8_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlse8_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlse8_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlse8_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlse8_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlse8_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxei8_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxei8_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxei8_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxei8_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxei8_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxei8_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxei64_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxei64_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxei64_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxei64_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxei64_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxei64_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vse8_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vse8_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsse8_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsse8_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsuxei8_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsuxei8_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsuxei64_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsuxei64_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vneg_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vneg_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vneg_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vneg_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vneg_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vneg_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vnot_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vnot_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vnot_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vnot_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vnot_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vnot_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmmv_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfneg_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfneg_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfneg_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfneg_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfneg_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfneg_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwcvtu_x_x_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwcvtu_x_x_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwcvtu_x_x_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwcvtu_x_x_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwcvtu_x_x_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwcvtu_x_x_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vncvt_x_x_w,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vncvt_x_x_w_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vncvt_x_x_w_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vncvt_x_x_w_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vncvt_x_x_w_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vncvt_x_x_w_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vle16_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vle16_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vle16_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vle16_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vle16_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vle16_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vle32_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vle32_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vle32_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vle32_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vle32_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vle32_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vle64_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vle64_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vle64_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vle64_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vle64_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vle64_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vse16_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vse16_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vse32_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vse32_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vse64_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vse64_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlse16_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlse16_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlse16_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlse16_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlse16_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlse16_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlse32_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlse32_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlse32_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlse32_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlse32_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlse32_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlse64_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlse64_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlse64_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlse64_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlse64_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlse64_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsse16_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsse16_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsse32_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsse32_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsse64_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsse64_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxei16_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxei16_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxei16_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxei16_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxei16_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxei16_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxei32_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxei32_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxei32_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxei32_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxei32_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxei32_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxei8_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxei8_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxei8_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxei8_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxei8_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxei8_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxei16_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxei16_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxei16_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxei16_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxei16_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxei16_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxei32_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxei32_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxei32_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxei32_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxei32_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxei32_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxei64_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxei64_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxei64_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxei64_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxei64_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxei64_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsuxei16_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsuxei16_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsuxei32_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsuxei32_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsoxei8_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsoxei8_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsoxei16_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsoxei16_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsoxei32_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsoxei32_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsoxei64_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsoxei64_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vle16ff_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vle16ff_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vle16ff_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vle16ff_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vle16ff_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vle16ff_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vle32ff_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vle32ff_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vle32ff_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vle32ff_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vle32ff_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vle32ff_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vle64ff_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vle64ff_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vle64ff_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vle64ff_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vle64ff_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vle64ff_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg2e8_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg2e8_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg2e8_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg2e8_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg2e8_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg2e8_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsseg2e8_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsseg2e8_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg2e8ff_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg2e8ff_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg2e8ff_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg2e8ff_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg2e8ff_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg2e8ff_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg2e8_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg2e8_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg2e8_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg2e8_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg2e8_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg2e8_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vssseg2e8_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vssseg2e8_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg2ei8_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg2ei8_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg2ei8_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg2ei8_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg2ei8_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg2ei8_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsuxseg2ei8_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsuxseg2ei8_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg3e8_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg3e8_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg3e8_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg3e8_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg3e8_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg3e8_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg4e8_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg4e8_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg4e8_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg4e8_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg4e8_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg4e8_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg5e8_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg5e8_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg5e8_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg5e8_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg5e8_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg5e8_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg6e8_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg6e8_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg6e8_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg6e8_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg6e8_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg6e8_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg7e8_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg7e8_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg7e8_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg7e8_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg7e8_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg7e8_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg8e8_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg8e8_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg8e8_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg8e8_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg8e8_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg8e8_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg2e16_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg2e16_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg2e16_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg2e16_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg2e16_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg2e16_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg3e16_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg3e16_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg3e16_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg3e16_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg3e16_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg3e16_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg4e16_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg4e16_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg4e16_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg4e16_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg4e16_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg4e16_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg5e16_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg5e16_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg5e16_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg5e16_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg5e16_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg5e16_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg6e16_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg6e16_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg6e16_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg6e16_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg6e16_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg6e16_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg7e16_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg7e16_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg7e16_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg7e16_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg7e16_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg7e16_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg8e16_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg8e16_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg8e16_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg8e16_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg8e16_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg8e16_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg2e32_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg2e32_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg2e32_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg2e32_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg2e32_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg2e32_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg3e32_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg3e32_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg3e32_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg3e32_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg3e32_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg3e32_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg4e32_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg4e32_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg4e32_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg4e32_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg4e32_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg4e32_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg5e32_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg5e32_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg5e32_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg5e32_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg5e32_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg5e32_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg6e32_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg6e32_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg6e32_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg6e32_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg6e32_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg6e32_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg7e32_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg7e32_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg7e32_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg7e32_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg7e32_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg7e32_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg8e32_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg8e32_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg8e32_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg8e32_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg8e32_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg8e32_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg2e64_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg2e64_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg2e64_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg2e64_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg2e64_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg2e64_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg3e64_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg3e64_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg3e64_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg3e64_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg3e64_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg3e64_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg4e64_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg4e64_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg4e64_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg4e64_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg4e64_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg4e64_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg5e64_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg5e64_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg5e64_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg5e64_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg5e64_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg5e64_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg6e64_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg6e64_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg6e64_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg6e64_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg6e64_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg6e64_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg7e64_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg7e64_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg7e64_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg7e64_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg7e64_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg7e64_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg8e64_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg8e64_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg8e64_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg8e64_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg8e64_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg8e64_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg3e8ff_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg3e8ff_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg3e8ff_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg3e8ff_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg3e8ff_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg3e8ff_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg4e8ff_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg4e8ff_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg4e8ff_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg4e8ff_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg4e8ff_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg4e8ff_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg5e8ff_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg5e8ff_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg5e8ff_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg5e8ff_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg5e8ff_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg5e8ff_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg6e8ff_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg6e8ff_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg6e8ff_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg6e8ff_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg6e8ff_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg6e8ff_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg7e8ff_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg7e8ff_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg7e8ff_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg7e8ff_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg7e8ff_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg7e8ff_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg8e8ff_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg8e8ff_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg8e8ff_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg8e8ff_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg8e8ff_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg8e8ff_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg2e16ff_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg2e16ff_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg2e16ff_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg2e16ff_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg2e16ff_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg2e16ff_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg3e16ff_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg3e16ff_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg3e16ff_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg3e16ff_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg3e16ff_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg3e16ff_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg4e16ff_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg4e16ff_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg4e16ff_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg4e16ff_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg4e16ff_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg4e16ff_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg5e16ff_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg5e16ff_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg5e16ff_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg5e16ff_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg5e16ff_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg5e16ff_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg6e16ff_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg6e16ff_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg6e16ff_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg6e16ff_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg6e16ff_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg6e16ff_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg7e16ff_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg7e16ff_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg7e16ff_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg7e16ff_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg7e16ff_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg7e16ff_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg8e16ff_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg8e16ff_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg8e16ff_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg8e16ff_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg8e16ff_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg8e16ff_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg2e32ff_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg2e32ff_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg2e32ff_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg2e32ff_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg2e32ff_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg2e32ff_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg3e32ff_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg3e32ff_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg3e32ff_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg3e32ff_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg3e32ff_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg3e32ff_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg4e32ff_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg4e32ff_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg4e32ff_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg4e32ff_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg4e32ff_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg4e32ff_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg5e32ff_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg5e32ff_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg5e32ff_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg5e32ff_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg5e32ff_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg5e32ff_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg6e32ff_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg6e32ff_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg6e32ff_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg6e32ff_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg6e32ff_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg6e32ff_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg7e32ff_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg7e32ff_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg7e32ff_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg7e32ff_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg7e32ff_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg7e32ff_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg8e32ff_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg8e32ff_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg8e32ff_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg8e32ff_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg8e32ff_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg8e32ff_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg2e64ff_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg2e64ff_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg2e64ff_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg2e64ff_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg2e64ff_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg2e64ff_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg3e64ff_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg3e64ff_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg3e64ff_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg3e64ff_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg3e64ff_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg3e64ff_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg4e64ff_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg4e64ff_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg4e64ff_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg4e64ff_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg4e64ff_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg4e64ff_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg5e64ff_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg5e64ff_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg5e64ff_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg5e64ff_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg5e64ff_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg5e64ff_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg6e64ff_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg6e64ff_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg6e64ff_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg6e64ff_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg6e64ff_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg6e64ff_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg7e64ff_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg7e64ff_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg7e64ff_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg7e64ff_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg7e64ff_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg7e64ff_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg8e64ff_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg8e64ff_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg8e64ff_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg8e64ff_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg8e64ff_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlseg8e64ff_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg3e8_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg3e8_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg3e8_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg3e8_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg3e8_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg3e8_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg4e8_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg4e8_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg4e8_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg4e8_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg4e8_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg4e8_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg5e8_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg5e8_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg5e8_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg5e8_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg5e8_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg5e8_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg6e8_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg6e8_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg6e8_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg6e8_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg6e8_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg6e8_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg7e8_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg7e8_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg7e8_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg7e8_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg7e8_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg7e8_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg8e8_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg8e8_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg8e8_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg8e8_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg8e8_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg8e8_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg2e16_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg2e16_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg2e16_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg2e16_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg2e16_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg2e16_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg3e16_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg3e16_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg3e16_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg3e16_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg3e16_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg3e16_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg4e16_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg4e16_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg4e16_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg4e16_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg4e16_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg4e16_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg5e16_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg5e16_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg5e16_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg5e16_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg5e16_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg5e16_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg6e16_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg6e16_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg6e16_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg6e16_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg6e16_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg6e16_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg7e16_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg7e16_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg7e16_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg7e16_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg7e16_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg7e16_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg8e16_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg8e16_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg8e16_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg8e16_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg8e16_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg8e16_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg2e32_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg2e32_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg2e32_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg2e32_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg2e32_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg2e32_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg3e32_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg3e32_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg3e32_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg3e32_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg3e32_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg3e32_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg4e32_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg4e32_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg4e32_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg4e32_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg4e32_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg4e32_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg5e32_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg5e32_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg5e32_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg5e32_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg5e32_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg5e32_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg6e32_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg6e32_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg6e32_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg6e32_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg6e32_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg6e32_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg7e32_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg7e32_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg7e32_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg7e32_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg7e32_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg7e32_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg8e32_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg8e32_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg8e32_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg8e32_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg8e32_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg8e32_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg2e64_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg2e64_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg2e64_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg2e64_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg2e64_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg2e64_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg3e64_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg3e64_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg3e64_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg3e64_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg3e64_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg3e64_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg4e64_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg4e64_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg4e64_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg4e64_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg4e64_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg4e64_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg5e64_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg5e64_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg5e64_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg5e64_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg5e64_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg5e64_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg6e64_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg6e64_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg6e64_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg6e64_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg6e64_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg6e64_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg7e64_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg7e64_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg7e64_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg7e64_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg7e64_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg7e64_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg8e64_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg8e64_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg8e64_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg8e64_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg8e64_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlsseg8e64_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg3ei8_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg3ei8_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg3ei8_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg3ei8_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg3ei8_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg3ei8_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg4ei8_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg4ei8_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg4ei8_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg4ei8_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg4ei8_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg4ei8_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg5ei8_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg5ei8_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg5ei8_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg5ei8_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg5ei8_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg5ei8_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg6ei8_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg6ei8_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg6ei8_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg6ei8_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg6ei8_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg6ei8_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg7ei8_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg7ei8_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg7ei8_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg7ei8_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg7ei8_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg7ei8_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg8ei8_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg8ei8_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg8ei8_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg8ei8_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg8ei8_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg8ei8_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg2ei16_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg2ei16_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg2ei16_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg2ei16_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg2ei16_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg2ei16_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg3ei16_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg3ei16_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg3ei16_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg3ei16_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg3ei16_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg3ei16_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg4ei16_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg4ei16_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg4ei16_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg4ei16_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg4ei16_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg4ei16_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg5ei16_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg5ei16_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg5ei16_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg5ei16_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg5ei16_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg5ei16_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg6ei16_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg6ei16_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg6ei16_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg6ei16_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg6ei16_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg6ei16_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg7ei16_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg7ei16_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg7ei16_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg7ei16_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg7ei16_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg7ei16_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg8ei16_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg8ei16_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg8ei16_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg8ei16_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg8ei16_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg8ei16_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg2ei32_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg2ei32_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg2ei32_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg2ei32_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg2ei32_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg2ei32_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg3ei32_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg3ei32_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg3ei32_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg3ei32_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg3ei32_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg3ei32_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg4ei32_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg4ei32_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg4ei32_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg4ei32_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg4ei32_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg4ei32_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg5ei32_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg5ei32_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg5ei32_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg5ei32_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg5ei32_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg5ei32_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg6ei32_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg6ei32_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg6ei32_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg6ei32_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg6ei32_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg6ei32_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg7ei32_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg7ei32_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg7ei32_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg7ei32_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg7ei32_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg7ei32_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg8ei32_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg8ei32_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg8ei32_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg8ei32_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg8ei32_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg8ei32_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg2ei64_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg2ei64_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg2ei64_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg2ei64_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg2ei64_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg2ei64_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg3ei64_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg3ei64_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg3ei64_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg3ei64_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg3ei64_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg3ei64_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg4ei64_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg4ei64_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg4ei64_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg4ei64_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg4ei64_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg4ei64_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg5ei64_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg5ei64_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg5ei64_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg5ei64_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg5ei64_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg5ei64_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg6ei64_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg6ei64_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg6ei64_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg6ei64_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg6ei64_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg6ei64_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg7ei64_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg7ei64_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg7ei64_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg7ei64_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg7ei64_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg7ei64_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg8ei64_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg8ei64_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg8ei64_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg8ei64_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg8ei64_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vluxseg8ei64_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg2ei8_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg2ei8_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg2ei8_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg2ei8_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg2ei8_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg2ei8_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg3ei8_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg3ei8_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg3ei8_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg3ei8_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg3ei8_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg3ei8_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg4ei8_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg4ei8_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg4ei8_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg4ei8_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg4ei8_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg4ei8_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg5ei8_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg5ei8_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg5ei8_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg5ei8_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg5ei8_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg5ei8_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg6ei8_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg6ei8_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg6ei8_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg6ei8_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg6ei8_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg6ei8_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg7ei8_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg7ei8_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg7ei8_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg7ei8_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg7ei8_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg7ei8_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg8ei8_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg8ei8_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg8ei8_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg8ei8_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg8ei8_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg8ei8_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg2ei16_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg2ei16_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg2ei16_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg2ei16_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg2ei16_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg2ei16_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg3ei16_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg3ei16_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg3ei16_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg3ei16_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg3ei16_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg3ei16_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg4ei16_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg4ei16_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg4ei16_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg4ei16_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg4ei16_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg4ei16_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg5ei16_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg5ei16_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg5ei16_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg5ei16_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg5ei16_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg5ei16_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg6ei16_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg6ei16_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg6ei16_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg6ei16_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg6ei16_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg6ei16_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg7ei16_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg7ei16_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg7ei16_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg7ei16_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg7ei16_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg7ei16_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg8ei16_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg8ei16_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg8ei16_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg8ei16_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg8ei16_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg8ei16_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg2ei32_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg2ei32_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg2ei32_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg2ei32_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg2ei32_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg2ei32_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg3ei32_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg3ei32_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg3ei32_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg3ei32_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg3ei32_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg3ei32_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg4ei32_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg4ei32_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg4ei32_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg4ei32_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg4ei32_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg4ei32_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg5ei32_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg5ei32_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg5ei32_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg5ei32_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg5ei32_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg5ei32_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg6ei32_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg6ei32_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg6ei32_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg6ei32_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg6ei32_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg6ei32_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg7ei32_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg7ei32_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg7ei32_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg7ei32_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg7ei32_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg7ei32_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg8ei32_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg8ei32_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg8ei32_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg8ei32_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg8ei32_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg8ei32_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg2ei64_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg2ei64_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg2ei64_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg2ei64_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg2ei64_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg2ei64_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg3ei64_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg3ei64_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg3ei64_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg3ei64_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg3ei64_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg3ei64_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg4ei64_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg4ei64_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg4ei64_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg4ei64_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg4ei64_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg4ei64_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg5ei64_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg5ei64_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg5ei64_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg5ei64_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg5ei64_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg5ei64_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg6ei64_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg6ei64_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg6ei64_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg6ei64_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg6ei64_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg6ei64_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg7ei64_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg7ei64_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg7ei64_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg7ei64_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg7ei64_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg7ei64_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg8ei64_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg8ei64_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg8ei64_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg8ei64_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg8ei64_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vloxseg8ei64_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsseg3e8_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsseg3e8_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsseg4e8_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsseg4e8_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsseg5e8_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsseg5e8_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsseg6e8_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsseg6e8_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsseg7e8_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsseg7e8_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsseg8e8_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsseg8e8_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsseg2e16_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsseg2e16_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsseg3e16_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsseg3e16_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsseg4e16_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsseg4e16_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsseg5e16_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsseg5e16_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsseg6e16_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsseg6e16_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsseg7e16_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsseg7e16_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsseg8e16_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsseg8e16_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsseg2e32_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsseg2e32_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsseg3e32_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsseg3e32_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsseg4e32_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsseg4e32_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsseg5e32_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsseg5e32_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsseg6e32_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsseg6e32_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsseg7e32_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsseg7e32_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsseg8e32_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsseg8e32_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsseg2e64_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsseg2e64_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsseg3e64_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsseg3e64_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsseg4e64_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsseg4e64_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsseg5e64_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsseg5e64_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsseg6e64_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsseg6e64_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsseg7e64_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsseg7e64_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsseg8e64_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsseg8e64_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vssseg3e8_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vssseg3e8_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vssseg4e8_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vssseg4e8_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vssseg5e8_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vssseg5e8_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vssseg6e8_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vssseg6e8_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vssseg7e8_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vssseg7e8_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vssseg8e8_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vssseg8e8_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vssseg2e16_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vssseg2e16_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vssseg3e16_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vssseg3e16_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vssseg4e16_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vssseg4e16_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vssseg5e16_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vssseg5e16_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vssseg6e16_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vssseg6e16_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vssseg7e16_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vssseg7e16_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vssseg8e16_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vssseg8e16_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vssseg2e32_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vssseg2e32_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vssseg3e32_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vssseg3e32_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vssseg4e32_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vssseg4e32_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vssseg5e32_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vssseg5e32_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vssseg6e32_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vssseg6e32_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vssseg7e32_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vssseg7e32_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vssseg8e32_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vssseg8e32_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vssseg2e64_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vssseg2e64_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vssseg3e64_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vssseg3e64_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vssseg4e64_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vssseg4e64_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vssseg5e64_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vssseg5e64_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vssseg6e64_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vssseg6e64_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vssseg7e64_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vssseg7e64_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vssseg8e64_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vssseg8e64_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsuxseg3ei8_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsuxseg3ei8_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsuxseg4ei8_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsuxseg4ei8_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsuxseg5ei8_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsuxseg5ei8_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsuxseg6ei8_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsuxseg6ei8_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsuxseg7ei8_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsuxseg7ei8_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsuxseg8ei8_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsuxseg8ei8_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsuxseg2ei16_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsuxseg2ei16_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsuxseg3ei16_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsuxseg3ei16_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsuxseg4ei16_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsuxseg4ei16_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsuxseg5ei16_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsuxseg5ei16_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsuxseg6ei16_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsuxseg6ei16_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsuxseg7ei16_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsuxseg7ei16_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsuxseg8ei16_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsuxseg8ei16_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsuxseg2ei32_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsuxseg2ei32_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsuxseg3ei32_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsuxseg3ei32_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsuxseg4ei32_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsuxseg4ei32_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsuxseg5ei32_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsuxseg5ei32_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsuxseg6ei32_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsuxseg6ei32_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsuxseg7ei32_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsuxseg7ei32_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsuxseg8ei32_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsuxseg8ei32_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsuxseg2ei64_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsuxseg2ei64_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsuxseg3ei64_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsuxseg3ei64_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsuxseg4ei64_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsuxseg4ei64_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsuxseg5ei64_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsuxseg5ei64_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsuxseg6ei64_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsuxseg6ei64_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsuxseg7ei64_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsuxseg7ei64_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsuxseg8ei64_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsuxseg8ei64_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsoxseg2ei8_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsoxseg2ei8_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsoxseg3ei8_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsoxseg3ei8_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsoxseg4ei8_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsoxseg4ei8_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsoxseg5ei8_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsoxseg5ei8_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsoxseg6ei8_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsoxseg6ei8_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsoxseg7ei8_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsoxseg7ei8_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsoxseg8ei8_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsoxseg8ei8_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsoxseg2ei16_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsoxseg2ei16_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsoxseg3ei16_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsoxseg3ei16_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsoxseg4ei16_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsoxseg4ei16_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsoxseg5ei16_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsoxseg5ei16_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsoxseg6ei16_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsoxseg6ei16_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsoxseg7ei16_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsoxseg7ei16_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsoxseg8ei16_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsoxseg8ei16_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsoxseg2ei32_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsoxseg2ei32_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsoxseg3ei32_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsoxseg3ei32_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsoxseg4ei32_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsoxseg4ei32_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsoxseg5ei32_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsoxseg5ei32_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsoxseg6ei32_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsoxseg6ei32_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsoxseg7ei32_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsoxseg7ei32_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsoxseg8ei32_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsoxseg8ei32_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsoxseg2ei64_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsoxseg2ei64_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsoxseg3ei64_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsoxseg3ei64_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsoxseg4ei64_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsoxseg4ei64_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsoxseg5ei64_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsoxseg5ei64_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsoxseg6ei64_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsoxseg6ei64_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsoxseg7ei64_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsoxseg7ei64_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsoxseg8ei64_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsoxseg8ei64_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vadd_vx,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vadd_vx_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vadd_vx_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vadd_vx_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vadd_vx_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vadd_vx_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsub_vv,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsub_vv_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsub_vv_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsub_vv_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsub_vv_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsub_vv_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsub_vx,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsub_vx_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsub_vx_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsub_vx_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsub_vx_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsub_vx_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vrsub_vx,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vrsub_vx_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vrsub_vx_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vrsub_vx_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vrsub_vx_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vrsub_vx_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwaddu_vx,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwaddu_vx_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwaddu_vx_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwaddu_vx_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwaddu_vx_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwaddu_vx_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwsubu_vv,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwsubu_vv_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwsubu_vv_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwsubu_vv_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwsubu_vv_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwsubu_vv_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwsubu_vx,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwsubu_vx_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwsubu_vx_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwsubu_vx_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwsubu_vx_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwsubu_vx_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwadd_vv,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwadd_vv_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwadd_vv_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwadd_vv_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwadd_vv_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwadd_vv_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwadd_vx,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwadd_vx_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwadd_vx_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwadd_vx_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwadd_vx_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwadd_vx_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwsub_vv,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwsub_vv_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwsub_vv_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwsub_vv_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwsub_vv_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwsub_vv_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwsub_vx,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwsub_vx_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwsub_vx_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwsub_vx_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwsub_vx_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwsub_vx_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwaddu_wx,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwaddu_wx_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwaddu_wx_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwaddu_wx_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwaddu_wx_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwaddu_wx_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwsubu_wv,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwsubu_wv_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwsubu_wv_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwsubu_wv_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwsubu_wv_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwsubu_wv_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwsubu_wx,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwsubu_wx_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwsubu_wx_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwsubu_wx_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwsubu_wx_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwsubu_wx_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwadd_wv,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwadd_wv_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwadd_wv_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwadd_wv_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwadd_wv_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwadd_wv_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwadd_wx,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwadd_wx_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwadd_wx_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwadd_wx_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwadd_wx_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwadd_wx_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwsub_wv,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwsub_wv_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwsub_wv_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwsub_wv_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwsub_wv_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwsub_wv_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwsub_wx,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwsub_wx_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwsub_wx_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwsub_wx_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwsub_wx_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwsub_wx_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwcvt_x_x_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwcvt_x_x_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwcvt_x_x_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwcvt_x_x_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwcvt_x_x_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwcvt_x_x_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vadc_vvm,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vadc_vvm_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vadc_vxm,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vadc_vxm_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsbc_vvm,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsbc_vvm_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsbc_vxm,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsbc_vxm_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmadc_vvm,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmadc_vxm,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmadc_vv,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmadc_vx,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmsbc_vvm,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmsbc_vxm,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmsbc_vv,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmsbc_vx,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vand_vv,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vand_vv_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vand_vv_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vand_vv_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vand_vv_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vand_vv_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vand_vx,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vand_vx_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vand_vx_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vand_vx_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vand_vx_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vand_vx_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vxor_vv,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vxor_vv_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vxor_vv_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vxor_vv_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vxor_vv_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vxor_vv_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vxor_vx,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vxor_vx_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vxor_vx_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vxor_vx_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vxor_vx_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vxor_vx_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vor_vv,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vor_vv_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vor_vv_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vor_vv_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vor_vv_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vor_vv_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vor_vx,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vor_vx_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vor_vx_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vor_vx_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vor_vx_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vor_vx_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsll_vv,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsll_vv_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsll_vv_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsll_vv_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsll_vv_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsll_vv_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsll_vx,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsll_vx_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsll_vx_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsll_vx_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsll_vx_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsll_vx_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsrl_vv,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsrl_vv_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsrl_vv_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsrl_vv_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsrl_vv_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsrl_vv_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsrl_vx,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsrl_vx_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsrl_vx_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsrl_vx_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsrl_vx_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsrl_vx_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsra_vv,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsra_vv_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsra_vv_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsra_vv_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsra_vv_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsra_vv_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsra_vx,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsra_vx_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsra_vx_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsra_vx_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsra_vx_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsra_vx_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vnsrl_wv,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vnsrl_wv_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vnsrl_wv_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vnsrl_wv_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vnsrl_wv_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vnsrl_wv_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vnsrl_wx,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vnsrl_wx_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vnsrl_wx_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vnsrl_wx_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vnsrl_wx_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vnsrl_wx_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vnsra_wv,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vnsra_wv_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vnsra_wv_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vnsra_wv_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vnsra_wv_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vnsra_wv_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vnsra_wx,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vnsra_wx_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vnsra_wx_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vnsra_wx_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vnsra_wx_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vnsra_wx_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmseq_vv,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmseq_vv_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmseq_vv_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmseq_vx,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmseq_vx_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmseq_vx_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmsne_vv,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmsne_vv_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmsne_vv_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmsne_vx,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmsne_vx_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmsne_vx_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmsltu_vv,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmsltu_vv_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmsltu_vv_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmsltu_vx,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmsltu_vx_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmsltu_vx_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmslt_vv,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmslt_vv_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmslt_vv_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmslt_vx,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmslt_vx_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmslt_vx_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmsleu_vv,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmsleu_vv_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmsleu_vv_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmsleu_vx,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmsleu_vx_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmsleu_vx_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmsle_vv,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmsle_vv_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmsle_vv_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmsle_vx,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmsle_vx_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmsle_vx_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmsgtu_vv,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmsgtu_vv_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmsgtu_vv_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmsgtu_vx,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmsgtu_vx_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmsgtu_vx_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmsgt_vv,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmsgt_vv_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmsgt_vv_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmsgt_vx,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmsgt_vx_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmsgt_vx_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmsgeu_vv,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmsgeu_vv_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmsgeu_vv_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmsgeu_vx,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmsgeu_vx_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmsgeu_vx_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmsge_vv,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmsge_vv_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmsge_vv_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmsge_vx,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmsge_vx_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmsge_vx_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vminu_vv,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vminu_vv_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vminu_vv_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vminu_vv_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vminu_vv_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vminu_vv_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vminu_vx,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vminu_vx_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vminu_vx_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vminu_vx_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vminu_vx_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vminu_vx_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmin_vv,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmin_vv_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmin_vv_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmin_vv_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmin_vv_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmin_vv_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmin_vx,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmin_vx_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmin_vx_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmin_vx_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmin_vx_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmin_vx_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmaxu_vv,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmaxu_vv_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmaxu_vv_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmaxu_vv_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmaxu_vv_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmaxu_vv_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmaxu_vx,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmaxu_vx_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmaxu_vx_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmaxu_vx_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmaxu_vx_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmaxu_vx_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmax_vv,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmax_vv_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmax_vv_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmax_vv_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmax_vv_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmax_vv_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmax_vx,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmax_vx_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmax_vx_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmax_vx_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmax_vx_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmax_vx_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmul_vv,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmul_vv_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmul_vv_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmul_vv_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmul_vv_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmul_vv_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmul_vx,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmul_vx_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmul_vx_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmul_vx_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmul_vx_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmul_vx_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmulh_vv,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmulh_vv_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmulh_vv_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmulh_vv_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmulh_vv_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmulh_vv_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmulh_vx,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmulh_vx_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmulh_vx_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmulh_vx_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmulh_vx_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmulh_vx_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmulhu_vv,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmulhu_vv_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmulhu_vv_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmulhu_vv_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmulhu_vv_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmulhu_vv_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmulhu_vx,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmulhu_vx_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmulhu_vx_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmulhu_vx_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmulhu_vx_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmulhu_vx_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmulhsu_vv,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmulhsu_vv_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmulhsu_vv_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmulhsu_vv_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmulhsu_vv_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmulhsu_vv_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmulhsu_vx,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmulhsu_vx_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmulhsu_vx_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmulhsu_vx_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmulhsu_vx_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmulhsu_vx_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vdivu_vv,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vdivu_vv_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vdivu_vv_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vdivu_vv_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vdivu_vv_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vdivu_vv_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vdivu_vx,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vdivu_vx_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vdivu_vx_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vdivu_vx_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vdivu_vx_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vdivu_vx_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vdiv_vv,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vdiv_vv_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vdiv_vv_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vdiv_vv_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vdiv_vv_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vdiv_vv_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vdiv_vx,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vdiv_vx_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vdiv_vx_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vdiv_vx_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vdiv_vx_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vdiv_vx_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vremu_vv,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vremu_vv_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vremu_vv_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vremu_vv_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vremu_vv_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vremu_vv_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vremu_vx,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vremu_vx_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vremu_vx_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vremu_vx_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vremu_vx_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vremu_vx_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vrem_vv,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vrem_vv_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vrem_vv_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vrem_vv_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vrem_vv_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vrem_vv_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vrem_vx,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vrem_vx_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vrem_vx_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vrem_vx_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vrem_vx_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vrem_vx_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwmul_vv,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwmul_vv_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwmul_vv_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwmul_vv_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwmul_vv_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwmul_vv_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwmul_vx,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwmul_vx_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwmul_vx_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwmul_vx_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwmul_vx_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwmul_vx_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwmulu_vv,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwmulu_vv_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwmulu_vv_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwmulu_vv_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwmulu_vv_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwmulu_vv_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwmulu_vx,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwmulu_vx_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwmulu_vx_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwmulu_vx_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwmulu_vx_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwmulu_vx_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwmulsu_vv,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwmulsu_vv_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwmulsu_vv_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwmulsu_vv_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwmulsu_vv_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwmulsu_vv_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwmulsu_vx,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwmulsu_vx_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwmulsu_vx_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwmulsu_vx_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwmulsu_vx_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwmulsu_vx_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmacc_vv,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmacc_vv_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmacc_vv_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmacc_vv_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmacc_vv_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmacc_vv_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmacc_vx,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmacc_vx_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmacc_vx_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmacc_vx_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmacc_vx_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmacc_vx_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vnmsac_vv,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vnmsac_vv_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vnmsac_vv_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vnmsac_vv_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vnmsac_vv_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vnmsac_vv_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vnmsac_vx,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vnmsac_vx_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vnmsac_vx_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vnmsac_vx_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vnmsac_vx_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vnmsac_vx_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmadd_vv,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmadd_vv_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmadd_vv_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmadd_vv_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmadd_vv_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmadd_vv_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmadd_vx,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmadd_vx_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmadd_vx_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmadd_vx_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmadd_vx_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmadd_vx_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vnmsub_vv,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vnmsub_vv_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vnmsub_vv_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vnmsub_vv_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vnmsub_vv_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vnmsub_vv_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vnmsub_vx,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vnmsub_vx_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vnmsub_vx_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vnmsub_vx_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vnmsub_vx_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vnmsub_vx_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwmaccu_vv,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwmaccu_vv_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwmaccu_vv_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwmaccu_vv_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwmaccu_vv_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwmaccu_vv_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwmaccu_vx,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwmaccu_vx_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwmaccu_vx_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwmaccu_vx_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwmaccu_vx_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwmaccu_vx_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwmacc_vv,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwmacc_vv_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwmacc_vv_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwmacc_vv_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwmacc_vv_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwmacc_vv_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwmacc_vx,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwmacc_vx_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwmacc_vx_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwmacc_vx_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwmacc_vx_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwmacc_vx_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwmaccsu_vv,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwmaccsu_vv_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwmaccsu_vv_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwmaccsu_vv_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwmaccsu_vv_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwmaccsu_vv_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwmaccsu_vx,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwmaccsu_vx_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwmaccsu_vx_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwmaccsu_vx_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwmaccsu_vx_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwmaccsu_vx_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwmaccus_vx,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwmaccus_vx_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwmaccus_vx_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwmaccus_vx_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwmaccus_vx_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwmaccus_vx_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmerge_vvm,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmerge_vvm_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmerge_vxm,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmerge_vxm_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmv_v_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmv_v_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmv_v_x,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmv_v_x_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsaddu_vv,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsaddu_vv_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsaddu_vv_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsaddu_vv_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsaddu_vv_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsaddu_vv_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsaddu_vx,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsaddu_vx_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsaddu_vx_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsaddu_vx_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsaddu_vx_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsaddu_vx_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsadd_vv,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsadd_vv_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsadd_vv_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsadd_vv_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsadd_vv_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsadd_vv_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsadd_vx,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsadd_vx_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsadd_vx_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsadd_vx_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsadd_vx_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsadd_vx_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vssubu_vv,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vssubu_vv_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vssubu_vv_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vssubu_vv_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vssubu_vv_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vssubu_vv_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vssubu_vx,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vssubu_vx_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vssubu_vx_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vssubu_vx_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vssubu_vx_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vssubu_vx_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vssub_vv,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vssub_vv_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vssub_vv_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vssub_vv_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vssub_vv_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vssub_vv_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vssub_vx,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vssub_vx_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vssub_vx_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vssub_vx_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vssub_vx_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vssub_vx_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vaaddu_vv,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vaaddu_vv_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vaaddu_vv_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vaaddu_vv_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vaaddu_vv_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vaaddu_vv_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vaaddu_vx,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vaaddu_vx_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vaaddu_vx_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vaaddu_vx_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vaaddu_vx_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vaaddu_vx_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vaadd_vv,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vaadd_vv_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vaadd_vv_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vaadd_vv_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vaadd_vv_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vaadd_vv_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vaadd_vx,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vaadd_vx_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vaadd_vx_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vaadd_vx_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vaadd_vx_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vaadd_vx_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vasubu_vv,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vasubu_vv_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vasubu_vv_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vasubu_vv_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vasubu_vv_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vasubu_vv_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vasubu_vx,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vasubu_vx_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vasubu_vx_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vasubu_vx_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vasubu_vx_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vasubu_vx_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vasub_vv,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vasub_vv_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vasub_vv_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vasub_vv_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vasub_vv_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vasub_vv_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vasub_vx,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vasub_vx_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vasub_vx_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vasub_vx_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vasub_vx_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vasub_vx_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsmul_vv,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsmul_vv_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsmul_vv_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsmul_vv_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsmul_vv_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsmul_vv_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsmul_vx,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsmul_vx_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsmul_vx_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsmul_vx_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsmul_vx_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsmul_vx_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vssrl_vv,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vssrl_vv_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vssrl_vv_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vssrl_vv_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vssrl_vv_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vssrl_vv_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vssrl_vx,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vssrl_vx_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vssrl_vx_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vssrl_vx_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vssrl_vx_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vssrl_vx_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vssra_vv,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vssra_vv_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vssra_vv_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vssra_vv_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vssra_vv_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vssra_vv_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vssra_vx,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vssra_vx_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vssra_vx_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vssra_vx_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vssra_vx_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vssra_vx_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vnclipu_wv,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vnclipu_wv_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vnclipu_wv_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vnclipu_wv_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vnclipu_wv_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vnclipu_wv_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vnclipu_wx,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vnclipu_wx_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vnclipu_wx_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vnclipu_wx_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vnclipu_wx_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vnclipu_wx_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vnclip_wv,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vnclip_wv_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vnclip_wv_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vnclip_wv_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vnclip_wv_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vnclip_wv_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vnclip_wx,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vnclip_wx_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vnclip_wx_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vnclip_wx_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vnclip_wx_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vnclip_wx_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfadd_vv_rm,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfadd_vv_rm_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfadd_vv_rm_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfadd_vv_rm_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfadd_vv_rm_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfadd_vv_rm_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfadd_vf_rm,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfadd_vf_rm_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfadd_vf_rm_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfadd_vf_rm_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfadd_vf_rm_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfadd_vf_rm_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfsub_vv_rm,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfsub_vv_rm_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfsub_vv_rm_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfsub_vv_rm_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfsub_vv_rm_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfsub_vv_rm_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfsub_vf_rm,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfsub_vf_rm_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfsub_vf_rm_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfsub_vf_rm_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfsub_vf_rm_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfsub_vf_rm_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfrsub_vf_rm,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfrsub_vf_rm_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfrsub_vf_rm_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfrsub_vf_rm_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfrsub_vf_rm_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfrsub_vf_rm_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwadd_wv_rm,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwadd_wv_rm_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwadd_wv_rm_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwadd_wv_rm_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwadd_wv_rm_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwadd_wv_rm_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwadd_wf_rm,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwadd_wf_rm_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwadd_wf_rm_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwadd_wf_rm_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwadd_wf_rm_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwadd_wf_rm_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwsub_wv_rm,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwsub_wv_rm_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwsub_wv_rm_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwsub_wv_rm_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwsub_wv_rm_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwsub_wv_rm_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwsub_wf_rm,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwsub_wf_rm_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwsub_wf_rm_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwsub_wf_rm_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwsub_wf_rm_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwsub_wf_rm_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmul_vv_rm,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmul_vv_rm_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmul_vv_rm_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmul_vv_rm_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmul_vv_rm_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmul_vv_rm_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmul_vf_rm,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmul_vf_rm_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmul_vf_rm_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmul_vf_rm_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmul_vf_rm_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmul_vf_rm_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfdiv_vv_rm,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfdiv_vv_rm_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfdiv_vv_rm_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfdiv_vv_rm_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfdiv_vv_rm_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfdiv_vv_rm_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfdiv_vf_rm,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfdiv_vf_rm_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfdiv_vf_rm_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfdiv_vf_rm_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfdiv_vf_rm_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfdiv_vf_rm_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfrdiv_vf_rm,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfrdiv_vf_rm_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfrdiv_vf_rm_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfrdiv_vf_rm_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfrdiv_vf_rm_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfrdiv_vf_rm_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfadd_vv,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfadd_vv_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfadd_vv_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfadd_vv_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfadd_vv_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfadd_vv_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfadd_vf,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfadd_vf_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfadd_vf_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfadd_vf_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfadd_vf_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfadd_vf_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfsub_vv,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfsub_vv_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfsub_vv_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfsub_vv_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfsub_vv_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfsub_vv_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfsub_vf,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfsub_vf_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfsub_vf_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfsub_vf_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfsub_vf_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfsub_vf_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfrsub_vf,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfrsub_vf_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfrsub_vf_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfrsub_vf_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfrsub_vf_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfrsub_vf_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwadd_wv,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwadd_wv_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwadd_wv_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwadd_wv_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwadd_wv_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwadd_wv_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwadd_wf,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwadd_wf_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwadd_wf_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwadd_wf_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwadd_wf_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwadd_wf_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwsub_wv,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwsub_wv_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwsub_wv_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwsub_wv_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwsub_wv_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwsub_wv_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwsub_wf,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwsub_wf_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwsub_wf_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwsub_wf_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwsub_wf_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwsub_wf_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmul_vv,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmul_vv_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmul_vv_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmul_vv_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmul_vv_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmul_vv_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmul_vf,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmul_vf_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmul_vf_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmul_vf_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmul_vf_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmul_vf_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfdiv_vv,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfdiv_vv_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfdiv_vv_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfdiv_vv_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfdiv_vv_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfdiv_vv_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfdiv_vf,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfdiv_vf_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfdiv_vf_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfdiv_vf_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfdiv_vf_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfdiv_vf_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfrdiv_vf,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfrdiv_vf_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfrdiv_vf_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfrdiv_vf_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfrdiv_vf_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfrdiv_vf_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwadd_vv_rm,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwadd_vv_rm_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwadd_vv_rm_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwadd_vv_rm_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwadd_vv_rm_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwadd_vv_rm_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwadd_vf_rm,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwadd_vf_rm_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwadd_vf_rm_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwadd_vf_rm_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwadd_vf_rm_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwadd_vf_rm_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwsub_vv_rm,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwsub_vv_rm_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwsub_vv_rm_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwsub_vv_rm_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwsub_vv_rm_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwsub_vv_rm_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwsub_vf_rm,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwsub_vf_rm_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwsub_vf_rm_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwsub_vf_rm_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwsub_vf_rm_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwsub_vf_rm_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwmul_vv_rm,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwmul_vv_rm_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwmul_vv_rm_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwmul_vv_rm_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwmul_vv_rm_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwmul_vv_rm_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwmul_vf_rm,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwmul_vf_rm_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwmul_vf_rm_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwmul_vf_rm_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwmul_vf_rm_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwmul_vf_rm_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwadd_vv,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwadd_vv_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwadd_vv_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwadd_vv_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwadd_vv_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwadd_vv_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwadd_vf,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwadd_vf_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwadd_vf_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwadd_vf_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwadd_vf_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwadd_vf_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwsub_vv,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwsub_vv_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwsub_vv_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwsub_vv_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwsub_vv_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwsub_vv_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwsub_vf,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwsub_vf_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwsub_vf_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwsub_vf_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwsub_vf_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwsub_vf_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwmul_vv,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwmul_vv_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwmul_vv_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwmul_vv_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwmul_vv_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwmul_vv_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwmul_vf,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwmul_vf_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwmul_vf_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwmul_vf_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwmul_vf_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwmul_vf_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmacc_vv_rm,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmacc_vv_rm_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmacc_vv_rm_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmacc_vv_rm_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmacc_vv_rm_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmacc_vv_rm_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmacc_vf_rm,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmacc_vf_rm_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmacc_vf_rm_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmacc_vf_rm_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmacc_vf_rm_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmacc_vf_rm_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfnmacc_vv_rm,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfnmacc_vv_rm_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfnmacc_vv_rm_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfnmacc_vv_rm_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfnmacc_vv_rm_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfnmacc_vv_rm_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfnmacc_vf_rm,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfnmacc_vf_rm_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfnmacc_vf_rm_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfnmacc_vf_rm_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfnmacc_vf_rm_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfnmacc_vf_rm_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmsac_vv_rm,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmsac_vv_rm_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmsac_vv_rm_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmsac_vv_rm_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmsac_vv_rm_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmsac_vv_rm_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmsac_vf_rm,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmsac_vf_rm_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmsac_vf_rm_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmsac_vf_rm_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmsac_vf_rm_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmsac_vf_rm_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfnmsac_vv_rm,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfnmsac_vv_rm_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfnmsac_vv_rm_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfnmsac_vv_rm_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfnmsac_vv_rm_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfnmsac_vv_rm_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfnmsac_vf_rm,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfnmsac_vf_rm_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfnmsac_vf_rm_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfnmsac_vf_rm_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfnmsac_vf_rm_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfnmsac_vf_rm_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmadd_vv_rm,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmadd_vv_rm_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmadd_vv_rm_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmadd_vv_rm_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmadd_vv_rm_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmadd_vv_rm_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmadd_vf_rm,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmadd_vf_rm_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmadd_vf_rm_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmadd_vf_rm_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmadd_vf_rm_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmadd_vf_rm_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfnmadd_vv_rm,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfnmadd_vv_rm_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfnmadd_vv_rm_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfnmadd_vv_rm_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfnmadd_vv_rm_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfnmadd_vv_rm_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfnmadd_vf_rm,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfnmadd_vf_rm_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfnmadd_vf_rm_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfnmadd_vf_rm_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfnmadd_vf_rm_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfnmadd_vf_rm_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmsub_vv_rm,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmsub_vv_rm_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmsub_vv_rm_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmsub_vv_rm_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmsub_vv_rm_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmsub_vv_rm_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmsub_vf_rm,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmsub_vf_rm_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmsub_vf_rm_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmsub_vf_rm_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmsub_vf_rm_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmsub_vf_rm_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfnmsub_vv_rm,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfnmsub_vv_rm_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfnmsub_vv_rm_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfnmsub_vv_rm_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfnmsub_vv_rm_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfnmsub_vv_rm_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfnmsub_vf_rm,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfnmsub_vf_rm_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfnmsub_vf_rm_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfnmsub_vf_rm_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfnmsub_vf_rm_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfnmsub_vf_rm_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmacc_vv,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmacc_vv_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmacc_vv_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmacc_vv_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmacc_vv_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmacc_vv_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmacc_vf,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmacc_vf_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmacc_vf_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmacc_vf_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmacc_vf_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmacc_vf_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfnmacc_vv,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfnmacc_vv_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfnmacc_vv_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfnmacc_vv_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfnmacc_vv_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfnmacc_vv_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfnmacc_vf,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfnmacc_vf_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfnmacc_vf_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfnmacc_vf_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfnmacc_vf_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfnmacc_vf_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmsac_vv,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmsac_vv_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmsac_vv_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmsac_vv_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmsac_vv_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmsac_vv_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmsac_vf,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmsac_vf_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmsac_vf_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmsac_vf_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmsac_vf_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmsac_vf_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfnmsac_vv,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfnmsac_vv_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfnmsac_vv_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfnmsac_vv_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfnmsac_vv_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfnmsac_vv_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfnmsac_vf,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfnmsac_vf_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfnmsac_vf_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfnmsac_vf_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfnmsac_vf_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfnmsac_vf_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmadd_vv,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmadd_vv_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmadd_vv_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmadd_vv_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmadd_vv_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmadd_vv_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmadd_vf,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmadd_vf_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmadd_vf_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmadd_vf_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmadd_vf_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmadd_vf_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfnmadd_vv,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfnmadd_vv_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfnmadd_vv_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfnmadd_vv_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfnmadd_vv_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfnmadd_vv_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfnmadd_vf,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfnmadd_vf_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfnmadd_vf_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfnmadd_vf_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfnmadd_vf_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfnmadd_vf_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmsub_vv,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmsub_vv_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmsub_vv_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmsub_vv_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmsub_vv_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmsub_vv_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmsub_vf,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmsub_vf_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmsub_vf_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmsub_vf_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmsub_vf_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmsub_vf_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfnmsub_vv,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfnmsub_vv_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfnmsub_vv_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfnmsub_vv_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfnmsub_vv_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfnmsub_vv_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfnmsub_vf,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfnmsub_vf_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfnmsub_vf_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfnmsub_vf_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfnmsub_vf_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfnmsub_vf_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwmacc_vv_rm,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwmacc_vv_rm_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwmacc_vv_rm_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwmacc_vv_rm_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwmacc_vv_rm_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwmacc_vv_rm_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwmacc_vf_rm,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwmacc_vf_rm_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwmacc_vf_rm_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwmacc_vf_rm_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwmacc_vf_rm_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwmacc_vf_rm_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwnmacc_vv_rm,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwnmacc_vv_rm_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwnmacc_vv_rm_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwnmacc_vv_rm_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwnmacc_vv_rm_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwnmacc_vv_rm_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwnmacc_vf_rm,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwnmacc_vf_rm_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwnmacc_vf_rm_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwnmacc_vf_rm_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwnmacc_vf_rm_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwnmacc_vf_rm_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwmsac_vv_rm,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwmsac_vv_rm_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwmsac_vv_rm_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwmsac_vv_rm_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwmsac_vv_rm_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwmsac_vv_rm_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwmsac_vf_rm,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwmsac_vf_rm_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwmsac_vf_rm_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwmsac_vf_rm_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwmsac_vf_rm_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwmsac_vf_rm_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwnmsac_vv_rm,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwnmsac_vv_rm_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwnmsac_vv_rm_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwnmsac_vv_rm_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwnmsac_vv_rm_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwnmsac_vv_rm_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwnmsac_vf_rm,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwnmsac_vf_rm_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwnmsac_vf_rm_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwnmsac_vf_rm_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwnmsac_vf_rm_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwnmsac_vf_rm_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwmacc_vv,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwmacc_vv_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwmacc_vv_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwmacc_vv_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwmacc_vv_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwmacc_vv_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwmacc_vf,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwmacc_vf_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwmacc_vf_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwmacc_vf_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwmacc_vf_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwmacc_vf_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwnmacc_vv,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwnmacc_vv_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwnmacc_vv_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwnmacc_vv_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwnmacc_vv_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwnmacc_vv_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwnmacc_vf,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwnmacc_vf_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwnmacc_vf_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwnmacc_vf_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwnmacc_vf_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwnmacc_vf_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwmsac_vv,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwmsac_vv_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwmsac_vv_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwmsac_vv_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwmsac_vv_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwmsac_vv_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwmsac_vf,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwmsac_vf_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwmsac_vf_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwmsac_vf_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwmsac_vf_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwmsac_vf_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwnmsac_vv,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwnmsac_vv_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwnmsac_vv_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwnmsac_vv_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwnmsac_vv_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwnmsac_vv_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwnmsac_vf,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwnmsac_vf_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwnmsac_vf_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwnmsac_vf_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwnmsac_vf_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwnmsac_vf_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfsqrt_v_rm,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfsqrt_v_rm_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfsqrt_v_rm_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfsqrt_v_rm_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfsqrt_v_rm_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfsqrt_v_rm_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfrec7_v_rm,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfrec7_v_rm_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfrec7_v_rm_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfrec7_v_rm_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfrec7_v_rm_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfrec7_v_rm_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfsqrt_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfsqrt_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfsqrt_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfsqrt_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfsqrt_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfsqrt_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfrec7_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfrec7_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfrec7_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfrec7_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfrec7_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfrec7_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmin_vv,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmin_vv_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmin_vv_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmin_vv_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmin_vv_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmin_vv_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmin_vf,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmin_vf_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmin_vf_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmin_vf_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmin_vf_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmin_vf_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmax_vv,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmax_vv_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmax_vv_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmax_vv_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmax_vv_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmax_vv_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmax_vf,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmax_vf_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmax_vf_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmax_vf_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmax_vf_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmax_vf_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfsgnj_vv,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfsgnj_vv_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfsgnj_vv_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfsgnj_vv_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfsgnj_vv_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfsgnj_vv_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfsgnj_vf,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfsgnj_vf_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfsgnj_vf_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfsgnj_vf_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfsgnj_vf_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfsgnj_vf_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfsgnjn_vv,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfsgnjn_vv_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfsgnjn_vv_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfsgnjn_vv_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfsgnjn_vv_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfsgnjn_vv_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfsgnjn_vf,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfsgnjn_vf_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfsgnjn_vf_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfsgnjn_vf_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfsgnjn_vf_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfsgnjn_vf_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfsgnjx_vv,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfsgnjx_vv_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfsgnjx_vv_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfsgnjx_vv_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfsgnjx_vv_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfsgnjx_vv_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfsgnjx_vf,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfsgnjx_vf_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfsgnjx_vf_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfsgnjx_vf_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfsgnjx_vf_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfsgnjx_vf_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfabs_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfabs_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfabs_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfabs_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfabs_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfabs_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmfeq_vv,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmfeq_vv_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmfeq_vv_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmfeq_vf,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmfeq_vf_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmfeq_vf_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmfne_vv,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmfne_vv_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmfne_vv_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmfne_vf,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmfne_vf_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmfne_vf_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmflt_vv,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmflt_vv_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmflt_vv_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmflt_vf,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmflt_vf_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmflt_vf_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmfle_vv,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmfle_vv_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmfle_vv_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmfle_vf,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmfle_vf_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmfle_vf_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmfgt_vv,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmfgt_vv_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmfgt_vv_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmfgt_vf,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmfgt_vf_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmfgt_vf_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmfge_vv,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmfge_vv_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmfge_vv_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmfge_vf,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmfge_vf_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmfge_vf_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmerge_vfm,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmerge_vfm_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmv_v_f,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmv_v_f_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfcvt_xu_f_v_rm,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfcvt_xu_f_v_rm_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfcvt_xu_f_v_rm_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfcvt_xu_f_v_rm_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfcvt_xu_f_v_rm_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfcvt_xu_f_v_rm_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfcvt_f_x_v_rm,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfcvt_f_x_v_rm_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfcvt_f_x_v_rm_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfcvt_f_x_v_rm_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfcvt_f_x_v_rm_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfcvt_f_x_v_rm_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfcvt_f_xu_v_rm,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfcvt_f_xu_v_rm_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfcvt_f_xu_v_rm_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfcvt_f_xu_v_rm_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfcvt_f_xu_v_rm_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfcvt_f_xu_v_rm_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwcvt_x_f_v_rm,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwcvt_x_f_v_rm_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwcvt_x_f_v_rm_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwcvt_x_f_v_rm_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwcvt_x_f_v_rm_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwcvt_x_f_v_rm_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwcvt_xu_f_v_rm,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwcvt_xu_f_v_rm_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwcvt_xu_f_v_rm_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwcvt_xu_f_v_rm_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwcvt_xu_f_v_rm_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwcvt_xu_f_v_rm_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfncvt_x_f_w_rm,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfncvt_x_f_w_rm_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfncvt_x_f_w_rm_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfncvt_x_f_w_rm_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfncvt_x_f_w_rm_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfncvt_x_f_w_rm_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfncvt_xu_f_w_rm,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfncvt_xu_f_w_rm_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfncvt_xu_f_w_rm_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfncvt_xu_f_w_rm_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfncvt_xu_f_w_rm_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfncvt_xu_f_w_rm_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfncvt_f_x_w_rm,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfncvt_f_x_w_rm_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfncvt_f_x_w_rm_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfncvt_f_x_w_rm_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfncvt_f_x_w_rm_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfncvt_f_x_w_rm_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfncvt_f_xu_w_rm,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfncvt_f_xu_w_rm_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfncvt_f_xu_w_rm_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfncvt_f_xu_w_rm_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfncvt_f_xu_w_rm_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfncvt_f_xu_w_rm_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfncvt_f_f_w_rm,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfncvt_f_f_w_rm_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfncvt_f_f_w_rm_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfncvt_f_f_w_rm_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfncvt_f_f_w_rm_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfncvt_f_f_w_rm_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfcvt_x_f_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfcvt_x_f_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfcvt_x_f_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfcvt_x_f_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfcvt_x_f_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfcvt_x_f_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfcvt_xu_f_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfcvt_xu_f_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfcvt_xu_f_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfcvt_xu_f_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfcvt_xu_f_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfcvt_xu_f_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfcvt_f_x_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfcvt_f_x_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfcvt_f_x_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfcvt_f_x_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfcvt_f_x_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfcvt_f_x_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfcvt_f_xu_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfcvt_f_xu_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfcvt_f_xu_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfcvt_f_xu_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfcvt_f_xu_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfcvt_f_xu_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwcvt_x_f_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwcvt_x_f_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwcvt_x_f_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwcvt_x_f_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwcvt_x_f_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwcvt_x_f_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwcvt_xu_f_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwcvt_xu_f_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwcvt_xu_f_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwcvt_xu_f_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwcvt_xu_f_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwcvt_xu_f_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfncvt_x_f_w,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfncvt_x_f_w_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfncvt_x_f_w_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfncvt_x_f_w_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfncvt_x_f_w_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfncvt_x_f_w_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfncvt_xu_f_w,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfncvt_xu_f_w_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfncvt_xu_f_w_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfncvt_xu_f_w_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfncvt_xu_f_w_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfncvt_xu_f_w_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfncvt_f_x_w,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfncvt_f_x_w_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfncvt_f_x_w_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfncvt_f_x_w_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfncvt_f_x_w_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfncvt_f_x_w_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfncvt_f_xu_w,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfncvt_f_xu_w_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfncvt_f_xu_w_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfncvt_f_xu_w_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfncvt_f_xu_w_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfncvt_f_xu_w_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfncvt_f_f_w,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfncvt_f_f_w_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfncvt_f_f_w_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfncvt_f_f_w_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfncvt_f_f_w_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfncvt_f_f_w_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vredsum_vs,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vredsum_vs_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vredsum_vs_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vredsum_vs_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vredmaxu_vs,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vredmaxu_vs_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vredmaxu_vs_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vredmaxu_vs_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vredmax_vs,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vredmax_vs_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vredmax_vs_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vredmax_vs_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vredminu_vs,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vredminu_vs_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vredminu_vs_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vredminu_vs_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vredmin_vs,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vredmin_vs_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vredmin_vs_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vredmin_vs_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vredand_vs,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vredand_vs_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vredand_vs_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vredand_vs_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vredor_vs,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vredor_vs_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vredor_vs_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vredor_vs_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vredxor_vs,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vredxor_vs_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vredxor_vs_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vredxor_vs_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwredsum_vs,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwredsum_vs_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwredsum_vs_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwredsum_vs_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwredsumu_vs,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwredsumu_vs_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwredsumu_vs_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vwredsumu_vs_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfredmax_vs,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfredmax_vs_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfredmax_vs_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfredmax_vs_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfredmin_vs,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfredmin_vs_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfredmin_vs_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfredmin_vs_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfredusum_vs_rm,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfredusum_vs_rm_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfredusum_vs_rm_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfredusum_vs_rm_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfredosum_vs_rm,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfredosum_vs_rm_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfredosum_vs_rm_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfredosum_vs_rm_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwredusum_vs_rm,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwredusum_vs_rm_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwredusum_vs_rm_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwredusum_vs_rm_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwredosum_vs_rm,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwredosum_vs_rm_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwredosum_vs_rm_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwredosum_vs_rm_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfredusum_vs,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfredusum_vs_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfredusum_vs_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfredusum_vs_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfredosum_vs,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfredosum_vs_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfredosum_vs_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfredosum_vs_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwredusum_vs,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwredusum_vs_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwredusum_vs_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwredusum_vs_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwredosum_vs,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwredosum_vs_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwredosum_vs_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwredosum_vs_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmnot_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_viota_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_viota_m_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_viota_m_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_viota_m_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_viota_m_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_viota_m_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vid_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vid_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vid_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vid_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vid_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vid_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmv_x_s,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmv_s_x,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmv_s_x_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmv_f_s,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmv_s_f,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmv_s_f_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmv_s_x,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfmv_s_x_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vslideup_vx,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vslideup_vx_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vslideup_vx_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vslideup_vx_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vslideup_vx_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vslideup_vx_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vslidedown_vx,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vslidedown_vx_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vslidedown_vx_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vslidedown_vx_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vslidedown_vx_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vslidedown_vx_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vslide1up_vx,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vslide1up_vx_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vslide1up_vx_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vslide1up_vx_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vslide1up_vx_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vslide1up_vx_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfslide1up_vf,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfslide1up_vf_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfslide1up_vf_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfslide1up_vf_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfslide1up_vf_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfslide1up_vf_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vslide1down_vx,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vslide1down_vx_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vslide1down_vx_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vslide1down_vx_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vslide1down_vx_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vslide1down_vx_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfslide1down_vf,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfslide1down_vf_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfslide1down_vf_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfslide1down_vf_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfslide1down_vf_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfslide1down_vf_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vrgather_vv,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vrgather_vv_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vrgather_vv_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vrgather_vv_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vrgather_vv_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vrgather_vv_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vrgather_vx,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vrgather_vx_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vrgather_vx_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vrgather_vx_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vrgather_vx_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vrgather_vx_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vrgatherei16_vv,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vrgatherei16_vv_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vrgatherei16_vv_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vrgatherei16_vv_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vrgatherei16_vv_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vrgatherei16_vv_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vcompress_vm,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vcompress_vm_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vget_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vset_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vcpop_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vcpop_m_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfclass_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfclass_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfclass_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfclass_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfclass_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfclass_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfcvt_rtz_x_f_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfcvt_rtz_x_f_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfcvt_rtz_x_f_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfcvt_rtz_x_f_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfcvt_rtz_x_f_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfcvt_rtz_x_f_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfcvt_rtz_xu_f_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfcvt_rtz_xu_f_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfcvt_rtz_xu_f_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfcvt_rtz_xu_f_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfcvt_rtz_xu_f_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfcvt_rtz_xu_f_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfirst_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfirst_m_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfncvt_rod_f_f_w,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfncvt_rod_f_f_w_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfncvt_rod_f_f_w_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfncvt_rod_f_f_w_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfncvt_rod_f_f_w_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfncvt_rod_f_f_w_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfncvt_rtz_x_f_w,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfncvt_rtz_x_f_w_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfncvt_rtz_x_f_w_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfncvt_rtz_x_f_w_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfncvt_rtz_x_f_w_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfncvt_rtz_x_f_w_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfncvt_rtz_xu_f_w,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfncvt_rtz_xu_f_w_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfncvt_rtz_xu_f_w_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfncvt_rtz_xu_f_w_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfncvt_rtz_xu_f_w_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfncvt_rtz_xu_f_w_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfrsqrt7_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfrsqrt7_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfrsqrt7_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfrsqrt7_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfrsqrt7_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfrsqrt7_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwcvt_f_f_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwcvt_f_f_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwcvt_f_f_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwcvt_f_f_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwcvt_f_f_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwcvt_f_f_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwcvt_f_x_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwcvt_f_x_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwcvt_f_x_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwcvt_f_x_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwcvt_f_x_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwcvt_f_x_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwcvt_f_xu_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwcvt_f_xu_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwcvt_f_xu_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwcvt_f_xu_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwcvt_f_xu_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwcvt_f_xu_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwcvt_rtz_x_f_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwcvt_rtz_x_f_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwcvt_rtz_x_f_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwcvt_rtz_x_f_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwcvt_rtz_x_f_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwcvt_rtz_x_f_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwcvt_rtz_xu_f_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwcvt_rtz_xu_f_v_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwcvt_rtz_xu_f_v_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwcvt_rtz_xu_f_v_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwcvt_rtz_xu_f_v_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vfwcvt_rtz_xu_f_v_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlenb,"ULi", "n")
RISCVV_BUILTIN(__builtin_rvv_vlm_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlmul_ext_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vlmul_trunc_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmand_mm,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmandn_mm,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmclr_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmnand_mm,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmnor_mm,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmor_mm,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmorn_mm,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmsbf_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmsbf_m_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmsbf_m_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmset_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmsif_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmsif_m_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmsif_m_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmsof_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmsof_m_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmsof_m_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmxnor_mm,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vmxor_mm,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vreinterpret_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsetvli,"zzIzIz", "n")
RISCVV_BUILTIN(__builtin_rvv_vsetvlimax,"zIzIz", "n")
RISCVV_BUILTIN(__builtin_rvv_vsext_vf2,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsext_vf2_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsext_vf2_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsext_vf2_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsext_vf2_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsext_vf2_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsext_vf4,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsext_vf4_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsext_vf4_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsext_vf4_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsext_vf4_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsext_vf4_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsext_vf8,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsext_vf8_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsext_vf8_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsext_vf8_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsext_vf8_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsext_vf8_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vsm_v,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vundefined,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vzext_vf2,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vzext_vf2_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vzext_vf2_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vzext_vf2_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vzext_vf2_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vzext_vf2_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vzext_vf4,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vzext_vf4_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vzext_vf4_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vzext_vf4_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vzext_vf4_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vzext_vf4_mu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vzext_vf8,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vzext_vf8_tu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vzext_vf8_m,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vzext_vf8_tum,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vzext_vf8_tumu,"", "n")
RISCVV_BUILTIN(__builtin_rvv_vzext_vf8_mu,"", "n")
#undef RISCVV_BUILTIN
