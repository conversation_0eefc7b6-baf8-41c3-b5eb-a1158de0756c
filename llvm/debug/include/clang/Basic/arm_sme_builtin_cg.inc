#ifdef GET_SME_LLVM_INTRINSIC_MAP
SMEMAP1(svaddha_za32_s32_m, aarch64_sme_addha, 687194767619),
SMEMAP1(svaddha_za32_u32_m, aarch64_sme_addha, 687194767619),
SMEMAP1(svaddha_za64_s64_m, aarch64_sme_addha, 687194767620),
SMEMAP1(svaddha_za64_u64_m, aarch64_sme_addha, 687194767620),
SMEMAP1(svaddva_za32_s32_m, aarch64_sme_addva, 687194767619),
SMEMAP1(svaddva_za32_u32_m, aarch64_sme_addva, 687194767619),
SMEMAP1(svaddva_za64_s64_m, aarch64_sme_addva, 687194767620),
SMEMAP1(svaddva_za64_u64_m, aarch64_sme_addva, 687194767620),
SMEMAP1(svcntsb, aarch64_sme_cntsb, 1374390583299),
SMEMAP1(svcntsd, aarch64_sme_cntsd, 1374390583299),
SMEMAP1(svcntsh, aarch64_sme_cntsh, 1374390583299),
SMEMAP1(svcntsw, aarch64_sme_cntsw, 1374390583299),
SMEMAP1(svld1_hor_vnum_za128, aarch64_sme_ld1q_horiz, 687195824133),
SMEMAP1(svld1_hor_vnum_za16, aarch64_sme_ld1h_horiz, 687195824130),
SMEMAP1(svld1_hor_vnum_za32, aarch64_sme_ld1w_horiz, 687195824131),
SMEMAP1(svld1_hor_vnum_za64, aarch64_sme_ld1d_horiz, 687195824132),
SMEMAP1(svld1_hor_vnum_za8, aarch64_sme_ld1b_horiz, 687195824129),
SMEMAP1(svld1_hor_za128, aarch64_sme_ld1q_horiz, 687195824133),
SMEMAP1(svld1_hor_za16, aarch64_sme_ld1h_horiz, 687195824130),
SMEMAP1(svld1_hor_za32, aarch64_sme_ld1w_horiz, 687195824131),
SMEMAP1(svld1_hor_za64, aarch64_sme_ld1d_horiz, 687195824132),
SMEMAP1(svld1_hor_za8, aarch64_sme_ld1b_horiz, 687195824129),
SMEMAP1(svld1_ver_vnum_za128, aarch64_sme_ld1q_vert, 687195824133),
SMEMAP1(svld1_ver_vnum_za16, aarch64_sme_ld1h_vert, 687195824130),
SMEMAP1(svld1_ver_vnum_za32, aarch64_sme_ld1w_vert, 687195824131),
SMEMAP1(svld1_ver_vnum_za64, aarch64_sme_ld1d_vert, 687195824132),
SMEMAP1(svld1_ver_vnum_za8, aarch64_sme_ld1b_vert, 687195824129),
SMEMAP1(svld1_ver_za128, aarch64_sme_ld1q_vert, 687195824133),
SMEMAP1(svld1_ver_za16, aarch64_sme_ld1h_vert, 687195824130),
SMEMAP1(svld1_ver_za32, aarch64_sme_ld1w_vert, 687195824131),
SMEMAP1(svld1_ver_za64, aarch64_sme_ld1d_vert, 687195824132),
SMEMAP1(svld1_ver_za8, aarch64_sme_ld1b_vert, 687195824129),
SMEMAP1(svldr_vnum_za, aarch64_sme_ldr, 824634769411),
SMEMAP1(svmopa_za32_bf16_m, aarch64_sme_mopa_wide, 687194767629),
SMEMAP1(svmopa_za32_f16_m, aarch64_sme_mopa_wide, 687194767622),
SMEMAP1(svmopa_za32_f32_m, aarch64_sme_mopa, 687194767623),
SMEMAP1(svmopa_za32_s8_m, aarch64_sme_smopa_wide, 687194767617),
SMEMAP1(svmopa_za32_u8_m, aarch64_sme_umopa_wide, 687194767617),
SMEMAP1(svmopa_za64_f64_m, aarch64_sme_mopa, 687194767624),
SMEMAP1(svmopa_za64_s16_m, aarch64_sme_smopa_wide, 687194767618),
SMEMAP1(svmopa_za64_u16_m, aarch64_sme_umopa_wide, 687194767618),
SMEMAP1(svmops_za32_bf16_m, aarch64_sme_mops_wide, 687194767629),
SMEMAP1(svmops_za32_f16_m, aarch64_sme_mops_wide, 687194767622),
SMEMAP1(svmops_za32_f32_m, aarch64_sme_mops, 687194767623),
SMEMAP1(svmops_za32_s8_m, aarch64_sme_smops_wide, 687194767617),
SMEMAP1(svmops_za32_u8_m, aarch64_sme_umops_wide, 687194767617),
SMEMAP1(svmops_za64_f64_m, aarch64_sme_mops, 687194767624),
SMEMAP1(svmops_za64_s16_m, aarch64_sme_smops_wide, 687194767618),
SMEMAP1(svmops_za64_u16_m, aarch64_sme_umops_wide, 687194767618),
SMEMAP1(svread_hor_za128_bf16_m, aarch64_sme_readq_horiz, 3985729650957),
SMEMAP1(svread_hor_za128_f16_m, aarch64_sme_readq_horiz, 3985729650950),
SMEMAP1(svread_hor_za128_f32_m, aarch64_sme_readq_horiz, 3985729650951),
SMEMAP1(svread_hor_za128_f64_m, aarch64_sme_readq_horiz, 3985729650952),
SMEMAP1(svread_hor_za128_s16_m, aarch64_sme_readq_horiz, 3985729650946),
SMEMAP1(svread_hor_za128_s32_m, aarch64_sme_readq_horiz, 3985729650947),
SMEMAP1(svread_hor_za128_s64_m, aarch64_sme_readq_horiz, 3985729650948),
SMEMAP1(svread_hor_za128_s8_m, aarch64_sme_readq_horiz, 3985729650945),
SMEMAP1(svread_hor_za128_u16_m, aarch64_sme_readq_horiz, 3985729650946),
SMEMAP1(svread_hor_za128_u32_m, aarch64_sme_readq_horiz, 3985729650947),
SMEMAP1(svread_hor_za128_u64_m, aarch64_sme_readq_horiz, 3985729650948),
SMEMAP1(svread_hor_za128_u8_m, aarch64_sme_readq_horiz, 3985729650945),
SMEMAP1(svread_hor_za16_bf16_m, aarch64_sme_read_horiz, 3985729650957),
SMEMAP1(svread_hor_za16_f16_m, aarch64_sme_read_horiz, 3985729650950),
SMEMAP1(svread_hor_za16_s16_m, aarch64_sme_read_horiz, 3985729650946),
SMEMAP1(svread_hor_za16_u16_m, aarch64_sme_read_horiz, 3985729650946),
SMEMAP1(svread_hor_za32_f32_m, aarch64_sme_read_horiz, 3985729650951),
SMEMAP1(svread_hor_za32_s32_m, aarch64_sme_read_horiz, 3985729650947),
SMEMAP1(svread_hor_za32_u32_m, aarch64_sme_read_horiz, 3985729650947),
SMEMAP1(svread_hor_za64_f64_m, aarch64_sme_read_horiz, 3985729650952),
SMEMAP1(svread_hor_za64_s64_m, aarch64_sme_read_horiz, 3985729650948),
SMEMAP1(svread_hor_za64_u64_m, aarch64_sme_read_horiz, 3985729650948),
SMEMAP1(svread_hor_za8_s8_m, aarch64_sme_read_horiz, 3985729650945),
SMEMAP1(svread_hor_za8_u8_m, aarch64_sme_read_horiz, 3985729650945),
SMEMAP1(svread_ver_za128_bf16_m, aarch64_sme_readq_vert, 3985729650957),
SMEMAP1(svread_ver_za128_f16_m, aarch64_sme_readq_vert, 3985729650950),
SMEMAP1(svread_ver_za128_f32_m, aarch64_sme_readq_vert, 3985729650951),
SMEMAP1(svread_ver_za128_f64_m, aarch64_sme_readq_vert, 3985729650952),
SMEMAP1(svread_ver_za128_s16_m, aarch64_sme_readq_vert, 3985729650946),
SMEMAP1(svread_ver_za128_s32_m, aarch64_sme_readq_vert, 3985729650947),
SMEMAP1(svread_ver_za128_s64_m, aarch64_sme_readq_vert, 3985729650948),
SMEMAP1(svread_ver_za128_s8_m, aarch64_sme_readq_vert, 3985729650945),
SMEMAP1(svread_ver_za128_u16_m, aarch64_sme_readq_vert, 3985729650946),
SMEMAP1(svread_ver_za128_u32_m, aarch64_sme_readq_vert, 3985729650947),
SMEMAP1(svread_ver_za128_u64_m, aarch64_sme_readq_vert, 3985729650948),
SMEMAP1(svread_ver_za128_u8_m, aarch64_sme_readq_vert, 3985729650945),
SMEMAP1(svread_ver_za16_bf16_m, aarch64_sme_read_vert, 3985729650957),
SMEMAP1(svread_ver_za16_f16_m, aarch64_sme_read_vert, 3985729650950),
SMEMAP1(svread_ver_za16_s16_m, aarch64_sme_read_vert, 3985729650946),
SMEMAP1(svread_ver_za16_u16_m, aarch64_sme_read_vert, 3985729650946),
SMEMAP1(svread_ver_za32_f32_m, aarch64_sme_read_vert, 3985729650951),
SMEMAP1(svread_ver_za32_s32_m, aarch64_sme_read_vert, 3985729650947),
SMEMAP1(svread_ver_za32_u32_m, aarch64_sme_read_vert, 3985729650947),
SMEMAP1(svread_ver_za64_f64_m, aarch64_sme_read_vert, 3985729650952),
SMEMAP1(svread_ver_za64_s64_m, aarch64_sme_read_vert, 3985729650948),
SMEMAP1(svread_ver_za64_u64_m, aarch64_sme_read_vert, 3985729650948),
SMEMAP1(svread_ver_za8_s8_m, aarch64_sme_read_vert, 3985729650945),
SMEMAP1(svread_ver_za8_u8_m, aarch64_sme_read_vert, 3985729650945),
SMEMAP1(svst1_hor_vnum_za128, aarch64_sme_st1q_horiz, 1786707460101),
SMEMAP1(svst1_hor_vnum_za16, aarch64_sme_st1h_horiz, 1786707460098),
SMEMAP1(svst1_hor_vnum_za32, aarch64_sme_st1w_horiz, 1786707460099),
SMEMAP1(svst1_hor_vnum_za64, aarch64_sme_st1d_horiz, 1786707460100),
SMEMAP1(svst1_hor_vnum_za8, aarch64_sme_st1b_horiz, 1786707460097),
SMEMAP1(svst1_hor_za128, aarch64_sme_st1q_horiz, 1786707460101),
SMEMAP1(svst1_hor_za16, aarch64_sme_st1h_horiz, 1786707460098),
SMEMAP1(svst1_hor_za32, aarch64_sme_st1w_horiz, 1786707460099),
SMEMAP1(svst1_hor_za64, aarch64_sme_st1d_horiz, 1786707460100),
SMEMAP1(svst1_hor_za8, aarch64_sme_st1b_horiz, 1786707460097),
SMEMAP1(svst1_ver_vnum_za128, aarch64_sme_st1q_vert, 1786707460101),
SMEMAP1(svst1_ver_vnum_za16, aarch64_sme_st1h_vert, 1786707460098),
SMEMAP1(svst1_ver_vnum_za32, aarch64_sme_st1w_vert, 1786707460099),
SMEMAP1(svst1_ver_vnum_za64, aarch64_sme_st1d_vert, 1786707460100),
SMEMAP1(svst1_ver_vnum_za8, aarch64_sme_st1b_vert, 1786707460097),
SMEMAP1(svst1_ver_za128, aarch64_sme_st1q_vert, 1786707460101),
SMEMAP1(svst1_ver_za16, aarch64_sme_st1h_vert, 1786707460098),
SMEMAP1(svst1_ver_za32, aarch64_sme_st1w_vert, 1786707460099),
SMEMAP1(svst1_ver_za64, aarch64_sme_st1d_vert, 1786707460100),
SMEMAP1(svst1_ver_za8, aarch64_sme_st1b_vert, 1786707460097),
SMEMAP1(svstr_vnum_za, aarch64_sme_str, 1924146397187),
SMEMAP1(svsumopa_za32_s8_m, aarch64_sme_sumopa_wide, 687194767617),
SMEMAP1(svsumopa_za64_s16_m, aarch64_sme_sumopa_wide, 687194767618),
SMEMAP1(svsumops_za32_s8_m, aarch64_sme_sumops_wide, 687194767617),
SMEMAP1(svsumops_za64_s16_m, aarch64_sme_sumops_wide, 687194767618),
SMEMAP1(svusmopa_za32_u8_m, aarch64_sme_usmopa_wide, 687194767617),
SMEMAP1(svusmopa_za64_u16_m, aarch64_sme_usmopa_wide, 687194767618),
SMEMAP1(svusmops_za32_u8_m, aarch64_sme_usmops_wide, 687194767617),
SMEMAP1(svusmops_za64_u16_m, aarch64_sme_usmops_wide, 687194767618),
SMEMAP1(svwrite_hor_za128_bf16_m, aarch64_sme_writeq_horiz, 5085241278733),
SMEMAP1(svwrite_hor_za128_f16_m, aarch64_sme_writeq_horiz, 5085241278726),
SMEMAP1(svwrite_hor_za128_f32_m, aarch64_sme_writeq_horiz, 5085241278727),
SMEMAP1(svwrite_hor_za128_f64_m, aarch64_sme_writeq_horiz, 5085241278728),
SMEMAP1(svwrite_hor_za128_s16_m, aarch64_sme_writeq_horiz, 5085241278722),
SMEMAP1(svwrite_hor_za128_s32_m, aarch64_sme_writeq_horiz, 5085241278723),
SMEMAP1(svwrite_hor_za128_s64_m, aarch64_sme_writeq_horiz, 5085241278724),
SMEMAP1(svwrite_hor_za128_s8_m, aarch64_sme_writeq_horiz, 5085241278721),
SMEMAP1(svwrite_hor_za128_u16_m, aarch64_sme_writeq_horiz, 5085241278722),
SMEMAP1(svwrite_hor_za128_u32_m, aarch64_sme_writeq_horiz, 5085241278723),
SMEMAP1(svwrite_hor_za128_u64_m, aarch64_sme_writeq_horiz, 5085241278724),
SMEMAP1(svwrite_hor_za128_u8_m, aarch64_sme_writeq_horiz, 5085241278721),
SMEMAP1(svwrite_hor_za16_bf16_m, aarch64_sme_write_horiz, 5085241278733),
SMEMAP1(svwrite_hor_za16_f16_m, aarch64_sme_write_horiz, 5085241278726),
SMEMAP1(svwrite_hor_za16_s16_m, aarch64_sme_write_horiz, 5085241278722),
SMEMAP1(svwrite_hor_za16_u16_m, aarch64_sme_write_horiz, 5085241278722),
SMEMAP1(svwrite_hor_za32_f32_m, aarch64_sme_write_horiz, 5085241278727),
SMEMAP1(svwrite_hor_za32_s32_m, aarch64_sme_write_horiz, 5085241278723),
SMEMAP1(svwrite_hor_za32_u32_m, aarch64_sme_write_horiz, 5085241278723),
SMEMAP1(svwrite_hor_za64_f64_m, aarch64_sme_write_horiz, 5085241278728),
SMEMAP1(svwrite_hor_za64_s64_m, aarch64_sme_write_horiz, 5085241278724),
SMEMAP1(svwrite_hor_za64_u64_m, aarch64_sme_write_horiz, 5085241278724),
SMEMAP1(svwrite_hor_za8_s8_m, aarch64_sme_write_horiz, 5085241278721),
SMEMAP1(svwrite_hor_za8_u8_m, aarch64_sme_write_horiz, 5085241278721),
SMEMAP1(svwrite_ver_za128_bf16_m, aarch64_sme_writeq_vert, 5085241278733),
SMEMAP1(svwrite_ver_za128_f16_m, aarch64_sme_writeq_vert, 5085241278726),
SMEMAP1(svwrite_ver_za128_f32_m, aarch64_sme_writeq_vert, 5085241278727),
SMEMAP1(svwrite_ver_za128_f64_m, aarch64_sme_writeq_vert, 5085241278728),
SMEMAP1(svwrite_ver_za128_s16_m, aarch64_sme_writeq_vert, 5085241278722),
SMEMAP1(svwrite_ver_za128_s32_m, aarch64_sme_writeq_vert, 5085241278723),
SMEMAP1(svwrite_ver_za128_s64_m, aarch64_sme_writeq_vert, 5085241278724),
SMEMAP1(svwrite_ver_za128_s8_m, aarch64_sme_writeq_vert, 5085241278721),
SMEMAP1(svwrite_ver_za128_u16_m, aarch64_sme_writeq_vert, 5085241278722),
SMEMAP1(svwrite_ver_za128_u32_m, aarch64_sme_writeq_vert, 5085241278723),
SMEMAP1(svwrite_ver_za128_u64_m, aarch64_sme_writeq_vert, 5085241278724),
SMEMAP1(svwrite_ver_za128_u8_m, aarch64_sme_writeq_vert, 5085241278721),
SMEMAP1(svwrite_ver_za16_bf16_m, aarch64_sme_write_vert, 5085241278733),
SMEMAP1(svwrite_ver_za16_f16_m, aarch64_sme_write_vert, 5085241278726),
SMEMAP1(svwrite_ver_za16_s16_m, aarch64_sme_write_vert, 5085241278722),
SMEMAP1(svwrite_ver_za16_u16_m, aarch64_sme_write_vert, 5085241278722),
SMEMAP1(svwrite_ver_za32_f32_m, aarch64_sme_write_vert, 5085241278727),
SMEMAP1(svwrite_ver_za32_s32_m, aarch64_sme_write_vert, 5085241278723),
SMEMAP1(svwrite_ver_za32_u32_m, aarch64_sme_write_vert, 5085241278723),
SMEMAP1(svwrite_ver_za64_f64_m, aarch64_sme_write_vert, 5085241278728),
SMEMAP1(svwrite_ver_za64_s64_m, aarch64_sme_write_vert, 5085241278724),
SMEMAP1(svwrite_ver_za64_u64_m, aarch64_sme_write_vert, 5085241278724),
SMEMAP1(svwrite_ver_za8_s8_m, aarch64_sme_write_vert, 5085241278721),
SMEMAP1(svwrite_ver_za8_u8_m, aarch64_sme_write_vert, 5085241278721),
SMEMAP1(svzero_mask_za, aarch64_sme_zero, 824634769411),
SMEMAP1(svzero_za, aarch64_sme_zero, 824634769411),
#endif

