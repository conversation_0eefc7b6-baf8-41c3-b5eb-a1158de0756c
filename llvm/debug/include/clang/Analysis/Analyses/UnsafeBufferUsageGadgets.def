//=- UnsafeBufferUsageGadgets.def - List of ways to use a buffer --*- C++ -*-=//
//
// Part of the LLVM Project, under the Apache License v2.0 with LLVM Exceptions.
// See https://llvm.org/LICENSE.txt for license information.
// SPDX-License-Identifier: Apache-2.0 WITH LLVM-exception
//
//===----------------------------------------------------------------------===//

/// A gadget is an individual operation in the code that may be of interest to
/// the UnsafeBufferUsage analysis.
#ifndef GADGET
#define GADGET(name)
#endif

/// Unsafe gadgets correspond to unsafe code patterns that warrant
/// an immediate warning.
#ifndef WARNING_GADGET
#define WARNING_GADGET(name) GADGET(name)
#endif

/// Safe gadgets correspond to code patterns that aren't unsafe but need to be
/// properly recognized in order to emit correct warnings and fixes over unsafe
/// gadgets.
#ifndef FIXABLE_GADGET
#define FIXABLE_GADGET(name) GADGET(name)
#endif

WARNING_GADGET(Increment)
WARNING_GADGET(Decrement)
WARNING_GADGET(ArraySubscript)
WARNING_GADGET(PointerArithmetic)
WARNING_GADGET(UnsafeBufferUsageAttr)
FIXABLE_GADGET(ULCArraySubscript)          // `DRE[any]` in an Unspecified Lvalue Context
FIXABLE_GADGET(DerefSimplePtrArithFixable)
FIXABLE_GADGET(PointerDereference)
FIXABLE_GADGET(UPCAddressofArraySubscript) // '&DRE[any]' in an Unspecified Pointer Context
FIXABLE_GADGET(UPCStandalonePointer)
FIXABLE_GADGET(UPCPreIncrement)            // '++Ptr' in an Unspecified Pointer Context
FIXABLE_GADGET(PointerAssignment)
FIXABLE_GADGET(PointerInit)

#undef FIXABLE_GADGET
#undef WARNING_GADGET
#undef GADGET
