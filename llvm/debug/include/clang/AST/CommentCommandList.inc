/*===- TableGen'erated file -------------------------------------*- C++ -*-===*\
|*                                                                            *|
|* A list of commands useable in documentation comments                       *|
|*                                                                            *|
|* Automatically generated file, do not edit!                                 *|
|*                                                                            *|
\*===----------------------------------------------------------------------===*/

#ifndef COMMENT_COMMAND
#  define COMMENT_COMMAND(NAME)
#endif
COMMENT_COMMAND(a)
COMMENT_COMMAND(abstract)
COMMENT_COMMAND(addindex)
COMMENT_COMMAND(addtogroup)
COMMENT_COMMAND(anchor)
COMMENT_COMMAND(arg)
COMMENT_COMMAND(attention)
COMMENT_COMMAND(author)
COMMENT_COMMAND(authors)
COMMENT_COMMAND(b)
COMMENT_COMMAND(brief)
COMMENT_COMMAND(bug)
COMMENT_COMMAND(c)
COMMENT_COMMAND(callgraph)
COMMENT_COMMAND(callback)
COMMENT_COMMAND(callergraph)
COMMENT_COMMAND(category)
COMMENT_COMMAND(cite)
COMMENT_COMMAND(class)
COMMENT_COMMAND(classdesign)
COMMENT_COMMAND(coclass)
COMMENT_COMMAND(code)
COMMENT_COMMAND(endcode)
COMMENT_COMMAND(concept)
COMMENT_COMMAND(cond)
COMMENT_COMMAND(const)
COMMENT_COMMAND(constant)
COMMENT_COMMAND(copybrief)
COMMENT_COMMAND(copydetails)
COMMENT_COMMAND(copydoc)
COMMENT_COMMAND(copyright)
COMMENT_COMMAND(date)
COMMENT_COMMAND(def)
COMMENT_COMMAND(defgroup)
COMMENT_COMMAND(dependency)
COMMENT_COMMAND(deprecated)
COMMENT_COMMAND(details)
COMMENT_COMMAND(diafile)
COMMENT_COMMAND(dir)
COMMENT_COMMAND(discussion)
COMMENT_COMMAND(docbookinclude)
COMMENT_COMMAND(docbookonly)
COMMENT_COMMAND(enddocbookonly)
COMMENT_COMMAND(dontinclude)
COMMENT_COMMAND(dot)
COMMENT_COMMAND(enddot)
COMMENT_COMMAND(dotfile)
COMMENT_COMMAND(e)
COMMENT_COMMAND(else)
COMMENT_COMMAND(elseif)
COMMENT_COMMAND(em)
COMMENT_COMMAND(emoji)
COMMENT_COMMAND(endcond)
COMMENT_COMMAND(endif)
COMMENT_COMMAND(enum)
COMMENT_COMMAND(example)
COMMENT_COMMAND(exception)
COMMENT_COMMAND(extends)
COMMENT_COMMAND(flbrace)
COMMENT_COMMAND(frbrace)
COMMENT_COMMAND(flsquare)
COMMENT_COMMAND(frsquare)
COMMENT_COMMAND(fdollar)
COMMENT_COMMAND(flparen)
COMMENT_COMMAND(frparen)
COMMENT_COMMAND(file)
COMMENT_COMMAND(fn)
COMMENT_COMMAND(function)
COMMENT_COMMAND(functiongroup)
COMMENT_COMMAND(headerfile)
COMMENT_COMMAND(helper)
COMMENT_COMMAND(helperclass)
COMMENT_COMMAND(helps)
COMMENT_COMMAND(hidecallgraph)
COMMENT_COMMAND(hidecallergraph)
COMMENT_COMMAND(hideinitializer)
COMMENT_COMMAND(hiderefby)
COMMENT_COMMAND(hiderefs)
COMMENT_COMMAND(htmlinclude)
COMMENT_COMMAND(htmlonly)
COMMENT_COMMAND(endhtmlonly)
COMMENT_COMMAND(idlexcept)
COMMENT_COMMAND(if)
COMMENT_COMMAND(ifnot)
COMMENT_COMMAND(image)
COMMENT_COMMAND(implements)
COMMENT_COMMAND(include)
COMMENT_COMMAND(ingroup)
COMMENT_COMMAND(instancesize)
COMMENT_COMMAND(interface)
COMMENT_COMMAND(internal)
COMMENT_COMMAND(endinternal)
COMMENT_COMMAND(invariant)
COMMENT_COMMAND(latexinclude)
COMMENT_COMMAND(latexonly)
COMMENT_COMMAND(endlatexonly)
COMMENT_COMMAND(li)
COMMENT_COMMAND(line)
COMMENT_COMMAND(link)
COMMENT_COMMAND(slashlink)
COMMENT_COMMAND(mainpage)
COMMENT_COMMAND(maninclude)
COMMENT_COMMAND(manonly)
COMMENT_COMMAND(endmanonly)
COMMENT_COMMAND(memberof)
COMMENT_COMMAND(method)
COMMENT_COMMAND(methodgroup)
COMMENT_COMMAND(msc)
COMMENT_COMMAND(endmsc)
COMMENT_COMMAND(mscfile)
COMMENT_COMMAND(n)
COMMENT_COMMAND(name)
COMMENT_COMMAND(namespace)
COMMENT_COMMAND(noop)
COMMENT_COMMAND(nosubgrouping)
COMMENT_COMMAND(note)
COMMENT_COMMAND(overload)
COMMENT_COMMAND(ownership)
COMMENT_COMMAND(p)
COMMENT_COMMAND(page)
COMMENT_COMMAND(par)
COMMENT_COMMAND(parblock)
COMMENT_COMMAND(endparblock)
COMMENT_COMMAND(paragraph)
COMMENT_COMMAND(param)
COMMENT_COMMAND(performance)
COMMENT_COMMAND(post)
COMMENT_COMMAND(pre)
COMMENT_COMMAND(private)
COMMENT_COMMAND(privatesection)
COMMENT_COMMAND(property)
COMMENT_COMMAND(protected)
COMMENT_COMMAND(protectedsection)
COMMENT_COMMAND(protocol)
COMMENT_COMMAND(public)
COMMENT_COMMAND(publicsection)
COMMENT_COMMAND(pure)
COMMENT_COMMAND(ref)
COMMENT_COMMAND(refitem)
COMMENT_COMMAND(related)
COMMENT_COMMAND(relatedalso)
COMMENT_COMMAND(relates)
COMMENT_COMMAND(relatesalso)
COMMENT_COMMAND(remark)
COMMENT_COMMAND(remarks)
COMMENT_COMMAND(result)
COMMENT_COMMAND(return)
COMMENT_COMMAND(returns)
COMMENT_COMMAND(retval)
COMMENT_COMMAND(rtfinclude)
COMMENT_COMMAND(rtfonly)
COMMENT_COMMAND(endrtfonly)
COMMENT_COMMAND(sa)
COMMENT_COMMAND(secreflist)
COMMENT_COMMAND(endsecreflist)
COMMENT_COMMAND(section)
COMMENT_COMMAND(security)
COMMENT_COMMAND(see)
COMMENT_COMMAND(seealso)
COMMENT_COMMAND(short)
COMMENT_COMMAND(showinitializer)
COMMENT_COMMAND(showrefby)
COMMENT_COMMAND(showrefs)
COMMENT_COMMAND(since)
COMMENT_COMMAND(skip)
COMMENT_COMMAND(skipline)
COMMENT_COMMAND(snippet)
COMMENT_COMMAND(static)
COMMENT_COMMAND(struct)
COMMENT_COMMAND(subpage)
COMMENT_COMMAND(subsection)
COMMENT_COMMAND(subsubsection)
COMMENT_COMMAND(superclass)
COMMENT_COMMAND(tableofcontents)
COMMENT_COMMAND(template)
COMMENT_COMMAND(templatefield)
COMMENT_COMMAND(test)
COMMENT_COMMAND(textblock)
COMMENT_COMMAND(slashtextblock)
COMMENT_COMMAND(throw)
COMMENT_COMMAND(throws)
COMMENT_COMMAND(todo)
COMMENT_COMMAND(tparam)
COMMENT_COMMAND(typedef)
COMMENT_COMMAND(startuml)
COMMENT_COMMAND(enduml)
COMMENT_COMMAND(union)
COMMENT_COMMAND(until)
COMMENT_COMMAND(var)
COMMENT_COMMAND(verbinclude)
COMMENT_COMMAND(verbatim)
COMMENT_COMMAND(endverbatim)
COMMENT_COMMAND(version)
COMMENT_COMMAND(warning)
COMMENT_COMMAND(weakgroup)
COMMENT_COMMAND(xrefitem)
COMMENT_COMMAND(xmlinclude)
COMMENT_COMMAND(xmlonly)
COMMENT_COMMAND(endxmlonly)
