//===--- OperationKinds.def - Operations Database ---------------*- C++ -*-===//
//
// Part of the LLVM Project, under the Apache License v2.0 with LLVM Exceptions.
// See https://llvm.org/LICENSE.txt for license information.
// SPDX-License-Identifier: Apache-2.0 WITH LLVM-exception
//
//===----------------------------------------------------------------------===//
//
// This file enumerates the different kinds of operations that can be
// performed by various expressions.
//
//===----------------------------------------------------------------------===//
//
/// @file OperationKinds.def
///
/// In this file, each of the C/C++ operations is enumerated CAST_OPERATION,
/// BINARY_OPERATION or UNARY_OPERATION macro, each of which can be specified by
/// the code including this file.
///
/// Macros had one or two arguments:
///
/// Name: The name of the operation. Name (prefixed with CK_, UO_ or BO_) will
/// be the name of the corresponding enumerator (see OperationsKinds.h).
///
/// Spelling: A string that provides a canonical spelling for the operation.

#ifndef CAST_OPERATION
#  define CAST_OPERATION(Name)
#endif

#ifndef BINARY_OPERATION
#  define BINARY_OPERATION(Name, Spelling)
#endif

#ifndef UNARY_OPERATION
#  define UNARY_OPERATION(Name, Spelling)
#endif

//===- Cast Operations  ---------------------------------------------------===//

/// CK_Dependent - A conversion which cannot yet be analyzed because
/// either the expression or target type is dependent.  These are
/// created only for explicit casts; dependent ASTs aren't required
/// to even approximately type-check.
///   (T*) malloc(sizeof(T))
///   reinterpret_cast<intptr_t>(A<T>::alloc());
CAST_OPERATION(Dependent)

/// CK_BitCast - A conversion which causes a bit pattern of one type
/// to be reinterpreted as a bit pattern of another type.  Generally
/// the operands must have equivalent size and unrelated types.
///
/// The pointer conversion char* -> int* is a bitcast.  A conversion
/// from any pointer type to a C pointer type is a bitcast unless
/// it's actually BaseToDerived or DerivedToBase.  A conversion to a
/// block pointer or ObjC pointer type is a bitcast only if the
/// operand has the same type kind; otherwise, it's one of the
/// specialized casts below.
///
/// Vector coercions are bitcasts.
CAST_OPERATION(BitCast)

/// CK_LValueBitCast - A conversion which reinterprets the address of
/// an l-value as an l-value of a different kind.  Used for
/// reinterpret_casts of l-value expressions to reference types.
///    bool b; reinterpret_cast<char&>(b) = 'a';
CAST_OPERATION(LValueBitCast)

/// CK_LValueToRValueBitCast - A conversion that causes us to reinterpret the
/// object representation of an lvalue as an rvalue. Created by
/// __builtin_bit_cast.
CAST_OPERATION(LValueToRValueBitCast)

/// CK_LValueToRValue - A conversion which causes the extraction of
/// an r-value from the operand gl-value.  The result of an r-value
/// conversion is always unqualified.
CAST_OPERATION(LValueToRValue)

/// CK_NoOp - A conversion which does not affect the type other than
/// (possibly) adding qualifiers or removing noexcept.
///   int    -> int
///   char** -> const char * const *
///   void () noexcept -> void ()
CAST_OPERATION(NoOp)

/// CK_BaseToDerived - A conversion from a C++ class pointer/reference
/// to a derived class pointer/reference.
///   B *b = static_cast<B*>(a);
CAST_OPERATION(BaseToDerived)

/// CK_DerivedToBase - A conversion from a C++ class pointer
/// to a base class pointer.
///   A *a = new B();
CAST_OPERATION(DerivedToBase)

/// CK_UncheckedDerivedToBase - A conversion from a C++ class
/// pointer/reference to a base class that can assume that the
/// derived pointer is not null.
///   const A &a = B();
///   b->method_from_a();
CAST_OPERATION(UncheckedDerivedToBase)

/// CK_Dynamic - A C++ dynamic_cast.
CAST_OPERATION(Dynamic)

/// CK_ToUnion - The GCC cast-to-union extension.
///   int   -> union { int x; float y; }
///   float -> union { int x; float y; }
CAST_OPERATION(ToUnion)

/// CK_ArrayToPointerDecay - Array to pointer decay.
///   int[10] -> int*
///   char[5][6] -> char(*)[6]
CAST_OPERATION(ArrayToPointerDecay)

/// CK_FunctionToPointerDecay - Function to pointer decay.
///   void(int) -> void(*)(int)
CAST_OPERATION(FunctionToPointerDecay)

/// CK_NullToPointer - Null pointer constant to pointer, ObjC
/// pointer, or block pointer.
///   (void*) 0
///   void (^block)() = 0;
CAST_OPERATION(NullToPointer)

/// CK_NullToMemberPointer - Null pointer constant to member pointer.
///   int A::*mptr = 0;
///   int (A::*fptr)(int) = nullptr;
CAST_OPERATION(NullToMemberPointer)

/// CK_BaseToDerivedMemberPointer - Member pointer in base class to
/// member pointer in derived class.
///   int B::*mptr = &A::member;
CAST_OPERATION(BaseToDerivedMemberPointer)

/// CK_DerivedToBaseMemberPointer - Member pointer in derived class to
/// member pointer in base class.
///   int A::*mptr = static_cast<int A::*>(&B::member);
CAST_OPERATION(DerivedToBaseMemberPointer)

/// CK_MemberPointerToBoolean - Member pointer to boolean.  A check
/// against the null member pointer.
CAST_OPERATION(MemberPointerToBoolean)

/// CK_ReinterpretMemberPointer - Reinterpret a member pointer as a
/// different kind of member pointer.  C++ forbids this from
/// crossing between function and object types, but otherwise does
/// not restrict it.  However, the only operation that is permitted
/// on a "punned" member pointer is casting it back to the original
/// type, which is required to be a lossless operation (although
/// many ABIs do not guarantee this on all possible intermediate types).
CAST_OPERATION(ReinterpretMemberPointer)

/// CK_UserDefinedConversion - Conversion using a user defined type
/// conversion function.
///    struct A { operator int(); }; int i = int(A());
CAST_OPERATION(UserDefinedConversion)

/// CK_ConstructorConversion - Conversion by constructor.
///    struct A { A(int); }; A a = A(10);
CAST_OPERATION(ConstructorConversion)

/// CK_IntegralToPointer - Integral to pointer.  A special kind of
/// reinterpreting conversion.  Applies to normal, ObjC, and block
/// pointers.
///    (char*) 0x1001aab0
///    reinterpret_cast<int*>(0)
CAST_OPERATION(IntegralToPointer)

/// CK_PointerToIntegral - Pointer to integral.  A special kind of
/// reinterpreting conversion.  Applies to normal, ObjC, and block
/// pointers.
///    (intptr_t) "help!"
CAST_OPERATION(PointerToIntegral)

/// CK_PointerToBoolean - Pointer to boolean conversion.  A check
/// against null.  Applies to normal, ObjC, and block pointers.
CAST_OPERATION(PointerToBoolean)

/// CK_ToVoid - Cast to void, discarding the computed value.
///    (void) malloc(2048)
CAST_OPERATION(ToVoid)

/// CK_MatrixCast - A cast between matrix types of the same dimensions.
CAST_OPERATION(MatrixCast)

/// CK_VectorSplat - A conversion from an arithmetic type to a
/// vector of that element type.  Fills all elements ("splats") with
/// the source value.
///    __attribute__((ext_vector_type(4))) int v = 5;
CAST_OPERATION(VectorSplat)

/// CK_IntegralCast - A cast between integral types (other than to
/// boolean).  Variously a bitcast, a truncation, a sign-extension,
/// or a zero-extension.
///    long l = 5;
///    (unsigned) i
CAST_OPERATION(IntegralCast)

/// CK_IntegralToBoolean - Integral to boolean.  A check against zero.
///    (bool) i
CAST_OPERATION(IntegralToBoolean)

/// CK_IntegralToFloating - Integral to floating point.
///    float f = i;
CAST_OPERATION(IntegralToFloating)

/// CK_FloatingToFixedPoint - Floating to fixed point.
///    _Accum a = f;
CAST_OPERATION(FloatingToFixedPoint)

/// CK_FixedPointToFloating - Fixed point to floating.
///    (float) 2.5k
CAST_OPERATION(FixedPointToFloating)

/// CK_FixedPointCast - Fixed point to fixed point.
///    (_Accum) 0.5r
CAST_OPERATION(FixedPointCast)

/// CK_FixedPointToIntegral - Fixed point to integral.
///    (int) 2.0k
CAST_OPERATION(FixedPointToIntegral)

/// CK_IntegralToFixedPoint - Integral to a fixed point.
///    (_Accum) 2
CAST_OPERATION(IntegralToFixedPoint)

/// CK_FixedPointToBoolean - Fixed point to boolean.
///    (bool) 0.5r
CAST_OPERATION(FixedPointToBoolean)

/// CK_FloatingToIntegral - Floating point to integral.  Rounds
/// towards zero, discarding any fractional component.
///    (int) f
CAST_OPERATION(FloatingToIntegral)

/// CK_FloatingToBoolean - Floating point to boolean.
///    (bool) f
CAST_OPERATION(FloatingToBoolean)

// CK_BooleanToSignedIntegral - Convert a boolean to -1 or 0 for true and
// false, respectively.
CAST_OPERATION(BooleanToSignedIntegral)

/// CK_FloatingCast - Casting between floating types of different size.
///    (double) f
///    (float) ld
CAST_OPERATION(FloatingCast)

/// CK_CPointerToObjCPointerCast - Casting a C pointer kind to an
/// Objective-C pointer.
CAST_OPERATION(CPointerToObjCPointerCast)

/// CK_BlockPointerToObjCPointerCast - Casting a block pointer to an
/// ObjC pointer.
CAST_OPERATION(BlockPointerToObjCPointerCast)

/// CK_AnyPointerToBlockPointerCast - Casting any non-block pointer
/// to a block pointer.  Block-to-block casts are bitcasts.
CAST_OPERATION(AnyPointerToBlockPointerCast)

/// Converting between two Objective-C object types, which
/// can occur when performing reference binding to an Objective-C
/// object.
CAST_OPERATION(ObjCObjectLValueCast)

/// A conversion of a floating point real to a floating point
/// complex of the original type.  Injects the value as the real
/// component with a zero imaginary component.
///   float -> _Complex float
CAST_OPERATION(FloatingRealToComplex)

/// Converts a floating point complex to floating point real
/// of the source's element type.  Just discards the imaginary
/// component.
///   _Complex long double -> long double
CAST_OPERATION(FloatingComplexToReal)

/// Converts a floating point complex to bool by comparing
/// against 0+0i.
CAST_OPERATION(FloatingComplexToBoolean)

/// Converts between different floating point complex types.
///   _Complex float -> _Complex double
CAST_OPERATION(FloatingComplexCast)

/// Converts from a floating complex to an integral complex.
///   _Complex float -> _Complex int
CAST_OPERATION(FloatingComplexToIntegralComplex)

/// Converts from an integral real to an integral complex
/// whose element type matches the source.  Injects the value as
/// the real component with a zero imaginary component.
///   long -> _Complex long
CAST_OPERATION(IntegralRealToComplex)

/// Converts an integral complex to an integral real of the
/// source's element type by discarding the imaginary component.
///   _Complex short -> short
CAST_OPERATION(IntegralComplexToReal)

/// Converts an integral complex to bool by comparing against
/// 0+0i.
CAST_OPERATION(IntegralComplexToBoolean)

/// Converts between different integral complex types.
///   _Complex char -> _Complex long long
///   _Complex unsigned int -> _Complex signed int
CAST_OPERATION(IntegralComplexCast)

/// Converts from an integral complex to a floating complex.
///   _Complex unsigned -> _Complex float
CAST_OPERATION(IntegralComplexToFloatingComplex)

/// [ARC] Produces a retainable object pointer so that it may
/// be consumed, e.g. by being passed to a consuming parameter.
/// Calls objc_retain.
CAST_OPERATION(ARCProduceObject)

/// [ARC] Consumes a retainable object pointer that has just
/// been produced, e.g. as the return value of a retaining call.
/// Enters a cleanup to call objc_release at some indefinite time.
CAST_OPERATION(ARCConsumeObject)

/// [ARC] Reclaim a retainable object pointer object that may
/// have been produced and autoreleased as part of a function return
/// sequence.
CAST_OPERATION(ARCReclaimReturnedObject)

/// [ARC] Causes a value of block type to be copied to the
/// heap, if it is not already there.  A number of other operations
/// in ARC cause blocks to be copied; this is for cases where that
/// would not otherwise be guaranteed, such as when casting to a
/// non-block pointer type.
CAST_OPERATION(ARCExtendBlockObject)

/// Converts from _Atomic(T) to T.
CAST_OPERATION(AtomicToNonAtomic)
/// Converts from T to _Atomic(T).
CAST_OPERATION(NonAtomicToAtomic)

/// Causes a block literal to by copied to the heap and then
/// autoreleased.
///
/// This particular cast kind is used for the conversion from a C++11
/// lambda expression to a block pointer.
CAST_OPERATION(CopyAndAutoreleaseBlockObject)

// Convert a builtin function to a function pointer; only allowed in the
// callee of a call expression.
CAST_OPERATION(BuiltinFnToFnPtr)

// Convert a zero value for OpenCL opaque types initialization (event_t,
// queue_t, etc.)
CAST_OPERATION(ZeroToOCLOpaqueType)

// Convert a pointer to a different address space.
CAST_OPERATION(AddressSpaceConversion)

// Convert an integer initializer to an OpenCL sampler.
CAST_OPERATION(IntToOCLSampler)

//===- Binary Operations  -------------------------------------------------===//
// Operators listed in order of precedence.
// Note that additions to this should also update the StmtVisitor class,
// BinaryOperator::getOverloadedOperator and CXBinaryOperatorKind enum.

// [C++ 5.5] Pointer-to-member operators.
BINARY_OPERATION(PtrMemD, ".*")
BINARY_OPERATION(PtrMemI, "->*")
// [C99 6.5.5] Multiplicative operators.
BINARY_OPERATION(Mul, "*")
BINARY_OPERATION(Div, "/")
BINARY_OPERATION(Rem, "%")
// [C99 6.5.6] Additive operators.
BINARY_OPERATION(Add, "+")
BINARY_OPERATION(Sub, "-")
// [C99 6.5.7] Bitwise shift operators.
BINARY_OPERATION(Shl, "<<")
BINARY_OPERATION(Shr, ">>")
// C++20 [expr.spaceship] Three-way comparison operator.
BINARY_OPERATION(Cmp, "<=>")
// [C99 6.5.8] Relational operators.
BINARY_OPERATION(LT, "<")
BINARY_OPERATION(GT, ">")
BINARY_OPERATION(LE, "<=")
BINARY_OPERATION(GE, ">=")
// [C99 6.5.9] Equality operators.
BINARY_OPERATION(EQ, "==")
BINARY_OPERATION(NE, "!=")
// [C99 6.5.10] Bitwise AND operator.
BINARY_OPERATION(And, "&")
// [C99 6.5.11] Bitwise XOR operator.
BINARY_OPERATION(Xor, "^")
// [C99 6.5.12] Bitwise OR operator.
BINARY_OPERATION(Or, "|")
// [C99 6.5.13] Logical AND operator.
BINARY_OPERATION(LAnd, "&&")
// [C99 6.5.14] Logical OR operator.
BINARY_OPERATION(LOr, "||")
// [C99 6.5.16] Assignment operators.
BINARY_OPERATION(Assign, "=")
BINARY_OPERATION(MulAssign, "*=")
BINARY_OPERATION(DivAssign, "/=")
BINARY_OPERATION(RemAssign, "%=")
BINARY_OPERATION(AddAssign, "+=")
BINARY_OPERATION(SubAssign, "-=")
BINARY_OPERATION(ShlAssign, "<<=")
BINARY_OPERATION(ShrAssign, ">>=")
BINARY_OPERATION(AndAssign, "&=")
BINARY_OPERATION(XorAssign, "^=")
BINARY_OPERATION(OrAssign, "|=")
// [C99 6.5.17] Comma operator.
BINARY_OPERATION(Comma, ",")


//===- Unary Operations ---------------------------------------------------===//
// Note that additions to this should also update the StmtVisitor class,
// UnaryOperator::getOverloadedOperator and CXUnaryOperatorKind enum.

// [C99 *******] Postfix increment and decrement
UNARY_OPERATION(PostInc, "++")
UNARY_OPERATION(PostDec, "--")
// [C99 *******] Prefix increment and decrement
UNARY_OPERATION(PreInc, "++")
UNARY_OPERATION(PreDec, "--")
// [C99 *******] Address and indirection
UNARY_OPERATION(AddrOf, "&")
UNARY_OPERATION(Deref, "*")
// [C99 *******] Unary arithmetic
UNARY_OPERATION(Plus, "+")
UNARY_OPERATION(Minus, "-")
UNARY_OPERATION(Not, "~")
UNARY_OPERATION(LNot, "!")
// "__real expr"/"__imag expr" Extension.
UNARY_OPERATION(Real, "__real")
UNARY_OPERATION(Imag, "__imag")
// __extension__ marker.
UNARY_OPERATION(Extension, "__extension__")
// [C++ Coroutines] co_await operator
UNARY_OPERATION(Coawait, "co_await")

#undef CAST_OPERATION
#undef BINARY_OPERATION
#undef UNARY_OPERATION
