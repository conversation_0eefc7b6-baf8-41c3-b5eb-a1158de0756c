/*===- TableGen'erated file -------------------------------------*- C++ -*-===*\
|*                                                                            *|
|* Parser-related llvm::StringSwitch cases                                    *|
|*                                                                            *|
|* Automatically generated file, do not edit!                                 *|
|*                                                                            *|
\*===----------------------------------------------------------------------===*/

#if defined(CLANG_ATTR_ARG_CONTEXT_LIST)
.Case("acquire_capability", true)
.Case("acquire_shared_capability", true)
.Case("exclusive_lock_function", true)
.Case("shared_lock_function", true)
.Case("acquired_after", true)
.Case("acquired_before", true)
.Case("assert_capability", true)
.Case("assert_shared_capability", true)
.Case("assert_exclusive_lock", true)
.Case("assert_shared_lock", true)
.Case("exclusive_trylock_function", true)
.Case("guarded_by", true)
.Case("lock_returned", true)
.Case("locks_excluded", true)
.Case("pt_guarded_by", true)
.Case("release_capability", true)
.Case("release_shared_capability", true)
.Case("release_generic_capability", true)
.Case("unlock_function", true)
.Case("requires_capability", true)
.Case("exclusive_locks_required", true)
.Case("requires_shared_capability", true)
.Case("shared_locks_required", true)
.Case("shared_trylock_function", true)
.Case("try_acquire_capability", true)
.Case("try_acquire_shared_capability", true)
#endif // CLANG_ATTR_ARG_CONTEXT_LIST

#if defined(CLANG_ATTR_IDENTIFIER_ARG_LIST)
.Case("interrupt", true)
.Case("argument_with_type_tag", true)
.Case("pointer_with_type_tag", true)
.Case("__clang_arm_builtin_alias", true)
.Case("availability", true)
.Case("blocks", true)
.Case("builtin_alias", true)
.Case("clang_builtin_alias", true)
.Case("guard", true)
.Case("callable_when", true)
.Case("consumable", true)
.Case("enum_extensibility", true)
.Case("format", true)
.Case("function_return", true)
.Case("shader", true)
.Case("loop", true)
.Case("unroll", true)
.Case("nounroll", true)
.Case("unroll_and_jam", true)
.Case("nounroll_and_jam", true)
.Case("interrupt", true)
.Case("mode", true)
.Case("declare simd", true)
.Case("declare target", true)
.Case("objc_bridge", true)
.Case("objc_bridge_mutable", true)
.Case("objc_bridge_related", true)
.Case("objc_gc", true)
.Case("objc_method_family", true)
.Case("objc_ownership", true)
.Case("ownership_holds", true)
.Case("ownership_returns", true)
.Case("ownership_takes", true)
.Case("param_typestate", true)
.Case("pcs", true)
.Case("interrupt", true)
.Case("return_typestate", true)
.Case("set_typestate", true)
.Case("swift_async", true)
.Case("swift_async_error", true)
.Case("swift_error", true)
.Case("swift_newtype", true)
.Case("swift_wrapper", true)
.Case("test_typestate", true)
.Case("type_tag_for_datatype", true)
.Case("type_visibility", true)
.Case("visibility", true)
.Case("zero_call_used_regs", true)
#endif // CLANG_ATTR_IDENTIFIER_ARG_LIST

#if defined(CLANG_ATTR_VARIADIC_IDENTIFIER_ARG_LIST)
.Case("cpu_dispatch", true)
.Case("cpu_specific", true)
.Case("callback", true)
#endif // CLANG_ATTR_VARIADIC_IDENTIFIER_ARG_LIST

#if defined(CLANG_ATTR_THIS_ISA_IDENTIFIER_ARG_LIST)
.Case("callback", true)
#endif // CLANG_ATTR_THIS_ISA_IDENTIFIER_ARG_LIST

#if defined(CLANG_ATTR_ACCEPTS_EXPR_PACK)
.Case("annotate", true)
.Case("annotate_type", true)
#endif // CLANG_ATTR_ACCEPTS_EXPR_PACK

#if defined(CLANG_ATTR_TYPE_ARG_LIST)
.Case("iboutletcollection", true)
.Case("Owner", true)
.Case("Pointer", true)
.Case("preferred_name", true)
.Case("vec_type_hint", true)
#endif // CLANG_ATTR_TYPE_ARG_LIST

#if defined(CLANG_ATTR_LATE_PARSED_LIST)
.Case("acquire_capability", 1)
.Case("acquire_shared_capability", 1)
.Case("exclusive_lock_function", 1)
.Case("shared_lock_function", 1)
.Case("acquired_after", 1)
.Case("acquired_before", 1)
.Case("assert_capability", 1)
.Case("assert_shared_capability", 1)
.Case("assert_exclusive_lock", 1)
.Case("assert_shared_lock", 1)
.Case("diagnose_if", 1)
.Case("exclusive_trylock_function", 1)
.Case("guarded_by", 1)
.Case("lock_returned", 1)
.Case("locks_excluded", 1)
.Case("pt_guarded_by", 1)
.Case("release_capability", 1)
.Case("release_shared_capability", 1)
.Case("release_generic_capability", 1)
.Case("unlock_function", 1)
.Case("requires_capability", 1)
.Case("exclusive_locks_required", 1)
.Case("requires_shared_capability", 1)
.Case("shared_locks_required", 1)
.Case("shared_trylock_function", 1)
.Case("try_acquire_capability", 1)
.Case("try_acquire_shared_capability", 1)
#endif // CLANG_ATTR_LATE_PARSED_LIST

