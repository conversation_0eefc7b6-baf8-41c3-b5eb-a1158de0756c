// This file is automatically generated. Do not edit this file by hand.

#ifdef GET_PACKAGES
PACKAGE("optin.mpi")
PACKAGE("alpha.core")
PACKAGE("alpha.cplusplus")
PACKAGE("optin.osx")
PACKAGE("unix.cstring")
PACKAGE("valist")
PACKAGE("alpha.unix")
PACKAGE("alpha.fuchsia")
PACKAGE("cplusplus")
PACKAGE("alpha.security.taint")
PACKAGE("optin.osx.cocoa.localizability")
PACKAGE("optin.performance")
PACKAGE("alpha.nondeterminism")
PACKAGE("osx.cocoa")
PACKAGE("core.uninitialized")
PACKAGE("alpha.osx.cocoa")
PACKAGE("optin.cplusplus")
PACKAGE("core")
PACKAGE("apiModeling.google")
PACKAGE("osx.coreFoundation.containers")
PACKAGE("deadcode")
PACKAGE("alpha.security.cert.pos")
PACKAGE("security.insecureAPI")
PACKAGE("alpha.osx")
PACKAGE("alpha.deadcode")
PACKAGE("alpha.osx.cocoa.localizability")
PACKAGE("security")
PACKAGE("osx.coreFoundation")
PACKAGE("alpha.llvm")
PACKAGE("optin.portability")
PACKAGE("apiModeling")
PACKAGE("optin")
PACKAGE("fuchsia")
PACKAGE("nullability")
PACKAGE("alpha")
PACKAGE("alpha.webkit")
PACKAGE("optin.osx.cocoa")
PACKAGE("alpha.security.cert.env")
PACKAGE("alpha.unix.cstring")
PACKAGE("llvm")
PACKAGE("alpha.apiModeling")
PACKAGE("core.builtin")
PACKAGE("osx")
PACKAGE("apiModeling.llvm")
PACKAGE("alpha.clone")
PACKAGE("webkit")
PACKAGE("alpha.security.cert")
PACKAGE("debug")
PACKAGE("unix")
PACKAGE("alpha.security")
#endif // GET_PACKAGES


#ifdef GET_PACKAGE_OPTIONS
PACKAGE_OPTION("bool", "nullability", "NoDiagnoseCallsToSystemHeaders", "Suppresses warnings for violating nullability annotations of system header functions. This is useful if you are concerned with your custom nullability annotations more than with following nullability specifications of system header functions.", "false", "released", false)
#endif // GET_PACKAGE_OPTIONS


#ifdef GET_CHECKERS

CHECKER("debug.AnalysisOrder", AnalysisOrderChecker, "Print callbacks that are called during analysis in order", "", true)
CHECKER("debug.Stats", AnalyzerStatsChecker, "Emit warnings with analyzer statistics", "", true)
CHECKER("alpha.security.ArrayBound", ArrayBoundChecker, "Warn about buffer overflows (older checker)", "https://clang.llvm.org/docs/analyzer/checkers.html#alpha-security-arraybound", false)
CHECKER("alpha.security.ArrayBoundV2", ArrayBoundCheckerV2, "Warn about buffer overflows (newer checker)", "https://clang.llvm.org/docs/analyzer/checkers.html#alpha-security-arrayboundv2", false)
CHECKER("osx.cocoa.AutoreleaseWrite", AutoreleaseWriteChecker, "Warn about potentially crashing writes to autoreleasing objects from different autoreleasing pools in Objective-C", "", false)
CHECKER("alpha.unix.BlockInCriticalSection", BlockInCriticalSectionChecker, "Check for calls to blocking functions inside a critical section", "https://clang.llvm.org/docs/analyzer/checkers.html#alpha-unix-blockincriticalsection", false)
CHECKER("alpha.core.BoolAssignment", BoolAssignmentChecker, "Warn about assigning non-{0,1} values to Boolean variables", "https://clang.llvm.org/docs/analyzer/checkers.html#alpha-core-boolassignment", false)
CHECKER("core.builtin.BuiltinFunctions", BuiltinFunctionChecker, "Evaluate compiler builtin functions (e.g., alloca())", "", true)
CHECKER("alpha.core.C11Lock", C11LockChecker, "Simple lock -> unlock checker", "https://clang.llvm.org/docs/analyzer/checkers.html#alpha-core-c11lock", false)
CHECKER("osx.coreFoundation.CFError", CFErrorChecker, "Check usage of CFErrorRef* parameters", "https://clang.llvm.org/docs/analyzer/checkers.html#osx-corefoundation-cferror", false)
CHECKER("debug.DumpCFG", CFGDumper, "Display Control-Flow Graphs", "", true)
CHECKER("debug.ViewCFG", CFGViewer, "View Control-Flow Graphs using GraphViz", "", true)
CHECKER("osx.coreFoundation.CFNumber", CFNumberChecker, "Check for proper uses of CFNumber APIs", "https://clang.llvm.org/docs/analyzer/checkers.html#osx-corefoundation-cfnumber", false)
CHECKER("osx.coreFoundation.CFRetainRelease", CFRetainReleaseChecker, "Check for null arguments to CFRetain/CFRelease/CFMakeCollectable", "https://clang.llvm.org/docs/analyzer/checkers.html#osx-corefoundation-cfretainrelease", false)
CHECKER("alpha.unix.cstring.BufferOverlap", CStringBufferOverlap, "Checks for overlap in two buffer arguments", "https://clang.llvm.org/docs/analyzer/checkers.html#alpha-unix-cstring-bufferoverlap", false)
CHECKER("unix.cstring.CStringModeling", CStringModeling, "The base of several CString related checkers. On it's own it emits no reports, but adds valuable information to the analysis when enabled.", "", true)
CHECKER("alpha.unix.cstring.NotNullTerminated", CStringNotNullTerm, "Check for arguments which are not null-terminating strings", "https://clang.llvm.org/docs/analyzer/checkers.html#alpha-unix-cstring-notnullterminated", false)
CHECKER("unix.cstring.NullArg", CStringNullArg, "Check for null pointers being passed as arguments to C string functions", "https://clang.llvm.org/docs/analyzer/checkers.html#unix-cstring-nullarg", false)
CHECKER("alpha.unix.cstring.OutOfBounds", CStringOutOfBounds, "Check for out-of-bounds access in string functions", "https://clang.llvm.org/docs/analyzer/checkers.html#alpha-unix-cstring-outofbounds", false)
CHECKER("unix.cstring.BadSizeArg", CStringSyntaxChecker, "Check the size argument passed into C string functions for common erroneous patterns", "https://clang.llvm.org/docs/analyzer/checkers.html#unix-cstring-badsizearg", false)
CHECKER("alpha.unix.cstring.UninitializedRead", CStringUninitializedRead, "Checks if the string manipulation function would read uninitialized bytes", "https://clang.llvm.org/docs/analyzer/checkers.html#alpha-unix-cstring-uninitializedread", false)
CHECKER("cplusplus.SelfAssignment", CXXSelfAssignmentChecker, "Checks C++ copy and move assignment operators for self assignment", "", true)
CHECKER("core.CallAndMessage", CallAndMessageChecker, "Check for logical errors for function calls and Objective-C message expressions (e.g., uninitialized arguments, null function pointers)", "https://clang.llvm.org/docs/analyzer/checkers.html#core-callandmessage", false)
CHECKER("core.CallAndMessageModeling", CallAndMessageModeling, "Responsible for essential modeling and assumptions after a function/method call. For instance, if we can't reason about the nullability of the implicit this parameter after a method call, this checker conservatively assumes it to be non-null", "https://clang.llvm.org/docs/analyzer/checkers.html#core-callandmessagemodeling", true)
CHECKER("debug.DumpCalls", CallDumper, "Print calls as they are traversed by the engine", "", true)
CHECKER("debug.DumpCallGraph", CallGraphDumper, "Display Call Graph", "", true)
CHECKER("debug.ViewCallGraph", CallGraphViewer, "View Call Graph using GraphViz", "", true)
CHECKER("alpha.core.CastSize", CastSizeChecker, "Check when casting a malloc'ed type T, whether the size is a multiple of the size of T", "https://clang.llvm.org/docs/analyzer/checkers.html#alpha-core-castsize", false)
CHECKER("alpha.core.CastToStruct", CastToStructChecker, "Check for cast from non-struct pointer to struct pointer", "https://clang.llvm.org/docs/analyzer/checkers.html#alpha-core-casttostruct", false)
CHECKER("apiModeling.llvm.CastValue", CastValueChecker, "Model implementation of custom RTTIs", "", true)
CHECKER("alpha.unix.Chroot", ChrootChecker, "Check improper use of chroot", "https://clang.llvm.org/docs/analyzer/checkers.html#alpha-unix-chroot", false)
CHECKER("osx.cocoa.ClassRelease", ClassReleaseChecker, "Check for sending 'retain', 'release', or 'autorelease' directly to a Class", "https://clang.llvm.org/docs/analyzer/checkers.html#osx-cocoa-classrelease", false)
CHECKER("alpha.clone.CloneChecker", CloneChecker, "Reports similar pieces of code.", "https://clang.llvm.org/docs/analyzer/checkers.html#alpha-clone-clonechecker", false)
CHECKER("debug.ConfigDumper", ConfigDumper, "Dump config table", "", true)
CHECKER("alpha.cplusplus.ContainerModeling", ContainerModeling, "Models C++ containers", "", true)
CHECKER("debug.DumpControlDependencies", ControlDependencyTreeDumper, "Print the post control dependency tree for a given CFG", "", true)
CHECKER("alpha.core.Conversion", ConversionChecker, "Loss of sign/precision in implicit conversions", "https://clang.llvm.org/docs/analyzer/checkers.html#alpha-core-conversion", false)
CHECKER("valist.CopyToSelf", CopyToSelfChecker, "Check for va_lists which are copied onto itself.", "", false)
CHECKER("deadcode.DeadStores", DeadStoresChecker, "Check for values stored to variables that are never read afterwards", "https://clang.llvm.org/docs/analyzer/checkers.html#deadcode-deadstores", false)
CHECKER("debug.DebugContainerModeling", DebugContainerModeling, "Check the analyzer's understanding of C++ containers", "", true)
CHECKER("debug.DebugIteratorModeling", DebugIteratorModeling, "Check the analyzer's understanding of C++ iterators", "", true)
CHECKER("alpha.cplusplus.DeleteWithNonVirtualDtor", DeleteWithNonVirtualDtorChecker, "Reports destructions of polymorphic objects with a non-virtual destructor in their base class", "https://clang.llvm.org/docs/analyzer/checkers.html#alpha-cplusplus-deletewithnonvirtualdtor", false)
CHECKER("security.insecureAPI.DeprecatedOrUnsafeBufferHandling", DeprecatedOrUnsafeBufferHandling, "Warn on uses of unsecure or deprecated buffer manipulating functions", "https://clang.llvm.org/docs/analyzer/checkers.html#security-insecureapi-deprecatedorunsafebufferhandling", false)
CHECKER("core.NullDereference", DereferenceChecker, "Check for dereferences of null pointers", "https://clang.llvm.org/docs/analyzer/checkers.html#core-nulldereference", false)
CHECKER("alpha.osx.cocoa.DirectIvarAssignment", DirectIvarAssignment, "Check for direct assignments to instance variables", "https://clang.llvm.org/docs/analyzer/checkers.html#alpha-osx-cocoa-directivarassignment", false)
CHECKER("core.DivideZero", DivZeroChecker, "Check for division by zero", "https://clang.llvm.org/docs/analyzer/checkers.html#core-dividezero", false)
CHECKER("debug.DumpDominators", DominatorsTreeDumper, "Print the dominance tree for a given CFG", "", true)
CHECKER("unix.DynamicMemoryModeling", DynamicMemoryModeling, "The base of several malloc() related checkers. On it's own it emits no reports, but adds valuable information to the analysis when enabled.", "", true)
CHECKER("alpha.core.DynamicTypeChecker", DynamicTypeChecker, "Check for cases where the dynamic and the static type of an object are unrelated.", "https://clang.llvm.org/docs/analyzer/checkers.html#alpha-core-dynamictypechecker", false)
CHECKER("core.DynamicTypePropagation", DynamicTypePropagation, "Generate dynamic type information", "", true)
CHECKER("optin.osx.cocoa.localizability.EmptyLocalizationContextChecker", EmptyLocalizationContextChecker, "Check that NSLocalizedString macros include a comment for context", "https://clang.llvm.org/docs/analyzer/checkers.html#optin-osx-cocoa-localizability-emptylocalizationcontextchecker", false)
CHECKER("alpha.cplusplus.EnumCastOutOfRange", EnumCastOutOfRangeChecker, "Check integer to enumeration casts for out of range values", "https://clang.llvm.org/docs/analyzer/checkers.html#alpha-cplusplus-enumcastoutofrange", false)
CHECKER("alpha.unix.Errno", ErrnoChecker, "Check for improper use of 'errno'", "https://clang.llvm.org/docs/analyzer/checkers.html#alpha-unix-errno", false)
CHECKER("apiModeling.Errno", ErrnoModeling, "Make the special value 'errno' available to other checkers.", "", true)
CHECKER("debug.ErrnoTest", ErrnoTesterChecker, "Check modeling aspects of 'errno'.", "", true)
CHECKER("debug.ViewExplodedGraph", ExplodedGraphViewer, "View Exploded Graphs using GraphViz", "", true)
CHECKER("debug.ExprInspection", ExprInspectionChecker, "Check the analyzer's understanding of expressions", "", true)
CHECKER("alpha.core.FixedAddr", FixedAddressChecker, "Check for assignment of a fixed address to a pointer", "https://clang.llvm.org/docs/analyzer/checkers.html#alpha-core-fixedaddr", false)
CHECKER("security.FloatLoopCounter", FloatLoopCounter, "Warn on using a floating point value as a loop counter (CERT: FLP30-C, FLP30-CPP)", "https://clang.llvm.org/docs/analyzer/checkers.html#security-floatloopcounter", false)
CHECKER("fuchsia.HandleChecker", FuchsiaHandleChecker, "A Checker that detect leaks related to Fuchsia handles", "https://clang.llvm.org/docs/analyzer/checkers.html#fuchsia-handlechecker", false)
CHECKER("alpha.fuchsia.Lock", FuchsiaLockChecker, "Check for the correct usage of locking APIs.", "https://clang.llvm.org/docs/analyzer/checkers.html#alpha-fuchsia-lock", false)
CHECKER("optin.performance.GCDAntipattern", GCDAntipattern, "Check for performance anti-patterns when using Grand Central Dispatch", "", false)
CHECKER("apiModeling.google.GTest", GTestChecker, "Model gtest assertion APIs", "", true)
CHECKER("alpha.security.taint.TaintPropagation", GenericTaintChecker, "Generate taint information used by other checkers", "https://clang.llvm.org/docs/analyzer/checkers.html#alpha-security-taint-taintpropagation", false)
CHECKER("alpha.core.IdenticalExpr", IdenticalExprChecker, "Warn about unintended use of identical expressions in operators", "https://clang.llvm.org/docs/analyzer/checkers.html#alpha-core-identicalexpr", false)
CHECKER("cplusplus.InnerPointer", InnerPointerChecker, "Check for inner pointers of C++ containers used after re/deallocation", "", false)
CHECKER("alpha.osx.cocoa.InstanceVariableInvalidation", InstanceVariableInvalidation, "Check that the invalidatable instance variables are invalidated in the methods annotated with objc_instance_variable_invalidator", "https://clang.llvm.org/docs/analyzer/checkers.html#alpha-osx-cocoa-instancevariableinvalidation", false)
CHECKER("alpha.security.cert.env.InvalidPtr", InvalidPtrChecker, "Finds usages of possibly invalidated pointers", "https://clang.llvm.org/docs/analyzer/checkers.html#alpha-security-cert-env-invalidptr", false)
CHECKER("alpha.cplusplus.InvalidatedIterator", InvalidatedIteratorChecker, "Check for use of invalidated iterators", "https://clang.llvm.org/docs/analyzer/checkers.html#alpha-cplusplus-invalidatediterator", false)
CHECKER("alpha.cplusplus.IteratorModeling", IteratorModeling, "Models iterators of C++ containers", "", true)
CHECKER("alpha.cplusplus.IteratorRange", IteratorRangeChecker, "Check for iterators used outside their valid ranges", "https://clang.llvm.org/docs/analyzer/checkers.html#alpha-cplusplus-iteratorrange", false)
CHECKER("alpha.osx.cocoa.IvarInvalidationModeling", IvarInvalidationModeling, "Gathers information for annotation driven invalidation checking for classes that contains a method annotated with 'objc_instance_variable_invalidator'", "", true)
CHECKER("alpha.llvm.Conventions", LLVMConventionsChecker, "Check code for LLVM codebase conventions", "https://clang.llvm.org/docs/analyzer/checkers.html#alpha-llvm-conventions", false)
CHECKER("debug.DumpLiveExprs", LiveExpressionsDumper, "Print results of live expression analysis", "", true)
CHECKER("debug.DumpLiveVars", LiveVariablesDumper, "Print results of live variable analysis", "", true)
CHECKER("osx.MIG", MIGChecker, "Find violations of the Mach Interface Generator calling convention", "", false)
CHECKER("optin.mpi.MPI-Checker", MPIChecker, "Checks MPI code", "https://clang.llvm.org/docs/analyzer/checkers.html#optin-mpi-mpi-checker", false)
CHECKER("osx.SecKeychainAPI", MacOSKeychainAPIChecker, "Check for proper uses of Secure Keychain APIs", "https://clang.llvm.org/docs/analyzer/checkers.html#osx-seckeychainapi", false)
CHECKER("osx.API", MacOSXAPIChecker, "Check for proper uses of various Apple APIs", "https://clang.llvm.org/docs/analyzer/checkers.html#osx-api", false)
CHECKER("unix.Malloc", MallocChecker, "Check for memory leaks, double free, and use-after-free problems. Traces memory managed by malloc()/free().", "https://clang.llvm.org/docs/analyzer/checkers.html#unix-malloc", false)
CHECKER("alpha.security.MallocOverflow", MallocOverflowSecurityChecker, "Check for overflows in the arguments to malloc()", "https://clang.llvm.org/docs/analyzer/checkers.html#alpha-security-mallocoverflow", false)
CHECKER("unix.MallocSizeof", MallocSizeofChecker, "Check for dubious malloc arguments involving sizeof", "https://clang.llvm.org/docs/analyzer/checkers.html#unix-mallocsizeof", false)
CHECKER("unix.MismatchedDeallocator", MismatchedDeallocatorChecker, "Check for mismatched deallocators.", "https://clang.llvm.org/docs/analyzer/checkers.html#unix-mismatcheddeallocator", false)
CHECKER("alpha.cplusplus.MismatchedIterator", MismatchedIteratorChecker, "Check for use of iterators of different containers where iterators of the same container are expected", "https://clang.llvm.org/docs/analyzer/checkers.html#alpha-cplusplus-mismatchediterator", false)
CHECKER("alpha.osx.cocoa.MissingInvalidationMethod", MissingInvalidationMethod, "Check that the invalidation methods are present in classes that contain invalidatable instance variables", "https://clang.llvm.org/docs/analyzer/checkers.html#alpha-osx-cocoa-missinginvalidationmethod", false)
CHECKER("alpha.security.MmapWriteExec", MmapWriteExecChecker, "Warn on mmap() calls that are both writable and executable", "https://clang.llvm.org/docs/analyzer/checkers.html#alpha-security-mmapwriteexec", false)
CHECKER("cplusplus.Move", MoveChecker, "Find use-after-move bugs in C++", "https://clang.llvm.org/docs/analyzer/checkers.html#cplusplus-move", false)
CHECKER("osx.cocoa.NSAutoreleasePool", NSAutoreleasePoolChecker, "Warn for suboptimal uses of NSAutoreleasePool in Objective-C GC mode", "https://clang.llvm.org/docs/analyzer/checkers.html#osx-cocoa-nsautoreleasepool", false)
CHECKER("osx.cocoa.NSError", NSErrorChecker, "Check usage of NSError** parameters", "https://clang.llvm.org/docs/analyzer/checkers.html#osx-cocoa-nserror", false)
CHECKER("osx.NSOrCFErrorDerefChecker", NSOrCFErrorDerefChecker, "Implementation checker for NSErrorChecker and CFErrorChecker", "", true)
CHECKER("cplusplus.NewDelete", NewDeleteChecker, "Check for double-free and use-after-free problems. Traces memory managed by new/delete.", "https://clang.llvm.org/docs/analyzer/checkers.html#cplusplus-newdelete", false)
CHECKER("cplusplus.NewDeleteLeaks", NewDeleteLeaksChecker, "Check for memory leaks. Traces memory managed by new/delete.", "https://clang.llvm.org/docs/analyzer/checkers.html#cplusplus-newdeleteleaks", false)
CHECKER("osx.cocoa.NilArg", NilArgChecker, "Check for prohibited nil arguments to ObjC method calls", "https://clang.llvm.org/docs/analyzer/checkers.html#osx-cocoa-nilarg", false)
CHECKER("core.builtin.NoReturnFunctions", NoReturnFunctionChecker, "Evaluate \"panic\" functions that are known to not return to the caller", "", true)
CHECKER("webkit.NoUncountedMemberChecker", NoUncountedMemberChecker, "Check for no uncounted member variables.", "https://clang.llvm.org/docs/analyzer/checkers.html#webkit-nouncountedmemberchecker", false)
CHECKER("optin.osx.cocoa.localizability.NonLocalizedStringChecker", NonLocalizedStringChecker, "Warns about uses of non-localized NSStrings passed to UI methods expecting localized NSStrings", "https://clang.llvm.org/docs/analyzer/checkers.html#optin-osx-cocoa-localizability-nonlocalizedstringchecker", false)
CHECKER("core.NonNullParamChecker", NonNullParamChecker, "Check for null pointers passed as arguments to a function whose arguments are references or marked with the 'nonnull' attribute", "https://clang.llvm.org/docs/analyzer/checkers.html#core-nonnullparamchecker", false)
CHECKER("core.NonnilStringConstants", NonnullGlobalConstantsChecker, "Assume that const string-like globals are non-null", "", true)
CHECKER("nullability.NullPassedToNonnull", NullPassedToNonnullChecker, "Warns when a null pointer is passed to a pointer which has a _Nonnull type.", "https://clang.llvm.org/docs/analyzer/checkers.html#nullability-nullpassedtononnull", false)
CHECKER("nullability.NullReturnedFromNonnull", NullReturnedFromNonnullChecker, "Warns when a null pointer is returned from a function that has _Nonnull return type.", "https://clang.llvm.org/docs/analyzer/checkers.html#nullability-nullreturnedfromnonnull", false)
CHECKER("nullability.NullabilityBase", NullabilityBase, "Stores information during the analysis about nullability.", "", true)
CHECKER("nullability.NullableDereferenced", NullableDereferencedChecker, "Warns when a nullable pointer is dereferenced.", "https://clang.llvm.org/docs/analyzer/checkers.html#nullability-nullabledereferenced", false)
CHECKER("nullability.NullablePassedToNonnull", NullablePassedToNonnullChecker, "Warns when a nullable pointer is passed to a pointer which has a _Nonnull type.", "https://clang.llvm.org/docs/analyzer/checkers.html#nullability-nullablepassedtononnull", false)
CHECKER("nullability.NullableReturnedFromNonnull", NullableReturnedFromNonnullChecker, "Warns when a nullable pointer is returned from a function that has _Nonnull return type.", "", false)
CHECKER("osx.NumberObjectConversion", NumberObjectConversionChecker, "Check for erroneous conversions of objects representing numbers into numbers", "", false)
CHECKER("optin.osx.OSObjectCStyleCast", OSObjectCStyleCast, "Checker for C-style casts of OSObjects", "", false)
CHECKER("osx.OSObjectRetainCount", OSObjectRetainCountChecker, "Check for leaks and improper reference count management for OSObject", "", false)
CHECKER("osx.cocoa.AtSync", ObjCAtSyncChecker, "Check for nil pointers used as mutexes for @synchronized", "https://clang.llvm.org/docs/analyzer/checkers.html#osx-cocoa-atsync", false)
CHECKER("osx.coreFoundation.containers.PointerSizedValues", ObjCContainersASTChecker, "Warns if 'CFArray', 'CFDictionary', 'CFSet' are created with non-pointer-size values", "https://clang.llvm.org/docs/analyzer/checkers.html#osx-corefoundation-containers-pointersizedvalues", false)
CHECKER("osx.coreFoundation.containers.OutOfBounds", ObjCContainersChecker, "Checks for index out-of-bounds when using 'CFArray' API", "https://clang.llvm.org/docs/analyzer/checkers.html#osx-corefoundation-containers-outofbounds", false)
CHECKER("osx.cocoa.Dealloc", ObjCDeallocChecker, "Warn about Objective-C classes that lack a correct implementation of -dealloc", "https://clang.llvm.org/docs/analyzer/checkers.html#osx-cocoa-dealloc", false)
CHECKER("osx.cocoa.ObjCGenerics", ObjCGenericsChecker, "Check for type errors when using Objective-C generics", "https://clang.llvm.org/docs/analyzer/checkers.html#osx-cocoa-objcgenerics", false)
CHECKER("osx.cocoa.Loops", ObjCLoopChecker, "Improved modeling of loops using Cocoa collection types", "", false)
CHECKER("osx.cocoa.IncompatibleMethodTypes", ObjCMethSigsChecker, "Warn about Objective-C method signatures with type incompatibilities", "https://clang.llvm.org/docs/analyzer/checkers.html#osx-cocoa-incompatiblemethodtypes", false)
CHECKER("osx.cocoa.NonNilReturnValue", ObjCNonNilReturnValueChecker, "Model the APIs that are guaranteed to return a non-nil value", "", false)
CHECKER("osx.ObjCProperty", ObjCPropertyChecker, "Check for proper uses of Objective-C properties", "", false)
CHECKER("osx.cocoa.SelfInit", ObjCSelfInitChecker, "Check that 'self' is properly initialized inside an initializer method", "https://clang.llvm.org/docs/analyzer/checkers.html#osx-cocoa-selfinit", false)
CHECKER("osx.cocoa.MissingSuperCall", ObjCSuperCallChecker, "Warn about Objective-C methods that lack a necessary call to super", "", false)
CHECKER("osx.cocoa.SuperDealloc", ObjCSuperDeallocChecker, "Warn about improper use of '[super dealloc]' in Objective-C", "https://clang.llvm.org/docs/analyzer/checkers.html#osx-cocoa-superdealloc", false)
CHECKER("osx.cocoa.UnusedIvars", ObjCUnusedIvarsChecker, "Warn about private ivars that are never used", "https://clang.llvm.org/docs/analyzer/checkers.html#osx-cocoa-unusedivars", false)
CHECKER("optin.performance.Padding", PaddingChecker, "Check for excessively padded structs.", "", false)
CHECKER("cplusplus.PlacementNew", PlacementNewChecker, "Check if default placement new is provided with pointers to sufficient storage capacity", "https://clang.llvm.org/docs/analyzer/checkers.html#cplusplus-placementnew", false)
CHECKER("alpha.osx.cocoa.localizability.PluralMisuseChecker", PluralMisuseChecker, "Warns against using one vs. many plural pattern in code when generating localized strings.", "https://clang.llvm.org/docs/analyzer/checkers.html#alpha-osx-cocoa-localizability-pluralmisusechecker", false)
CHECKER("alpha.core.PointerArithm", PointerArithChecker, "Check for pointer arithmetic on locations other than array elements", "https://clang.llvm.org/docs/analyzer/checkers.html#alpha-core-pointerarithm", false)
CHECKER("alpha.nondeterminism.PointerIteration", PointerIterationChecker, "Checks for non-determinism caused by iteration of unordered containers of pointers", "https://clang.llvm.org/docs/analyzer/checkers.html#alpha-nondeterminism-pointeriteration", false)
CHECKER("alpha.nondeterminism.PointerSorting", PointerSortingChecker, "Check for non-determinism caused by sorting of pointers", "https://clang.llvm.org/docs/analyzer/checkers.html#alpha-nondeterminism-pointersorting", false)
CHECKER("alpha.core.PointerSub", PointerSubChecker, "Check for pointer subtractions on two pointers pointing to different memory chunks", "https://clang.llvm.org/docs/analyzer/checkers.html#alpha-core-pointersub", false)
CHECKER("debug.DumpPostDominators", PostDominatorsTreeDumper, "Print the post dominance tree for a given CFG", "", true)
CHECKER("alpha.core.PthreadLockBase", PthreadLockBase, "Helper registering multiple checks.", "", true)
CHECKER("alpha.unix.PthreadLock", PthreadLockChecker, "Simple lock -> unlock checker", "https://clang.llvm.org/docs/analyzer/checkers.html#alpha-unix-pthreadlock", false)
CHECKER("cplusplus.PureVirtualCall", PureVirtualCallChecker, "Check pure virtual function calls during construction/destruction", "https://clang.llvm.org/docs/analyzer/checkers.html#cplusplus-purevirtualcall", false)
CHECKER("alpha.security.cert.pos.34c", PutenvWithAuto, "Finds calls to the 'putenv' function which pass a pointer to an automatic variable as the argument.", "https://clang.llvm.org/docs/analyzer/checkers.html#alpha-security-cert-pos-34c", false)
CHECKER("webkit.RefCntblBaseVirtualDtor", RefCntblBaseVirtualDtorChecker, "Check for any ref-countable base class having virtual destructor.", "https://clang.llvm.org/docs/analyzer/checkers.html#webkit-refcntblbasevirtualdtor", false)
CHECKER("debug.ReportStmts", ReportStmts, "Emits a warning for every statement.", "", true)
CHECKER("osx.cocoa.RetainCountBase", RetainCountBase, "Common base of various retain count related checkers", "", true)
CHECKER("osx.cocoa.RetainCount", RetainCountChecker, "Check for leaks and improper reference count management", "https://clang.llvm.org/docs/analyzer/checkers.html#osx-cocoa-retaincount", false)
CHECKER("alpha.security.ReturnPtrRange", ReturnPointerRangeChecker, "Check for an out-of-bound pointer being returned to callers", "https://clang.llvm.org/docs/analyzer/checkers.html#alpha-security-returnptrrange", false)
CHECKER("core.uninitialized.UndefReturn", ReturnUndefChecker, "Check for uninitialized values being returned to the caller", "https://clang.llvm.org/docs/analyzer/checkers.html#core-uninitialized-undefreturn", false)
CHECKER("apiModeling.llvm.ReturnValue", ReturnValueChecker, "Model the guaranteed boolean return value of function calls", "", true)
CHECKER("osx.cocoa.RunLoopAutoreleaseLeak", RunLoopAutoreleaseLeakChecker, "Check for leaked memory in autorelease pools that will never be drained", "", false)
CHECKER("alpha.cplusplus.STLAlgorithmModeling", STLAlgorithmModeling, "Models the algorithm library of the C++ STL.", "", false)
CHECKER("security.insecureAPI.SecuritySyntaxChecker", SecuritySyntaxChecker, "Base of various security function related checkers", "", true)
CHECKER("alpha.unix.SimpleStream", SimpleStreamChecker, "Check for misuses of stream APIs", "https://clang.llvm.org/docs/analyzer/checkers.html#alpha-unix-simplestream", false)
CHECKER("alpha.core.SizeofPtr", SizeofPointerChecker, "Warn about unintended use of sizeof() on pointer expressions", "https://clang.llvm.org/docs/analyzer/checkers.html#alpha-core-sizeofptr", false)
CHECKER("alpha.cplusplus.SmartPtr", SmartPtrChecker, "Find the dereference of null SmrtPtr", "https://clang.llvm.org/docs/analyzer/checkers.html#alpha-cplusplus-smartptr", false)
CHECKER("cplusplus.SmartPtrModeling", SmartPtrModeling, "Model behavior of C++ smart pointers", "", true)
CHECKER("alpha.core.StackAddressAsyncEscape", StackAddrAsyncEscapeChecker, "Check that addresses to stack memory do not escape the function", "https://clang.llvm.org/docs/analyzer/checkers.html#alpha-core-stackaddressasyncescape", false)
CHECKER("core.StackAddrEscapeBase", StackAddrEscapeBase, "Generate information about stack address escapes.", "", true)
CHECKER("core.StackAddressEscape", StackAddrEscapeChecker, "Check that addresses to stack memory do not escape the function", "https://clang.llvm.org/docs/analyzer/checkers.html#core-stackaddressescape", false)
CHECKER("alpha.unix.StdCLibraryFunctions", StdCLibraryFunctionsChecker, "Check for invalid arguments of C standard library functions, and apply relations between arguments and return value", "https://clang.llvm.org/docs/analyzer/checkers.html#alpha-unix-stdclibraryfunctions", false)
CHECKER("debug.StdCLibraryFunctionsTester", StdCLibraryFunctionsTesterChecker, "Add test functions to the summary map, so testing of individual summary constituents becomes possible.", "", true)
CHECKER("alpha.unix.Stream", StreamChecker, "Check stream handling functions", "https://clang.llvm.org/docs/analyzer/checkers.html#alpha-unix-stream", false)
CHECKER("debug.StreamTester", StreamTesterChecker, "Add test functions to StreamChecker for test and debugging purposes.", "", true)
CHECKER("cplusplus.StringChecker", StringChecker, "Checks C++ std::string bugs", "https://clang.llvm.org/docs/analyzer/checkers.html#cplusplus-stringchecker", false)
CHECKER("debug.TaintTest", TaintTesterChecker, "Mark tainted symbols as such.", "", true)
CHECKER("alpha.core.TestAfterDivZero", TestAfterDivZeroChecker, "Check for division by variable that is later compared against 0. Either the comparison is useless or there is division by zero.", "https://clang.llvm.org/docs/analyzer/checkers.html#alpha-core-testafterdivzero", false)
CHECKER("debug.DumpTraversal", TraversalDumper, "Print branch conditions as they are traversed by the engine", "", true)
CHECKER("apiModeling.TrustNonnull", TrustNonnullChecker, "Trust that returns from framework methods annotated with _Nonnull are not null", "", true)
CHECKER("apiModeling.TrustReturnsNonnull", TrustReturnsNonnullChecker, "Trust that returns from methods annotated with returns_nonnull are not null", "", true)
CHECKER("security.insecureAPI.UncheckedReturn", UncheckedReturn, "Warn on uses of functions whose return values must be always checked", "https://clang.llvm.org/docs/analyzer/checkers.html#security-insecureapi-uncheckedreturn", false)
CHECKER("alpha.webkit.UncountedCallArgsChecker", UncountedCallArgsChecker, "Check uncounted call arguments.", "https://clang.llvm.org/docs/analyzer/checkers.html#alpha-webkit-uncountedcallargschecker", false)
CHECKER("webkit.UncountedLambdaCapturesChecker", UncountedLambdaCapturesChecker, "Check uncounted lambda captures.", "https://clang.llvm.org/docs/analyzer/checkers.html#webkit-uncountedlambdacaptureschecker", false)
CHECKER("alpha.webkit.UncountedLocalVarsChecker", UncountedLocalVarsChecker, "Check uncounted local variables.", "https://clang.llvm.org/docs/analyzer/checkers.html#alpha-webkit-uncountedlocalvarschecker", false)
CHECKER("core.uninitialized.Branch", UndefBranchChecker, "Check for uninitialized values used as branch conditions", "https://clang.llvm.org/docs/analyzer/checkers.html#core-uninitialized-branch", false)
CHECKER("core.uninitialized.CapturedBlockVariable", UndefCapturedBlockVarChecker, "Check for blocks that capture uninitialized values", "", false)
CHECKER("core.UndefinedBinaryOperatorResult", UndefResultChecker, "Check for undefined results of binary operators", "https://clang.llvm.org/docs/analyzer/checkers.html#core-undefinedbinaryoperatorresult", false)
CHECKER("core.uninitialized.ArraySubscript", UndefinedArraySubscriptChecker, "Check for uninitialized values used as array subscripts", "https://clang.llvm.org/docs/analyzer/checkers.html#core-uninitialized-arraysubscript", false)
CHECKER("core.uninitialized.Assign", UndefinedAssignmentChecker, "Check for assigning uninitialized values", "https://clang.llvm.org/docs/analyzer/checkers.html#core-uninitialized-assign", false)
CHECKER("core.uninitialized.NewArraySize", UndefinedNewArraySizeChecker, "Check if the size of the array in a new[] expression is undefined", "https://clang.llvm.org/docs/analyzer/checkers.html#core-uninitialized-newarraysize", false)
CHECKER("valist.Uninitialized", UninitializedChecker, "Check for usages of uninitialized (or already released) va_lists.", "", false)
CHECKER("optin.cplusplus.UninitializedObject", UninitializedObjectChecker, "Reports uninitialized fields after object construction", "https://clang.llvm.org/docs/analyzer/checkers.html#optin-cplusplus-uninitializedobject", false)
CHECKER("unix.API", UnixAPIMisuseChecker, "Check calls to various UNIX/Posix functions", "https://clang.llvm.org/docs/analyzer/checkers.html#unix-api", false)
CHECKER("optin.portability.UnixAPI", UnixAPIPortabilityChecker, "Finds implementation-defined behavior in UNIX/Posix functions", "", false)
CHECKER("alpha.deadcode.UnreachableCode", UnreachableCodeChecker, "Check unreachable code", "https://clang.llvm.org/docs/analyzer/checkers.html#alpha-deadcode-unreachablecode", false)
CHECKER("valist.Unterminated", UnterminatedChecker, "Check for va_lists which are not released by a va_end call.", "", false)
CHECKER("core.VLASize", VLASizeChecker, "Check for declarations of VLA of undefined or zero size", "https://clang.llvm.org/docs/analyzer/checkers.html#core-vlasize", false)
CHECKER("valist.ValistBase", ValistBase, "Gathers information about va_lists.", "", true)
CHECKER("osx.cocoa.VariadicMethodTypes", VariadicMethodTypeChecker, "Check for passing non-Objective-C types to variadic collection initialization methods that expect only Objective-C types", "https://clang.llvm.org/docs/analyzer/checkers.html#osx-cocoa-variadicmethodtypes", false)
CHECKER("unix.Vfork", VforkChecker, "Check for proper usage of vfork", "https://clang.llvm.org/docs/analyzer/checkers.html#unix-vfork", false)
CHECKER("optin.cplusplus.VirtualCall", VirtualCallChecker, "Check virtual function calls during construction/destruction", "https://clang.llvm.org/docs/analyzer/checkers.html#optin-cplusplus-virtualcall", false)
CHECKER("cplusplus.VirtualCallModeling", VirtualCallModeling, "Auxiliary modeling for the virtual method call checkers", "", true)
CHECKER("security.insecureAPI.bcmp", bcmp, "Warn on uses of the 'bcmp' function", "https://clang.llvm.org/docs/analyzer/checkers.html#security-insecureapi-bcmp", false)
CHECKER("security.insecureAPI.bcopy", bcopy, "Warn on uses of the 'bcopy' function", "https://clang.llvm.org/docs/analyzer/checkers.html#security-insecureapi-bcopy", false)
CHECKER("security.insecureAPI.bzero", bzero, "Warn on uses of the 'bzero' function", "https://clang.llvm.org/docs/analyzer/checkers.html#security-insecureapi-bzero", false)
CHECKER("security.insecureAPI.decodeValueOfObjCType", decodeValueOfObjCType, "Warn on uses of the '-decodeValueOfObjCType:at:' method", "https://clang.llvm.org/docs/analyzer/checkers.html#security-insecureapi-decodevalueofobjctype", false)
CHECKER("security.insecureAPI.getpw", getpw, "Warn on uses of the 'getpw' function", "https://clang.llvm.org/docs/analyzer/checkers.html#security-insecureapi-getpw", false)
CHECKER("security.insecureAPI.gets", gets, "Warn on uses of the 'gets' function", "https://clang.llvm.org/docs/analyzer/checkers.html#security-insecureapi-gets", false)
CHECKER("security.insecureAPI.mkstemp", mkstemp, "Warn when 'mkstemp' is passed fewer than 6 X's in the format string", "https://clang.llvm.org/docs/analyzer/checkers.html#security-insecureapi-mkstemp", false)
CHECKER("security.insecureAPI.mktemp", mktemp, "Warn on uses of the 'mktemp' function", "https://clang.llvm.org/docs/analyzer/checkers.html#security-insecureapi-mktemp", false)
CHECKER("security.insecureAPI.rand", rand, "Warn on uses of the 'rand', 'random', and related functions", "https://clang.llvm.org/docs/analyzer/checkers.html#security-insecureapi-rand", false)
CHECKER("security.insecureAPI.strcpy", strcpy, "Warn on uses of the 'strcpy' and 'strcat' functions", "https://clang.llvm.org/docs/analyzer/checkers.html#security-insecureapi-strcpy", false)
CHECKER("security.insecureAPI.vfork", vfork, "Warn on uses of the 'vfork' function", "https://clang.llvm.org/docs/analyzer/checkers.html#security-insecureapi-vfork", false)

#endif // GET_CHECKERS


#ifdef GET_CHECKER_DEPENDENCIES
CHECKER_DEPENDENCY("alpha.core.C11Lock", "alpha.core.PthreadLockBase")
CHECKER_DEPENDENCY("osx.coreFoundation.CFError", "osx.NSOrCFErrorDerefChecker")
CHECKER_DEPENDENCY("alpha.unix.cstring.BufferOverlap", "unix.cstring.CStringModeling")
CHECKER_DEPENDENCY("alpha.unix.cstring.NotNullTerminated", "unix.cstring.CStringModeling")
CHECKER_DEPENDENCY("unix.cstring.NullArg", "unix.cstring.CStringModeling")
CHECKER_DEPENDENCY("alpha.unix.cstring.OutOfBounds", "unix.cstring.CStringModeling")
CHECKER_DEPENDENCY("unix.cstring.BadSizeArg", "unix.cstring.CStringModeling")
CHECKER_DEPENDENCY("alpha.unix.cstring.UninitializedRead", "unix.cstring.CStringModeling")
CHECKER_DEPENDENCY("core.CallAndMessage", "core.CallAndMessageModeling")
CHECKER_DEPENDENCY("valist.CopyToSelf", "valist.ValistBase")
CHECKER_DEPENDENCY("debug.DebugContainerModeling", "alpha.cplusplus.ContainerModeling")
CHECKER_DEPENDENCY("debug.DebugIteratorModeling", "debug.DebugContainerModeling")
CHECKER_DEPENDENCY("debug.DebugIteratorModeling", "alpha.cplusplus.IteratorModeling")
CHECKER_DEPENDENCY("security.insecureAPI.DeprecatedOrUnsafeBufferHandling", "security.insecureAPI.SecuritySyntaxChecker")
CHECKER_DEPENDENCY("unix.DynamicMemoryModeling", "unix.cstring.CStringModeling")
CHECKER_DEPENDENCY("alpha.unix.Errno", "apiModeling.Errno")
CHECKER_DEPENDENCY("debug.ErrnoTest", "apiModeling.Errno")
CHECKER_DEPENDENCY("security.FloatLoopCounter", "security.insecureAPI.SecuritySyntaxChecker")
CHECKER_DEPENDENCY("alpha.fuchsia.Lock", "alpha.core.PthreadLockBase")
CHECKER_DEPENDENCY("cplusplus.InnerPointer", "unix.DynamicMemoryModeling")
CHECKER_DEPENDENCY("alpha.osx.cocoa.InstanceVariableInvalidation", "alpha.osx.cocoa.IvarInvalidationModeling")
CHECKER_DEPENDENCY("alpha.cplusplus.InvalidatedIterator", "alpha.cplusplus.IteratorModeling")
CHECKER_DEPENDENCY("alpha.cplusplus.IteratorModeling", "alpha.cplusplus.ContainerModeling")
CHECKER_DEPENDENCY("alpha.cplusplus.IteratorRange", "alpha.cplusplus.IteratorModeling")
CHECKER_DEPENDENCY("unix.Malloc", "unix.DynamicMemoryModeling")
CHECKER_DEPENDENCY("unix.MismatchedDeallocator", "unix.DynamicMemoryModeling")
CHECKER_DEPENDENCY("alpha.cplusplus.MismatchedIterator", "alpha.cplusplus.IteratorModeling")
CHECKER_DEPENDENCY("alpha.osx.cocoa.MissingInvalidationMethod", "alpha.osx.cocoa.IvarInvalidationModeling")
CHECKER_DEPENDENCY("osx.cocoa.NSError", "osx.NSOrCFErrorDerefChecker")
CHECKER_DEPENDENCY("cplusplus.NewDelete", "unix.DynamicMemoryModeling")
CHECKER_DEPENDENCY("cplusplus.NewDeleteLeaks", "unix.DynamicMemoryModeling")
CHECKER_DEPENDENCY("nullability.NullPassedToNonnull", "nullability.NullabilityBase")
CHECKER_DEPENDENCY("nullability.NullReturnedFromNonnull", "nullability.NullabilityBase")
CHECKER_DEPENDENCY("nullability.NullableDereferenced", "nullability.NullabilityBase")
CHECKER_DEPENDENCY("nullability.NullablePassedToNonnull", "nullability.NullabilityBase")
CHECKER_DEPENDENCY("nullability.NullableReturnedFromNonnull", "nullability.NullabilityBase")
CHECKER_DEPENDENCY("osx.OSObjectRetainCount", "osx.cocoa.RetainCountBase")
CHECKER_DEPENDENCY("osx.cocoa.ObjCGenerics", "core.DynamicTypePropagation")
CHECKER_DEPENDENCY("cplusplus.PlacementNew", "unix.DynamicMemoryModeling")
CHECKER_DEPENDENCY("alpha.unix.PthreadLock", "alpha.core.PthreadLockBase")
CHECKER_DEPENDENCY("cplusplus.PureVirtualCall", "cplusplus.VirtualCallModeling")
CHECKER_DEPENDENCY("osx.cocoa.RetainCount", "osx.cocoa.RetainCountBase")
CHECKER_DEPENDENCY("alpha.cplusplus.STLAlgorithmModeling", "alpha.cplusplus.ContainerModeling")
CHECKER_DEPENDENCY("alpha.cplusplus.SmartPtr", "cplusplus.SmartPtrModeling")
CHECKER_DEPENDENCY("alpha.core.StackAddressAsyncEscape", "core.StackAddrEscapeBase")
CHECKER_DEPENDENCY("core.StackAddressEscape", "core.StackAddrEscapeBase")
CHECKER_DEPENDENCY("security.insecureAPI.UncheckedReturn", "security.insecureAPI.SecuritySyntaxChecker")
CHECKER_DEPENDENCY("valist.Uninitialized", "valist.ValistBase")
CHECKER_DEPENDENCY("valist.Unterminated", "valist.ValistBase")
CHECKER_DEPENDENCY("optin.cplusplus.VirtualCall", "cplusplus.VirtualCallModeling")
CHECKER_DEPENDENCY("security.insecureAPI.bcmp", "security.insecureAPI.SecuritySyntaxChecker")
CHECKER_DEPENDENCY("security.insecureAPI.bcopy", "security.insecureAPI.SecuritySyntaxChecker")
CHECKER_DEPENDENCY("security.insecureAPI.bzero", "security.insecureAPI.SecuritySyntaxChecker")
CHECKER_DEPENDENCY("security.insecureAPI.decodeValueOfObjCType", "security.insecureAPI.SecuritySyntaxChecker")
CHECKER_DEPENDENCY("security.insecureAPI.getpw", "security.insecureAPI.SecuritySyntaxChecker")
CHECKER_DEPENDENCY("security.insecureAPI.gets", "security.insecureAPI.SecuritySyntaxChecker")
CHECKER_DEPENDENCY("security.insecureAPI.mkstemp", "security.insecureAPI.SecuritySyntaxChecker")
CHECKER_DEPENDENCY("security.insecureAPI.mktemp", "security.insecureAPI.SecuritySyntaxChecker")
CHECKER_DEPENDENCY("security.insecureAPI.rand", "security.insecureAPI.SecuritySyntaxChecker")
CHECKER_DEPENDENCY("security.insecureAPI.strcpy", "security.insecureAPI.SecuritySyntaxChecker")
CHECKER_DEPENDENCY("security.insecureAPI.vfork", "security.insecureAPI.SecuritySyntaxChecker")

#endif // GET_CHECKER_DEPENDENCIES

#ifdef GET_CHECKER_WEAK_DEPENDENCIES
CHECKER_WEAK_DEPENDENCY("alpha.unix.StdCLibraryFunctions", "core.CallAndMessage")
CHECKER_WEAK_DEPENDENCY("alpha.unix.StdCLibraryFunctions", "core.NonNullParamChecker")
CHECKER_WEAK_DEPENDENCY("alpha.unix.StdCLibraryFunctions", "alpha.unix.Stream")
CHECKER_WEAK_DEPENDENCY("alpha.unix.Stream", "core.NonNullParamChecker")

#endif // GET_CHECKER_WEAK_DEPENDENCIES

#ifdef GET_CHECKER_OPTIONS
CHECKER_OPTION("bool", "debug.AnalysisOrder", "PreStmtCastExpr", "", "false", "released", true)
CHECKER_OPTION("bool", "debug.AnalysisOrder", "PostStmtCastExpr", "", "false", "released", true)
CHECKER_OPTION("bool", "debug.AnalysisOrder", "PreStmtArraySubscriptExpr", "", "false", "released", true)
CHECKER_OPTION("bool", "debug.AnalysisOrder", "PostStmtArraySubscriptExpr", "", "false", "released", true)
CHECKER_OPTION("bool", "debug.AnalysisOrder", "PreStmtCXXNewExpr", "", "false", "released", true)
CHECKER_OPTION("bool", "debug.AnalysisOrder", "PostStmtCXXNewExpr", "", "false", "released", true)
CHECKER_OPTION("bool", "debug.AnalysisOrder", "PreStmtCXXDeleteExpr", "", "false", "released", true)
CHECKER_OPTION("bool", "debug.AnalysisOrder", "PostStmtCXXDeleteExpr", "", "false", "released", true)
CHECKER_OPTION("bool", "debug.AnalysisOrder", "PreStmtCXXConstructExpr", "", "false", "released", true)
CHECKER_OPTION("bool", "debug.AnalysisOrder", "PostStmtCXXConstructExpr", "", "false", "released", true)
CHECKER_OPTION("bool", "debug.AnalysisOrder", "PreStmtOffsetOfExpr", "", "false", "released", true)
CHECKER_OPTION("bool", "debug.AnalysisOrder", "PostStmtOffsetOfExpr", "", "false", "released", true)
CHECKER_OPTION("bool", "debug.AnalysisOrder", "EvalCall", "", "false", "released", true)
CHECKER_OPTION("bool", "debug.AnalysisOrder", "PreCall", "", "false", "released", true)
CHECKER_OPTION("bool", "debug.AnalysisOrder", "PostCall", "", "false", "released", true)
CHECKER_OPTION("bool", "debug.AnalysisOrder", "EndFunction", "", "false", "released", true)
CHECKER_OPTION("bool", "debug.AnalysisOrder", "EndAnalysis", "", "false", "released", true)
CHECKER_OPTION("bool", "debug.AnalysisOrder", "NewAllocator", "", "false", "released", true)
CHECKER_OPTION("bool", "debug.AnalysisOrder", "Bind", "", "false", "released", true)
CHECKER_OPTION("bool", "debug.AnalysisOrder", "LiveSymbols", "", "false", "released", true)
CHECKER_OPTION("bool", "debug.AnalysisOrder", "RegionChanges", "", "false", "released", true)
CHECKER_OPTION("bool", "debug.AnalysisOrder", "PointerEscape", "", "false", "released", true)
CHECKER_OPTION("bool", "debug.AnalysisOrder", "*", "Enables all callbacks.", "false", "released", true)
CHECKER_OPTION("bool", "core.CallAndMessage", "FunctionPointer", "Check whether a called function pointer is null or undefined", "true", "released", false)
CHECKER_OPTION("bool", "core.CallAndMessage", "ParameterCount", "Check whether a function was called with the appropriate number of arguments", "true", "released", false)
CHECKER_OPTION("bool", "core.CallAndMessage", "CXXThisMethodCall", "Check whether the implicit this parameter is null or undefined upon a method call", "true", "released", false)
CHECKER_OPTION("bool", "core.CallAndMessage", "CXXDeallocationArg", "Check whether the argument of operator delete is undefined", "true", "released", false)
CHECKER_OPTION("bool", "core.CallAndMessage", "ArgInitializedness", "Check whether any of the pass-by-value parameters is undefined", "true", "released", false)
CHECKER_OPTION("bool", "core.CallAndMessage", "ArgPointeeInitializedness", "Check whether the pointee of a pass-by-reference or pass-by-pointer is undefined", "false", "alpha", false)
CHECKER_OPTION("bool", "core.CallAndMessage", "NilReceiver", "Check whether the reciever in the message expression is nil", "true", "released", false)
CHECKER_OPTION("bool", "core.CallAndMessage", "UndefReceiver", "Check whether the reciever in the message expression is undefined", "true", "released", false)
CHECKER_OPTION("int", "alpha.clone.CloneChecker", "MinimumCloneComplexity", "Ensures that every clone has at least the given complexity. Complexity is here defined as the total amount of children of a statement. This constraint assumes the first statement in the group is representative for all other statements in the group in terms of complexity.", "50", "released", false)
CHECKER_OPTION("bool", "alpha.clone.CloneChecker", "ReportNormalClones", "Report all clones, even less suspicious ones.", "true", "released", false)
CHECKER_OPTION("string", "alpha.clone.CloneChecker", "IgnoredFilesPattern", "If supplied, the checker wont analyze files with a filename that matches the given pattern.", "\"\"", "released", false)
CHECKER_OPTION("bool", "deadcode.DeadStores", "WarnForDeadNestedAssignments", "Warns for deadstores in nested assignments.E.g.: if ((P = f())) where P is unused.", "true", "released", false)
CHECKER_OPTION("bool", "deadcode.DeadStores", "ShowFixIts", "Enable fix-it hints for this checker", "false", "alpha", false)
CHECKER_OPTION("bool", "core.NullDereference", "SuppressAddressSpaces", "Suppresses warning when pointer dereferences an address space", "true", "released", false)
CHECKER_OPTION("bool", "alpha.osx.cocoa.DirectIvarAssignment", "AnnotatedFunctions", "Check for direct assignments to instance variables in the methods annotated with objc_no_direct_instance_variable_assignment", "false", "alpha", false)
CHECKER_OPTION("bool", "unix.DynamicMemoryModeling", "Optimistic", "If set to true, the checker assumes that all the allocating and deallocating functions are annotated with ownership_holds, ownership_takes and ownership_returns.", "false", "alpha", false)
CHECKER_OPTION("bool", "unix.DynamicMemoryModeling", "AddNoOwnershipChangeNotes", "Add an additional note to the bug report for leak-like bugs. Dynamically allocated objects passed to functions that neither deallocated it, or have taken responsibility of the ownership are noted, similarly to NoStoreFuncVisitor.", "true", "released", true)
CHECKER_OPTION("bool", "alpha.unix.Errno", "AllowErrnoReadOutsideConditionExpressions", "Allow read of undefined value from errno outside of conditions", "true", "alpha", false)
CHECKER_OPTION("string", "alpha.security.taint.TaintPropagation", "Config", "Specifies the name of the configuration file.", "", "alpha", false)
CHECKER_OPTION("int", "alpha.security.MmapWriteExec", "MmapProtExec", "Specifies the value of PROT_EXEC", "0x04", "released", false)
CHECKER_OPTION("int", "alpha.security.MmapWriteExec", "MmapProtRead", "Specifies the value of PROT_READ", "0x01", "released", false)
CHECKER_OPTION("string", "cplusplus.Move", "WarnOn", "In non-aggressive mode, only warn on use-after-move of local variables (or local rvalue references) and of STL objects. The former is possible because local variables (or local rvalue references) are not tempting their user to re-use the storage. The latter is possible because STL objects are known to end up in a valid but unspecified state after the move and their state-reset methods are also known, which allows us to predict precisely when use-after-move is invalid. Some STL objects are known to conform to additional contracts after move, so they are not tracked. However, smart pointers specifically are tracked because we can perform extra checking over them. In aggressive mode, warn on any use-after-move because the user has intentionally asked us to completely eliminate use-after-move in his code. Values: \"KnownsOnly\", \"KnownsAndLocals\", \"All\".", "KnownsAndLocals", "released", false)
CHECKER_OPTION("bool", "optin.osx.cocoa.localizability.NonLocalizedStringChecker", "AggressiveReport", "Marks a string being returned by any call as localized if it is in LocStringFunctions (LSF) or the function is annotated. Otherwise, we mark it as NonLocalized (Aggressive) or NonLocalized only if it is not backed by a SymRegion (Non-Aggressive), basically leaving only string literals as NonLocalized.", "false", "alpha", true)
CHECKER_OPTION("bool", "osx.NumberObjectConversion", "Pedantic", "Enables detection of more conversion patterns (which are most likely more harmless, and therefore are more likely to produce false positives).", "false", "released", false)
CHECKER_OPTION("int", "optin.performance.Padding", "AllowedPad", "Reports are only generated if the excessive padding exceeds 'AllowedPad' in bytes.", "24", "released", false)
CHECKER_OPTION("bool", "osx.cocoa.RetainCount", "TrackNSCFStartParam", "Check not only that the code follows retain-release rules with respect to objects it allocates or borrows from elsewhere, but also that it fulfills its own retain count specification with respect to objects that it receives as arguments.", "false", "released", false)
CHECKER_OPTION("bool", "alpha.cplusplus.STLAlgorithmModeling", "AggressiveStdFindModeling", "Enables exploration of the failure branch in std::find-like functions.", "false", "released", false)
CHECKER_OPTION("bool", "cplusplus.SmartPtrModeling", "ModelSmartPtrDereference", "Enable modeling for SmartPtr null dereferences", "false", "alpha", true)
CHECKER_OPTION("bool", "alpha.unix.StdCLibraryFunctions", "DisplayLoadedSummaries", "If set to true, the checker displays the found summaries for the given translation unit.", "false", "released", true)
CHECKER_OPTION("bool", "alpha.unix.StdCLibraryFunctions", "ModelPOSIX", "If set to true, the checker models additional functions from the POSIX standard.", "false", "alpha", false)
CHECKER_OPTION("bool", "optin.cplusplus.UninitializedObject", "Pedantic", "If set to false, the checker won't emit warnings for objects that don't have at least one initialized field.", "false", "released", false)
CHECKER_OPTION("bool", "optin.cplusplus.UninitializedObject", "NotesAsWarnings", "If set to true, the checker will emit a warning for each uninitalized field, as opposed to emitting one warning per constructor call, and listing the uninitialized fields that belongs to it in notes.", "false", "released", true)
CHECKER_OPTION("bool", "optin.cplusplus.UninitializedObject", "CheckPointeeInitialization", "If set to false, the checker will not analyze the pointee of pointer/reference fields, and will only check whether the object itself is initialized.", "false", "alpha", false)
CHECKER_OPTION("string", "optin.cplusplus.UninitializedObject", "IgnoreRecordsWithField", "If supplied, the checker will not analyze structures that have a field with a name or type name that matches the given pattern.", "\"\"", "released", false)
CHECKER_OPTION("bool", "optin.cplusplus.UninitializedObject", "IgnoreGuardedFields", "If set to true, the checker will analyze _syntactically_ whether the found uninitialized object is used without a preceding assert call. Defaults to false.", "false", "alpha", false)
CHECKER_OPTION("bool", "optin.cplusplus.VirtualCall", "ShowFixIts", "Enable fix-it hints for this checker", "false", "alpha", false)
CHECKER_OPTION("bool", "optin.cplusplus.VirtualCall", "PureOnly", "Disables the checker. Keeps cplusplus.PureVirtualCall enabled. This option is only provided for backwards compatibility.", "false", "alpha", false)
#endif // GET_CHECKER_OPTIONS

