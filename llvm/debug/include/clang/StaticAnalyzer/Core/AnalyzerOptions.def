//===-- AnalyzerOptions.def - Metadata about Static Analyses ----*- C++ -*-===//
//
// Part of the LLVM Project, under the Apache License v2.0 with LLVM Exceptions.
// See https://llvm.org/LICENSE.txt for license information.
// SPDX-License-Identifier: Apache-2.0 WITH LLVM-exception
//
//===----------------------------------------------------------------------===//
//
//  This file defines the analyzer options avaible with -analyzer-config.
//
//===----------------------------------------------------------------------===//

#ifndef LLVM_ADT_STRINGREF_H
#error This .def file is expected to be included in translation units where \
"llvm/ADT/StringRef.h" is already included!
#endif

#ifdef ANALYZER_OPTION
#ifndef ANALYZER_OPTION_DEPENDS_ON_USER_MODE
#error If you didnt include this file with the intent of generating methods, \
define both 'ANALYZER_OPTION' and 'ANALYZER_OPTION_DEPENDS_ON_USER_MODE' macros!
#endif
#endif

#ifndef ANALYZER_OPTION_DEPENDS_ON_USER_MODE
#ifdef ANALYZER_OPTION
#error If you didnt include this file with the intent of generating methods, \
define both 'ANALYZER_OPTION' and 'ANALYZER_OPTION_DEPENDS_ON_USER_MODE' macros!
#endif
#endif

#ifndef ANALYZER_OPTION
/// Create a new analyzer option, but dont generate a method for it in
/// AnalyzerOptions.
///
///   TYPE - The type of the option object that will be stored in
///          AnalyzerOptions. This file is expected to be icluded in translation
///          units where AnalyzerOptions.h is included, so types from that
///          header should be used.
///   NAME - The name of the option object.
///   CMDFLAG - The command line flag for the option.
///             (-analyzer-config CMDFLAG=VALUE)
///   DESC - Description of the flag.
///   DEFAULT_VAL - The default value for CMDFLAG.
#define ANALYZER_OPTION(TYPE, NAME, CMDFLAG, DESC, DEFAULT_VAL)
#endif

#ifndef ANALYZER_OPTION_DEPENDS_ON_USER_MODE
/// Create a new analyzer option, but dont generate a method for it in
/// AnalyzerOptions. It's value depends on the option "user-mode".
///
///   TYPE - The type of the option object that will be stored in
///          AnalyzerOptions. This file is expected to be icluded in translation
///          units where AnalyzerOptions.h is included, so types from that
///          header should be used.
///   NAME - The name of the option object.
///   CMDFLAG - The command line flag for the option.
///             (-analyzer-config CMDFLAG=VALUE)
///   DESC - Description of the flag.
///   SHALLOW_VAL - The default value for CMDFLAG, when "user-mode" was set to
///                 "shallow".
///   DEEP_VAL - The default value for CMDFLAG, when "user-mode" was set to
///              "deep".
#define ANALYZER_OPTION_DEPENDS_ON_USER_MODE(TYPE, NAME, CMDFLAG, DESC,        \
                                             SHALLOW_VAL, DEEP_VAL)
#endif

//===----------------------------------------------------------------------===//
// The "mode" option. Since some options depend on this, we list it on top of
// this file in order to make sure that the generated field for it is
// initialized before the rest.
//===----------------------------------------------------------------------===//

ANALYZER_OPTION(
    StringRef, UserMode, "mode",
    "(string) Controls the high-level analyzer mode, which influences the "
    "default settings for some of the lower-level config options (such as "
    "IPAMode). Value: \"deep\", \"shallow\".",
    "deep")

//===----------------------------------------------------------------------===//
// Boolean analyzer options.
//===----------------------------------------------------------------------===//

ANALYZER_OPTION(bool, ShouldIncludeImplicitDtorsInCFG, "cfg-implicit-dtors",
                "Whether or not implicit destructors for C++ objects "
                "should be included in the CFG.",
                true)

ANALYZER_OPTION(bool, ShouldIncludeTemporaryDtorsInCFG, "cfg-temporary-dtors",
                "Whether or not the destructors for C++ temporary "
                "objects should be included in the CFG.",
                true)

ANALYZER_OPTION(
    bool, ShouldIncludeLifetimeInCFG, "cfg-lifetime",
    "Whether or not end-of-lifetime information should be included in the CFG.",
    false)

ANALYZER_OPTION(bool, ShouldIncludeLoopExitInCFG, "cfg-loopexit",
                "Whether or not the end of the loop information should "
                "be included in the CFG.",
                false)

ANALYZER_OPTION(bool, ShouldIncludeRichConstructorsInCFG,
                "cfg-rich-constructors",
                "Whether or not construction site information should be "
                "included in the CFG C++ constructor elements.",
                true)

ANALYZER_OPTION(
    bool, ShouldIncludeScopesInCFG, "cfg-scopes",
    "Whether or not scope information should be included in the CFG.", false)

ANALYZER_OPTION(bool, ShouldIncludeDefaultInitForAggregates,
                "cfg-expand-default-aggr-inits",
                "Whether or not inline CXXDefaultInitializers for aggregate "
                "initialization in the CFG.",
                false)

ANALYZER_OPTION(
    bool, MayInlineTemplateFunctions, "c++-template-inlining",
    "Whether or not templated functions may be considered for inlining.", true)

ANALYZER_OPTION(bool, MayInlineCXXStandardLibrary, "c++-stdlib-inlining",
                "Whether or not C++ standard library functions may be "
                "considered for inlining.",
                true)

ANALYZER_OPTION(bool, MayInlineCXXAllocator, "c++-allocator-inlining",
                "Whether or not allocator and deallocator calls may be "
                "considered for inlining.",
                true)

ANALYZER_OPTION(
    bool, MayInlineCXXSharedPtrDtor, "c++-shared_ptr-inlining",
    "Whether or not the destructor of C++ 'shared_ptr' may be considered for "
    "inlining. This covers std::shared_ptr, std::tr1::shared_ptr, and "
    "boost::shared_ptr, and indeed any destructor named '~shared_ptr'.",
    false)

ANALYZER_OPTION(bool, MayInlineCXXTemporaryDtors, "c++-temp-dtor-inlining",
                "Whether C++ temporary destructors should be inlined "
                "during analysis. If temporary destructors are disabled "
                "in the CFG via the 'cfg-temporary-dtors' option, "
                "temporary destructors would not be inlined anyway.",
                true)

ANALYZER_OPTION(
    bool, ShouldSuppressNullReturnPaths, "suppress-null-return-paths",
    "Whether or not paths that go through null returns should be suppressed. "
    "This is a heuristic for avoiding bug reports with paths that go through "
    "inlined functions that are more defensive than their callers.",
    true)

ANALYZER_OPTION(
    bool, ShouldAvoidSuppressingNullArgumentPaths,
    "avoid-suppressing-null-argument-paths",
    "Whether a bug report should not be suppressed if its path includes a call "
    "with a null argument, even if that call has a null return. This option "
    "has no effect when ShouldSuppressNullReturnPaths is false. This is a "
    "counter-heuristic to avoid false negatives.",
    false)

ANALYZER_OPTION(bool, ShouldSuppressInlinedDefensiveChecks,
                "suppress-inlined-defensive-checks",
                "Whether or not diagnostics containing inlined "
                "defensive NULL checks should be suppressed.",
                true)

ANALYZER_OPTION(bool, MayInlineCXXContainerMethods, "c++-container-inlining",
                "Whether or not methods of C++ container objects may be "
                "considered for inlining.",
                false)

ANALYZER_OPTION(bool, ShouldSuppressFromCXXStandardLibrary,
                "suppress-c++-stdlib",
                "Whether or not diagnostics reported within the C++ "
                "standard library should be suppressed.",
                true)

ANALYZER_OPTION(bool, ShouldCrosscheckWithZ3, "crosscheck-with-z3",
                "Whether bug reports should be crosschecked with the Z3 "
                "constraint manager backend.",
                false)

ANALYZER_OPTION(bool, ShouldReportIssuesInMainSourceFile,
                "report-in-main-source-file",
                "Whether or not the diagnostic report should be always "
                "reported in the main source file and not the headers.",
                false)

ANALYZER_OPTION(bool, ShouldWriteStableReportFilename, "stable-report-filename",
                "Deprecated: report filenames are now always stable. "
                "See also 'verbose-report-filename'.",
                false)

ANALYZER_OPTION(bool, ShouldWriteVerboseReportFilename, "verbose-report-filename",
                "Whether or not the report filename should contain extra "
                "information about the issue.",
                false)

ANALYZER_OPTION(
    bool, ShouldSerializeStats, "serialize-stats",
    "Whether the analyzer should serialize statistics to plist output. "
    "Statistics would be serialized in JSON format inside the main dictionary "
    "under the statistics key. Available only if compiled in assert mode or "
    "with LLVM statistics explicitly enabled.",
    false)

ANALYZER_OPTION(bool, MayInlineObjCMethod, "objc-inlining",
                "Whether ObjectiveC inlining is enabled, false otherwise.",
                true)

ANALYZER_OPTION(bool, ShouldPrunePaths, "prune-paths",
                "Whether irrelevant parts of a bug report path should "
                "be pruned out of the final output.",
                true)

ANALYZER_OPTION(bool, ShouldAddPopUpNotes, "add-pop-up-notes",
                "Whether pop-up notes should be added to the final output.",
                true)

ANALYZER_OPTION(
    bool, ShouldConditionalizeStaticInitializers,
    "cfg-conditional-static-initializers",
    "Whether 'static' initializers should be in conditional logic in the CFG.",
    true)

ANALYZER_OPTION(bool, ShouldSynthesizeBodies, "faux-bodies",
                "Whether the analyzer engine should synthesize fake "
                "bodies for well-known functions.",
                true)

ANALYZER_OPTION(
    bool, ShouldElideConstructors, "elide-constructors",
    "Whether elidable C++ copy-constructors and move-constructors should be "
    "actually elided during analysis. Both behaviors are allowed by the C++ "
    "standard, and the analyzer, like CodeGen, defaults to eliding. Starting "
    "with C++17 some elisions become mandatory, and in these cases the option "
    "will be ignored.",
    true)

ANALYZER_OPTION(
    bool, ShouldInlineLambdas, "inline-lambdas",
    "Whether lambdas should be inlined. Otherwise a sink node will be "
    "generated each time a LambdaExpr is visited.",
    true)

ANALYZER_OPTION(bool, ShouldWidenLoops, "widen-loops",
                "Whether the analysis should try to widen loops.", false)

ANALYZER_OPTION(
    bool, ShouldUnrollLoops, "unroll-loops",
    "Whether the analysis should try to unroll loops with known bounds.", false)

ANALYZER_OPTION(
    bool, ShouldDisplayNotesAsEvents, "notes-as-events",
    "Whether the bug reporter should transparently treat extra note diagnostic "
    "pieces as event diagnostic pieces. Useful when the diagnostic consumer "
    "doesn't support the extra note pieces.",
    false)

ANALYZER_OPTION(
    bool, ShouldAggressivelySimplifyBinaryOperation,
    "aggressive-binary-operation-simplification",
    "Whether SValBuilder should rearrange comparisons and additive operations "
    "of symbolic expressions which consist of a sum of a symbol and a concrete "
    "integer into the format where symbols are on the left-hand side and the "
    "integer is on the right. This is only done if both symbols and both "
    "concrete integers are signed, greater than or equal to the quarter of the "
    "minimum value of the type and less than or equal to the quarter of the "
    "maximum value of that type. A + n <OP> B + m becomes A - B <OP> m - n, "
    "where A and B symbolic, n and m are integers. <OP> is any of '==', '!=', "
    "'<', '<=', '>', '>=', '+' or '-'. The rearrangement also happens with '-' "
    "instead of '+' on either or both side and also if any or both integers "
    "are missing.",
    false)

ANALYZER_OPTION(
    bool, ShouldEagerlyAssume, "eagerly-assume",
    "Whether we should eagerly assume evaluations of conditionals, thus, "
    "bifurcating the path. This indicates how the engine should handle "
    "expressions such as: 'x = (y != 0)'. When this is true then the "
    "subexpression 'y != 0' will be eagerly assumed to be true or false, thus "
    "evaluating it to the integers 0 or 1 respectively. The upside is that "
    "this can increase analysis precision until we have a better way to lazily "
    "evaluate such logic. The downside is that it eagerly bifurcates paths.",
    true)

ANALYZER_OPTION(
    bool, IsNaiveCTUEnabled, "experimental-enable-naive-ctu-analysis",
    "Whether naive cross translation unit analysis is enabled. This is an "
    "experimental feature to inline functions from other translation units.",
    false)

ANALYZER_OPTION(bool, ShouldDisplayMacroExpansions, "expand-macros",
                "Whether macros related to the bugpath should be "
                "expanded and included in the plist output.",
                false)

ANALYZER_OPTION(bool, DisplayCTUProgress, "display-ctu-progress",
                "Whether to emit verbose output about "
                "the analyzer's progress related to ctu.",
                false)

ANALYZER_OPTION(bool, ShouldTrackConditions, "track-conditions",
                "Whether to track conditions that are a control dependency of "
                "an already tracked variable.",
                true)

ANALYZER_OPTION(bool, ShouldTrackConditionsDebug, "track-conditions-debug",
                "Whether to place an event at each tracked condition.",
                false)

ANALYZER_OPTION(bool, ShouldApplyFixIts, "apply-fixits",
                "Apply the fix-it hints to the files",
                false)

ANALYZER_OPTION(bool, ShouldDisplayCheckerNameForText, "display-checker-name",
                "Display the checker name for textual outputs",
                true)

ANALYZER_OPTION(bool, ShouldSupportSymbolicIntegerCasts,
                "support-symbolic-integer-casts",
                "Produce cast symbols for integral types.",
                false)

ANALYZER_OPTION(
    bool, ShouldAssumeControlledEnvironment, "assume-controlled-environment",
    "Whether the analyzed application runs in a controlled environment. "
    "We will assume that environment variables exist in queries and they hold "
    "no malicious data. For instance, if this option is enabled, 'getenv()' "
    "might be modeled by the analyzer to never return NULL.",
    false)

ANALYZER_OPTION(
    bool, ShouldIgnoreBisonGeneratedFiles, "ignore-bison-generated-files",
    "If enabled, any files containing the \"/* A Bison parser, made by\" "
    "won't be analyzed.",
    true)

ANALYZER_OPTION(
    bool, ShouldIgnoreFlexGeneratedFiles, "ignore-flex-generated-files",
    "If enabled, any files containing the \"/* A lexical scanner generated by "
    "flex\" won't be analyzed.",
    true)

//===----------------------------------------------------------------------===//
// Unsigned analyzer options.
//===----------------------------------------------------------------------===//

ANALYZER_OPTION(unsigned, CTUImportThreshold, "ctu-import-threshold",
                "The maximal amount of translation units that is considered "
                "for import when inlining functions during CTU analysis. "
                "Lowering this threshold can alleviate the memory burden of "
                "analysis with many interdependent definitions located in "
                "various translation units. This is valid only for non C++ "
                "source files.",
                24u)

ANALYZER_OPTION(unsigned, CTUImportCppThreshold, "ctu-import-cpp-threshold",
                "The maximal amount of translation units that is considered "
                "for import when inlining functions during CTU analysis of C++ "
                "source files.",
                8u)

ANALYZER_OPTION(
    unsigned, AlwaysInlineSize, "ipa-always-inline-size",
    "The size of the functions (in basic blocks), which should be considered "
    "to be small enough to always inline.",
    3)

ANALYZER_OPTION(
    unsigned, GraphTrimInterval, "graph-trim-interval",
    "How often nodes in the ExplodedGraph should be recycled to save memory. "
    "To disable node reclamation, set the option to 0.",
    1000)

ANALYZER_OPTION(
    unsigned, MinCFGSizeTreatFunctionsAsLarge,
    "min-cfg-size-treat-functions-as-large",
    "The number of basic blocks a function needs to have to be considered "
    "large for the 'max-times-inline-large' config option.",
    14)

ANALYZER_OPTION(unsigned, MaxSymbolComplexity, "max-symbol-complexity",
                "The maximum complexity of symbolic constraint.", 35)

ANALYZER_OPTION(unsigned, MaxTimesInlineLarge, "max-times-inline-large",
                "The maximum times a large function could be inlined.", 32)

ANALYZER_OPTION_DEPENDS_ON_USER_MODE(
    unsigned, MaxInlinableSize, "max-inlinable-size",
    "The bound on the number of basic blocks in an inlined function.",
    /* SHALLOW_VAL */ 4, /* DEEP_VAL */ 100)

ANALYZER_OPTION_DEPENDS_ON_USER_MODE(
    unsigned, MaxNodesPerTopLevelFunction, "max-nodes",
    "The maximum number of nodes the analyzer can generate while exploring a "
    "top level function (for each exploded graph). 0 means no limit.",
    /* SHALLOW_VAL */ 75000, /* DEEP_VAL */ 225000)

ANALYZER_OPTION(
    unsigned, CTUMaxNodesPercentage, "ctu-max-nodes-pct",
    "The percentage of single-TU analysed nodes that the CTU analysis is "
    "allowed to visit.", 50)

ANALYZER_OPTION(
    unsigned, CTUMaxNodesMin, "ctu-max-nodes-min",
    "The maximum number of nodes in CTU mode is determinded by "
    "'ctu-max-nodes-pct'. However, if the number of nodes in single-TU "
    "analysis is too low, it is meaningful to provide a minimum value that "
    "serves as an upper bound instead.", 10000)

ANALYZER_OPTION(
    StringRef, CTUPhase1InliningMode, "ctu-phase1-inlining",
    "Controls which functions will be inlined during the first phase of the ctu "
    "analysis. "
    "If the value is set to 'all' then all foreign functions are inlinied "
    "immediately during the first phase, thus rendering the second phase a noop. "
    "The 'ctu-max-nodes-*' budge has no effect in this case. "
    "If the value is 'small' then only functions with a linear CFG and with a "
    "limited number of statements would be inlined during the first phase. The "
    "long and/or nontrivial functions are handled in the second phase and are "
    "controlled by the 'ctu-max-nodes-*' budge. "
    "The value 'none' means that all foreign functions are inlined only in the "
    "second phase, 'ctu-max-nodes-*' budge limits the second phase. "
    "Value: \"none\", \"small\", \"all\".",
    "small")

ANALYZER_OPTION(
    unsigned, RegionStoreSmallStructLimit, "region-store-small-struct-limit",
    "The largest number of fields a struct can have and still be considered "
    "small. This is currently used to decide whether or not it is worth forcing "
    "a LazyCompoundVal on bind. To disable all small-struct-dependent "
    "behavior, set the option to 0.",
    2)

ANALYZER_OPTION(
    unsigned, RegionStoreSmallArrayLimit, "region-store-small-array-limit",
    "The largest number of elements an array can have and still be considered "
    "small. This is currently used to decide whether or not it is worth forcing "
    "a LazyCompoundVal on bind. To disable all small-array-dependent "
    "behavior, set the option to 0.",
    5)

//===----------------------------------------------------------------------===//
// String analyzer options.
//===----------------------------------------------------------------------===//

ANALYZER_OPTION(StringRef, CTUDir, "ctu-dir",
                "The directory containing the CTU related files.", "")

ANALYZER_OPTION(StringRef, CTUIndexName, "ctu-index-name",
                "The name of the file containing the CTU index of definitions. "
                "The index file maps USR-names to identifiers. An identifier "
                "can end with an '.ast' suffix, indicating the indentifier is "
                "a path to a pch-dump. Otherwise the identifier is regarded as "
                "path to a source file which is parsed on-demand. Relative "
                "paths are prefixed with ctu-dir, absolute paths are used "
                "unmodified during lookup.",
                "externalDefMap.txt")

ANALYZER_OPTION(
    StringRef, CTUInvocationList, "ctu-invocation-list",
    "The path to the YAML format file containing a mapping from source file "
    "paths to command-line invocations represented as a list of arguments. "
    "This invocation is used produce the source-file's AST in case on-demand "
    "loading is performed. Example file-content: "
    "{/main.cpp: [clang++, /main.cpp], other.cpp: [clang++, /other.cpp]}",
    "invocations.yaml")

ANALYZER_OPTION(
    StringRef, ModelPath, "model-path",
    "The analyzer can inline an alternative implementation written in C at the "
    "call site if the called function's body is not available. This is a path "
    "where to look for those alternative implementations (called models).",
    "")

ANALYZER_OPTION(
    StringRef, CXXMemberInliningMode, "c++-inlining",
    "Controls which C++ member functions will be considered for inlining. "
    "Value: \"constructors\", \"destructors\", \"methods\".",
    "destructors")

ANALYZER_OPTION(
    StringRef, ExplorationStrategy, "exploration_strategy",
    "Value: \"dfs\", \"bfs\", \"unexplored_first\", "
    "\"unexplored_first_queue\", \"unexplored_first_location_queue\", "
    "\"bfs_block_dfs_contents\".",
    "unexplored_first_queue")

ANALYZER_OPTION(
    StringRef, RawSilencedCheckersAndPackages, "silence-checkers",
    "A semicolon separated list of checker and package names to silence. "
    "Silenced checkers will not emit reports, but the modeling remain enabled.",
    "")

ANALYZER_OPTION_DEPENDS_ON_USER_MODE(
    StringRef, IPAMode, "ipa",
    "Controls the mode of inter-procedural analysis. Value: \"none\", "
    "\"basic-inlining\", \"inlining\", \"dynamic\", \"dynamic-bifurcate\".",
    /* SHALLOW_VAL */ "inlining", /* DEEP_VAL */ "dynamic-bifurcate")

#undef ANALYZER_OPTION_DEPENDS_ON_USER_MODE
#undef ANALYZER_OPTION
