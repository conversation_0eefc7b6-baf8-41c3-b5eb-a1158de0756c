//===-- Symbols.def - Metadata about SymExpr kinds --------------*- C++ -*-===//
//
// Part of the LLVM Project, under the Apache License v2.0 with LLVM Exceptions.
// See https://llvm.org/LICENSE.txt for license information.
// SPDX-License-Identifier: Apache-2.0 WITH LLVM-exception
//
//===----------------------------------------------------------------------===//
//
// The list of symbols (SymExpr sub-classes) used in the Static Analyzer.
// In order to use this information, users of this file must define
// one or more of the three macros:
//
// SYMBOL(Id, Parent) - for specific SymExpr sub-classes, reserving the
// IdKind identifier for its kind enumeration value.
//
// ABSTRACT_SYMBOL(Id, Parent) - for abstract symbol classes,
//
// SYMBOL_RANGE(Id, First, Last) - for ranges of kind-enums,
// allowing to determine abstract class of a symbol
// based on the kind enumeration value.
//
//===----------------------------------------------------------------------===//

#ifndef SYMBOL
#define SYMBOL(Id, Parent)
#endif

#ifndef ABSTRACT_SYMBOL
#define ABSTRACT_SYMBOL(Id, Parent)
#endif

#ifndef SYMBOL_RANGE
#define SYMBOL_RANGE(Id, First, Last)
#endif

SYMBOL(UnarySymExpr, SymExpr)

ABSTRACT_SYMBOL(BinarySymExpr, SymExpr)
  SYMBOL(IntSymExpr, BinarySymExpr)
  SYMBOL(SymIntExpr, BinarySymExpr)
  SYMBOL(SymSymExpr, BinarySymExpr)
SYMBOL_RANGE(BINARYSYMEXPRS, IntSymExprKind, SymSymExprKind)

SYMBOL(SymbolCast, SymExpr)

ABSTRACT_SYMBOL(SymbolData, SymExpr)
  SYMBOL(SymbolConjured, SymbolData)
  SYMBOL(SymbolDerived, SymbolData)
  SYMBOL(SymbolExtent, SymbolData)
  SYMBOL(SymbolMetadata, SymbolData)
  SYMBOL(SymbolRegionValue, SymbolData)
SYMBOL_RANGE(SYMBOLS, SymbolConjuredKind, SymbolRegionValueKind)

#undef SYMBOL
#undef ABSTRACT_SYMBOL
#undef SYMBOL_RANGE
