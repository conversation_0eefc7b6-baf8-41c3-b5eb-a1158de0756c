/*===- TableGen'erated file -------------------------------------*- C++ -*-===*\
|*                                                                            *|
|* Intrinsic Function Source Fragment                                         *|
|*                                                                            *|
|* Automatically generated file, do not edit!                                 *|
|*                                                                            *|
\*===----------------------------------------------------------------------===*/

#ifndef LLVM_IR_INTRINSIC_AARCH64_ENUMS_H
#define LLVM_IR_INTRINSIC_AARCH64_ENUMS_H

namespace llvm {
namespace Intrinsic {
enum AARCH64Intrinsics : unsigned {
// Enum values for intrinsics
    aarch64_addg = 428,                              // llvm.aarch64.addg
    aarch64_break,                             // llvm.aarch64.break
    aarch64_clrex,                             // llvm.aarch64.clrex
    aarch64_cls,                               // llvm.aarch64.cls
    aarch64_cls64,                             // llvm.aarch64.cls64
    aarch64_crc32b,                            // llvm.aarch64.crc32b
    aarch64_crc32cb,                           // llvm.aarch64.crc32cb
    aarch64_crc32ch,                           // llvm.aarch64.crc32ch
    aarch64_crc32cw,                           // llvm.aarch64.crc32cw
    aarch64_crc32cx,                           // llvm.aarch64.crc32cx
    aarch64_crc32h,                            // llvm.aarch64.crc32h
    aarch64_crc32w,                            // llvm.aarch64.crc32w
    aarch64_crc32x,                            // llvm.aarch64.crc32x
    aarch64_crypto_aesd,                       // llvm.aarch64.crypto.aesd
    aarch64_crypto_aese,                       // llvm.aarch64.crypto.aese
    aarch64_crypto_aesimc,                     // llvm.aarch64.crypto.aesimc
    aarch64_crypto_aesmc,                      // llvm.aarch64.crypto.aesmc
    aarch64_crypto_bcaxs,                      // llvm.aarch64.crypto.bcaxs
    aarch64_crypto_bcaxu,                      // llvm.aarch64.crypto.bcaxu
    aarch64_crypto_eor3s,                      // llvm.aarch64.crypto.eor3s
    aarch64_crypto_eor3u,                      // llvm.aarch64.crypto.eor3u
    aarch64_crypto_rax1,                       // llvm.aarch64.crypto.rax1
    aarch64_crypto_sha1c,                      // llvm.aarch64.crypto.sha1c
    aarch64_crypto_sha1h,                      // llvm.aarch64.crypto.sha1h
    aarch64_crypto_sha1m,                      // llvm.aarch64.crypto.sha1m
    aarch64_crypto_sha1p,                      // llvm.aarch64.crypto.sha1p
    aarch64_crypto_sha1su0,                    // llvm.aarch64.crypto.sha1su0
    aarch64_crypto_sha1su1,                    // llvm.aarch64.crypto.sha1su1
    aarch64_crypto_sha256h,                    // llvm.aarch64.crypto.sha256h
    aarch64_crypto_sha256h2,                   // llvm.aarch64.crypto.sha256h2
    aarch64_crypto_sha256su0,                  // llvm.aarch64.crypto.sha256su0
    aarch64_crypto_sha256su1,                  // llvm.aarch64.crypto.sha256su1
    aarch64_crypto_sha512h,                    // llvm.aarch64.crypto.sha512h
    aarch64_crypto_sha512h2,                   // llvm.aarch64.crypto.sha512h2
    aarch64_crypto_sha512su0,                  // llvm.aarch64.crypto.sha512su0
    aarch64_crypto_sha512su1,                  // llvm.aarch64.crypto.sha512su1
    aarch64_crypto_sm3partw1,                  // llvm.aarch64.crypto.sm3partw1
    aarch64_crypto_sm3partw2,                  // llvm.aarch64.crypto.sm3partw2
    aarch64_crypto_sm3ss1,                     // llvm.aarch64.crypto.sm3ss1
    aarch64_crypto_sm3tt1a,                    // llvm.aarch64.crypto.sm3tt1a
    aarch64_crypto_sm3tt1b,                    // llvm.aarch64.crypto.sm3tt1b
    aarch64_crypto_sm3tt2a,                    // llvm.aarch64.crypto.sm3tt2a
    aarch64_crypto_sm3tt2b,                    // llvm.aarch64.crypto.sm3tt2b
    aarch64_crypto_sm4e,                       // llvm.aarch64.crypto.sm4e
    aarch64_crypto_sm4ekey,                    // llvm.aarch64.crypto.sm4ekey
    aarch64_crypto_xar,                        // llvm.aarch64.crypto.xar
    aarch64_dmb,                               // llvm.aarch64.dmb
    aarch64_dsb,                               // llvm.aarch64.dsb
    aarch64_fjcvtzs,                           // llvm.aarch64.fjcvtzs
    aarch64_frint32x,                          // llvm.aarch64.frint32x
    aarch64_frint32z,                          // llvm.aarch64.frint32z
    aarch64_frint64x,                          // llvm.aarch64.frint64x
    aarch64_frint64z,                          // llvm.aarch64.frint64z
    aarch64_get_fpcr,                          // llvm.aarch64.get.fpcr
    aarch64_gmi,                               // llvm.aarch64.gmi
    aarch64_hint,                              // llvm.aarch64.hint
    aarch64_irg,                               // llvm.aarch64.irg
    aarch64_irg_sp,                            // llvm.aarch64.irg.sp
    aarch64_isb,                               // llvm.aarch64.isb
    aarch64_ld64b,                             // llvm.aarch64.ld64b
    aarch64_ldaxp,                             // llvm.aarch64.ldaxp
    aarch64_ldaxr,                             // llvm.aarch64.ldaxr
    aarch64_ldg,                               // llvm.aarch64.ldg
    aarch64_ldxp,                              // llvm.aarch64.ldxp
    aarch64_ldxr,                              // llvm.aarch64.ldxr
    aarch64_mops_memset_tag,                   // llvm.aarch64.mops.memset.tag
    aarch64_neon_abs,                          // llvm.aarch64.neon.abs
    aarch64_neon_addhn,                        // llvm.aarch64.neon.addhn
    aarch64_neon_addp,                         // llvm.aarch64.neon.addp
    aarch64_neon_bfcvt,                        // llvm.aarch64.neon.bfcvt
    aarch64_neon_bfcvtn,                       // llvm.aarch64.neon.bfcvtn
    aarch64_neon_bfcvtn2,                      // llvm.aarch64.neon.bfcvtn2
    aarch64_neon_bfdot,                        // llvm.aarch64.neon.bfdot
    aarch64_neon_bfmlalb,                      // llvm.aarch64.neon.bfmlalb
    aarch64_neon_bfmlalt,                      // llvm.aarch64.neon.bfmlalt
    aarch64_neon_bfmmla,                       // llvm.aarch64.neon.bfmmla
    aarch64_neon_cls,                          // llvm.aarch64.neon.cls
    aarch64_neon_fabd,                         // llvm.aarch64.neon.fabd
    aarch64_neon_facge,                        // llvm.aarch64.neon.facge
    aarch64_neon_facgt,                        // llvm.aarch64.neon.facgt
    aarch64_neon_faddp,                        // llvm.aarch64.neon.faddp
    aarch64_neon_faddv,                        // llvm.aarch64.neon.faddv
    aarch64_neon_fcvtas,                       // llvm.aarch64.neon.fcvtas
    aarch64_neon_fcvtau,                       // llvm.aarch64.neon.fcvtau
    aarch64_neon_fcvtms,                       // llvm.aarch64.neon.fcvtms
    aarch64_neon_fcvtmu,                       // llvm.aarch64.neon.fcvtmu
    aarch64_neon_fcvtns,                       // llvm.aarch64.neon.fcvtns
    aarch64_neon_fcvtnu,                       // llvm.aarch64.neon.fcvtnu
    aarch64_neon_fcvtps,                       // llvm.aarch64.neon.fcvtps
    aarch64_neon_fcvtpu,                       // llvm.aarch64.neon.fcvtpu
    aarch64_neon_fcvtxn,                       // llvm.aarch64.neon.fcvtxn
    aarch64_neon_fcvtzs,                       // llvm.aarch64.neon.fcvtzs
    aarch64_neon_fcvtzu,                       // llvm.aarch64.neon.fcvtzu
    aarch64_neon_fmax,                         // llvm.aarch64.neon.fmax
    aarch64_neon_fmaxnm,                       // llvm.aarch64.neon.fmaxnm
    aarch64_neon_fmaxnmp,                      // llvm.aarch64.neon.fmaxnmp
    aarch64_neon_fmaxnmv,                      // llvm.aarch64.neon.fmaxnmv
    aarch64_neon_fmaxp,                        // llvm.aarch64.neon.fmaxp
    aarch64_neon_fmaxv,                        // llvm.aarch64.neon.fmaxv
    aarch64_neon_fmin,                         // llvm.aarch64.neon.fmin
    aarch64_neon_fminnm,                       // llvm.aarch64.neon.fminnm
    aarch64_neon_fminnmp,                      // llvm.aarch64.neon.fminnmp
    aarch64_neon_fminnmv,                      // llvm.aarch64.neon.fminnmv
    aarch64_neon_fminp,                        // llvm.aarch64.neon.fminp
    aarch64_neon_fminv,                        // llvm.aarch64.neon.fminv
    aarch64_neon_fmlal,                        // llvm.aarch64.neon.fmlal
    aarch64_neon_fmlal2,                       // llvm.aarch64.neon.fmlal2
    aarch64_neon_fmlsl,                        // llvm.aarch64.neon.fmlsl
    aarch64_neon_fmlsl2,                       // llvm.aarch64.neon.fmlsl2
    aarch64_neon_fmulx,                        // llvm.aarch64.neon.fmulx
    aarch64_neon_frecpe,                       // llvm.aarch64.neon.frecpe
    aarch64_neon_frecps,                       // llvm.aarch64.neon.frecps
    aarch64_neon_frecpx,                       // llvm.aarch64.neon.frecpx
    aarch64_neon_frint32x,                     // llvm.aarch64.neon.frint32x
    aarch64_neon_frint32z,                     // llvm.aarch64.neon.frint32z
    aarch64_neon_frint64x,                     // llvm.aarch64.neon.frint64x
    aarch64_neon_frint64z,                     // llvm.aarch64.neon.frint64z
    aarch64_neon_frsqrte,                      // llvm.aarch64.neon.frsqrte
    aarch64_neon_frsqrts,                      // llvm.aarch64.neon.frsqrts
    aarch64_neon_ld1x2,                        // llvm.aarch64.neon.ld1x2
    aarch64_neon_ld1x3,                        // llvm.aarch64.neon.ld1x3
    aarch64_neon_ld1x4,                        // llvm.aarch64.neon.ld1x4
    aarch64_neon_ld2,                          // llvm.aarch64.neon.ld2
    aarch64_neon_ld2lane,                      // llvm.aarch64.neon.ld2lane
    aarch64_neon_ld2r,                         // llvm.aarch64.neon.ld2r
    aarch64_neon_ld3,                          // llvm.aarch64.neon.ld3
    aarch64_neon_ld3lane,                      // llvm.aarch64.neon.ld3lane
    aarch64_neon_ld3r,                         // llvm.aarch64.neon.ld3r
    aarch64_neon_ld4,                          // llvm.aarch64.neon.ld4
    aarch64_neon_ld4lane,                      // llvm.aarch64.neon.ld4lane
    aarch64_neon_ld4r,                         // llvm.aarch64.neon.ld4r
    aarch64_neon_pmul,                         // llvm.aarch64.neon.pmul
    aarch64_neon_pmull,                        // llvm.aarch64.neon.pmull
    aarch64_neon_pmull64,                      // llvm.aarch64.neon.pmull64
    aarch64_neon_raddhn,                       // llvm.aarch64.neon.raddhn
    aarch64_neon_rshrn,                        // llvm.aarch64.neon.rshrn
    aarch64_neon_rsubhn,                       // llvm.aarch64.neon.rsubhn
    aarch64_neon_sabd,                         // llvm.aarch64.neon.sabd
    aarch64_neon_saddlp,                       // llvm.aarch64.neon.saddlp
    aarch64_neon_saddlv,                       // llvm.aarch64.neon.saddlv
    aarch64_neon_saddv,                        // llvm.aarch64.neon.saddv
    aarch64_neon_scalar_sqxtn,                 // llvm.aarch64.neon.scalar.sqxtn
    aarch64_neon_scalar_sqxtun,                // llvm.aarch64.neon.scalar.sqxtun
    aarch64_neon_scalar_uqxtn,                 // llvm.aarch64.neon.scalar.uqxtn
    aarch64_neon_sdot,                         // llvm.aarch64.neon.sdot
    aarch64_neon_shadd,                        // llvm.aarch64.neon.shadd
    aarch64_neon_shll,                         // llvm.aarch64.neon.shll
    aarch64_neon_shsub,                        // llvm.aarch64.neon.shsub
    aarch64_neon_smax,                         // llvm.aarch64.neon.smax
    aarch64_neon_smaxp,                        // llvm.aarch64.neon.smaxp
    aarch64_neon_smaxv,                        // llvm.aarch64.neon.smaxv
    aarch64_neon_smin,                         // llvm.aarch64.neon.smin
    aarch64_neon_sminp,                        // llvm.aarch64.neon.sminp
    aarch64_neon_sminv,                        // llvm.aarch64.neon.sminv
    aarch64_neon_smmla,                        // llvm.aarch64.neon.smmla
    aarch64_neon_smull,                        // llvm.aarch64.neon.smull
    aarch64_neon_sqabs,                        // llvm.aarch64.neon.sqabs
    aarch64_neon_sqadd,                        // llvm.aarch64.neon.sqadd
    aarch64_neon_sqdmulh,                      // llvm.aarch64.neon.sqdmulh
    aarch64_neon_sqdmulh_lane,                 // llvm.aarch64.neon.sqdmulh.lane
    aarch64_neon_sqdmulh_laneq,                // llvm.aarch64.neon.sqdmulh.laneq
    aarch64_neon_sqdmull,                      // llvm.aarch64.neon.sqdmull
    aarch64_neon_sqdmulls_scalar,              // llvm.aarch64.neon.sqdmulls.scalar
    aarch64_neon_sqneg,                        // llvm.aarch64.neon.sqneg
    aarch64_neon_sqrdmlah,                     // llvm.aarch64.neon.sqrdmlah
    aarch64_neon_sqrdmlsh,                     // llvm.aarch64.neon.sqrdmlsh
    aarch64_neon_sqrdmulh,                     // llvm.aarch64.neon.sqrdmulh
    aarch64_neon_sqrdmulh_lane,                // llvm.aarch64.neon.sqrdmulh.lane
    aarch64_neon_sqrdmulh_laneq,               // llvm.aarch64.neon.sqrdmulh.laneq
    aarch64_neon_sqrshl,                       // llvm.aarch64.neon.sqrshl
    aarch64_neon_sqrshrn,                      // llvm.aarch64.neon.sqrshrn
    aarch64_neon_sqrshrun,                     // llvm.aarch64.neon.sqrshrun
    aarch64_neon_sqshl,                        // llvm.aarch64.neon.sqshl
    aarch64_neon_sqshlu,                       // llvm.aarch64.neon.sqshlu
    aarch64_neon_sqshrn,                       // llvm.aarch64.neon.sqshrn
    aarch64_neon_sqshrun,                      // llvm.aarch64.neon.sqshrun
    aarch64_neon_sqsub,                        // llvm.aarch64.neon.sqsub
    aarch64_neon_sqxtn,                        // llvm.aarch64.neon.sqxtn
    aarch64_neon_sqxtun,                       // llvm.aarch64.neon.sqxtun
    aarch64_neon_srhadd,                       // llvm.aarch64.neon.srhadd
    aarch64_neon_srshl,                        // llvm.aarch64.neon.srshl
    aarch64_neon_sshl,                         // llvm.aarch64.neon.sshl
    aarch64_neon_sshll,                        // llvm.aarch64.neon.sshll
    aarch64_neon_st1x2,                        // llvm.aarch64.neon.st1x2
    aarch64_neon_st1x3,                        // llvm.aarch64.neon.st1x3
    aarch64_neon_st1x4,                        // llvm.aarch64.neon.st1x4
    aarch64_neon_st2,                          // llvm.aarch64.neon.st2
    aarch64_neon_st2lane,                      // llvm.aarch64.neon.st2lane
    aarch64_neon_st3,                          // llvm.aarch64.neon.st3
    aarch64_neon_st3lane,                      // llvm.aarch64.neon.st3lane
    aarch64_neon_st4,                          // llvm.aarch64.neon.st4
    aarch64_neon_st4lane,                      // llvm.aarch64.neon.st4lane
    aarch64_neon_subhn,                        // llvm.aarch64.neon.subhn
    aarch64_neon_suqadd,                       // llvm.aarch64.neon.suqadd
    aarch64_neon_tbl1,                         // llvm.aarch64.neon.tbl1
    aarch64_neon_tbl2,                         // llvm.aarch64.neon.tbl2
    aarch64_neon_tbl3,                         // llvm.aarch64.neon.tbl3
    aarch64_neon_tbl4,                         // llvm.aarch64.neon.tbl4
    aarch64_neon_tbx1,                         // llvm.aarch64.neon.tbx1
    aarch64_neon_tbx2,                         // llvm.aarch64.neon.tbx2
    aarch64_neon_tbx3,                         // llvm.aarch64.neon.tbx3
    aarch64_neon_tbx4,                         // llvm.aarch64.neon.tbx4
    aarch64_neon_uabd,                         // llvm.aarch64.neon.uabd
    aarch64_neon_uaddlp,                       // llvm.aarch64.neon.uaddlp
    aarch64_neon_uaddlv,                       // llvm.aarch64.neon.uaddlv
    aarch64_neon_uaddv,                        // llvm.aarch64.neon.uaddv
    aarch64_neon_udot,                         // llvm.aarch64.neon.udot
    aarch64_neon_uhadd,                        // llvm.aarch64.neon.uhadd
    aarch64_neon_uhsub,                        // llvm.aarch64.neon.uhsub
    aarch64_neon_umax,                         // llvm.aarch64.neon.umax
    aarch64_neon_umaxp,                        // llvm.aarch64.neon.umaxp
    aarch64_neon_umaxv,                        // llvm.aarch64.neon.umaxv
    aarch64_neon_umin,                         // llvm.aarch64.neon.umin
    aarch64_neon_uminp,                        // llvm.aarch64.neon.uminp
    aarch64_neon_uminv,                        // llvm.aarch64.neon.uminv
    aarch64_neon_ummla,                        // llvm.aarch64.neon.ummla
    aarch64_neon_umull,                        // llvm.aarch64.neon.umull
    aarch64_neon_uqadd,                        // llvm.aarch64.neon.uqadd
    aarch64_neon_uqrshl,                       // llvm.aarch64.neon.uqrshl
    aarch64_neon_uqrshrn,                      // llvm.aarch64.neon.uqrshrn
    aarch64_neon_uqshl,                        // llvm.aarch64.neon.uqshl
    aarch64_neon_uqshrn,                       // llvm.aarch64.neon.uqshrn
    aarch64_neon_uqsub,                        // llvm.aarch64.neon.uqsub
    aarch64_neon_uqxtn,                        // llvm.aarch64.neon.uqxtn
    aarch64_neon_urecpe,                       // llvm.aarch64.neon.urecpe
    aarch64_neon_urhadd,                       // llvm.aarch64.neon.urhadd
    aarch64_neon_urshl,                        // llvm.aarch64.neon.urshl
    aarch64_neon_ursqrte,                      // llvm.aarch64.neon.ursqrte
    aarch64_neon_usdot,                        // llvm.aarch64.neon.usdot
    aarch64_neon_ushl,                         // llvm.aarch64.neon.ushl
    aarch64_neon_ushll,                        // llvm.aarch64.neon.ushll
    aarch64_neon_usmmla,                       // llvm.aarch64.neon.usmmla
    aarch64_neon_usqadd,                       // llvm.aarch64.neon.usqadd
    aarch64_neon_vcadd_rot270,                 // llvm.aarch64.neon.vcadd.rot270
    aarch64_neon_vcadd_rot90,                  // llvm.aarch64.neon.vcadd.rot90
    aarch64_neon_vcmla_rot0,                   // llvm.aarch64.neon.vcmla.rot0
    aarch64_neon_vcmla_rot180,                 // llvm.aarch64.neon.vcmla.rot180
    aarch64_neon_vcmla_rot270,                 // llvm.aarch64.neon.vcmla.rot270
    aarch64_neon_vcmla_rot90,                  // llvm.aarch64.neon.vcmla.rot90
    aarch64_neon_vcopy_lane,                   // llvm.aarch64.neon.vcopy.lane
    aarch64_neon_vcvtfp2fxs,                   // llvm.aarch64.neon.vcvtfp2fxs
    aarch64_neon_vcvtfp2fxu,                   // llvm.aarch64.neon.vcvtfp2fxu
    aarch64_neon_vcvtfp2hf,                    // llvm.aarch64.neon.vcvtfp2hf
    aarch64_neon_vcvtfxs2fp,                   // llvm.aarch64.neon.vcvtfxs2fp
    aarch64_neon_vcvtfxu2fp,                   // llvm.aarch64.neon.vcvtfxu2fp
    aarch64_neon_vcvthf2fp,                    // llvm.aarch64.neon.vcvthf2fp
    aarch64_neon_vsli,                         // llvm.aarch64.neon.vsli
    aarch64_neon_vsri,                         // llvm.aarch64.neon.vsri
    aarch64_prefetch,                          // llvm.aarch64.prefetch
    aarch64_rndr,                              // llvm.aarch64.rndr
    aarch64_rndrrs,                            // llvm.aarch64.rndrrs
    aarch64_sdiv,                              // llvm.aarch64.sdiv
    aarch64_set_fpcr,                          // llvm.aarch64.set.fpcr
    aarch64_settag,                            // llvm.aarch64.settag
    aarch64_settag_zero,                       // llvm.aarch64.settag.zero
    aarch64_sisd_fabd,                         // llvm.aarch64.sisd.fabd
    aarch64_sisd_fcvtxn,                       // llvm.aarch64.sisd.fcvtxn
    aarch64_sme_add_write_single_za_vg1x2,     // llvm.aarch64.sme.add.write.single.za.vg1x2
    aarch64_sme_add_write_single_za_vg1x4,     // llvm.aarch64.sme.add.write.single.za.vg1x4
    aarch64_sme_add_write_za_vg1x2,            // llvm.aarch64.sme.add.write.za.vg1x2
    aarch64_sme_add_write_za_vg1x4,            // llvm.aarch64.sme.add.write.za.vg1x4
    aarch64_sme_add_za32_vg1x2,                // llvm.aarch64.sme.add.za32.vg1x2
    aarch64_sme_add_za32_vg1x4,                // llvm.aarch64.sme.add.za32.vg1x4
    aarch64_sme_add_za64_vg1x2,                // llvm.aarch64.sme.add.za64.vg1x2
    aarch64_sme_add_za64_vg1x4,                // llvm.aarch64.sme.add.za64.vg1x4
    aarch64_sme_addha,                         // llvm.aarch64.sme.addha
    aarch64_sme_addva,                         // llvm.aarch64.sme.addva
    aarch64_sme_bmopa_za32,                    // llvm.aarch64.sme.bmopa.za32
    aarch64_sme_bmops_za32,                    // llvm.aarch64.sme.bmops.za32
    aarch64_sme_cntsb,                         // llvm.aarch64.sme.cntsb
    aarch64_sme_cntsd,                         // llvm.aarch64.sme.cntsd
    aarch64_sme_cntsh,                         // llvm.aarch64.sme.cntsh
    aarch64_sme_cntsw,                         // llvm.aarch64.sme.cntsw
    aarch64_sme_fdot_lane_za32_vg1x2,          // llvm.aarch64.sme.fdot.lane.za32.vg1x2
    aarch64_sme_fdot_lane_za32_vg1x4,          // llvm.aarch64.sme.fdot.lane.za32.vg1x4
    aarch64_sme_fdot_single_za32_vg1x2,        // llvm.aarch64.sme.fdot.single.za32.vg1x2
    aarch64_sme_fdot_single_za32_vg1x4,        // llvm.aarch64.sme.fdot.single.za32.vg1x4
    aarch64_sme_fdot_za32_vg1x2,               // llvm.aarch64.sme.fdot.za32.vg1x2
    aarch64_sme_fdot_za32_vg1x4,               // llvm.aarch64.sme.fdot.za32.vg1x4
    aarch64_sme_fmla_lane_vg1x2,               // llvm.aarch64.sme.fmla.lane.vg1x2
    aarch64_sme_fmla_lane_vg1x4,               // llvm.aarch64.sme.fmla.lane.vg1x4
    aarch64_sme_fmla_single_vg1x2,             // llvm.aarch64.sme.fmla.single.vg1x2
    aarch64_sme_fmla_single_vg1x4,             // llvm.aarch64.sme.fmla.single.vg1x4
    aarch64_sme_fmla_vg1x2,                    // llvm.aarch64.sme.fmla.vg1x2
    aarch64_sme_fmla_vg1x4,                    // llvm.aarch64.sme.fmla.vg1x4
    aarch64_sme_fmlal_lane_vg2x1,              // llvm.aarch64.sme.fmlal.lane.vg2x1
    aarch64_sme_fmlal_lane_vg2x2,              // llvm.aarch64.sme.fmlal.lane.vg2x2
    aarch64_sme_fmlal_lane_vg2x4,              // llvm.aarch64.sme.fmlal.lane.vg2x4
    aarch64_sme_fmlal_single_vg2x1,            // llvm.aarch64.sme.fmlal.single.vg2x1
    aarch64_sme_fmlal_single_vg2x2,            // llvm.aarch64.sme.fmlal.single.vg2x2
    aarch64_sme_fmlal_single_vg2x4,            // llvm.aarch64.sme.fmlal.single.vg2x4
    aarch64_sme_fmlal_vg2x2,                   // llvm.aarch64.sme.fmlal.vg2x2
    aarch64_sme_fmlal_vg2x4,                   // llvm.aarch64.sme.fmlal.vg2x4
    aarch64_sme_fmls_lane_vg1x2,               // llvm.aarch64.sme.fmls.lane.vg1x2
    aarch64_sme_fmls_lane_vg1x4,               // llvm.aarch64.sme.fmls.lane.vg1x4
    aarch64_sme_fmls_single_vg1x2,             // llvm.aarch64.sme.fmls.single.vg1x2
    aarch64_sme_fmls_single_vg1x4,             // llvm.aarch64.sme.fmls.single.vg1x4
    aarch64_sme_fmls_vg1x2,                    // llvm.aarch64.sme.fmls.vg1x2
    aarch64_sme_fmls_vg1x4,                    // llvm.aarch64.sme.fmls.vg1x4
    aarch64_sme_fmlsl_lane_vg2x1,              // llvm.aarch64.sme.fmlsl.lane.vg2x1
    aarch64_sme_fmlsl_lane_vg2x2,              // llvm.aarch64.sme.fmlsl.lane.vg2x2
    aarch64_sme_fmlsl_lane_vg2x4,              // llvm.aarch64.sme.fmlsl.lane.vg2x4
    aarch64_sme_fmlsl_single_vg2x1,            // llvm.aarch64.sme.fmlsl.single.vg2x1
    aarch64_sme_fmlsl_single_vg2x2,            // llvm.aarch64.sme.fmlsl.single.vg2x2
    aarch64_sme_fmlsl_single_vg2x4,            // llvm.aarch64.sme.fmlsl.single.vg2x4
    aarch64_sme_fmlsl_vg2x2,                   // llvm.aarch64.sme.fmlsl.vg2x2
    aarch64_sme_fmlsl_vg2x4,                   // llvm.aarch64.sme.fmlsl.vg2x4
    aarch64_sme_fvdot_lane_za32_vg1x2,         // llvm.aarch64.sme.fvdot.lane.za32.vg1x2
    aarch64_sme_get_tpidr2,                    // llvm.aarch64.sme.get.tpidr2
    aarch64_sme_ld1b_horiz,                    // llvm.aarch64.sme.ld1b.horiz
    aarch64_sme_ld1b_vert,                     // llvm.aarch64.sme.ld1b.vert
    aarch64_sme_ld1d_horiz,                    // llvm.aarch64.sme.ld1d.horiz
    aarch64_sme_ld1d_vert,                     // llvm.aarch64.sme.ld1d.vert
    aarch64_sme_ld1h_horiz,                    // llvm.aarch64.sme.ld1h.horiz
    aarch64_sme_ld1h_vert,                     // llvm.aarch64.sme.ld1h.vert
    aarch64_sme_ld1q_horiz,                    // llvm.aarch64.sme.ld1q.horiz
    aarch64_sme_ld1q_vert,                     // llvm.aarch64.sme.ld1q.vert
    aarch64_sme_ld1w_horiz,                    // llvm.aarch64.sme.ld1w.horiz
    aarch64_sme_ld1w_vert,                     // llvm.aarch64.sme.ld1w.vert
    aarch64_sme_ldr,                           // llvm.aarch64.sme.ldr
    aarch64_sme_mopa,                          // llvm.aarch64.sme.mopa
    aarch64_sme_mopa_wide,                     // llvm.aarch64.sme.mopa.wide
    aarch64_sme_mops,                          // llvm.aarch64.sme.mops
    aarch64_sme_mops_wide,                     // llvm.aarch64.sme.mops.wide
    aarch64_sme_read_hor_vg2,                  // llvm.aarch64.sme.read.hor.vg2
    aarch64_sme_read_hor_vg4,                  // llvm.aarch64.sme.read.hor.vg4
    aarch64_sme_read_horiz,                    // llvm.aarch64.sme.read.horiz
    aarch64_sme_read_ver_vg2,                  // llvm.aarch64.sme.read.ver.vg2
    aarch64_sme_read_ver_vg4,                  // llvm.aarch64.sme.read.ver.vg4
    aarch64_sme_read_vert,                     // llvm.aarch64.sme.read.vert
    aarch64_sme_read_vg1x2,                    // llvm.aarch64.sme.read.vg1x2
    aarch64_sme_read_vg1x4,                    // llvm.aarch64.sme.read.vg1x4
    aarch64_sme_readq_horiz,                   // llvm.aarch64.sme.readq.horiz
    aarch64_sme_readq_vert,                    // llvm.aarch64.sme.readq.vert
    aarch64_sme_sdot_lane_za32_vg1x2,          // llvm.aarch64.sme.sdot.lane.za32.vg1x2
    aarch64_sme_sdot_lane_za32_vg1x4,          // llvm.aarch64.sme.sdot.lane.za32.vg1x4
    aarch64_sme_sdot_lane_za64_vg1x2,          // llvm.aarch64.sme.sdot.lane.za64.vg1x2
    aarch64_sme_sdot_lane_za64_vg1x4,          // llvm.aarch64.sme.sdot.lane.za64.vg1x4
    aarch64_sme_sdot_single_za32_vg1x2,        // llvm.aarch64.sme.sdot.single.za32.vg1x2
    aarch64_sme_sdot_single_za32_vg1x4,        // llvm.aarch64.sme.sdot.single.za32.vg1x4
    aarch64_sme_sdot_single_za64_vg1x2,        // llvm.aarch64.sme.sdot.single.za64.vg1x2
    aarch64_sme_sdot_single_za64_vg1x4,        // llvm.aarch64.sme.sdot.single.za64.vg1x4
    aarch64_sme_sdot_za32_vg1x2,               // llvm.aarch64.sme.sdot.za32.vg1x2
    aarch64_sme_sdot_za32_vg1x4,               // llvm.aarch64.sme.sdot.za32.vg1x4
    aarch64_sme_sdot_za64_vg1x2,               // llvm.aarch64.sme.sdot.za64.vg1x2
    aarch64_sme_sdot_za64_vg1x4,               // llvm.aarch64.sme.sdot.za64.vg1x4
    aarch64_sme_set_tpidr2,                    // llvm.aarch64.sme.set.tpidr2
    aarch64_sme_smla_za32_lane_vg4x1,          // llvm.aarch64.sme.smla.za32.lane.vg4x1
    aarch64_sme_smla_za32_lane_vg4x2,          // llvm.aarch64.sme.smla.za32.lane.vg4x2
    aarch64_sme_smla_za32_lane_vg4x4,          // llvm.aarch64.sme.smla.za32.lane.vg4x4
    aarch64_sme_smla_za32_single_vg4x1,        // llvm.aarch64.sme.smla.za32.single.vg4x1
    aarch64_sme_smla_za32_single_vg4x2,        // llvm.aarch64.sme.smla.za32.single.vg4x2
    aarch64_sme_smla_za32_single_vg4x4,        // llvm.aarch64.sme.smla.za32.single.vg4x4
    aarch64_sme_smla_za32_vg4x2,               // llvm.aarch64.sme.smla.za32.vg4x2
    aarch64_sme_smla_za32_vg4x4,               // llvm.aarch64.sme.smla.za32.vg4x4
    aarch64_sme_smla_za64_lane_vg4x1,          // llvm.aarch64.sme.smla.za64.lane.vg4x1
    aarch64_sme_smla_za64_lane_vg4x2,          // llvm.aarch64.sme.smla.za64.lane.vg4x2
    aarch64_sme_smla_za64_lane_vg4x4,          // llvm.aarch64.sme.smla.za64.lane.vg4x4
    aarch64_sme_smla_za64_single_vg4x1,        // llvm.aarch64.sme.smla.za64.single.vg4x1
    aarch64_sme_smla_za64_single_vg4x2,        // llvm.aarch64.sme.smla.za64.single.vg4x2
    aarch64_sme_smla_za64_single_vg4x4,        // llvm.aarch64.sme.smla.za64.single.vg4x4
    aarch64_sme_smla_za64_vg4x2,               // llvm.aarch64.sme.smla.za64.vg4x2
    aarch64_sme_smla_za64_vg4x4,               // llvm.aarch64.sme.smla.za64.vg4x4
    aarch64_sme_smlal_lane_vg2x1,              // llvm.aarch64.sme.smlal.lane.vg2x1
    aarch64_sme_smlal_lane_vg2x2,              // llvm.aarch64.sme.smlal.lane.vg2x2
    aarch64_sme_smlal_lane_vg2x4,              // llvm.aarch64.sme.smlal.lane.vg2x4
    aarch64_sme_smlal_single_vg2x1,            // llvm.aarch64.sme.smlal.single.vg2x1
    aarch64_sme_smlal_single_vg2x2,            // llvm.aarch64.sme.smlal.single.vg2x2
    aarch64_sme_smlal_single_vg2x4,            // llvm.aarch64.sme.smlal.single.vg2x4
    aarch64_sme_smlal_vg2x2,                   // llvm.aarch64.sme.smlal.vg2x2
    aarch64_sme_smlal_vg2x4,                   // llvm.aarch64.sme.smlal.vg2x4
    aarch64_sme_smls_za32_lane_vg4x1,          // llvm.aarch64.sme.smls.za32.lane.vg4x1
    aarch64_sme_smls_za32_lane_vg4x2,          // llvm.aarch64.sme.smls.za32.lane.vg4x2
    aarch64_sme_smls_za32_lane_vg4x4,          // llvm.aarch64.sme.smls.za32.lane.vg4x4
    aarch64_sme_smls_za32_single_vg4x1,        // llvm.aarch64.sme.smls.za32.single.vg4x1
    aarch64_sme_smls_za32_single_vg4x2,        // llvm.aarch64.sme.smls.za32.single.vg4x2
    aarch64_sme_smls_za32_single_vg4x4,        // llvm.aarch64.sme.smls.za32.single.vg4x4
    aarch64_sme_smls_za32_vg4x2,               // llvm.aarch64.sme.smls.za32.vg4x2
    aarch64_sme_smls_za32_vg4x4,               // llvm.aarch64.sme.smls.za32.vg4x4
    aarch64_sme_smls_za64_lane_vg4x1,          // llvm.aarch64.sme.smls.za64.lane.vg4x1
    aarch64_sme_smls_za64_lane_vg4x2,          // llvm.aarch64.sme.smls.za64.lane.vg4x2
    aarch64_sme_smls_za64_lane_vg4x4,          // llvm.aarch64.sme.smls.za64.lane.vg4x4
    aarch64_sme_smls_za64_single_vg4x1,        // llvm.aarch64.sme.smls.za64.single.vg4x1
    aarch64_sme_smls_za64_single_vg4x2,        // llvm.aarch64.sme.smls.za64.single.vg4x2
    aarch64_sme_smls_za64_single_vg4x4,        // llvm.aarch64.sme.smls.za64.single.vg4x4
    aarch64_sme_smls_za64_vg4x2,               // llvm.aarch64.sme.smls.za64.vg4x2
    aarch64_sme_smls_za64_vg4x4,               // llvm.aarch64.sme.smls.za64.vg4x4
    aarch64_sme_smlsl_lane_vg2x1,              // llvm.aarch64.sme.smlsl.lane.vg2x1
    aarch64_sme_smlsl_lane_vg2x2,              // llvm.aarch64.sme.smlsl.lane.vg2x2
    aarch64_sme_smlsl_lane_vg2x4,              // llvm.aarch64.sme.smlsl.lane.vg2x4
    aarch64_sme_smlsl_single_vg2x1,            // llvm.aarch64.sme.smlsl.single.vg2x1
    aarch64_sme_smlsl_single_vg2x2,            // llvm.aarch64.sme.smlsl.single.vg2x2
    aarch64_sme_smlsl_single_vg2x4,            // llvm.aarch64.sme.smlsl.single.vg2x4
    aarch64_sme_smlsl_vg2x2,                   // llvm.aarch64.sme.smlsl.vg2x2
    aarch64_sme_smlsl_vg2x4,                   // llvm.aarch64.sme.smlsl.vg2x4
    aarch64_sme_smopa_wide,                    // llvm.aarch64.sme.smopa.wide
    aarch64_sme_smopa_za32,                    // llvm.aarch64.sme.smopa.za32
    aarch64_sme_smops_wide,                    // llvm.aarch64.sme.smops.wide
    aarch64_sme_smops_za32,                    // llvm.aarch64.sme.smops.za32
    aarch64_sme_st1b_horiz,                    // llvm.aarch64.sme.st1b.horiz
    aarch64_sme_st1b_vert,                     // llvm.aarch64.sme.st1b.vert
    aarch64_sme_st1d_horiz,                    // llvm.aarch64.sme.st1d.horiz
    aarch64_sme_st1d_vert,                     // llvm.aarch64.sme.st1d.vert
    aarch64_sme_st1h_horiz,                    // llvm.aarch64.sme.st1h.horiz
    aarch64_sme_st1h_vert,                     // llvm.aarch64.sme.st1h.vert
    aarch64_sme_st1q_horiz,                    // llvm.aarch64.sme.st1q.horiz
    aarch64_sme_st1q_vert,                     // llvm.aarch64.sme.st1q.vert
    aarch64_sme_st1w_horiz,                    // llvm.aarch64.sme.st1w.horiz
    aarch64_sme_st1w_vert,                     // llvm.aarch64.sme.st1w.vert
    aarch64_sme_str,                           // llvm.aarch64.sme.str
    aarch64_sme_sub_write_single_za_vg1x2,     // llvm.aarch64.sme.sub.write.single.za.vg1x2
    aarch64_sme_sub_write_single_za_vg1x4,     // llvm.aarch64.sme.sub.write.single.za.vg1x4
    aarch64_sme_sub_write_za_vg1x2,            // llvm.aarch64.sme.sub.write.za.vg1x2
    aarch64_sme_sub_write_za_vg1x4,            // llvm.aarch64.sme.sub.write.za.vg1x4
    aarch64_sme_sub_za32_vg1x2,                // llvm.aarch64.sme.sub.za32.vg1x2
    aarch64_sme_sub_za32_vg1x4,                // llvm.aarch64.sme.sub.za32.vg1x4
    aarch64_sme_sub_za64_vg1x2,                // llvm.aarch64.sme.sub.za64.vg1x2
    aarch64_sme_sub_za64_vg1x4,                // llvm.aarch64.sme.sub.za64.vg1x4
    aarch64_sme_sudot_lane_za32_vg1x2,         // llvm.aarch64.sme.sudot.lane.za32.vg1x2
    aarch64_sme_sudot_lane_za32_vg1x4,         // llvm.aarch64.sme.sudot.lane.za32.vg1x4
    aarch64_sme_sudot_single_za32_vg1x2,       // llvm.aarch64.sme.sudot.single.za32.vg1x2
    aarch64_sme_sudot_single_za32_vg1x4,       // llvm.aarch64.sme.sudot.single.za32.vg1x4
    aarch64_sme_sumla_za32_lane_vg4x1,         // llvm.aarch64.sme.sumla.za32.lane.vg4x1
    aarch64_sme_sumla_za32_lane_vg4x2,         // llvm.aarch64.sme.sumla.za32.lane.vg4x2
    aarch64_sme_sumla_za32_lane_vg4x4,         // llvm.aarch64.sme.sumla.za32.lane.vg4x4
    aarch64_sme_sumla_za32_single_vg4x2,       // llvm.aarch64.sme.sumla.za32.single.vg4x2
    aarch64_sme_sumla_za32_single_vg4x4,       // llvm.aarch64.sme.sumla.za32.single.vg4x4
    aarch64_sme_sumopa_wide,                   // llvm.aarch64.sme.sumopa.wide
    aarch64_sme_sumops_wide,                   // llvm.aarch64.sme.sumops.wide
    aarch64_sme_suvdot_lane_za32_vg1x4,        // llvm.aarch64.sme.suvdot.lane.za32.vg1x4
    aarch64_sme_svdot_lane_za32_vg1x2,         // llvm.aarch64.sme.svdot.lane.za32.vg1x2
    aarch64_sme_svdot_lane_za32_vg1x4,         // llvm.aarch64.sme.svdot.lane.za32.vg1x4
    aarch64_sme_svdot_lane_za64_vg1x4,         // llvm.aarch64.sme.svdot.lane.za64.vg1x4
    aarch64_sme_udot_lane_za32_vg1x2,          // llvm.aarch64.sme.udot.lane.za32.vg1x2
    aarch64_sme_udot_lane_za32_vg1x4,          // llvm.aarch64.sme.udot.lane.za32.vg1x4
    aarch64_sme_udot_lane_za64_vg1x2,          // llvm.aarch64.sme.udot.lane.za64.vg1x2
    aarch64_sme_udot_lane_za64_vg1x4,          // llvm.aarch64.sme.udot.lane.za64.vg1x4
    aarch64_sme_udot_single_za32_vg1x2,        // llvm.aarch64.sme.udot.single.za32.vg1x2
    aarch64_sme_udot_single_za32_vg1x4,        // llvm.aarch64.sme.udot.single.za32.vg1x4
    aarch64_sme_udot_single_za64_vg1x2,        // llvm.aarch64.sme.udot.single.za64.vg1x2
    aarch64_sme_udot_single_za64_vg1x4,        // llvm.aarch64.sme.udot.single.za64.vg1x4
    aarch64_sme_udot_za32_vg1x2,               // llvm.aarch64.sme.udot.za32.vg1x2
    aarch64_sme_udot_za32_vg1x4,               // llvm.aarch64.sme.udot.za32.vg1x4
    aarch64_sme_udot_za64_vg1x2,               // llvm.aarch64.sme.udot.za64.vg1x2
    aarch64_sme_udot_za64_vg1x4,               // llvm.aarch64.sme.udot.za64.vg1x4
    aarch64_sme_umla_za32_lane_vg4x1,          // llvm.aarch64.sme.umla.za32.lane.vg4x1
    aarch64_sme_umla_za32_lane_vg4x2,          // llvm.aarch64.sme.umla.za32.lane.vg4x2
    aarch64_sme_umla_za32_lane_vg4x4,          // llvm.aarch64.sme.umla.za32.lane.vg4x4
    aarch64_sme_umla_za32_single_vg4x1,        // llvm.aarch64.sme.umla.za32.single.vg4x1
    aarch64_sme_umla_za32_single_vg4x2,        // llvm.aarch64.sme.umla.za32.single.vg4x2
    aarch64_sme_umla_za32_single_vg4x4,        // llvm.aarch64.sme.umla.za32.single.vg4x4
    aarch64_sme_umla_za32_vg4x2,               // llvm.aarch64.sme.umla.za32.vg4x2
    aarch64_sme_umla_za32_vg4x4,               // llvm.aarch64.sme.umla.za32.vg4x4
    aarch64_sme_umla_za64_lane_vg4x1,          // llvm.aarch64.sme.umla.za64.lane.vg4x1
    aarch64_sme_umla_za64_lane_vg4x2,          // llvm.aarch64.sme.umla.za64.lane.vg4x2
    aarch64_sme_umla_za64_lane_vg4x4,          // llvm.aarch64.sme.umla.za64.lane.vg4x4
    aarch64_sme_umla_za64_single_vg4x1,        // llvm.aarch64.sme.umla.za64.single.vg4x1
    aarch64_sme_umla_za64_single_vg4x2,        // llvm.aarch64.sme.umla.za64.single.vg4x2
    aarch64_sme_umla_za64_single_vg4x4,        // llvm.aarch64.sme.umla.za64.single.vg4x4
    aarch64_sme_umla_za64_vg4x2,               // llvm.aarch64.sme.umla.za64.vg4x2
    aarch64_sme_umla_za64_vg4x4,               // llvm.aarch64.sme.umla.za64.vg4x4
    aarch64_sme_umlal_lane_vg2x1,              // llvm.aarch64.sme.umlal.lane.vg2x1
    aarch64_sme_umlal_lane_vg2x2,              // llvm.aarch64.sme.umlal.lane.vg2x2
    aarch64_sme_umlal_lane_vg2x4,              // llvm.aarch64.sme.umlal.lane.vg2x4
    aarch64_sme_umlal_single_vg2x1,            // llvm.aarch64.sme.umlal.single.vg2x1
    aarch64_sme_umlal_single_vg2x2,            // llvm.aarch64.sme.umlal.single.vg2x2
    aarch64_sme_umlal_single_vg2x4,            // llvm.aarch64.sme.umlal.single.vg2x4
    aarch64_sme_umlal_vg2x2,                   // llvm.aarch64.sme.umlal.vg2x2
    aarch64_sme_umlal_vg2x4,                   // llvm.aarch64.sme.umlal.vg2x4
    aarch64_sme_umls_za32_lane_vg4x1,          // llvm.aarch64.sme.umls.za32.lane.vg4x1
    aarch64_sme_umls_za32_lane_vg4x2,          // llvm.aarch64.sme.umls.za32.lane.vg4x2
    aarch64_sme_umls_za32_lane_vg4x4,          // llvm.aarch64.sme.umls.za32.lane.vg4x4
    aarch64_sme_umls_za32_single_vg4x1,        // llvm.aarch64.sme.umls.za32.single.vg4x1
    aarch64_sme_umls_za32_single_vg4x2,        // llvm.aarch64.sme.umls.za32.single.vg4x2
    aarch64_sme_umls_za32_single_vg4x4,        // llvm.aarch64.sme.umls.za32.single.vg4x4
    aarch64_sme_umls_za32_vg4x2,               // llvm.aarch64.sme.umls.za32.vg4x2
    aarch64_sme_umls_za32_vg4x4,               // llvm.aarch64.sme.umls.za32.vg4x4
    aarch64_sme_umls_za64_lane_vg4x1,          // llvm.aarch64.sme.umls.za64.lane.vg4x1
    aarch64_sme_umls_za64_lane_vg4x2,          // llvm.aarch64.sme.umls.za64.lane.vg4x2
    aarch64_sme_umls_za64_lane_vg4x4,          // llvm.aarch64.sme.umls.za64.lane.vg4x4
    aarch64_sme_umls_za64_single_vg4x1,        // llvm.aarch64.sme.umls.za64.single.vg4x1
    aarch64_sme_umls_za64_single_vg4x2,        // llvm.aarch64.sme.umls.za64.single.vg4x2
    aarch64_sme_umls_za64_single_vg4x4,        // llvm.aarch64.sme.umls.za64.single.vg4x4
    aarch64_sme_umls_za64_vg4x2,               // llvm.aarch64.sme.umls.za64.vg4x2
    aarch64_sme_umls_za64_vg4x4,               // llvm.aarch64.sme.umls.za64.vg4x4
    aarch64_sme_umlsl_lane_vg2x1,              // llvm.aarch64.sme.umlsl.lane.vg2x1
    aarch64_sme_umlsl_lane_vg2x2,              // llvm.aarch64.sme.umlsl.lane.vg2x2
    aarch64_sme_umlsl_lane_vg2x4,              // llvm.aarch64.sme.umlsl.lane.vg2x4
    aarch64_sme_umlsl_single_vg2x1,            // llvm.aarch64.sme.umlsl.single.vg2x1
    aarch64_sme_umlsl_single_vg2x2,            // llvm.aarch64.sme.umlsl.single.vg2x2
    aarch64_sme_umlsl_single_vg2x4,            // llvm.aarch64.sme.umlsl.single.vg2x4
    aarch64_sme_umlsl_vg2x2,                   // llvm.aarch64.sme.umlsl.vg2x2
    aarch64_sme_umlsl_vg2x4,                   // llvm.aarch64.sme.umlsl.vg2x4
    aarch64_sme_umopa_wide,                    // llvm.aarch64.sme.umopa.wide
    aarch64_sme_umopa_za32,                    // llvm.aarch64.sme.umopa.za32
    aarch64_sme_umops_wide,                    // llvm.aarch64.sme.umops.wide
    aarch64_sme_umops_za32,                    // llvm.aarch64.sme.umops.za32
    aarch64_sme_usdot_lane_za32_vg1x2,         // llvm.aarch64.sme.usdot.lane.za32.vg1x2
    aarch64_sme_usdot_lane_za32_vg1x4,         // llvm.aarch64.sme.usdot.lane.za32.vg1x4
    aarch64_sme_usdot_single_za32_vg1x2,       // llvm.aarch64.sme.usdot.single.za32.vg1x2
    aarch64_sme_usdot_single_za32_vg1x4,       // llvm.aarch64.sme.usdot.single.za32.vg1x4
    aarch64_sme_usdot_za32_vg1x2,              // llvm.aarch64.sme.usdot.za32.vg1x2
    aarch64_sme_usdot_za32_vg1x4,              // llvm.aarch64.sme.usdot.za32.vg1x4
    aarch64_sme_usmla_za32_lane_vg4x1,         // llvm.aarch64.sme.usmla.za32.lane.vg4x1
    aarch64_sme_usmla_za32_lane_vg4x2,         // llvm.aarch64.sme.usmla.za32.lane.vg4x2
    aarch64_sme_usmla_za32_lane_vg4x4,         // llvm.aarch64.sme.usmla.za32.lane.vg4x4
    aarch64_sme_usmla_za32_single_vg4x1,       // llvm.aarch64.sme.usmla.za32.single.vg4x1
    aarch64_sme_usmla_za32_single_vg4x2,       // llvm.aarch64.sme.usmla.za32.single.vg4x2
    aarch64_sme_usmla_za32_single_vg4x4,       // llvm.aarch64.sme.usmla.za32.single.vg4x4
    aarch64_sme_usmla_za32_vg4x2,              // llvm.aarch64.sme.usmla.za32.vg4x2
    aarch64_sme_usmla_za32_vg4x4,              // llvm.aarch64.sme.usmla.za32.vg4x4
    aarch64_sme_usmopa_wide,                   // llvm.aarch64.sme.usmopa.wide
    aarch64_sme_usmops_wide,                   // llvm.aarch64.sme.usmops.wide
    aarch64_sme_usvdot_lane_za32_vg1x4,        // llvm.aarch64.sme.usvdot.lane.za32.vg1x4
    aarch64_sme_uvdot_lane_za32_vg1x2,         // llvm.aarch64.sme.uvdot.lane.za32.vg1x2
    aarch64_sme_uvdot_lane_za32_vg1x4,         // llvm.aarch64.sme.uvdot.lane.za32.vg1x4
    aarch64_sme_uvdot_lane_za64_vg1x4,         // llvm.aarch64.sme.uvdot.lane.za64.vg1x4
    aarch64_sme_write_hor_vg2,                 // llvm.aarch64.sme.write.hor.vg2
    aarch64_sme_write_hor_vg4,                 // llvm.aarch64.sme.write.hor.vg4
    aarch64_sme_write_horiz,                   // llvm.aarch64.sme.write.horiz
    aarch64_sme_write_ver_vg2,                 // llvm.aarch64.sme.write.ver.vg2
    aarch64_sme_write_ver_vg4,                 // llvm.aarch64.sme.write.ver.vg4
    aarch64_sme_write_vert,                    // llvm.aarch64.sme.write.vert
    aarch64_sme_write_vg1x2,                   // llvm.aarch64.sme.write.vg1x2
    aarch64_sme_write_vg1x4,                   // llvm.aarch64.sme.write.vg1x4
    aarch64_sme_writeq_horiz,                  // llvm.aarch64.sme.writeq.horiz
    aarch64_sme_writeq_vert,                   // llvm.aarch64.sme.writeq.vert
    aarch64_sme_za_disable,                    // llvm.aarch64.sme.za.disable
    aarch64_sme_za_enable,                     // llvm.aarch64.sme.za.enable
    aarch64_sme_zero,                          // llvm.aarch64.sme.zero
    aarch64_space,                             // llvm.aarch64.space
    aarch64_st64b,                             // llvm.aarch64.st64b
    aarch64_st64bv,                            // llvm.aarch64.st64bv
    aarch64_st64bv0,                           // llvm.aarch64.st64bv0
    aarch64_stg,                               // llvm.aarch64.stg
    aarch64_stgp,                              // llvm.aarch64.stgp
    aarch64_stlxp,                             // llvm.aarch64.stlxp
    aarch64_stlxr,                             // llvm.aarch64.stlxr
    aarch64_stxp,                              // llvm.aarch64.stxp
    aarch64_stxr,                              // llvm.aarch64.stxr
    aarch64_subp,                              // llvm.aarch64.subp
    aarch64_sve_abs,                           // llvm.aarch64.sve.abs
    aarch64_sve_adclb,                         // llvm.aarch64.sve.adclb
    aarch64_sve_adclt,                         // llvm.aarch64.sve.adclt
    aarch64_sve_add,                           // llvm.aarch64.sve.add
    aarch64_sve_add_single_x2,                 // llvm.aarch64.sve.add.single.x2
    aarch64_sve_add_single_x4,                 // llvm.aarch64.sve.add.single.x4
    aarch64_sve_add_u,                         // llvm.aarch64.sve.add.u
    aarch64_sve_addhnb,                        // llvm.aarch64.sve.addhnb
    aarch64_sve_addhnt,                        // llvm.aarch64.sve.addhnt
    aarch64_sve_addp,                          // llvm.aarch64.sve.addp
    aarch64_sve_adrb,                          // llvm.aarch64.sve.adrb
    aarch64_sve_adrd,                          // llvm.aarch64.sve.adrd
    aarch64_sve_adrh,                          // llvm.aarch64.sve.adrh
    aarch64_sve_adrw,                          // llvm.aarch64.sve.adrw
    aarch64_sve_aesd,                          // llvm.aarch64.sve.aesd
    aarch64_sve_aese,                          // llvm.aarch64.sve.aese
    aarch64_sve_aesimc,                        // llvm.aarch64.sve.aesimc
    aarch64_sve_aesmc,                         // llvm.aarch64.sve.aesmc
    aarch64_sve_and,                           // llvm.aarch64.sve.and
    aarch64_sve_and_u,                         // llvm.aarch64.sve.and.u
    aarch64_sve_and_z,                         // llvm.aarch64.sve.and.z
    aarch64_sve_andv,                          // llvm.aarch64.sve.andv
    aarch64_sve_asr,                           // llvm.aarch64.sve.asr
    aarch64_sve_asr_u,                         // llvm.aarch64.sve.asr.u
    aarch64_sve_asr_wide,                      // llvm.aarch64.sve.asr.wide
    aarch64_sve_asrd,                          // llvm.aarch64.sve.asrd
    aarch64_sve_bcax,                          // llvm.aarch64.sve.bcax
    aarch64_sve_bdep_x,                        // llvm.aarch64.sve.bdep.x
    aarch64_sve_bext_x,                        // llvm.aarch64.sve.bext.x
    aarch64_sve_bfcvt_x2,                      // llvm.aarch64.sve.bfcvt.x2
    aarch64_sve_bfcvtn_x2,                     // llvm.aarch64.sve.bfcvtn.x2
    aarch64_sve_bfdot,                         // llvm.aarch64.sve.bfdot
    aarch64_sve_bfdot_lane_v2,                 // llvm.aarch64.sve.bfdot.lane.v2
    aarch64_sve_bfmlalb,                       // llvm.aarch64.sve.bfmlalb
    aarch64_sve_bfmlalb_lane_v2,               // llvm.aarch64.sve.bfmlalb.lane.v2
    aarch64_sve_bfmlalt,                       // llvm.aarch64.sve.bfmlalt
    aarch64_sve_bfmlalt_lane_v2,               // llvm.aarch64.sve.bfmlalt.lane.v2
    aarch64_sve_bfmmla,                        // llvm.aarch64.sve.bfmmla
    aarch64_sve_bgrp_x,                        // llvm.aarch64.sve.bgrp.x
    aarch64_sve_bic,                           // llvm.aarch64.sve.bic
    aarch64_sve_bic_u,                         // llvm.aarch64.sve.bic.u
    aarch64_sve_bic_z,                         // llvm.aarch64.sve.bic.z
    aarch64_sve_brka,                          // llvm.aarch64.sve.brka
    aarch64_sve_brka_z,                        // llvm.aarch64.sve.brka.z
    aarch64_sve_brkb,                          // llvm.aarch64.sve.brkb
    aarch64_sve_brkb_z,                        // llvm.aarch64.sve.brkb.z
    aarch64_sve_brkn_z,                        // llvm.aarch64.sve.brkn.z
    aarch64_sve_brkpa_z,                       // llvm.aarch64.sve.brkpa.z
    aarch64_sve_brkpb_z,                       // llvm.aarch64.sve.brkpb.z
    aarch64_sve_bsl,                           // llvm.aarch64.sve.bsl
    aarch64_sve_bsl1n,                         // llvm.aarch64.sve.bsl1n
    aarch64_sve_bsl2n,                         // llvm.aarch64.sve.bsl2n
    aarch64_sve_cadd_x,                        // llvm.aarch64.sve.cadd.x
    aarch64_sve_cdot,                          // llvm.aarch64.sve.cdot
    aarch64_sve_cdot_lane,                     // llvm.aarch64.sve.cdot.lane
    aarch64_sve_clasta,                        // llvm.aarch64.sve.clasta
    aarch64_sve_clasta_n,                      // llvm.aarch64.sve.clasta.n
    aarch64_sve_clastb,                        // llvm.aarch64.sve.clastb
    aarch64_sve_clastb_n,                      // llvm.aarch64.sve.clastb.n
    aarch64_sve_cls,                           // llvm.aarch64.sve.cls
    aarch64_sve_clz,                           // llvm.aarch64.sve.clz
    aarch64_sve_cmla_lane_x,                   // llvm.aarch64.sve.cmla.lane.x
    aarch64_sve_cmla_x,                        // llvm.aarch64.sve.cmla.x
    aarch64_sve_cmpeq,                         // llvm.aarch64.sve.cmpeq
    aarch64_sve_cmpeq_wide,                    // llvm.aarch64.sve.cmpeq.wide
    aarch64_sve_cmpge,                         // llvm.aarch64.sve.cmpge
    aarch64_sve_cmpge_wide,                    // llvm.aarch64.sve.cmpge.wide
    aarch64_sve_cmpgt,                         // llvm.aarch64.sve.cmpgt
    aarch64_sve_cmpgt_wide,                    // llvm.aarch64.sve.cmpgt.wide
    aarch64_sve_cmphi,                         // llvm.aarch64.sve.cmphi
    aarch64_sve_cmphi_wide,                    // llvm.aarch64.sve.cmphi.wide
    aarch64_sve_cmphs,                         // llvm.aarch64.sve.cmphs
    aarch64_sve_cmphs_wide,                    // llvm.aarch64.sve.cmphs.wide
    aarch64_sve_cmple_wide,                    // llvm.aarch64.sve.cmple.wide
    aarch64_sve_cmplo_wide,                    // llvm.aarch64.sve.cmplo.wide
    aarch64_sve_cmpls_wide,                    // llvm.aarch64.sve.cmpls.wide
    aarch64_sve_cmplt_wide,                    // llvm.aarch64.sve.cmplt.wide
    aarch64_sve_cmpne,                         // llvm.aarch64.sve.cmpne
    aarch64_sve_cmpne_wide,                    // llvm.aarch64.sve.cmpne.wide
    aarch64_sve_cnot,                          // llvm.aarch64.sve.cnot
    aarch64_sve_cnt,                           // llvm.aarch64.sve.cnt
    aarch64_sve_cntb,                          // llvm.aarch64.sve.cntb
    aarch64_sve_cntd,                          // llvm.aarch64.sve.cntd
    aarch64_sve_cnth,                          // llvm.aarch64.sve.cnth
    aarch64_sve_cntp,                          // llvm.aarch64.sve.cntp
    aarch64_sve_cntp_c16,                      // llvm.aarch64.sve.cntp.c16
    aarch64_sve_cntp_c32,                      // llvm.aarch64.sve.cntp.c32
    aarch64_sve_cntp_c64,                      // llvm.aarch64.sve.cntp.c64
    aarch64_sve_cntp_c8,                       // llvm.aarch64.sve.cntp.c8
    aarch64_sve_cntw,                          // llvm.aarch64.sve.cntw
    aarch64_sve_compact,                       // llvm.aarch64.sve.compact
    aarch64_sve_convert_from_svbool,           // llvm.aarch64.sve.convert.from.svbool
    aarch64_sve_convert_to_svbool,             // llvm.aarch64.sve.convert.to.svbool
    aarch64_sve_dup,                           // llvm.aarch64.sve.dup
    aarch64_sve_dup_x,                         // llvm.aarch64.sve.dup.x
    aarch64_sve_dupq_lane,                     // llvm.aarch64.sve.dupq.lane
    aarch64_sve_eor,                           // llvm.aarch64.sve.eor
    aarch64_sve_eor_u,                         // llvm.aarch64.sve.eor.u
    aarch64_sve_eor_z,                         // llvm.aarch64.sve.eor.z
    aarch64_sve_eor3,                          // llvm.aarch64.sve.eor3
    aarch64_sve_eorbt,                         // llvm.aarch64.sve.eorbt
    aarch64_sve_eortb,                         // llvm.aarch64.sve.eortb
    aarch64_sve_eorv,                          // llvm.aarch64.sve.eorv
    aarch64_sve_ext,                           // llvm.aarch64.sve.ext
    aarch64_sve_fabd,                          // llvm.aarch64.sve.fabd
    aarch64_sve_fabd_u,                        // llvm.aarch64.sve.fabd.u
    aarch64_sve_fabs,                          // llvm.aarch64.sve.fabs
    aarch64_sve_facge,                         // llvm.aarch64.sve.facge
    aarch64_sve_facgt,                         // llvm.aarch64.sve.facgt
    aarch64_sve_fadd,                          // llvm.aarch64.sve.fadd
    aarch64_sve_fadd_u,                        // llvm.aarch64.sve.fadd.u
    aarch64_sve_fadda,                         // llvm.aarch64.sve.fadda
    aarch64_sve_faddp,                         // llvm.aarch64.sve.faddp
    aarch64_sve_faddv,                         // llvm.aarch64.sve.faddv
    aarch64_sve_fcadd,                         // llvm.aarch64.sve.fcadd
    aarch64_sve_fclamp,                        // llvm.aarch64.sve.fclamp
    aarch64_sve_fclamp_single_x2,              // llvm.aarch64.sve.fclamp.single.x2
    aarch64_sve_fclamp_single_x4,              // llvm.aarch64.sve.fclamp.single.x4
    aarch64_sve_fcmla,                         // llvm.aarch64.sve.fcmla
    aarch64_sve_fcmla_lane,                    // llvm.aarch64.sve.fcmla.lane
    aarch64_sve_fcmpeq,                        // llvm.aarch64.sve.fcmpeq
    aarch64_sve_fcmpge,                        // llvm.aarch64.sve.fcmpge
    aarch64_sve_fcmpgt,                        // llvm.aarch64.sve.fcmpgt
    aarch64_sve_fcmpne,                        // llvm.aarch64.sve.fcmpne
    aarch64_sve_fcmpuo,                        // llvm.aarch64.sve.fcmpuo
    aarch64_sve_fcvt,                          // llvm.aarch64.sve.fcvt
    aarch64_sve_fcvt_bf16f32,                  // llvm.aarch64.sve.fcvt.bf16f32
    aarch64_sve_fcvt_f16f32,                   // llvm.aarch64.sve.fcvt.f16f32
    aarch64_sve_fcvt_f16f64,                   // llvm.aarch64.sve.fcvt.f16f64
    aarch64_sve_fcvt_f32f16,                   // llvm.aarch64.sve.fcvt.f32f16
    aarch64_sve_fcvt_f32f64,                   // llvm.aarch64.sve.fcvt.f32f64
    aarch64_sve_fcvt_f64f16,                   // llvm.aarch64.sve.fcvt.f64f16
    aarch64_sve_fcvt_f64f32,                   // llvm.aarch64.sve.fcvt.f64f32
    aarch64_sve_fcvt_x2,                       // llvm.aarch64.sve.fcvt.x2
    aarch64_sve_fcvtlt_f32f16,                 // llvm.aarch64.sve.fcvtlt.f32f16
    aarch64_sve_fcvtlt_f64f32,                 // llvm.aarch64.sve.fcvtlt.f64f32
    aarch64_sve_fcvtn_x2,                      // llvm.aarch64.sve.fcvtn.x2
    aarch64_sve_fcvtnt_bf16f32,                // llvm.aarch64.sve.fcvtnt.bf16f32
    aarch64_sve_fcvtnt_f16f32,                 // llvm.aarch64.sve.fcvtnt.f16f32
    aarch64_sve_fcvtnt_f32f64,                 // llvm.aarch64.sve.fcvtnt.f32f64
    aarch64_sve_fcvts_x2,                      // llvm.aarch64.sve.fcvts.x2
    aarch64_sve_fcvts_x4,                      // llvm.aarch64.sve.fcvts.x4
    aarch64_sve_fcvtu_x2,                      // llvm.aarch64.sve.fcvtu.x2
    aarch64_sve_fcvtu_x4,                      // llvm.aarch64.sve.fcvtu.x4
    aarch64_sve_fcvtx_f32f64,                  // llvm.aarch64.sve.fcvtx.f32f64
    aarch64_sve_fcvtxnt_f32f64,                // llvm.aarch64.sve.fcvtxnt.f32f64
    aarch64_sve_fcvtzs,                        // llvm.aarch64.sve.fcvtzs
    aarch64_sve_fcvtzs_i32f16,                 // llvm.aarch64.sve.fcvtzs.i32f16
    aarch64_sve_fcvtzs_i32f64,                 // llvm.aarch64.sve.fcvtzs.i32f64
    aarch64_sve_fcvtzs_i64f16,                 // llvm.aarch64.sve.fcvtzs.i64f16
    aarch64_sve_fcvtzs_i64f32,                 // llvm.aarch64.sve.fcvtzs.i64f32
    aarch64_sve_fcvtzu,                        // llvm.aarch64.sve.fcvtzu
    aarch64_sve_fcvtzu_i32f16,                 // llvm.aarch64.sve.fcvtzu.i32f16
    aarch64_sve_fcvtzu_i32f64,                 // llvm.aarch64.sve.fcvtzu.i32f64
    aarch64_sve_fcvtzu_i64f16,                 // llvm.aarch64.sve.fcvtzu.i64f16
    aarch64_sve_fcvtzu_i64f32,                 // llvm.aarch64.sve.fcvtzu.i64f32
    aarch64_sve_fdiv,                          // llvm.aarch64.sve.fdiv
    aarch64_sve_fdiv_u,                        // llvm.aarch64.sve.fdiv.u
    aarch64_sve_fdivr,                         // llvm.aarch64.sve.fdivr
    aarch64_sve_fdot_lane_x2,                  // llvm.aarch64.sve.fdot.lane.x2
    aarch64_sve_fdot_x2,                       // llvm.aarch64.sve.fdot.x2
    aarch64_sve_fexpa_x,                       // llvm.aarch64.sve.fexpa.x
    aarch64_sve_flogb,                         // llvm.aarch64.sve.flogb
    aarch64_sve_fmad,                          // llvm.aarch64.sve.fmad
    aarch64_sve_fmax,                          // llvm.aarch64.sve.fmax
    aarch64_sve_fmax_single_x2,                // llvm.aarch64.sve.fmax.single.x2
    aarch64_sve_fmax_single_x4,                // llvm.aarch64.sve.fmax.single.x4
    aarch64_sve_fmax_u,                        // llvm.aarch64.sve.fmax.u
    aarch64_sve_fmax_x2,                       // llvm.aarch64.sve.fmax.x2
    aarch64_sve_fmax_x4,                       // llvm.aarch64.sve.fmax.x4
    aarch64_sve_fmaxnm,                        // llvm.aarch64.sve.fmaxnm
    aarch64_sve_fmaxnm_single_x2,              // llvm.aarch64.sve.fmaxnm.single.x2
    aarch64_sve_fmaxnm_single_x4,              // llvm.aarch64.sve.fmaxnm.single.x4
    aarch64_sve_fmaxnm_u,                      // llvm.aarch64.sve.fmaxnm.u
    aarch64_sve_fmaxnm_x2,                     // llvm.aarch64.sve.fmaxnm.x2
    aarch64_sve_fmaxnm_x4,                     // llvm.aarch64.sve.fmaxnm.x4
    aarch64_sve_fmaxnmp,                       // llvm.aarch64.sve.fmaxnmp
    aarch64_sve_fmaxnmv,                       // llvm.aarch64.sve.fmaxnmv
    aarch64_sve_fmaxp,                         // llvm.aarch64.sve.fmaxp
    aarch64_sve_fmaxv,                         // llvm.aarch64.sve.fmaxv
    aarch64_sve_fmin,                          // llvm.aarch64.sve.fmin
    aarch64_sve_fmin_single_x2,                // llvm.aarch64.sve.fmin.single.x2
    aarch64_sve_fmin_single_x4,                // llvm.aarch64.sve.fmin.single.x4
    aarch64_sve_fmin_u,                        // llvm.aarch64.sve.fmin.u
    aarch64_sve_fmin_x2,                       // llvm.aarch64.sve.fmin.x2
    aarch64_sve_fmin_x4,                       // llvm.aarch64.sve.fmin.x4
    aarch64_sve_fminnm,                        // llvm.aarch64.sve.fminnm
    aarch64_sve_fminnm_single_x2,              // llvm.aarch64.sve.fminnm.single.x2
    aarch64_sve_fminnm_single_x4,              // llvm.aarch64.sve.fminnm.single.x4
    aarch64_sve_fminnm_u,                      // llvm.aarch64.sve.fminnm.u
    aarch64_sve_fminnm_x2,                     // llvm.aarch64.sve.fminnm.x2
    aarch64_sve_fminnm_x4,                     // llvm.aarch64.sve.fminnm.x4
    aarch64_sve_fminnmp,                       // llvm.aarch64.sve.fminnmp
    aarch64_sve_fminnmv,                       // llvm.aarch64.sve.fminnmv
    aarch64_sve_fminp,                         // llvm.aarch64.sve.fminp
    aarch64_sve_fminv,                         // llvm.aarch64.sve.fminv
    aarch64_sve_fmla,                          // llvm.aarch64.sve.fmla
    aarch64_sve_fmla_lane,                     // llvm.aarch64.sve.fmla.lane
    aarch64_sve_fmla_u,                        // llvm.aarch64.sve.fmla.u
    aarch64_sve_fmlalb,                        // llvm.aarch64.sve.fmlalb
    aarch64_sve_fmlalb_lane,                   // llvm.aarch64.sve.fmlalb.lane
    aarch64_sve_fmlalt,                        // llvm.aarch64.sve.fmlalt
    aarch64_sve_fmlalt_lane,                   // llvm.aarch64.sve.fmlalt.lane
    aarch64_sve_fmls,                          // llvm.aarch64.sve.fmls
    aarch64_sve_fmls_lane,                     // llvm.aarch64.sve.fmls.lane
    aarch64_sve_fmls_u,                        // llvm.aarch64.sve.fmls.u
    aarch64_sve_fmlslb,                        // llvm.aarch64.sve.fmlslb
    aarch64_sve_fmlslb_lane,                   // llvm.aarch64.sve.fmlslb.lane
    aarch64_sve_fmlslt,                        // llvm.aarch64.sve.fmlslt
    aarch64_sve_fmlslt_lane,                   // llvm.aarch64.sve.fmlslt.lane
    aarch64_sve_fmmla,                         // llvm.aarch64.sve.fmmla
    aarch64_sve_fmsb,                          // llvm.aarch64.sve.fmsb
    aarch64_sve_fmul,                          // llvm.aarch64.sve.fmul
    aarch64_sve_fmul_lane,                     // llvm.aarch64.sve.fmul.lane
    aarch64_sve_fmul_u,                        // llvm.aarch64.sve.fmul.u
    aarch64_sve_fmulx,                         // llvm.aarch64.sve.fmulx
    aarch64_sve_fmulx_u,                       // llvm.aarch64.sve.fmulx.u
    aarch64_sve_fneg,                          // llvm.aarch64.sve.fneg
    aarch64_sve_fnmad,                         // llvm.aarch64.sve.fnmad
    aarch64_sve_fnmla,                         // llvm.aarch64.sve.fnmla
    aarch64_sve_fnmla_u,                       // llvm.aarch64.sve.fnmla.u
    aarch64_sve_fnmls,                         // llvm.aarch64.sve.fnmls
    aarch64_sve_fnmls_u,                       // llvm.aarch64.sve.fnmls.u
    aarch64_sve_fnmsb,                         // llvm.aarch64.sve.fnmsb
    aarch64_sve_frecpe_x,                      // llvm.aarch64.sve.frecpe.x
    aarch64_sve_frecps_x,                      // llvm.aarch64.sve.frecps.x
    aarch64_sve_frecpx,                        // llvm.aarch64.sve.frecpx
    aarch64_sve_frinta,                        // llvm.aarch64.sve.frinta
    aarch64_sve_frinta_x2,                     // llvm.aarch64.sve.frinta.x2
    aarch64_sve_frinta_x4,                     // llvm.aarch64.sve.frinta.x4
    aarch64_sve_frinti,                        // llvm.aarch64.sve.frinti
    aarch64_sve_frintm,                        // llvm.aarch64.sve.frintm
    aarch64_sve_frintm_x2,                     // llvm.aarch64.sve.frintm.x2
    aarch64_sve_frintm_x4,                     // llvm.aarch64.sve.frintm.x4
    aarch64_sve_frintn,                        // llvm.aarch64.sve.frintn
    aarch64_sve_frintn_x2,                     // llvm.aarch64.sve.frintn.x2
    aarch64_sve_frintn_x4,                     // llvm.aarch64.sve.frintn.x4
    aarch64_sve_frintp,                        // llvm.aarch64.sve.frintp
    aarch64_sve_frintp_x2,                     // llvm.aarch64.sve.frintp.x2
    aarch64_sve_frintp_x4,                     // llvm.aarch64.sve.frintp.x4
    aarch64_sve_frintx,                        // llvm.aarch64.sve.frintx
    aarch64_sve_frintz,                        // llvm.aarch64.sve.frintz
    aarch64_sve_frsqrte_x,                     // llvm.aarch64.sve.frsqrte.x
    aarch64_sve_frsqrts_x,                     // llvm.aarch64.sve.frsqrts.x
    aarch64_sve_fscale,                        // llvm.aarch64.sve.fscale
    aarch64_sve_fsqrt,                         // llvm.aarch64.sve.fsqrt
    aarch64_sve_fsub,                          // llvm.aarch64.sve.fsub
    aarch64_sve_fsub_u,                        // llvm.aarch64.sve.fsub.u
    aarch64_sve_fsubr,                         // llvm.aarch64.sve.fsubr
    aarch64_sve_ftmad_x,                       // llvm.aarch64.sve.ftmad.x
    aarch64_sve_ftsmul_x,                      // llvm.aarch64.sve.ftsmul.x
    aarch64_sve_ftssel_x,                      // llvm.aarch64.sve.ftssel.x
    aarch64_sve_histcnt,                       // llvm.aarch64.sve.histcnt
    aarch64_sve_histseg,                       // llvm.aarch64.sve.histseg
    aarch64_sve_index,                         // llvm.aarch64.sve.index
    aarch64_sve_insr,                          // llvm.aarch64.sve.insr
    aarch64_sve_lasta,                         // llvm.aarch64.sve.lasta
    aarch64_sve_lastb,                         // llvm.aarch64.sve.lastb
    aarch64_sve_ld1,                           // llvm.aarch64.sve.ld1
    aarch64_sve_ld1_gather,                    // llvm.aarch64.sve.ld1.gather
    aarch64_sve_ld1_gather_index,              // llvm.aarch64.sve.ld1.gather.index
    aarch64_sve_ld1_gather_scalar_offset,      // llvm.aarch64.sve.ld1.gather.scalar.offset
    aarch64_sve_ld1_gather_sxtw,               // llvm.aarch64.sve.ld1.gather.sxtw
    aarch64_sve_ld1_gather_sxtw_index,         // llvm.aarch64.sve.ld1.gather.sxtw.index
    aarch64_sve_ld1_gather_uxtw,               // llvm.aarch64.sve.ld1.gather.uxtw
    aarch64_sve_ld1_gather_uxtw_index,         // llvm.aarch64.sve.ld1.gather.uxtw.index
    aarch64_sve_ld1_pn_x2,                     // llvm.aarch64.sve.ld1.pn.x2
    aarch64_sve_ld1_pn_x4,                     // llvm.aarch64.sve.ld1.pn.x4
    aarch64_sve_ld1ro,                         // llvm.aarch64.sve.ld1ro
    aarch64_sve_ld1rq,                         // llvm.aarch64.sve.ld1rq
    aarch64_sve_ld2_sret,                      // llvm.aarch64.sve.ld2.sret
    aarch64_sve_ld3_sret,                      // llvm.aarch64.sve.ld3.sret
    aarch64_sve_ld4_sret,                      // llvm.aarch64.sve.ld4.sret
    aarch64_sve_ldff1,                         // llvm.aarch64.sve.ldff1
    aarch64_sve_ldff1_gather,                  // llvm.aarch64.sve.ldff1.gather
    aarch64_sve_ldff1_gather_index,            // llvm.aarch64.sve.ldff1.gather.index
    aarch64_sve_ldff1_gather_scalar_offset,    // llvm.aarch64.sve.ldff1.gather.scalar.offset
    aarch64_sve_ldff1_gather_sxtw,             // llvm.aarch64.sve.ldff1.gather.sxtw
    aarch64_sve_ldff1_gather_sxtw_index,       // llvm.aarch64.sve.ldff1.gather.sxtw.index
    aarch64_sve_ldff1_gather_uxtw,             // llvm.aarch64.sve.ldff1.gather.uxtw
    aarch64_sve_ldff1_gather_uxtw_index,       // llvm.aarch64.sve.ldff1.gather.uxtw.index
    aarch64_sve_ldnf1,                         // llvm.aarch64.sve.ldnf1
    aarch64_sve_ldnt1,                         // llvm.aarch64.sve.ldnt1
    aarch64_sve_ldnt1_gather,                  // llvm.aarch64.sve.ldnt1.gather
    aarch64_sve_ldnt1_gather_index,            // llvm.aarch64.sve.ldnt1.gather.index
    aarch64_sve_ldnt1_gather_scalar_offset,    // llvm.aarch64.sve.ldnt1.gather.scalar.offset
    aarch64_sve_ldnt1_gather_uxtw,             // llvm.aarch64.sve.ldnt1.gather.uxtw
    aarch64_sve_ldnt1_pn_x2,                   // llvm.aarch64.sve.ldnt1.pn.x2
    aarch64_sve_ldnt1_pn_x4,                   // llvm.aarch64.sve.ldnt1.pn.x4
    aarch64_sve_lsl,                           // llvm.aarch64.sve.lsl
    aarch64_sve_lsl_u,                         // llvm.aarch64.sve.lsl.u
    aarch64_sve_lsl_wide,                      // llvm.aarch64.sve.lsl.wide
    aarch64_sve_lsr,                           // llvm.aarch64.sve.lsr
    aarch64_sve_lsr_u,                         // llvm.aarch64.sve.lsr.u
    aarch64_sve_lsr_wide,                      // llvm.aarch64.sve.lsr.wide
    aarch64_sve_mad,                           // llvm.aarch64.sve.mad
    aarch64_sve_match,                         // llvm.aarch64.sve.match
    aarch64_sve_mla,                           // llvm.aarch64.sve.mla
    aarch64_sve_mla_lane,                      // llvm.aarch64.sve.mla.lane
    aarch64_sve_mla_u,                         // llvm.aarch64.sve.mla.u
    aarch64_sve_mls,                           // llvm.aarch64.sve.mls
    aarch64_sve_mls_lane,                      // llvm.aarch64.sve.mls.lane
    aarch64_sve_mls_u,                         // llvm.aarch64.sve.mls.u
    aarch64_sve_msb,                           // llvm.aarch64.sve.msb
    aarch64_sve_mul,                           // llvm.aarch64.sve.mul
    aarch64_sve_mul_lane,                      // llvm.aarch64.sve.mul.lane
    aarch64_sve_mul_u,                         // llvm.aarch64.sve.mul.u
    aarch64_sve_nand_z,                        // llvm.aarch64.sve.nand.z
    aarch64_sve_nbsl,                          // llvm.aarch64.sve.nbsl
    aarch64_sve_neg,                           // llvm.aarch64.sve.neg
    aarch64_sve_nmatch,                        // llvm.aarch64.sve.nmatch
    aarch64_sve_nor_z,                         // llvm.aarch64.sve.nor.z
    aarch64_sve_not,                           // llvm.aarch64.sve.not
    aarch64_sve_orn_z,                         // llvm.aarch64.sve.orn.z
    aarch64_sve_orr,                           // llvm.aarch64.sve.orr
    aarch64_sve_orr_u,                         // llvm.aarch64.sve.orr.u
    aarch64_sve_orr_z,                         // llvm.aarch64.sve.orr.z
    aarch64_sve_orv,                           // llvm.aarch64.sve.orv
    aarch64_sve_pext,                          // llvm.aarch64.sve.pext
    aarch64_sve_pext_x2,                       // llvm.aarch64.sve.pext.x2
    aarch64_sve_pfirst,                        // llvm.aarch64.sve.pfirst
    aarch64_sve_pmul,                          // llvm.aarch64.sve.pmul
    aarch64_sve_pmullb_pair,                   // llvm.aarch64.sve.pmullb.pair
    aarch64_sve_pmullt_pair,                   // llvm.aarch64.sve.pmullt.pair
    aarch64_sve_pnext,                         // llvm.aarch64.sve.pnext
    aarch64_sve_prf,                           // llvm.aarch64.sve.prf
    aarch64_sve_prfb_gather_index,             // llvm.aarch64.sve.prfb.gather.index
    aarch64_sve_prfb_gather_scalar_offset,     // llvm.aarch64.sve.prfb.gather.scalar.offset
    aarch64_sve_prfb_gather_sxtw_index,        // llvm.aarch64.sve.prfb.gather.sxtw.index
    aarch64_sve_prfb_gather_uxtw_index,        // llvm.aarch64.sve.prfb.gather.uxtw.index
    aarch64_sve_prfd_gather_index,             // llvm.aarch64.sve.prfd.gather.index
    aarch64_sve_prfd_gather_scalar_offset,     // llvm.aarch64.sve.prfd.gather.scalar.offset
    aarch64_sve_prfd_gather_sxtw_index,        // llvm.aarch64.sve.prfd.gather.sxtw.index
    aarch64_sve_prfd_gather_uxtw_index,        // llvm.aarch64.sve.prfd.gather.uxtw.index
    aarch64_sve_prfh_gather_index,             // llvm.aarch64.sve.prfh.gather.index
    aarch64_sve_prfh_gather_scalar_offset,     // llvm.aarch64.sve.prfh.gather.scalar.offset
    aarch64_sve_prfh_gather_sxtw_index,        // llvm.aarch64.sve.prfh.gather.sxtw.index
    aarch64_sve_prfh_gather_uxtw_index,        // llvm.aarch64.sve.prfh.gather.uxtw.index
    aarch64_sve_prfw_gather_index,             // llvm.aarch64.sve.prfw.gather.index
    aarch64_sve_prfw_gather_scalar_offset,     // llvm.aarch64.sve.prfw.gather.scalar.offset
    aarch64_sve_prfw_gather_sxtw_index,        // llvm.aarch64.sve.prfw.gather.sxtw.index
    aarch64_sve_prfw_gather_uxtw_index,        // llvm.aarch64.sve.prfw.gather.uxtw.index
    aarch64_sve_psel,                          // llvm.aarch64.sve.psel
    aarch64_sve_ptest_any,                     // llvm.aarch64.sve.ptest.any
    aarch64_sve_ptest_first,                   // llvm.aarch64.sve.ptest.first
    aarch64_sve_ptest_last,                    // llvm.aarch64.sve.ptest.last
    aarch64_sve_ptrue,                         // llvm.aarch64.sve.ptrue
    aarch64_sve_ptrue_c16,                     // llvm.aarch64.sve.ptrue.c16
    aarch64_sve_ptrue_c32,                     // llvm.aarch64.sve.ptrue.c32
    aarch64_sve_ptrue_c64,                     // llvm.aarch64.sve.ptrue.c64
    aarch64_sve_ptrue_c8,                      // llvm.aarch64.sve.ptrue.c8
    aarch64_sve_punpkhi,                       // llvm.aarch64.sve.punpkhi
    aarch64_sve_punpklo,                       // llvm.aarch64.sve.punpklo
    aarch64_sve_raddhnb,                       // llvm.aarch64.sve.raddhnb
    aarch64_sve_raddhnt,                       // llvm.aarch64.sve.raddhnt
    aarch64_sve_rax1,                          // llvm.aarch64.sve.rax1
    aarch64_sve_rbit,                          // llvm.aarch64.sve.rbit
    aarch64_sve_rdffr,                         // llvm.aarch64.sve.rdffr
    aarch64_sve_rdffr_z,                       // llvm.aarch64.sve.rdffr.z
    aarch64_sve_rev,                           // llvm.aarch64.sve.rev
    aarch64_sve_rev_b16,                       // llvm.aarch64.sve.rev.b16
    aarch64_sve_rev_b32,                       // llvm.aarch64.sve.rev.b32
    aarch64_sve_rev_b64,                       // llvm.aarch64.sve.rev.b64
    aarch64_sve_revb,                          // llvm.aarch64.sve.revb
    aarch64_sve_revd,                          // llvm.aarch64.sve.revd
    aarch64_sve_revh,                          // llvm.aarch64.sve.revh
    aarch64_sve_revw,                          // llvm.aarch64.sve.revw
    aarch64_sve_rshrnb,                        // llvm.aarch64.sve.rshrnb
    aarch64_sve_rshrnt,                        // llvm.aarch64.sve.rshrnt
    aarch64_sve_rsubhnb,                       // llvm.aarch64.sve.rsubhnb
    aarch64_sve_rsubhnt,                       // llvm.aarch64.sve.rsubhnt
    aarch64_sve_saba,                          // llvm.aarch64.sve.saba
    aarch64_sve_sabalb,                        // llvm.aarch64.sve.sabalb
    aarch64_sve_sabalt,                        // llvm.aarch64.sve.sabalt
    aarch64_sve_sabd,                          // llvm.aarch64.sve.sabd
    aarch64_sve_sabd_u,                        // llvm.aarch64.sve.sabd.u
    aarch64_sve_sabdlb,                        // llvm.aarch64.sve.sabdlb
    aarch64_sve_sabdlt,                        // llvm.aarch64.sve.sabdlt
    aarch64_sve_sadalp,                        // llvm.aarch64.sve.sadalp
    aarch64_sve_saddlb,                        // llvm.aarch64.sve.saddlb
    aarch64_sve_saddlbt,                       // llvm.aarch64.sve.saddlbt
    aarch64_sve_saddlt,                        // llvm.aarch64.sve.saddlt
    aarch64_sve_saddv,                         // llvm.aarch64.sve.saddv
    aarch64_sve_saddwb,                        // llvm.aarch64.sve.saddwb
    aarch64_sve_saddwt,                        // llvm.aarch64.sve.saddwt
    aarch64_sve_sbclb,                         // llvm.aarch64.sve.sbclb
    aarch64_sve_sbclt,                         // llvm.aarch64.sve.sbclt
    aarch64_sve_sclamp,                        // llvm.aarch64.sve.sclamp
    aarch64_sve_sclamp_single_x2,              // llvm.aarch64.sve.sclamp.single.x2
    aarch64_sve_sclamp_single_x4,              // llvm.aarch64.sve.sclamp.single.x4
    aarch64_sve_scvtf,                         // llvm.aarch64.sve.scvtf
    aarch64_sve_scvtf_f16i32,                  // llvm.aarch64.sve.scvtf.f16i32
    aarch64_sve_scvtf_f16i64,                  // llvm.aarch64.sve.scvtf.f16i64
    aarch64_sve_scvtf_f32i64,                  // llvm.aarch64.sve.scvtf.f32i64
    aarch64_sve_scvtf_f64i32,                  // llvm.aarch64.sve.scvtf.f64i32
    aarch64_sve_scvtf_x2,                      // llvm.aarch64.sve.scvtf.x2
    aarch64_sve_scvtf_x4,                      // llvm.aarch64.sve.scvtf.x4
    aarch64_sve_sdiv,                          // llvm.aarch64.sve.sdiv
    aarch64_sve_sdiv_u,                        // llvm.aarch64.sve.sdiv.u
    aarch64_sve_sdivr,                         // llvm.aarch64.sve.sdivr
    aarch64_sve_sdot,                          // llvm.aarch64.sve.sdot
    aarch64_sve_sdot_lane,                     // llvm.aarch64.sve.sdot.lane
    aarch64_sve_sdot_lane_x2,                  // llvm.aarch64.sve.sdot.lane.x2
    aarch64_sve_sdot_x2,                       // llvm.aarch64.sve.sdot.x2
    aarch64_sve_sel,                           // llvm.aarch64.sve.sel
    aarch64_sve_sel_x2,                        // llvm.aarch64.sve.sel.x2
    aarch64_sve_sel_x4,                        // llvm.aarch64.sve.sel.x4
    aarch64_sve_setffr,                        // llvm.aarch64.sve.setffr
    aarch64_sve_shadd,                         // llvm.aarch64.sve.shadd
    aarch64_sve_shrnb,                         // llvm.aarch64.sve.shrnb
    aarch64_sve_shrnt,                         // llvm.aarch64.sve.shrnt
    aarch64_sve_shsub,                         // llvm.aarch64.sve.shsub
    aarch64_sve_shsubr,                        // llvm.aarch64.sve.shsubr
    aarch64_sve_sli,                           // llvm.aarch64.sve.sli
    aarch64_sve_sm4e,                          // llvm.aarch64.sve.sm4e
    aarch64_sve_sm4ekey,                       // llvm.aarch64.sve.sm4ekey
    aarch64_sve_smax,                          // llvm.aarch64.sve.smax
    aarch64_sve_smax_single_x2,                // llvm.aarch64.sve.smax.single.x2
    aarch64_sve_smax_single_x4,                // llvm.aarch64.sve.smax.single.x4
    aarch64_sve_smax_u,                        // llvm.aarch64.sve.smax.u
    aarch64_sve_smax_x2,                       // llvm.aarch64.sve.smax.x2
    aarch64_sve_smax_x4,                       // llvm.aarch64.sve.smax.x4
    aarch64_sve_smaxp,                         // llvm.aarch64.sve.smaxp
    aarch64_sve_smaxv,                         // llvm.aarch64.sve.smaxv
    aarch64_sve_smin,                          // llvm.aarch64.sve.smin
    aarch64_sve_smin_single_x2,                // llvm.aarch64.sve.smin.single.x2
    aarch64_sve_smin_single_x4,                // llvm.aarch64.sve.smin.single.x4
    aarch64_sve_smin_u,                        // llvm.aarch64.sve.smin.u
    aarch64_sve_smin_x2,                       // llvm.aarch64.sve.smin.x2
    aarch64_sve_smin_x4,                       // llvm.aarch64.sve.smin.x4
    aarch64_sve_sminp,                         // llvm.aarch64.sve.sminp
    aarch64_sve_sminv,                         // llvm.aarch64.sve.sminv
    aarch64_sve_smlalb,                        // llvm.aarch64.sve.smlalb
    aarch64_sve_smlalb_lane,                   // llvm.aarch64.sve.smlalb.lane
    aarch64_sve_smlalt,                        // llvm.aarch64.sve.smlalt
    aarch64_sve_smlalt_lane,                   // llvm.aarch64.sve.smlalt.lane
    aarch64_sve_smlslb,                        // llvm.aarch64.sve.smlslb
    aarch64_sve_smlslb_lane,                   // llvm.aarch64.sve.smlslb.lane
    aarch64_sve_smlslt,                        // llvm.aarch64.sve.smlslt
    aarch64_sve_smlslt_lane,                   // llvm.aarch64.sve.smlslt.lane
    aarch64_sve_smmla,                         // llvm.aarch64.sve.smmla
    aarch64_sve_smulh,                         // llvm.aarch64.sve.smulh
    aarch64_sve_smulh_u,                       // llvm.aarch64.sve.smulh.u
    aarch64_sve_smullb,                        // llvm.aarch64.sve.smullb
    aarch64_sve_smullb_lane,                   // llvm.aarch64.sve.smullb.lane
    aarch64_sve_smullt,                        // llvm.aarch64.sve.smullt
    aarch64_sve_smullt_lane,                   // llvm.aarch64.sve.smullt.lane
    aarch64_sve_splice,                        // llvm.aarch64.sve.splice
    aarch64_sve_sqabs,                         // llvm.aarch64.sve.sqabs
    aarch64_sve_sqadd,                         // llvm.aarch64.sve.sqadd
    aarch64_sve_sqadd_x,                       // llvm.aarch64.sve.sqadd.x
    aarch64_sve_sqcadd_x,                      // llvm.aarch64.sve.sqcadd.x
    aarch64_sve_sqcvt_x2,                      // llvm.aarch64.sve.sqcvt.x2
    aarch64_sve_sqcvt_x4,                      // llvm.aarch64.sve.sqcvt.x4
    aarch64_sve_sqcvtn_x2,                     // llvm.aarch64.sve.sqcvtn.x2
    aarch64_sve_sqcvtn_x4,                     // llvm.aarch64.sve.sqcvtn.x4
    aarch64_sve_sqcvtu_x2,                     // llvm.aarch64.sve.sqcvtu.x2
    aarch64_sve_sqcvtu_x4,                     // llvm.aarch64.sve.sqcvtu.x4
    aarch64_sve_sqcvtun_x2,                    // llvm.aarch64.sve.sqcvtun.x2
    aarch64_sve_sqcvtun_x4,                    // llvm.aarch64.sve.sqcvtun.x4
    aarch64_sve_sqdecb_n32,                    // llvm.aarch64.sve.sqdecb.n32
    aarch64_sve_sqdecb_n64,                    // llvm.aarch64.sve.sqdecb.n64
    aarch64_sve_sqdecd,                        // llvm.aarch64.sve.sqdecd
    aarch64_sve_sqdecd_n32,                    // llvm.aarch64.sve.sqdecd.n32
    aarch64_sve_sqdecd_n64,                    // llvm.aarch64.sve.sqdecd.n64
    aarch64_sve_sqdech,                        // llvm.aarch64.sve.sqdech
    aarch64_sve_sqdech_n32,                    // llvm.aarch64.sve.sqdech.n32
    aarch64_sve_sqdech_n64,                    // llvm.aarch64.sve.sqdech.n64
    aarch64_sve_sqdecp,                        // llvm.aarch64.sve.sqdecp
    aarch64_sve_sqdecp_n32,                    // llvm.aarch64.sve.sqdecp.n32
    aarch64_sve_sqdecp_n64,                    // llvm.aarch64.sve.sqdecp.n64
    aarch64_sve_sqdecw,                        // llvm.aarch64.sve.sqdecw
    aarch64_sve_sqdecw_n32,                    // llvm.aarch64.sve.sqdecw.n32
    aarch64_sve_sqdecw_n64,                    // llvm.aarch64.sve.sqdecw.n64
    aarch64_sve_sqdmlalb,                      // llvm.aarch64.sve.sqdmlalb
    aarch64_sve_sqdmlalb_lane,                 // llvm.aarch64.sve.sqdmlalb.lane
    aarch64_sve_sqdmlalbt,                     // llvm.aarch64.sve.sqdmlalbt
    aarch64_sve_sqdmlalt,                      // llvm.aarch64.sve.sqdmlalt
    aarch64_sve_sqdmlalt_lane,                 // llvm.aarch64.sve.sqdmlalt.lane
    aarch64_sve_sqdmlslb,                      // llvm.aarch64.sve.sqdmlslb
    aarch64_sve_sqdmlslb_lane,                 // llvm.aarch64.sve.sqdmlslb.lane
    aarch64_sve_sqdmlslbt,                     // llvm.aarch64.sve.sqdmlslbt
    aarch64_sve_sqdmlslt,                      // llvm.aarch64.sve.sqdmlslt
    aarch64_sve_sqdmlslt_lane,                 // llvm.aarch64.sve.sqdmlslt.lane
    aarch64_sve_sqdmulh,                       // llvm.aarch64.sve.sqdmulh
    aarch64_sve_sqdmulh_lane,                  // llvm.aarch64.sve.sqdmulh.lane
    aarch64_sve_sqdmulh_single_vgx2,           // llvm.aarch64.sve.sqdmulh.single.vgx2
    aarch64_sve_sqdmulh_single_vgx4,           // llvm.aarch64.sve.sqdmulh.single.vgx4
    aarch64_sve_sqdmulh_vgx2,                  // llvm.aarch64.sve.sqdmulh.vgx2
    aarch64_sve_sqdmulh_vgx4,                  // llvm.aarch64.sve.sqdmulh.vgx4
    aarch64_sve_sqdmullb,                      // llvm.aarch64.sve.sqdmullb
    aarch64_sve_sqdmullb_lane,                 // llvm.aarch64.sve.sqdmullb.lane
    aarch64_sve_sqdmullt,                      // llvm.aarch64.sve.sqdmullt
    aarch64_sve_sqdmullt_lane,                 // llvm.aarch64.sve.sqdmullt.lane
    aarch64_sve_sqincb_n32,                    // llvm.aarch64.sve.sqincb.n32
    aarch64_sve_sqincb_n64,                    // llvm.aarch64.sve.sqincb.n64
    aarch64_sve_sqincd,                        // llvm.aarch64.sve.sqincd
    aarch64_sve_sqincd_n32,                    // llvm.aarch64.sve.sqincd.n32
    aarch64_sve_sqincd_n64,                    // llvm.aarch64.sve.sqincd.n64
    aarch64_sve_sqinch,                        // llvm.aarch64.sve.sqinch
    aarch64_sve_sqinch_n32,                    // llvm.aarch64.sve.sqinch.n32
    aarch64_sve_sqinch_n64,                    // llvm.aarch64.sve.sqinch.n64
    aarch64_sve_sqincp,                        // llvm.aarch64.sve.sqincp
    aarch64_sve_sqincp_n32,                    // llvm.aarch64.sve.sqincp.n32
    aarch64_sve_sqincp_n64,                    // llvm.aarch64.sve.sqincp.n64
    aarch64_sve_sqincw,                        // llvm.aarch64.sve.sqincw
    aarch64_sve_sqincw_n32,                    // llvm.aarch64.sve.sqincw.n32
    aarch64_sve_sqincw_n64,                    // llvm.aarch64.sve.sqincw.n64
    aarch64_sve_sqneg,                         // llvm.aarch64.sve.sqneg
    aarch64_sve_sqrdcmlah_lane_x,              // llvm.aarch64.sve.sqrdcmlah.lane.x
    aarch64_sve_sqrdcmlah_x,                   // llvm.aarch64.sve.sqrdcmlah.x
    aarch64_sve_sqrdmlah,                      // llvm.aarch64.sve.sqrdmlah
    aarch64_sve_sqrdmlah_lane,                 // llvm.aarch64.sve.sqrdmlah.lane
    aarch64_sve_sqrdmlsh,                      // llvm.aarch64.sve.sqrdmlsh
    aarch64_sve_sqrdmlsh_lane,                 // llvm.aarch64.sve.sqrdmlsh.lane
    aarch64_sve_sqrdmulh,                      // llvm.aarch64.sve.sqrdmulh
    aarch64_sve_sqrdmulh_lane,                 // llvm.aarch64.sve.sqrdmulh.lane
    aarch64_sve_sqrshl,                        // llvm.aarch64.sve.sqrshl
    aarch64_sve_sqrshr_x2,                     // llvm.aarch64.sve.sqrshr.x2
    aarch64_sve_sqrshr_x4,                     // llvm.aarch64.sve.sqrshr.x4
    aarch64_sve_sqrshrn_x2,                    // llvm.aarch64.sve.sqrshrn.x2
    aarch64_sve_sqrshrn_x4,                    // llvm.aarch64.sve.sqrshrn.x4
    aarch64_sve_sqrshrnb,                      // llvm.aarch64.sve.sqrshrnb
    aarch64_sve_sqrshrnt,                      // llvm.aarch64.sve.sqrshrnt
    aarch64_sve_sqrshru_x2,                    // llvm.aarch64.sve.sqrshru.x2
    aarch64_sve_sqrshru_x4,                    // llvm.aarch64.sve.sqrshru.x4
    aarch64_sve_sqrshrun_x2,                   // llvm.aarch64.sve.sqrshrun.x2
    aarch64_sve_sqrshrun_x4,                   // llvm.aarch64.sve.sqrshrun.x4
    aarch64_sve_sqrshrunb,                     // llvm.aarch64.sve.sqrshrunb
    aarch64_sve_sqrshrunt,                     // llvm.aarch64.sve.sqrshrunt
    aarch64_sve_sqshl,                         // llvm.aarch64.sve.sqshl
    aarch64_sve_sqshlu,                        // llvm.aarch64.sve.sqshlu
    aarch64_sve_sqshrnb,                       // llvm.aarch64.sve.sqshrnb
    aarch64_sve_sqshrnt,                       // llvm.aarch64.sve.sqshrnt
    aarch64_sve_sqshrunb,                      // llvm.aarch64.sve.sqshrunb
    aarch64_sve_sqshrunt,                      // llvm.aarch64.sve.sqshrunt
    aarch64_sve_sqsub,                         // llvm.aarch64.sve.sqsub
    aarch64_sve_sqsub_u,                       // llvm.aarch64.sve.sqsub.u
    aarch64_sve_sqsub_x,                       // llvm.aarch64.sve.sqsub.x
    aarch64_sve_sqsubr,                        // llvm.aarch64.sve.sqsubr
    aarch64_sve_sqxtnb,                        // llvm.aarch64.sve.sqxtnb
    aarch64_sve_sqxtnt,                        // llvm.aarch64.sve.sqxtnt
    aarch64_sve_sqxtunb,                       // llvm.aarch64.sve.sqxtunb
    aarch64_sve_sqxtunt,                       // llvm.aarch64.sve.sqxtunt
    aarch64_sve_srhadd,                        // llvm.aarch64.sve.srhadd
    aarch64_sve_sri,                           // llvm.aarch64.sve.sri
    aarch64_sve_srshl,                         // llvm.aarch64.sve.srshl
    aarch64_sve_srshl_single_x2,               // llvm.aarch64.sve.srshl.single.x2
    aarch64_sve_srshl_single_x4,               // llvm.aarch64.sve.srshl.single.x4
    aarch64_sve_srshl_x2,                      // llvm.aarch64.sve.srshl.x2
    aarch64_sve_srshl_x4,                      // llvm.aarch64.sve.srshl.x4
    aarch64_sve_srshr,                         // llvm.aarch64.sve.srshr
    aarch64_sve_srsra,                         // llvm.aarch64.sve.srsra
    aarch64_sve_sshllb,                        // llvm.aarch64.sve.sshllb
    aarch64_sve_sshllt,                        // llvm.aarch64.sve.sshllt
    aarch64_sve_ssra,                          // llvm.aarch64.sve.ssra
    aarch64_sve_ssublb,                        // llvm.aarch64.sve.ssublb
    aarch64_sve_ssublbt,                       // llvm.aarch64.sve.ssublbt
    aarch64_sve_ssublt,                        // llvm.aarch64.sve.ssublt
    aarch64_sve_ssubltb,                       // llvm.aarch64.sve.ssubltb
    aarch64_sve_ssubwb,                        // llvm.aarch64.sve.ssubwb
    aarch64_sve_ssubwt,                        // llvm.aarch64.sve.ssubwt
    aarch64_sve_st1,                           // llvm.aarch64.sve.st1
    aarch64_sve_st1_pn_x2,                     // llvm.aarch64.sve.st1.pn.x2
    aarch64_sve_st1_pn_x4,                     // llvm.aarch64.sve.st1.pn.x4
    aarch64_sve_st1_scatter,                   // llvm.aarch64.sve.st1.scatter
    aarch64_sve_st1_scatter_index,             // llvm.aarch64.sve.st1.scatter.index
    aarch64_sve_st1_scatter_scalar_offset,     // llvm.aarch64.sve.st1.scatter.scalar.offset
    aarch64_sve_st1_scatter_sxtw,              // llvm.aarch64.sve.st1.scatter.sxtw
    aarch64_sve_st1_scatter_sxtw_index,        // llvm.aarch64.sve.st1.scatter.sxtw.index
    aarch64_sve_st1_scatter_uxtw,              // llvm.aarch64.sve.st1.scatter.uxtw
    aarch64_sve_st1_scatter_uxtw_index,        // llvm.aarch64.sve.st1.scatter.uxtw.index
    aarch64_sve_st2,                           // llvm.aarch64.sve.st2
    aarch64_sve_st3,                           // llvm.aarch64.sve.st3
    aarch64_sve_st4,                           // llvm.aarch64.sve.st4
    aarch64_sve_stnt1,                         // llvm.aarch64.sve.stnt1
    aarch64_sve_stnt1_pn_x2,                   // llvm.aarch64.sve.stnt1.pn.x2
    aarch64_sve_stnt1_pn_x4,                   // llvm.aarch64.sve.stnt1.pn.x4
    aarch64_sve_stnt1_scatter,                 // llvm.aarch64.sve.stnt1.scatter
    aarch64_sve_stnt1_scatter_index,           // llvm.aarch64.sve.stnt1.scatter.index
    aarch64_sve_stnt1_scatter_scalar_offset,   // llvm.aarch64.sve.stnt1.scatter.scalar.offset
    aarch64_sve_stnt1_scatter_uxtw,            // llvm.aarch64.sve.stnt1.scatter.uxtw
    aarch64_sve_sub,                           // llvm.aarch64.sve.sub
    aarch64_sve_sub_u,                         // llvm.aarch64.sve.sub.u
    aarch64_sve_subhnb,                        // llvm.aarch64.sve.subhnb
    aarch64_sve_subhnt,                        // llvm.aarch64.sve.subhnt
    aarch64_sve_subr,                          // llvm.aarch64.sve.subr
    aarch64_sve_sudot_lane,                    // llvm.aarch64.sve.sudot.lane
    aarch64_sve_sunpk_x2,                      // llvm.aarch64.sve.sunpk.x2
    aarch64_sve_sunpk_x4,                      // llvm.aarch64.sve.sunpk.x4
    aarch64_sve_sunpkhi,                       // llvm.aarch64.sve.sunpkhi
    aarch64_sve_sunpklo,                       // llvm.aarch64.sve.sunpklo
    aarch64_sve_suqadd,                        // llvm.aarch64.sve.suqadd
    aarch64_sve_sxtb,                          // llvm.aarch64.sve.sxtb
    aarch64_sve_sxth,                          // llvm.aarch64.sve.sxth
    aarch64_sve_sxtw,                          // llvm.aarch64.sve.sxtw
    aarch64_sve_tbl,                           // llvm.aarch64.sve.tbl
    aarch64_sve_tbl2,                          // llvm.aarch64.sve.tbl2
    aarch64_sve_tbx,                           // llvm.aarch64.sve.tbx
    aarch64_sve_trn1,                          // llvm.aarch64.sve.trn1
    aarch64_sve_trn1_b16,                      // llvm.aarch64.sve.trn1.b16
    aarch64_sve_trn1_b32,                      // llvm.aarch64.sve.trn1.b32
    aarch64_sve_trn1_b64,                      // llvm.aarch64.sve.trn1.b64
    aarch64_sve_trn1q,                         // llvm.aarch64.sve.trn1q
    aarch64_sve_trn2,                          // llvm.aarch64.sve.trn2
    aarch64_sve_trn2_b16,                      // llvm.aarch64.sve.trn2.b16
    aarch64_sve_trn2_b32,                      // llvm.aarch64.sve.trn2.b32
    aarch64_sve_trn2_b64,                      // llvm.aarch64.sve.trn2.b64
    aarch64_sve_trn2q,                         // llvm.aarch64.sve.trn2q
    aarch64_sve_uaba,                          // llvm.aarch64.sve.uaba
    aarch64_sve_uabalb,                        // llvm.aarch64.sve.uabalb
    aarch64_sve_uabalt,                        // llvm.aarch64.sve.uabalt
    aarch64_sve_uabd,                          // llvm.aarch64.sve.uabd
    aarch64_sve_uabd_u,                        // llvm.aarch64.sve.uabd.u
    aarch64_sve_uabdlb,                        // llvm.aarch64.sve.uabdlb
    aarch64_sve_uabdlt,                        // llvm.aarch64.sve.uabdlt
    aarch64_sve_uadalp,                        // llvm.aarch64.sve.uadalp
    aarch64_sve_uaddlb,                        // llvm.aarch64.sve.uaddlb
    aarch64_sve_uaddlt,                        // llvm.aarch64.sve.uaddlt
    aarch64_sve_uaddv,                         // llvm.aarch64.sve.uaddv
    aarch64_sve_uaddwb,                        // llvm.aarch64.sve.uaddwb
    aarch64_sve_uaddwt,                        // llvm.aarch64.sve.uaddwt
    aarch64_sve_uclamp,                        // llvm.aarch64.sve.uclamp
    aarch64_sve_uclamp_single_x2,              // llvm.aarch64.sve.uclamp.single.x2
    aarch64_sve_uclamp_single_x4,              // llvm.aarch64.sve.uclamp.single.x4
    aarch64_sve_ucvtf,                         // llvm.aarch64.sve.ucvtf
    aarch64_sve_ucvtf_f16i32,                  // llvm.aarch64.sve.ucvtf.f16i32
    aarch64_sve_ucvtf_f16i64,                  // llvm.aarch64.sve.ucvtf.f16i64
    aarch64_sve_ucvtf_f32i64,                  // llvm.aarch64.sve.ucvtf.f32i64
    aarch64_sve_ucvtf_f64i32,                  // llvm.aarch64.sve.ucvtf.f64i32
    aarch64_sve_ucvtf_x2,                      // llvm.aarch64.sve.ucvtf.x2
    aarch64_sve_ucvtf_x4,                      // llvm.aarch64.sve.ucvtf.x4
    aarch64_sve_udiv,                          // llvm.aarch64.sve.udiv
    aarch64_sve_udiv_u,                        // llvm.aarch64.sve.udiv.u
    aarch64_sve_udivr,                         // llvm.aarch64.sve.udivr
    aarch64_sve_udot,                          // llvm.aarch64.sve.udot
    aarch64_sve_udot_lane,                     // llvm.aarch64.sve.udot.lane
    aarch64_sve_udot_lane_x2,                  // llvm.aarch64.sve.udot.lane.x2
    aarch64_sve_udot_x2,                       // llvm.aarch64.sve.udot.x2
    aarch64_sve_uhadd,                         // llvm.aarch64.sve.uhadd
    aarch64_sve_uhsub,                         // llvm.aarch64.sve.uhsub
    aarch64_sve_uhsubr,                        // llvm.aarch64.sve.uhsubr
    aarch64_sve_umax,                          // llvm.aarch64.sve.umax
    aarch64_sve_umax_single_x2,                // llvm.aarch64.sve.umax.single.x2
    aarch64_sve_umax_single_x4,                // llvm.aarch64.sve.umax.single.x4
    aarch64_sve_umax_u,                        // llvm.aarch64.sve.umax.u
    aarch64_sve_umax_x2,                       // llvm.aarch64.sve.umax.x2
    aarch64_sve_umax_x4,                       // llvm.aarch64.sve.umax.x4
    aarch64_sve_umaxp,                         // llvm.aarch64.sve.umaxp
    aarch64_sve_umaxv,                         // llvm.aarch64.sve.umaxv
    aarch64_sve_umin,                          // llvm.aarch64.sve.umin
    aarch64_sve_umin_single_x2,                // llvm.aarch64.sve.umin.single.x2
    aarch64_sve_umin_single_x4,                // llvm.aarch64.sve.umin.single.x4
    aarch64_sve_umin_u,                        // llvm.aarch64.sve.umin.u
    aarch64_sve_umin_x2,                       // llvm.aarch64.sve.umin.x2
    aarch64_sve_umin_x4,                       // llvm.aarch64.sve.umin.x4
    aarch64_sve_uminp,                         // llvm.aarch64.sve.uminp
    aarch64_sve_uminv,                         // llvm.aarch64.sve.uminv
    aarch64_sve_umlalb,                        // llvm.aarch64.sve.umlalb
    aarch64_sve_umlalb_lane,                   // llvm.aarch64.sve.umlalb.lane
    aarch64_sve_umlalt,                        // llvm.aarch64.sve.umlalt
    aarch64_sve_umlalt_lane,                   // llvm.aarch64.sve.umlalt.lane
    aarch64_sve_umlslb,                        // llvm.aarch64.sve.umlslb
    aarch64_sve_umlslb_lane,                   // llvm.aarch64.sve.umlslb.lane
    aarch64_sve_umlslt,                        // llvm.aarch64.sve.umlslt
    aarch64_sve_umlslt_lane,                   // llvm.aarch64.sve.umlslt.lane
    aarch64_sve_ummla,                         // llvm.aarch64.sve.ummla
    aarch64_sve_umulh,                         // llvm.aarch64.sve.umulh
    aarch64_sve_umulh_u,                       // llvm.aarch64.sve.umulh.u
    aarch64_sve_umullb,                        // llvm.aarch64.sve.umullb
    aarch64_sve_umullb_lane,                   // llvm.aarch64.sve.umullb.lane
    aarch64_sve_umullt,                        // llvm.aarch64.sve.umullt
    aarch64_sve_umullt_lane,                   // llvm.aarch64.sve.umullt.lane
    aarch64_sve_uqadd,                         // llvm.aarch64.sve.uqadd
    aarch64_sve_uqadd_x,                       // llvm.aarch64.sve.uqadd.x
    aarch64_sve_uqcvt_x2,                      // llvm.aarch64.sve.uqcvt.x2
    aarch64_sve_uqcvt_x4,                      // llvm.aarch64.sve.uqcvt.x4
    aarch64_sve_uqcvtn_x2,                     // llvm.aarch64.sve.uqcvtn.x2
    aarch64_sve_uqcvtn_x4,                     // llvm.aarch64.sve.uqcvtn.x4
    aarch64_sve_uqdecb_n32,                    // llvm.aarch64.sve.uqdecb.n32
    aarch64_sve_uqdecb_n64,                    // llvm.aarch64.sve.uqdecb.n64
    aarch64_sve_uqdecd,                        // llvm.aarch64.sve.uqdecd
    aarch64_sve_uqdecd_n32,                    // llvm.aarch64.sve.uqdecd.n32
    aarch64_sve_uqdecd_n64,                    // llvm.aarch64.sve.uqdecd.n64
    aarch64_sve_uqdech,                        // llvm.aarch64.sve.uqdech
    aarch64_sve_uqdech_n32,                    // llvm.aarch64.sve.uqdech.n32
    aarch64_sve_uqdech_n64,                    // llvm.aarch64.sve.uqdech.n64
    aarch64_sve_uqdecp,                        // llvm.aarch64.sve.uqdecp
    aarch64_sve_uqdecp_n32,                    // llvm.aarch64.sve.uqdecp.n32
    aarch64_sve_uqdecp_n64,                    // llvm.aarch64.sve.uqdecp.n64
    aarch64_sve_uqdecw,                        // llvm.aarch64.sve.uqdecw
    aarch64_sve_uqdecw_n32,                    // llvm.aarch64.sve.uqdecw.n32
    aarch64_sve_uqdecw_n64,                    // llvm.aarch64.sve.uqdecw.n64
    aarch64_sve_uqincb_n32,                    // llvm.aarch64.sve.uqincb.n32
    aarch64_sve_uqincb_n64,                    // llvm.aarch64.sve.uqincb.n64
    aarch64_sve_uqincd,                        // llvm.aarch64.sve.uqincd
    aarch64_sve_uqincd_n32,                    // llvm.aarch64.sve.uqincd.n32
    aarch64_sve_uqincd_n64,                    // llvm.aarch64.sve.uqincd.n64
    aarch64_sve_uqinch,                        // llvm.aarch64.sve.uqinch
    aarch64_sve_uqinch_n32,                    // llvm.aarch64.sve.uqinch.n32
    aarch64_sve_uqinch_n64,                    // llvm.aarch64.sve.uqinch.n64
    aarch64_sve_uqincp,                        // llvm.aarch64.sve.uqincp
    aarch64_sve_uqincp_n32,                    // llvm.aarch64.sve.uqincp.n32
    aarch64_sve_uqincp_n64,                    // llvm.aarch64.sve.uqincp.n64
    aarch64_sve_uqincw,                        // llvm.aarch64.sve.uqincw
    aarch64_sve_uqincw_n32,                    // llvm.aarch64.sve.uqincw.n32
    aarch64_sve_uqincw_n64,                    // llvm.aarch64.sve.uqincw.n64
    aarch64_sve_uqrshl,                        // llvm.aarch64.sve.uqrshl
    aarch64_sve_uqrshr_x2,                     // llvm.aarch64.sve.uqrshr.x2
    aarch64_sve_uqrshr_x4,                     // llvm.aarch64.sve.uqrshr.x4
    aarch64_sve_uqrshrn_x2,                    // llvm.aarch64.sve.uqrshrn.x2
    aarch64_sve_uqrshrn_x4,                    // llvm.aarch64.sve.uqrshrn.x4
    aarch64_sve_uqrshrnb,                      // llvm.aarch64.sve.uqrshrnb
    aarch64_sve_uqrshrnt,                      // llvm.aarch64.sve.uqrshrnt
    aarch64_sve_uqshl,                         // llvm.aarch64.sve.uqshl
    aarch64_sve_uqshrnb,                       // llvm.aarch64.sve.uqshrnb
    aarch64_sve_uqshrnt,                       // llvm.aarch64.sve.uqshrnt
    aarch64_sve_uqsub,                         // llvm.aarch64.sve.uqsub
    aarch64_sve_uqsub_u,                       // llvm.aarch64.sve.uqsub.u
    aarch64_sve_uqsub_x,                       // llvm.aarch64.sve.uqsub.x
    aarch64_sve_uqsubr,                        // llvm.aarch64.sve.uqsubr
    aarch64_sve_uqxtnb,                        // llvm.aarch64.sve.uqxtnb
    aarch64_sve_uqxtnt,                        // llvm.aarch64.sve.uqxtnt
    aarch64_sve_urecpe,                        // llvm.aarch64.sve.urecpe
    aarch64_sve_urhadd,                        // llvm.aarch64.sve.urhadd
    aarch64_sve_urshl,                         // llvm.aarch64.sve.urshl
    aarch64_sve_urshl_single_x2,               // llvm.aarch64.sve.urshl.single.x2
    aarch64_sve_urshl_single_x4,               // llvm.aarch64.sve.urshl.single.x4
    aarch64_sve_urshl_x2,                      // llvm.aarch64.sve.urshl.x2
    aarch64_sve_urshl_x4,                      // llvm.aarch64.sve.urshl.x4
    aarch64_sve_urshr,                         // llvm.aarch64.sve.urshr
    aarch64_sve_ursqrte,                       // llvm.aarch64.sve.ursqrte
    aarch64_sve_ursra,                         // llvm.aarch64.sve.ursra
    aarch64_sve_usdot,                         // llvm.aarch64.sve.usdot
    aarch64_sve_usdot_lane,                    // llvm.aarch64.sve.usdot.lane
    aarch64_sve_ushllb,                        // llvm.aarch64.sve.ushllb
    aarch64_sve_ushllt,                        // llvm.aarch64.sve.ushllt
    aarch64_sve_usmmla,                        // llvm.aarch64.sve.usmmla
    aarch64_sve_usqadd,                        // llvm.aarch64.sve.usqadd
    aarch64_sve_usra,                          // llvm.aarch64.sve.usra
    aarch64_sve_usublb,                        // llvm.aarch64.sve.usublb
    aarch64_sve_usublt,                        // llvm.aarch64.sve.usublt
    aarch64_sve_usubwb,                        // llvm.aarch64.sve.usubwb
    aarch64_sve_usubwt,                        // llvm.aarch64.sve.usubwt
    aarch64_sve_uunpk_x2,                      // llvm.aarch64.sve.uunpk.x2
    aarch64_sve_uunpk_x4,                      // llvm.aarch64.sve.uunpk.x4
    aarch64_sve_uunpkhi,                       // llvm.aarch64.sve.uunpkhi
    aarch64_sve_uunpklo,                       // llvm.aarch64.sve.uunpklo
    aarch64_sve_uxtb,                          // llvm.aarch64.sve.uxtb
    aarch64_sve_uxth,                          // llvm.aarch64.sve.uxth
    aarch64_sve_uxtw,                          // llvm.aarch64.sve.uxtw
    aarch64_sve_uzp_x2,                        // llvm.aarch64.sve.uzp.x2
    aarch64_sve_uzp_x4,                        // llvm.aarch64.sve.uzp.x4
    aarch64_sve_uzp1,                          // llvm.aarch64.sve.uzp1
    aarch64_sve_uzp1_b16,                      // llvm.aarch64.sve.uzp1.b16
    aarch64_sve_uzp1_b32,                      // llvm.aarch64.sve.uzp1.b32
    aarch64_sve_uzp1_b64,                      // llvm.aarch64.sve.uzp1.b64
    aarch64_sve_uzp1q,                         // llvm.aarch64.sve.uzp1q
    aarch64_sve_uzp2,                          // llvm.aarch64.sve.uzp2
    aarch64_sve_uzp2_b16,                      // llvm.aarch64.sve.uzp2.b16
    aarch64_sve_uzp2_b32,                      // llvm.aarch64.sve.uzp2.b32
    aarch64_sve_uzp2_b64,                      // llvm.aarch64.sve.uzp2.b64
    aarch64_sve_uzp2q,                         // llvm.aarch64.sve.uzp2q
    aarch64_sve_uzpq_x2,                       // llvm.aarch64.sve.uzpq.x2
    aarch64_sve_uzpq_x4,                       // llvm.aarch64.sve.uzpq.x4
    aarch64_sve_whilege,                       // llvm.aarch64.sve.whilege
    aarch64_sve_whilege_c16,                   // llvm.aarch64.sve.whilege.c16
    aarch64_sve_whilege_c32,                   // llvm.aarch64.sve.whilege.c32
    aarch64_sve_whilege_c64,                   // llvm.aarch64.sve.whilege.c64
    aarch64_sve_whilege_c8,                    // llvm.aarch64.sve.whilege.c8
    aarch64_sve_whilege_x2,                    // llvm.aarch64.sve.whilege.x2
    aarch64_sve_whilegt,                       // llvm.aarch64.sve.whilegt
    aarch64_sve_whilegt_c16,                   // llvm.aarch64.sve.whilegt.c16
    aarch64_sve_whilegt_c32,                   // llvm.aarch64.sve.whilegt.c32
    aarch64_sve_whilegt_c64,                   // llvm.aarch64.sve.whilegt.c64
    aarch64_sve_whilegt_c8,                    // llvm.aarch64.sve.whilegt.c8
    aarch64_sve_whilegt_x2,                    // llvm.aarch64.sve.whilegt.x2
    aarch64_sve_whilehi,                       // llvm.aarch64.sve.whilehi
    aarch64_sve_whilehi_c16,                   // llvm.aarch64.sve.whilehi.c16
    aarch64_sve_whilehi_c32,                   // llvm.aarch64.sve.whilehi.c32
    aarch64_sve_whilehi_c64,                   // llvm.aarch64.sve.whilehi.c64
    aarch64_sve_whilehi_c8,                    // llvm.aarch64.sve.whilehi.c8
    aarch64_sve_whilehi_x2,                    // llvm.aarch64.sve.whilehi.x2
    aarch64_sve_whilehs,                       // llvm.aarch64.sve.whilehs
    aarch64_sve_whilehs_c16,                   // llvm.aarch64.sve.whilehs.c16
    aarch64_sve_whilehs_c32,                   // llvm.aarch64.sve.whilehs.c32
    aarch64_sve_whilehs_c64,                   // llvm.aarch64.sve.whilehs.c64
    aarch64_sve_whilehs_c8,                    // llvm.aarch64.sve.whilehs.c8
    aarch64_sve_whilehs_x2,                    // llvm.aarch64.sve.whilehs.x2
    aarch64_sve_whilele,                       // llvm.aarch64.sve.whilele
    aarch64_sve_whilele_c16,                   // llvm.aarch64.sve.whilele.c16
    aarch64_sve_whilele_c32,                   // llvm.aarch64.sve.whilele.c32
    aarch64_sve_whilele_c64,                   // llvm.aarch64.sve.whilele.c64
    aarch64_sve_whilele_c8,                    // llvm.aarch64.sve.whilele.c8
    aarch64_sve_whilele_x2,                    // llvm.aarch64.sve.whilele.x2
    aarch64_sve_whilelo,                       // llvm.aarch64.sve.whilelo
    aarch64_sve_whilelo_c16,                   // llvm.aarch64.sve.whilelo.c16
    aarch64_sve_whilelo_c32,                   // llvm.aarch64.sve.whilelo.c32
    aarch64_sve_whilelo_c64,                   // llvm.aarch64.sve.whilelo.c64
    aarch64_sve_whilelo_c8,                    // llvm.aarch64.sve.whilelo.c8
    aarch64_sve_whilelo_x2,                    // llvm.aarch64.sve.whilelo.x2
    aarch64_sve_whilels,                       // llvm.aarch64.sve.whilels
    aarch64_sve_whilels_c16,                   // llvm.aarch64.sve.whilels.c16
    aarch64_sve_whilels_c32,                   // llvm.aarch64.sve.whilels.c32
    aarch64_sve_whilels_c64,                   // llvm.aarch64.sve.whilels.c64
    aarch64_sve_whilels_c8,                    // llvm.aarch64.sve.whilels.c8
    aarch64_sve_whilels_x2,                    // llvm.aarch64.sve.whilels.x2
    aarch64_sve_whilelt,                       // llvm.aarch64.sve.whilelt
    aarch64_sve_whilelt_c16,                   // llvm.aarch64.sve.whilelt.c16
    aarch64_sve_whilelt_c32,                   // llvm.aarch64.sve.whilelt.c32
    aarch64_sve_whilelt_c64,                   // llvm.aarch64.sve.whilelt.c64
    aarch64_sve_whilelt_c8,                    // llvm.aarch64.sve.whilelt.c8
    aarch64_sve_whilelt_x2,                    // llvm.aarch64.sve.whilelt.x2
    aarch64_sve_whilerw_b,                     // llvm.aarch64.sve.whilerw.b
    aarch64_sve_whilerw_d,                     // llvm.aarch64.sve.whilerw.d
    aarch64_sve_whilerw_h,                     // llvm.aarch64.sve.whilerw.h
    aarch64_sve_whilerw_s,                     // llvm.aarch64.sve.whilerw.s
    aarch64_sve_whilewr_b,                     // llvm.aarch64.sve.whilewr.b
    aarch64_sve_whilewr_d,                     // llvm.aarch64.sve.whilewr.d
    aarch64_sve_whilewr_h,                     // llvm.aarch64.sve.whilewr.h
    aarch64_sve_whilewr_s,                     // llvm.aarch64.sve.whilewr.s
    aarch64_sve_wrffr,                         // llvm.aarch64.sve.wrffr
    aarch64_sve_xar,                           // llvm.aarch64.sve.xar
    aarch64_sve_zip_x2,                        // llvm.aarch64.sve.zip.x2
    aarch64_sve_zip_x4,                        // llvm.aarch64.sve.zip.x4
    aarch64_sve_zip1,                          // llvm.aarch64.sve.zip1
    aarch64_sve_zip1_b16,                      // llvm.aarch64.sve.zip1.b16
    aarch64_sve_zip1_b32,                      // llvm.aarch64.sve.zip1.b32
    aarch64_sve_zip1_b64,                      // llvm.aarch64.sve.zip1.b64
    aarch64_sve_zip1q,                         // llvm.aarch64.sve.zip1q
    aarch64_sve_zip2,                          // llvm.aarch64.sve.zip2
    aarch64_sve_zip2_b16,                      // llvm.aarch64.sve.zip2.b16
    aarch64_sve_zip2_b32,                      // llvm.aarch64.sve.zip2.b32
    aarch64_sve_zip2_b64,                      // llvm.aarch64.sve.zip2.b64
    aarch64_sve_zip2q,                         // llvm.aarch64.sve.zip2q
    aarch64_sve_zipq_x2,                       // llvm.aarch64.sve.zipq.x2
    aarch64_sve_zipq_x4,                       // llvm.aarch64.sve.zipq.x4
    aarch64_tagp,                              // llvm.aarch64.tagp
    aarch64_tcancel,                           // llvm.aarch64.tcancel
    aarch64_tcommit,                           // llvm.aarch64.tcommit
    aarch64_tstart,                            // llvm.aarch64.tstart
    aarch64_ttest,                             // llvm.aarch64.ttest
    aarch64_udiv,                              // llvm.aarch64.udiv
}; // enum
} // namespace Intrinsic
} // namespace llvm

#endif
