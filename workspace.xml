<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="CMakePresetLoader">{
  &quot;useNewFormat&quot;: true
}</component>
  <component name="CMakeProjectFlavorService">
    <option name="flavorId" value="CMakePlainProjectFlavor" />
  </component>
  <component name="CMakeReloadState">
    <option name="reloaded" value="true" />
  </component>
  <component name="CMakeRunConfigurationManager">
    <generated>
      <config projectName="Blender" targetName="bf_editor_geometry" />
      <config projectName="Blender" targetName="extern_curve_fit_nd" />
      <config projectName="Blender" targetName="bf_intern_ghost" />
      <config projectName="Blender" targetName="bf_nodes" />
      <config projectName="Blender" targetName="blender" />
      <config projectName="Blender" targetName="bf_editor_space_topbar" />
      <config projectName="Blender" targetName="blender_cpu_check" />
      <config projectName="Blender" targetName="extern_quadriflow" />
      <config projectName="Blender" targetName="bf_blenfont" />
      <config projectName="Blender" targetName="bf_blenloader_core" />
      <config projectName="Blender" targetName="bf_intern_decklink" />
      <config projectName="Blender" targetName="bf_intern_libmv" />
      <config projectName="Blender" targetName="bf_io_grease_pencil" />
      <config projectName="Blender" targetName="bf_editor_space_console" />
      <config projectName="Blender" targetName="cycles_integrator" />
      <config projectName="Blender" targetName="ge_logic_bricks" />
      <config projectName="Blender" targetName="bf_intern_spindle" />
      <config projectName="Blender" targetName="bf_editor_screen" />
      <config projectName="Blender" targetName="bf_editor_id_management" />
      <config projectName="Blender" targetName="bf_nodes_texture" />
      <config projectName="Blender" targetName="bf_intern_mantaflow" />
      <config projectName="Blender" targetName="extern_wcwidth" />
      <config projectName="Blender" targetName="bf_intern_memutil" />
      <config projectName="Blender" targetName="ge_converter" />
      <config projectName="Blender" targetName="ge_msg_network" />
      <config projectName="Blender" targetName="cycles_bvh" />
      <config projectName="Blender" targetName="bf_editor_space_info" />
      <config projectName="Blender" targetName="bf_intern_uriconvert" />
      <config projectName="Blender" targetName="bf_shader_fx" />
      <config projectName="Blender" targetName="cycles_kernel" />
      <config projectName="Blender" targetName="bf_editor_space_outliner" />
      <config projectName="Blender" targetName="bf_editor_object" />
      <config projectName="Blender" targetName="bf_intern_utfconv" />
      <config projectName="Blender" targetName="ge_common" />
      <config projectName="Blender" targetName="extern_draco" />
      <config projectName="Blender" targetName="bf_intern_slim" />
      <config projectName="Blender" targetName="makesrna" />
      <config projectName="Blender" targetName="bf_editor_scene" />
      <config projectName="Blender" targetName="bf_imbuf_movie" />
      <config projectName="Blender" targetName="extern_vulkan_memory_allocator" />
      <config projectName="Blender" targetName="bf_editor_metaball" />
      <config projectName="Blender" targetName="bf_editor_interface" />
      <config projectName="Blender" targetName="bf_gpu_shaders" />
      <config projectName="Blender" targetName="bf_io_ply" />
      <config projectName="Blender" targetName="bf_intern_rigidbody" />
      <config projectName="Blender" targetName="bf_render" />
      <config projectName="Blender" targetName="extern_bullet" />
      <config projectName="Blender" targetName="cycles_device" />
      <config projectName="Blender" targetName="bf_sequencer" />
      <config projectName="Blender" targetName="ge_physics_dummy" />
      <config projectName="Blender" targetName="bf_io_csv" />
      <config projectName="Blender" targetName="bf_editor_undo" />
      <config projectName="Blender" targetName="bf_rna" />
      <config projectName="Blender" targetName="BlendThumb" />
      <config projectName="Blender" targetName="bf_intern_openvdb" />
      <config projectName="Blender" targetName="audaspace" />
      <config projectName="Blender" targetName="ge_videotexture" />
      <config projectName="Blender" targetName="bf_simulation" />
      <config projectName="Blender" targetName="bf_editor_grease_pencil" />
      <config projectName="Blender" targetName="bf_editor_space_userpref" />
      <config projectName="Blender" targetName="bf_imbuf_cineon" />
      <config projectName="Blender" targetName="ge_rasterizer_opengl" />
      <config projectName="Blender" targetName="bf_editor_gizmo_library" />
      <config projectName="Blender" targetName="extern_rangetree" />
      <config projectName="Blender" targetName="bf_editor_space_text" />
      <config projectName="Blender" targetName="bf_intern_opensubdiv" />
      <config projectName="Blender" targetName="bf_io_fbx" />
      <config projectName="Blender" targetName="extern_gflags" />
      <config projectName="Blender" targetName="bf_blenlib" />
      <config projectName="Blender" targetName="bf_editor_mask" />
      <config projectName="Blender" targetName="bf_editor_space_view3d" />
      <config projectName="Blender" targetName="blenderplayer" />
      <config projectName="Blender" targetName="msgfmt" />
      <config projectName="Blender" targetName="bf_editor_space_api" />
      <config projectName="Blender" targetName="bf_freestyle" />
      <config projectName="Blender" targetName="bf_io_wavefront_obj" />
      <config projectName="Blender" targetName="bf_intern_sky" />
      <config projectName="Blender" targetName="bf_compositor" />
      <config projectName="Blender" targetName="bf_dna_blenlib" />
      <config projectName="Blender" targetName="bf_imbuf_openexr" />
      <config projectName="Blender" targetName="bf_io_alembic" />
      <config projectName="Blender" targetName="bf_editor_space_action" />
      <config projectName="Blender" targetName="bf_nodes_geometry_generated" />
      <config projectName="Blender" targetName="bf_draw" />
      <config projectName="Blender" targetName="extern_minilzo" />
      <config projectName="Blender" targetName="bf_editor_sound" />
      <config projectName="Blender" targetName="bf_compositor_shaders" />
      <config projectName="Blender" targetName="bf_draw_shaders" />
      <config projectName="Blender" targetName="bf_editor_sculpt_paint" />
      <config projectName="Blender" targetName="extern_lzma" />
      <config projectName="Blender" targetName="bf_editor_space_clip" />
      <config projectName="Blender" targetName="bf_editor_space_sequencer" />
      <config projectName="Blender" targetName="buildinfo_player" />
      <config projectName="Blender" targetName="cycles_session" />
      <config projectName="Blender" targetName="bf_render_hydra" />
      <config projectName="Blender" targetName="bf_io_usd" />
      <config projectName="Blender" targetName="bf_imbuf_opencolorio_shaders" />
      <config projectName="Blender" targetName="ge_device" />
      <config projectName="Blender" targetName="ge_expressions" />
      <config projectName="Blender" targetName="ge_ketsji" />
      <config projectName="Blender" targetName="bf_intern_clog" />
      <config projectName="Blender" targetName="bf_nodes_composite" />
      <config projectName="Blender" targetName="bf_windowmanager" />
      <config projectName="Blender" targetName="bf_python_mathutils" />
      <config projectName="Blender" targetName="draco" />
      <config projectName="Blender" targetName="bf_editor_space_nla" />
      <config projectName="Blender" targetName="bf_editor_space_file" />
      <config projectName="Blender" targetName="bf_imbuf_opencolorio" />
      <config projectName="Blender" targetName="cycles_subd" />
      <config projectName="Blender" targetName="bf_editor_render" />
      <config projectName="Blender" targetName="bf_animrig" />
      <config projectName="Blender" targetName="bf_intern_quadriflow" />
      <config projectName="Blender" targetName="glsl_preprocess" />
      <config projectName="Blender" targetName="bf_editor_pointcloud" />
      <config projectName="Blender" targetName="bf_geometry" />
      <config projectName="Blender" targetName="bf_intern_audaspace" />
      <config projectName="Blender" targetName="cycles_util" />
      <config projectName="Blender" targetName="extern_glog" />
      <config projectName="Blender" targetName="bf_osd_shaders" />
      <config projectName="Blender" targetName="extern_xxhash" />
      <config projectName="Blender" targetName="extern_fmtlib" />
      <config projectName="Blender" targetName="bf_intern_itasc" />
      <config projectName="Blender" targetName="audaspace-py" />
      <config projectName="Blender" targetName="bf_python" />
      <config projectName="Blender" targetName="bf_ikplugin" />
      <config projectName="Blender" targetName="ge_launcher" />
      <config projectName="Blender" targetName="bf_functions" />
      <config projectName="Blender" targetName="bf_editor_transform" />
      <config projectName="Blender" targetName="buildinfoobj" />
      <config projectName="Blender" targetName="datatoc" />
      <config projectName="Blender" targetName="extern_recastnavigation" />
      <config projectName="Blender" targetName="bf_editor_space_graph" />
      <config projectName="Blender" targetName="ge_player" />
      <config projectName="Blender" targetName="bf_editor_space_image" />
      <config projectName="Blender" targetName="bf_editor_animation" />
      <config projectName="Blender" targetName="bf_intern_iksolver" />
      <config projectName="Blender" targetName="buildinfo" />
      <config projectName="Blender" targetName="bf_dna" />
      <config projectName="Blender" targetName="extern_nanosvg" />
      <config projectName="Blender" targetName="bf_python_ext" />
      <config projectName="Blender" targetName="ge_scenegraph" />
      <config projectName="Blender" targetName="bf_io_common" />
      <config projectName="Blender" targetName="bf_python_bmesh" />
      <config projectName="Blender" targetName="bf_editor_space_node" />
      <config projectName="Blender" targetName="bf_python_gpu" />
      <config projectName="Blender" targetName="cycles_scene" />
      <config projectName="Blender" targetName="ge_physics_bullet" />
      <config projectName="Blender" targetName="blender-launcher" />
      <config projectName="Blender" targetName="bf_editor_physics" />
      <config projectName="Blender" targetName="bf_editor_space_logic" />
      <config projectName="Blender" targetName="extern_ceres" />
      <config projectName="Blender" targetName="locales" />
      <config projectName="Blender" targetName="bf_intern_guardedalloc" />
      <config projectName="Blender" targetName="bf_editor_space_script" />
      <config projectName="Blender" targetName="bf_intern_atomic" />
      <config projectName="Blender" targetName="bf_intern_gpudirect" />
      <config projectName="Blender" targetName="bf_gpu" />
      <config projectName="Blender" targetName="bf_io_stl" />
      <config projectName="Blender" targetName="zstd_compress" />
      <config projectName="Blender" targetName="cycles_osl_shaders" />
      <config projectName="Blender" targetName="bf_editor_uvedit" />
      <config projectName="Blender" targetName="bf_editor_gpencil_legacy" />
      <config projectName="Blender" targetName="bf_editor_space_spreadsheet" />
      <config projectName="Blender" targetName="bf_editor_curves" />
      <config projectName="Blender" targetName="bf_editor_util" />
      <config projectName="Blender" targetName="buildinfoobj_player" />
      <config projectName="Blender" targetName="bf_blenloader" />
      <config projectName="Blender" targetName="bf_imbuf_openimageio" />
      <config projectName="Blender" targetName="bf_editor_armature" />
      <config projectName="Blender" targetName="bf_modifiers" />
      <config projectName="Blender" targetName="bf_editor_mesh" />
      <config projectName="Blender" targetName="bf_nodes_geometry" />
      <config projectName="Blender" targetName="bf_blenkernel" />
      <config projectName="Blender" targetName="bf_asset_system" />
      <config projectName="Blender" targetName="bf_editor_lattice" />
      <config projectName="Blender" targetName="bf_intern_eigen" />
      <config projectName="Blender" targetName="bf_blentranslation" />
      <config projectName="Blender" targetName="bf_nodes_compositor_generated" />
      <config projectName="Blender" targetName="bf_editor_io" />
      <config projectName="Blender" targetName="extern_hipew" />
      <config projectName="Blender" targetName="cycles_kernel_osl" />
      <config projectName="Blender" targetName="bf_nodes_shader" />
      <config projectName="Blender" targetName="bf_bmesh" />
      <config projectName="Blender" targetName="ge_blender_routines" />
      <config projectName="Blender" targetName="bf_depsgraph" />
      <config projectName="Blender" targetName="extern_mantaflow" />
      <config projectName="Blender" targetName="ge_rasterizer" />
      <config projectName="Blender" targetName="bf_editor_space_statusbar" />
      <config projectName="Blender" targetName="bf_nodes_functions_generated" />
      <config projectName="Blender" targetName="bf_editor_curve" />
      <config projectName="Blender" targetName="bf_nodes_function" />
      <config projectName="Blender" targetName="cycles_graph" />
      <config projectName="Blender" targetName="bf_editor_space_buttons" />
      <config projectName="Blender" targetName="makesdna" />
      <config projectName="Blender" targetName="bf_editor_datafiles" />
      <config projectName="Blender" targetName="extern_cuew" />
      <config projectName="Blender" targetName="bf_intern_cycles" />
      <config projectName="Blender" targetName="bf_intern_dualcon" />
      <config projectName="Blender" targetName="bf_intern_mikktspace" />
      <config projectName="Blender" targetName="bf_editor_asset" />
      <config projectName="Blender" targetName="bf_intern_moto" />
      <config projectName="Blender" targetName="extern_ufbx" />
      <config projectName="Blender" targetName="bf_imbuf" />
    </generated>
  </component>
  <component name="CMakeSettings">
    <configurations>
      <configuration PROFILE_NAME="Release" ENABLED="true" CONFIG_NAME="Release" />
    </configurations>
  </component>
  <component name="ChangeListManager">
    <list default="true" id="6fccab9c-f71e-4d2b-9da8-f66295ba7a4c" name="更改" comment="">
      <change beforePath="$PROJECT_DIR$/lib/windows_x64/.gitattributes" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/lib/windows_x64/.gitignore" beforeDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ExecutionTargetManager" SELECTED_TARGET="CMakeBuildProfile:Release" />
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/lib/windows_x64" />
  </component>
  <component name="ProjectApplicationVersion">
    <option name="ide" value="CLion" />
    <option name="majorVersion" value="2025" />
    <option name="minorVersion" value="1.1" />
    <option name="productBranch" value="Classic" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 7
}</component>
  <component name="ProjectId" id="2yIR3kjwHn0W21MoP08GEzcwgxa" />
  <component name="ProjectViewState">
    <option name="autoscrollFromSource" value="true" />
    <option name="autoscrollToSource" value="true" />
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "CMake 应用程序.blender.executor": "Debug",
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.cidr.known.project.marker": "true",
    "RunOnceActivity.git.unshallow": "true",
    "RunOnceActivity.readMode.enableVisualFormatting": "true",
    "RunOnceActivity.west.config.association.type.startup.service": "true",
    "cf.advertisement.text.overridden": "true",
    "cf.first.check.clang-format": "false",
    "cidr.known.project.marker": "true",
    "ctest.advertisement.all.test.configuration.is.created": "true",
    "git-widget-placeholder": "main",
    "ignore.virus.scanning.warn.message": "true",
    "last_opened_file_path": "I:/upbge-5/upbge-20250610",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RunManager" selected="CMake 应用程序.blender">
    <configuration name="BlendThumb" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="BlendThumb" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="audaspace-py" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="audaspace-py" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="audaspace" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="audaspace" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_animrig" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_animrig" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_asset_system" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_asset_system" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_blenfont" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_blenfont" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_blenkernel" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_blenkernel" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_blenlib" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_blenlib" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_blenloader" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_blenloader" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_blenloader_core" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_blenloader_core" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_blentranslation" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_blentranslation" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_bmesh" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_bmesh" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_compositor" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_compositor" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_compositor_shaders" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_compositor_shaders" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_depsgraph" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_depsgraph" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_dna" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_dna" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_dna_blenlib" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_dna_blenlib" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_draw" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_draw" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_draw_shaders" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_draw_shaders" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_editor_animation" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_editor_animation" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_editor_armature" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_editor_armature" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_editor_asset" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_editor_asset" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_editor_curve" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_editor_curve" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_editor_curves" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_editor_curves" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_editor_datafiles" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_editor_datafiles" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_editor_geometry" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_editor_geometry" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_editor_gizmo_library" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_editor_gizmo_library" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_editor_gpencil_legacy" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_editor_gpencil_legacy" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_editor_grease_pencil" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_editor_grease_pencil" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_editor_id_management" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_editor_id_management" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_editor_interface" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_editor_interface" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_editor_io" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_editor_io" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_editor_lattice" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_editor_lattice" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_editor_mask" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_editor_mask" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_editor_mesh" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_editor_mesh" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_editor_metaball" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_editor_metaball" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_editor_object" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_editor_object" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_editor_physics" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_editor_physics" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_editor_pointcloud" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_editor_pointcloud" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_editor_render" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_editor_render" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_editor_scene" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_editor_scene" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_editor_screen" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_editor_screen" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_editor_sculpt_paint" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_editor_sculpt_paint" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_editor_sound" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_editor_sound" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_editor_space_action" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_editor_space_action" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_editor_space_api" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_editor_space_api" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_editor_space_buttons" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_editor_space_buttons" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_editor_space_clip" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_editor_space_clip" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_editor_space_console" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_editor_space_console" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_editor_space_file" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_editor_space_file" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_editor_space_graph" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_editor_space_graph" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_editor_space_image" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_editor_space_image" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_editor_space_info" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_editor_space_info" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_editor_space_logic" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_editor_space_logic" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_editor_space_nla" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_editor_space_nla" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_editor_space_node" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_editor_space_node" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_editor_space_outliner" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_editor_space_outliner" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_editor_space_script" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_editor_space_script" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_editor_space_sequencer" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_editor_space_sequencer" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_editor_space_spreadsheet" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_editor_space_spreadsheet" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_editor_space_statusbar" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_editor_space_statusbar" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_editor_space_text" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_editor_space_text" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_editor_space_topbar" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_editor_space_topbar" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_editor_space_userpref" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_editor_space_userpref" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_editor_space_view3d" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_editor_space_view3d" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_editor_transform" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_editor_transform" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_editor_undo" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_editor_undo" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_editor_util" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_editor_util" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_editor_uvedit" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_editor_uvedit" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_freestyle" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_freestyle" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_functions" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_functions" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_geometry" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_geometry" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_gpu" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_gpu" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_gpu_shaders" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_gpu_shaders" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_ikplugin" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_ikplugin" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_imbuf" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_imbuf" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_imbuf_cineon" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_imbuf_cineon" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_imbuf_movie" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_imbuf_movie" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_imbuf_opencolorio" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_imbuf_opencolorio" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_imbuf_opencolorio_shaders" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_imbuf_opencolorio_shaders" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_imbuf_openexr" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_imbuf_openexr" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_imbuf_openimageio" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_imbuf_openimageio" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_intern_atomic" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_intern_atomic" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_intern_audaspace" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_intern_audaspace" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_intern_clog" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_intern_clog" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_intern_cycles" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_intern_cycles" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_intern_decklink" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_intern_decklink" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_intern_dualcon" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_intern_dualcon" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_intern_eigen" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_intern_eigen" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_intern_ghost" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_intern_ghost" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_intern_gpudirect" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_intern_gpudirect" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_intern_guardedalloc" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_intern_guardedalloc" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_intern_iksolver" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_intern_iksolver" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_intern_itasc" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_intern_itasc" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_intern_libmv" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_intern_libmv" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_intern_mantaflow" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_intern_mantaflow" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_intern_memutil" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_intern_memutil" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_intern_mikktspace" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_intern_mikktspace" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_intern_moto" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_intern_moto" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_intern_opensubdiv" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_intern_opensubdiv" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_intern_openvdb" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_intern_openvdb" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_intern_quadriflow" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_intern_quadriflow" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_intern_rigidbody" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_intern_rigidbody" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_intern_sky" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_intern_sky" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_intern_slim" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_intern_slim" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_intern_spindle" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_intern_spindle" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_intern_uriconvert" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_intern_uriconvert" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_intern_utfconv" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_intern_utfconv" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_io_alembic" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_io_alembic" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_io_common" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_io_common" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_io_csv" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_io_csv" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_io_fbx" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_io_fbx" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_io_grease_pencil" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_io_grease_pencil" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_io_ply" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_io_ply" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_io_stl" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_io_stl" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_io_usd" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_io_usd" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_io_wavefront_obj" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_io_wavefront_obj" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_modifiers" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_modifiers" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_nodes" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_nodes" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_nodes_composite" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_nodes_composite" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_nodes_compositor_generated" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_nodes_compositor_generated" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_nodes_function" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_nodes_function" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_nodes_functions_generated" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_nodes_functions_generated" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_nodes_geometry" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_nodes_geometry" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_nodes_geometry_generated" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_nodes_geometry_generated" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_nodes_shader" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_nodes_shader" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_nodes_texture" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_nodes_texture" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_osd_shaders" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_osd_shaders" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_python" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_python" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_python_bmesh" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_python_bmesh" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_python_ext" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_python_ext" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_python_gpu" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_python_gpu" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_python_mathutils" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_python_mathutils" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_render" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_render" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_render_hydra" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_render_hydra" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_rna" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_rna" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_sequencer" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_sequencer" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_shader_fx" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_shader_fx" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_simulation" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_simulation" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="bf_windowmanager" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="bf_windowmanager" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="blender-launcher" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="blender-launcher" CONFIG_NAME="Release" RUN_TARGET_PROJECT_NAME="Blender" RUN_TARGET_NAME="blender-launcher">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="blender" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="blender" CONFIG_NAME="Release" RUN_TARGET_PROJECT_NAME="Blender" RUN_TARGET_NAME="blender">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="blender_cpu_check" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="blender_cpu_check" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="blenderplayer" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="blenderplayer" CONFIG_NAME="Release" RUN_TARGET_PROJECT_NAME="Blender" RUN_TARGET_NAME="blenderplayer">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="buildinfo" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="buildinfo" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="buildinfo_player" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="buildinfo_player" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="buildinfoobj" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="buildinfoobj" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="buildinfoobj_player" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="buildinfoobj_player" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="cycles_bvh" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="cycles_bvh" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="cycles_device" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="cycles_device" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="cycles_graph" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="cycles_graph" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="cycles_integrator" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="cycles_integrator" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="cycles_kernel" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="cycles_kernel" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="cycles_kernel_osl" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="cycles_kernel_osl" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="cycles_osl_shaders" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="cycles_osl_shaders" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="cycles_scene" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="cycles_scene" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="cycles_session" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="cycles_session" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="cycles_subd" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="cycles_subd" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="cycles_util" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="cycles_util" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="datatoc" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="datatoc" CONFIG_NAME="Release" RUN_TARGET_PROJECT_NAME="Blender" RUN_TARGET_NAME="datatoc">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="draco" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="draco" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="extern_bullet" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="extern_bullet" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="extern_ceres" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="extern_ceres" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="extern_cuew" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="extern_cuew" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="extern_curve_fit_nd" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="extern_curve_fit_nd" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="extern_draco" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="extern_draco" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="extern_fmtlib" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="extern_fmtlib" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="extern_gflags" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="extern_gflags" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="extern_glog" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="extern_glog" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="extern_hipew" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="extern_hipew" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="extern_lzma" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="extern_lzma" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="extern_mantaflow" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="extern_mantaflow" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="extern_minilzo" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="extern_minilzo" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="extern_nanosvg" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="extern_nanosvg" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="extern_quadriflow" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="extern_quadriflow" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="extern_rangetree" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="extern_rangetree" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="extern_recastnavigation" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="extern_recastnavigation" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="extern_ufbx" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="extern_ufbx" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="extern_vulkan_memory_allocator" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="extern_vulkan_memory_allocator" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="extern_wcwidth" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="extern_wcwidth" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="extern_xxhash" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="extern_xxhash" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="ge_blender_routines" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="ge_blender_routines" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="ge_common" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="ge_common" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="ge_converter" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="ge_converter" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="ge_device" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="ge_device" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="ge_expressions" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="ge_expressions" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="ge_ketsji" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="ge_ketsji" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="ge_launcher" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="ge_launcher" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="ge_logic_bricks" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="ge_logic_bricks" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="ge_msg_network" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="ge_msg_network" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="ge_physics_bullet" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="ge_physics_bullet" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="ge_physics_dummy" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="ge_physics_dummy" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="ge_player" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="ge_player" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="ge_rasterizer" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="ge_rasterizer" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="ge_rasterizer_opengl" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="ge_rasterizer_opengl" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="ge_scenegraph" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="ge_scenegraph" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="ge_videotexture" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="ge_videotexture" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="glsl_preprocess" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="glsl_preprocess" CONFIG_NAME="Release" RUN_TARGET_PROJECT_NAME="Blender" RUN_TARGET_NAME="glsl_preprocess">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="locales" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="locales" CONFIG_NAME="Release">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="makesdna" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="makesdna" CONFIG_NAME="Release" RUN_TARGET_PROJECT_NAME="Blender" RUN_TARGET_NAME="makesdna">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="makesrna" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="makesrna" CONFIG_NAME="Release" RUN_TARGET_PROJECT_NAME="Blender" RUN_TARGET_NAME="makesrna">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="msgfmt" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="msgfmt" CONFIG_NAME="Release" RUN_TARGET_PROJECT_NAME="Blender" RUN_TARGET_NAME="msgfmt">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="zstd_compress" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Blender" TARGET_NAME="zstd_compress" CONFIG_NAME="Release" RUN_TARGET_PROJECT_NAME="Blender" RUN_TARGET_NAME="zstd_compress">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="所有 CTest" type="CTestRunConfiguration" factoryName="CTestRun" PROGRAM_PARAMS="--extra-verbose" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" WORKING_DIR="file://$CMakeCurrentLocalGenerationDir$" PASS_PARENT_ENVS_2="true" RUN_PATH="$CTestCurrentExecutableName$" EXPLICIT_BUILD_TARGET_NAME="all" TEST_MODE="SUITE_TEST">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
        <option name="BeforeTestRunTask" enabled="true" />
      </method>
    </configuration>
    <list>
      <item itemvalue="CMake 应用程序.BlendThumb" />
      <item itemvalue="CMake 应用程序.audaspace-py" />
      <item itemvalue="CMake 应用程序.audaspace" />
      <item itemvalue="CMake 应用程序.bf_animrig" />
      <item itemvalue="CMake 应用程序.bf_asset_system" />
      <item itemvalue="CMake 应用程序.bf_blenfont" />
      <item itemvalue="CMake 应用程序.bf_blenkernel" />
      <item itemvalue="CMake 应用程序.bf_blenlib" />
      <item itemvalue="CMake 应用程序.bf_blenloader" />
      <item itemvalue="CMake 应用程序.bf_blenloader_core" />
      <item itemvalue="CMake 应用程序.bf_blentranslation" />
      <item itemvalue="CMake 应用程序.bf_bmesh" />
      <item itemvalue="CMake 应用程序.bf_compositor" />
      <item itemvalue="CMake 应用程序.bf_compositor_shaders" />
      <item itemvalue="CMake 应用程序.bf_depsgraph" />
      <item itemvalue="CMake 应用程序.bf_dna" />
      <item itemvalue="CMake 应用程序.bf_dna_blenlib" />
      <item itemvalue="CMake 应用程序.bf_draw" />
      <item itemvalue="CMake 应用程序.bf_draw_shaders" />
      <item itemvalue="CMake 应用程序.bf_editor_animation" />
      <item itemvalue="CMake 应用程序.bf_editor_armature" />
      <item itemvalue="CMake 应用程序.bf_editor_asset" />
      <item itemvalue="CMake 应用程序.bf_editor_curve" />
      <item itemvalue="CMake 应用程序.bf_editor_curves" />
      <item itemvalue="CMake 应用程序.bf_editor_datafiles" />
      <item itemvalue="CMake 应用程序.bf_editor_geometry" />
      <item itemvalue="CMake 应用程序.bf_editor_gizmo_library" />
      <item itemvalue="CMake 应用程序.bf_editor_gpencil_legacy" />
      <item itemvalue="CMake 应用程序.bf_editor_grease_pencil" />
      <item itemvalue="CMake 应用程序.bf_editor_id_management" />
      <item itemvalue="CMake 应用程序.bf_editor_interface" />
      <item itemvalue="CMake 应用程序.bf_editor_io" />
      <item itemvalue="CMake 应用程序.bf_editor_lattice" />
      <item itemvalue="CMake 应用程序.bf_editor_mask" />
      <item itemvalue="CMake 应用程序.bf_editor_mesh" />
      <item itemvalue="CMake 应用程序.bf_editor_metaball" />
      <item itemvalue="CMake 应用程序.bf_editor_object" />
      <item itemvalue="CMake 应用程序.bf_editor_physics" />
      <item itemvalue="CMake 应用程序.bf_editor_pointcloud" />
      <item itemvalue="CMake 应用程序.bf_editor_render" />
      <item itemvalue="CMake 应用程序.bf_editor_scene" />
      <item itemvalue="CMake 应用程序.bf_editor_screen" />
      <item itemvalue="CMake 应用程序.bf_editor_sculpt_paint" />
      <item itemvalue="CMake 应用程序.bf_editor_sound" />
      <item itemvalue="CMake 应用程序.bf_editor_space_action" />
      <item itemvalue="CMake 应用程序.bf_editor_space_api" />
      <item itemvalue="CMake 应用程序.bf_editor_space_buttons" />
      <item itemvalue="CMake 应用程序.bf_editor_space_clip" />
      <item itemvalue="CMake 应用程序.bf_editor_space_console" />
      <item itemvalue="CMake 应用程序.bf_editor_space_file" />
      <item itemvalue="CMake 应用程序.bf_editor_space_graph" />
      <item itemvalue="CMake 应用程序.bf_editor_space_image" />
      <item itemvalue="CMake 应用程序.bf_editor_space_info" />
      <item itemvalue="CMake 应用程序.bf_editor_space_logic" />
      <item itemvalue="CMake 应用程序.bf_editor_space_nla" />
      <item itemvalue="CMake 应用程序.bf_editor_space_node" />
      <item itemvalue="CMake 应用程序.bf_editor_space_outliner" />
      <item itemvalue="CMake 应用程序.bf_editor_space_script" />
      <item itemvalue="CMake 应用程序.bf_editor_space_sequencer" />
      <item itemvalue="CMake 应用程序.bf_editor_space_spreadsheet" />
      <item itemvalue="CMake 应用程序.bf_editor_space_statusbar" />
      <item itemvalue="CMake 应用程序.bf_editor_space_text" />
      <item itemvalue="CMake 应用程序.bf_editor_space_topbar" />
      <item itemvalue="CMake 应用程序.bf_editor_space_userpref" />
      <item itemvalue="CMake 应用程序.bf_editor_space_view3d" />
      <item itemvalue="CMake 应用程序.bf_editor_transform" />
      <item itemvalue="CMake 应用程序.bf_editor_undo" />
      <item itemvalue="CMake 应用程序.bf_editor_util" />
      <item itemvalue="CMake 应用程序.bf_editor_uvedit" />
      <item itemvalue="CMake 应用程序.bf_freestyle" />
      <item itemvalue="CMake 应用程序.bf_functions" />
      <item itemvalue="CMake 应用程序.bf_geometry" />
      <item itemvalue="CMake 应用程序.bf_gpu" />
      <item itemvalue="CMake 应用程序.bf_gpu_shaders" />
      <item itemvalue="CMake 应用程序.bf_ikplugin" />
      <item itemvalue="CMake 应用程序.bf_imbuf" />
      <item itemvalue="CMake 应用程序.bf_imbuf_cineon" />
      <item itemvalue="CMake 应用程序.bf_imbuf_movie" />
      <item itemvalue="CMake 应用程序.bf_imbuf_opencolorio" />
      <item itemvalue="CMake 应用程序.bf_imbuf_opencolorio_shaders" />
      <item itemvalue="CMake 应用程序.bf_imbuf_openexr" />
      <item itemvalue="CMake 应用程序.bf_imbuf_openimageio" />
      <item itemvalue="CMake 应用程序.bf_intern_atomic" />
      <item itemvalue="CMake 应用程序.bf_intern_audaspace" />
      <item itemvalue="CMake 应用程序.bf_intern_clog" />
      <item itemvalue="CMake 应用程序.bf_intern_cycles" />
      <item itemvalue="CMake 应用程序.bf_intern_decklink" />
      <item itemvalue="CMake 应用程序.bf_intern_dualcon" />
      <item itemvalue="CMake 应用程序.bf_intern_eigen" />
      <item itemvalue="CMake 应用程序.bf_intern_ghost" />
      <item itemvalue="CMake 应用程序.bf_intern_gpudirect" />
      <item itemvalue="CMake 应用程序.bf_intern_guardedalloc" />
      <item itemvalue="CMake 应用程序.bf_intern_iksolver" />
      <item itemvalue="CMake 应用程序.bf_intern_itasc" />
      <item itemvalue="CMake 应用程序.bf_intern_libmv" />
      <item itemvalue="CMake 应用程序.bf_intern_mantaflow" />
      <item itemvalue="CMake 应用程序.bf_intern_memutil" />
      <item itemvalue="CMake 应用程序.bf_intern_mikktspace" />
      <item itemvalue="CMake 应用程序.bf_intern_moto" />
      <item itemvalue="CMake 应用程序.bf_intern_opensubdiv" />
      <item itemvalue="CMake 应用程序.bf_intern_openvdb" />
      <item itemvalue="CMake 应用程序.bf_intern_quadriflow" />
      <item itemvalue="CMake 应用程序.bf_intern_rigidbody" />
      <item itemvalue="CMake 应用程序.bf_intern_sky" />
      <item itemvalue="CMake 应用程序.bf_intern_slim" />
      <item itemvalue="CMake 应用程序.bf_intern_spindle" />
      <item itemvalue="CMake 应用程序.bf_intern_uriconvert" />
      <item itemvalue="CMake 应用程序.bf_intern_utfconv" />
      <item itemvalue="CMake 应用程序.bf_io_alembic" />
      <item itemvalue="CMake 应用程序.bf_io_common" />
      <item itemvalue="CMake 应用程序.bf_io_csv" />
      <item itemvalue="CMake 应用程序.bf_io_fbx" />
      <item itemvalue="CMake 应用程序.bf_io_grease_pencil" />
      <item itemvalue="CMake 应用程序.bf_io_ply" />
      <item itemvalue="CMake 应用程序.bf_io_stl" />
      <item itemvalue="CMake 应用程序.bf_io_usd" />
      <item itemvalue="CMake 应用程序.bf_io_wavefront_obj" />
      <item itemvalue="CMake 应用程序.bf_modifiers" />
      <item itemvalue="CMake 应用程序.bf_nodes" />
      <item itemvalue="CMake 应用程序.bf_nodes_composite" />
      <item itemvalue="CMake 应用程序.bf_nodes_compositor_generated" />
      <item itemvalue="CMake 应用程序.bf_nodes_function" />
      <item itemvalue="CMake 应用程序.bf_nodes_functions_generated" />
      <item itemvalue="CMake 应用程序.bf_nodes_geometry" />
      <item itemvalue="CMake 应用程序.bf_nodes_geometry_generated" />
      <item itemvalue="CMake 应用程序.bf_nodes_shader" />
      <item itemvalue="CMake 应用程序.bf_nodes_texture" />
      <item itemvalue="CMake 应用程序.bf_osd_shaders" />
      <item itemvalue="CMake 应用程序.bf_python" />
      <item itemvalue="CMake 应用程序.bf_python_bmesh" />
      <item itemvalue="CMake 应用程序.bf_python_ext" />
      <item itemvalue="CMake 应用程序.bf_python_gpu" />
      <item itemvalue="CMake 应用程序.bf_python_mathutils" />
      <item itemvalue="CMake 应用程序.bf_render" />
      <item itemvalue="CMake 应用程序.bf_render_hydra" />
      <item itemvalue="CMake 应用程序.bf_rna" />
      <item itemvalue="CMake 应用程序.bf_sequencer" />
      <item itemvalue="CMake 应用程序.bf_shader_fx" />
      <item itemvalue="CMake 应用程序.bf_simulation" />
      <item itemvalue="CMake 应用程序.bf_windowmanager" />
      <item itemvalue="CMake 应用程序.blender-launcher" />
      <item itemvalue="CMake 应用程序.blender" />
      <item itemvalue="CMake 应用程序.blender_cpu_check" />
      <item itemvalue="CMake 应用程序.blenderplayer" />
      <item itemvalue="CMake 应用程序.buildinfo" />
      <item itemvalue="CMake 应用程序.buildinfo_player" />
      <item itemvalue="CMake 应用程序.buildinfoobj" />
      <item itemvalue="CMake 应用程序.buildinfoobj_player" />
      <item itemvalue="CMake 应用程序.cycles_bvh" />
      <item itemvalue="CMake 应用程序.cycles_device" />
      <item itemvalue="CMake 应用程序.cycles_graph" />
      <item itemvalue="CMake 应用程序.cycles_integrator" />
      <item itemvalue="CMake 应用程序.cycles_kernel" />
      <item itemvalue="CMake 应用程序.cycles_kernel_osl" />
      <item itemvalue="CMake 应用程序.cycles_osl_shaders" />
      <item itemvalue="CMake 应用程序.cycles_scene" />
      <item itemvalue="CMake 应用程序.cycles_session" />
      <item itemvalue="CMake 应用程序.cycles_subd" />
      <item itemvalue="CMake 应用程序.cycles_util" />
      <item itemvalue="CMake 应用程序.datatoc" />
      <item itemvalue="CMake 应用程序.draco" />
      <item itemvalue="CMake 应用程序.extern_bullet" />
      <item itemvalue="CMake 应用程序.extern_ceres" />
      <item itemvalue="CMake 应用程序.extern_cuew" />
      <item itemvalue="CMake 应用程序.extern_curve_fit_nd" />
      <item itemvalue="CMake 应用程序.extern_draco" />
      <item itemvalue="CMake 应用程序.extern_fmtlib" />
      <item itemvalue="CMake 应用程序.extern_gflags" />
      <item itemvalue="CMake 应用程序.extern_glog" />
      <item itemvalue="CMake 应用程序.extern_hipew" />
      <item itemvalue="CMake 应用程序.extern_lzma" />
      <item itemvalue="CMake 应用程序.extern_mantaflow" />
      <item itemvalue="CMake 应用程序.extern_minilzo" />
      <item itemvalue="CMake 应用程序.extern_nanosvg" />
      <item itemvalue="CMake 应用程序.extern_quadriflow" />
      <item itemvalue="CMake 应用程序.extern_rangetree" />
      <item itemvalue="CMake 应用程序.extern_recastnavigation" />
      <item itemvalue="CMake 应用程序.extern_ufbx" />
      <item itemvalue="CMake 应用程序.extern_vulkan_memory_allocator" />
      <item itemvalue="CMake 应用程序.extern_wcwidth" />
      <item itemvalue="CMake 应用程序.extern_xxhash" />
      <item itemvalue="CMake 应用程序.ge_blender_routines" />
      <item itemvalue="CMake 应用程序.ge_common" />
      <item itemvalue="CMake 应用程序.ge_converter" />
      <item itemvalue="CMake 应用程序.ge_device" />
      <item itemvalue="CMake 应用程序.ge_expressions" />
      <item itemvalue="CMake 应用程序.ge_ketsji" />
      <item itemvalue="CMake 应用程序.ge_launcher" />
      <item itemvalue="CMake 应用程序.ge_logic_bricks" />
      <item itemvalue="CMake 应用程序.ge_msg_network" />
      <item itemvalue="CMake 应用程序.ge_physics_bullet" />
      <item itemvalue="CMake 应用程序.ge_physics_dummy" />
      <item itemvalue="CMake 应用程序.ge_player" />
      <item itemvalue="CMake 应用程序.ge_rasterizer" />
      <item itemvalue="CMake 应用程序.ge_rasterizer_opengl" />
      <item itemvalue="CMake 应用程序.ge_scenegraph" />
      <item itemvalue="CMake 应用程序.ge_videotexture" />
      <item itemvalue="CMake 应用程序.glsl_preprocess" />
      <item itemvalue="CMake 应用程序.locales" />
      <item itemvalue="CMake 应用程序.makesdna" />
      <item itemvalue="CMake 应用程序.makesrna" />
      <item itemvalue="CMake 应用程序.msgfmt" />
      <item itemvalue="CMake 应用程序.zstd_compress" />
      <item itemvalue="CTest 应用程序.所有 CTest" />
    </list>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="6fccab9c-f71e-4d2b-9da8-f66295ba7a4c" name="更改" comment="" />
      <created>1749519903801</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1749519903801</updated>
      <workItem from="1749519905605" duration="6231000" />
      <workItem from="1749874449356" duration="2335000" />
      <workItem from="1749915666060" duration="169000" />
      <workItem from="1749915899510" duration="1118000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="VCPKGProject">
    <isAutomaticCheckingOnLaunch value="false" />
    <isAutomaticFoundErrors value="true" />
    <isAutomaticReloadCMake value="true" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/intern/cycles/blender/device.cpp</url>
          <option name="group" value="cuda" />
          <line>96</line>
          <option name="timeStamp" value="1" />
          <group>cuda</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/intern/cycles/device/device.cpp</url>
          <option name="group" value="cuda" />
          <line>187</line>
          <option name="timeStamp" value="2" />
          <group>cuda</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/python/intern/bpy_props.cc</url>
          <option name="group" value="cuda" />
          <line>1991</line>
          <option name="timeStamp" value="4" />
          <group>cuda</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/python/intern/bpy_props.cc</url>
          <option name="group" value="cuda" />
          <line>2048</line>
          <option name="timeStamp" value="5" />
          <group>cuda</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/makesrna/intern/rna_access.cc</url>
          <option name="group" value="cuda" />
          <line>565</line>
          <option name="timeStamp" value="6" />
          <group>cuda</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/makesrna/intern/rna_access.cc</url>
          <option name="group" value="cuda" />
          <line>769</line>
          <option name="timeStamp" value="7" />
          <group>cuda</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/makesrna/intern/rna_access.cc</url>
          <option name="group" value="cuda" />
          <line>761</line>
          <option name="timeStamp" value="10" />
          <group>cuda</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/makesrna/intern/rna_access.cc</url>
          <option name="group" value="cuda" />
          <line>674</line>
          <option name="timeStamp" value="11" />
          <group>cuda</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/makesrna/intern/rna_access.cc</url>
          <option name="group" value="cuda" />
          <line>4562</line>
          <option name="timeStamp" value="12" />
          <group>cuda</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/makesrna/intern/rna_access.cc</url>
          <option name="group" value="cuda" />
          <line>4498</line>
          <option name="timeStamp" value="13" />
          <group>cuda</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/makesrna/intern/rna_access.cc</url>
          <option name="group" value="cuda" />
          <line>558</line>
          <option name="timeStamp" value="14" />
          <group>cuda</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/makesrna/intern/rna_access.cc</url>
          <option name="group" value="cuda" />
          <line>4510</line>
          <option name="timeStamp" value="17" />
          <group>cuda</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/intern/cycles/blender/python.cpp</url>
          <option name="group" value="cuda" />
          <line>898</line>
          <option name="timeStamp" value="19" />
          <group>cuda</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/makesrna/intern/rna_access.cc</url>
          <option name="group" value="cuda" />
          <line>563</line>
          <option name="timeStamp" value="20" />
          <group>cuda</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/makesrna/intern/rna_access.cc</url>
          <option name="group" value="cuda" />
          <line>559</line>
          <option name="timeStamp" value="21" />
          <group>cuda</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/python/intern/bpy_props.cc</url>
          <option name="group" value="cuda" />
          <line>2209</line>
          <option name="timeStamp" value="26" />
          <group>cuda</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/intern/cycles/blender/python.cpp</url>
          <option name="group" value="cuda" />
          <line>402</line>
          <option name="timeStamp" value="28" />
          <group>cuda</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/intern/cycles/device/device.cpp</url>
          <option name="group" value="cuda" />
          <line>226</line>
          <option name="timeStamp" value="30" />
          <group>cuda</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/intern/cycles/device/device.cpp</url>
          <option name="group" value="cuda" />
          <line>221</line>
          <option name="timeStamp" value="31" />
          <group>cuda</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/intern/cycles/device/device.cpp</url>
          <option name="group" value="cuda" />
          <line>228</line>
          <option name="timeStamp" value="32" />
          <group>cuda</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/intern/cycles/device/cuda/device.cpp</url>
          <option name="group" value="cuda" />
          <line>103</line>
          <option name="timeStamp" value="33" />
          <group>cuda</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/intern/cycles/device/cuda/device.cpp</url>
          <option name="group" value="cuda" />
          <line>33</line>
          <option name="timeStamp" value="34" />
          <group>cuda</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/extern/cuew/src/cuew.c</url>
          <option name="group" value="cuda" />
          <line>685</line>
          <option name="timeStamp" value="35" />
          <group>cuda</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/extern/cuew/src/cuew.c</url>
          <option name="group" value="cuda" />
          <line>348</line>
          <option name="timeStamp" value="36" />
          <group>cuda</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/extern/cuew/src/cuew.c</url>
          <option name="group" value="cuda" />
          <line>315</line>
          <option name="timeStamp" value="38" />
          <group>cuda</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/draw/intern/draw_manager_c.cc</url>
          <option name="group" value="渲染" />
          <line>1693</line>
          <option name="timeStamp" value="45" />
          <group>渲染</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/editors/space_view3d/view3d_draw.cc</url>
          <option name="group" value="渲染" />
          <line>1763</line>
          <option name="timeStamp" value="46" />
          <group>渲染</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/editors/space_view3d/view3d_draw.cc</url>
          <option name="group" value="渲染" />
          <line>1619</line>
          <option name="timeStamp" value="47" />
          <group>渲染</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/draw/intern/draw_manager_c.cc</url>
          <option name="group" value="渲染" />
          <line>1826</line>
          <option name="timeStamp" value="48" />
          <group>渲染</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/draw/engines/eevee_next/eevee_instance.cc</url>
          <option name="group" value="渲染" />
          <line>433</line>
          <option name="timeStamp" value="49" />
          <group>渲染</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/draw/engines/eevee_next/eevee_view.cc</url>
          <option name="group" value="渲染" />
          <line>136</line>
          <option name="timeStamp" value="50" />
          <group>渲染</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/draw/intern/draw_manager_c.cc</url>
          <option name="group" value="渲染" />
          <line>1127</line>
          <option name="timeStamp" value="51" />
          <group>渲染</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/draw/intern/draw_manager_c.cc</url>
          <option name="group" value="渲染" />
          <line>3845</line>
          <option name="timeStamp" value="52" />
          <group>渲染</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/editors/space_action/action_draw.cc</url>
          <option name="group" value="act" />
          <line>133</line>
          <option name="timeStamp" value="53" />
          <group>act</group>
        </line-breakpoint>
        <line-breakpoint enabled="true" type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/editors/space_action/action_draw.cc</url>
          <option name="group" value="bake" />
          <line>704</line>
          <option name="timeStamp" value="54" />
          <group>bake</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/editors/space_action/action_draw.cc</url>
          <option name="group" value="act" />
          <line>767</line>
          <option name="timeStamp" value="55" />
          <group>act</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/editors/space_action/action_draw.cc</url>
          <option name="group" value="act" />
          <line>875</line>
          <option name="timeStamp" value="56" />
          <group>act</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/editors/transform/transform_convert_action.cc</url>
          <option name="group" value="act" />
          <line>1252</line>
          <option name="timeStamp" value="57" />
          <group>act</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/editors/space_action/action_select.cc</url>
          <option name="group" value="act" />
          <line>98</line>
          <option name="timeStamp" value="58" />
          <group>act</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/editors/space_action/action_draw.cc</url>
          <option name="group" value="act" />
          <line>684</line>
          <option name="timeStamp" value="59" />
          <group>act</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/editors/space_action/action_draw.cc</url>
          <option name="group" value="act" />
          <line>669</line>
          <option name="timeStamp" value="60" />
          <group>act</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/editors/animation/anim_channels_edit.cc</url>
          <option name="group" value="act" />
          <line>2155</line>
          <option name="timeStamp" value="61" />
          <group>act</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/editors/space_action/action_select.cc</url>
          <option name="group" value="act" />
          <line>69</line>
          <option name="timeStamp" value="63" />
          <group>act</group>
        </line-breakpoint>
        <line-breakpoint enabled="true" type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/editors/space_sequencer/sequencer_channels_draw.cc</url>
          <option name="group" value="seq-tl" />
          <line>211</line>
          <option name="timeStamp" value="64" />
          <group>seq-tl</group>
        </line-breakpoint>
        <line-breakpoint enabled="true" type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/editors/space_sequencer/sequencer_channels_draw.cc</url>
          <option name="group" value="seq-tl" />
          <line>304</line>
          <option name="timeStamp" value="65" />
          <group>seq-tl</group>
        </line-breakpoint>
        <line-breakpoint enabled="true" type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/editors/space_sequencer/sequencer_channels_draw.cc</url>
          <option name="group" value="seq-tl" />
          <line>78</line>
          <option name="timeStamp" value="66" />
          <group>seq-tl</group>
        </line-breakpoint>
        <line-breakpoint enabled="true" type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/editors/space_sequencer/sequencer_channels_draw.cc</url>
          <option name="group" value="seq-tl" />
          <line>102</line>
          <option name="timeStamp" value="67" />
          <group>seq-tl</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/editors/space_action/space_action.cc</url>
          <option name="group" value="act" />
          <line>182</line>
          <option name="timeStamp" value="70" />
          <group>act</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/editors/space_action/action_draw.cc</url>
          <option name="group" value="act" />
          <line>512</line>
          <option name="timeStamp" value="72" />
          <group>act</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/editors/space_action/action_draw.cc</url>
          <option name="group" value="act" />
          <line>353</line>
          <option name="timeStamp" value="73" />
          <group>act</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/editors/space_action/action_draw.cc</url>
          <option name="group" value="act" />
          <line>463</line>
          <option name="timeStamp" value="74" />
          <group>act</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/editors/space_action/action_draw.cc</url>
          <option name="group" value="act" />
          <line>339</line>
          <option name="timeStamp" value="75" />
          <group>act</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/editors/animation/anim_filter.cc</url>
          <option name="group" value="act" />
          <line>3709</line>
          <option name="timeStamp" value="76" />
          <group>act</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/editors/animation/anim_filter.cc</url>
          <option name="group" value="act" />
          <line>3714</line>
          <option name="timeStamp" value="78" />
          <group>act</group>
        </line-breakpoint>
        <line-breakpoint enabled="true" type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/editors/physics/physics_pointcache.cc</url>
          <option name="group" value="bake" />
          <line>177</line>
          <option name="timeStamp" value="79" />
          <group>bake</group>
        </line-breakpoint>
        <line-breakpoint enabled="true" type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/editors/physics/physics_pointcache.cc</url>
          <option name="group" value="bake" />
          <line>202</line>
          <option name="timeStamp" value="80" />
          <group>bake</group>
        </line-breakpoint>
        <line-breakpoint enabled="true" type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/editors/physics/physics_pointcache.cc</url>
          <option name="group" value="bake" />
          <line>213</line>
          <option name="timeStamp" value="81" />
          <group>bake</group>
        </line-breakpoint>
        <line-breakpoint enabled="true" type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/editors/physics/physics_pointcache.cc</url>
          <option name="group" value="bake" />
          <line>248</line>
          <option name="timeStamp" value="82" />
          <group>bake</group>
        </line-breakpoint>
        <line-breakpoint enabled="true" type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/editors/physics/physics_pointcache.cc</url>
          <option name="group" value="bake" />
          <line>335</line>
          <option name="timeStamp" value="83" />
          <group>bake</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/editors/physics/rigidbody_object.cc</url>
          <option name="group" value="rigplay" />
          <line>296</line>
          <option name="timeStamp" value="84" />
          <group>rigplay</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/editors/physics/rigidbody_object.cc</url>
          <option name="group" value="rigplay" />
          <line>509</line>
          <option name="timeStamp" value="85" />
          <group>rigplay</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/editors/physics/rigidbody_object.cc</url>
          <option name="group" value="rigplay" />
          <line>424</line>
          <option name="timeStamp" value="86" />
          <group>rigplay</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/editors/physics/rigidbody_object.cc</url>
          <option name="group" value="rigplay" />
          <line>247</line>
          <option name="timeStamp" value="87" />
          <group>rigplay</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/editors/physics/rigidbody_object.cc</url>
          <option name="group" value="rigplay" />
          <line>198</line>
          <option name="timeStamp" value="88" />
          <group>rigplay</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/editors/physics/rigidbody_object.cc</url>
          <option name="group" value="rigplay" />
          <line>152</line>
          <option name="timeStamp" value="90" />
          <group>rigplay</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/editors/physics/rigidbody_object.cc</url>
          <option name="group" value="rigplay" />
          <line>62</line>
          <option name="timeStamp" value="91" />
          <group>rigplay</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/editors/physics/rigidbody_object.cc</url>
          <option name="group" value="rigplay" />
          <line>45</line>
          <option name="timeStamp" value="92" />
          <group>rigplay</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/editors/physics/rigidbody_world.cc</url>
          <option name="group" value="rigplay" />
          <line>158</line>
          <option name="timeStamp" value="93" />
          <group>rigplay</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/editors/physics/rigidbody_constraint.cc</url>
          <option name="group" value="rigplay" />
          <line>45</line>
          <option name="timeStamp" value="95" />
          <group>rigplay</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/editors/physics/rigidbody_constraint.cc</url>
          <option name="group" value="rigplay" />
          <line>57</line>
          <option name="timeStamp" value="96" />
          <group>rigplay</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/editors/physics/rigidbody_constraint.cc</url>
          <option name="group" value="rigplay" />
          <line>71</line>
          <option name="timeStamp" value="97" />
          <group>rigplay</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/editors/physics/rigidbody_constraint.cc</url>
          <option name="group" value="rigplay" />
          <line>81</line>
          <option name="timeStamp" value="98" />
          <group>rigplay</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/editors/physics/rigidbody_constraint.cc</url>
          <option name="group" value="rigplay" />
          <line>109</line>
          <option name="timeStamp" value="99" />
          <group>rigplay</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/editors/physics/rigidbody_constraint.cc</url>
          <option name="group" value="rigplay" />
          <line>123</line>
          <option name="timeStamp" value="100" />
          <group>rigplay</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/editors/physics/rigidbody_constraint.cc</url>
          <option name="group" value="rigplay" />
          <line>177</line>
          <option name="timeStamp" value="101" />
          <group>rigplay</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/editors/physics/particle_object.cc</url>
          <option name="group" value="rigplay" />
          <line>70</line>
          <option name="timeStamp" value="102" />
          <group>rigplay</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/editors/physics/particle_object.cc</url>
          <option name="group" value="rigplay" />
          <line>104</line>
          <option name="timeStamp" value="103" />
          <group>rigplay</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/editors/physics/particle_object.cc</url>
          <option name="group" value="rigplay" />
          <line>161</line>
          <option name="timeStamp" value="104" />
          <group>rigplay</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/editors/physics/particle_object.cc</url>
          <option name="group" value="rigplay" />
          <line>216</line>
          <option name="timeStamp" value="105" />
          <group>rigplay</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/editors/physics/particle_object.cc</url>
          <option name="group" value="rigplay" />
          <line>263</line>
          <option name="timeStamp" value="106" />
          <group>rigplay</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/editors/physics/particle_object.cc</url>
          <option name="group" value="rigplay" />
          <line>314</line>
          <option name="timeStamp" value="107" />
          <group>rigplay</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/editors/physics/particle_object.cc</url>
          <option name="group" value="rigplay" />
          <line>354</line>
          <option name="timeStamp" value="108" />
          <group>rigplay</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/editors/physics/particle_object.cc</url>
          <option name="group" value="rigplay" />
          <line>423</line>
          <option name="timeStamp" value="109" />
          <group>rigplay</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/editors/physics/particle_object.cc</url>
          <option name="group" value="rigplay" />
          <line>462</line>
          <option name="timeStamp" value="110" />
          <group>rigplay</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/editors/physics/particle_object.cc</url>
          <option name="group" value="rigplay" />
          <line>502</line>
          <option name="timeStamp" value="111" />
          <group>rigplay</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/editors/physics/particle_object.cc</url>
          <option name="group" value="rigplay" />
          <line>548</line>
          <option name="timeStamp" value="112" />
          <group>rigplay</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/editors/physics/particle_object.cc</url>
          <option name="group" value="rigplay" />
          <line>587</line>
          <option name="timeStamp" value="113" />
          <group>rigplay</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/editors/physics/particle_object.cc</url>
          <option name="group" value="rigplay" />
          <line>641</line>
          <option name="timeStamp" value="114" />
          <group>rigplay</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/editors/physics/particle_object.cc</url>
          <option name="group" value="rigplay" />
          <line>698</line>
          <option name="timeStamp" value="115" />
          <group>rigplay</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/editors/physics/particle_object.cc</url>
          <option name="group" value="rigplay" />
          <line>724</line>
          <option name="timeStamp" value="116" />
          <group>rigplay</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/editors/physics/particle_object.cc</url>
          <option name="group" value="rigplay" />
          <line>917</line>
          <option name="timeStamp" value="117" />
          <group>rigplay</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/editors/physics/particle_object.cc</url>
          <option name="group" value="rigplay" />
          <line>938</line>
          <option name="timeStamp" value="118" />
          <group>rigplay</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/editors/physics/particle_object.cc</url>
          <option name="group" value="rigplay" />
          <line>1000</line>
          <option name="timeStamp" value="119" />
          <group>rigplay</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/editors/physics/particle_object.cc</url>
          <option name="group" value="rigplay" />
          <line>1086</line>
          <option name="timeStamp" value="120" />
          <group>rigplay</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/editors/physics/particle_object.cc</url>
          <option name="group" value="rigplay" />
          <line>1236</line>
          <option name="timeStamp" value="121" />
          <group>rigplay</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/editors/physics/particle_object.cc</url>
          <option name="group" value="rigplay" />
          <line>1342</line>
          <option name="timeStamp" value="122" />
          <group>rigplay</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/draw/engines/eevee_next/eevee_velocity.cc</url>
          <option name="group" value="rigplay" />
          <line>420</line>
          <option name="timeStamp" value="124" />
          <group>rigplay</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/draw/engines/overlay/overlay_extra.cc</url>
          <option name="group" value="rigplay" />
          <line>433</line>
          <option name="timeStamp" value="125" />
          <group>rigplay</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/draw/engines/overlay/overlay_extra.cc</url>
          <option name="group" value="rigplay" />
          <line>453</line>
          <option name="timeStamp" value="127" />
          <group>rigplay</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/draw/engines/overlay/overlay_next_relation.hh</url>
          <option name="group" value="rigplay" />
          <line>85</line>
          <option name="timeStamp" value="130" />
          <group>rigplay</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/draw/engines/overlay/overlay_extra.cc</url>
          <option name="group" value="rigplay" />
          <line>1350</line>
          <option name="timeStamp" value="131" />
          <group>rigplay</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/draw/engines/overlay/overlay_extra.cc</url>
          <option name="group" value="rigplay" />
          <line>1673</line>
          <option name="timeStamp" value="132" />
          <group>rigplay</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/draw/engines/eevee_next/eevee_velocity.cc</url>
          <option name="group" value="rigplay" />
          <line>415</line>
          <option name="timeStamp" value="134" />
          <group>rigplay</group>
        </line-breakpoint>
        <line-breakpoint enabled="true" type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/blenkernel/intern/pointcache.cc</url>
          <option name="group" value="bake" />
          <line>3163</line>
          <option name="timeStamp" value="135" />
          <group>bake</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/windowmanager/intern/wm_jobs.cc</url>
          <option name="group" value="job" />
          <line>487</line>
          <option name="timeStamp" value="136" />
          <group>job</group>
        </line-breakpoint>
        <line-breakpoint enabled="true" type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/blenkernel/intern/pointcache.cc</url>
          <option name="group" value="bake" />
          <line>3283</line>
          <option name="timeStamp" value="137" />
          <group>bake</group>
        </line-breakpoint>
        <line-breakpoint enabled="true" type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/blenkernel/intern/pointcache.cc</url>
          <option name="group" value="bake" />
          <line>3318</line>
          <option name="timeStamp" value="138" />
          <group>bake</group>
        </line-breakpoint>
        <line-breakpoint enabled="true" type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/blenkernel/intern/pointcache.cc</url>
          <option name="group" value="bake" />
          <line>3296</line>
          <option name="timeStamp" value="139" />
          <group>bake</group>
        </line-breakpoint>
        <line-breakpoint enabled="true" type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/blenkernel/intern/pointcache.cc</url>
          <option name="group" value="bake" />
          <line>3337</line>
          <option name="timeStamp" value="140" />
          <group>bake</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/blenkernel/intern/scene.cc</url>
          <option name="group" value="deg" />
          <line>2582</line>
          <option name="timeStamp" value="141" />
          <group>deg</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/blenkernel/intern/scene.cc</url>
          <option name="group" value="deg" />
          <line>2592</line>
          <option name="timeStamp" value="143" />
          <group>deg</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/blenkernel/intern/scene.cc</url>
          <option name="group" value="deg" />
          <line>2595</line>
          <option name="timeStamp" value="144" />
          <group>deg</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/blenkernel/intern/scene.cc</url>
          <option name="group" value="deg" />
          <line>2606</line>
          <option name="timeStamp" value="145" />
          <group>deg</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/depsgraph/intern/depsgraph_eval.cc</url>
          <option name="group" value="deg" />
          <line>101</line>
          <option name="timeStamp" value="146" />
          <group>deg</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/depsgraph/intern/eval/deg_eval_flush.cc</url>
          <option name="group" value="deg" />
          <line>344</line>
          <option name="timeStamp" value="149" />
          <group>deg</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/depsgraph/intern/eval/deg_eval_flush.cc</url>
          <option name="group" value="deg" />
          <line>362</line>
          <option name="timeStamp" value="151" />
          <group>deg</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/windowmanager/intern/wm_operators.cc</url>
          <option name="group" value="deg" />
          <line>3674</line>
          <option name="timeStamp" value="152" />
          <group>deg</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/draw/intern/draw_manager_c.cc</url>
          <option name="group" value="deg" />
          <line>1496</line>
          <option name="timeStamp" value="155" />
          <group>deg</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/draw/intern/draw_manager_c.cc</url>
          <option name="group" value="deg" />
          <line>2779</line>
          <option name="timeStamp" value="156" />
          <group>deg</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/draw/intern/draw_manager_c.cc</url>
          <option name="group" value="deg" />
          <line>1733</line>
          <option name="timeStamp" value="157" />
          <group>deg</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/draw/intern/draw_manager_c.cc</url>
          <option name="group" value="deg" />
          <line>1803</line>
          <option name="timeStamp" value="158" />
          <group>deg</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/draw/intern/draw_manager_c.cc</url>
          <line>1515</line>
          <option name="timeStamp" value="161" />
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/editors/screen/screen_edit.cc</url>
          <line>1881</line>
          <option name="timeStamp" value="162" />
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/blenkernel/intern/scene.cc</url>
          <line>2498</line>
          <option name="timeStamp" value="163" />
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/blenkernel/intern/scene.cc</url>
          <line>2505</line>
          <option name="timeStamp" value="164" />
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/depsgraph/intern/depsgraph_eval.cc</url>
          <line>46</line>
          <option name="timeStamp" value="167" />
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/depsgraph/intern/eval/deg_eval.cc</url>
          <line>448</line>
          <option name="timeStamp" value="168" />
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/depsgraph/intern/eval/deg_eval.cc</url>
          <option name="group" value="obj" />
          <line>247</line>
          <option name="timeStamp" value="171" />
          <group>obj</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/depsgraph/intern/eval/deg_eval.cc</url>
          <option name="group" value="obj" />
          <line>381</line>
          <option name="timeStamp" value="172" />
          <group>obj</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/blenkernel/intern/object_update.cc</url>
          <option name="group" value="obj" />
          <line>309</line>
          <option name="timeStamp" value="173" />
          <group>obj</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/blenkernel/intern/object_update.cc</url>
          <option name="group" value="obj" />
          <line>161</line>
          <option name="timeStamp" value="175" />
          <group>obj</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/blenkernel/intern/object_update.cc</url>
          <option name="group" value="obj" />
          <line>157</line>
          <option name="timeStamp" value="176" />
          <group>obj</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/depsgraph/intern/eval/deg_eval.cc</url>
          <option name="group" value="obj" />
          <line>521</line>
          <option name="timeStamp" value="177" />
          <group>obj</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/blenlib/intern/task_range.cc</url>
          <option name="group" value="obj" />
          <line>107</line>
          <option name="timeStamp" value="178" />
          <group>obj</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/blenkernel/intern/rigidbody.cc</url>
          <option name="group" value="obj" />
          <line>2545</line>
          <option name="timeStamp" value="179" />
          <group>obj</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/blenkernel/intern/rigidbody.cc</url>
          <option name="group" value="obj" />
          <line>2284</line>
          <option name="timeStamp" value="180" />
          <group>obj</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/blenkernel/intern/collection.cc</url>
          <option name="group" value="obj" />
          <line>874</line>
          <option name="timeStamp" value="181" />
          <group>obj</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/blenkernel/intern/anim_sys.cc</url>
          <option name="group" value="obj" />
          <line>4210</line>
          <option name="timeStamp" value="182" />
          <group>obj</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/blenkernel/intern/anim_sys.cc</url>
          <option name="group" value="obj" />
          <line>903</line>
          <option name="timeStamp" value="185" />
          <group>obj</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/blenkernel/intern/armature_update.cc</url>
          <option name="group" value="obj" />
          <line>833</line>
          <option name="timeStamp" value="187" />
          <group>obj</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/blenkernel/intern/armature_update.cc</url>
          <option name="group" value="obj" />
          <line>908</line>
          <option name="timeStamp" value="188" />
          <group>obj</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/depsgraph/intern/builder/deg_builder_nodes_rig.cc</url>
          <option name="group" value="obj" />
          <line>228</line>
          <option name="timeStamp" value="189" />
          <group>obj</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/depsgraph/intern/builder/deg_builder_nodes_rig.cc</url>
          <option name="group" value="obj" />
          <line>222</line>
          <option name="timeStamp" value="190" />
          <group>obj</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/blenkernel/intern/armature_update.cc</url>
          <option name="group" value="obj" />
          <line>1022</line>
          <option name="timeStamp" value="191" />
          <group>obj</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/blenkernel/intern/object_update.cc</url>
          <option name="group" value="obj" />
          <line>77</line>
          <option name="timeStamp" value="192" />
          <group>obj</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/blenkernel/intern/object_update.cc</url>
          <option name="group" value="obj" />
          <line>108</line>
          <option name="timeStamp" value="193" />
          <group>obj</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/blenkernel/intern/armature_update.cc</url>
          <option name="group" value="obj" />
          <line>936</line>
          <option name="timeStamp" value="194" />
          <group>obj</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/blenkernel/intern/armature_update.cc</url>
          <option name="group" value="obj" />
          <line>979</line>
          <option name="timeStamp" value="195" />
          <group>obj</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/blenkernel/intern/rigidbody.cc</url>
          <option name="group" value="obj" />
          <line>2568</line>
          <option name="timeStamp" value="196" />
          <group>obj</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/blenkernel/intern/object_update.cc</url>
          <option name="group" value="obj" />
          <line>57</line>
          <option name="timeStamp" value="197" />
          <group>obj</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/blenkernel/intern/object_update.cc</url>
          <option name="group" value="obj" />
          <line>126</line>
          <option name="timeStamp" value="198" />
          <group>obj</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/blenkernel/intern/object_update.cc</url>
          <option name="group" value="obj" />
          <line>243</line>
          <option name="timeStamp" value="199" />
          <group>obj</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/blenkernel/intern/rigidbody.cc</url>
          <line>231</line>
          <option name="timeStamp" value="200" />
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/blenkernel/intern/rigidbody.cc</url>
          <line>228</line>
          <option name="timeStamp" value="201" />
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/blenkernel/intern/scene.cc</url>
          <line>2568</line>
          <option name="timeStamp" value="202" />
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/depsgraph/intern/eval/deg_eval.cc</url>
          <line>509</line>
          <option name="timeStamp" value="203" />
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/gameengine/Ketsji/KX_Scene.cpp</url>
          <line>760</line>
          <option name="timeStamp" value="204" />
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/draw/engines/eevee_next/eevee_view.cc</url>
          <line>47</line>
          <option name="timeStamp" value="205" />
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/depsgraph/intern/eval/deg_eval.cc</url>
          <line>500</line>
          <option name="timeStamp" value="206" />
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/depsgraph/intern/eval/deg_eval.cc</url>
          <line>137</line>
          <option name="timeStamp" value="207" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/depsgraph/intern/eval/deg_eval.cc</url>
          <option name="group" value="riganim" />
          <line>127</line>
          <option name="timeStamp" value="215" />
          <group>riganim</group>
        </line-breakpoint>
        <line-breakpoint enabled="true" type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/depsgraph/intern/builder/deg_builder_nodes.cc</url>
          <option name="group" value="riganim" />
          <line>1543</line>
          <option name="timeStamp" value="216" />
          <group>riganim</group>
        </line-breakpoint>
        <line-breakpoint enabled="true" type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/depsgraph/intern/eval/deg_eval_flush.cc</url>
          <option name="group" value="riganim" />
          <line>334</line>
          <option name="timeStamp" value="222" />
          <group>riganim</group>
        </line-breakpoint>
        <line-breakpoint enabled="true" type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/editors/screen/screen_ops.cc</url>
          <option name="group" value="riganim" />
          <line>5548</line>
          <option name="timeStamp" value="223" />
          <group>riganim</group>
        </line-breakpoint>
        <line-breakpoint enabled="true" type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/editors/screen/screen_ops.cc</url>
          <option name="group" value="riganim" />
          <line>5551</line>
          <option name="timeStamp" value="225" />
          <group>riganim</group>
        </line-breakpoint>
        <line-breakpoint enabled="true" type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/editors/screen/screen_ops.cc</url>
          <option name="group" value="riganim" />
          <line>5546</line>
          <option name="timeStamp" value="226" />
          <group>riganim</group>
        </line-breakpoint>
        <line-breakpoint enabled="true" type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/depsgraph/intern/eval/deg_eval.cc</url>
          <option name="group" value="riganim" />
          <line>109</line>
          <option name="timeStamp" value="227" />
          <group>riganim</group>
        </line-breakpoint>
        <line-breakpoint enabled="true" type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/blenkernel/intern/rigidbody.cc</url>
          <option name="group" value="riganim" />
          <line>2390</line>
          <option name="timeStamp" value="231" />
          <group>riganim</group>
        </line-breakpoint>
        <line-breakpoint enabled="true" type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/blenkernel/intern/rigidbody.cc</url>
          <option name="group" value="riganim" />
          <line>2389</line>
          <option name="timeStamp" value="232" />
          <group>riganim</group>
        </line-breakpoint>
        <line-breakpoint enabled="true" type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/blenkernel/intern/rigidbody.cc</url>
          <option name="group" value="riganim" />
          <line>1192</line>
          <option name="timeStamp" value="233" />
          <group>riganim</group>
        </line-breakpoint>
        <line-breakpoint enabled="true" type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/blenkernel/intern/rigidbody.cc</url>
          <option name="group" value="riganim" />
          <line>2317</line>
          <option name="timeStamp" value="234" />
          <group>riganim</group>
        </line-breakpoint>
        <line-breakpoint enabled="true" type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/blenkernel/intern/rigidbody.cc</url>
          <option name="group" value="riganim" />
          <line>2314</line>
          <option name="timeStamp" value="235" />
          <group>riganim</group>
        </line-breakpoint>
        <line-breakpoint enabled="true" type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/blenkernel/intern/rigidbody.cc</url>
          <option name="group" value="riganim" />
          <line>2315</line>
          <option name="timeStamp" value="236" />
          <group>riganim</group>
        </line-breakpoint>
        <line-breakpoint enabled="true" type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/blenkernel/intern/scene.cc</url>
          <option name="group" value="riganim" />
          <line>1459</line>
          <option name="timeStamp" value="237" />
          <group>riganim</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/depsgraph/intern/depsgraph_eval.cc</url>
          <option name="group" value="logic-act" />
          <line>63</line>
          <option name="timeStamp" value="238" />
          <group>logic-act</group>
        </line-breakpoint>
        <line-breakpoint enabled="true" type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/blenkernel/intern/rigidbody.cc</url>
          <option name="group" value="riganim" />
          <line>2413</line>
          <option name="timeStamp" value="239" />
          <group>riganim</group>
        </line-breakpoint>
        <line-breakpoint enabled="true" type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/blenkernel/intern/rigidbody.cc</url>
          <option name="group" value="riganim" />
          <line>2380</line>
          <option name="timeStamp" value="241" />
          <group>riganim</group>
        </line-breakpoint>
        <line-breakpoint enabled="true" type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/blenkernel/intern/rigidbody.cc</url>
          <option name="group" value="riganim" />
          <line>2381</line>
          <option name="timeStamp" value="242" />
          <group>riganim</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/gameengine/GameLogic/SCA_ActionActuator.cpp</url>
          <option name="group" value="gameanimplay" />
          <line>278</line>
          <option name="timeStamp" value="244" />
          <group>gameanimplay</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/gameengine/Ketsji/KX_Scene.cpp</url>
          <option name="group" value="gameanimplay" />
          <line>2457</line>
          <option name="timeStamp" value="245" />
          <group>gameanimplay</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/gameengine/Ketsji/BL_Action.cpp</url>
          <option name="group" value="gameanimplay" />
          <line>186</line>
          <option name="timeStamp" value="246" />
          <group>gameanimplay</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/editors/screen/screen_ops.cc</url>
          <option name="group" value="gameanimplay" />
          <line>5374</line>
          <option name="timeStamp" value="247" />
          <group>gameanimplay</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/depsgraph/intern/depsgraph_eval.cc</url>
          <option name="group" value="gameanimplay" />
          <line>100</line>
          <option name="timeStamp" value="250" />
          <group>gameanimplay</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/render/intern/engine.cc</url>
          <option name="group" value="gameanimplay" />
          <line>693</line>
          <option name="timeStamp" value="251" />
          <group>gameanimplay</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/render/intern/pipeline.cc</url>
          <option name="group" value="gameanimplay" />
          <line>1956</line>
          <option name="timeStamp" value="252" />
          <group>gameanimplay</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/gameengine/Ketsji/BL_Action.cpp</url>
          <option name="group" value="logic-act" />
          <line>584</line>
          <option name="timeStamp" value="253" />
          <group>logic-act</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/gameengine/Ketsji/BL_Action.cpp</url>
          <option name="group" value="logic-act" />
          <line>615</line>
          <option name="timeStamp" value="254" />
          <group>logic-act</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/gameengine/Ketsji/KX_KetsjiEngine.cpp</url>
          <option name="group" value="logic-act" />
          <line>538</line>
          <option name="timeStamp" value="255" />
          <group>logic-act</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/gameengine/GameLogic/SCA_ActionActuator.cpp</url>
          <option name="group" value="logic-act" />
          <line>168</line>
          <option name="timeStamp" value="257" />
          <group>logic-act</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/gameengine/GameLogic/SCA_ActionActuator.cpp</url>
          <option name="group" value="logic-act" />
          <line>126</line>
          <option name="timeStamp" value="260" />
          <group>logic-act</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/gameengine/Ketsji/BL_Action.cpp</url>
          <option name="group" value="logic-act" />
          <line>251</line>
          <option name="timeStamp" value="261" />
          <group>logic-act</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/gameengine/Ketsji/BL_Action.cpp</url>
          <option name="group" value="logic-act" />
          <line>275</line>
          <option name="timeStamp" value="262" />
          <group>logic-act</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/gameengine/Ketsji/KX_Scene.cpp</url>
          <option name="group" value="logic-act" />
          <line>712</line>
          <option name="timeStamp" value="263" />
          <group>logic-act</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/gameengine/GameLogic/SCA_LogicManager.cpp</url>
          <option name="group" value="logic-act" />
          <line>161</line>
          <option name="timeStamp" value="264" />
          <group>logic-act</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/gameengine/Ketsji/BL_Action.cpp</url>
          <option name="group" value="gameanimplay" />
          <line>262</line>
          <option name="timeStamp" value="268" />
          <group>gameanimplay</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/gameengine/Ketsji/BL_Action.cpp</url>
          <option name="group" value="gameanimplay" />
          <line>388</line>
          <option name="timeStamp" value="269" />
          <group>gameanimplay</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/gameengine/Ketsji/KX_Scene.cpp</url>
          <option name="group" value="gameanimplay" />
          <line>1404</line>
          <option name="timeStamp" value="272" />
          <group>gameanimplay</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/gameengine/GameLogic/SCA_ActionActuator.cpp</url>
          <option name="group" value="gameanimplay" />
          <line>133</line>
          <option name="timeStamp" value="274" />
          <group>gameanimplay</group>
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/depsgraph/intern/depsgraph_eval.cc</url>
          <option name="group" value="gameanimplay" />
          <line>72</line>
          <option name="timeStamp" value="275" />
          <group>gameanimplay</group>
        </line-breakpoint>
        <line-breakpoint enabled="true" type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/intern/rigidbody/rb_bullet_api.cpp</url>
          <line>198</line>
          <option name="timeStamp" value="276" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/extern/bullet2/src/BulletDynamics/Dynamics/btDiscreteDynamicsWorld.cpp</url>
          <line>385</line>
          <option name="timeStamp" value="277" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/gameengine/Physics/Bullet/CcdPhysicsEnvironment.cpp</url>
          <line>737</line>
          <option name="timeStamp" value="278" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/blenkernel/intern/rigidbody.cc</url>
          <line>2323</line>
          <option name="timeStamp" value="281" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/blenkernel/intern/rigidbody.cc</url>
          <line>2542</line>
          <option name="timeStamp" value="284" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/windowmanager/intern/wm_event_system.cc</url>
          <line>598</line>
          <option name="timeStamp" value="285" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/windowmanager/intern/wm_event_system.cc</url>
          <line>585</line>
          <option name="timeStamp" value="286" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/source/blender/windowmanager/intern/wm_event_system.cc</url>
          <line>586</line>
          <option name="timeStamp" value="289" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/cmake-build-release/bin/4.3/scripts/addons_core/cycles/properties.py</url>
          <line>1622</line>
          <option name="timeStamp" value="27" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
</project>