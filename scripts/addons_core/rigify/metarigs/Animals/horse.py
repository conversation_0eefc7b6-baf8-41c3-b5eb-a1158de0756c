# SPDX-FileCopyrightText: 2017-2022 Blender Foundation
#
# SPDX-License-Identifier: GPL-2.0-or-later

import bpy

from mathutils import Color


def create(obj):  # noqa
    # generated by rigify.utils.write_metarig
    bpy.ops.object.mode_set(mode='EDIT')
    arm = obj.data

    for i in range(6):
        arm.rigify_colors.add()

    arm.rigify_colors[0].name = "Root"
    arm.rigify_colors[0].active = Color((0.5490, 1.0000, 1.0000))
    arm.rigify_colors[0].normal = Color((0.4353, 0.1843, 0.4157))
    arm.rigify_colors[0].select = Color((0.3140, 0.7840, 1.0000))
    arm.rigify_colors[0].standard_colors_lock = True
    arm.rigify_colors[1].name = "IK"
    arm.rigify_colors[1].active = Color((0.5490, 1.0000, 1.0000))
    arm.rigify_colors[1].normal = Color((0.6039, 0.0000, 0.0000))
    arm.rigify_colors[1].select = Color((0.3140, 0.7840, 1.0000))
    arm.rigify_colors[1].standard_colors_lock = True
    arm.rigify_colors[2].name = "Special"
    arm.rigify_colors[2].active = Color((0.5490, 1.0000, 1.0000))
    arm.rigify_colors[2].normal = Color((0.9569, 0.7882, 0.0471))
    arm.rigify_colors[2].select = Color((0.3140, 0.7840, 1.0000))
    arm.rigify_colors[2].standard_colors_lock = True
    arm.rigify_colors[3].name = "Tweak"
    arm.rigify_colors[3].active = Color((0.5490, 1.0000, 1.0000))
    arm.rigify_colors[3].normal = Color((0.0392, 0.2118, 0.5804))
    arm.rigify_colors[3].select = Color((0.3140, 0.7840, 1.0000))
    arm.rigify_colors[3].standard_colors_lock = True
    arm.rigify_colors[4].name = "FK"
    arm.rigify_colors[4].active = Color((0.5490, 1.0000, 1.0000))
    arm.rigify_colors[4].normal = Color((0.1176, 0.5686, 0.0353))
    arm.rigify_colors[4].select = Color((0.3140, 0.7840, 1.0000))
    arm.rigify_colors[4].standard_colors_lock = True
    arm.rigify_colors[5].name = "Extra"
    arm.rigify_colors[5].active = Color((0.5490, 1.0000, 1.0000))
    arm.rigify_colors[5].normal = Color((0.9686, 0.2510, 0.0941))
    arm.rigify_colors[5].select = Color((0.3140, 0.7840, 1.0000))
    arm.rigify_colors[5].standard_colors_lock = True

    bone_collections = {}

    for bcoll in list(arm.collections_all):
        arm.collections.remove(bcoll)

    def add_bone_collection(name, *, ui_row=0, ui_title='', sel_set=False, color_set_id=0):
        new_bcoll = arm.collections.new(name)
        new_bcoll.rigify_ui_row = ui_row
        new_bcoll.rigify_ui_title = ui_title
        new_bcoll.rigify_sel_set = sel_set
        new_bcoll.rigify_color_set_id = color_set_id
        bone_collections[name] = new_bcoll

    def assign_bone_collections(pose_bone, *coll_names):
        assert not len(pose_bone.bone.collections)
        for name in coll_names:
            bone_collections[name].assign(pose_bone)

    def assign_bone_collection_refs(params, attr_name, *coll_names):
        ref_list = getattr(params, attr_name + '_coll_refs', None)
        if ref_list is not None:
            for name in coll_names:
                ref_list.add().set_collection(bone_collections[name])

    add_bone_collection('Face', ui_row=1, color_set_id=5)
    add_bone_collection('Face (Primary)', ui_row=2, ui_title='(Primary)', color_set_id=4)
    add_bone_collection('Spine', ui_row=4, color_set_id=3)
    add_bone_collection('Spine (Tweak)', ui_row=5, ui_title='(Tweak)', color_set_id=4)
    add_bone_collection('Arm.L (IK)', ui_row=7, color_set_id=2)
    add_bone_collection('Arm.L (FK)', ui_row=8, ui_title='(FK)', color_set_id=5)
    add_bone_collection('Arm.L (Tweak)', ui_row=9, ui_title='(Tweak)', color_set_id=4)
    add_bone_collection('Arm.R (IK)', ui_row=7, color_set_id=2)
    add_bone_collection('Arm.R (FK)', ui_row=8, ui_title='(FK)', color_set_id=5)
    add_bone_collection('Arm.R (Tweak)', ui_row=9, ui_title='(Tweak)', color_set_id=4)
    add_bone_collection('Leg.L (IK)', ui_row=11, color_set_id=2)
    add_bone_collection('Leg.L (FK)', ui_row=12, ui_title='(FK)', color_set_id=5)
    add_bone_collection('Leg.L (Tweak)', ui_row=13, ui_title='(Tweak)', color_set_id=4)
    add_bone_collection('Leg.R (IK)', ui_row=11, color_set_id=2)
    add_bone_collection('Leg.R (FK)', ui_row=12, ui_title='(FK)', color_set_id=5)
    add_bone_collection('Leg.R (Tweak)', ui_row=13, ui_title='(Tweak)', color_set_id=4)
    add_bone_collection('Tail', ui_row=15, color_set_id=6)
    add_bone_collection('Hair', ui_row=2, color_set_id=6)
    add_bone_collection('Root', ui_row=18, color_set_id=1)

    bones = {}

    bone = arm.edit_bones.new('spine.001')
    bone.head = 0.0000, 0.8059, 1.3950
    bone.tail = 0.0000, 0.3939, 1.3950
    bone.roll = 0.0000
    bone.use_connect = False
    bones['spine.001'] = bone.name
    bone = arm.edit_bones.new('tail.001')
    bone.head = -0.0000, 0.8970, 1.4526
    bone.tail = 0.0000, 1.0033, 1.4420
    bone.roll = 0.0000
    bone.use_connect = False
    bone.parent = arm.edit_bones[bones['spine.001']]
    bones['tail.001'] = bone.name
    bone = arm.edit_bones.new('spine.002')
    bone.head = 0.0000, 0.3939, 1.3950
    bone.tail = -0.0000, 0.2088, 1.3270
    bone.roll = 0.0000
    bone.use_connect = True
    bone.parent = arm.edit_bones[bones['spine.001']]
    bones['spine.002'] = bone.name
    bone = arm.edit_bones.new('tail.002')
    bone.head = 0.0000, 1.0033, 1.4420
    bone.tail = -0.0000, 1.1423, 1.3128
    bone.roll = -0.0000
    bone.use_connect = True
    bone.parent = arm.edit_bones[bones['tail.001']]
    bones['tail.002'] = bone.name
    bone = arm.edit_bones.new('spine.003')
    bone.head = -0.0000, 0.2088, 1.3270
    bone.tail = -0.0000, 0.0294, 1.2857
    bone.roll = 0.0000
    bone.use_connect = True
    bone.parent = arm.edit_bones[bones['spine.002']]
    bones['spine.003'] = bone.name
    bone = arm.edit_bones.new('pelvis.L')
    bone.head = -0.0000, 0.8059, 1.3950
    bone.tail = 0.1803, 0.4418, 1.5783
    bone.roll = 0.6304
    bone.use_connect = False
    bone.parent = arm.edit_bones[bones['spine.002']]
    bones['pelvis.L'] = bone.name
    bone = arm.edit_bones.new('pelvis.R')
    bone.head = 0.0000, 0.8059, 1.3950
    bone.tail = -0.1803, 0.4418, 1.5783
    bone.roll = -0.6304
    bone.use_connect = False
    bone.parent = arm.edit_bones[bones['spine.002']]
    bones['pelvis.R'] = bone.name
    bone = arm.edit_bones.new('hip')
    bone.head = -0.0000, 0.8059, 1.3950
    bone.tail = -0.0000, 0.4636, 1.0212
    bone.roll = 0.0000
    bone.use_connect = False
    bone.parent = arm.edit_bones[bones['spine.002']]
    bones['hip'] = bone.name
    bone = arm.edit_bones.new('tail.003')
    bone.head = -0.0000, 1.1423, 1.3128
    bone.tail = -0.0000, 1.3779, 1.1589
    bone.roll = -0.0000
    bone.use_connect = True
    bone.parent = arm.edit_bones[bones['tail.002']]
    bones['tail.003'] = bone.name
    bone = arm.edit_bones.new('spine.004')
    bone.head = -0.0000, 0.0294, 1.2857
    bone.tail = -0.0000, -0.1769, 1.2735
    bone.roll = 0.0000
    bone.use_connect = True
    bone.parent = arm.edit_bones[bones['spine.003']]
    bones['spine.004'] = bone.name
    bone = arm.edit_bones.new('abdomen')
    bone.head = -0.0000, 0.1503, 1.2207
    bone.tail = -0.0000, 0.2042, 0.9012
    bone.roll = 0.0000
    bone.use_connect = False
    bone.parent = arm.edit_bones[bones['spine.003']]
    bones['abdomen'] = bone.name
    bone = arm.edit_bones.new('thigh.L')
    bone.head = 0.1922, 0.6243, 1.4090
    bone.tail = 0.1933, 0.5569, 1.0335
    bone.roll = 3.1378
    bone.use_connect = False
    bone.parent = arm.edit_bones[bones['pelvis.L']]
    bones['thigh.L'] = bone.name
    bone = arm.edit_bones.new('thigh.R')
    bone.head = -0.1922, 0.6243, 1.4090
    bone.tail = -0.1933, 0.5569, 1.0335
    bone.roll = -3.1378
    bone.use_connect = False
    bone.parent = arm.edit_bones[bones['pelvis.R']]
    bones['thigh.R'] = bone.name
    bone = arm.edit_bones.new('tail.004')
    bone.head = -0.0000, 1.3779, 1.1589
    bone.tail = -0.0000, 1.5754, 1.1088
    bone.roll = -0.0000
    bone.use_connect = True
    bone.parent = arm.edit_bones[bones['tail.003']]
    bones['tail.004'] = bone.name
    bone = arm.edit_bones.new('spine.005')
    bone.head = -0.0000, -0.1769, 1.2735
    bone.tail = -0.0000, -0.3674, 1.2808
    bone.roll = 0.0000
    bone.use_connect = True
    bone.parent = arm.edit_bones[bones['spine.004']]
    bones['spine.005'] = bone.name
    bone = arm.edit_bones.new('lower_leg.L')
    bone.head = 0.1933, 0.5569, 1.0335
    bone.tail = 0.1933, 0.7635, 0.6074
    bone.roll = -3.1416
    bone.use_connect = True
    bone.parent = arm.edit_bones[bones['thigh.L']]
    bones['lower_leg.L'] = bone.name
    bone = arm.edit_bones.new('lower_leg.R')
    bone.head = -0.1933, 0.5569, 1.0335
    bone.tail = -0.1933, 0.7635, 0.6074
    bone.roll = 3.1416
    bone.use_connect = True
    bone.parent = arm.edit_bones[bones['thigh.R']]
    bones['lower_leg.R'] = bone.name
    bone = arm.edit_bones.new('tail.005')
    bone.head = -0.0000, 1.5754, 1.1088
    bone.tail = -0.0000, 1.7610, 1.1153
    bone.roll = -0.0000
    bone.use_connect = True
    bone.parent = arm.edit_bones[bones['tail.004']]
    bones['tail.005'] = bone.name
    bone = arm.edit_bones.new('spine.006')
    bone.head = -0.0000, -0.3674, 1.2808
    bone.tail = 0.0000, -0.7593, 1.3826
    bone.roll = 0.0000
    bone.use_connect = True
    bone.parent = arm.edit_bones[bones['spine.005']]
    bones['spine.006'] = bone.name
    bone = arm.edit_bones.new('shoulder.L')
    bone.head = 0.0936, -0.5035, 1.5783
    bone.tail = 0.1868, -0.6945, 1.1976
    bone.roll = -0.3119
    bone.use_connect = False
    bone.parent = arm.edit_bones[bones['spine.005']]
    bones['shoulder.L'] = bone.name
    bone = arm.edit_bones.new('breast.L')
    bone.head = 0.0905, -0.5541, 1.0931
    bone.tail = 0.0905, -0.8436, 0.9400
    bone.roll = 0.0000
    bone.use_connect = False
    bone.parent = arm.edit_bones[bones['spine.005']]
    bones['breast.L'] = bone.name
    bone = arm.edit_bones.new('shoulder.R')
    bone.head = -0.0936, -0.5035, 1.5783
    bone.tail = -0.1868, -0.6945, 1.1976
    bone.roll = 0.3119
    bone.use_connect = False
    bone.parent = arm.edit_bones[bones['spine.005']]
    bones['shoulder.R'] = bone.name
    bone = arm.edit_bones.new('breast.R')
    bone.head = -0.0905, -0.5541, 1.0931
    bone.tail = -0.0905, -0.8436, 0.9400
    bone.roll = -0.0000
    bone.use_connect = False
    bone.parent = arm.edit_bones[bones['spine.005']]
    bones['breast.R'] = bone.name
    bone = arm.edit_bones.new('chest')
    bone.head = -0.0000, -0.2180, 1.2173
    bone.tail = -0.0000, -0.2239, 0.8063
    bone.roll = 0.0000
    bone.use_connect = False
    bone.parent = arm.edit_bones[bones['spine.005']]
    bones['chest'] = bone.name
    bone = arm.edit_bones.new('hind_foot.L')
    bone.head = 0.1933, 0.7635, 0.6074
    bone.tail = 0.1933, 0.6900, 0.2011
    bone.roll = -3.1416
    bone.use_connect = True
    bone.parent = arm.edit_bones[bones['lower_leg.L']]
    bones['hind_foot.L'] = bone.name
    bone = arm.edit_bones.new('hind_foot.R')
    bone.head = -0.1933, 0.7635, 0.6074
    bone.tail = -0.1933, 0.6900, 0.2011
    bone.roll = 3.1416
    bone.use_connect = True
    bone.parent = arm.edit_bones[bones['lower_leg.R']]
    bones['hind_foot.R'] = bone.name
    bone = arm.edit_bones.new('neck.001')
    bone.head = 0.0000, -0.7593, 1.3826
    bone.tail = 0.0000, -0.9004, 1.5475
    bone.roll = 0.0000
    bone.use_connect = False
    bone.parent = arm.edit_bones[bones['spine.006']]
    bones['neck.001'] = bone.name
    bone = arm.edit_bones.new('mane_base.05')
    bone.head = -0.0000, -0.6120, 1.6888
    bone.tail = -0.0000, -0.5782, 1.7371
    bone.roll = 0.0000
    bone.use_connect = False
    bone.parent = arm.edit_bones[bones['spine.006']]
    bones['mane_base.05'] = bone.name
    bone = arm.edit_bones.new('upper_arm.L')
    bone.head = 0.1639, -0.6896, 1.1597
    bone.tail = 0.1639, -0.5323, 0.8412
    bone.roll = -3.1416
    bone.use_connect = False
    bone.parent = arm.edit_bones[bones['shoulder.L']]
    bones['upper_arm.L'] = bone.name
    bone = arm.edit_bones.new('upper_arm.R')
    bone.head = -0.1639, -0.6896, 1.1597
    bone.tail = -0.1639, -0.5323, 0.8412
    bone.roll = 3.1416
    bone.use_connect = False
    bone.parent = arm.edit_bones[bones['shoulder.R']]
    bones['upper_arm.R'] = bone.name
    bone = arm.edit_bones.new('r_toe.L')
    bone.head = 0.1933, 0.6900, 0.2011
    bone.tail = 0.1933, 0.6341, 0.1052
    bone.roll = 3.1416
    bone.use_connect = True
    bone.parent = arm.edit_bones[bones['hind_foot.L']]
    bones['r_toe.L'] = bone.name
    bone = arm.edit_bones.new('r_toe.R')
    bone.head = -0.1933, 0.6900, 0.2011
    bone.tail = -0.1933, 0.6341, 0.1052
    bone.roll = -3.1416
    bone.use_connect = True
    bone.parent = arm.edit_bones[bones['hind_foot.R']]
    bones['r_toe.R'] = bone.name
    bone = arm.edit_bones.new('neck.002')
    bone.head = 0.0000, -0.9004, 1.5475
    bone.tail = 0.0000, -1.0348, 1.7032
    bone.roll = 0.0000
    bone.use_connect = True
    bone.parent = arm.edit_bones[bones['neck.001']]
    bones['neck.002'] = bone.name
    bone = arm.edit_bones.new('mane_base.04')
    bone.head = 0.0000, -0.7885, 1.7610
    bone.tail = -0.0000, -0.7366, 1.8394
    bone.roll = 0.0000
    bone.use_connect = False
    bone.parent = arm.edit_bones[bones['neck.001']]
    bones['mane_base.04'] = bone.name
    bone = arm.edit_bones.new('mane_top.05')
    bone.head = -0.0000, -0.5782, 1.7371
    bone.tail = -0.0000, -0.5444, 1.7855
    bone.roll = 0.0000
    bone.use_connect = True
    bone.parent = arm.edit_bones[bones['mane_base.05']]
    bones['mane_top.05'] = bone.name
    bone = arm.edit_bones.new('forearm.L')
    bone.head = 0.1639, -0.5323, 0.8412
    bone.tail = 0.1639, -0.5386, 0.4807
    bone.roll = -3.1416
    bone.use_connect = True
    bone.parent = arm.edit_bones[bones['upper_arm.L']]
    bones['forearm.L'] = bone.name
    bone = arm.edit_bones.new('forearm.R')
    bone.head = -0.1639, -0.5323, 0.8412
    bone.tail = -0.1639, -0.5386, 0.4807
    bone.roll = 3.1416
    bone.use_connect = True
    bone.parent = arm.edit_bones[bones['upper_arm.R']]
    bones['forearm.R'] = bone.name
    bone = arm.edit_bones.new('r_hoof.L')
    bone.head = 0.1933, 0.6341, 0.1052
    bone.tail = 0.1933, 0.5916, 0.0007
    bone.roll = -3.1416
    bone.use_connect = True
    bone.parent = arm.edit_bones[bones['r_toe.L']]
    bones['r_hoof.L'] = bone.name
    bone = arm.edit_bones.new('r_hoof.R')
    bone.head = -0.1933, 0.6341, 0.1052
    bone.tail = -0.1933, 0.5916, 0.0007
    bone.roll = 3.1416
    bone.use_connect = True
    bone.parent = arm.edit_bones[bones['r_toe.R']]
    bones['r_hoof.R'] = bone.name
    bone = arm.edit_bones.new('neck.003')
    bone.head = 0.0000, -1.0348, 1.7032
    bone.tail = 0.0000, -1.1618, 1.7694
    bone.roll = -0.0000
    bone.use_connect = True
    bone.parent = arm.edit_bones[bones['neck.002']]
    bones['neck.003'] = bone.name
    bone = arm.edit_bones.new('mane_base.03')
    bone.head = 0.0000, -0.9627, 1.8513
    bone.tail = 0.0000, -0.9211, 1.9357
    bone.roll = 0.0000
    bone.use_connect = False
    bone.parent = arm.edit_bones[bones['neck.002']]
    bones['mane_base.03'] = bone.name
    bone = arm.edit_bones.new('mane_top.04')
    bone.head = -0.0000, -0.7366, 1.8394
    bone.tail = -0.0000, -0.6847, 1.9178
    bone.roll = 0.0000
    bone.use_connect = True
    bone.parent = arm.edit_bones[bones['mane_base.04']]
    bones['mane_top.04'] = bone.name
    bone = arm.edit_bones.new('forefoot.L')
    bone.head = 0.1639, -0.5386, 0.4807
    bone.tail = 0.1639, -0.5158, 0.2212
    bone.roll = -3.1416
    bone.use_connect = True
    bone.parent = arm.edit_bones[bones['forearm.L']]
    bones['forefoot.L'] = bone.name
    bone = arm.edit_bones.new('forefoot.R')
    bone.head = -0.1639, -0.5386, 0.4807
    bone.tail = -0.1639, -0.5158, 0.2212
    bone.roll = 3.1416
    bone.use_connect = True
    bone.parent = arm.edit_bones[bones['forearm.R']]
    bones['forefoot.R'] = bone.name
    bone = arm.edit_bones.new('neck.004')
    bone.head = 0.0000, -1.1618, 1.7694
    bone.tail = -0.0000, -1.2836, 1.7841
    bone.roll = -0.0000
    bone.use_connect = True
    bone.parent = arm.edit_bones[bones['neck.003']]
    bones['neck.004'] = bone.name
    bone = arm.edit_bones.new('mane_base.02')
    bone.head = 0.0000, -1.1437, 1.9124
    bone.tail = 0.0000, -1.1217, 2.0038
    bone.roll = 0.0000
    bone.use_connect = False
    bone.parent = arm.edit_bones[bones['neck.003']]
    bones['mane_base.02'] = bone.name
    bone = arm.edit_bones.new('mane_top.03')
    bone.head = 0.0000, -0.9211, 1.9357
    bone.tail = -0.0000, -0.8795, 2.0200
    bone.roll = 0.0000
    bone.use_connect = True
    bone.parent = arm.edit_bones[bones['mane_base.03']]
    bones['mane_top.03'] = bone.name
    bone = arm.edit_bones.new('f_toe.L')
    bone.head = 0.1639, -0.5158, 0.2212
    bone.tail = 0.1639, -0.5722, 0.0941
    bone.roll = 3.1416
    bone.use_connect = True
    bone.parent = arm.edit_bones[bones['forefoot.L']]
    bones['f_toe.L'] = bone.name
    bone = arm.edit_bones.new('f_toe.R')
    bone.head = -0.1639, -0.5158, 0.2212
    bone.tail = -0.1639, -0.5722, 0.0941
    bone.roll = -3.1416
    bone.use_connect = True
    bone.parent = arm.edit_bones[bones['forefoot.R']]
    bones['f_toe.R'] = bone.name
    bone = arm.edit_bones.new('head')
    bone.head = -0.0000, -1.2836, 1.7841
    bone.tail = -0.0000, -1.6414, 1.7070
    bone.roll = 0.0000
    bone.use_connect = True
    bone.parent = arm.edit_bones[bones['neck.004']]
    bones['head'] = bone.name
    bone = arm.edit_bones.new('mane_base.01')
    bone.head = 0.0000, -1.3074, 1.9345
    bone.tail = 0.0000, -1.3182, 2.0279
    bone.roll = 0.0000
    bone.use_connect = False
    bone.parent = arm.edit_bones[bones['neck.004']]
    bones['mane_base.01'] = bone.name
    bone = arm.edit_bones.new('mane_base.06')
    bone.head = 0.0000, -1.4117, 1.9184
    bone.tail = 0.0000, -1.4566, 1.9477
    bone.roll = 0.0000
    bone.use_connect = False
    bone.parent = arm.edit_bones[bones['neck.004']]
    bones['mane_base.06'] = bone.name
    bone = arm.edit_bones.new('mane_top.02')
    bone.head = 0.0000, -1.1217, 2.0038
    bone.tail = 0.0000, -1.0996, 2.0953
    bone.roll = 0.0000
    bone.use_connect = True
    bone.parent = arm.edit_bones[bones['mane_base.02']]
    bones['mane_top.02'] = bone.name
    bone = arm.edit_bones.new('f_hoof.L')
    bone.head = 0.1639, -0.5722, 0.0941
    bone.tail = 0.1639, -0.6384, 0.0007
    bone.roll = -3.1416
    bone.use_connect = True
    bone.parent = arm.edit_bones[bones['f_toe.L']]
    bones['f_hoof.L'] = bone.name
    bone = arm.edit_bones.new('f_hoof.R')
    bone.head = -0.1639, -0.5722, 0.0941
    bone.tail = -0.1639, -0.6384, 0.0007
    bone.roll = 3.1416
    bone.use_connect = True
    bone.parent = arm.edit_bones[bones['f_toe.R']]
    bones['f_hoof.R'] = bone.name
    bone = arm.edit_bones.new('skull')
    bone.head = -0.0000, -1.3014, 1.8284
    bone.tail = 0.0000, -1.6749, 1.3100
    bone.roll = 0.0000
    bone.use_connect = False
    bone.parent = arm.edit_bones[bones['head']]
    bones['skull'] = bone.name
    bone = arm.edit_bones.new('ear.L')
    bone.head = 0.0664, -1.3623, 1.8612
    bone.tail = 0.1056, -1.4118, 1.9537
    bone.roll = 0.6751
    bone.use_connect = False
    bone.parent = arm.edit_bones[bones['head']]
    bones['ear.L'] = bone.name
    bone = arm.edit_bones.new('ear.R')
    bone.head = -0.0664, -1.3623, 1.8612
    bone.tail = -0.1056, -1.4118, 1.9537
    bone.roll = -0.6751
    bone.use_connect = False
    bone.parent = arm.edit_bones[bones['head']]
    bones['ear.R'] = bone.name
    bone = arm.edit_bones.new('jaw')
    bone.head = 0.0000, -1.3507, 1.5819
    bone.tail = 0.0000, -1.4799, 1.4569
    bone.roll = 0.0000
    bone.use_connect = False
    bone.parent = arm.edit_bones[bones['head']]
    bones['jaw'] = bone.name
    bone = arm.edit_bones.new('eye.L')
    bone.head = 0.0988, -1.4596, 1.7351
    bone.tail = 0.1990, -1.4668, 1.7420
    bone.roll = 0.0000
    bone.use_connect = False
    bone.parent = arm.edit_bones[bones['head']]
    bones['eye.L'] = bone.name
    bone = arm.edit_bones.new('nose.L')
    bone.head = 0.0450, -1.6240, 1.4228
    bone.tail = 0.1039, -1.6613, 1.4269
    bone.roll = 0.0000
    bone.use_connect = False
    bone.parent = arm.edit_bones[bones['head']]
    bones['nose.L'] = bone.name
    bone = arm.edit_bones.new('eye.R')
    bone.head = -0.0988, -1.4596, 1.7351
    bone.tail = -0.1990, -1.4668, 1.7420
    bone.roll = -0.0000
    bone.use_connect = False
    bone.parent = arm.edit_bones[bones['head']]
    bones['eye.R'] = bone.name
    bone = arm.edit_bones.new('nose.R')
    bone.head = -0.0450, -1.6240, 1.4228
    bone.tail = -0.1039, -1.6613, 1.4269
    bone.roll = -0.0000
    bone.use_connect = False
    bone.parent = arm.edit_bones[bones['head']]
    bones['nose.R'] = bone.name
    bone = arm.edit_bones.new('mane_top.01')
    bone.head = 0.0000, -1.3182, 2.0279
    bone.tail = 0.0000, -1.3290, 2.1213
    bone.roll = 0.0000
    bone.use_connect = True
    bone.parent = arm.edit_bones[bones['mane_base.01']]
    bones['mane_top.01'] = bone.name
    bone = arm.edit_bones.new('mane_top.06')
    bone.head = 0.0000, -1.4566, 1.9477
    bone.tail = -0.0000, -1.5014, 1.9770
    bone.roll = 0.0000
    bone.use_connect = True
    bone.parent = arm.edit_bones[bones['mane_base.06']]
    bones['mane_top.06'] = bone.name
    bone = arm.edit_bones.new('skull.L')
    bone.head = 0.0000, -1.3014, 1.8284
    bone.tail = 0.1564, -1.4143, 1.5755
    bone.roll = -0.7698
    bone.use_connect = False
    bone.parent = arm.edit_bones[bones['skull']]
    bones['skull.L'] = bone.name
    bone = arm.edit_bones.new('skull.R')
    bone.head = -0.0000, -1.3014, 1.8284
    bone.tail = -0.1564, -1.4143, 1.5755
    bone.roll = 0.7698
    bone.use_connect = False
    bone.parent = arm.edit_bones[bones['skull']]
    bones['skull.R'] = bone.name
    bone = arm.edit_bones.new('ear.L.001')
    bone.head = 0.1056, -1.4118, 1.9537
    bone.tail = 0.1448, -1.4613, 2.0462
    bone.roll = 0.6751
    bone.use_connect = True
    bone.parent = arm.edit_bones[bones['ear.L']]
    bones['ear.L.001'] = bone.name
    bone = arm.edit_bones.new('ear.R.001')
    bone.head = -0.1056, -1.4118, 1.9537
    bone.tail = -0.1448, -1.4613, 2.0462
    bone.roll = -0.6751
    bone.use_connect = True
    bone.parent = arm.edit_bones[bones['ear.R']]
    bones['ear.R.001'] = bone.name
    bone = arm.edit_bones.new('jaw.001')
    bone.head = 0.0000, -1.4799, 1.4569
    bone.tail = 0.0000, -1.5599, 1.3210
    bone.roll = 0.0000
    bone.use_connect = True
    bone.parent = arm.edit_bones[bones['jaw']]
    bones['jaw.001'] = bone.name

    bpy.ops.object.mode_set(mode='OBJECT')
    pbone = obj.pose.bones[bones['spine.001']]
    pbone.rigify_type = 'spines.basic_spine'
    pbone.lock_location = (False, False, False)
    pbone.lock_rotation = (False, False, False)
    pbone.lock_rotation_w = False
    pbone.lock_scale = (False, False, False)
    pbone.rotation_mode = 'QUATERNION'
    assign_bone_collections(pbone, 'Spine')
    try:
        pbone.rigify_parameters.pivot_pos = 2
    except AttributeError:
        pass
    assign_bone_collection_refs(pbone.rigify_parameters, 'tweak', 'Spine (Tweak)')
    assign_bone_collection_refs(pbone.rigify_parameters, 'fk', 'Spine (Tweak)')
    pbone = obj.pose.bones[bones['tail.001']]
    pbone.rigify_type = 'spines.basic_tail'
    pbone.lock_location = (False, False, False)
    pbone.lock_rotation = (False, False, False)
    pbone.lock_rotation_w = False
    pbone.lock_scale = (False, False, False)
    pbone.rotation_mode = 'QUATERNION'
    assign_bone_collections(pbone, 'Tail')
    try:
        pbone.rigify_parameters.copy_rotation_axes = (True, False, True)
    except AttributeError:
        pass
    try:
        pbone.rigify_parameters.connect_chain = False
    except AttributeError:
        pass
    assign_bone_collection_refs(pbone.rigify_parameters, 'tweak', 'Spine (Tweak)')
    pbone = obj.pose.bones[bones['spine.002']]
    pbone.rigify_type = ''
    pbone.lock_location = (False, False, False)
    pbone.lock_rotation = (False, False, False)
    pbone.lock_rotation_w = False
    pbone.lock_scale = (False, False, False)
    pbone.rotation_mode = 'QUATERNION'
    assign_bone_collections(pbone, 'Spine')
    pbone = obj.pose.bones[bones['tail.002']]
    pbone.rigify_type = ''
    pbone.lock_location = (False, False, False)
    pbone.lock_rotation = (False, False, False)
    pbone.lock_rotation_w = False
    pbone.lock_scale = (False, False, False)
    pbone.rotation_mode = 'QUATERNION'
    assign_bone_collections(pbone, 'Tail')
    pbone = obj.pose.bones[bones['spine.003']]
    pbone.rigify_type = ''
    pbone.lock_location = (False, False, False)
    pbone.lock_rotation = (False, False, False)
    pbone.lock_rotation_w = False
    pbone.lock_scale = (False, False, False)
    pbone.rotation_mode = 'QUATERNION'
    assign_bone_collections(pbone, 'Spine')
    pbone = obj.pose.bones[bones['pelvis.L']]
    pbone.rigify_type = 'basic.super_copy'
    pbone.lock_location = (False, False, False)
    pbone.lock_rotation = (False, False, False)
    pbone.lock_rotation_w = False
    pbone.lock_scale = (False, False, False)
    pbone.rotation_mode = 'QUATERNION'
    assign_bone_collections(pbone, 'Spine')
    try:
        pbone.rigify_parameters.make_widget = False
    except AttributeError:
        pass
    try:
        pbone.rigify_parameters.make_control = False
    except AttributeError:
        pass
    pbone = obj.pose.bones[bones['pelvis.R']]
    pbone.rigify_type = 'basic.super_copy'
    pbone.lock_location = (False, False, False)
    pbone.lock_rotation = (False, False, False)
    pbone.lock_rotation_w = False
    pbone.lock_scale = (False, False, False)
    pbone.rotation_mode = 'QUATERNION'
    assign_bone_collections(pbone, 'Spine')
    try:
        pbone.rigify_parameters.make_widget = False
    except AttributeError:
        pass
    try:
        pbone.rigify_parameters.make_control = False
    except AttributeError:
        pass
    pbone = obj.pose.bones[bones['hip']]
    pbone.rigify_type = ''
    pbone.lock_location = (False, False, False)
    pbone.lock_rotation = (False, False, False)
    pbone.lock_rotation_w = False
    pbone.lock_scale = (False, False, False)
    pbone.rotation_mode = 'QUATERNION'
    assign_bone_collections(pbone, 'Spine')
    pbone = obj.pose.bones[bones['tail.003']]
    pbone.rigify_type = ''
    pbone.lock_location = (False, False, False)
    pbone.lock_rotation = (False, False, False)
    pbone.lock_rotation_w = False
    pbone.lock_scale = (False, False, False)
    pbone.rotation_mode = 'QUATERNION'
    assign_bone_collections(pbone, 'Tail')
    pbone = obj.pose.bones[bones['spine.004']]
    pbone.rigify_type = ''
    pbone.lock_location = (False, False, False)
    pbone.lock_rotation = (False, False, False)
    pbone.lock_rotation_w = False
    pbone.lock_scale = (False, False, False)
    pbone.rotation_mode = 'QUATERNION'
    assign_bone_collections(pbone, 'Spine')
    pbone = obj.pose.bones[bones['abdomen']]
    pbone.rigify_type = ''
    pbone.lock_location = (False, False, False)
    pbone.lock_rotation = (False, False, False)
    pbone.lock_rotation_w = False
    pbone.lock_scale = (False, False, False)
    pbone.rotation_mode = 'QUATERNION'
    assign_bone_collections(pbone, 'Spine (Tweak)')
    pbone = obj.pose.bones[bones['thigh.L']]
    pbone.rigify_type = 'limbs.rear_paw'
    pbone.lock_location = (False, False, False)
    pbone.lock_rotation = (False, False, False)
    pbone.lock_rotation_w = False
    pbone.lock_scale = (False, False, False)
    pbone.rotation_mode = 'QUATERNION'
    assign_bone_collections(pbone, 'Leg.L (IK)')
    try:
        pbone.rigify_parameters.segments = 2
    except AttributeError:
        pass
    try:
        pbone.rigify_parameters.ik_local_location = False
    except AttributeError:
        pass
    try:
        pbone.rigify_parameters.bbones = 10
    except AttributeError:
        pass
    try:
        pbone.rigify_parameters.fk_layers_extra = True
    except AttributeError:
        pass
    try:
        pbone.rigify_parameters.tweak_layers_extra = True
    except AttributeError:
        pass
    assign_bone_collection_refs(pbone.rigify_parameters, 'fk', 'Leg.L (FK)')
    assign_bone_collection_refs(pbone.rigify_parameters, 'tweak', 'Leg.L (Tweak)')
    pbone = obj.pose.bones[bones['thigh.R']]
    pbone.rigify_type = 'limbs.rear_paw'
    pbone.lock_location = (False, False, False)
    pbone.lock_rotation = (False, False, False)
    pbone.lock_rotation_w = False
    pbone.lock_scale = (False, False, False)
    pbone.rotation_mode = 'QUATERNION'
    assign_bone_collections(pbone, 'Leg.R (IK)')
    try:
        pbone.rigify_parameters.segments = 2
    except AttributeError:
        pass
    try:
        pbone.rigify_parameters.ik_local_location = False
    except AttributeError:
        pass
    try:
        pbone.rigify_parameters.bbones = 10
    except AttributeError:
        pass
    try:
        pbone.rigify_parameters.fk_layers_extra = True
    except AttributeError:
        pass
    try:
        pbone.rigify_parameters.tweak_layers_extra = True
    except AttributeError:
        pass
    assign_bone_collection_refs(pbone.rigify_parameters, 'fk', 'Leg.R (FK)')
    assign_bone_collection_refs(pbone.rigify_parameters, 'tweak', 'Leg.R (Tweak)')
    pbone = obj.pose.bones[bones['tail.004']]
    pbone.rigify_type = ''
    pbone.lock_location = (False, False, False)
    pbone.lock_rotation = (False, False, False)
    pbone.lock_rotation_w = False
    pbone.lock_scale = (False, False, False)
    pbone.rotation_mode = 'QUATERNION'
    assign_bone_collections(pbone, 'Tail')
    pbone = obj.pose.bones[bones['spine.005']]
    pbone.rigify_type = ''
    pbone.lock_location = (False, False, False)
    pbone.lock_rotation = (False, False, False)
    pbone.lock_rotation_w = False
    pbone.lock_scale = (False, False, False)
    pbone.rotation_mode = 'QUATERNION'
    assign_bone_collections(pbone, 'Spine')
    pbone = obj.pose.bones[bones['lower_leg.L']]
    pbone.rigify_type = ''
    pbone.lock_location = (False, False, False)
    pbone.lock_rotation = (False, False, False)
    pbone.lock_rotation_w = False
    pbone.lock_scale = (False, False, False)
    pbone.rotation_mode = 'QUATERNION'
    assign_bone_collections(pbone, 'Leg.L (IK)')
    pbone = obj.pose.bones[bones['lower_leg.R']]
    pbone.rigify_type = ''
    pbone.lock_location = (False, False, False)
    pbone.lock_rotation = (False, False, False)
    pbone.lock_rotation_w = False
    pbone.lock_scale = (False, False, False)
    pbone.rotation_mode = 'QUATERNION'
    assign_bone_collections(pbone, 'Leg.R (IK)')
    pbone = obj.pose.bones[bones['tail.005']]
    pbone.rigify_type = ''
    pbone.lock_location = (False, False, False)
    pbone.lock_rotation = (False, False, False)
    pbone.lock_rotation_w = False
    pbone.lock_scale = (False, False, False)
    pbone.rotation_mode = 'QUATERNION'
    assign_bone_collections(pbone, 'Tail')
    pbone = obj.pose.bones[bones['spine.006']]
    pbone.rigify_type = ''
    pbone.lock_location = (False, False, False)
    pbone.lock_rotation = (False, False, False)
    pbone.lock_rotation_w = False
    pbone.lock_scale = (False, False, False)
    pbone.rotation_mode = 'QUATERNION'
    assign_bone_collections(pbone, 'Spine')
    pbone = obj.pose.bones[bones['shoulder.L']]
    pbone.rigify_type = 'basic.super_copy'
    pbone.lock_location = (False, False, False)
    pbone.lock_rotation = (False, False, False)
    pbone.lock_rotation_w = False
    pbone.lock_scale = (False, False, False)
    pbone.rotation_mode = 'QUATERNION'
    assign_bone_collections(pbone, 'Arm.L (IK)')
    try:
        pbone.rigify_parameters.make_widget = False
    except AttributeError:
        pass
    pbone = obj.pose.bones[bones['breast.L']]
    pbone.rigify_type = 'basic.super_copy'
    pbone.lock_location = (False, False, False)
    pbone.lock_rotation = (False, False, False)
    pbone.lock_rotation_w = False
    pbone.lock_scale = (False, False, False)
    pbone.rotation_mode = 'QUATERNION'
    assign_bone_collections(pbone, 'Spine (Tweak)')
    pbone = obj.pose.bones[bones['shoulder.R']]
    pbone.rigify_type = 'basic.super_copy'
    pbone.lock_location = (False, False, False)
    pbone.lock_rotation = (False, False, False)
    pbone.lock_rotation_w = False
    pbone.lock_scale = (False, False, False)
    pbone.rotation_mode = 'QUATERNION'
    assign_bone_collections(pbone, 'Arm.R (IK)')
    try:
        pbone.rigify_parameters.make_widget = False
    except AttributeError:
        pass
    pbone = obj.pose.bones[bones['breast.R']]
    pbone.rigify_type = 'basic.super_copy'
    pbone.lock_location = (False, False, False)
    pbone.lock_rotation = (False, False, False)
    pbone.lock_rotation_w = False
    pbone.lock_scale = (False, False, False)
    pbone.rotation_mode = 'QUATERNION'
    assign_bone_collections(pbone, 'Spine (Tweak)')
    pbone = obj.pose.bones[bones['chest']]
    pbone.rigify_type = 'basic.super_copy'
    pbone.lock_location = (False, False, False)
    pbone.lock_rotation = (False, False, False)
    pbone.lock_rotation_w = False
    pbone.lock_scale = (False, False, False)
    pbone.rotation_mode = 'QUATERNION'
    assign_bone_collections(pbone, 'Spine (Tweak)')
    pbone = obj.pose.bones[bones['hind_foot.L']]
    pbone.rigify_type = ''
    pbone.lock_location = (False, False, False)
    pbone.lock_rotation = (False, False, False)
    pbone.lock_rotation_w = False
    pbone.lock_scale = (False, False, False)
    pbone.rotation_mode = 'QUATERNION'
    assign_bone_collections(pbone, 'Leg.L (IK)')
    pbone = obj.pose.bones[bones['hind_foot.R']]
    pbone.rigify_type = ''
    pbone.lock_location = (False, False, False)
    pbone.lock_rotation = (False, False, False)
    pbone.lock_rotation_w = False
    pbone.lock_scale = (False, False, False)
    pbone.rotation_mode = 'QUATERNION'
    assign_bone_collections(pbone, 'Leg.R (IK)')
    pbone = obj.pose.bones[bones['neck.001']]
    pbone.rigify_type = 'spines.super_head'
    pbone.lock_location = (False, False, False)
    pbone.lock_rotation = (False, False, False)
    pbone.lock_rotation_w = False
    pbone.lock_scale = (False, False, False)
    pbone.rotation_mode = 'QUATERNION'
    assign_bone_collections(pbone, 'Spine')
    try:
        pbone.rigify_parameters.connect_chain = True
    except AttributeError:
        pass
    assign_bone_collection_refs(pbone.rigify_parameters, 'tweak', 'Spine (Tweak)')
    pbone = obj.pose.bones[bones['mane_base.05']]
    pbone.rigify_type = 'limbs.super_finger'
    pbone.lock_location = (False, False, False)
    pbone.lock_rotation = (False, False, False)
    pbone.lock_rotation_w = False
    pbone.lock_scale = (False, False, False)
    pbone.rotation_mode = 'QUATERNION'
    assign_bone_collections(pbone, 'Hair')
    try:
        pbone.rigify_parameters.copy_rotation_axes = (True, False, True)
    except AttributeError:
        pass
    try:
        pbone.rigify_parameters.primary_rotation_axis = 'Z'
    except AttributeError:
        pass
    try:
        pbone.rigify_parameters.tweak_layers_extra = False
    except AttributeError:
        pass
    pbone = obj.pose.bones[bones['upper_arm.L']]
    pbone.rigify_type = 'limbs.front_paw'
    pbone.lock_location = (False, False, False)
    pbone.lock_rotation = (False, False, False)
    pbone.lock_rotation_w = False
    pbone.lock_scale = (False, False, False)
    pbone.rotation_mode = 'QUATERNION'
    assign_bone_collections(pbone, 'Arm.L (IK)')
    try:
        pbone.rigify_parameters.limb_type = 'paw'
    except AttributeError:
        pass
    try:
        pbone.rigify_parameters.ik_local_location = False
    except AttributeError:
        pass
    try:
        pbone.rigify_parameters.front_paw_heel_influence = 0.6000000238418579
    except AttributeError:
        pass
    assign_bone_collection_refs(pbone.rigify_parameters, 'fk', 'Arm.L (FK)')
    assign_bone_collection_refs(pbone.rigify_parameters, 'tweak', 'Arm.L (Tweak)')
    pbone = obj.pose.bones[bones['upper_arm.R']]
    pbone.rigify_type = 'limbs.front_paw'
    pbone.lock_location = (False, False, False)
    pbone.lock_rotation = (False, False, False)
    pbone.lock_rotation_w = False
    pbone.lock_scale = (False, False, False)
    pbone.rotation_mode = 'QUATERNION'
    assign_bone_collections(pbone, 'Arm.R (IK)')
    try:
        pbone.rigify_parameters.limb_type = 'paw'
    except AttributeError:
        pass
    try:
        pbone.rigify_parameters.ik_local_location = False
    except AttributeError:
        pass
    try:
        pbone.rigify_parameters.front_paw_heel_influence = 0.6000000238418579
    except AttributeError:
        pass
    assign_bone_collection_refs(pbone.rigify_parameters, 'fk', 'Arm.R (FK)')
    assign_bone_collection_refs(pbone.rigify_parameters, 'tweak', 'Arm.R (Tweak)')
    pbone = obj.pose.bones[bones['r_toe.L']]
    pbone.rigify_type = ''
    pbone.lock_location = (False, False, False)
    pbone.lock_rotation = (False, False, False)
    pbone.lock_rotation_w = False
    pbone.lock_scale = (False, False, False)
    pbone.rotation_mode = 'QUATERNION'
    assign_bone_collections(pbone, 'Leg.L (IK)')
    pbone = obj.pose.bones[bones['r_toe.R']]
    pbone.rigify_type = ''
    pbone.lock_location = (False, False, False)
    pbone.lock_rotation = (False, False, False)
    pbone.lock_rotation_w = False
    pbone.lock_scale = (False, False, False)
    pbone.rotation_mode = 'QUATERNION'
    assign_bone_collections(pbone, 'Leg.R (IK)')
    pbone = obj.pose.bones[bones['neck.002']]
    pbone.rigify_type = ''
    pbone.lock_location = (False, False, False)
    pbone.lock_rotation = (False, False, False)
    pbone.lock_rotation_w = False
    pbone.lock_scale = (False, False, False)
    pbone.rotation_mode = 'QUATERNION'
    assign_bone_collections(pbone, 'Spine')
    pbone = obj.pose.bones[bones['mane_base.04']]
    pbone.rigify_type = 'limbs.super_finger'
    pbone.lock_location = (False, False, False)
    pbone.lock_rotation = (False, False, False)
    pbone.lock_rotation_w = False
    pbone.lock_scale = (False, False, False)
    pbone.rotation_mode = 'QUATERNION'
    assign_bone_collections(pbone, 'Hair')
    try:
        pbone.rigify_parameters.copy_rotation_axes = (True, False, True)
    except AttributeError:
        pass
    try:
        pbone.rigify_parameters.primary_rotation_axis = 'Z'
    except AttributeError:
        pass
    try:
        pbone.rigify_parameters.tweak_layers_extra = False
    except AttributeError:
        pass
    pbone = obj.pose.bones[bones['mane_top.05']]
    pbone.rigify_type = ''
    pbone.lock_location = (False, False, False)
    pbone.lock_rotation = (False, False, False)
    pbone.lock_rotation_w = False
    pbone.lock_scale = (False, False, False)
    pbone.rotation_mode = 'QUATERNION'
    assign_bone_collections(pbone, 'Hair')
    pbone = obj.pose.bones[bones['forearm.L']]
    pbone.rigify_type = ''
    pbone.lock_location = (False, False, False)
    pbone.lock_rotation = (False, False, False)
    pbone.lock_rotation_w = False
    pbone.lock_scale = (False, False, False)
    pbone.rotation_mode = 'QUATERNION'
    assign_bone_collections(pbone, 'Arm.L (IK)')
    pbone = obj.pose.bones[bones['forearm.R']]
    pbone.rigify_type = ''
    pbone.lock_location = (False, False, False)
    pbone.lock_rotation = (False, False, False)
    pbone.lock_rotation_w = False
    pbone.lock_scale = (False, False, False)
    pbone.rotation_mode = 'QUATERNION'
    assign_bone_collections(pbone, 'Arm.R (IK)')
    pbone = obj.pose.bones[bones['r_hoof.L']]
    pbone.rigify_type = ''
    pbone.lock_location = (False, False, False)
    pbone.lock_rotation = (False, False, False)
    pbone.lock_rotation_w = False
    pbone.lock_scale = (False, False, False)
    pbone.rotation_mode = 'QUATERNION'
    assign_bone_collections(pbone, 'Leg.L (IK)')
    pbone = obj.pose.bones[bones['r_hoof.R']]
    pbone.rigify_type = ''
    pbone.lock_location = (False, False, False)
    pbone.lock_rotation = (False, False, False)
    pbone.lock_rotation_w = False
    pbone.lock_scale = (False, False, False)
    pbone.rotation_mode = 'QUATERNION'
    assign_bone_collections(pbone, 'Leg.R (IK)')
    pbone = obj.pose.bones[bones['neck.003']]
    pbone.rigify_type = ''
    pbone.lock_location = (False, False, False)
    pbone.lock_rotation = (False, False, False)
    pbone.lock_rotation_w = False
    pbone.lock_scale = (False, False, False)
    pbone.rotation_mode = 'QUATERNION'
    assign_bone_collections(pbone, 'Spine')
    pbone = obj.pose.bones[bones['mane_base.03']]
    pbone.rigify_type = 'limbs.super_finger'
    pbone.lock_location = (False, False, False)
    pbone.lock_rotation = (False, False, False)
    pbone.lock_rotation_w = False
    pbone.lock_scale = (False, False, False)
    pbone.rotation_mode = 'QUATERNION'
    assign_bone_collections(pbone, 'Hair')
    try:
        pbone.rigify_parameters.copy_rotation_axes = (True, False, True)
    except AttributeError:
        pass
    try:
        pbone.rigify_parameters.primary_rotation_axis = 'Z'
    except AttributeError:
        pass
    try:
        pbone.rigify_parameters.tweak_layers_extra = False
    except AttributeError:
        pass
    pbone = obj.pose.bones[bones['mane_top.04']]
    pbone.rigify_type = ''
    pbone.lock_location = (False, False, False)
    pbone.lock_rotation = (False, False, False)
    pbone.lock_rotation_w = False
    pbone.lock_scale = (False, False, False)
    pbone.rotation_mode = 'QUATERNION'
    assign_bone_collections(pbone, 'Hair')
    pbone = obj.pose.bones[bones['forefoot.L']]
    pbone.rigify_type = ''
    pbone.lock_location = (False, False, False)
    pbone.lock_rotation = (False, False, False)
    pbone.lock_rotation_w = False
    pbone.lock_scale = (False, False, False)
    pbone.rotation_mode = 'QUATERNION'
    assign_bone_collections(pbone, 'Arm.L (IK)')
    pbone = obj.pose.bones[bones['forefoot.R']]
    pbone.rigify_type = ''
    pbone.lock_location = (False, False, False)
    pbone.lock_rotation = (False, False, False)
    pbone.lock_rotation_w = False
    pbone.lock_scale = (False, False, False)
    pbone.rotation_mode = 'QUATERNION'
    assign_bone_collections(pbone, 'Arm.R (IK)')
    pbone = obj.pose.bones[bones['neck.004']]
    pbone.rigify_type = ''
    pbone.lock_location = (False, False, False)
    pbone.lock_rotation = (False, False, False)
    pbone.lock_rotation_w = False
    pbone.lock_scale = (False, False, False)
    pbone.rotation_mode = 'QUATERNION'
    assign_bone_collections(pbone, 'Spine')
    pbone = obj.pose.bones[bones['mane_base.02']]
    pbone.rigify_type = 'limbs.super_finger'
    pbone.lock_location = (False, False, False)
    pbone.lock_rotation = (False, False, False)
    pbone.lock_rotation_w = False
    pbone.lock_scale = (False, False, False)
    pbone.rotation_mode = 'QUATERNION'
    assign_bone_collections(pbone, 'Hair')
    try:
        pbone.rigify_parameters.copy_rotation_axes = (True, False, True)
    except AttributeError:
        pass
    try:
        pbone.rigify_parameters.primary_rotation_axis = 'Z'
    except AttributeError:
        pass
    try:
        pbone.rigify_parameters.tweak_layers_extra = False
    except AttributeError:
        pass
    pbone = obj.pose.bones[bones['mane_top.03']]
    pbone.rigify_type = ''
    pbone.lock_location = (False, False, False)
    pbone.lock_rotation = (False, False, False)
    pbone.lock_rotation_w = False
    pbone.lock_scale = (False, False, False)
    pbone.rotation_mode = 'QUATERNION'
    assign_bone_collections(pbone, 'Hair')
    pbone = obj.pose.bones[bones['f_toe.L']]
    pbone.rigify_type = ''
    pbone.lock_location = (False, False, False)
    pbone.lock_rotation = (False, False, False)
    pbone.lock_rotation_w = False
    pbone.lock_scale = (False, False, False)
    pbone.rotation_mode = 'QUATERNION'
    assign_bone_collections(pbone, 'Arm.L (IK)')
    pbone = obj.pose.bones[bones['f_toe.R']]
    pbone.rigify_type = ''
    pbone.lock_location = (False, False, False)
    pbone.lock_rotation = (False, False, False)
    pbone.lock_rotation_w = False
    pbone.lock_scale = (False, False, False)
    pbone.rotation_mode = 'QUATERNION'
    assign_bone_collections(pbone, 'Arm.R (IK)')
    pbone = obj.pose.bones[bones['head']]
    pbone.rigify_type = ''
    pbone.lock_location = (False, False, False)
    pbone.lock_rotation = (False, False, False)
    pbone.lock_rotation_w = False
    pbone.lock_scale = (False, False, False)
    pbone.rotation_mode = 'QUATERNION'
    assign_bone_collections(pbone, 'Spine')
    pbone = obj.pose.bones[bones['mane_base.01']]
    pbone.rigify_type = 'limbs.super_finger'
    pbone.lock_location = (False, False, False)
    pbone.lock_rotation = (False, False, False)
    pbone.lock_rotation_w = False
    pbone.lock_scale = (False, False, False)
    pbone.rotation_mode = 'QUATERNION'
    assign_bone_collections(pbone, 'Hair')
    try:
        pbone.rigify_parameters.copy_rotation_axes = (True, False, True)
    except AttributeError:
        pass
    try:
        pbone.rigify_parameters.primary_rotation_axis = 'Z'
    except AttributeError:
        pass
    try:
        pbone.rigify_parameters.tweak_layers_extra = False
    except AttributeError:
        pass
    pbone = obj.pose.bones[bones['mane_base.06']]
    pbone.rigify_type = 'limbs.super_finger'
    pbone.lock_location = (False, False, False)
    pbone.lock_rotation = (False, False, False)
    pbone.lock_rotation_w = False
    pbone.lock_scale = (False, False, False)
    pbone.rotation_mode = 'QUATERNION'
    assign_bone_collections(pbone, 'Hair')
    try:
        pbone.rigify_parameters.copy_rotation_axes = (True, False, True)
    except AttributeError:
        pass
    try:
        pbone.rigify_parameters.primary_rotation_axis = 'Z'
    except AttributeError:
        pass
    try:
        pbone.rigify_parameters.tweak_layers_extra = False
    except AttributeError:
        pass
    pbone = obj.pose.bones[bones['mane_top.02']]
    pbone.rigify_type = ''
    pbone.lock_location = (False, False, False)
    pbone.lock_rotation = (False, False, False)
    pbone.lock_rotation_w = False
    pbone.lock_scale = (False, False, False)
    pbone.rotation_mode = 'QUATERNION'
    assign_bone_collections(pbone, 'Hair')
    pbone = obj.pose.bones[bones['f_hoof.L']]
    pbone.rigify_type = ''
    pbone.lock_location = (False, False, False)
    pbone.lock_rotation = (False, False, False)
    pbone.lock_rotation_w = False
    pbone.lock_scale = (False, False, False)
    pbone.rotation_mode = 'QUATERNION'
    assign_bone_collections(pbone, 'Arm.L (IK)')
    pbone = obj.pose.bones[bones['f_hoof.R']]
    pbone.rigify_type = ''
    pbone.lock_location = (False, False, False)
    pbone.lock_rotation = (False, False, False)
    pbone.lock_rotation_w = False
    pbone.lock_scale = (False, False, False)
    pbone.rotation_mode = 'QUATERNION'
    assign_bone_collections(pbone, 'Arm.R (IK)')
    pbone = obj.pose.bones[bones['skull']]
    pbone.rigify_type = 'basic.super_copy'
    pbone.lock_location = (False, False, False)
    pbone.lock_rotation = (False, False, False)
    pbone.lock_rotation_w = False
    pbone.lock_scale = (False, False, False)
    pbone.rotation_mode = 'QUATERNION'
    assign_bone_collections(pbone, 'Spine')
    try:
        pbone.rigify_parameters.make_widget = False
    except AttributeError:
        pass
    try:
        pbone.rigify_parameters.make_control = False
    except AttributeError:
        pass
    pbone = obj.pose.bones[bones['ear.L']]
    pbone.rigify_type = 'limbs.simple_tentacle'
    pbone.lock_location = (False, False, False)
    pbone.lock_rotation = (False, False, False)
    pbone.lock_rotation_w = False
    pbone.lock_scale = (False, False, False)
    pbone.rotation_mode = 'QUATERNION'
    assign_bone_collections(pbone, 'Face')
    try:
        pbone.rigify_parameters.copy_rotation_axes = (True, False, True)
    except AttributeError:
        pass
    assign_bone_collection_refs(pbone.rigify_parameters, 'tweak', 'Face (Primary)')
    pbone = obj.pose.bones[bones['ear.R']]
    pbone.rigify_type = 'limbs.simple_tentacle'
    pbone.lock_location = (False, False, False)
    pbone.lock_rotation = (False, False, False)
    pbone.lock_rotation_w = False
    pbone.lock_scale = (False, False, False)
    pbone.rotation_mode = 'QUATERNION'
    assign_bone_collections(pbone, 'Face')
    try:
        pbone.rigify_parameters.copy_rotation_axes = (True, False, True)
    except AttributeError:
        pass
    assign_bone_collection_refs(pbone.rigify_parameters, 'tweak', 'Face (Primary)')
    pbone = obj.pose.bones[bones['jaw']]
    pbone.rigify_type = 'limbs.simple_tentacle'
    pbone.lock_location = (False, False, False)
    pbone.lock_rotation = (False, False, False)
    pbone.lock_rotation_w = False
    pbone.lock_scale = (False, False, False)
    pbone.rotation_mode = 'QUATERNION'
    assign_bone_collections(pbone, 'Face')
    try:
        pbone.rigify_parameters.make_widget = False
    except AttributeError:
        pass
    try:
        pbone.rigify_parameters.make_control = False
    except AttributeError:
        pass
    try:
        pbone.rigify_parameters.copy_rotation_axes = (False, False, False)
    except AttributeError:
        pass
    assign_bone_collection_refs(pbone.rigify_parameters, 'tweak', 'Face (Primary)')
    pbone = obj.pose.bones[bones['eye.L']]
    pbone.rigify_type = 'basic.super_copy'
    pbone.lock_location = (False, False, False)
    pbone.lock_rotation = (False, False, False)
    pbone.lock_rotation_w = False
    pbone.lock_scale = (False, False, False)
    pbone.rotation_mode = 'QUATERNION'
    assign_bone_collections(pbone, 'Face')
    pbone = obj.pose.bones[bones['nose.L']]
    pbone.rigify_type = 'basic.super_copy'
    pbone.lock_location = (False, False, False)
    pbone.lock_rotation = (False, False, False)
    pbone.lock_rotation_w = False
    pbone.lock_scale = (False, False, False)
    pbone.rotation_mode = 'QUATERNION'
    assign_bone_collections(pbone, 'Face')
    pbone = obj.pose.bones[bones['eye.R']]
    pbone.rigify_type = 'basic.super_copy'
    pbone.lock_location = (False, False, False)
    pbone.lock_rotation = (False, False, False)
    pbone.lock_rotation_w = False
    pbone.lock_scale = (False, False, False)
    pbone.rotation_mode = 'QUATERNION'
    assign_bone_collections(pbone, 'Face')
    pbone = obj.pose.bones[bones['nose.R']]
    pbone.rigify_type = 'basic.super_copy'
    pbone.lock_location = (False, False, False)
    pbone.lock_rotation = (False, False, False)
    pbone.lock_rotation_w = False
    pbone.lock_scale = (False, False, False)
    pbone.rotation_mode = 'QUATERNION'
    assign_bone_collections(pbone, 'Face')
    pbone = obj.pose.bones[bones['mane_top.01']]
    pbone.rigify_type = ''
    pbone.lock_location = (False, False, False)
    pbone.lock_rotation = (False, False, False)
    pbone.lock_rotation_w = False
    pbone.lock_scale = (False, False, False)
    pbone.rotation_mode = 'QUATERNION'
    assign_bone_collections(pbone, 'Hair')
    pbone = obj.pose.bones[bones['mane_top.06']]
    pbone.rigify_type = ''
    pbone.lock_location = (False, False, False)
    pbone.lock_rotation = (False, False, False)
    pbone.lock_rotation_w = False
    pbone.lock_scale = (False, False, False)
    pbone.rotation_mode = 'QUATERNION'
    assign_bone_collections(pbone, 'Hair')
    pbone = obj.pose.bones[bones['skull.L']]
    pbone.rigify_type = 'basic.super_copy'
    pbone.lock_location = (False, False, False)
    pbone.lock_rotation = (False, False, False)
    pbone.lock_rotation_w = False
    pbone.lock_scale = (False, False, False)
    pbone.rotation_mode = 'QUATERNION'
    assign_bone_collections(pbone, 'Spine')
    try:
        pbone.rigify_parameters.make_widget = False
    except AttributeError:
        pass
    try:
        pbone.rigify_parameters.make_control = False
    except AttributeError:
        pass
    pbone = obj.pose.bones[bones['skull.R']]
    pbone.rigify_type = 'basic.super_copy'
    pbone.lock_location = (False, False, False)
    pbone.lock_rotation = (False, False, False)
    pbone.lock_rotation_w = False
    pbone.lock_scale = (False, False, False)
    pbone.rotation_mode = 'QUATERNION'
    assign_bone_collections(pbone, 'Spine')
    try:
        pbone.rigify_parameters.make_widget = False
    except AttributeError:
        pass
    try:
        pbone.rigify_parameters.make_control = False
    except AttributeError:
        pass
    pbone = obj.pose.bones[bones['ear.L.001']]
    pbone.rigify_type = ''
    pbone.lock_location = (False, False, False)
    pbone.lock_rotation = (False, False, False)
    pbone.lock_rotation_w = False
    pbone.lock_scale = (False, False, False)
    pbone.rotation_mode = 'QUATERNION'
    assign_bone_collections(pbone, 'Face')
    pbone = obj.pose.bones[bones['ear.R.001']]
    pbone.rigify_type = ''
    pbone.lock_location = (False, False, False)
    pbone.lock_rotation = (False, False, False)
    pbone.lock_rotation_w = False
    pbone.lock_scale = (False, False, False)
    pbone.rotation_mode = 'QUATERNION'
    assign_bone_collections(pbone, 'Face')
    pbone = obj.pose.bones[bones['jaw.001']]
    pbone.rigify_type = ''
    pbone.lock_location = (False, False, False)
    pbone.lock_rotation = (False, False, False)
    pbone.lock_rotation_w = False
    pbone.lock_scale = (False, False, False)
    pbone.rotation_mode = 'QUATERNION'
    assign_bone_collections(pbone, 'Face')

    bpy.ops.object.mode_set(mode='EDIT')
    for bone in arm.edit_bones:
        bone.select = False
        bone.select_head = False
        bone.select_tail = False
    for b in bones:
        bone = arm.edit_bones[bones[b]]
        bone.select = True
        bone.select_head = True
        bone.select_tail = True
        bone.bbone_x = bone.bbone_z = bone.length * 0.05
        arm.edit_bones.active = bone

    arm.collections.active_index = 0

    return bones


if __name__ == "__main__":
    create(bpy.context.active_object)
