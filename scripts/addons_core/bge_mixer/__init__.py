# GPLv3 License
#
# Copyright (C) 2020 Ubisoft
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 2 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <https://www.gnu.org/licenses/>.

"""
Mixer addon entry point for Blender.

Register/unregister functions and logging setup.
"""

import atexit
import faulthandler
import logging
import os
from pathlib import Path
import time

__version__ = "v1.0.0"  # Generated by inject_version.py
display_version = "1.0.0"  # Generated by inject_version.py
version_date = "2021-04-21:16:17:00 UTC"  # Generated by inject_version.py


def about_date():
    date = time.strptime(version_date[0:10], "%Y-%m-%d")
    return time.strftime("%d %B %Y", date)


bl_info = {
    "name": "Mixer",
    "author": "Ubisoft Animation Studio",
    "description": "Collaborative 3D edition accross 3D software",
    "version": (1, 0, 0),  # Generated by inject_version.py
    "blender": (2, 91, 0),
    "location": "View3D > Mixer",
    "warning": "Experimental addon, can break your scenes",
    "wiki_url": "https://github.com/ubisoft/mixer/",
    "doc_url": "https://ubisoft-mixer.readthedocs.io/en/latest/",
    "tracker_url": "",
    "category": "Collaboration",
}

logger = logging.getLogger(__name__)
logger.propagate = False
MODULE_PATH = Path(__file__).parent.parent
_disable_fault_handler = False


def cleanup():
    from mixer.share_data import share_data

    try:
        if share_data.local_server_process:
            share_data.local_server_process.kill()
    except Exception:
        pass

    if _disable_fault_handler:
        faulthandler.disable()


def register():
    from mixer import bl_panels
    from mixer import bl_operators
    from mixer import bl_properties, bl_preferences
    from mixer import blender_data
    from mixer.blender_data import debug_addon
    from mixer.log_utils import Formatter, get_log_file
    from mixer import icons
    from mixer import ui
    from mixer.utils import utils_ui_operators
    from mixer import vrtist

    print("\n ------ UAS: Loading Mixer Add-on ------- ")

    if len(logger.handlers) == 0:
        # Add the pid to the log. Just enough for the tests, that merge the logs and need to distinguish
        # two Blender on the same machine. Pids might collide during regular networked operation
        old_factory = logging.getLogRecordFactory()
        pid = str(os.getpid())

        def record_factory(*args, **kwargs):
            record = old_factory(*args, **kwargs)
            record.custom_attribute = pid
            return record

        logging.setLogRecordFactory(record_factory)

        logger.setLevel(logging.WARNING)
        formatter = Formatter("{asctime} {custom_attribute:<6} {levelname[0]} {name:<36}  - {message:<80}", style="{")
        handler = logging.StreamHandler()
        handler.setFormatter(formatter)
        logger.addHandler(handler)

        handler = logging.FileHandler(get_log_file())
        handler.setFormatter(formatter)
        logger.addHandler(handler)

    if not faulthandler.is_enabled():
        faulthandler.enable()
        global _disable_fault_handler
        _disable_fault_handler = True

    debug_addon.register()

    icons.register()
    bl_preferences.register()
    bl_properties.register()
    bl_panels.register()
    bl_operators.register()
    utils_ui_operators.register()
    ui.register()
    blender_data.register()
    vrtist.register()

    atexit.register(cleanup)


def unregister():
    from mixer import bl_panels
    from mixer import bl_operators
    from mixer import bl_properties, bl_preferences
    from mixer import blender_data
    from mixer.blender_data import debug_addon
    from mixer import icons
    from mixer.utils import utils_ui_operators
    from mixer import ui
    from mixer import vrtist

    cleanup()

    atexit.unregister(cleanup)

    vrtist.unregister()
    ui.unregister()
    utils_ui_operators.unregister()
    blender_data.unregister()
    bl_operators.unregister()
    bl_panels.unregister()
    bl_properties.unregister()
    bl_preferences.unregister()
    icons.unregister()

    debug_addon.unregister()
