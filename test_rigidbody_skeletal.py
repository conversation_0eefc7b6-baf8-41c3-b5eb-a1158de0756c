#!/usr/bin/env python3
"""
测试脚本：验证骨骼动画物理更新功能
"""

import sys
import os

# 添加UPBGE的Python路径（如果需要）
# sys.path.append('/path/to/upbge/python/modules')

def test_rigidbody_skeletal_functions():
    """测试骨骼动画物理更新函数"""
    
    try:
        # 尝试导入bpy模块
        import bpy
        print("✓ 成功导入bpy模块")
        
        # 检查我们的函数是否存在
        if hasattr(bpy, 'update_for_skeletal_animation'):
            print("✓ 找到 update_for_skeletal_animation 函数")
            
            # 尝试调用函数（使用None作为场景参数）
            try:
                result = bpy.update_for_skeletal_animation(scene=None)
                print("✓ 成功调用 update_for_skeletal_animation")
            except Exception as e:
                print(f"⚠ 调用 update_for_skeletal_animation 时出现预期错误: {e}")
                # 这是预期的，因为可能没有加载场景
        else:
            print("✗ 未找到 update_for_skeletal_animation 函数")
        
        if hasattr(bpy, 'update_fallback'):
            print("✓ 找到 update_fallback 函数")
            
            # 尝试调用备用函数
            try:
                result = bpy.update_fallback(scene=None)
                print("✓ 成功调用 update_fallback")
            except Exception as e:
                print(f"⚠ 调用 update_fallback 时出现预期错误: {e}")
                # 这是预期的，因为可能没有加载场景
        else:
            print("✗ 未找到 update_fallback 函数")
            
        # 检查函数的文档字符串
        if hasattr(bpy, 'update_for_skeletal_animation'):
            doc = bpy.update_for_skeletal_animation.__doc__
            if doc and 'skeletal animation' in doc.lower():
                print("✓ 函数文档字符串正确")
            else:
                print("⚠ 函数文档字符串可能有问题")
        
        return True
        
    except ImportError as e:
        print(f"✗ 无法导入bpy模块: {e}")
        print("注意：此测试需要在UPBGE环境中运行")
        return False
    except Exception as e:
        print(f"✗ 测试过程中出现错误: {e}")
        return False

def test_compilation_syntax():
    """测试C++代码的基本语法正确性"""
    
    print("\n检查C++代码语法...")
    
    # 检查头文件包含
    cpp_file = "source/blender/python/intern/bpy_rigidbody_skeletal.cc"
    if os.path.exists(cpp_file):
        print(f"✓ 找到C++源文件: {cpp_file}")
        
        with open(cpp_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # 检查基本语法元素
        checks = [
            ('#include <Python.h>', "Python.h包含"),
            ('PyObject *bpy_rigidbody_update_for_skeletal_animation', "主函数声明"),
            ('PyObject *bpy_rigidbody_update_fallback', "备用函数声明"),
            ('static PyMethodDef rigidbody_skeletal_methods[]', "方法定义数组"),
            ('void BPY_rigidbody_skeletal_add_to_module', "模块添加函数"),
            ('BKE_rigidbody_update_for_skeletal_animation', "C函数调用"),
        ]
        
        for check, desc in checks:
            if check in content:
                print(f"✓ {desc}")
            else:
                print(f"✗ 缺少 {desc}")
                
        # 检查常见的语法错误
        error_patterns = [
            ('UNUSED(self)', "使用了过时的UNUSED宏"),
            ('pyrna_struct_as_ptr(py_scene, "Scene")', "使用了错误的API调用"),
            ('BPy_GetContext()', "使用了不存在的函数"),
        ]
        
        for pattern, desc in error_patterns:
            if pattern in content:
                print(f"⚠ 可能的问题: {desc}")
        
        return True
    else:
        print(f"✗ 未找到C++源文件: {cpp_file}")
        return False

if __name__ == "__main__":
    print("UPBGE 骨骼动画物理更新功能测试")
    print("=" * 50)
    
    # 测试编译语法
    syntax_ok = test_compilation_syntax()
    
    print("\n" + "=" * 50)
    
    # 测试Python绑定
    if syntax_ok:
        print("开始测试Python绑定...")
        binding_ok = test_rigidbody_skeletal_functions()
        
        if binding_ok:
            print("\n🎉 所有测试通过！")
        else:
            print("\n⚠ 部分测试失败，但这可能是因为不在UPBGE环境中运行")
    else:
        print("\n❌ C++代码存在语法问题，请先修复")
    
    print("\n使用说明：")
    print("1. 确保UPBGE已正确编译")
    print("2. 在UPBGE中运行此脚本以测试Python绑定")
    print("3. 在游戏中使用 bpy.update_for_skeletal_animation() 更新物理") 