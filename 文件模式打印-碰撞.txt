Successfully added function update_for_skeletal_animation to bpy module
Successfully added function update_fallback to bpy module
Successfully added function init_for_game_start to bpy module
Read blend: "D:\lida\blender\blender_a17656\game\game8-50.blend"
use_interactive_rb=false,should_simulate=false----
Should simulate...
DEBUG: clothModifier_do() start
DEBUG: clothModifier_do() - framenr=1
DEBUG: clothModifier_do() - calling do_init_cloth
DEBUG: do_init_cloth() start - framenr=1
DEBUG: cloth_from_object() start - first=1
DEBUG: cloth_from_mesh() start
DEBUG: cloth_from_mesh() end
DEBUG: cloth_apply_vgroup() start
DEBUG: cloth_apply_vgroup() end
DEBUG: cloth_build_springs() start
DEBUG: cloth_build_springs() end - success
DEBUG: SIM_cloth_solver_init() start
DEBUG: SIM_cloth_solver_init() end - success
DEBUG: bvhtree_build_from_cloth() start - epsilon=0.001000
DEBUG: bvhtree_build_from_cloth() end - success
DEBUG: bvhtree_build_from_cloth() start - epsilon=0.015000
DEBUG: bvhtree_build_from_cloth() end - success
DEBUG: cloth_from_object() end - success
DEBUG: SIM_cloth_solver_set_positions() start
DEBUG: SIM_cloth_solver_set_positions() end
DEBUG: do_init_cloth() end - success
DEBUG: clothModifier_do() - do_init_cloth success
need_first_init = 1
DEBUG: clothModifier_do() - ״γʼ
DEBUG: SIM_cloth_solver_free() start
DEBUG: SIM_cloth_solver_free() end
DEBUG: do_init_cloth() start - framenr=1
DEBUG: cloth_from_object() start - first=1
DEBUG: cloth_from_mesh() start
DEBUG: cloth_from_mesh() end
DEBUG: cloth_apply_vgroup() start
DEBUG: cloth_apply_vgroup() end
DEBUG: cloth_build_springs() start
DEBUG: cloth_build_springs() end - success
DEBUG: SIM_cloth_solver_init() start
DEBUG: SIM_cloth_solver_init() end - success
DEBUG: bvhtree_build_from_cloth() start - epsilon=0.001000
DEBUG: bvhtree_build_from_cloth() end - success
DEBUG: bvhtree_build_from_cloth() start - epsilon=0.015000
DEBUG: bvhtree_build_from_cloth() end - success
DEBUG: cloth_from_object() end - success
DEBUG: SIM_cloth_solver_set_positions() start
DEBUG: SIM_cloth_solver_set_positions() end
DEBUG: do_init_cloth() end - success
--do_init_cloth end---once--
DEBUG: clothModifier_do() - ״γʼɣ\nMOD_cloth: copying cloth results back to mesh positions
  MOD_cloth Vertex 0: pos=[-0.000018,-0.111819,1.024277]
  MOD_cloth Vertex 1: pos=[0.028666,-0.107355,1.024218]
  MOD_cloth Vertex 2: pos=[0.055114,-0.094275,1.024068]
  MOD_cloth Vertex 3: pos=[0.077224,-0.073531,1.023880]
  MOD_cloth Vertex 4: pos=[0.093138,-0.046725,1.023717]
MOD_cloth: forced object data re-evaluation
RB: Rebuilding world--[ctime == startframe + 1 && rbw->ltime == startframe]--startframe = 1
RB: Rebuilding world--[cache->flag & PTCACHE_OUTDATED]--startframe = 1
addRigidBody: --------
addRigidBody: --------
addRigidBody: --------
addRigidBody: --------
addRigidBody: --------
addRigidBody: --------
addRigidBody: --------
addRigidBody: --------
addRigidBody: --------
addRigidBody: --------
addRigidBody: --------
addRigidBody: --------
addRigidBody: --------
addRigidBody: --------
addRigidBody: --------
addRigidBody: --------
addRigidBody: --------
addRigidBody: --------
addRigidBody: --------
addRigidBody: --------
addRigidBody: --------
addRigidBody: --------
addRigidBody: --------
addRigidBody: --------
addRigidBody: --------
addRigidBody: --------
addRigidBody: --------
addRigidBody: --------
addRigidBody: --------
addRigidBody: --------
addRigidBody: --------
addRigidBody: --------
addRigidBody: --------
addRigidBody: --------
addRigidBody: --------
addRigidBody: --------
addRigidBody: --------
Should simulate...
DEBUG: clothModifier_do() start
DEBUG: clothModifier_do() - framenr=2
DEBUG: clothModifier_do() - calling do_init_cloth
DEBUG: do_init_cloth() start - framenr=2
DEBUG: do_init_cloth() end - success
DEBUG: clothModifier_do() - do_init_cloth success
need_first_init = 0
DEBUG: cloth_is_upbge_single_frame_mode() start - current_frame=2, last_frame=1
DEBUG: cloth_is_upbge_single_frame_mode() end - result=0
cache_flag_baked = 1 --- is_single_frame_mode = 0 --- can_simulate = 1
framenr = 2, last_frame = 1, timescale = 1.000000
DEBUG: clothModifier_do() - calling BKE_ptcache_read
DEBUG: clothModifier_do() - cache_result=0
DEBUG: clothModifier_do() - writing cache for first frame
DEBUG: clothModifier_do() - starting simulation
DEBUG: clothModifier_do() - calling do_step_cloth
DEBUG: do_step_cloth() start - framenr=2
Matrix initialized
Vertex 0: local=[-0.000018,-0.111819,1.024277] world=[-0.000018,-0.111819,1.065248]
Vertex 0: old_rest=[-0.000018,-0.111819,1.065248] current_mesh=[-0.000018,-0.111819,1.065248] delta=[0.000000,0.000000,0.000000]
Vertex 1: old_rest=[0.028666,-0.107355,1.065187] current_mesh=[0.028666,-0.107355,1.065187] delta=[0.000000,0.000000,0.000000]
Vertex 2: old_rest=[0.055114,-0.094275,1.065030] current_mesh=[0.055114,-0.094275,1.065030] delta=[0.000000,0.000000,0.000000]
DEBUG: do_step_cloth() - calling BKE_effectors_create
DEBUG: do_step_cloth() - calling cloth_apply_vgroup
DEBUG: cloth_apply_vgroup() start
DEBUG: cloth_apply_vgroup() end
DEBUG: do_step_cloth() - calling cloth_update_springs
DEBUG: cloth_update_springs() start
DEBUG: cloth_update_springs() end
DEBUG: do_step_cloth() - calling SIM_cloth_solve
DEBUG: SIM_cloth_solve() start - frame=2.000000
DEBUG: SIM_cloth_solve() - is_hair=0
SIM_cloth_solve: tf=1.000000, dt_base=0.200000, timescale=1.000000, dt_final=0.200000
cloth_solve--tf-- 1.000000
=== Physics step 0.000000 ===
DEBUG: cloth_setup_constraints() start
DEBUG: cloth_setup_constraints() end
1. Constraints setup completed
2. Forces cleared
DEBUG: cloth_calc_force() start - time=0.000000
cloth_calc_force called - mvert_num: 198, avg_spring_len: 0.036831
Gravity enabled: [0.000000, 0.000000, -0.019620]
  Spring 1 (active): type=12, ij=153, kl=176, restlen=0.059902, stiffness=0.000000
  Spring 2 (active): type=12, ij=132, kl=197, restlen=0.059063, stiffness=0.000000
  Spring 3 (active): type=12, ij=152, kl=197, restlen=0.060961, stiffness=0.000000
  Spring 4 (active): type=12, ij=153, kl=196, restlen=0.058732, stiffness=0.000000
  Spring 5 (active): type=12, ij=151, kl=196, restlen=0.061899, stiffness=0.000000
Spring 0: restlen=0.047214, scaling_tension=15.000000, k_tension=610.900146, avg_spring_len=0.036831
Spring 1: restlen=0.047826, scaling_tension=15.000000, k_tension=610.900146, avg_spring_len=0.036831
Spring 2: restlen=0.048827, scaling_tension=15.000000, k_tension=610.900146, avg_spring_len=0.036831
Spring 3: restlen=0.049895, scaling_tension=15.000000, k_tension=610.900146, avg_spring_len=0.036831
Spring 4: restlen=0.050697, scaling_tension=15.000000, k_tension=610.900146, avg_spring_len=0.036831
cloth_calc_force completed - total springs: 726 (active: 726, inactive: 0)
  ...and 721 more active springs not shown
DEBUG: cloth_calc_force() end
3. Forces calculated
DEBUG: SIM_mass_spring_solve_velocities() start (Blender) - dt=0.200000
DEBUG: SIM_mass_spring_solve_velocities() end (Blender) - status=1
4. Velocities solved - status: 1, iterations: 8
DEBUG: cloth_solve_collisions() start - step=0.000000, dt=0.200000
DEBUG: SIM_mass_spring_solve_positions() start (Blender) - dt=0.200000
DEBUG: SIM_mass_spring_solve_positions() end (Blender)
DEBUG: cloth_solve_collisions() - iteration 1/1
DEBUG: cloth_bvh_collision() start - step=0.000000, dt=0.200000
DEBUG: ret variables initialized - ret=0, ret2=0
DEBUG: cloth_bvh_collision() - cloth_bvh=000001FEBCD77D88, cloth=000001FEBB2173D8
DEBUG: cloth_bvh_collision() - single_frame=0, max_loops=2
DEBUG: bvhtree_update_from_cloth() start - moving=0, self=0
DEBUG: bvhtree_update_from_cloth() end
DEBUG: collision loop iteration 1 - resetting ret2 to 0
DEBUG: cloth_bvh_objcollisions_resolve() start - numcollobj=3, dt=0.200000
DEBUG: objcollisions_resolve - ret initialized to 0
DEBUG: objcollisions_resolve - iteration 0, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 0 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 64 vertices, ret now=64
DEBUG: objcollisions_resolve - iteration 1, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 1 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 64 vertices, ret now=128
DEBUG: objcollisions_resolve() end - returning ret=128
DEBUG: object collision resolve returned 128
DEBUG: after object collisions - ret=128 (added 128)
DEBUG: after object collisions - ret2=128 (added 128)
DEBUG: applying collision resolution - ret2=128, updating 198 vertices
DEBUG: end of collision loop round 1 - ret=128, ret2=128
DEBUG: collision loop iteration 2 - resetting ret2 to 0
DEBUG: cloth_bvh_objcollisions_resolve() start - numcollobj=3, dt=0.200000
DEBUG: objcollisions_resolve - ret initialized to 0
DEBUG: objcollisions_resolve - iteration 0, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 0 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 61 vertices, ret now=61
DEBUG: objcollisions_resolve - iteration 1, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 1 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 61 vertices, ret now=122
DEBUG: objcollisions_resolve() end - returning ret=122
DEBUG: object collision resolve returned 122
DEBUG: after object collisions - ret=250 (added 122)
DEBUG: after object collisions - ret2=250 (added 250)
DEBUG: applying collision resolution - ret2=250, updating 198 vertices
DEBUG: end of collision loop round 2 - ret=250, ret2=250
DEBUG: cloth_bvh_collision() end - ret=250, rounds=2
DEBUG: final return value - min(ret=250, 1) = 1
DEBUG: cloth_solve_collisions() end - completed 1 collision iterations
5. Collisions processed
DEBUG: SIM_mass_spring_solve_positions() start (Blender) - dt=0.200000
DEBUG: SIM_mass_spring_solve_positions() end (Blender)
6. Positions solved
DEBUG: SIM_mass_spring_apply_result() start (Blender)
DEBUG: SIM_mass_spring_apply_result() end (Blender)
7. Results applied
Pinned vertex 154: set to xconst=[0.000304,-0.096996,1.098845]
Pinned vertex 155: set to xconst=[0.025214,-0.092702,1.098805]
Pinned vertex 156: set to xconst=[0.048131,-0.080568,1.098667]
...and 19 more pinned vertices (total: 22)
8. Pinned vertices updated
=== Physics step 0.200000 ===
DEBUG: cloth_setup_constraints() start
DEBUG: cloth_setup_constraints() end
1. Constraints setup completed
2. Forces cleared
DEBUG: cloth_calc_force() start - time=0.200000
cloth_calc_force called - mvert_num: 198, avg_spring_len: 0.036831
Gravity enabled: [0.000000, 0.000000, -0.019620]
  Spring 1 (active): type=12, ij=153, kl=176, restlen=0.059902, stiffness=0.000000
  Spring 2 (active): type=12, ij=132, kl=197, restlen=0.059063, stiffness=0.000000
  Spring 3 (active): type=12, ij=152, kl=197, restlen=0.060961, stiffness=0.000000
  Spring 4 (active): type=12, ij=153, kl=196, restlen=0.058732, stiffness=0.000000
  Spring 5 (active): type=12, ij=151, kl=196, restlen=0.061899, stiffness=0.000000
cloth_calc_force completed - total springs: 726 (active: 726, inactive: 0)
  ...and 721 more active springs not shown
DEBUG: cloth_calc_force() end
3. Forces calculated
DEBUG: SIM_mass_spring_solve_velocities() start (Blender) - dt=0.200000
DEBUG: SIM_mass_spring_solve_velocities() end (Blender) - status=1
4. Velocities solved - status: 1, iterations: 7
DEBUG: cloth_solve_collisions() start - step=0.200000, dt=0.200000
DEBUG: SIM_mass_spring_solve_positions() start (Blender) - dt=0.200000
DEBUG: SIM_mass_spring_solve_positions() end (Blender)
DEBUG: cloth_solve_collisions() - iteration 1/1
DEBUG: cloth_bvh_collision() start - step=0.200000, dt=0.200000
DEBUG: ret variables initialized - ret=0, ret2=0
DEBUG: cloth_bvh_collision() - cloth_bvh=000001FEBCD77D88, cloth=000001FEBB2173D8
DEBUG: cloth_bvh_collision() - single_frame=0, max_loops=2
DEBUG: bvhtree_update_from_cloth() start - moving=0, self=0
DEBUG: bvhtree_update_from_cloth() end
DEBUG: collision loop iteration 1 - resetting ret2 to 0
DEBUG: cloth_bvh_objcollisions_resolve() start - numcollobj=3, dt=0.200000
DEBUG: objcollisions_resolve - ret initialized to 0
DEBUG: objcollisions_resolve - iteration 0, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 0 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 63 vertices, ret now=63
DEBUG: objcollisions_resolve - iteration 1, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 1 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 63 vertices, ret now=126
DEBUG: objcollisions_resolve() end - returning ret=126
DEBUG: object collision resolve returned 126
DEBUG: after object collisions - ret=126 (added 126)
DEBUG: after object collisions - ret2=126 (added 126)
DEBUG: applying collision resolution - ret2=126, updating 198 vertices
DEBUG: end of collision loop round 1 - ret=126, ret2=126
DEBUG: collision loop iteration 2 - resetting ret2 to 0
DEBUG: cloth_bvh_objcollisions_resolve() start - numcollobj=3, dt=0.200000
DEBUG: objcollisions_resolve - ret initialized to 0
DEBUG: objcollisions_resolve - iteration 0, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 0 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 58 vertices, ret now=58
DEBUG: objcollisions_resolve - iteration 1, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 1 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 58 vertices, ret now=116
DEBUG: objcollisions_resolve() end - returning ret=116
DEBUG: object collision resolve returned 116
DEBUG: after object collisions - ret=242 (added 116)
DEBUG: after object collisions - ret2=242 (added 242)
DEBUG: applying collision resolution - ret2=242, updating 198 vertices
DEBUG: end of collision loop round 2 - ret=242, ret2=242
DEBUG: cloth_bvh_collision() end - ret=242, rounds=2
DEBUG: final return value - min(ret=242, 1) = 1
DEBUG: cloth_solve_collisions() end - completed 1 collision iterations
5. Collisions processed
DEBUG: SIM_mass_spring_solve_positions() start (Blender) - dt=0.200000
DEBUG: SIM_mass_spring_solve_positions() end (Blender)
6. Positions solved
DEBUG: SIM_mass_spring_apply_result() start (Blender)
DEBUG: SIM_mass_spring_apply_result() end (Blender)
7. Results applied
Pinned vertex 154: set to xconst=[0.000304,-0.096996,1.098845]
Pinned vertex 155: set to xconst=[0.025214,-0.092702,1.098805]
Pinned vertex 156: set to xconst=[0.048131,-0.080568,1.098667]
...and 19 more pinned vertices (total: 22)
8. Pinned vertices updated
=== Physics step 0.400000 ===
DEBUG: cloth_setup_constraints() start
DEBUG: cloth_setup_constraints() end
1. Constraints setup completed
2. Forces cleared
DEBUG: cloth_calc_force() start - time=0.400000
cloth_calc_force called - mvert_num: 198, avg_spring_len: 0.036831
Gravity enabled: [0.000000, 0.000000, -0.019620]
  Spring 1 (active): type=12, ij=153, kl=176, restlen=0.059902, stiffness=0.000000
  Spring 2 (active): type=12, ij=132, kl=197, restlen=0.059063, stiffness=0.000000
  Spring 3 (active): type=12, ij=152, kl=197, restlen=0.060961, stiffness=0.000000
  Spring 4 (active): type=12, ij=153, kl=196, restlen=0.058732, stiffness=0.000000
  Spring 5 (active): type=12, ij=151, kl=196, restlen=0.061899, stiffness=0.000000
cloth_calc_force completed - total springs: 726 (active: 726, inactive: 0)
  ...and 721 more active springs not shown
DEBUG: cloth_calc_force() end
3. Forces calculated
DEBUG: SIM_mass_spring_solve_velocities() start (Blender) - dt=0.200000
DEBUG: SIM_mass_spring_solve_velocities() end (Blender) - status=1
4. Velocities solved - status: 1, iterations: 8
DEBUG: cloth_solve_collisions() start - step=0.400000, dt=0.200000
DEBUG: SIM_mass_spring_solve_positions() start (Blender) - dt=0.200000
DEBUG: SIM_mass_spring_solve_positions() end (Blender)
DEBUG: cloth_solve_collisions() - iteration 1/1
DEBUG: cloth_bvh_collision() start - step=0.400000, dt=0.200000
DEBUG: ret variables initialized - ret=0, ret2=0
DEBUG: cloth_bvh_collision() - cloth_bvh=000001FEBCD77D88, cloth=000001FEBB2173D8
DEBUG: cloth_bvh_collision() - single_frame=0, max_loops=2
DEBUG: bvhtree_update_from_cloth() start - moving=0, self=0
DEBUG: bvhtree_update_from_cloth() end
DEBUG: collision loop iteration 1 - resetting ret2 to 0
DEBUG: cloth_bvh_objcollisions_resolve() start - numcollobj=3, dt=0.200000
DEBUG: objcollisions_resolve - ret initialized to 0
DEBUG: objcollisions_resolve - iteration 0, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 0 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 64 vertices, ret now=64
DEBUG: objcollisions_resolve - iteration 1, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 1 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 64 vertices, ret now=128
DEBUG: objcollisions_resolve() end - returning ret=128
DEBUG: object collision resolve returned 128
DEBUG: after object collisions - ret=128 (added 128)
DEBUG: after object collisions - ret2=128 (added 128)
DEBUG: applying collision resolution - ret2=128, updating 198 vertices
DEBUG: end of collision loop round 1 - ret=128, ret2=128
DEBUG: collision loop iteration 2 - resetting ret2 to 0
DEBUG: cloth_bvh_objcollisions_resolve() start - numcollobj=3, dt=0.200000
DEBUG: objcollisions_resolve - ret initialized to 0
DEBUG: objcollisions_resolve - iteration 0, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 0 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 59 vertices, ret now=59
DEBUG: objcollisions_resolve - iteration 1, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 1 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 59 vertices, ret now=118
DEBUG: objcollisions_resolve() end - returning ret=118
DEBUG: object collision resolve returned 118
DEBUG: after object collisions - ret=246 (added 118)
DEBUG: after object collisions - ret2=246 (added 246)
DEBUG: applying collision resolution - ret2=246, updating 198 vertices
DEBUG: end of collision loop round 2 - ret=246, ret2=246
DEBUG: cloth_bvh_collision() end - ret=246, rounds=2
DEBUG: final return value - min(ret=246, 1) = 1
DEBUG: cloth_solve_collisions() end - completed 1 collision iterations
5. Collisions processed
DEBUG: SIM_mass_spring_solve_positions() start (Blender) - dt=0.200000
DEBUG: SIM_mass_spring_solve_positions() end (Blender)
6. Positions solved
DEBUG: SIM_mass_spring_apply_result() start (Blender)
DEBUG: SIM_mass_spring_apply_result() end (Blender)
7. Results applied
Pinned vertex 154: set to xconst=[0.000304,-0.096996,1.098845]
Pinned vertex 155: set to xconst=[0.025214,-0.092702,1.098805]
Pinned vertex 156: set to xconst=[0.048131,-0.080568,1.098667]
...and 19 more pinned vertices (total: 22)
8. Pinned vertices updated
=== Physics step 0.600000 ===
DEBUG: cloth_setup_constraints() start
DEBUG: cloth_setup_constraints() end
1. Constraints setup completed
2. Forces cleared
DEBUG: cloth_calc_force() start - time=0.600000
cloth_calc_force called - mvert_num: 198, avg_spring_len: 0.036831
Gravity enabled: [0.000000, 0.000000, -0.019620]
  Spring 1 (active): type=12, ij=153, kl=176, restlen=0.059902, stiffness=0.000000
  Spring 2 (active): type=12, ij=132, kl=197, restlen=0.059063, stiffness=0.000000
  Spring 3 (active): type=12, ij=152, kl=197, restlen=0.060961, stiffness=0.000000
  Spring 4 (active): type=12, ij=153, kl=196, restlen=0.058732, stiffness=0.000000
  Spring 5 (active): type=12, ij=151, kl=196, restlen=0.061899, stiffness=0.000000
cloth_calc_force completed - total springs: 726 (active: 726, inactive: 0)
  ...and 721 more active springs not shown
DEBUG: cloth_calc_force() end
3. Forces calculated
DEBUG: SIM_mass_spring_solve_velocities() start (Blender) - dt=0.200000
DEBUG: SIM_mass_spring_solve_velocities() end (Blender) - status=1
4. Velocities solved - status: 1, iterations: 8
DEBUG: cloth_solve_collisions() start - step=0.600000, dt=0.200000
DEBUG: SIM_mass_spring_solve_positions() start (Blender) - dt=0.200000
DEBUG: SIM_mass_spring_solve_positions() end (Blender)
DEBUG: cloth_solve_collisions() - iteration 1/1
DEBUG: cloth_bvh_collision() start - step=0.600000, dt=0.200000
DEBUG: ret variables initialized - ret=0, ret2=0
DEBUG: cloth_bvh_collision() - cloth_bvh=000001FEBCD77D88, cloth=000001FEBB2173D8
DEBUG: cloth_bvh_collision() - single_frame=0, max_loops=2
DEBUG: bvhtree_update_from_cloth() start - moving=0, self=0
DEBUG: bvhtree_update_from_cloth() end
DEBUG: collision loop iteration 1 - resetting ret2 to 0
DEBUG: cloth_bvh_objcollisions_resolve() start - numcollobj=3, dt=0.200000
DEBUG: objcollisions_resolve - ret initialized to 0
DEBUG: objcollisions_resolve - iteration 0, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 0 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 67 vertices, ret now=67
DEBUG: objcollisions_resolve - iteration 1, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 1 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 67 vertices, ret now=134
DEBUG: objcollisions_resolve() end - returning ret=134
DEBUG: object collision resolve returned 134
DEBUG: after object collisions - ret=134 (added 134)
DEBUG: after object collisions - ret2=134 (added 134)
DEBUG: applying collision resolution - ret2=134, updating 198 vertices
DEBUG: end of collision loop round 1 - ret=134, ret2=134
DEBUG: collision loop iteration 2 - resetting ret2 to 0
DEBUG: cloth_bvh_objcollisions_resolve() start - numcollobj=3, dt=0.200000
DEBUG: objcollisions_resolve - ret initialized to 0
DEBUG: objcollisions_resolve - iteration 0, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 0 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 60 vertices, ret now=60
DEBUG: objcollisions_resolve - iteration 1, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 1 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 60 vertices, ret now=120
DEBUG: objcollisions_resolve() end - returning ret=120
DEBUG: object collision resolve returned 120
DEBUG: after object collisions - ret=254 (added 120)
DEBUG: after object collisions - ret2=254 (added 254)
DEBUG: applying collision resolution - ret2=254, updating 198 vertices
DEBUG: end of collision loop round 2 - ret=254, ret2=254
DEBUG: cloth_bvh_collision() end - ret=254, rounds=2
DEBUG: final return value - min(ret=254, 1) = 1
DEBUG: cloth_solve_collisions() end - completed 1 collision iterations
5. Collisions processed
DEBUG: SIM_mass_spring_solve_positions() start (Blender) - dt=0.200000
DEBUG: SIM_mass_spring_solve_positions() end (Blender)
6. Positions solved
DEBUG: SIM_mass_spring_apply_result() start (Blender)
DEBUG: SIM_mass_spring_apply_result() end (Blender)
7. Results applied
Pinned vertex 154: set to xconst=[0.000304,-0.096996,1.098845]
Pinned vertex 155: set to xconst=[0.025214,-0.092702,1.098805]
Pinned vertex 156: set to xconst=[0.048131,-0.080568,1.098667]
...and 19 more pinned vertices (total: 22)
8. Pinned vertices updated
=== Physics step 0.800000 ===
DEBUG: cloth_setup_constraints() start
DEBUG: cloth_setup_constraints() end
1. Constraints setup completed
2. Forces cleared
DEBUG: cloth_calc_force() start - time=0.800000
cloth_calc_force called - mvert_num: 198, avg_spring_len: 0.036831
Gravity enabled: [0.000000, 0.000000, -0.019620]
  Spring 1 (active): type=12, ij=153, kl=176, restlen=0.059902, stiffness=0.000000
  Spring 2 (active): type=12, ij=132, kl=197, restlen=0.059063, stiffness=0.000000
  Spring 3 (active): type=12, ij=152, kl=197, restlen=0.060961, stiffness=0.000000
  Spring 4 (active): type=12, ij=153, kl=196, restlen=0.058732, stiffness=0.000000
  Spring 5 (active): type=12, ij=151, kl=196, restlen=0.061899, stiffness=0.000000
cloth_calc_force completed - total springs: 726 (active: 726, inactive: 0)
  ...and 721 more active springs not shown
DEBUG: cloth_calc_force() end
3. Forces calculated
DEBUG: SIM_mass_spring_solve_velocities() start (Blender) - dt=0.200000
DEBUG: SIM_mass_spring_solve_velocities() end (Blender) - status=1
4. Velocities solved - status: 1, iterations: 7
DEBUG: cloth_solve_collisions() start - step=0.800000, dt=0.200000
DEBUG: SIM_mass_spring_solve_positions() start (Blender) - dt=0.200000
DEBUG: SIM_mass_spring_solve_positions() end (Blender)
DEBUG: cloth_solve_collisions() - iteration 1/1
DEBUG: cloth_bvh_collision() start - step=0.800000, dt=0.200000
DEBUG: ret variables initialized - ret=0, ret2=0
DEBUG: cloth_bvh_collision() - cloth_bvh=000001FEBCD77D88, cloth=000001FEBB2173D8
DEBUG: cloth_bvh_collision() - single_frame=0, max_loops=2
DEBUG: bvhtree_update_from_cloth() start - moving=0, self=0
DEBUG: bvhtree_update_from_cloth() end
DEBUG: collision loop iteration 1 - resetting ret2 to 0
DEBUG: cloth_bvh_objcollisions_resolve() start - numcollobj=3, dt=0.200000
DEBUG: objcollisions_resolve - ret initialized to 0
DEBUG: objcollisions_resolve - iteration 0, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 0 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 69 vertices, ret now=69
DEBUG: objcollisions_resolve - iteration 1, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 1 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 69 vertices, ret now=138
DEBUG: objcollisions_resolve() end - returning ret=138
DEBUG: object collision resolve returned 138
DEBUG: after object collisions - ret=138 (added 138)
DEBUG: after object collisions - ret2=138 (added 138)
DEBUG: applying collision resolution - ret2=138, updating 198 vertices
DEBUG: end of collision loop round 1 - ret=138, ret2=138
DEBUG: collision loop iteration 2 - resetting ret2 to 0
DEBUG: cloth_bvh_objcollisions_resolve() start - numcollobj=3, dt=0.200000
DEBUG: objcollisions_resolve - ret initialized to 0
DEBUG: objcollisions_resolve - iteration 0, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 0 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 61 vertices, ret now=61
DEBUG: objcollisions_resolve - iteration 1, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 1 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 61 vertices, ret now=122
DEBUG: objcollisions_resolve() end - returning ret=122
DEBUG: object collision resolve returned 122
DEBUG: after object collisions - ret=260 (added 122)
DEBUG: after object collisions - ret2=260 (added 260)
DEBUG: applying collision resolution - ret2=260, updating 198 vertices
DEBUG: end of collision loop round 2 - ret=260, ret2=260
DEBUG: cloth_bvh_collision() end - ret=260, rounds=2
DEBUG: final return value - min(ret=260, 1) = 1
DEBUG: cloth_solve_collisions() end - completed 1 collision iterations
5. Collisions processed
DEBUG: SIM_mass_spring_solve_positions() start (Blender) - dt=0.200000
DEBUG: SIM_mass_spring_solve_positions() end (Blender)
6. Positions solved
DEBUG: SIM_mass_spring_apply_result() start (Blender)
DEBUG: SIM_mass_spring_apply_result() end (Blender)
7. Results applied
Pinned vertex 154: set to xconst=[0.000304,-0.096996,1.098845]
Pinned vertex 155: set to xconst=[0.025214,-0.092702,1.098805]
Pinned vertex 156: set to xconst=[0.048131,-0.080568,1.098667]
...and 19 more pinned vertices (total: 22)
8. Pinned vertices updated
=== Final vertex states ===
Vertex 0 (free): pos=[0.000152,-0.107845,1.060437] vel=[0.000414,0.008144,-0.005002]
Vertex 1 (free): pos=[0.028298,-0.103331,1.060386] vel=[-0.000510,0.008244,-0.005027]
Vertex 2 (free): pos=[0.054207,-0.090326,1.060153] vel=[-0.001489,0.008302,-0.005075]
Vertex 154 (pinned): pos=[0.000304,-0.096996,1.098845]
Vertex 155 (pinned): pos=[0.025214,-0.092702,1.098805]
Vertex 156 (pinned): pos=[0.048131,-0.080568,1.098667]
Summary: 198 total vertices (176 free, 22 pinned)
DEBUG: SIM_cloth_solve() end - success
DEBUG: do_step_cloth() - SIM_cloth_solve returned 1
DEBUG: do_step_cloth() - calling BKE_effectors_free
DEBUG: do_step_cloth() end - ret=1
DEBUG: clothModifier_do() - do_step_cloth success
DEBUG: clothModifier_do() - writing cache (normal mode)
DEBUG: clothModifier_do() - calling cloth_to_object
DEBUG: cloth_to_object() start
cloth_to_object: copying 198 vertices to mesh
DEBUG: cloth_is_upbge_single_frame_mode() start - current_frame=1, last_frame=1
DEBUG: cloth_is_upbge_single_frame_mode() end - result=1
  Free Vertex 0: using x=[0.000152,-0.107845,1.060437] -> mesh_pos=[0.000152,-0.107845,1.019651]
  Free Vertex 1: using x=[0.028298,-0.103331,1.060386] -> mesh_pos=[0.028298,-0.103331,1.019602]
  Free Vertex 2: using x=[0.054207,-0.090326,1.060153] -> mesh_pos=[0.054207,-0.090326,1.019378]
  Pinned Vertex 154: using xconst=[0.000304,-0.096996,1.098845] -> mesh_pos=[0.000304,-0.096996,1.056581]
  Pinned Vertex 155: using xconst=[0.025214,-0.092702,1.098805] -> mesh_pos=[0.025214,-0.092702,1.056543]
  Pinned Vertex 156: using xconst=[0.048131,-0.080568,1.098667] -> mesh_pos=[0.048131,-0.080568,1.056410]
  Total vertices processed: 198 (pinned=22, free=176)
UPBGE single frame mode: using pinned vertex constraint positions
DEBUG: cloth_to_object() end
DEBUG: clothModifier_do() end - success
MOD_cloth: copying cloth results back to mesh positions
  MOD_cloth Vertex 0: pos=[0.000152,-0.107845,1.019651]
  MOD_cloth Vertex 1: pos=[0.028298,-0.103331,1.019602]
  MOD_cloth Vertex 2: pos=[0.054207,-0.090326,1.019378]
  MOD_cloth Vertex 3: pos=[0.075855,-0.069997,1.019118]
  MOD_cloth Vertex 4: pos=[0.091442,-0.043563,1.018815]
MOD_cloth: forced object data re-evaluation
Should simulate...
DEBUG: clothModifier_do() start
DEBUG: clothModifier_do() - framenr=3
DEBUG: clothModifier_do() - calling do_init_cloth
DEBUG: do_init_cloth() start - framenr=3
DEBUG: do_init_cloth() end - success
DEBUG: clothModifier_do() - do_init_cloth success
need_first_init = 0
DEBUG: cloth_is_upbge_single_frame_mode() start - current_frame=3, last_frame=2
DEBUG: cloth_is_upbge_single_frame_mode() end - result=0
cache_flag_baked = 1 --- is_single_frame_mode = 0 --- can_simulate = 1
framenr = 3, last_frame = 2, timescale = 1.000000
DEBUG: clothModifier_do() - calling BKE_ptcache_read
DEBUG: clothModifier_do() - cache_result=0
DEBUG: clothModifier_do() - starting simulation
DEBUG: clothModifier_do() - calling do_step_cloth
DEBUG: do_step_cloth() start - framenr=3
Object matrix unchanged
Vertex 0: local=[-0.000018,-0.111819,1.024277] world=[-0.000018,-0.111819,1.065248]
DEBUG: do_step_cloth() - calling BKE_effectors_create
DEBUG: do_step_cloth() - calling cloth_apply_vgroup
DEBUG: cloth_apply_vgroup() start
DEBUG: cloth_apply_vgroup() end
DEBUG: do_step_cloth() - calling cloth_update_springs
DEBUG: cloth_update_springs() start
DEBUG: cloth_update_springs() end
DEBUG: do_step_cloth() - calling SIM_cloth_solve
DEBUG: SIM_cloth_solve() start - frame=3.000000
DEBUG: SIM_cloth_solve() - is_hair=0
SIM_cloth_solve: tf=1.000000, dt_base=0.200000, timescale=1.000000, dt_final=0.200000
cloth_solve--tf-- 1.000000
=== Physics step 0.000000 ===
DEBUG: cloth_setup_constraints() start
DEBUG: cloth_setup_constraints() end
1. Constraints setup completed
2. Forces cleared
DEBUG: cloth_calc_force() start - time=0.000000
cloth_calc_force called - mvert_num: 198, avg_spring_len: 0.036831
Gravity enabled: [0.000000, 0.000000, -0.019620]
  Spring 1 (active): type=12, ij=153, kl=176, restlen=0.059902, stiffness=0.000000
  Spring 2 (active): type=12, ij=132, kl=197, restlen=0.059063, stiffness=0.000000
  Spring 3 (active): type=12, ij=152, kl=197, restlen=0.060961, stiffness=0.000000
  Spring 4 (active): type=12, ij=153, kl=196, restlen=0.058732, stiffness=0.000000
  Spring 5 (active): type=12, ij=151, kl=196, restlen=0.061899, stiffness=0.000000
cloth_calc_force completed - total springs: 726 (active: 726, inactive: 0)
  ...and 721 more active springs not shown
DEBUG: cloth_calc_force() end
3. Forces calculated
DEBUG: SIM_mass_spring_solve_velocities() start (Blender) - dt=0.200000
DEBUG: SIM_mass_spring_solve_velocities() end (Blender) - status=1
4. Velocities solved - status: 1, iterations: 7
DEBUG: cloth_solve_collisions() start - step=0.000000, dt=0.200000
DEBUG: SIM_mass_spring_solve_positions() start (Blender) - dt=0.200000
DEBUG: SIM_mass_spring_solve_positions() end (Blender)
DEBUG: cloth_solve_collisions() - iteration 1/1
DEBUG: cloth_bvh_collision() start - step=0.000000, dt=0.200000
DEBUG: ret variables initialized - ret=0, ret2=0
DEBUG: cloth_bvh_collision() - cloth_bvh=000001FEBCD77D88, cloth=000001FEBB2173D8
DEBUG: cloth_bvh_collision() - single_frame=0, max_loops=2
DEBUG: bvhtree_update_from_cloth() start - moving=0, self=0
DEBUG: bvhtree_update_from_cloth() end
DEBUG: collision loop iteration 1 - resetting ret2 to 0
DEBUG: cloth_bvh_objcollisions_resolve() start - numcollobj=3, dt=0.200000
DEBUG: objcollisions_resolve - ret initialized to 0
DEBUG: objcollisions_resolve - iteration 0, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 0 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 70 vertices, ret now=70
DEBUG: objcollisions_resolve - iteration 1, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 1 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 70 vertices, ret now=140
DEBUG: objcollisions_resolve() end - returning ret=140
DEBUG: object collision resolve returned 140
DEBUG: after object collisions - ret=140 (added 140)
DEBUG: after object collisions - ret2=140 (added 140)
DEBUG: applying collision resolution - ret2=140, updating 198 vertices
DEBUG: end of collision loop round 1 - ret=140, ret2=140
DEBUG: collision loop iteration 2 - resetting ret2 to 0
DEBUG: cloth_bvh_objcollisions_resolve() start - numcollobj=3, dt=0.200000
DEBUG: objcollisions_resolve - ret initialized to 0
DEBUG: objcollisions_resolve - iteration 0, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 0 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 66 vertices, ret now=66
DEBUG: objcollisions_resolve - iteration 1, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 1 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 66 vertices, ret now=132
DEBUG: objcollisions_resolve() end - returning ret=132
DEBUG: object collision resolve returned 132
DEBUG: after object collisions - ret=272 (added 132)
DEBUG: after object collisions - ret2=272 (added 272)
DEBUG: applying collision resolution - ret2=272, updating 198 vertices
DEBUG: end of collision loop round 2 - ret=272, ret2=272
DEBUG: cloth_bvh_collision() end - ret=272, rounds=2
DEBUG: final return value - min(ret=272, 1) = 1
DEBUG: cloth_solve_collisions() end - completed 1 collision iterations
5. Collisions processed
DEBUG: SIM_mass_spring_solve_positions() start (Blender) - dt=0.200000
DEBUG: SIM_mass_spring_solve_positions() end (Blender)
6. Positions solved
DEBUG: SIM_mass_spring_apply_result() start (Blender)
DEBUG: SIM_mass_spring_apply_result() end (Blender)
7. Results applied
Pinned vertex 154: set to xconst=[0.000627,-0.091779,1.096204]
Pinned vertex 155: set to xconst=[0.025497,-0.087258,1.096185]
Pinned vertex 156: set to xconst=[0.048302,-0.074915,1.096065]
...and 19 more pinned vertices (total: 22)
8. Pinned vertices updated
=== Physics step 0.200000 ===
DEBUG: cloth_setup_constraints() start
DEBUG: cloth_setup_constraints() end
1. Constraints setup completed
2. Forces cleared
DEBUG: cloth_calc_force() start - time=0.200000
cloth_calc_force called - mvert_num: 198, avg_spring_len: 0.036831
Gravity enabled: [0.000000, 0.000000, -0.019620]
  Spring 1 (active): type=12, ij=153, kl=176, restlen=0.059902, stiffness=0.000000
  Spring 2 (active): type=12, ij=132, kl=197, restlen=0.059063, stiffness=0.000000
  Spring 3 (active): type=12, ij=152, kl=197, restlen=0.060961, stiffness=0.000000
  Spring 4 (active): type=12, ij=153, kl=196, restlen=0.058732, stiffness=0.000000
  Spring 5 (active): type=12, ij=151, kl=196, restlen=0.061899, stiffness=0.000000
cloth_calc_force completed - total springs: 726 (active: 726, inactive: 0)
  ...and 721 more active springs not shown
DEBUG: cloth_calc_force() end
3. Forces calculated
DEBUG: SIM_mass_spring_solve_velocities() start (Blender) - dt=0.200000
DEBUG: SIM_mass_spring_solve_velocities() end (Blender) - status=1
4. Velocities solved - status: 1, iterations: 8
DEBUG: cloth_solve_collisions() start - step=0.200000, dt=0.200000
DEBUG: SIM_mass_spring_solve_positions() start (Blender) - dt=0.200000
DEBUG: SIM_mass_spring_solve_positions() end (Blender)
DEBUG: cloth_solve_collisions() - iteration 1/1
DEBUG: cloth_bvh_collision() start - step=0.200000, dt=0.200000
DEBUG: ret variables initialized - ret=0, ret2=0
DEBUG: cloth_bvh_collision() - cloth_bvh=000001FEBCD77D88, cloth=000001FEBB2173D8
DEBUG: cloth_bvh_collision() - single_frame=0, max_loops=2
DEBUG: bvhtree_update_from_cloth() start - moving=0, self=0
DEBUG: bvhtree_update_from_cloth() end
DEBUG: collision loop iteration 1 - resetting ret2 to 0
DEBUG: cloth_bvh_objcollisions_resolve() start - numcollobj=3, dt=0.200000
DEBUG: objcollisions_resolve - ret initialized to 0
DEBUG: objcollisions_resolve - iteration 0, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 0 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 75 vertices, ret now=75
DEBUG: objcollisions_resolve - iteration 1, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 1 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 75 vertices, ret now=150
DEBUG: objcollisions_resolve() end - returning ret=150
DEBUG: object collision resolve returned 150
DEBUG: after object collisions - ret=150 (added 150)
DEBUG: after object collisions - ret2=150 (added 150)
DEBUG: applying collision resolution - ret2=150, updating 198 vertices
DEBUG: end of collision loop round 1 - ret=150, ret2=150
DEBUG: collision loop iteration 2 - resetting ret2 to 0
DEBUG: cloth_bvh_objcollisions_resolve() start - numcollobj=3, dt=0.200000
DEBUG: objcollisions_resolve - ret initialized to 0
DEBUG: objcollisions_resolve - iteration 0, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 0 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 68 vertices, ret now=68
DEBUG: objcollisions_resolve - iteration 1, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 1 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 68 vertices, ret now=136
DEBUG: objcollisions_resolve() end - returning ret=136
DEBUG: object collision resolve returned 136
DEBUG: after object collisions - ret=286 (added 136)
DEBUG: after object collisions - ret2=286 (added 286)
DEBUG: applying collision resolution - ret2=286, updating 198 vertices
DEBUG: end of collision loop round 2 - ret=286, ret2=286
DEBUG: cloth_bvh_collision() end - ret=286, rounds=2
DEBUG: final return value - min(ret=286, 1) = 1
DEBUG: cloth_solve_collisions() end - completed 1 collision iterations
5. Collisions processed
DEBUG: SIM_mass_spring_solve_positions() start (Blender) - dt=0.200000
DEBUG: SIM_mass_spring_solve_positions() end (Blender)
6. Positions solved
DEBUG: SIM_mass_spring_apply_result() start (Blender)
DEBUG: SIM_mass_spring_apply_result() end (Blender)
7. Results applied
Pinned vertex 154: set to xconst=[0.000627,-0.091779,1.096204]
Pinned vertex 155: set to xconst=[0.025497,-0.087258,1.096185]
Pinned vertex 156: set to xconst=[0.048302,-0.074915,1.096065]
...and 19 more pinned vertices (total: 22)
8. Pinned vertices updated
=== Physics step 0.400000 ===
DEBUG: cloth_setup_constraints() start
DEBUG: cloth_setup_constraints() end
1. Constraints setup completed
2. Forces cleared
DEBUG: cloth_calc_force() start - time=0.400000
cloth_calc_force called - mvert_num: 198, avg_spring_len: 0.036831
Gravity enabled: [0.000000, 0.000000, -0.019620]
  Spring 1 (active): type=12, ij=153, kl=176, restlen=0.059902, stiffness=0.000000
  Spring 2 (active): type=12, ij=132, kl=197, restlen=0.059063, stiffness=0.000000
  Spring 3 (active): type=12, ij=152, kl=197, restlen=0.060961, stiffness=0.000000
  Spring 4 (active): type=12, ij=153, kl=196, restlen=0.058732, stiffness=0.000000
  Spring 5 (active): type=12, ij=151, kl=196, restlen=0.061899, stiffness=0.000000
cloth_calc_force completed - total springs: 726 (active: 726, inactive: 0)
  ...and 721 more active springs not shown
DEBUG: cloth_calc_force() end
3. Forces calculated
DEBUG: SIM_mass_spring_solve_velocities() start (Blender) - dt=0.200000
DEBUG: SIM_mass_spring_solve_velocities() end (Blender) - status=1
4. Velocities solved - status: 1, iterations: 7
DEBUG: cloth_solve_collisions() start - step=0.400000, dt=0.200000
DEBUG: SIM_mass_spring_solve_positions() start (Blender) - dt=0.200000
DEBUG: SIM_mass_spring_solve_positions() end (Blender)
DEBUG: cloth_solve_collisions() - iteration 1/1
DEBUG: cloth_bvh_collision() start - step=0.400000, dt=0.200000
DEBUG: ret variables initialized - ret=0, ret2=0
DEBUG: cloth_bvh_collision() - cloth_bvh=000001FEBCD77D88, cloth=000001FEBB2173D8
DEBUG: cloth_bvh_collision() - single_frame=0, max_loops=2
DEBUG: bvhtree_update_from_cloth() start - moving=0, self=0
DEBUG: bvhtree_update_from_cloth() end
DEBUG: collision loop iteration 1 - resetting ret2 to 0
DEBUG: cloth_bvh_objcollisions_resolve() start - numcollobj=3, dt=0.200000
DEBUG: objcollisions_resolve - ret initialized to 0
DEBUG: objcollisions_resolve - iteration 0, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 0 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 77 vertices, ret now=77
DEBUG: objcollisions_resolve - iteration 1, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 1 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 77 vertices, ret now=154
DEBUG: objcollisions_resolve() end - returning ret=154
DEBUG: object collision resolve returned 154
DEBUG: after object collisions - ret=154 (added 154)
DEBUG: after object collisions - ret2=154 (added 154)
DEBUG: applying collision resolution - ret2=154, updating 198 vertices
DEBUG: end of collision loop round 1 - ret=154, ret2=154
DEBUG: collision loop iteration 2 - resetting ret2 to 0
DEBUG: cloth_bvh_objcollisions_resolve() start - numcollobj=3, dt=0.200000
DEBUG: objcollisions_resolve - ret initialized to 0
DEBUG: objcollisions_resolve - iteration 0, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 0 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 67 vertices, ret now=67
DEBUG: objcollisions_resolve - iteration 1, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 1 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 67 vertices, ret now=134
DEBUG: objcollisions_resolve() end - returning ret=134
DEBUG: object collision resolve returned 134
DEBUG: after object collisions - ret=288 (added 134)
DEBUG: after object collisions - ret2=288 (added 288)
DEBUG: applying collision resolution - ret2=288, updating 198 vertices
DEBUG: end of collision loop round 2 - ret=288, ret2=288
DEBUG: cloth_bvh_collision() end - ret=288, rounds=2
DEBUG: final return value - min(ret=288, 1) = 1
DEBUG: cloth_solve_collisions() end - completed 1 collision iterations
5. Collisions processed
DEBUG: SIM_mass_spring_solve_positions() start (Blender) - dt=0.200000
DEBUG: SIM_mass_spring_solve_positions() end (Blender)
6. Positions solved
DEBUG: SIM_mass_spring_apply_result() start (Blender)
DEBUG: SIM_mass_spring_apply_result() end (Blender)
7. Results applied
Pinned vertex 154: set to xconst=[0.000627,-0.091779,1.096204]
Pinned vertex 155: set to xconst=[0.025497,-0.087258,1.096185]
Pinned vertex 156: set to xconst=[0.048302,-0.074915,1.096065]
...and 19 more pinned vertices (total: 22)
8. Pinned vertices updated
=== Physics step 0.600000 ===
DEBUG: cloth_setup_constraints() start
DEBUG: cloth_setup_constraints() end
1. Constraints setup completed
2. Forces cleared
DEBUG: cloth_calc_force() start - time=0.600000
cloth_calc_force called - mvert_num: 198, avg_spring_len: 0.036831
Gravity enabled: [0.000000, 0.000000, -0.019620]
  Spring 1 (active): type=12, ij=153, kl=176, restlen=0.059902, stiffness=0.000000
  Spring 2 (active): type=12, ij=132, kl=197, restlen=0.059063, stiffness=0.000000
  Spring 3 (active): type=12, ij=152, kl=197, restlen=0.060961, stiffness=0.000000
  Spring 4 (active): type=12, ij=153, kl=196, restlen=0.058732, stiffness=0.000000
  Spring 5 (active): type=12, ij=151, kl=196, restlen=0.061899, stiffness=0.000000
cloth_calc_force completed - total springs: 726 (active: 726, inactive: 0)
  ...and 721 more active springs not shown
DEBUG: cloth_calc_force() end
3. Forces calculated
DEBUG: SIM_mass_spring_solve_velocities() start (Blender) - dt=0.200000
DEBUG: SIM_mass_spring_solve_velocities() end (Blender) - status=1
4. Velocities solved - status: 1, iterations: 8
DEBUG: cloth_solve_collisions() start - step=0.600000, dt=0.200000
DEBUG: SIM_mass_spring_solve_positions() start (Blender) - dt=0.200000
DEBUG: SIM_mass_spring_solve_positions() end (Blender)
DEBUG: cloth_solve_collisions() - iteration 1/1
DEBUG: cloth_bvh_collision() start - step=0.600000, dt=0.200000
DEBUG: ret variables initialized - ret=0, ret2=0
DEBUG: cloth_bvh_collision() - cloth_bvh=000001FEBCD77D88, cloth=000001FEBB2173D8
DEBUG: cloth_bvh_collision() - single_frame=0, max_loops=2
DEBUG: bvhtree_update_from_cloth() start - moving=0, self=0
DEBUG: bvhtree_update_from_cloth() end
DEBUG: collision loop iteration 1 - resetting ret2 to 0
DEBUG: cloth_bvh_objcollisions_resolve() start - numcollobj=3, dt=0.200000
DEBUG: objcollisions_resolve - ret initialized to 0
DEBUG: objcollisions_resolve - iteration 0, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 0 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 90 vertices, ret now=90
DEBUG: objcollisions_resolve - iteration 1, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 1 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 90 vertices, ret now=180
DEBUG: objcollisions_resolve() end - returning ret=180
DEBUG: object collision resolve returned 180
DEBUG: after object collisions - ret=180 (added 180)
DEBUG: after object collisions - ret2=180 (added 180)
DEBUG: applying collision resolution - ret2=180, updating 198 vertices
DEBUG: end of collision loop round 1 - ret=180, ret2=180
DEBUG: collision loop iteration 2 - resetting ret2 to 0
DEBUG: cloth_bvh_objcollisions_resolve() start - numcollobj=3, dt=0.200000
DEBUG: objcollisions_resolve - ret initialized to 0
DEBUG: objcollisions_resolve - iteration 0, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 0 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 67 vertices, ret now=67
DEBUG: objcollisions_resolve - iteration 1, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 1 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 67 vertices, ret now=134
DEBUG: objcollisions_resolve() end - returning ret=134
DEBUG: object collision resolve returned 134
DEBUG: after object collisions - ret=314 (added 134)
DEBUG: after object collisions - ret2=314 (added 314)
DEBUG: applying collision resolution - ret2=314, updating 198 vertices
DEBUG: end of collision loop round 2 - ret=314, ret2=314
DEBUG: cloth_bvh_collision() end - ret=314, rounds=2
DEBUG: final return value - min(ret=314, 1) = 1
DEBUG: cloth_solve_collisions() end - completed 1 collision iterations
5. Collisions processed
DEBUG: SIM_mass_spring_solve_positions() start (Blender) - dt=0.200000
DEBUG: SIM_mass_spring_solve_positions() end (Blender)
6. Positions solved
DEBUG: SIM_mass_spring_apply_result() start (Blender)
DEBUG: SIM_mass_spring_apply_result() end (Blender)
7. Results applied
Pinned vertex 154: set to xconst=[0.000627,-0.091779,1.096204]
Pinned vertex 155: set to xconst=[0.025497,-0.087258,1.096185]
Pinned vertex 156: set to xconst=[0.048302,-0.074915,1.096065]
...and 19 more pinned vertices (total: 22)
8. Pinned vertices updated
=== Physics step 0.800000 ===
DEBUG: cloth_setup_constraints() start
DEBUG: cloth_setup_constraints() end
1. Constraints setup completed
2. Forces cleared
DEBUG: cloth_calc_force() start - time=0.800000
cloth_calc_force called - mvert_num: 198, avg_spring_len: 0.036831
Gravity enabled: [0.000000, 0.000000, -0.019620]
  Spring 1 (active): type=12, ij=153, kl=176, restlen=0.059902, stiffness=0.000000
  Spring 2 (active): type=12, ij=132, kl=197, restlen=0.059063, stiffness=0.000000
  Spring 3 (active): type=12, ij=152, kl=197, restlen=0.060961, stiffness=0.000000
  Spring 4 (active): type=12, ij=153, kl=196, restlen=0.058732, stiffness=0.000000
  Spring 5 (active): type=12, ij=151, kl=196, restlen=0.061899, stiffness=0.000000
cloth_calc_force completed - total springs: 726 (active: 726, inactive: 0)
  ...and 721 more active springs not shown
DEBUG: cloth_calc_force() end
3. Forces calculated
DEBUG: SIM_mass_spring_solve_velocities() start (Blender) - dt=0.200000
DEBUG: SIM_mass_spring_solve_velocities() end (Blender) - status=1
4. Velocities solved - status: 1, iterations: 9
DEBUG: cloth_solve_collisions() start - step=0.800000, dt=0.200000
DEBUG: SIM_mass_spring_solve_positions() start (Blender) - dt=0.200000
DEBUG: SIM_mass_spring_solve_positions() end (Blender)
DEBUG: cloth_solve_collisions() - iteration 1/1
DEBUG: cloth_bvh_collision() start - step=0.800000, dt=0.200000
DEBUG: ret variables initialized - ret=0, ret2=0
DEBUG: cloth_bvh_collision() - cloth_bvh=000001FEBCD77D88, cloth=000001FEBB2173D8
DEBUG: cloth_bvh_collision() - single_frame=0, max_loops=2
DEBUG: bvhtree_update_from_cloth() start - moving=0, self=0
DEBUG: bvhtree_update_from_cloth() end
DEBUG: collision loop iteration 1 - resetting ret2 to 0
DEBUG: cloth_bvh_objcollisions_resolve() start - numcollobj=3, dt=0.200000
DEBUG: objcollisions_resolve - ret initialized to 0
DEBUG: objcollisions_resolve - iteration 0, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 0 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 95 vertices, ret now=95
DEBUG: objcollisions_resolve - iteration 1, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 1 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 95 vertices, ret now=190
DEBUG: objcollisions_resolve() end - returning ret=190
DEBUG: object collision resolve returned 190
DEBUG: after object collisions - ret=190 (added 190)
DEBUG: after object collisions - ret2=190 (added 190)
DEBUG: applying collision resolution - ret2=190, updating 198 vertices
DEBUG: end of collision loop round 1 - ret=190, ret2=190
DEBUG: collision loop iteration 2 - resetting ret2 to 0
DEBUG: cloth_bvh_objcollisions_resolve() start - numcollobj=3, dt=0.200000
DEBUG: objcollisions_resolve - ret initialized to 0
DEBUG: objcollisions_resolve - iteration 0, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 0 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 73 vertices, ret now=73
DEBUG: objcollisions_resolve - iteration 1, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 1 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 72 vertices, ret now=145
DEBUG: objcollisions_resolve() end - returning ret=145
DEBUG: object collision resolve returned 145
DEBUG: after object collisions - ret=335 (added 145)
DEBUG: after object collisions - ret2=335 (added 335)
DEBUG: applying collision resolution - ret2=335, updating 198 vertices
DEBUG: end of collision loop round 2 - ret=335, ret2=335
DEBUG: cloth_bvh_collision() end - ret=335, rounds=2
DEBUG: final return value - min(ret=335, 1) = 1
DEBUG: cloth_solve_collisions() end - completed 1 collision iterations
5. Collisions processed
DEBUG: SIM_mass_spring_solve_positions() start (Blender) - dt=0.200000
DEBUG: SIM_mass_spring_solve_positions() end (Blender)
6. Positions solved
DEBUG: SIM_mass_spring_apply_result() start (Blender)
DEBUG: SIM_mass_spring_apply_result() end (Blender)
7. Results applied
Pinned vertex 154: set to xconst=[0.000627,-0.091779,1.096204]
Pinned vertex 155: set to xconst=[0.025497,-0.087258,1.096185]
Pinned vertex 156: set to xconst=[0.048302,-0.074915,1.096065]
...and 19 more pinned vertices (total: 22)
8. Pinned vertices updated
=== Final vertex states ===
Vertex 0 (free): pos=[0.001152,-0.099007,1.055018] vel=[0.001207,0.006884,-0.002798]
Vertex 1 (free): pos=[0.028989,-0.094597,1.055248] vel=[0.001430,0.006880,-0.002450]
Vertex 2 (free): pos=[0.054141,-0.080690,1.054875] vel=[0.001115,0.007867,-0.002481]
Vertex 154 (pinned): pos=[0.000627,-0.091779,1.096204]
Vertex 155 (pinned): pos=[0.025497,-0.087258,1.096185]
Vertex 156 (pinned): pos=[0.048302,-0.074915,1.096065]
Summary: 198 total vertices (176 free, 22 pinned)
DEBUG: SIM_cloth_solve() end - success
DEBUG: do_step_cloth() - SIM_cloth_solve returned 1
DEBUG: do_step_cloth() - calling BKE_effectors_free
DEBUG: do_step_cloth() end - ret=1
DEBUG: clothModifier_do() - do_step_cloth success
DEBUG: clothModifier_do() - writing cache (normal mode)
DEBUG: clothModifier_do() - calling cloth_to_object
DEBUG: cloth_to_object() start
cloth_to_object: copying 198 vertices to mesh
DEBUG: cloth_is_upbge_single_frame_mode() start - current_frame=2, last_frame=2
DEBUG: cloth_is_upbge_single_frame_mode() end - result=1
  Free Vertex 0: using x=[0.001152,-0.099007,1.055018] -> mesh_pos=[0.001152,-0.099007,1.014441]
  Free Vertex 1: using x=[0.028989,-0.094597,1.055248] -> mesh_pos=[0.028989,-0.094597,1.014662]
  Free Vertex 2: using x=[0.054141,-0.080690,1.054875] -> mesh_pos=[0.054141,-0.080690,1.014303]
  Pinned Vertex 154: using xconst=[0.000627,-0.091779,1.096204] -> mesh_pos=[0.000627,-0.091779,1.054042]
  Pinned Vertex 155: using xconst=[0.025497,-0.087258,1.096185] -> mesh_pos=[0.025497,-0.087258,1.054024]
  Pinned Vertex 156: using xconst=[0.048302,-0.074915,1.096065] -> mesh_pos=[0.048302,-0.074915,1.053909]
  Total vertices processed: 198 (pinned=22, free=176)
UPBGE single frame mode: using pinned vertex constraint positions
DEBUG: cloth_to_object() end
DEBUG: clothModifier_do() end - success
MOD_cloth: copying cloth results back to mesh positions
  MOD_cloth Vertex 0: pos=[0.001152,-0.099007,1.014441]
  MOD_cloth Vertex 1: pos=[0.028989,-0.094597,1.014662]
  MOD_cloth Vertex 2: pos=[0.054141,-0.080690,1.014303]
  MOD_cloth Vertex 3: pos=[0.075752,-0.059908,1.014001]
  MOD_cloth Vertex 4: pos=[0.092182,-0.033575,1.014274]
MOD_cloth: forced object data re-evaluation
Should simulate...
DEBUG: clothModifier_do() start
DEBUG: clothModifier_do() - framenr=4
DEBUG: clothModifier_do() - calling do_init_cloth
DEBUG: do_init_cloth() start - framenr=4
DEBUG: do_init_cloth() end - success
DEBUG: clothModifier_do() - do_init_cloth success
need_first_init = 0
DEBUG: cloth_is_upbge_single_frame_mode() start - current_frame=4, last_frame=3
DEBUG: cloth_is_upbge_single_frame_mode() end - result=0
cache_flag_baked = 1 --- is_single_frame_mode = 0 --- can_simulate = 1
framenr = 4, last_frame = 3, timescale = 1.000000
DEBUG: clothModifier_do() - calling BKE_ptcache_read
DEBUG: clothModifier_do() - cache_result=0
DEBUG: clothModifier_do() - starting simulation
DEBUG: clothModifier_do() - calling do_step_cloth
DEBUG: do_step_cloth() start - framenr=4
Object matrix unchanged
Vertex 0: local=[-0.000018,-0.111819,1.024277] world=[-0.000018,-0.111819,1.065248]
DEBUG: do_step_cloth() - calling BKE_effectors_create
DEBUG: do_step_cloth() - calling cloth_apply_vgroup
DEBUG: cloth_apply_vgroup() start
DEBUG: cloth_apply_vgroup() end
DEBUG: do_step_cloth() - calling cloth_update_springs
DEBUG: cloth_update_springs() start
DEBUG: cloth_update_springs() end
DEBUG: do_step_cloth() - calling SIM_cloth_solve
DEBUG: SIM_cloth_solve() start - frame=4.000000
DEBUG: SIM_cloth_solve() - is_hair=0
SIM_cloth_solve: tf=1.000000, dt_base=0.200000, timescale=1.000000, dt_final=0.200000
cloth_solve--tf-- 1.000000
=== Physics step 0.000000 ===
DEBUG: cloth_setup_constraints() start
DEBUG: cloth_setup_constraints() end
1. Constraints setup completed
2. Forces cleared
DEBUG: cloth_calc_force() start - time=0.000000
cloth_calc_force called - mvert_num: 198, avg_spring_len: 0.036831
Gravity enabled: [0.000000, 0.000000, -0.019620]
  Spring 1 (active): type=12, ij=153, kl=176, restlen=0.059902, stiffness=0.000000
  Spring 2 (active): type=12, ij=132, kl=197, restlen=0.059063, stiffness=0.000000
  Spring 3 (active): type=12, ij=152, kl=197, restlen=0.060961, stiffness=0.000000
  Spring 4 (active): type=12, ij=153, kl=196, restlen=0.058732, stiffness=0.000000
  Spring 5 (active): type=12, ij=151, kl=196, restlen=0.061899, stiffness=0.000000
cloth_calc_force completed - total springs: 726 (active: 726, inactive: 0)
  ...and 721 more active springs not shown
DEBUG: cloth_calc_force() end
3. Forces calculated
DEBUG: SIM_mass_spring_solve_velocities() start (Blender) - dt=0.200000
DEBUG: SIM_mass_spring_solve_velocities() end (Blender) - status=1
4. Velocities solved - status: 1, iterations: 9
DEBUG: cloth_solve_collisions() start - step=0.000000, dt=0.200000
DEBUG: SIM_mass_spring_solve_positions() start (Blender) - dt=0.200000
DEBUG: SIM_mass_spring_solve_positions() end (Blender)
DEBUG: cloth_solve_collisions() - iteration 1/1
DEBUG: cloth_bvh_collision() start - step=0.000000, dt=0.200000
DEBUG: ret variables initialized - ret=0, ret2=0
DEBUG: cloth_bvh_collision() - cloth_bvh=000001FEBCD77D88, cloth=000001FEBB2173D8
DEBUG: cloth_bvh_collision() - single_frame=0, max_loops=2
DEBUG: bvhtree_update_from_cloth() start - moving=0, self=0
DEBUG: bvhtree_update_from_cloth() end
DEBUG: collision loop iteration 1 - resetting ret2 to 0
DEBUG: cloth_bvh_objcollisions_resolve() start - numcollobj=3, dt=0.200000
DEBUG: objcollisions_resolve - ret initialized to 0
DEBUG: objcollisions_resolve - iteration 0, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 0 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 93 vertices, ret now=93
DEBUG: objcollisions_resolve - iteration 1, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 1 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 93 vertices, ret now=186
DEBUG: objcollisions_resolve() end - returning ret=186
DEBUG: object collision resolve returned 186
DEBUG: after object collisions - ret=186 (added 186)
DEBUG: after object collisions - ret2=186 (added 186)
DEBUG: applying collision resolution - ret2=186, updating 198 vertices
DEBUG: end of collision loop round 1 - ret=186, ret2=186
DEBUG: collision loop iteration 2 - resetting ret2 to 0
DEBUG: cloth_bvh_objcollisions_resolve() start - numcollobj=3, dt=0.200000
DEBUG: objcollisions_resolve - ret initialized to 0
DEBUG: objcollisions_resolve - iteration 0, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 0 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 76 vertices, ret now=76
DEBUG: objcollisions_resolve - iteration 1, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 1 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 76 vertices, ret now=152
DEBUG: objcollisions_resolve() end - returning ret=152
DEBUG: object collision resolve returned 152
DEBUG: after object collisions - ret=338 (added 152)
DEBUG: after object collisions - ret2=338 (added 338)
DEBUG: applying collision resolution - ret2=338, updating 198 vertices
DEBUG: end of collision loop round 2 - ret=338, ret2=338
DEBUG: cloth_bvh_collision() end - ret=338, rounds=2
DEBUG: final return value - min(ret=338, 1) = 1
DEBUG: cloth_solve_collisions() end - completed 1 collision iterations
5. Collisions processed
DEBUG: SIM_mass_spring_solve_positions() start (Blender) - dt=0.200000
DEBUG: SIM_mass_spring_solve_positions() end (Blender)
6. Positions solved
DEBUG: SIM_mass_spring_apply_result() start (Blender)
DEBUG: SIM_mass_spring_apply_result() end (Blender)
7. Results applied
Pinned vertex 154: set to xconst=[0.000951,-0.086557,1.093563]
Pinned vertex 155: set to xconst=[0.025779,-0.081809,1.093566]
Pinned vertex 156: set to xconst=[0.048470,-0.069258,1.093464]
...and 19 more pinned vertices (total: 22)
8. Pinned vertices updated
=== Physics step 0.200000 ===
DEBUG: cloth_setup_constraints() start
DEBUG: cloth_setup_constraints() end
1. Constraints setup completed
2. Forces cleared
DEBUG: cloth_calc_force() start - time=0.200000
cloth_calc_force called - mvert_num: 198, avg_spring_len: 0.036831
Gravity enabled: [0.000000, 0.000000, -0.019620]
  Spring 1 (active): type=12, ij=153, kl=176, restlen=0.059902, stiffness=0.000000
  Spring 2 (active): type=12, ij=132, kl=197, restlen=0.059063, stiffness=0.000000
  Spring 3 (active): type=12, ij=152, kl=197, restlen=0.060961, stiffness=0.000000
  Spring 4 (active): type=12, ij=153, kl=196, restlen=0.058732, stiffness=0.000000
  Spring 5 (active): type=12, ij=151, kl=196, restlen=0.061899, stiffness=0.000000
cloth_calc_force completed - total springs: 726 (active: 726, inactive: 0)
  ...and 721 more active springs not shown
DEBUG: cloth_calc_force() end
3. Forces calculated
DEBUG: SIM_mass_spring_solve_velocities() start (Blender) - dt=0.200000
DEBUG: SIM_mass_spring_solve_velocities() end (Blender) - status=1
4. Velocities solved - status: 1, iterations: 9
DEBUG: cloth_solve_collisions() start - step=0.200000, dt=0.200000
DEBUG: SIM_mass_spring_solve_positions() start (Blender) - dt=0.200000
DEBUG: SIM_mass_spring_solve_positions() end (Blender)
DEBUG: cloth_solve_collisions() - iteration 1/1
DEBUG: cloth_bvh_collision() start - step=0.200000, dt=0.200000
DEBUG: ret variables initialized - ret=0, ret2=0
DEBUG: cloth_bvh_collision() - cloth_bvh=000001FEBCD77D88, cloth=000001FEBB2173D8
DEBUG: cloth_bvh_collision() - single_frame=0, max_loops=2
DEBUG: bvhtree_update_from_cloth() start - moving=0, self=0
DEBUG: bvhtree_update_from_cloth() end
DEBUG: collision loop iteration 1 - resetting ret2 to 0
DEBUG: cloth_bvh_objcollisions_resolve() start - numcollobj=3, dt=0.200000
DEBUG: objcollisions_resolve - ret initialized to 0
DEBUG: objcollisions_resolve - iteration 0, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 0 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 98 vertices, ret now=98
DEBUG: objcollisions_resolve - iteration 1, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 1 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 98 vertices, ret now=196
DEBUG: objcollisions_resolve() end - returning ret=196
DEBUG: object collision resolve returned 196
DEBUG: after object collisions - ret=196 (added 196)
DEBUG: after object collisions - ret2=196 (added 196)
DEBUG: applying collision resolution - ret2=196, updating 198 vertices
DEBUG: end of collision loop round 1 - ret=196, ret2=196
DEBUG: collision loop iteration 2 - resetting ret2 to 0
DEBUG: cloth_bvh_objcollisions_resolve() start - numcollobj=3, dt=0.200000
DEBUG: objcollisions_resolve - ret initialized to 0
DEBUG: objcollisions_resolve - iteration 0, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 0 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 77 vertices, ret now=77
DEBUG: objcollisions_resolve - iteration 1, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 1 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 77 vertices, ret now=154
DEBUG: objcollisions_resolve() end - returning ret=154
DEBUG: object collision resolve returned 154
DEBUG: after object collisions - ret=350 (added 154)
DEBUG: after object collisions - ret2=350 (added 350)
DEBUG: applying collision resolution - ret2=350, updating 198 vertices
DEBUG: end of collision loop round 2 - ret=350, ret2=350
DEBUG: cloth_bvh_collision() end - ret=350, rounds=2
DEBUG: final return value - min(ret=350, 1) = 1
DEBUG: cloth_solve_collisions() end - completed 1 collision iterations
5. Collisions processed
DEBUG: SIM_mass_spring_solve_positions() start (Blender) - dt=0.200000
DEBUG: SIM_mass_spring_solve_positions() end (Blender)
6. Positions solved
DEBUG: SIM_mass_spring_apply_result() start (Blender)
DEBUG: SIM_mass_spring_apply_result() end (Blender)
7. Results applied
Pinned vertex 154: set to xconst=[0.000951,-0.086557,1.093563]
Pinned vertex 155: set to xconst=[0.025779,-0.081809,1.093566]
Pinned vertex 156: set to xconst=[0.048470,-0.069258,1.093464]
...and 19 more pinned vertices (total: 22)
8. Pinned vertices updated
=== Physics step 0.400000 ===
DEBUG: cloth_setup_constraints() start
DEBUG: cloth_setup_constraints() end
1. Constraints setup completed
2. Forces cleared
DEBUG: cloth_calc_force() start - time=0.400000
cloth_calc_force called - mvert_num: 198, avg_spring_len: 0.036831
Gravity enabled: [0.000000, 0.000000, -0.019620]
  Spring 1 (active): type=12, ij=153, kl=176, restlen=0.059902, stiffness=0.000000
  Spring 2 (active): type=12, ij=132, kl=197, restlen=0.059063, stiffness=0.000000
  Spring 3 (active): type=12, ij=152, kl=197, restlen=0.060961, stiffness=0.000000
  Spring 4 (active): type=12, ij=153, kl=196, restlen=0.058732, stiffness=0.000000
  Spring 5 (active): type=12, ij=151, kl=196, restlen=0.061899, stiffness=0.000000
cloth_calc_force completed - total springs: 726 (active: 726, inactive: 0)
  ...and 721 more active springs not shown
DEBUG: cloth_calc_force() end
3. Forces calculated
DEBUG: SIM_mass_spring_solve_velocities() start (Blender) - dt=0.200000
DEBUG: SIM_mass_spring_solve_velocities() end (Blender) - status=1
4. Velocities solved - status: 1, iterations: 8
DEBUG: cloth_solve_collisions() start - step=0.400000, dt=0.200000
DEBUG: SIM_mass_spring_solve_positions() start (Blender) - dt=0.200000
DEBUG: SIM_mass_spring_solve_positions() end (Blender)
DEBUG: cloth_solve_collisions() - iteration 1/1
DEBUG: cloth_bvh_collision() start - step=0.400000, dt=0.200000
DEBUG: ret variables initialized - ret=0, ret2=0
DEBUG: cloth_bvh_collision() - cloth_bvh=000001FEBCD77D88, cloth=000001FEBB2173D8
DEBUG: cloth_bvh_collision() - single_frame=0, max_loops=2
DEBUG: bvhtree_update_from_cloth() start - moving=0, self=0
DEBUG: bvhtree_update_from_cloth() end
DEBUG: collision loop iteration 1 - resetting ret2 to 0
DEBUG: cloth_bvh_objcollisions_resolve() start - numcollobj=3, dt=0.200000
DEBUG: objcollisions_resolve - ret initialized to 0
DEBUG: objcollisions_resolve - iteration 0, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 0 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 99 vertices, ret now=99
DEBUG: objcollisions_resolve - iteration 1, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 1 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 99 vertices, ret now=198
DEBUG: objcollisions_resolve() end - returning ret=198
DEBUG: object collision resolve returned 198
DEBUG: after object collisions - ret=198 (added 198)
DEBUG: after object collisions - ret2=198 (added 198)
DEBUG: applying collision resolution - ret2=198, updating 198 vertices
DEBUG: end of collision loop round 1 - ret=198, ret2=198
DEBUG: collision loop iteration 2 - resetting ret2 to 0
DEBUG: cloth_bvh_objcollisions_resolve() start - numcollobj=3, dt=0.200000
DEBUG: objcollisions_resolve - ret initialized to 0
DEBUG: objcollisions_resolve - iteration 0, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 0 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 86 vertices, ret now=86
DEBUG: objcollisions_resolve - iteration 1, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 1 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 86 vertices, ret now=172
DEBUG: objcollisions_resolve() end - returning ret=172
DEBUG: object collision resolve returned 172
DEBUG: after object collisions - ret=370 (added 172)
DEBUG: after object collisions - ret2=370 (added 370)
DEBUG: applying collision resolution - ret2=370, updating 198 vertices
DEBUG: end of collision loop round 2 - ret=370, ret2=370
DEBUG: cloth_bvh_collision() end - ret=370, rounds=2
DEBUG: final return value - min(ret=370, 1) = 1
DEBUG: cloth_solve_collisions() end - completed 1 collision iterations
5. Collisions processed
DEBUG: SIM_mass_spring_solve_positions() start (Blender) - dt=0.200000
DEBUG: SIM_mass_spring_solve_positions() end (Blender)
6. Positions solved
DEBUG: SIM_mass_spring_apply_result() start (Blender)
DEBUG: SIM_mass_spring_apply_result() end (Blender)
7. Results applied
Pinned vertex 154: set to xconst=[0.000951,-0.086557,1.093563]
Pinned vertex 155: set to xconst=[0.025779,-0.081809,1.093566]
Pinned vertex 156: set to xconst=[0.048470,-0.069258,1.093464]
...and 19 more pinned vertices (total: 22)
8. Pinned vertices updated
=== Physics step 0.600000 ===
DEBUG: cloth_setup_constraints() start
DEBUG: cloth_setup_constraints() end
1. Constraints setup completed
2. Forces cleared
DEBUG: cloth_calc_force() start - time=0.600000
cloth_calc_force called - mvert_num: 198, avg_spring_len: 0.036831
Gravity enabled: [0.000000, 0.000000, -0.019620]
  Spring 1 (active): type=12, ij=153, kl=176, restlen=0.059902, stiffness=0.000000
  Spring 2 (active): type=12, ij=132, kl=197, restlen=0.059063, stiffness=0.000000
  Spring 3 (active): type=12, ij=152, kl=197, restlen=0.060961, stiffness=0.000000
  Spring 4 (active): type=12, ij=153, kl=196, restlen=0.058732, stiffness=0.000000
  Spring 5 (active): type=12, ij=151, kl=196, restlen=0.061899, stiffness=0.000000
cloth_calc_force completed - total springs: 726 (active: 726, inactive: 0)
  ...and 721 more active springs not shown
DEBUG: cloth_calc_force() end
3. Forces calculated
DEBUG: SIM_mass_spring_solve_velocities() start (Blender) - dt=0.200000
DEBUG: SIM_mass_spring_solve_velocities() end (Blender) - status=1
4. Velocities solved - status: 1, iterations: 9
DEBUG: cloth_solve_collisions() start - step=0.600000, dt=0.200000
DEBUG: SIM_mass_spring_solve_positions() start (Blender) - dt=0.200000
DEBUG: SIM_mass_spring_solve_positions() end (Blender)
DEBUG: cloth_solve_collisions() - iteration 1/1
DEBUG: cloth_bvh_collision() start - step=0.600000, dt=0.200000
DEBUG: ret variables initialized - ret=0, ret2=0
DEBUG: cloth_bvh_collision() - cloth_bvh=000001FEBCD77D88, cloth=000001FEBB2173D8
DEBUG: cloth_bvh_collision() - single_frame=0, max_loops=2
DEBUG: bvhtree_update_from_cloth() start - moving=0, self=0
DEBUG: bvhtree_update_from_cloth() end
DEBUG: collision loop iteration 1 - resetting ret2 to 0
DEBUG: cloth_bvh_objcollisions_resolve() start - numcollobj=3, dt=0.200000
DEBUG: objcollisions_resolve - ret initialized to 0
DEBUG: objcollisions_resolve - iteration 0, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 0 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 96 vertices, ret now=96
DEBUG: objcollisions_resolve - iteration 1, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 1 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 96 vertices, ret now=192
DEBUG: objcollisions_resolve() end - returning ret=192
DEBUG: object collision resolve returned 192
DEBUG: after object collisions - ret=192 (added 192)
DEBUG: after object collisions - ret2=192 (added 192)
DEBUG: applying collision resolution - ret2=192, updating 198 vertices
DEBUG: end of collision loop round 1 - ret=192, ret2=192
DEBUG: collision loop iteration 2 - resetting ret2 to 0
DEBUG: cloth_bvh_objcollisions_resolve() start - numcollobj=3, dt=0.200000
DEBUG: objcollisions_resolve - ret initialized to 0
DEBUG: objcollisions_resolve - iteration 0, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 0 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 87 vertices, ret now=87
DEBUG: objcollisions_resolve - iteration 1, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 1 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 87 vertices, ret now=174
DEBUG: objcollisions_resolve() end - returning ret=174
DEBUG: object collision resolve returned 174
DEBUG: after object collisions - ret=366 (added 174)
DEBUG: after object collisions - ret2=366 (added 366)
DEBUG: applying collision resolution - ret2=366, updating 198 vertices
DEBUG: end of collision loop round 2 - ret=366, ret2=366
DEBUG: cloth_bvh_collision() end - ret=366, rounds=2
DEBUG: final return value - min(ret=366, 1) = 1
DEBUG: cloth_solve_collisions() end - completed 1 collision iterations
5. Collisions processed
DEBUG: SIM_mass_spring_solve_positions() start (Blender) - dt=0.200000
DEBUG: SIM_mass_spring_solve_positions() end (Blender)
6. Positions solved
DEBUG: SIM_mass_spring_apply_result() start (Blender)
DEBUG: SIM_mass_spring_apply_result() end (Blender)
7. Results applied
Pinned vertex 154: set to xconst=[0.000951,-0.086557,1.093563]
Pinned vertex 155: set to xconst=[0.025779,-0.081809,1.093566]
Pinned vertex 156: set to xconst=[0.048470,-0.069258,1.093464]
...and 19 more pinned vertices (total: 22)
8. Pinned vertices updated
=== Physics step 0.800000 ===
DEBUG: cloth_setup_constraints() start
DEBUG: cloth_setup_constraints() end
1. Constraints setup completed
2. Forces cleared
DEBUG: cloth_calc_force() start - time=0.800000
cloth_calc_force called - mvert_num: 198, avg_spring_len: 0.036831
Gravity enabled: [0.000000, 0.000000, -0.019620]
  Spring 1 (active): type=12, ij=153, kl=176, restlen=0.059902, stiffness=0.000000
  Spring 2 (active): type=12, ij=132, kl=197, restlen=0.059063, stiffness=0.000000
  Spring 3 (active): type=12, ij=152, kl=197, restlen=0.060961, stiffness=0.000000
  Spring 4 (active): type=12, ij=153, kl=196, restlen=0.058732, stiffness=0.000000
  Spring 5 (active): type=12, ij=151, kl=196, restlen=0.061899, stiffness=0.000000
cloth_calc_force completed - total springs: 726 (active: 726, inactive: 0)
  ...and 721 more active springs not shown
DEBUG: cloth_calc_force() end
3. Forces calculated
DEBUG: SIM_mass_spring_solve_velocities() start (Blender) - dt=0.200000
DEBUG: SIM_mass_spring_solve_velocities() end (Blender) - status=1
4. Velocities solved - status: 1, iterations: 9
DEBUG: cloth_solve_collisions() start - step=0.800000, dt=0.200000
DEBUG: SIM_mass_spring_solve_positions() start (Blender) - dt=0.200000
DEBUG: SIM_mass_spring_solve_positions() end (Blender)
DEBUG: cloth_solve_collisions() - iteration 1/1
DEBUG: cloth_bvh_collision() start - step=0.800000, dt=0.200000
DEBUG: ret variables initialized - ret=0, ret2=0
DEBUG: cloth_bvh_collision() - cloth_bvh=000001FEBCD77D88, cloth=000001FEBB2173D8
DEBUG: cloth_bvh_collision() - single_frame=0, max_loops=2
DEBUG: bvhtree_update_from_cloth() start - moving=0, self=0
DEBUG: bvhtree_update_from_cloth() end
DEBUG: collision loop iteration 1 - resetting ret2 to 0
DEBUG: cloth_bvh_objcollisions_resolve() start - numcollobj=3, dt=0.200000
DEBUG: objcollisions_resolve - ret initialized to 0
DEBUG: objcollisions_resolve - iteration 0, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 0 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 95 vertices, ret now=95
DEBUG: objcollisions_resolve - iteration 1, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 1 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 95 vertices, ret now=190
DEBUG: objcollisions_resolve() end - returning ret=190
DEBUG: object collision resolve returned 190
DEBUG: after object collisions - ret=190 (added 190)
DEBUG: after object collisions - ret2=190 (added 190)
DEBUG: applying collision resolution - ret2=190, updating 198 vertices
DEBUG: end of collision loop round 1 - ret=190, ret2=190
DEBUG: collision loop iteration 2 - resetting ret2 to 0
DEBUG: cloth_bvh_objcollisions_resolve() start - numcollobj=3, dt=0.200000
DEBUG: objcollisions_resolve - ret initialized to 0
DEBUG: objcollisions_resolve - iteration 0, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 0 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 89 vertices, ret now=89
DEBUG: objcollisions_resolve - iteration 1, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 1 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 89 vertices, ret now=178
DEBUG: objcollisions_resolve() end - returning ret=178
DEBUG: object collision resolve returned 178
DEBUG: after object collisions - ret=368 (added 178)
DEBUG: after object collisions - ret2=368 (added 368)
DEBUG: applying collision resolution - ret2=368, updating 198 vertices
DEBUG: end of collision loop round 2 - ret=368, ret2=368
DEBUG: cloth_bvh_collision() end - ret=368, rounds=2
DEBUG: final return value - min(ret=368, 1) = 1
DEBUG: cloth_solve_collisions() end - completed 1 collision iterations
5. Collisions processed
DEBUG: SIM_mass_spring_solve_positions() start (Blender) - dt=0.200000
DEBUG: SIM_mass_spring_solve_positions() end (Blender)
6. Positions solved
DEBUG: SIM_mass_spring_apply_result() start (Blender)
DEBUG: SIM_mass_spring_apply_result() end (Blender)
7. Results applied
Pinned vertex 154: set to xconst=[0.000951,-0.086557,1.093563]
Pinned vertex 155: set to xconst=[0.025779,-0.081809,1.093566]
Pinned vertex 156: set to xconst=[0.048470,-0.069258,1.093464]
...and 19 more pinned vertices (total: 22)
8. Pinned vertices updated
=== Final vertex states ===
Vertex 0 (free): pos=[0.001632,-0.094823,1.053181] vel=[-0.000163,0.003878,0.000109]
Vertex 1 (free): pos=[0.030034,-0.089226,1.053246] vel=[0.000089,0.004461,-0.000041]
Vertex 2 (free): pos=[0.055799,-0.075311,1.053094] vel=[0.000659,0.004063,0.000305]
Vertex 154 (pinned): pos=[0.000951,-0.086557,1.093563]
Vertex 155 (pinned): pos=[0.025779,-0.081809,1.093566]
Vertex 156 (pinned): pos=[0.048470,-0.069258,1.093464]
Summary: 198 total vertices (176 free, 22 pinned)
DEBUG: SIM_cloth_solve() end - success
DEBUG: do_step_cloth() - SIM_cloth_solve returned 1
DEBUG: do_step_cloth() - calling BKE_effectors_free
DEBUG: do_step_cloth() end - ret=1
DEBUG: clothModifier_do() - do_step_cloth success
DEBUG: clothModifier_do() - writing cache (normal mode)
DEBUG: clothModifier_do() - calling cloth_to_object
DEBUG: cloth_to_object() start
cloth_to_object: copying 198 vertices to mesh
DEBUG: cloth_is_upbge_single_frame_mode() start - current_frame=3, last_frame=3
DEBUG: cloth_is_upbge_single_frame_mode() end - result=1
  Free Vertex 0: using x=[0.001632,-0.094823,1.053181] -> mesh_pos=[0.001632,-0.094823,1.012674]
  Free Vertex 1: using x=[0.030034,-0.089226,1.053246] -> mesh_pos=[0.030034,-0.089226,1.012736]
  Free Vertex 2: using x=[0.055799,-0.075311,1.053094] -> mesh_pos=[0.055799,-0.075311,1.012591]
  Pinned Vertex 154: using xconst=[0.000951,-0.086557,1.093563] -> mesh_pos=[0.000951,-0.086557,1.051503]
  Pinned Vertex 155: using xconst=[0.025779,-0.081809,1.093566] -> mesh_pos=[0.025779,-0.081809,1.051505]
  Pinned Vertex 156: using xconst=[0.048470,-0.069258,1.093464] -> mesh_pos=[0.048470,-0.069258,1.051408]
  Total vertices processed: 198 (pinned=22, free=176)
UPBGE single frame mode: using pinned vertex constraint positions
DEBUG: cloth_to_object() end
DEBUG: clothModifier_do() end - success
MOD_cloth: copying cloth results back to mesh positions
  MOD_cloth Vertex 0: pos=[0.001632,-0.094823,1.012674]
  MOD_cloth Vertex 1: pos=[0.030034,-0.089226,1.012736]
  MOD_cloth Vertex 2: pos=[0.055799,-0.075311,1.012591]
  MOD_cloth Vertex 3: pos=[0.076552,-0.053462,1.012144]
  MOD_cloth Vertex 4: pos=[0.093945,-0.028036,1.012926]
MOD_cloth: forced object data re-evaluation
Should simulate...
DEBUG: clothModifier_do() start
DEBUG: clothModifier_do() - framenr=5
DEBUG: clothModifier_do() - calling do_init_cloth
DEBUG: do_init_cloth() start - framenr=5
DEBUG: do_init_cloth() end - success
DEBUG: clothModifier_do() - do_init_cloth success
need_first_init = 0
DEBUG: cloth_is_upbge_single_frame_mode() start - current_frame=5, last_frame=4
DEBUG: cloth_is_upbge_single_frame_mode() end - result=0
cache_flag_baked = 1 --- is_single_frame_mode = 0 --- can_simulate = 1
framenr = 5, last_frame = 4, timescale = 1.000000
DEBUG: clothModifier_do() - calling BKE_ptcache_read
DEBUG: clothModifier_do() - cache_result=0
DEBUG: clothModifier_do() - starting simulation
DEBUG: clothModifier_do() - calling do_step_cloth
DEBUG: do_step_cloth() start - framenr=5
Object matrix unchanged
Vertex 0: local=[-0.000018,-0.111819,1.024277] world=[-0.000018,-0.111819,1.065248]
DEBUG: do_step_cloth() - calling BKE_effectors_create
DEBUG: do_step_cloth() - calling cloth_apply_vgroup
DEBUG: cloth_apply_vgroup() start
DEBUG: cloth_apply_vgroup() end
DEBUG: do_step_cloth() - calling cloth_update_springs
DEBUG: cloth_update_springs() start
DEBUG: cloth_update_springs() end
DEBUG: do_step_cloth() - calling SIM_cloth_solve
DEBUG: SIM_cloth_solve() start - frame=5.000000
DEBUG: SIM_cloth_solve() - is_hair=0
SIM_cloth_solve: tf=1.000000, dt_base=0.200000, timescale=1.000000, dt_final=0.200000
cloth_solve--tf-- 1.000000
=== Physics step 0.000000 ===
DEBUG: cloth_setup_constraints() start
DEBUG: cloth_setup_constraints() end
1. Constraints setup completed
2. Forces cleared
DEBUG: cloth_calc_force() start - time=0.000000
cloth_calc_force called - mvert_num: 198, avg_spring_len: 0.036831
Gravity enabled: [0.000000, 0.000000, -0.019620]
  Spring 1 (active): type=12, ij=153, kl=176, restlen=0.059902, stiffness=0.000000
  Spring 2 (active): type=12, ij=132, kl=197, restlen=0.059063, stiffness=0.000000
  Spring 3 (active): type=12, ij=152, kl=197, restlen=0.060961, stiffness=0.000000
  Spring 4 (active): type=12, ij=153, kl=196, restlen=0.058732, stiffness=0.000000
  Spring 5 (active): type=12, ij=151, kl=196, restlen=0.061899, stiffness=0.000000
cloth_calc_force completed - total springs: 726 (active: 726, inactive: 0)
  ...and 721 more active springs not shown
DEBUG: cloth_calc_force() end
3. Forces calculated
DEBUG: SIM_mass_spring_solve_velocities() start (Blender) - dt=0.200000
DEBUG: SIM_mass_spring_solve_velocities() end (Blender) - status=1
4. Velocities solved - status: 1, iterations: 9
DEBUG: cloth_solve_collisions() start - step=0.000000, dt=0.200000
DEBUG: SIM_mass_spring_solve_positions() start (Blender) - dt=0.200000
DEBUG: SIM_mass_spring_solve_positions() end (Blender)
DEBUG: cloth_solve_collisions() - iteration 1/1
DEBUG: cloth_bvh_collision() start - step=0.000000, dt=0.200000
DEBUG: ret variables initialized - ret=0, ret2=0
DEBUG: cloth_bvh_collision() - cloth_bvh=000001FEBCD77D88, cloth=000001FEBB2173D8
DEBUG: cloth_bvh_collision() - single_frame=0, max_loops=2
DEBUG: bvhtree_update_from_cloth() start - moving=0, self=0
DEBUG: bvhtree_update_from_cloth() end
DEBUG: collision loop iteration 1 - resetting ret2 to 0
DEBUG: cloth_bvh_objcollisions_resolve() start - numcollobj=3, dt=0.200000
DEBUG: objcollisions_resolve - ret initialized to 0
DEBUG: objcollisions_resolve - iteration 0, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 0 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 96 vertices, ret now=96
DEBUG: objcollisions_resolve - iteration 1, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 1 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 96 vertices, ret now=192
DEBUG: objcollisions_resolve() end - returning ret=192
DEBUG: object collision resolve returned 192
DEBUG: after object collisions - ret=192 (added 192)
DEBUG: after object collisions - ret2=192 (added 192)
DEBUG: applying collision resolution - ret2=192, updating 198 vertices
DEBUG: end of collision loop round 1 - ret=192, ret2=192
DEBUG: collision loop iteration 2 - resetting ret2 to 0
DEBUG: cloth_bvh_objcollisions_resolve() start - numcollobj=3, dt=0.200000
DEBUG: objcollisions_resolve - ret initialized to 0
DEBUG: objcollisions_resolve - iteration 0, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 0 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 90 vertices, ret now=90
DEBUG: objcollisions_resolve - iteration 1, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 1 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 89 vertices, ret now=179
DEBUG: objcollisions_resolve() end - returning ret=179
DEBUG: object collision resolve returned 179
DEBUG: after object collisions - ret=371 (added 179)
DEBUG: after object collisions - ret2=371 (added 371)
DEBUG: applying collision resolution - ret2=371, updating 198 vertices
DEBUG: end of collision loop round 2 - ret=371, ret2=371
DEBUG: cloth_bvh_collision() end - ret=371, rounds=2
DEBUG: final return value - min(ret=371, 1) = 1
DEBUG: cloth_solve_collisions() end - completed 1 collision iterations
5. Collisions processed
DEBUG: SIM_mass_spring_solve_positions() start (Blender) - dt=0.200000
DEBUG: SIM_mass_spring_solve_positions() end (Blender)
6. Positions solved
DEBUG: SIM_mass_spring_apply_result() start (Blender)
DEBUG: SIM_mass_spring_apply_result() end (Blender)
7. Results applied
Pinned vertex 154: set to xconst=[0.001274,-0.081331,1.090924]
Pinned vertex 155: set to xconst=[0.026057,-0.076357,1.090947]
Pinned vertex 156: set to xconst=[0.048633,-0.063598,1.090864]
...and 19 more pinned vertices (total: 22)
8. Pinned vertices updated
=== Physics step 0.200000 ===
DEBUG: cloth_setup_constraints() start
DEBUG: cloth_setup_constraints() end
1. Constraints setup completed
2. Forces cleared
DEBUG: cloth_calc_force() start - time=0.200000
cloth_calc_force called - mvert_num: 198, avg_spring_len: 0.036831
Gravity enabled: [0.000000, 0.000000, -0.019620]
  Spring 1 (active): type=12, ij=153, kl=176, restlen=0.059902, stiffness=0.000000
  Spring 2 (active): type=12, ij=132, kl=197, restlen=0.059063, stiffness=0.000000
  Spring 3 (active): type=12, ij=152, kl=197, restlen=0.060961, stiffness=0.000000
  Spring 4 (active): type=12, ij=153, kl=196, restlen=0.058732, stiffness=0.000000
  Spring 5 (active): type=12, ij=151, kl=196, restlen=0.061899, stiffness=0.000000
cloth_calc_force completed - total springs: 726 (active: 726, inactive: 0)
  ...and 721 more active springs not shown
DEBUG: cloth_calc_force() end
3. Forces calculated
DEBUG: SIM_mass_spring_solve_velocities() start (Blender) - dt=0.200000
DEBUG: SIM_mass_spring_solve_velocities() end (Blender) - status=1
4. Velocities solved - status: 1, iterations: 8
DEBUG: cloth_solve_collisions() start - step=0.200000, dt=0.200000
DEBUG: SIM_mass_spring_solve_positions() start (Blender) - dt=0.200000
DEBUG: SIM_mass_spring_solve_positions() end (Blender)
DEBUG: cloth_solve_collisions() - iteration 1/1
DEBUG: cloth_bvh_collision() start - step=0.200000, dt=0.200000
DEBUG: ret variables initialized - ret=0, ret2=0
DEBUG: cloth_bvh_collision() - cloth_bvh=000001FEBCD77D88, cloth=000001FEBB2173D8
DEBUG: cloth_bvh_collision() - single_frame=0, max_loops=2
DEBUG: bvhtree_update_from_cloth() start - moving=0, self=0
DEBUG: bvhtree_update_from_cloth() end
DEBUG: collision loop iteration 1 - resetting ret2 to 0
DEBUG: cloth_bvh_objcollisions_resolve() start - numcollobj=3, dt=0.200000
DEBUG: objcollisions_resolve - ret initialized to 0
DEBUG: objcollisions_resolve - iteration 0, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 0 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 95 vertices, ret now=95
DEBUG: objcollisions_resolve - iteration 1, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 1 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 95 vertices, ret now=190
DEBUG: objcollisions_resolve() end - returning ret=190
DEBUG: object collision resolve returned 190
DEBUG: after object collisions - ret=190 (added 190)
DEBUG: after object collisions - ret2=190 (added 190)
DEBUG: applying collision resolution - ret2=190, updating 198 vertices
DEBUG: end of collision loop round 1 - ret=190, ret2=190
DEBUG: collision loop iteration 2 - resetting ret2 to 0
DEBUG: cloth_bvh_objcollisions_resolve() start - numcollobj=3, dt=0.200000
DEBUG: objcollisions_resolve - ret initialized to 0
DEBUG: objcollisions_resolve - iteration 0, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 0 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 88 vertices, ret now=88
DEBUG: objcollisions_resolve - iteration 1, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 1 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 88 vertices, ret now=176
DEBUG: objcollisions_resolve() end - returning ret=176
DEBUG: object collision resolve returned 176
DEBUG: after object collisions - ret=366 (added 176)
DEBUG: after object collisions - ret2=366 (added 366)
DEBUG: applying collision resolution - ret2=366, updating 198 vertices
DEBUG: end of collision loop round 2 - ret=366, ret2=366
DEBUG: cloth_bvh_collision() end - ret=366, rounds=2
DEBUG: final return value - min(ret=366, 1) = 1
DEBUG: cloth_solve_collisions() end - completed 1 collision iterations
5. Collisions processed
DEBUG: SIM_mass_spring_solve_positions() start (Blender) - dt=0.200000
DEBUG: SIM_mass_spring_solve_positions() end (Blender)
6. Positions solved
DEBUG: SIM_mass_spring_apply_result() start (Blender)
DEBUG: SIM_mass_spring_apply_result() end (Blender)
7. Results applied
Pinned vertex 154: set to xconst=[0.001274,-0.081331,1.090924]
Pinned vertex 155: set to xconst=[0.026057,-0.076357,1.090947]
Pinned vertex 156: set to xconst=[0.048633,-0.063598,1.090864]
...and 19 more pinned vertices (total: 22)
8. Pinned vertices updated
=== Physics step 0.400000 ===
DEBUG: cloth_setup_constraints() start
DEBUG: cloth_setup_constraints() end
1. Constraints setup completed
2. Forces cleared
DEBUG: cloth_calc_force() start - time=0.400000
cloth_calc_force called - mvert_num: 198, avg_spring_len: 0.036831
Gravity enabled: [0.000000, 0.000000, -0.019620]
  Spring 1 (active): type=12, ij=153, kl=176, restlen=0.059902, stiffness=0.000000
  Spring 2 (active): type=12, ij=132, kl=197, restlen=0.059063, stiffness=0.000000
  Spring 3 (active): type=12, ij=152, kl=197, restlen=0.060961, stiffness=0.000000
  Spring 4 (active): type=12, ij=153, kl=196, restlen=0.058732, stiffness=0.000000
  Spring 5 (active): type=12, ij=151, kl=196, restlen=0.061899, stiffness=0.000000
cloth_calc_force completed - total springs: 726 (active: 726, inactive: 0)
  ...and 721 more active springs not shown
DEBUG: cloth_calc_force() end
3. Forces calculated
DEBUG: SIM_mass_spring_solve_velocities() start (Blender) - dt=0.200000
DEBUG: SIM_mass_spring_solve_velocities() end (Blender) - status=1
4. Velocities solved - status: 1, iterations: 8
DEBUG: cloth_solve_collisions() start - step=0.400000, dt=0.200000
DEBUG: SIM_mass_spring_solve_positions() start (Blender) - dt=0.200000
DEBUG: SIM_mass_spring_solve_positions() end (Blender)
DEBUG: cloth_solve_collisions() - iteration 1/1
DEBUG: cloth_bvh_collision() start - step=0.400000, dt=0.200000
DEBUG: ret variables initialized - ret=0, ret2=0
DEBUG: cloth_bvh_collision() - cloth_bvh=000001FEBCD77D88, cloth=000001FEBB2173D8
DEBUG: cloth_bvh_collision() - single_frame=0, max_loops=2
DEBUG: bvhtree_update_from_cloth() start - moving=0, self=0
DEBUG: bvhtree_update_from_cloth() end
DEBUG: collision loop iteration 1 - resetting ret2 to 0
DEBUG: cloth_bvh_objcollisions_resolve() start - numcollobj=3, dt=0.200000
DEBUG: objcollisions_resolve - ret initialized to 0
DEBUG: objcollisions_resolve - iteration 0, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 0 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 96 vertices, ret now=96
DEBUG: objcollisions_resolve - iteration 1, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 1 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 96 vertices, ret now=192
DEBUG: objcollisions_resolve() end - returning ret=192
DEBUG: object collision resolve returned 192
DEBUG: after object collisions - ret=192 (added 192)
DEBUG: after object collisions - ret2=192 (added 192)
DEBUG: applying collision resolution - ret2=192, updating 198 vertices
DEBUG: end of collision loop round 1 - ret=192, ret2=192
DEBUG: collision loop iteration 2 - resetting ret2 to 0
DEBUG: cloth_bvh_objcollisions_resolve() start - numcollobj=3, dt=0.200000
DEBUG: objcollisions_resolve - ret initialized to 0
DEBUG: objcollisions_resolve - iteration 0, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 0 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 90 vertices, ret now=90
DEBUG: objcollisions_resolve - iteration 1, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 1 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 90 vertices, ret now=180
DEBUG: objcollisions_resolve() end - returning ret=180
DEBUG: object collision resolve returned 180
DEBUG: after object collisions - ret=372 (added 180)
DEBUG: after object collisions - ret2=372 (added 372)
DEBUG: applying collision resolution - ret2=372, updating 198 vertices
DEBUG: end of collision loop round 2 - ret=372, ret2=372
DEBUG: cloth_bvh_collision() end - ret=372, rounds=2
DEBUG: final return value - min(ret=372, 1) = 1
DEBUG: cloth_solve_collisions() end - completed 1 collision iterations
5. Collisions processed
DEBUG: SIM_mass_spring_solve_positions() start (Blender) - dt=0.200000
DEBUG: SIM_mass_spring_solve_positions() end (Blender)
6. Positions solved
DEBUG: SIM_mass_spring_apply_result() start (Blender)
DEBUG: SIM_mass_spring_apply_result() end (Blender)
7. Results applied
Pinned vertex 154: set to xconst=[0.001274,-0.081331,1.090924]
Pinned vertex 155: set to xconst=[0.026057,-0.076357,1.090947]
Pinned vertex 156: set to xconst=[0.048633,-0.063598,1.090864]
...and 19 more pinned vertices (total: 22)
8. Pinned vertices updated
=== Physics step 0.600000 ===
DEBUG: cloth_setup_constraints() start
DEBUG: cloth_setup_constraints() end
1. Constraints setup completed
2. Forces cleared
DEBUG: cloth_calc_force() start - time=0.600000
cloth_calc_force called - mvert_num: 198, avg_spring_len: 0.036831
Gravity enabled: [0.000000, 0.000000, -0.019620]
  Spring 1 (active): type=12, ij=153, kl=176, restlen=0.059902, stiffness=0.000000
  Spring 2 (active): type=12, ij=132, kl=197, restlen=0.059063, stiffness=0.000000
  Spring 3 (active): type=12, ij=152, kl=197, restlen=0.060961, stiffness=0.000000
  Spring 4 (active): type=12, ij=153, kl=196, restlen=0.058732, stiffness=0.000000
  Spring 5 (active): type=12, ij=151, kl=196, restlen=0.061899, stiffness=0.000000
cloth_calc_force completed - total springs: 726 (active: 726, inactive: 0)
  ...and 721 more active springs not shown
DEBUG: cloth_calc_force() end
3. Forces calculated
DEBUG: SIM_mass_spring_solve_velocities() start (Blender) - dt=0.200000
DEBUG: SIM_mass_spring_solve_velocities() end (Blender) - status=1
4. Velocities solved - status: 1, iterations: 9
DEBUG: cloth_solve_collisions() start - step=0.600000, dt=0.200000
DEBUG: SIM_mass_spring_solve_positions() start (Blender) - dt=0.200000
DEBUG: SIM_mass_spring_solve_positions() end (Blender)
DEBUG: cloth_solve_collisions() - iteration 1/1
DEBUG: cloth_bvh_collision() start - step=0.600000, dt=0.200000
DEBUG: ret variables initialized - ret=0, ret2=0
DEBUG: cloth_bvh_collision() - cloth_bvh=000001FEBCD77D88, cloth=000001FEBB2173D8
DEBUG: cloth_bvh_collision() - single_frame=0, max_loops=2
DEBUG: bvhtree_update_from_cloth() start - moving=0, self=0
DEBUG: bvhtree_update_from_cloth() end
DEBUG: collision loop iteration 1 - resetting ret2 to 0
DEBUG: cloth_bvh_objcollisions_resolve() start - numcollobj=3, dt=0.200000
DEBUG: objcollisions_resolve - ret initialized to 0
DEBUG: objcollisions_resolve - iteration 0, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 0 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 95 vertices, ret now=95
DEBUG: objcollisions_resolve - iteration 1, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 1 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 95 vertices, ret now=190
DEBUG: objcollisions_resolve() end - returning ret=190
DEBUG: object collision resolve returned 190
DEBUG: after object collisions - ret=190 (added 190)
DEBUG: after object collisions - ret2=190 (added 190)
DEBUG: applying collision resolution - ret2=190, updating 198 vertices
DEBUG: end of collision loop round 1 - ret=190, ret2=190
DEBUG: collision loop iteration 2 - resetting ret2 to 0
DEBUG: cloth_bvh_objcollisions_resolve() start - numcollobj=3, dt=0.200000
DEBUG: objcollisions_resolve - ret initialized to 0
DEBUG: objcollisions_resolve - iteration 0, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 0 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 88 vertices, ret now=88
DEBUG: objcollisions_resolve - iteration 1, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 1 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 88 vertices, ret now=176
DEBUG: objcollisions_resolve() end - returning ret=176
DEBUG: object collision resolve returned 176
DEBUG: after object collisions - ret=366 (added 176)
DEBUG: after object collisions - ret2=366 (added 366)
DEBUG: applying collision resolution - ret2=366, updating 198 vertices
DEBUG: end of collision loop round 2 - ret=366, ret2=366
DEBUG: cloth_bvh_collision() end - ret=366, rounds=2
DEBUG: final return value - min(ret=366, 1) = 1
DEBUG: cloth_solve_collisions() end - completed 1 collision iterations
5. Collisions processed
DEBUG: SIM_mass_spring_solve_positions() start (Blender) - dt=0.200000
DEBUG: SIM_mass_spring_solve_positions() end (Blender)
6. Positions solved
DEBUG: SIM_mass_spring_apply_result() start (Blender)
DEBUG: SIM_mass_spring_apply_result() end (Blender)
7. Results applied
Pinned vertex 154: set to xconst=[0.001274,-0.081331,1.090924]
Pinned vertex 155: set to xconst=[0.026057,-0.076357,1.090947]
Pinned vertex 156: set to xconst=[0.048633,-0.063598,1.090864]
...and 19 more pinned vertices (total: 22)
8. Pinned vertices updated
=== Physics step 0.800000 ===
DEBUG: cloth_setup_constraints() start
DEBUG: cloth_setup_constraints() end
1. Constraints setup completed
2. Forces cleared
DEBUG: cloth_calc_force() start - time=0.800000
cloth_calc_force called - mvert_num: 198, avg_spring_len: 0.036831
Gravity enabled: [0.000000, 0.000000, -0.019620]
  Spring 1 (active): type=12, ij=153, kl=176, restlen=0.059902, stiffness=0.000000
  Spring 2 (active): type=12, ij=132, kl=197, restlen=0.059063, stiffness=0.000000
  Spring 3 (active): type=12, ij=152, kl=197, restlen=0.060961, stiffness=0.000000
  Spring 4 (active): type=12, ij=153, kl=196, restlen=0.058732, stiffness=0.000000
  Spring 5 (active): type=12, ij=151, kl=196, restlen=0.061899, stiffness=0.000000
cloth_calc_force completed - total springs: 726 (active: 726, inactive: 0)
  ...and 721 more active springs not shown
DEBUG: cloth_calc_force() end
3. Forces calculated
DEBUG: SIM_mass_spring_solve_velocities() start (Blender) - dt=0.200000
DEBUG: SIM_mass_spring_solve_velocities() end (Blender) - status=1
4. Velocities solved - status: 1, iterations: 8
DEBUG: cloth_solve_collisions() start - step=0.800000, dt=0.200000
DEBUG: SIM_mass_spring_solve_positions() start (Blender) - dt=0.200000
DEBUG: SIM_mass_spring_solve_positions() end (Blender)
DEBUG: cloth_solve_collisions() - iteration 1/1
DEBUG: cloth_bvh_collision() start - step=0.800000, dt=0.200000
DEBUG: ret variables initialized - ret=0, ret2=0
DEBUG: cloth_bvh_collision() - cloth_bvh=000001FEBCD77D88, cloth=000001FEBB2173D8
DEBUG: cloth_bvh_collision() - single_frame=0, max_loops=2
DEBUG: bvhtree_update_from_cloth() start - moving=0, self=0
DEBUG: bvhtree_update_from_cloth() end
DEBUG: collision loop iteration 1 - resetting ret2 to 0
DEBUG: cloth_bvh_objcollisions_resolve() start - numcollobj=3, dt=0.200000
DEBUG: objcollisions_resolve - ret initialized to 0
DEBUG: objcollisions_resolve - iteration 0, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 0 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 98 vertices, ret now=98
DEBUG: objcollisions_resolve - iteration 1, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 1 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 98 vertices, ret now=196
DEBUG: objcollisions_resolve() end - returning ret=196
DEBUG: object collision resolve returned 196
DEBUG: after object collisions - ret=196 (added 196)
DEBUG: after object collisions - ret2=196 (added 196)
DEBUG: applying collision resolution - ret2=196, updating 198 vertices
DEBUG: end of collision loop round 1 - ret=196, ret2=196
DEBUG: collision loop iteration 2 - resetting ret2 to 0
DEBUG: cloth_bvh_objcollisions_resolve() start - numcollobj=3, dt=0.200000
DEBUG: objcollisions_resolve - ret initialized to 0
DEBUG: objcollisions_resolve - iteration 0, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 0 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 89 vertices, ret now=89
DEBUG: objcollisions_resolve - iteration 1, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 1 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 89 vertices, ret now=178
DEBUG: objcollisions_resolve() end - returning ret=178
DEBUG: object collision resolve returned 178
DEBUG: after object collisions - ret=374 (added 178)
DEBUG: after object collisions - ret2=374 (added 374)
DEBUG: applying collision resolution - ret2=374, updating 198 vertices
DEBUG: end of collision loop round 2 - ret=374, ret2=374
DEBUG: cloth_bvh_collision() end - ret=374, rounds=2
DEBUG: final return value - min(ret=374, 1) = 1
DEBUG: cloth_solve_collisions() end - completed 1 collision iterations
5. Collisions processed
DEBUG: SIM_mass_spring_solve_positions() start (Blender) - dt=0.200000
DEBUG: SIM_mass_spring_solve_positions() end (Blender)
6. Positions solved
DEBUG: SIM_mass_spring_apply_result() start (Blender)
DEBUG: SIM_mass_spring_apply_result() end (Blender)
7. Results applied
Pinned vertex 154: set to xconst=[0.001274,-0.081331,1.090924]
Pinned vertex 155: set to xconst=[0.026057,-0.076357,1.090947]
Pinned vertex 156: set to xconst=[0.048633,-0.063598,1.090864]
...and 19 more pinned vertices (total: 22)
8. Pinned vertices updated
=== Final vertex states ===
Vertex 0 (free): pos=[0.001460,-0.089131,1.051737] vel=[-0.000008,0.006774,-0.001120]
Vertex 1 (free): pos=[0.029527,-0.084398,1.051913] vel=[-0.000716,0.005628,-0.000967]
Vertex 2 (free): pos=[0.055047,-0.070480,1.051725] vel=[-0.001583,0.005972,-0.001145]
Vertex 154 (pinned): pos=[0.001274,-0.081331,1.090924]
Vertex 155 (pinned): pos=[0.026057,-0.076357,1.090947]
Vertex 156 (pinned): pos=[0.048633,-0.063598,1.090864]
Summary: 198 total vertices (176 free, 22 pinned)
DEBUG: SIM_cloth_solve() end - success
DEBUG: do_step_cloth() - SIM_cloth_solve returned 1
DEBUG: do_step_cloth() - calling BKE_effectors_free
DEBUG: do_step_cloth() end - ret=1
DEBUG: clothModifier_do() - do_step_cloth success
DEBUG: clothModifier_do() - writing cache (normal mode)
DEBUG: clothModifier_do() - calling cloth_to_object
DEBUG: cloth_to_object() start
cloth_to_object: copying 198 vertices to mesh
DEBUG: cloth_is_upbge_single_frame_mode() start - current_frame=4, last_frame=4
DEBUG: cloth_is_upbge_single_frame_mode() end - result=1
  Free Vertex 0: using x=[0.001460,-0.089131,1.051737] -> mesh_pos=[0.001460,-0.089131,1.011286]
  Free Vertex 1: using x=[0.029527,-0.084398,1.051913] -> mesh_pos=[0.029527,-0.084398,1.011455]
  Free Vertex 2: using x=[0.055047,-0.070480,1.051725] -> mesh_pos=[0.055047,-0.070480,1.011274]
  Pinned Vertex 154: using xconst=[0.001274,-0.081331,1.090924] -> mesh_pos=[0.001274,-0.081331,1.048965]
  Pinned Vertex 155: using xconst=[0.026057,-0.076357,1.090947] -> mesh_pos=[0.026057,-0.076357,1.048987]
  Pinned Vertex 156: using xconst=[0.048633,-0.063598,1.090864] -> mesh_pos=[0.048633,-0.063598,1.048907]
  Total vertices processed: 198 (pinned=22, free=176)
UPBGE single frame mode: using pinned vertex constraint positions
DEBUG: cloth_to_object() end
DEBUG: clothModifier_do() end - success
MOD_cloth: copying cloth results back to mesh positions
  MOD_cloth Vertex 0: pos=[0.001460,-0.089131,1.011286]
  MOD_cloth Vertex 1: pos=[0.029527,-0.084398,1.011455]
  MOD_cloth Vertex 2: pos=[0.055047,-0.070480,1.011274]
  MOD_cloth Vertex 3: pos=[0.074774,-0.048110,1.010544]
  MOD_cloth Vertex 4: pos=[0.091876,-0.023119,1.011068]
MOD_cloth: forced object data re-evaluation
Should simulate...
DEBUG: clothModifier_do() start
DEBUG: clothModifier_do() - framenr=6
DEBUG: clothModifier_do() - calling do_init_cloth
DEBUG: do_init_cloth() start - framenr=6
DEBUG: do_init_cloth() end - success
DEBUG: clothModifier_do() - do_init_cloth success
need_first_init = 0
DEBUG: cloth_is_upbge_single_frame_mode() start - current_frame=6, last_frame=5
DEBUG: cloth_is_upbge_single_frame_mode() end - result=0
cache_flag_baked = 1 --- is_single_frame_mode = 0 --- can_simulate = 1
framenr = 6, last_frame = 5, timescale = 1.000000
DEBUG: clothModifier_do() - calling BKE_ptcache_read
DEBUG: clothModifier_do() - cache_result=0
DEBUG: clothModifier_do() - starting simulation
DEBUG: clothModifier_do() - calling do_step_cloth
DEBUG: do_step_cloth() start - framenr=6
Object matrix unchanged
Vertex 0: local=[-0.000018,-0.111819,1.024277] world=[-0.000018,-0.111819,1.065248]
DEBUG: do_step_cloth() - calling BKE_effectors_create
DEBUG: do_step_cloth() - calling cloth_apply_vgroup
DEBUG: cloth_apply_vgroup() start
DEBUG: cloth_apply_vgroup() end
DEBUG: do_step_cloth() - calling cloth_update_springs
DEBUG: cloth_update_springs() start
DEBUG: cloth_update_springs() end
DEBUG: do_step_cloth() - calling SIM_cloth_solve
DEBUG: SIM_cloth_solve() start - frame=6.000000
DEBUG: SIM_cloth_solve() - is_hair=0
SIM_cloth_solve: tf=1.000000, dt_base=0.200000, timescale=1.000000, dt_final=0.200000
cloth_solve--tf-- 1.000000
=== Physics step 0.000000 ===
DEBUG: cloth_setup_constraints() start
DEBUG: cloth_setup_constraints() end
1. Constraints setup completed
2. Forces cleared
DEBUG: cloth_calc_force() start - time=0.000000
cloth_calc_force called - mvert_num: 198, avg_spring_len: 0.036831
Gravity enabled: [0.000000, 0.000000, -0.019620]
  Spring 1 (active): type=12, ij=153, kl=176, restlen=0.059902, stiffness=0.000000
  Spring 2 (active): type=12, ij=132, kl=197, restlen=0.059063, stiffness=0.000000
  Spring 3 (active): type=12, ij=152, kl=197, restlen=0.060961, stiffness=0.000000
  Spring 4 (active): type=12, ij=153, kl=196, restlen=0.058732, stiffness=0.000000
  Spring 5 (active): type=12, ij=151, kl=196, restlen=0.061899, stiffness=0.000000
cloth_calc_force completed - total springs: 726 (active: 726, inactive: 0)
  ...and 721 more active springs not shown
DEBUG: cloth_calc_force() end
3. Forces calculated
DEBUG: SIM_mass_spring_solve_velocities() start (Blender) - dt=0.200000
DEBUG: SIM_mass_spring_solve_velocities() end (Blender) - status=1
4. Velocities solved - status: 1, iterations: 9
DEBUG: cloth_solve_collisions() start - step=0.000000, dt=0.200000
DEBUG: SIM_mass_spring_solve_positions() start (Blender) - dt=0.200000
DEBUG: SIM_mass_spring_solve_positions() end (Blender)
DEBUG: cloth_solve_collisions() - iteration 1/1
DEBUG: cloth_bvh_collision() start - step=0.000000, dt=0.200000
DEBUG: ret variables initialized - ret=0, ret2=0
DEBUG: cloth_bvh_collision() - cloth_bvh=000001FEBCD77D88, cloth=000001FEBB2173D8
DEBUG: cloth_bvh_collision() - single_frame=0, max_loops=2
DEBUG: bvhtree_update_from_cloth() start - moving=0, self=0
DEBUG: bvhtree_update_from_cloth() end
DEBUG: collision loop iteration 1 - resetting ret2 to 0
DEBUG: cloth_bvh_objcollisions_resolve() start - numcollobj=3, dt=0.200000
DEBUG: objcollisions_resolve - ret initialized to 0
DEBUG: objcollisions_resolve - iteration 0, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 0 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 100 vertices, ret now=100
DEBUG: objcollisions_resolve - iteration 1, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 1 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 100 vertices, ret now=200
DEBUG: objcollisions_resolve() end - returning ret=200
DEBUG: object collision resolve returned 200
DEBUG: after object collisions - ret=200 (added 200)
DEBUG: after object collisions - ret2=200 (added 200)
DEBUG: applying collision resolution - ret2=200, updating 198 vertices
DEBUG: end of collision loop round 1 - ret=200, ret2=200
DEBUG: collision loop iteration 2 - resetting ret2 to 0
DEBUG: cloth_bvh_objcollisions_resolve() start - numcollobj=3, dt=0.200000
DEBUG: objcollisions_resolve - ret initialized to 0
DEBUG: objcollisions_resolve - iteration 0, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 0 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 93 vertices, ret now=93
DEBUG: objcollisions_resolve - iteration 1, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 1 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 93 vertices, ret now=186
DEBUG: objcollisions_resolve() end - returning ret=186
DEBUG: object collision resolve returned 186
DEBUG: after object collisions - ret=386 (added 186)
DEBUG: after object collisions - ret2=386 (added 386)
DEBUG: applying collision resolution - ret2=386, updating 198 vertices
DEBUG: end of collision loop round 2 - ret=386, ret2=386
DEBUG: cloth_bvh_collision() end - ret=386, rounds=2
DEBUG: final return value - min(ret=386, 1) = 1
DEBUG: cloth_solve_collisions() end - completed 1 collision iterations
5. Collisions processed
DEBUG: SIM_mass_spring_solve_positions() start (Blender) - dt=0.200000
DEBUG: SIM_mass_spring_solve_positions() end (Blender)
6. Positions solved
DEBUG: SIM_mass_spring_apply_result() start (Blender)
DEBUG: SIM_mass_spring_apply_result() end (Blender)
7. Results applied
Pinned vertex 154: set to xconst=[0.001597,-0.076100,1.088284]
Pinned vertex 155: set to xconst=[0.026334,-0.070899,1.088328]
Pinned vertex 156: set to xconst=[0.048792,-0.057935,1.088263]
...and 19 more pinned vertices (total: 22)
8. Pinned vertices updated
=== Physics step 0.200000 ===
DEBUG: cloth_setup_constraints() start
DEBUG: cloth_setup_constraints() end
1. Constraints setup completed
2. Forces cleared
DEBUG: cloth_calc_force() start - time=0.200000
cloth_calc_force called - mvert_num: 198, avg_spring_len: 0.036831
Gravity enabled: [0.000000, 0.000000, -0.019620]
  Spring 1 (active): type=12, ij=153, kl=176, restlen=0.059902, stiffness=0.000000
  Spring 2 (active): type=12, ij=132, kl=197, restlen=0.059063, stiffness=0.000000
  Spring 3 (active): type=12, ij=152, kl=197, restlen=0.060961, stiffness=0.000000
  Spring 4 (active): type=12, ij=153, kl=196, restlen=0.058732, stiffness=0.000000
  Spring 5 (active): type=12, ij=151, kl=196, restlen=0.061899, stiffness=0.000000
cloth_calc_force completed - total springs: 726 (active: 726, inactive: 0)
  ...and 721 more active springs not shown
DEBUG: cloth_calc_force() end
3. Forces calculated
DEBUG: SIM_mass_spring_solve_velocities() start (Blender) - dt=0.200000
DEBUG: SIM_mass_spring_solve_velocities() end (Blender) - status=1
4. Velocities solved - status: 1, iterations: 8
DEBUG: cloth_solve_collisions() start - step=0.200000, dt=0.200000
DEBUG: SIM_mass_spring_solve_positions() start (Blender) - dt=0.200000
DEBUG: SIM_mass_spring_solve_positions() end (Blender)
DEBUG: cloth_solve_collisions() - iteration 1/1
DEBUG: cloth_bvh_collision() start - step=0.200000, dt=0.200000
DEBUG: ret variables initialized - ret=0, ret2=0
DEBUG: cloth_bvh_collision() - cloth_bvh=000001FEBCD77D88, cloth=000001FEBB2173D8
DEBUG: cloth_bvh_collision() - single_frame=0, max_loops=2
DEBUG: bvhtree_update_from_cloth() start - moving=0, self=0
DEBUG: bvhtree_update_from_cloth() end
DEBUG: collision loop iteration 1 - resetting ret2 to 0
DEBUG: cloth_bvh_objcollisions_resolve() start - numcollobj=3, dt=0.200000
DEBUG: objcollisions_resolve - ret initialized to 0
DEBUG: objcollisions_resolve - iteration 0, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 0 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 108 vertices, ret now=108
DEBUG: objcollisions_resolve - iteration 1, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 1 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 108 vertices, ret now=216
DEBUG: objcollisions_resolve() end - returning ret=216
DEBUG: object collision resolve returned 216
DEBUG: after object collisions - ret=216 (added 216)
DEBUG: after object collisions - ret2=216 (added 216)
DEBUG: applying collision resolution - ret2=216, updating 198 vertices
DEBUG: end of collision loop round 1 - ret=216, ret2=216
DEBUG: collision loop iteration 2 - resetting ret2 to 0
DEBUG: cloth_bvh_objcollisions_resolve() start - numcollobj=3, dt=0.200000
DEBUG: objcollisions_resolve - ret initialized to 0
DEBUG: objcollisions_resolve - iteration 0, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 0 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 90 vertices, ret now=90
DEBUG: objcollisions_resolve - iteration 1, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 1 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 90 vertices, ret now=180
DEBUG: objcollisions_resolve() end - returning ret=180
DEBUG: object collision resolve returned 180
DEBUG: after object collisions - ret=396 (added 180)
DEBUG: after object collisions - ret2=396 (added 396)
DEBUG: applying collision resolution - ret2=396, updating 198 vertices
DEBUG: end of collision loop round 2 - ret=396, ret2=396
DEBUG: cloth_bvh_collision() end - ret=396, rounds=2
DEBUG: final return value - min(ret=396, 1) = 1
DEBUG: cloth_solve_collisions() end - completed 1 collision iterations
5. Collisions processed
DEBUG: SIM_mass_spring_solve_positions() start (Blender) - dt=0.200000
DEBUG: SIM_mass_spring_solve_positions() end (Blender)
6. Positions solved
DEBUG: SIM_mass_spring_apply_result() start (Blender)
DEBUG: SIM_mass_spring_apply_result() end (Blender)
7. Results applied
Pinned vertex 154: set to xconst=[0.001597,-0.076100,1.088284]
Pinned vertex 155: set to xconst=[0.026334,-0.070899,1.088328]
Pinned vertex 156: set to xconst=[0.048792,-0.057935,1.088263]
...and 19 more pinned vertices (total: 22)
8. Pinned vertices updated
=== Physics step 0.400000 ===
DEBUG: cloth_setup_constraints() start
DEBUG: cloth_setup_constraints() end
1. Constraints setup completed
2. Forces cleared
DEBUG: cloth_calc_force() start - time=0.400000
cloth_calc_force called - mvert_num: 198, avg_spring_len: 0.036831
Gravity enabled: [0.000000, 0.000000, -0.019620]
  Spring 1 (active): type=12, ij=153, kl=176, restlen=0.059902, stiffness=0.000000
  Spring 2 (active): type=12, ij=132, kl=197, restlen=0.059063, stiffness=0.000000
  Spring 3 (active): type=12, ij=152, kl=197, restlen=0.060961, stiffness=0.000000
  Spring 4 (active): type=12, ij=153, kl=196, restlen=0.058732, stiffness=0.000000
  Spring 5 (active): type=12, ij=151, kl=196, restlen=0.061899, stiffness=0.000000
cloth_calc_force completed - total springs: 726 (active: 726, inactive: 0)
  ...and 721 more active springs not shown
DEBUG: cloth_calc_force() end
3. Forces calculated
DEBUG: SIM_mass_spring_solve_velocities() start (Blender) - dt=0.200000
DEBUG: SIM_mass_spring_solve_velocities() end (Blender) - status=1
4. Velocities solved - status: 1, iterations: 7
DEBUG: cloth_solve_collisions() start - step=0.400000, dt=0.200000
DEBUG: SIM_mass_spring_solve_positions() start (Blender) - dt=0.200000
DEBUG: SIM_mass_spring_solve_positions() end (Blender)
DEBUG: cloth_solve_collisions() - iteration 1/1
DEBUG: cloth_bvh_collision() start - step=0.400000, dt=0.200000
DEBUG: ret variables initialized - ret=0, ret2=0
DEBUG: cloth_bvh_collision() - cloth_bvh=000001FEBCD77D88, cloth=000001FEBB2173D8
DEBUG: cloth_bvh_collision() - single_frame=0, max_loops=2
DEBUG: bvhtree_update_from_cloth() start - moving=0, self=0
DEBUG: bvhtree_update_from_cloth() end
DEBUG: collision loop iteration 1 - resetting ret2 to 0
DEBUG: cloth_bvh_objcollisions_resolve() start - numcollobj=3, dt=0.200000
DEBUG: objcollisions_resolve - ret initialized to 0
DEBUG: objcollisions_resolve - iteration 0, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 0 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 108 vertices, ret now=108
DEBUG: objcollisions_resolve - iteration 1, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 1 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 108 vertices, ret now=216
DEBUG: objcollisions_resolve() end - returning ret=216
DEBUG: object collision resolve returned 216
DEBUG: after object collisions - ret=216 (added 216)
DEBUG: after object collisions - ret2=216 (added 216)
DEBUG: applying collision resolution - ret2=216, updating 198 vertices
DEBUG: end of collision loop round 1 - ret=216, ret2=216
DEBUG: collision loop iteration 2 - resetting ret2 to 0
DEBUG: cloth_bvh_objcollisions_resolve() start - numcollobj=3, dt=0.200000
DEBUG: objcollisions_resolve - ret initialized to 0
DEBUG: objcollisions_resolve - iteration 0, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 0 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 93 vertices, ret now=93
DEBUG: objcollisions_resolve - iteration 1, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 1 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 93 vertices, ret now=186
DEBUG: objcollisions_resolve() end - returning ret=186
DEBUG: object collision resolve returned 186
DEBUG: after object collisions - ret=402 (added 186)
DEBUG: after object collisions - ret2=402 (added 402)
DEBUG: applying collision resolution - ret2=402, updating 198 vertices
DEBUG: end of collision loop round 2 - ret=402, ret2=402
DEBUG: cloth_bvh_collision() end - ret=402, rounds=2
DEBUG: final return value - min(ret=402, 1) = 1
DEBUG: cloth_solve_collisions() end - completed 1 collision iterations
5. Collisions processed
DEBUG: SIM_mass_spring_solve_positions() start (Blender) - dt=0.200000
DEBUG: SIM_mass_spring_solve_positions() end (Blender)
6. Positions solved
DEBUG: SIM_mass_spring_apply_result() start (Blender)
DEBUG: SIM_mass_spring_apply_result() end (Blender)
7. Results applied
Pinned vertex 154: set to xconst=[0.001597,-0.076100,1.088284]
Pinned vertex 155: set to xconst=[0.026334,-0.070899,1.088328]
Pinned vertex 156: set to xconst=[0.048792,-0.057935,1.088263]
...and 19 more pinned vertices (total: 22)
8. Pinned vertices updated
=== Physics step 0.600000 ===
DEBUG: cloth_setup_constraints() start
DEBUG: cloth_setup_constraints() end
1. Constraints setup completed
2. Forces cleared
DEBUG: cloth_calc_force() start - time=0.600000
cloth_calc_force called - mvert_num: 198, avg_spring_len: 0.036831
Gravity enabled: [0.000000, 0.000000, -0.019620]
  Spring 1 (active): type=12, ij=153, kl=176, restlen=0.059902, stiffness=0.000000
  Spring 2 (active): type=12, ij=132, kl=197, restlen=0.059063, stiffness=0.000000
  Spring 3 (active): type=12, ij=152, kl=197, restlen=0.060961, stiffness=0.000000
  Spring 4 (active): type=12, ij=153, kl=196, restlen=0.058732, stiffness=0.000000
  Spring 5 (active): type=12, ij=151, kl=196, restlen=0.061899, stiffness=0.000000
cloth_calc_force completed - total springs: 726 (active: 726, inactive: 0)
  ...and 721 more active springs not shown
DEBUG: cloth_calc_force() end
3. Forces calculated
DEBUG: SIM_mass_spring_solve_velocities() start (Blender) - dt=0.200000
DEBUG: SIM_mass_spring_solve_velocities() end (Blender) - status=1
4. Velocities solved - status: 1, iterations: 8
DEBUG: cloth_solve_collisions() start - step=0.600000, dt=0.200000
DEBUG: SIM_mass_spring_solve_positions() start (Blender) - dt=0.200000
DEBUG: SIM_mass_spring_solve_positions() end (Blender)
DEBUG: cloth_solve_collisions() - iteration 1/1
DEBUG: cloth_bvh_collision() start - step=0.600000, dt=0.200000
DEBUG: ret variables initialized - ret=0, ret2=0
DEBUG: cloth_bvh_collision() - cloth_bvh=000001FEBCD77D88, cloth=000001FEBB2173D8
DEBUG: cloth_bvh_collision() - single_frame=0, max_loops=2
DEBUG: bvhtree_update_from_cloth() start - moving=0, self=0
DEBUG: bvhtree_update_from_cloth() end
DEBUG: collision loop iteration 1 - resetting ret2 to 0
DEBUG: cloth_bvh_objcollisions_resolve() start - numcollobj=3, dt=0.200000
DEBUG: objcollisions_resolve - ret initialized to 0
DEBUG: objcollisions_resolve - iteration 0, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 0 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 112 vertices, ret now=112
DEBUG: objcollisions_resolve - iteration 1, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 1 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 112 vertices, ret now=224
DEBUG: objcollisions_resolve() end - returning ret=224
DEBUG: object collision resolve returned 224
DEBUG: after object collisions - ret=224 (added 224)
DEBUG: after object collisions - ret2=224 (added 224)
DEBUG: applying collision resolution - ret2=224, updating 198 vertices
DEBUG: end of collision loop round 1 - ret=224, ret2=224
DEBUG: collision loop iteration 2 - resetting ret2 to 0
DEBUG: cloth_bvh_objcollisions_resolve() start - numcollobj=3, dt=0.200000
DEBUG: objcollisions_resolve - ret initialized to 0
DEBUG: objcollisions_resolve - iteration 0, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 0 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 97 vertices, ret now=97
DEBUG: objcollisions_resolve - iteration 1, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 1 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 97 vertices, ret now=194
DEBUG: objcollisions_resolve() end - returning ret=194
DEBUG: object collision resolve returned 194
DEBUG: after object collisions - ret=418 (added 194)
DEBUG: after object collisions - ret2=418 (added 418)
DEBUG: applying collision resolution - ret2=418, updating 198 vertices
DEBUG: end of collision loop round 2 - ret=418, ret2=418
DEBUG: cloth_bvh_collision() end - ret=418, rounds=2
DEBUG: final return value - min(ret=418, 1) = 1
DEBUG: cloth_solve_collisions() end - completed 1 collision iterations
5. Collisions processed
DEBUG: SIM_mass_spring_solve_positions() start (Blender) - dt=0.200000
DEBUG: SIM_mass_spring_solve_positions() end (Blender)
6. Positions solved
DEBUG: SIM_mass_spring_apply_result() start (Blender)
DEBUG: SIM_mass_spring_apply_result() end (Blender)
7. Results applied
Pinned vertex 154: set to xconst=[0.001597,-0.076100,1.088284]
Pinned vertex 155: set to xconst=[0.026334,-0.070899,1.088328]
Pinned vertex 156: set to xconst=[0.048792,-0.057935,1.088263]
...and 19 more pinned vertices (total: 22)
8. Pinned vertices updated
=== Physics step 0.800000 ===
DEBUG: cloth_setup_constraints() start
DEBUG: cloth_setup_constraints() end
1. Constraints setup completed
2. Forces cleared
DEBUG: cloth_calc_force() start - time=0.800000
cloth_calc_force called - mvert_num: 198, avg_spring_len: 0.036831
Gravity enabled: [0.000000, 0.000000, -0.019620]
  Spring 1 (active): type=12, ij=153, kl=176, restlen=0.059902, stiffness=0.000000
  Spring 2 (active): type=12, ij=132, kl=197, restlen=0.059063, stiffness=0.000000
  Spring 3 (active): type=12, ij=152, kl=197, restlen=0.060961, stiffness=0.000000
  Spring 4 (active): type=12, ij=153, kl=196, restlen=0.058732, stiffness=0.000000
  Spring 5 (active): type=12, ij=151, kl=196, restlen=0.061899, stiffness=0.000000
cloth_calc_force completed - total springs: 726 (active: 726, inactive: 0)
  ...and 721 more active springs not shown
DEBUG: cloth_calc_force() end
3. Forces calculated
DEBUG: SIM_mass_spring_solve_velocities() start (Blender) - dt=0.200000
DEBUG: SIM_mass_spring_solve_velocities() end (Blender) - status=1
4. Velocities solved - status: 1, iterations: 9
DEBUG: cloth_solve_collisions() start - step=0.800000, dt=0.200000
DEBUG: SIM_mass_spring_solve_positions() start (Blender) - dt=0.200000
DEBUG: SIM_mass_spring_solve_positions() end (Blender)
DEBUG: cloth_solve_collisions() - iteration 1/1
DEBUG: cloth_bvh_collision() start - step=0.800000, dt=0.200000
DEBUG: ret variables initialized - ret=0, ret2=0
DEBUG: cloth_bvh_collision() - cloth_bvh=000001FEBCD77D88, cloth=000001FEBB2173D8
DEBUG: cloth_bvh_collision() - single_frame=0, max_loops=2
DEBUG: bvhtree_update_from_cloth() start - moving=0, self=0
DEBUG: bvhtree_update_from_cloth() end
DEBUG: collision loop iteration 1 - resetting ret2 to 0
DEBUG: cloth_bvh_objcollisions_resolve() start - numcollobj=3, dt=0.200000
DEBUG: objcollisions_resolve - ret initialized to 0
DEBUG: objcollisions_resolve - iteration 0, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 0 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 117 vertices, ret now=117
DEBUG: objcollisions_resolve - iteration 1, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 1 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 117 vertices, ret now=234
DEBUG: objcollisions_resolve() end - returning ret=234
DEBUG: object collision resolve returned 234
DEBUG: after object collisions - ret=234 (added 234)
DEBUG: after object collisions - ret2=234 (added 234)
DEBUG: applying collision resolution - ret2=234, updating 198 vertices
DEBUG: end of collision loop round 1 - ret=234, ret2=234
DEBUG: collision loop iteration 2 - resetting ret2 to 0
DEBUG: cloth_bvh_objcollisions_resolve() start - numcollobj=3, dt=0.200000
DEBUG: objcollisions_resolve - ret initialized to 0
DEBUG: objcollisions_resolve - iteration 0, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 0 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 103 vertices, ret now=103
DEBUG: objcollisions_resolve - iteration 1, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 1 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 103 vertices, ret now=206
DEBUG: objcollisions_resolve() end - returning ret=206
DEBUG: object collision resolve returned 206
DEBUG: after object collisions - ret=440 (added 206)
DEBUG: after object collisions - ret2=440 (added 440)
DEBUG: applying collision resolution - ret2=440, updating 198 vertices
DEBUG: end of collision loop round 2 - ret=440, ret2=440
DEBUG: cloth_bvh_collision() end - ret=440, rounds=2
DEBUG: final return value - min(ret=440, 1) = 1
DEBUG: cloth_solve_collisions() end - completed 1 collision iterations
5. Collisions processed
DEBUG: SIM_mass_spring_solve_positions() start (Blender) - dt=0.200000
DEBUG: SIM_mass_spring_solve_positions() end (Blender)
6. Positions solved
DEBUG: SIM_mass_spring_apply_result() start (Blender)
DEBUG: SIM_mass_spring_apply_result() end (Blender)
7. Results applied
Pinned vertex 154: set to xconst=[0.001597,-0.076100,1.088284]
Pinned vertex 155: set to xconst=[0.026334,-0.070899,1.088328]
Pinned vertex 156: set to xconst=[0.048792,-0.057935,1.088263]
...and 19 more pinned vertices (total: 22)
8. Pinned vertices updated
=== Final vertex states ===
Vertex 0 (free): pos=[0.001756,-0.082788,1.048798] vel=[0.000474,0.005743,-0.002056]
Vertex 1 (free): pos=[0.029268,-0.078365,1.049004] vel=[0.000098,0.006148,-0.002313]
Vertex 2 (free): pos=[0.053805,-0.063816,1.048590] vel=[-0.000809,0.006841,-0.002336]
Vertex 154 (pinned): pos=[0.001597,-0.076100,1.088284]
Vertex 155 (pinned): pos=[0.026334,-0.070899,1.088328]
Vertex 156 (pinned): pos=[0.048792,-0.057935,1.088263]
Summary: 198 total vertices (176 free, 22 pinned)
DEBUG: SIM_cloth_solve() end - success
DEBUG: do_step_cloth() - SIM_cloth_solve returned 1
DEBUG: do_step_cloth() - calling BKE_effectors_free
DEBUG: do_step_cloth() end - ret=1
DEBUG: clothModifier_do() - do_step_cloth success
DEBUG: clothModifier_do() - writing cache (normal mode)
DEBUG: clothModifier_do() - calling cloth_to_object
DEBUG: cloth_to_object() start
cloth_to_object: copying 198 vertices to mesh
DEBUG: cloth_is_upbge_single_frame_mode() start - current_frame=5, last_frame=5
DEBUG: cloth_is_upbge_single_frame_mode() end - result=1
  Free Vertex 0: using x=[0.001756,-0.082788,1.048798] -> mesh_pos=[0.001756,-0.082788,1.008459]
  Free Vertex 1: using x=[0.029268,-0.078365,1.049004] -> mesh_pos=[0.029268,-0.078365,1.008658]
  Free Vertex 2: using x=[0.053805,-0.063816,1.048590] -> mesh_pos=[0.053805,-0.063816,1.008260]
  Pinned Vertex 154: using xconst=[0.001597,-0.076100,1.088284] -> mesh_pos=[0.001597,-0.076100,1.046427]
  Pinned Vertex 155: using xconst=[0.026334,-0.070899,1.088328] -> mesh_pos=[0.026334,-0.070899,1.046469]
  Pinned Vertex 156: using xconst=[0.048792,-0.057935,1.088263] -> mesh_pos=[0.048792,-0.057935,1.046407]
  Total vertices processed: 198 (pinned=22, free=176)
UPBGE single frame mode: using pinned vertex constraint positions
DEBUG: cloth_to_object() end
DEBUG: clothModifier_do() end - success
MOD_cloth: copying cloth results back to mesh positions
  MOD_cloth Vertex 0: pos=[0.001756,-0.082788,1.008459]
  MOD_cloth Vertex 1: pos=[0.029268,-0.078365,1.008658]
  MOD_cloth Vertex 2: pos=[0.053805,-0.063816,1.008260]
  MOD_cloth Vertex 3: pos=[0.073228,-0.041725,1.007766]
  MOD_cloth Vertex 4: pos=[0.089919,-0.016846,1.008227]
MOD_cloth: forced object data re-evaluation
Should simulate...
DEBUG: clothModifier_do() start
DEBUG: clothModifier_do() - framenr=7
DEBUG: clothModifier_do() - calling do_init_cloth
DEBUG: do_init_cloth() start - framenr=7
DEBUG: do_init_cloth() end - success
DEBUG: clothModifier_do() - do_init_cloth success
need_first_init = 0
DEBUG: cloth_is_upbge_single_frame_mode() start - current_frame=7, last_frame=6
DEBUG: cloth_is_upbge_single_frame_mode() end - result=0
cache_flag_baked = 1 --- is_single_frame_mode = 0 --- can_simulate = 1
framenr = 7, last_frame = 6, timescale = 1.000000
DEBUG: clothModifier_do() - calling BKE_ptcache_read
DEBUG: clothModifier_do() - cache_result=0
DEBUG: clothModifier_do() - starting simulation
DEBUG: clothModifier_do() - calling do_step_cloth
DEBUG: do_step_cloth() start - framenr=7
Object matrix unchanged
Vertex 0: local=[-0.000018,-0.111819,1.024277] world=[-0.000018,-0.111819,1.065248]
DEBUG: do_step_cloth() - calling BKE_effectors_create
DEBUG: do_step_cloth() - calling cloth_apply_vgroup
DEBUG: cloth_apply_vgroup() start
DEBUG: cloth_apply_vgroup() end
DEBUG: do_step_cloth() - calling cloth_update_springs
DEBUG: cloth_update_springs() start
DEBUG: cloth_update_springs() end
DEBUG: do_step_cloth() - calling SIM_cloth_solve
DEBUG: SIM_cloth_solve() start - frame=7.000000
DEBUG: SIM_cloth_solve() - is_hair=0
SIM_cloth_solve: tf=1.000000, dt_base=0.200000, timescale=1.000000, dt_final=0.200000
cloth_solve--tf-- 1.000000
=== Physics step 0.000000 ===
DEBUG: cloth_setup_constraints() start
DEBUG: cloth_setup_constraints() end
1. Constraints setup completed
2. Forces cleared
DEBUG: cloth_calc_force() start - time=0.000000
cloth_calc_force called - mvert_num: 198, avg_spring_len: 0.036831
Gravity enabled: [0.000000, 0.000000, -0.019620]
  Spring 1 (active): type=12, ij=153, kl=176, restlen=0.059902, stiffness=0.000000
  Spring 2 (active): type=12, ij=132, kl=197, restlen=0.059063, stiffness=0.000000
  Spring 3 (active): type=12, ij=152, kl=197, restlen=0.060961, stiffness=0.000000
  Spring 4 (active): type=12, ij=153, kl=196, restlen=0.058732, stiffness=0.000000
  Spring 5 (active): type=12, ij=151, kl=196, restlen=0.061899, stiffness=0.000000
cloth_calc_force completed - total springs: 726 (active: 726, inactive: 0)
  ...and 721 more active springs not shown
DEBUG: cloth_calc_force() end
3. Forces calculated
DEBUG: SIM_mass_spring_solve_velocities() start (Blender) - dt=0.200000
DEBUG: SIM_mass_spring_solve_velocities() end (Blender) - status=1
4. Velocities solved - status: 1, iterations: 9
DEBUG: cloth_solve_collisions() start - step=0.000000, dt=0.200000
DEBUG: SIM_mass_spring_solve_positions() start (Blender) - dt=0.200000
DEBUG: SIM_mass_spring_solve_positions() end (Blender)
DEBUG: cloth_solve_collisions() - iteration 1/1
DEBUG: cloth_bvh_collision() start - step=0.000000, dt=0.200000
DEBUG: ret variables initialized - ret=0, ret2=0
DEBUG: cloth_bvh_collision() - cloth_bvh=000001FEBCD77D88, cloth=000001FEBB2173D8
DEBUG: cloth_bvh_collision() - single_frame=0, max_loops=2
DEBUG: bvhtree_update_from_cloth() start - moving=0, self=0
DEBUG: bvhtree_update_from_cloth() end
DEBUG: collision loop iteration 1 - resetting ret2 to 0
DEBUG: cloth_bvh_objcollisions_resolve() start - numcollobj=3, dt=0.200000
DEBUG: objcollisions_resolve - ret initialized to 0
DEBUG: objcollisions_resolve - iteration 0, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 0 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 105 vertices, ret now=105
DEBUG: objcollisions_resolve - iteration 1, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 1 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 105 vertices, ret now=210
DEBUG: objcollisions_resolve() end - returning ret=210
DEBUG: object collision resolve returned 210
DEBUG: after object collisions - ret=210 (added 210)
DEBUG: after object collisions - ret2=210 (added 210)
DEBUG: applying collision resolution - ret2=210, updating 198 vertices
DEBUG: end of collision loop round 1 - ret=210, ret2=210
DEBUG: collision loop iteration 2 - resetting ret2 to 0
DEBUG: cloth_bvh_objcollisions_resolve() start - numcollobj=3, dt=0.200000
DEBUG: objcollisions_resolve - ret initialized to 0
DEBUG: objcollisions_resolve - iteration 0, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 0 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 96 vertices, ret now=96
DEBUG: objcollisions_resolve - iteration 1, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 1 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 96 vertices, ret now=192
DEBUG: objcollisions_resolve() end - returning ret=192
DEBUG: object collision resolve returned 192
DEBUG: after object collisions - ret=402 (added 192)
DEBUG: after object collisions - ret2=402 (added 402)
DEBUG: applying collision resolution - ret2=402, updating 198 vertices
DEBUG: end of collision loop round 2 - ret=402, ret2=402
DEBUG: cloth_bvh_collision() end - ret=402, rounds=2
DEBUG: final return value - min(ret=402, 1) = 1
DEBUG: cloth_solve_collisions() end - completed 1 collision iterations
5. Collisions processed
DEBUG: SIM_mass_spring_solve_positions() start (Blender) - dt=0.200000
DEBUG: SIM_mass_spring_solve_positions() end (Blender)
6. Positions solved
DEBUG: SIM_mass_spring_apply_result() start (Blender)
DEBUG: SIM_mass_spring_apply_result() end (Blender)
7. Results applied
Pinned vertex 154: set to xconst=[0.001632,-0.075877,1.088275]
Pinned vertex 155: set to xconst=[0.026371,-0.070686,1.088321]
Pinned vertex 156: set to xconst=[0.048834,-0.057730,1.088259]
...and 19 more pinned vertices (total: 22)
8. Pinned vertices updated
=== Physics step 0.200000 ===
DEBUG: cloth_setup_constraints() start
DEBUG: cloth_setup_constraints() end
1. Constraints setup completed
2. Forces cleared
DEBUG: cloth_calc_force() start - time=0.200000
cloth_calc_force called - mvert_num: 198, avg_spring_len: 0.036831
Gravity enabled: [0.000000, 0.000000, -0.019620]
  Spring 1 (active): type=12, ij=153, kl=176, restlen=0.059902, stiffness=0.000000
  Spring 2 (active): type=12, ij=132, kl=197, restlen=0.059063, stiffness=0.000000
  Spring 3 (active): type=12, ij=152, kl=197, restlen=0.060961, stiffness=0.000000
  Spring 4 (active): type=12, ij=153, kl=196, restlen=0.058732, stiffness=0.000000
  Spring 5 (active): type=12, ij=151, kl=196, restlen=0.061899, stiffness=0.000000
cloth_calc_force completed - total springs: 726 (active: 726, inactive: 0)
  ...and 721 more active springs not shown
DEBUG: cloth_calc_force() end
3. Forces calculated
DEBUG: SIM_mass_spring_solve_velocities() start (Blender) - dt=0.200000
DEBUG: SIM_mass_spring_solve_velocities() end (Blender) - status=1
4. Velocities solved - status: 1, iterations: 8
DEBUG: cloth_solve_collisions() start - step=0.200000, dt=0.200000
DEBUG: SIM_mass_spring_solve_positions() start (Blender) - dt=0.200000
DEBUG: SIM_mass_spring_solve_positions() end (Blender)
DEBUG: cloth_solve_collisions() - iteration 1/1
DEBUG: cloth_bvh_collision() start - step=0.200000, dt=0.200000
DEBUG: ret variables initialized - ret=0, ret2=0
DEBUG: cloth_bvh_collision() - cloth_bvh=000001FEBCD77D88, cloth=000001FEBB2173D8
DEBUG: cloth_bvh_collision() - single_frame=0, max_loops=2
DEBUG: bvhtree_update_from_cloth() start - moving=0, self=0
DEBUG: bvhtree_update_from_cloth() end
DEBUG: collision loop iteration 1 - resetting ret2 to 0
DEBUG: cloth_bvh_objcollisions_resolve() start - numcollobj=3, dt=0.200000
DEBUG: objcollisions_resolve - ret initialized to 0
DEBUG: objcollisions_resolve - iteration 0, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 0 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 107 vertices, ret now=107
DEBUG: objcollisions_resolve - iteration 1, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 1 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 107 vertices, ret now=214
DEBUG: objcollisions_resolve() end - returning ret=214
DEBUG: object collision resolve returned 214
DEBUG: after object collisions - ret=214 (added 214)
DEBUG: after object collisions - ret2=214 (added 214)
DEBUG: applying collision resolution - ret2=214, updating 198 vertices
DEBUG: end of collision loop round 1 - ret=214, ret2=214
DEBUG: collision loop iteration 2 - resetting ret2 to 0
DEBUG: cloth_bvh_objcollisions_resolve() start - numcollobj=3, dt=0.200000
DEBUG: objcollisions_resolve - ret initialized to 0
DEBUG: objcollisions_resolve - iteration 0, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 0 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 99 vertices, ret now=99
DEBUG: objcollisions_resolve - iteration 1, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 1 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 99 vertices, ret now=198
DEBUG: objcollisions_resolve() end - returning ret=198
DEBUG: object collision resolve returned 198
DEBUG: after object collisions - ret=412 (added 198)
DEBUG: after object collisions - ret2=412 (added 412)
DEBUG: applying collision resolution - ret2=412, updating 198 vertices
DEBUG: end of collision loop round 2 - ret=412, ret2=412
DEBUG: cloth_bvh_collision() end - ret=412, rounds=2
DEBUG: final return value - min(ret=412, 1) = 1
DEBUG: cloth_solve_collisions() end - completed 1 collision iterations
5. Collisions processed
DEBUG: SIM_mass_spring_solve_positions() start (Blender) - dt=0.200000
DEBUG: SIM_mass_spring_solve_positions() end (Blender)
6. Positions solved
DEBUG: SIM_mass_spring_apply_result() start (Blender)
DEBUG: SIM_mass_spring_apply_result() end (Blender)
7. Results applied
Pinned vertex 154: set to xconst=[0.001632,-0.075877,1.088275]
Pinned vertex 155: set to xconst=[0.026371,-0.070686,1.088321]
Pinned vertex 156: set to xconst=[0.048834,-0.057730,1.088259]
...and 19 more pinned vertices (total: 22)
8. Pinned vertices updated
=== Physics step 0.400000 ===
DEBUG: cloth_setup_constraints() start
DEBUG: cloth_setup_constraints() end
1. Constraints setup completed
2. Forces cleared
DEBUG: cloth_calc_force() start - time=0.400000
cloth_calc_force called - mvert_num: 198, avg_spring_len: 0.036831
Gravity enabled: [0.000000, 0.000000, -0.019620]
  Spring 1 (active): type=12, ij=153, kl=176, restlen=0.059902, stiffness=0.000000
  Spring 2 (active): type=12, ij=132, kl=197, restlen=0.059063, stiffness=0.000000
  Spring 3 (active): type=12, ij=152, kl=197, restlen=0.060961, stiffness=0.000000
  Spring 4 (active): type=12, ij=153, kl=196, restlen=0.058732, stiffness=0.000000
  Spring 5 (active): type=12, ij=151, kl=196, restlen=0.061899, stiffness=0.000000
cloth_calc_force completed - total springs: 726 (active: 726, inactive: 0)
  ...and 721 more active springs not shown
DEBUG: cloth_calc_force() end
3. Forces calculated
DEBUG: SIM_mass_spring_solve_velocities() start (Blender) - dt=0.200000
DEBUG: SIM_mass_spring_solve_velocities() end (Blender) - status=1
4. Velocities solved - status: 1, iterations: 8
DEBUG: cloth_solve_collisions() start - step=0.400000, dt=0.200000
DEBUG: SIM_mass_spring_solve_positions() start (Blender) - dt=0.200000
DEBUG: SIM_mass_spring_solve_positions() end (Blender)
DEBUG: cloth_solve_collisions() - iteration 1/1
DEBUG: cloth_bvh_collision() start - step=0.400000, dt=0.200000
DEBUG: ret variables initialized - ret=0, ret2=0
DEBUG: cloth_bvh_collision() - cloth_bvh=000001FEBCD77D88, cloth=000001FEBB2173D8
DEBUG: cloth_bvh_collision() - single_frame=0, max_loops=2
DEBUG: bvhtree_update_from_cloth() start - moving=0, self=0
DEBUG: bvhtree_update_from_cloth() end
DEBUG: collision loop iteration 1 - resetting ret2 to 0
DEBUG: cloth_bvh_objcollisions_resolve() start - numcollobj=3, dt=0.200000
DEBUG: objcollisions_resolve - ret initialized to 0
DEBUG: objcollisions_resolve - iteration 0, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 0 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 105 vertices, ret now=105
DEBUG: objcollisions_resolve - iteration 1, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 1 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 105 vertices, ret now=210
DEBUG: objcollisions_resolve() end - returning ret=210
DEBUG: object collision resolve returned 210
DEBUG: after object collisions - ret=210 (added 210)
DEBUG: after object collisions - ret2=210 (added 210)
DEBUG: applying collision resolution - ret2=210, updating 198 vertices
DEBUG: end of collision loop round 1 - ret=210, ret2=210
DEBUG: collision loop iteration 2 - resetting ret2 to 0
DEBUG: cloth_bvh_objcollisions_resolve() start - numcollobj=3, dt=0.200000
DEBUG: objcollisions_resolve - ret initialized to 0
DEBUG: objcollisions_resolve - iteration 0, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 0 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 93 vertices, ret now=93
DEBUG: objcollisions_resolve - iteration 1, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 1 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 93 vertices, ret now=186
DEBUG: objcollisions_resolve() end - returning ret=186
DEBUG: object collision resolve returned 186
DEBUG: after object collisions - ret=396 (added 186)
DEBUG: after object collisions - ret2=396 (added 396)
DEBUG: applying collision resolution - ret2=396, updating 198 vertices
DEBUG: end of collision loop round 2 - ret=396, ret2=396
DEBUG: cloth_bvh_collision() end - ret=396, rounds=2
DEBUG: final return value - min(ret=396, 1) = 1
DEBUG: cloth_solve_collisions() end - completed 1 collision iterations
5. Collisions processed
DEBUG: SIM_mass_spring_solve_positions() start (Blender) - dt=0.200000
DEBUG: SIM_mass_spring_solve_positions() end (Blender)
6. Positions solved
DEBUG: SIM_mass_spring_apply_result() start (Blender)
DEBUG: SIM_mass_spring_apply_result() end (Blender)
7. Results applied
Pinned vertex 154: set to xconst=[0.001632,-0.075877,1.088275]
Pinned vertex 155: set to xconst=[0.026371,-0.070686,1.088321]
Pinned vertex 156: set to xconst=[0.048834,-0.057730,1.088259]
...and 19 more pinned vertices (total: 22)
8. Pinned vertices updated
=== Physics step 0.600000 ===
DEBUG: cloth_setup_constraints() start
DEBUG: cloth_setup_constraints() end
1. Constraints setup completed
2. Forces cleared
DEBUG: cloth_calc_force() start - time=0.600000
cloth_calc_force called - mvert_num: 198, avg_spring_len: 0.036831
Gravity enabled: [0.000000, 0.000000, -0.019620]
  Spring 1 (active): type=12, ij=153, kl=176, restlen=0.059902, stiffness=0.000000
  Spring 2 (active): type=12, ij=132, kl=197, restlen=0.059063, stiffness=0.000000
  Spring 3 (active): type=12, ij=152, kl=197, restlen=0.060961, stiffness=0.000000
  Spring 4 (active): type=12, ij=153, kl=196, restlen=0.058732, stiffness=0.000000
  Spring 5 (active): type=12, ij=151, kl=196, restlen=0.061899, stiffness=0.000000
cloth_calc_force completed - total springs: 726 (active: 726, inactive: 0)
  ...and 721 more active springs not shown
DEBUG: cloth_calc_force() end
3. Forces calculated
DEBUG: SIM_mass_spring_solve_velocities() start (Blender) - dt=0.200000
DEBUG: SIM_mass_spring_solve_velocities() end (Blender) - status=1
4. Velocities solved - status: 1, iterations: 8
DEBUG: cloth_solve_collisions() start - step=0.600000, dt=0.200000
DEBUG: SIM_mass_spring_solve_positions() start (Blender) - dt=0.200000
DEBUG: SIM_mass_spring_solve_positions() end (Blender)
DEBUG: cloth_solve_collisions() - iteration 1/1
DEBUG: cloth_bvh_collision() start - step=0.600000, dt=0.200000
DEBUG: ret variables initialized - ret=0, ret2=0
DEBUG: cloth_bvh_collision() - cloth_bvh=000001FEBCD77D88, cloth=000001FEBB2173D8
DEBUG: cloth_bvh_collision() - single_frame=0, max_loops=2
DEBUG: bvhtree_update_from_cloth() start - moving=0, self=0
DEBUG: bvhtree_update_from_cloth() end
DEBUG: collision loop iteration 1 - resetting ret2 to 0
DEBUG: cloth_bvh_objcollisions_resolve() start - numcollobj=3, dt=0.200000
DEBUG: objcollisions_resolve - ret initialized to 0
DEBUG: objcollisions_resolve - iteration 0, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 0 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 106 vertices, ret now=106
DEBUG: objcollisions_resolve - iteration 1, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 1 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 106 vertices, ret now=212
DEBUG: objcollisions_resolve() end - returning ret=212
DEBUG: object collision resolve returned 212
DEBUG: after object collisions - ret=212 (added 212)
DEBUG: after object collisions - ret2=212 (added 212)
DEBUG: applying collision resolution - ret2=212, updating 198 vertices
DEBUG: end of collision loop round 1 - ret=212, ret2=212
DEBUG: collision loop iteration 2 - resetting ret2 to 0
DEBUG: cloth_bvh_objcollisions_resolve() start - numcollobj=3, dt=0.200000
DEBUG: objcollisions_resolve - ret initialized to 0
DEBUG: objcollisions_resolve - iteration 0, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 0 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 94 vertices, ret now=94
DEBUG: objcollisions_resolve - iteration 1, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 1 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 94 vertices, ret now=188
DEBUG: objcollisions_resolve() end - returning ret=188
DEBUG: object collision resolve returned 188
DEBUG: after object collisions - ret=400 (added 188)
DEBUG: after object collisions - ret2=400 (added 400)
DEBUG: applying collision resolution - ret2=400, updating 198 vertices
DEBUG: end of collision loop round 2 - ret=400, ret2=400
DEBUG: cloth_bvh_collision() end - ret=400, rounds=2
DEBUG: final return value - min(ret=400, 1) = 1
DEBUG: cloth_solve_collisions() end - completed 1 collision iterations
5. Collisions processed
DEBUG: SIM_mass_spring_solve_positions() start (Blender) - dt=0.200000
DEBUG: SIM_mass_spring_solve_positions() end (Blender)
6. Positions solved
DEBUG: SIM_mass_spring_apply_result() start (Blender)
DEBUG: SIM_mass_spring_apply_result() end (Blender)
7. Results applied
Pinned vertex 154: set to xconst=[0.001632,-0.075877,1.088275]
Pinned vertex 155: set to xconst=[0.026371,-0.070686,1.088321]
Pinned vertex 156: set to xconst=[0.048834,-0.057730,1.088259]
...and 19 more pinned vertices (total: 22)
8. Pinned vertices updated
=== Physics step 0.800000 ===
DEBUG: cloth_setup_constraints() start
DEBUG: cloth_setup_constraints() end
1. Constraints setup completed
2. Forces cleared
DEBUG: cloth_calc_force() start - time=0.800000
cloth_calc_force called - mvert_num: 198, avg_spring_len: 0.036831
Gravity enabled: [0.000000, 0.000000, -0.019620]
  Spring 1 (active): type=12, ij=153, kl=176, restlen=0.059902, stiffness=0.000000
  Spring 2 (active): type=12, ij=132, kl=197, restlen=0.059063, stiffness=0.000000
  Spring 3 (active): type=12, ij=152, kl=197, restlen=0.060961, stiffness=0.000000
  Spring 4 (active): type=12, ij=153, kl=196, restlen=0.058732, stiffness=0.000000
  Spring 5 (active): type=12, ij=151, kl=196, restlen=0.061899, stiffness=0.000000
cloth_calc_force completed - total springs: 726 (active: 726, inactive: 0)
  ...and 721 more active springs not shown
DEBUG: cloth_calc_force() end
3. Forces calculated
DEBUG: SIM_mass_spring_solve_velocities() start (Blender) - dt=0.200000
DEBUG: SIM_mass_spring_solve_velocities() end (Blender) - status=1
4. Velocities solved - status: 1, iterations: 8
DEBUG: cloth_solve_collisions() start - step=0.800000, dt=0.200000
DEBUG: SIM_mass_spring_solve_positions() start (Blender) - dt=0.200000
DEBUG: SIM_mass_spring_solve_positions() end (Blender)
DEBUG: cloth_solve_collisions() - iteration 1/1
DEBUG: cloth_bvh_collision() start - step=0.800000, dt=0.200000
DEBUG: ret variables initialized - ret=0, ret2=0
DEBUG: cloth_bvh_collision() - cloth_bvh=000001FEBCD77D88, cloth=000001FEBB2173D8
DEBUG: cloth_bvh_collision() - single_frame=0, max_loops=2
DEBUG: bvhtree_update_from_cloth() start - moving=0, self=0
DEBUG: bvhtree_update_from_cloth() end
DEBUG: collision loop iteration 1 - resetting ret2 to 0
DEBUG: cloth_bvh_objcollisions_resolve() start - numcollobj=3, dt=0.200000
DEBUG: objcollisions_resolve - ret initialized to 0
DEBUG: objcollisions_resolve - iteration 0, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 0 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 105 vertices, ret now=105
DEBUG: objcollisions_resolve - iteration 1, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 1 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 105 vertices, ret now=210
DEBUG: objcollisions_resolve() end - returning ret=210
DEBUG: object collision resolve returned 210
DEBUG: after object collisions - ret=210 (added 210)
DEBUG: after object collisions - ret2=210 (added 210)
DEBUG: applying collision resolution - ret2=210, updating 198 vertices
DEBUG: end of collision loop round 1 - ret=210, ret2=210
DEBUG: collision loop iteration 2 - resetting ret2 to 0
DEBUG: cloth_bvh_objcollisions_resolve() start - numcollobj=3, dt=0.200000
DEBUG: objcollisions_resolve - ret initialized to 0
DEBUG: objcollisions_resolve - iteration 0, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 0 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 93 vertices, ret now=93
DEBUG: objcollisions_resolve - iteration 1, result reset to 0
DEBUG: objcollisions_resolve - object 0 collision response: 1
DEBUG: objcollisions_resolve - after object 0, result=1
DEBUG: objcollisions_resolve - object 1 collision response: 1
DEBUG: objcollisions_resolve - after object 1, result=2
DEBUG: objcollisions_resolve - object 2 collision response: 1
DEBUG: objcollisions_resolve - after object 2, result=3
DEBUG: objcollisions_resolve - iteration 1 completed, total result=3
DEBUG: objcollisions_resolve - applying impulses, result=3
DEBUG: objcollisions_resolve - applied impulses to 93 vertices, ret now=186
DEBUG: objcollisions_resolve() end - returning ret=186
DEBUG: object collision resolve returned 186
DEBUG: after object collisions - ret=396 (added 186)
DEBUG: after object collisions - ret2=396 (added 396)
DEBUG: applying collision resolution - ret2=396, updating 198 vertices
DEBUG: end of collision loop round 2 - ret=396, ret2=396
DEBUG: cloth_bvh_collision() end - ret=396, rounds=2
DEBUG: final return value - min(ret=396, 1) = 1
DEBUG: cloth_solve_collisions() end - completed 1 collision iterations
5. Collisions processed
DEBUG: SIM_mass_spring_solve_positions() start (Blender) - dt=0.200000
DEBUG: SIM_mass_spring_solve_positions() end (Blender)
6. Positions solved
DEBUG: SIM_mass_spring_apply_result() start (Blender)
DEBUG: SIM_mass_spring_apply_result() end (Blender)
7. Results applied
Pinned vertex 154: set to xconst=[0.001632,-0.075877,1.088275]
Pinned vertex 155: set to xconst=[0.026371,-0.070686,1.088321]
Pinned vertex 156: set to xconst=[0.048834,-0.057730,1.088259]
...and 19 more pinned vertices (total: 22)
8. Pinned vertices updated
=== Final vertex states ===
Vertex 0 (free): pos=[0.002088,-0.084082,1.048709] vel=[0.000212,-0.004642,0.000228]
Vertex 1 (free): pos=[0.030068,-0.078029,1.048733] vel=[0.001165,-0.002671,0.000205]
Vertex 2 (free): pos=[0.054952,-0.063345,1.048679] vel=[0.002429,-0.002899,0.000457]
Vertex 154 (pinned): pos=[0.001632,-0.075877,1.088275]
Vertex 155 (pinned): pos=[0.026371,-0.070686,1.088321]
Vertex 156 (pinned): pos=[0.048834,-0.057730,1.088259]
Summary: 198 total vertices (176 free, 22 pinned)
DEBUG: SIM_cloth_solve() end - success
DEBUG: do_step_cloth() - SIM_cloth_solve returned 1
DEBUG: do_step_cloth() - calling BKE_effectors_free
DEBUG: do_step_cloth() end - ret=1
DEBUG: clothModifier_do() - do_step_cloth success
DEBUG: clothModifier_do() - writing cache (normal mode)
DEBUG: clothModifier_do() - calling cloth_to_object
DEBUG: cloth_to_object() start
cloth_to_object: copying 198 vertices to mesh
DEBUG: cloth_is_upbge_single_frame_mode() start - current_frame=6, last_frame=6
DEBUG: cloth_is_upbge_single_frame_mode() end - result=1
  Free Vertex 0: using x=[0.002088,-0.084082,1.048709] -> mesh_pos=[0.002088,-0.084082,1.008374]
  Free Vertex 1: using x=[0.030068,-0.078029,1.048733] -> mesh_pos=[0.030068,-0.078029,1.008397]
  Free Vertex 2: using x=[0.054952,-0.063345,1.048679] -> mesh_pos=[0.054952,-0.063345,1.008346]
  Pinned Vertex 154: using xconst=[0.001632,-0.075877,1.088275] -> mesh_pos=[0.001632,-0.075877,1.046418]
  Pinned Vertex 155: using xconst=[0.026371,-0.070686,1.088321] -> mesh_pos=[0.026371,-0.070686,1.046463]
  Pinned Vertex 156: using xconst=[0.048834,-0.057730,1.088259] -> mesh_pos=[0.048834,-0.057730,1.046403]
  Total vertices processed: 198 (pinned=22, free=176)
UPBGE single frame mode: using pinned vertex constraint positions
DEBUG: cloth_to_object() end
DEBUG: clothModifier_do() end - success
MOD_cloth: copying cloth results back to mesh positions
  MOD_cloth Vertex 0: pos=[0.002088,-0.084082,1.008374]
  MOD_cloth Vertex 1: pos=[0.030068,-0.078029,1.008397]
  MOD_cloth Vertex 2: pos=[0.054952,-0.063345,1.008346]
  MOD_cloth Vertex 3: pos=[0.074666,-0.041021,1.007778]
  MOD_cloth Vertex 4: pos=[0.092200,-0.015700,1.008749]
MOD_cloth: forced object data re-evaluation
