MZ�       ��  �       @                                      � �	�!�L�!This program cannot be run in DOS mode.

$       r
r6kb!6kb!6kb!?�!<kb!dc 4kb!dg =kb!df >kb!da 5kb!�c 4kb!" c ?kb!6kc!Lkb!�j 7kb!�b 7kb!��!7kb!�` 7kb!Rich6kb!        PE  d� 玶玤        � "  .   T      p2        �                        �          `                                   衎  H   c  �    �  x	   �  |           �  �   圴  T                           郪  8           @  �                          .text   �-      .                    `.rdata  x/   @   0   2              @  @.data      p      b              @  �.pdata  |   �      n              @  @.rsrc   x	   �   
   t              @  @.reloc  �    �      ~              @  B                                                                                                                                                                                                                                                                        H塡$VH冹 H嬺��2  H嬝H吚u6H�
v3  H�	�
3  �   A�   吚AE葔��2  3繦媆$8H兡 ^�3襀墊$0H嬎��2  H孁H吚uA�   D���2  H�+u	H嬎��2  H媆$8H嬊H媩$0H兡 ^锰烫烫虌蒆�%�2  烫烫烫蘃�%2  烫烫烫烫蘃塡$H塴$H塼$WH冹 嬟I嬹I嬔A嬭����H孁H吚uH岹��0弘D嬇嬘H嬒��0  H嬒Hc��1  凔�u
�50  ��H嬅H媆$0H媗$8H媡$@H兡 _锰烫烫烫烫H塡$H塼$WH冹 I嬹A嬝�E0  孁;胻��/  �嬊�H媆$0H媡$8H兡 _锰烫H塡$H塼$WH冹 I嬹A嬝��/  孁;胻��/  �嬊�H媆$0H媡$8H兡 _锰烫H塡$WH冹 H孃��/  嬝吚t�q/  �嬅�H媆$0H兡 _肏塡$WH冹 I孂��/  嬝凐�u�@/  �嬅�H媆$0H兡 _锰烫烫烫烫烫烫烫H塼$WH冹 H嬺杈��H孁H吚u岹�H媡$8H兡 _肏嬒H塡$0��.  H嬒嬝��0  呟t
��.  ��H媡$8嬅H媆$0H兡 _锰烫烫烫烫烫烫烫3烂烫烫烫烫烫烫蘃塡$WH冹 孃H嬞H��1  H�
�1  ��.  L嬋H吚tUI抢����I�繠�<  u鯠;莭6I+�@ �     ��H岪勆u騃嬌�.  �   H媆$0H兡 _肐嬌��-  H媆$03繦兡 _锰烫H冹8I嬃M吷tAD塂$(D嬌塗$ L�m1  H�歚  H嬋�Q/  H吚u
����H兡8肏�(u	H嬋��/  3繦兡8锰烫烫烫烫烫蘃塡$WH冹 I嬂H孂M吚tlD婭L� 1  H嬋H�&`  ��.  H嬝H吚u岰�H媆$0H兡 _肔婡A鱻�      u7H�
�.  H��0  M婡H�	�N/  H�+u	H嬎�//  3繦媆$0H兡 _肏嬎��/  H崗"  A�   H嬓�{-  H媆$0�   H兡 _锰烫@UVWAVH侅�   H�擺  H3腍墑$�   H嬺M嬹H嫈$�   I嬭鑛��H孁H吚u	H岹�槿   E3蒆荄$0    荄$(�   �   �H嬒H墱$�   荄$    E岮��+  H嬝H凐�tH峊$HH嬋��+  吚uH嬎��+  H嬒��-  H抢����隩H峊$@H峀$\��+  L嬇H峀$@H嬛��+  稤$HH嬎f冟'fA��w+  簚�  H嬒�9,  H嬒Hc��-  H嬅H嫓$�   H媽$�   H3惕�  H伳�   A^_^]锰烫烫@UAVH崿$��H侅�  H�^Z  H3腍墔�  I婡M嬸鱻�      uH�
-  H�C/  H�	��-  3篱�  �H嬍H墱$  H壌$�  H壖$�  3�L壃$�  A�   D塵鱀塵ㄇE���H荅擛B H墋渇墋癅坿矂纓*E嬇L+�<\t</uI�4禔H�羷纔镠侢   兇  H�2H抢����H�繞8<u鱄=   償  H咑~5L嬈H崓�  �=&  H崓�   @埣5�  H+�@ ��H峓劺u螂>��.  H崓�   f墔�  H+��o.  垍�  fff�     ��H峈劺u騂墊$`H岴怘塂$XL�
濝��H�桘��H塂$PL�z��H���H塂$HH�g��H�苞��H塂$@H峂�H�p��H塂$8H�$��H塂$0H�伫��H塂$(H�L��H塂$ ��(  H嬝H吚u"H�
�+  H��-  D婨�H�	��+  3篱�   H�5慃��L墹$�  L壖$�  L�=冫��I9~~vL�%岦��@ �     I婩L峀$pL岲$xH�k-  H���*  吚劜   L婦$pE3蒆婽$xH嬎fD塴$8L塪$0H塼$(L墊$ ��'  吚剴   H�荌;~|滾嬑M嬊3襀嬎��'  吚ttH嬎��'  吚tgH�+  H� L嫟$�  L嫾$�  H嫾$�  H嫶$�  H嫓$  L嫭$�  H媿�  H3惕�  H伳�  A^]肏�
*  H��,  H�	�8儅� H�
�*  H�	t!D婨�H�i,  �s*  H嬎�'  3篱r���H��,  �f*  H嬎��&  3篱U���H�
G*  H�,  H�	�?*  3篱G���烫烫烫烫@SH冹 H嬞婭��(  H嬎荂    H兡 [H�%�)  烫烫烫烫H塡$H塼$WH侅  H�V  H3腍墑$   嬞荄$ �  H峾$0�g(  嬸吚�   凔n彊   t~嬎冮t^冮t@冮t"凒J厰   H�
!g  H�*,  ��)  閟  H�
g  H��+  �k)  閆  H�
飂  H��+  �R)  锳  H�
謋  H��+  �9)  �(  H�
絝  H��+  � )  �  嬎侀[  t[冮t=侚@
  tH�
峟  H��+  D嬅��(  檐   H�
qf  H�j+  ��(  槊   H�
Xf  H��+  ��(  楠   H�
?f  H�+  ��(  閼   �   嬑�X'  L峀$ 嬛L岲$03��'  =�   u>婰$ ��)&  H孁H吚u嬑�	'  ��'  隚L峀$ L嬊嬛3�g'  婦$ �8 嬑��&  H�
竐  H嬜�(  H岲$0H;鴗	H嬒��%  3繦媽$   H3惕�  L崪$  I媅I媠 I嬨_锰烫烫H冹HH�mT  H3腍塂$8H峀$(�"%  =�  u+H�
,'  H��(  H�	��'  3繦婰$8H3惕U  H兡H肏峊$ H峀$(��$  凐u+H�
�'  H��(  H�	�d'  3繦婰$8H3惕  H兡H肏婰$ H锹����H塡$@H�耭�<Q u�7'  H峀$ H嬝��$  H嬅H媆$@H婰$8H3惕�  H兡H锰烫烫烫@SH冹 M嬓H嬟I凐tA�   H�
�)  E嬃I嬕�5'  吚t1H�H婣鱻�      u&L嬌L��)  H�
�)  H��)  ��&  3繦兡 [肏峊$@��&  H吚t鐷橇����怘�羳< u鱄;L$@tH�
a&  H�r)  H�	�Y&  3繦兡 [肔婥H嬓鑕��H兡 [锰烫烫烫烫烫烫烫@SH冹 H嬞婭��$  吚t嬋H兡 [���荂    H�&  H� H兡 [锰烫烫烫H冹(婭��$  嬋H兡(H�%&  烫烫烫H塡$WH冹 H孂H嬍��$  嬝凐�u��%  H吚u&婳嬘�e$  =   �u!H�
c  H�8)  ��%  3繦媆$0H兡 _脣��%  H媆$0H兡 _锰蘃塼$ WH侅�  H�銺  H3腍墑$�  H嬹H嬍�m$  孁凐�u�x%  H吚t3篱�   婲L峀$ H墱$   L岲$0嬜荄$ �  H峔$0��#  =�   u2婰$ �罤��"  H嬝H吚u�V$  隖婲L峀$ L嬂嬜��#  吚t	嬋璁���&婽$ H嬎��$  H孁H岲$0H;胻	H嬎�A"  H嬊H嫓$   H媽$�  H3惕.  H嫶$  H伳�  _锰烫烫烫烫烫烫H冹(婭��"  吚t嬋H兡(�4��H�E$  H� H兡(锰烫烫H塼$WH冹 M嬓H孃H嬹I凐t*A�   H�
"'  E嬃I嬕�N$  吚u
3繦媡$8H兡 _肏�H塡$0�/#  嬝凐�u�$  H吚u2H婳H婣鱻�      u0L嬌L��&  H�
�&  H��&  ��#  H媆$03繦媡$8H兡 _��#  H吚t銒NL嬂嬘��!  吚t嬋H媆$0H媡$8H兡 _镕��H�W#  H媆$0H媡$8H� H兡 _锰烫烫烫烫烫蘃塼$WH冹 M嬓H孃H嬹I凐t*A�   H�
B&  E嬃I嬕�N#  吚u
3繦媡$8H兡 _肏�H塡$0�/"  嬝凐�u�#  H吚u2H婳H婣鱻�      u0L嬌L��%  H�
�%  H��%  ��"  H媆$03繦媡$8H兡 _��"  H吚t銒NL嬂嬘��   吚t嬋H媆$0H媡$8H兡 _镕��H�W"  H媆$0H媡$8H� H兡 _锰烫烫烫烫烫蘃塡$VH冹 M嬓H嬟H嬹I凐t*A�   H�
R%  E嬃I嬕�N"  吚u
3繦媆$8H兡 ^肏�H墊$0�/!  孁凐�u�"  H吚uH婯�!  嬝凐�u��!  H吚tH媩$03繦媆$8H兡 ^脣ND嬅嬜�   吚t嬋H媩$0H媆$8H兡 ^間��H�x!  H媩$0H� H媆$8H兡 ^锰烫烫烫烫烫烫@SUVH侅`  H�芃  H3腍墑$P  H嬮3跦嬍�u   嬸凐�u�X!  H吚t嬅镵  婱H岲$@H塂$0L峀$HH岲$`H壖$�  H塂$(L岲$DH岲$P荄$@�  嬛H塂$ H峾$`�l  =�   uS婦$@�缷葔D$@�[  H孁H吚u�   橄   婱H岲$@H塂$0L峀$HH岲$PH墊$(L岲$DH塂$ 嬛�  吚t嬋鐿��閼   D婦$DA嬂E吚t]冭tI冭tD冭t-H�
�  H�	凐"tH�X$  �   階H�9$  �   �2婽$@H嬒�t  H嬝� 婰$H�   H嬝�H��  H� H��  H岲$`H;鴗	H嬒�t  H嬅H嫾$�  H媽$P  H3惕a  H伳`  ^][锰烫烫蘃冹(婭H峊$0��  吚t嬋鑣��H兡(脣L$0��  H兡(锰烫烫烫烫烫烫烫H塼$WH冹0I嬂H孃H嬹I凐t*A�   H�
�"  E嬃H嬓�~  吚u
3繦媡$HH兡0_肏�H塡$@�_  嬝凐�u�B  H吚呑   H婳H婣鱻�      t1�  H吚劥   婲E3蒆塂$(嬘H荄$     E岮��  隚H;�  ur�M  孁凐�u��  H吚uq婲D嬒H荄$(    A�   嬘H荄$     �=  吚t嬋H媆$@H媡$HH兡0_镃��H�T  H媆$@H� H媡$HH兡0_肏�
�  H�s"  H�	�  H媆$@3繦媡$HH兡0_肏冹(婭��  吚t嬋H兡(殇��H��  H� H兡(锰烫烫H冹(婭H峊$0�>  =  uH��  H� H兡(脜纓嬋铦��H兡(肏塡$ H�
蘋  媆$0��  H吚u嬎�%  H媆$ 3繦兡(脡XH媆$ H兡(锰烫烫烫烫烫蘃塡$H塼$WH冹 H嬹3跦嬍��  孁凐�u�v  H吚ub婲L岲$@嬜�	  吚t嬋���H媆$0H媡$8H兡 _脣|$@H�
+O  �U  H吚u嬒��  H嬅H媆$0H媡$8H兡 _脡xH嬝H嬅H媆$0H媡$8H兡 _锰烫烫烫烫烫蘃塡$VH冹 I嬂H嬟H嬹I凐t*A�   H�
�  E嬃H嬓��  吚u
3繦媆$8H兡 ^肏�H墊$0��  孁凐�u��  H吚u(H婯H�rN  H9Qt)H�
�  H��   H�	�  H媩$03繦媆$8H兡 ^肈婣嬜婲��  吚t嬋H媩$0H媆$8H兡 ^橼��H��  H媩$0H� H媆$8H兡 ^肏冹(婭��  吚t嬋H兡(椁��H��  H� H兡(锰烫烫@SH冹 H婤H嬞鱻�      u&L嬍L�i  H��  H�
�  �i  3繦兡 [肏嬍��  H吚t陭KL岲$8H嬓�B  吚t
嬋�'��H兡 [肏�
Q  ��  婰$8H吚u��  3繦兡 [脡HH兡 [锰烫蘃冹(婭��  吚t嬋H兡(樵��H��  H� H兡(锰烫烫H塡$WH冹 H孂H嬍�
  嬝凐�u��  H吚uE婳L峀$@D嬅3��  吚t嬋鑪��H媆$0H兡 _肏�
HN  ��  婰$@H吚u�  3繦媆$0H兡 _脡HH媆$0H兡 _锰烫烫烫藹WH冹 I嬂H孃I凐tA�   H�
�  E嬃H嬓�e  吚t1H�H婣鱻�      u&L嬌L��  H�
}  H��  ��  3繦兡 _肏塡$0��  H嬝H吚tH婳�  Hc葍�u��  H吚tH媆$03繦兡 _脙�v岮鄡�v筗   鑤��H媆$0H兡 _肏嬔L岲$@H嬎��  吚t嬋鐼��H媆$0H兡 _肏�
跧  ��  婰$@H吚u��  H媆$03繦兡 _肏媆$0塇H兡 _锰烫烫烫烫烫H塡$WH冹 H嬍3�K  孁凐�u�.  H吚uM嬒��  孁吚u3蒆媆$0H兡 _楹��H�
驤  �  H吚u嬒�P  H嬅H媆$0H兡 _脡xH嬝H嬅H媆$0H兡 _锰烫烫烫烫烫烫蘃冹(3繦;{  t.H�欽  H9BtH�
�  H��  H�	�=  3繦兡(脣B婭嬓�   吚t嬋H兡(���H�*  H� H兡(锰烫烫烫烫藹SH冹 乎  H�
nM  ��  H嬝H吚剈  A�   H�_  H嬋�.  A�   H�a  H嬎�  A�   H�c  H嬎�  E3繦�h  H嬎��  A�   H�j  H嬎��  A�    H�l  H嬎��  E3繦�q  H嬎��  A�   H�s  H嬎��  A����H�u  H嬎��  E3繦�r  H嬎�q  A�   H�t  H嬎�[  A�   H�v  H嬎�E  A�   H�x  H嬎�/  A�   H�z  H嬎�  A�   H�|  H嬎�  A�   H�v  H嬎��  A�   H�x  H嬎��  A�   H��  H嬎��  A�	   H��  H嬎��  A�
   H��  H嬎��  A�   H��  H嬎�  A�   H��  H嬎�i  A�   H��  H嬎�S  A�   H��  H嬎�=  A�   H��  H嬎�'  A�   H��  H嬎�  A�   H�|  H嬎��  A�   H�v  H嬎��  A�   H�p  H嬎��  A�	   H�j  H嬎��  A�   H�d  H嬎��  A�   H�^  H嬎��  A�
   H�X  H嬎�w  A�   H�Z  H嬎�a  A�   H�T  H嬎�K  A�   H�N  H嬎�5  A�   H�H  H嬎�  A�   H�B  H嬎�	  E3繦�
?  3�g  H�R  H吚tL嬂H�1  H嬎�0  H嬅H兡 [�3繦兡 [�%�  �%�  烫烫烫烫蘤f�     H;
袬  uH亮f髁��u肏辽闁  烫H冹(呉t9冴t(冴t凓t
�   H兡(描~  �鐿  独H兡(肐嬓H兡(�   M吚暳H兡(�  H塡$H塼$H墊$ AVH冹 H嬺L嬹3设�  劺勅   鑥  娯圖$@@��=罰   吪   �盤     枥  劺tO柘	  楮  �!  H�  H�
  棹  吚u)鑍  劺t H��  H�
�  柝  �\P     @2�娝枰  @�u?�  H嬝H�8 t$H嬋�  劺tL嬈�   I嬑H�L�
v  A��uJ  �   �3繦媆$0H媡$8H媩$HH兡 A^霉   杼  愄烫H塡$WH冹0@婛�5J  吚
3繦媆$@H兡0_�葔J  鑋  娯圖$ �=狾  u7鑟  �
  �	  �%扥   娝�  3褸娤�%  鲐蹆�鑡  嬅擘�   鐶  悙蘃嬆H塜 L堾塒H塇VWAVH冹@I嬸孃L嬹呉u9業  3篱�   岯�凐wEH��  H吚u
荄$0   ��c  嬝塂$0吚劜   L嬈嬜I嬑锠��嬝塂$0吚剹   L嬈嬜I嬑�
  嬝塂$0�u6吚u2L嬈3襂嬑桉  H咑暳杵��H�W  H吚tL嬈3襂嬑��  �t�u@L嬈嬜I嬑�.��嬝塂$0吚t)H�  H吚u	峏塡$0�L嬈嬜I嬑��  嬝塂$0�3蹓\$0嬅H媆$xH兡@A^_^锰烫H塡$H塼$WH冹 I孁嬟H嬹凓u铔  L嬊嬘H嬑H媆$0H媡$8H兡 _閺��烫藹SH冹 H嬞3��
  H嬎��
  ��
  H嬋�	 繦兡 [H�%�
  H塋$H冹8�   �h
  吚t�   �)H�
  瑭   H婦$8H�岻  H岲$8H兝H�I  H�vI  H�鏕  H婦$@H�際  �罣  	 狼籊     �臛     �   Hk� H�
紾  H�   �   Hk� H�
}<  H塋 �   Hk�H�
`<  H塋 H�
�  ���H兡8锰藹SVWH冹@H嬞��  H嫵�   3�E3繦峊$`H嬑��  H吚t9H僤$8 H峀$hH婽$`L嬋H塋$0L嬈H峀$pH塋$(3蒆塡$ �n  �莾�|盚兡@_^[锰烫H塡$ UH嬱H冹 H��;  H�2⑦-�+  H;胾tH僥 H峂��  H婨H塃��  嬂H1E��  嬂H峂 H1E��  婨 H峂H拎 H3E H3EH3罤������  H#罤�3⑦-�+  H;肏D罤�E;  H媆$HH餍H�.;  H兡 ]肏冹(凓uH�=g   u�G  �   H兡(锰H�
}K  H�%&  烫H�
mK  轭  H�qK  肏�qK  肏冹(桤���H�$桄���H�H兡(锰H冹(瑙  吚t!eH�%0   H婬�H;萾3鲤H�
8K  u�2繦兡(冒膑烫蘃冹(鑛  吚t瓒  �鑃  嬋鑴  吚t2离鑮  �H兡(肏冹(3设=  劺暲H兡(锰烫H冹(鑣  劺u2离鑖  劺u鑑  腱�H兡(肏冹(鐿  鐹  �H兡(锰烫H塡$H塴$H塼$WH冹 I孂I嬸嬟H嬮枘  吚u凔uL嬈3襀嬐H嬊��
  H婽$X婰$PH媆$0H媗$8H媡$@H兡 _椴  H冹(�  吚tH�
8J  H兡(榄  枋  吚u瑗  H兡(肏冹(3设�  H兡(椁  @SH冹 �驣  吷�   D脠鉏  鑦  鑝  劺u2离鑐  劺u	3设a  腙娒H兡 [锰烫@SH冹 �=↖   嬞ug凒wj栎  吚t(呟u$H�
扞  �  吚uH�
欼  桴  吚t.2离3fo%
  H內��aI  H�jI  �jI  H�sI  �=I  �H兡 [霉   楮   烫H冹L嬃窶Z  f9e��uxHc
樔��H�U��H蕘9PE  u_�  f9AuTL+�稟H峇H�稟H��L�蔋�$I;裻婮L;羠
婤罫;纑H兟(脒3襀呉u2离儂$ }2离
��2离2繦兡聾SH冹 娰枨  3覅纓勠uH�jH  H兡 [聾SH冹 �=_H   娰t勔u栩  娝桦  �H兡 [锰烫H�慔  脙%iH   肏塡$UH崿$@��H侅�  嬞�   ��  吚t嬎�)�   枘���3襀峂餉感  鐶  H峂��  H嫕�   H崟�  H嬎E3��  H吚t<H僤$8 H崓�  H嫊�  L嬋H塋$0L嬅H崓�  H塋$(H峂餒塋$ 3�^  H媴�  H峀$PH墔�   3襀崊�  A笜   H兝H墔�   璋  H媴�  H塂$`荄$P  @荄$T   ��  凐H岲$PH塂$@H岴�斆H塂$H3��  H峀$@��  吚u勠u岺杈��H嫓$�  H伳�  ]锰H塡$WH冹 H��!  H�=�!  �H�H吚t�
  H兠H;遰镠媆$0H兡 _肏塡$WH冹 H�w!  H�=p!  �H�H吚t��	  H兠H;遰镠媆$0H兡 _寐  蘃塡$H塼$WH冹3�3�嬃E3跠嬎A侌ntelA侎GenuD嬕嬸3葾岰E�侐ineI�$E蕢\$孂塋$塗$uPH�
w5  �%�?�=� t(=` t!=p t谤�凐 w$H�     HＡsD�F  A內D�	F  �D� F  �   D岺�;饇&3�$D嬠塡$塋$塗$恒	s
E罝�虴  ��4     D�
�4  虹儜   D�
�4  �   ��4  虹sy虹ss3�蠬菱 H蠬塗$ H婦$ "�:胾W��4  內��4     ��4  A雒 t8內 �l4     �j4  �  蠨#谼;豼H婦$ $�<鄒
�
K4  @�A4  H媆$(3繦媡$0H兡_锰烫�   锰�3�944  暲�%  �%�  �%�  �%@  �%B  �%D  �%F  �%H  �%J  �%  �%�  烫�锰�锰�锰�锰�锰3烂蘃冹(M婣8H嬍I嬔�
   �   H兡(锰烫@SE�H嬟A冦鳯嬌A� L嬔tA婡McP髫L袶c萀#袸c肑�H婥婬H婥鯠t禗冟餖萀3蔍嬌[閅���%  烫烫烫烫蘤f�     �嗵烫烫烫烫烫烫烫烫烫蘤f�     �%�  @UH冹 H嬯奙@H兡 ]轸��藹UH冹 H嬯奙 桠��怘兡 ]锰@UH冹 H嬯H兡 ]镃��藹UH冹0H嬯H��H塋$(塗$ L�
伛��L婨p婾hH婱`鑴��怘兡0]锰@UH嬯H�3蓙8  �斄嬃]锰                                                                                        
      �      �      �
      �        Zo      Do      (o      o      鴑      鈔      萵      琻      榥      刵      fn      Jn      6n      n      n      Nh      @h      zh      攈      lh              &h      h      h              no      l      .l      鰇              Tl              歭      甽              Tm      <m      唋      竘      膌      襩      鋖             m              Jl      |l      `l      rl      恖                    hl              M      �{      �\      ��      �}      ��      �y      �0      ��      �      ��      �      �t      ��      ��      ��      ��      ��      ��      �      �v      �       �s      ��      ��      �        ,j      詋      糼            択      俴      pk      `k      Dk      (k      k      k      頹      鄇      秊      榡      坖      rj      Xj      Hj      蔶      j      j       j      靑      趇      纈      猧      榠      唅      ri      `i      Hi      .i      i      i      頷      詇      篽              D: �   D: �   �< �   = �   = �                                                                           { �   皗 �           ����������������processing 'no address' result  out of memory in uuidgen    tmp     status  iii getnextcabinet  i       Incorrect return type %s from getnextcabinet    FCICreate expects a list        path name too long  .\  FCI error %d    ss      FCICreate expects a list of tuples containing two strings       FCI general error       access denied   function failed invalid data    invalid handle  invalid state   invalid parameter       open failed     create failed   unknown error %x        FCICreate   str argument 1      embedded null character SetString       argument 2      SetStream       SetInteger      SetProperty Modify      argument        OpenView        OpenDatabase    could not convert record field to integer       GetFieldCount   GetInteger      GetString       ClearData       _msi.Record     FILETIME result result of type %d       unsupported type        GetProperty     GetPropertyCount        Persist _msi.SummaryInformation Execute argument must be a record       Modify expects a record object  Execute GetColumnInfo   Fetch   Close   _msi.View   Commit      GetSummaryInformation   _msi.Database   UuidCreate      CreateRecord    _msi    MSIDBOPEN_CREATEDIRECT  MSIDBOPEN_CREATE        MSIDBOPEN_DIRECT        MSIDBOPEN_READONLY      MSIDBOPEN_TRANSACT      MSIDBOPEN_PATCHFILE     MSICOLINFO_NAMES        MSICOLINFO_TYPES        MSIMODIFY_SEEK  MSIMODIFY_REFRESH       MSIMODIFY_INSERT        MSIMODIFY_UPDATE        MSIMODIFY_ASSIGN        MSIMODIFY_REPLACE       MSIMODIFY_MERGE MSIMODIFY_DELETE        MSIMODIFY_INSERT_TEMPORARY      MSIMODIFY_VALIDATE      MSIMODIFY_VALIDATE_NEW  MSIMODIFY_VALIDATE_FIELD        MSIMODIFY_VALIDATE_DELETE       PID_CODEPAGE    PID_TITLE       PID_SUBJECT     PID_AUTHOR      PID_KEYWORDS    PID_COMMENTS    PID_TEMPLATE    PID_LASTAUTHOR  PID_REVNUMBER   PID_LASTPRINTED PID_CREATE_DTM  PID_LASTSAVE_DTM        PID_PAGECOUNT   PID_WORDCOUNT   PID_CHARCOUNT   PID_APPNAME     PID_SECURITY    _msi.MSIError   MSIError                UuidCreate($module, /)
--

Return the string representation of a new unique identifier. Close($self, /)
--

Close the view.     Persist($self, /)
--

Write the modified properties to the summary information stream.          GetString($self, field, /)
--

Return the value of field as a string where possible.            SetStream($self, field, value, /)
--

Set field to the contents of the file named value.        SetInteger($self, field, value, /)
--

Set field to an integer value.           SetProperty($self, field, value, /)
--

Set a property.

  field
    the name of the property, one of the PID_* constants
  value
    the new value of the property (integer or string)         Commit($self, /)
--

Commit the changes pending in the current transaction.     FCICreate($module, cabname, files, /)
--

Create a new CAB file.

  cabname
    the name of the CAB file
  files
    a list of tuples, each containing the name of the file on disk,
    and the name of the file inside the CAB file           OpenView($self, sql, /)
--

Return a view object.

  sql
    the SQL statement to execute       CreateRecord($module, count, /)
--

Return a new record object.

  count
    the number of fields of the record OpenDatabase($module, path, persist, /)
--

Return a new database object.

  path
    the file name of the MSI file
  persist
    the persistence mode          Modify($self, kind, data, /)
--

Modify the view.

  kind
    one of the MSIMODIFY_* constants
  data
    a record describing the new data      GetColumnInfo($self, kind, /)
--

Return a record describing the columns of the view.

  kind
    MSICOLINFO_NAMES or MSICOLINFO_TYPES  ClearData($self, /)
--

Set all fields of the record to 0.              GetProperty($self, field, /)
--

Return a property of the summary.

  field
    the name of the property, one of the PID_* constants    Fetch($self, /)
--

Return a result record of the query.                GetPropertyCount($self, /)
--

Return the number of summary properties.         GetSummaryInformation($self, count, /)
--

Return a new summary information object.

  count
    the maximum number of updated values   Close($self, /)
--

Close the database object.          Execute($self, params, /)
--

Execute the SQL query of the view.

  params
    a record describing actual values of the parameter tokens
    in the query or None               SetString($self, field, value, /)
--

Set field to a string value.              GetInteger($self, field, /)
--

Return the value of field as an integer where possible.         GetFieldCount($self, /)
--

Return the number of fields of the record.      玶玤       f   燲  燡      玶玤          Y  K      玶玤    
   X  Y  K      8                                                                                      p �                   鐲 �   鳦 �                                                                                                                                  �X �                   餋 �    D �   D �   饊 �                                                                                                              ��        榅          RSDS乑;1篃轉琲趃I@襯   C:\db\build\S\VS1564R\build\python\src\external_python\PCbuild\amd64\_msi.pdb                    GCTL   �,  .text$mn    �<  6   .text$mn$00 =  �   .text$x  @  �  .idata$5    鐲  (   .00cfg  D     .CRT$XCA    D     .CRT$XCZ     D     .CRT$XIA    (D     .CRT$XIZ    0D     .CRT$XPA    8D     .CRT$XPZ    @D     .CRT$XTA    HD     .CRT$XTZ    PD  0  .rdata  �X      .rdata$voltmd   燲  �  .rdata$zzzdbg   x[     .rtc$IAA    �[     .rtc$IZZ    圼     .rtc$TAA    怺     .rtc$TZZ    榌  8  .xdata  衎  H   .edata  c  �   .idata$2    鬰     .idata$3    d  �  .idata$4    餲  �  .idata$6     p     .data    {    .bss     �  |  .pdata   �  �   .rsrc$01    牋  �  .rsrc$02                                        X Xt 
4 
2` d T 4 2p d 4 2p d 4 2p
 
4 
2p
 
4 
2p0 04 
d 
2p
 
4 
2p b  
 
4 
2p 
 �p`P\<  �   ! 4 `  �  $\  !   `  �  $\  $  �P\<  �  !" "詛 t} d~ 4� �  �  `\  ! 魖 膢 �  �  t\  !   �  �  t\  !   �  �  `\  !   魖  詛  膢  t}  d~  4� �  �  `\  !   詛  t}  d~  4� �  �  `\   20$ d4p  \<     � �4 �  \<  8    20 20 B  
 
4 
2p 
d
�p  \<  �  ! 4   \  p]  !     \  p]   B  
 
d 
2p! 4 P  �  碷  !   P  �  碷  !   4 P  �  碷  !   4 P  �  碷  
 
d 
2p! 4 P   �   ^  !   P   �   ^  !   4 P   �   ^  !   4 P   �   ^  
 
4 
2`! t P!  �!  d^  !   t P!  �!  d^  !   t P!  �!  d^  !   P!  �!  d^   � `P0  \<  P  ! t� 0"  �"  糬  !   0"  �"  糬   B  
 
d	 
Rp! 4  $  f$   _  !   4  $  f$   _  !   4  $  f$   _  !    $  f$   _   B   B  ! 4 �%  �%  `_  !   4 �%  �%  `_   d 4 2p
 
4 
2`! t �&  '  燺  !   t �&  '  燺  !   t �&  '  燺  !   �&  '  燺   B   20 B  
 
4 
2p 2p! 4 @)  �)  `  !   @)  �)  `  !   4 @)  �)  `  !   4 @)  �)  `  !   4 @)  �)  `  !   4 @)  �)  `  
 
4 
2p B   20        t	 d 4 2� <     �/  G0  =      �0  �0  =       2P
 
4 
Rp <     �0  1  -=      �0  &1  F=      /1  :1  -=      /1  ;1  F=       2P 2P B  	 4 r�p` <     q1  W2  Z=  W2   RP d 4 2p	 	b   rp`0 20
 
4	 
2P B   B  	 "   <     �7  8  �=  8   P   B   20 20 20 20 d T 4 2p B   B   B   B   B   B   4� � P  
 
4 
2p
 
4 
2p d 4 p           0   B          ����    c           鴅  黚   c  p+  c    _msi.pyd PyInit__msi  d          餲   @  鄀          黦  谹  豥          4h  蠤  0d          琱  (@  癴          鑛  ˙  鴇          8l  餈  榚          ^m  怉   e          ~m  A  衑            華  He          苖  @A  0e          鑝  (A                      
      �      �      �
      �        Zo      Do      (o      o      鴑      鈔      萵      琻      榥      刵      fn      Jn      6n      n      n      Nh      @h      zh      攈      lh              &h      h      h              no      l      .l      鰇              Tl              歭      甽              Tm      <m      唋      竘      膌      襩      鋖             m              Jl      |l      `l      rl      恖                    hl              M      �{      �\      ��      �}      ��      �y      �0      ��      �      ��      �      �t      ��      ��      ��      ��      ��      ��      �      �v      �       �s      ��      ��      �        ,j      詋      糼            択      俴      pk      `k      Dk      (k      k      k      頹      鄇      秊      榡      坖      rj      Xj      Hj      蔶      j      j       j      靑      趇      纈      猧      榠      唅      ri      `i      Hi      .i      i      i      頷      詇      篽              Cabinet.dll msi.dll &UuidToStringW RpcStringFreeW  UuidCreate  RPCRT4.dll  � CreateFileW QGetFileInformationByHandle  � CloseHandle rFileTimeToLocalFileTime qFileTimeToDosDateTime KERNEL32.dll  PyModule_AddIntConstant 3_PyArg_CheckPositional   PyBytes_AsString  � PyErr_Occurred  tPyObject_GenericGetAttr {PyUnicode_AsUTF8AndSize �_PyUnicode_AsUnicode  �PyLong_FromLong PyExc_MemoryError �PyMem_RawMalloc 
_Py_NoneStruct  2_PyArg_BadArgument  �PyUnicode_FromWideChar  � PyErr_SetString <PyExc_ValueError  � PyErr_Format  �PyLong_Type �_Py_Dealloc �PyLong_AsUnsignedLongMask mPyObject_Free � PyErr_ExceptionMatches  PyModule_AddObject  �PyLong_AsLong �PyUnicode_AsWideCharString  	PyModule_Create2  � PyErr_NewException  � PyErr_Clear vPyObject_GenericSetAttr �PyMem_Free  � PyErr_NoMemory  ' PyBytes_FromStringAndSize !PyExc_NotImplementedError 1_PyObject_New 4PyExc_TypeError �_PyLong_AsInt �PyMem_RawFree _PyObject_CallMethodId  �PyUnicode_FromString   PyArg_ParseTuple  python311.dll  __C_specific_handler  % __std_type_info_destroy_list  > memset  VCRUNTIME140.dll  i _wopen  5 _wremove  R _read � strncpy  _close  k _write  ! _errno  E _lseek   free  ] _tempnam   malloc  6 _initterm 7 _initterm_e ? _seh_filter_dll  _configure_narrow_argv  3 _initialize_narrow_environment  4 _initialize_onexit_table  " _execute_onexit_table  _cexit  api-ms-win-crt-stdio-l1-1-0.dll api-ms-win-crt-filesystem-l1-1-0.dll  api-ms-win-crt-string-l1-1-0.dll  api-ms-win-crt-runtime-l1-1-0.dll api-ms-win-crt-heap-l1-1-0.dll  �RtlCaptureContext �RtlLookupFunctionEntry  �RtlVirtualUnwind  �UnhandledExceptionFilter  SetUnhandledExceptionFilter  GetCurrentProcess �TerminateProcess  �IsProcessorFeaturePresent RQueryPerformanceCounter !GetCurrentProcessId %GetCurrentThreadId  �GetSystemTimeAsFileTime %DisableThreadLibraryCalls oInitializeSListHead �IsDebuggerPresent < memcpy                                                                                                                                          蚞 襢��2⑦-�+  ����          /        �                     Documentation   pH �    + �          郥 �   xH �    & �          0R �   圚 �   �% �          圫 �   銯 �   �& �   �       燪 �   怘 �   �' �          楲 �                                   郍 �   0" �           S �   餑 �   �# �          蠸 �   谾 �    $ �   �       0N �   H �   p% �          繪 �                                   豀 �   � �          @L �   `F �   p �   �       @O �   G �   @) �   �        Q �   鐷 �   p* �          怭 �                                   PG �   � �          @V �   `G �   � �          郩 �   pG �    �           M �   楩 �   P �   �       怳 �   窮 �   P  �   �       �M �   菷 �   P! �   �       郙 �   �G �     �          窻 �                                    G �   �' �          0P �    �   �( �          餘 �   癏 �   �( �           T �   怘 �   @ �          ═ �                                   蠨 �   ��������腄 �   ��������                       菻 �                  @ �                                                                                           / �   / �                                                                           Ps �                                                                                                                                                                                                          怗 �                  @ �                                                                                           / �   / �                                                                           Pr �                                                                                                                                                                                                          H �                  @ �                                                                                           / �   / �                                                                           q �                                                                                                                                                                                                                          鳫 �   @p �   ��������皅 �                                                                  楬 �                  @ �                                                                                           / �   / �                                                                           Pp �                                                                                                                                                                                                                                                                                                                                                                                                                                                      �  榌  �  7  ╗  @  }  糩  �  �  蘙  �  �  躘  �  !  鑋  0  �  鬧  �  =  \  @  �  \  �  ]  \  `  �  $\  �  n  <\  n  �  P\  �  �  `\  �  �  t\  �    擻    �  琝  �  �  糪  �    蘚    8  鬨  @  h  ]  p  �  ]  �  i  8]  p  1  L]  @  y  T]  �  �  \]  �    d]    \  p]  \  �  圿  �    淽     K  琞  P  �  碷  �  �  繻  �  �  註  �  *   鋆  *   D   鴀  P   �   ^  �   �   ^  �   �   ,^  �   *!  <^  *!  D!  P^  P!  �!  d^  �!  �!  p^  �!  	"  刕  	"  "  榐  "  #"  琟  0"  �"  糬  �"  �#  証  �#  �#  鑎  �#  $  鴁   $  f$   _  f$  -%  _  -%  G%   _  G%  e%  4_  e%  p%  H_  p%  �%  X_  �%  �%  `_  �%  &  h_  &  &  |_   &  �&  恄  �&  '  燺  '  m'  琠  m'  �'  繽  �'  �'  訽  �'  �'  鑏  �'  �'  鴂  �'  |(   `  �(  �(  `  �(  8)  `  @)  �)  `  �)  �)  $`  �)  �)  8`  �)  *  H`  *  .*  \`  .*  W*  p`  W*  e*  刞  p*  �*  榒   +  f+    p+  /  琡  0/  N/  竊  P/  �/  da  �/  �0  糮  �0  <1   a  <1  m2  la  p2  �2  渁  �2  �2  繿  �2  �3  琣  �3  )4  碼  ,4  �4  萢  �4  �4  詀  (5  C5  躠  D5  }5  b  �5  �5  Pb  �5  �5  Hb  �5  �5  hb  �5  	6  pb  6  l6  4b  l6  �6  Xb  �6  �6  `b  �6  �6  b  �6  �7  ,b  �7   8  鋋   8  D8  b  D8  m8  $b  �8  �9  xb  �9  :  坆  :  D:  攂  H:  �;  燽  \<  y<  腷  |<  �<  糱  �<  �<  癰  =  =  竍  =  -=  鴃  -=  F=  Ta  F=  Z=  \a  Z=  �=  攁  �=  �=  b                                                                                                                                                           �   8  �                  P  �                  h  �               	  �                  	  �   去  �          牋  '          <?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<assembly xmlns="urn:schemas-microsoft-com:asm.v1" manifestVersion="1.0">
  <trustInfo xmlns="urn:schemas-microsoft-com:asm.v3">
    <security>
      <requestedPrivileges>
        <requestedExecutionLevel level="asInvoker" uiAccess="false"/>
      </requestedPrivileges>
    </security>
  </trustInfo>
  <compatibility xmlns="urn:schemas-microsoft-com:compatibility.v1">
    <application>
      <supportedOS Id="{e2011457-1546-43c5-a5fe-008deee3d3f0}"/>
      <supportedOS Id="{35138b9a-5d96-4fbd-8e2d-a2440225f93a}"/>
      <supportedOS Id="{4a2f28e3-53b9-4441-ba9c-d69d4a4a6e38}"/>
      <supportedOS Id="{1f676c76-80e1-4239-95bb-83d0f6d0da78}"/>
      <supportedOS Id="{8e0f7a12-bfb3-4fe8-b9a5-48fd50a15a9a}"/>
    </application>
  </compatibility>
  <application xmlns="urn:schemas-microsoft-com:asm.v3">
    <windowsSettings>
      <longPathAware xmlns="http://schemas.microsoft.com/SMI/2016/WindowsSettings">true</longPathAware>
    </windowsSettings>
  </application>
  <dependency>
    <dependentAssembly>
      <assemblyIdentity type="win32" name="Microsoft.Windows.Common-Controls"
                        version="6.0.0.0" processorArchitecture="*" publicKeyToken="6595b64144ccf1df" language="*" />
    </dependentAssembly>
  </dependency>
</assembly>
 �4   V S _ V E R S I O N _ I N F O     �稔     ��+  ��+?                            S t r i n g F i l e I n f o   �   0 0 0 0 0 4 b 0   V   C o m p a n y N a m e     P y t h o n   S o f t w a r e   F o u n d a t i o n     @   F i l e D e s c r i p t i o n     P y t h o n   C o r e   0   F i l e V e r s i o n     3 . 1 1 . 1 1   6   I n t e r n a l N a m e   P y t h o n   D L L     0�  L e g a l C o p y r i g h t   C o p y r i g h t   �   2 0 0 1 - 2 0 2 3   P y t h o n   S o f t w a r e   F o u n d a t i o n .   C o p y r i g h t   �   2 0 0 0   B e O p e n . c o m .   C o p y r i g h t   �   1 9 9 5 - 2 0 0 1   C N R I .   C o p y r i g h t   �   1 9 9 1 - 1 9 9 5   S M C .   : 	  O r i g i n a l F i l e n a m e   _ m s i . p y d     .   P r o d u c t N a m e     P y t h o n     4   P r o d u c t V e r s i o n   3 . 1 1 . 1 1   D    V a r F i l e I n f o     $    T r a n s l a t i o n       �                                                                                                                                             @     瑁穑 ��   P     8о� ��� p  �   P燲爃爌爔爤爯牁牗牥牳犎犘犡犺���(�0�8“「∪⌒∝¤○▲����(ⅷ⒏⑷⑿⒇㈣Ⅷ����(（０８Ｈｐ� �(えとムоц�8�� �0﹛〩�                                                                                                                                                                                                                                                                      