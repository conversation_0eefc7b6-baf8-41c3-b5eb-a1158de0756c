单帧模式

Should simulate...
need_first_init = 1
--do_init_cloth end---once--
MOD_cloth: copying cloth results back to mesh positions
  MOD_cloth Vertex 0: pos=[-0.000018,-0.111819,1.024277]
  MOD_cloth Vertex 1: pos=[0.028666,-0.107355,1.024218]
  MOD_cloth Vertex 2: pos=[0.055114,-0.094275,1.024068]
  MOD_cloth Vertex 3: pos=[0.077224,-0.073531,1.023880]
  MOD_cloth Vertex 4: pos=[0.093138,-0.046725,1.023717]
MOD_cloth: forced object data re-evaluation
RB: 涓烘父鎴忔ā寮忛噸寤轰笘鐣�--璧峰甯� = 1
addRigidBody: --------
addRigidBody: --------
addRigidBody: --------
addRigidBody: --------
addRigidBody: --------
addRigidBody: --------
addRigidBody: --------
addRigidBody: --------
addRigidBody: --------
addRigidBody: --------
addRigidBody: --------
addRigidBody: --------
addRigidBody: --------
addRigidBody: --------
addRigidBody: --------
addRigidBody: --------
addRigidBody: --------
addRigidBody: --------
addRigidBody: --------
addRigidBody: --------
addRigidBody: --------
addRigidBody: --------
addRigidBody: --------
addRigidBody: --------
addRigidBody: --------
addRigidBody: --------
addRigidBody: --------
addRigidBody: --------
addRigidBody: --------
addRigidBody: --------
addRigidBody: --------
addRigidBody: --------
addRigidBody: --------
addRigidBody: --------
addRigidBody: --------
addRigidBody: --------
addRigidBody: --------
addRigidBody: --------
addRigidBody: --------
addRigidBody: --------
addRigidBody: --------
addRigidBody: --------
addRigidBody: --------
addRigidBody: --------
addRigidBody: --------
addRigidBody: --------
addRigidBody: --------
addRigidBody: --------
addRigidBody: --------
addRigidBody: --------
addRigidBody: --------
addRigidBody: --------
addRigidBody: --------
addRigidBody: --------
addRigidBody: --------
addRigidBody: --------
addRigidBody: --------
addRigidBody: --------
addRigidBody: --------
addRigidBody: --------
addRigidBody: --------
addRigidBody: --------
addRigidBody: --------
addRigidBody: --------
addRigidBody: --------
addRigidBody: --------
addRigidBody: --------
addRigidBody: --------
addRigidBody: --------
addRigidBody: --------
addRigidBody: --------
addRigidBody: --------
addRigidBody: --------
addRigidBody: --------
RB: Game start initialization completed successfully
璋冪敤鎴愬姛---BKE_rigidbody_update_for_skeletal_animation
璋冪敤鎴愬姛---BKE_rigidbody_update_for_skeletal_animation
璋冪敤鎴愬姛---BKE_rigidbody_update_for_skeletal_animation
璋冪敤鎴愬姛---BKE_rigidbody_update_for_skeletal_animation
use_interactive_rb=false,should_simulate=false----
Should simulate...
need_first_init = 0
cache_flag_baked = 1 --- is_single_frame_mode = 1 --- can_simulate = 1
framenr = 1, last_frame = 1, timescale = 1.000000
UPBGE single frame mode: skipping cache, forcing simulation
Object matrix unchanged
Vertex 0: local=[-0.000018,-0.111819,1.024277] world=[-0.000018,-0.111819,1.065248]
Vertex 0: old_rest=[-0.000018,-0.111819,1.065248] current_mesh=[-0.000018,-0.111819,1.065248] delta=[0.000000,0.000000,0.000000]
Vertex 1: old_rest=[0.028666,-0.107355,1.065187] current_mesh=[0.028666,-0.107355,1.065187] delta=[0.000000,0.000000,0.000000]
Vertex 2: old_rest=[0.055114,-0.094275,1.065030] current_mesh=[0.055114,-0.094275,1.065030] delta=[0.000000,0.000000,0.000000]
SIM_cloth_solve: tf=1.000000, dt_base=0.200000, timescale=1.000000, dt_final=0.200000
cloth_solve--tf-- 1.000000
=== Physics step 0.000000 ===
1. Constraints setup completed
2. Forces cleared
cloth_calc_force called - mvert_num: 198, avg_spring_len: 0.036831
Gravity enabled: [0.000000, 0.000000, -0.019620]
cloth_calc_force completed - processed 726 springs
3. Forces calculated
4. Velocities solved - status: 1, iterations: 8
5. Collisions processed
6. Positions solved
7. Results applied
Pinned vertex 154: set to xconst=[-0.135840,0.078511,1.073723]
Pinned vertex 155: set to xconst=[-0.122557,0.057010,1.073291]
Pinned vertex 156: set to xconst=[-0.102748,0.040287,1.072660]
Pinned vertex 157: set to xconst=[-0.077888,0.029702,1.071925]
Pinned vertex 158: set to xconst=[-0.049934,0.026216,1.071190]
Pinned vertex 159: set to xconst=[-0.021189,0.030219,1.070552]
Pinned vertex 160: set to xconst=[-0.003175,0.037429,1.070089]
Pinned vertex 161: set to xconst=[0.003483,0.046956,1.069822]
Pinned vertex 162: set to xconst=[0.007377,0.062741,1.069524]
Pinned vertex 163: set to xconst=[0.007414,0.083650,1.069335]
Pinned vertex 164: set to xconst=[0.003165,0.107671,1.069341]
Pinned vertex 165: set to xconst=[-0.005152,0.132561,1.069562]
Pinned vertex 166: set to xconst=[-0.017211,0.156963,1.069994]
Pinned vertex 167: set to xconst=[-0.031525,0.177812,1.070582]
Pinned vertex 168: set to xconst=[-0.046626,0.193273,1.071252]
Pinned vertex 169: set to xconst=[-0.060748,0.202154,1.071873]
Pinned vertex 170: set to xconst=[-0.072236,0.204408,1.072286]
Pinned vertex 171: set to xconst=[-0.089999,0.196810,1.072743]
Pinned vertex 172: set to xconst=[-0.113039,0.179132,1.073204]
Pinned vertex 173: set to xconst=[-0.130174,0.156472,1.073597]
Pinned vertex 174: set to xconst=[-0.140057,0.130741,1.073857]
Pinned vertex 175: set to xconst=[-0.141971,0.104045,1.073915]
8. Pinned vertices updated
=== Physics step 0.200000 ===
1. Constraints setup completed
2. Forces cleared
cloth_calc_force called - mvert_num: 198, avg_spring_len: 0.036831
Gravity enabled: [0.000000, 0.000000, -0.019620]
cloth_calc_force completed - processed 726 springs
3. Forces calculated
4. Velocities solved - status: 1, iterations: 6
5. Collisions processed
6. Positions solved
7. Results applied
Pinned vertex 154: set to xconst=[-0.135840,0.078511,1.073723]
Pinned vertex 155: set to xconst=[-0.122557,0.057010,1.073291]
Pinned vertex 156: set to xconst=[-0.102748,0.040287,1.072660]
Pinned vertex 157: set to xconst=[-0.077888,0.029702,1.071925]
Pinned vertex 158: set to xconst=[-0.049934,0.026216,1.071190]
Pinned vertex 159: set to xconst=[-0.021189,0.030219,1.070552]
Pinned vertex 160: set to xconst=[-0.003175,0.037429,1.070089]
Pinned vertex 161: set to xconst=[0.003483,0.046956,1.069822]
Pinned vertex 162: set to xconst=[0.007377,0.062741,1.069524]
Pinned vertex 163: set to xconst=[0.007414,0.083650,1.069335]
Pinned vertex 164: set to xconst=[0.003165,0.107671,1.069341]
Pinned vertex 165: set to xconst=[-0.005152,0.132561,1.069562]
Pinned vertex 166: set to xconst=[-0.017211,0.156963,1.069994]
Pinned vertex 167: set to xconst=[-0.031525,0.177812,1.070582]
Pinned vertex 168: set to xconst=[-0.046626,0.193273,1.071252]
Pinned vertex 169: set to xconst=[-0.060748,0.202154,1.071873]
Pinned vertex 170: set to xconst=[-0.072236,0.204408,1.072286]
Pinned vertex 171: set to xconst=[-0.089999,0.196810,1.072743]
Pinned vertex 172: set to xconst=[-0.113039,0.179132,1.073204]
Pinned vertex 173: set to xconst=[-0.130174,0.156472,1.073597]
Pinned vertex 174: set to xconst=[-0.140057,0.130741,1.073857]
Pinned vertex 175: set to xconst=[-0.141971,0.104045,1.073915]
8. Pinned vertices updated
=== Physics step 0.400000 ===
1. Constraints setup completed
2. Forces cleared
cloth_calc_force called - mvert_num: 198, avg_spring_len: 0.036831
Gravity enabled: [0.000000, 0.000000, -0.019620]
cloth_calc_force completed - processed 726 springs
3. Forces calculated
4. Velocities solved - status: 1, iterations: 6
5. Collisions processed
6. Positions solved
7. Results applied
Pinned vertex 154: set to xconst=[-0.135840,0.078511,1.073723]
Pinned vertex 155: set to xconst=[-0.122557,0.057010,1.073291]
Pinned vertex 156: set to xconst=[-0.102748,0.040287,1.072660]
Pinned vertex 157: set to xconst=[-0.077888,0.029702,1.071925]
Pinned vertex 158: set to xconst=[-0.049934,0.026216,1.071190]
Pinned vertex 159: set to xconst=[-0.021189,0.030219,1.070552]
Pinned vertex 160: set to xconst=[-0.003175,0.037429,1.070089]
Pinned vertex 161: set to xconst=[0.003483,0.046956,1.069822]
Pinned vertex 162: set to xconst=[0.007377,0.062741,1.069524]
Pinned vertex 163: set to xconst=[0.007414,0.083650,1.069335]
Pinned vertex 164: set to xconst=[0.003165,0.107671,1.069341]
Pinned vertex 165: set to xconst=[-0.005152,0.132561,1.069562]
Pinned vertex 166: set to xconst=[-0.017211,0.156963,1.069994]
Pinned vertex 167: set to xconst=[-0.031525,0.177812,1.070582]
Pinned vertex 168: set to xconst=[-0.046626,0.193273,1.071252]
Pinned vertex 169: set to xconst=[-0.060748,0.202154,1.071873]
Pinned vertex 170: set to xconst=[-0.072236,0.204408,1.072286]
Pinned vertex 171: set to xconst=[-0.089999,0.196810,1.072743]
Pinned vertex 172: set to xconst=[-0.113039,0.179132,1.073204]
Pinned vertex 173: set to xconst=[-0.130174,0.156472,1.073597]
Pinned vertex 174: set to xconst=[-0.140057,0.130741,1.073857]
Pinned vertex 175: set to xconst=[-0.141971,0.104045,1.073915]
8. Pinned vertices updated
=== Physics step 0.600000 ===
1. Constraints setup completed
2. Forces cleared
cloth_calc_force called - mvert_num: 198, avg_spring_len: 0.036831
Gravity enabled: [0.000000, 0.000000, -0.019620]
cloth_calc_force completed - processed 726 springs
3. Forces calculated
4. Velocities solved - status: 1, iterations: 7
5. Collisions processed
6. Positions solved
7. Results applied
Pinned vertex 154: set to xconst=[-0.135840,0.078511,1.073723]
Pinned vertex 155: set to xconst=[-0.122557,0.057010,1.073291]
Pinned vertex 156: set to xconst=[-0.102748,0.040287,1.072660]
Pinned vertex 157: set to xconst=[-0.077888,0.029702,1.071925]
Pinned vertex 158: set to xconst=[-0.049934,0.026216,1.071190]
Pinned vertex 159: set to xconst=[-0.021189,0.030219,1.070552]
Pinned vertex 160: set to xconst=[-0.003175,0.037429,1.070089]
Pinned vertex 161: set to xconst=[0.003483,0.046956,1.069822]
Pinned vertex 162: set to xconst=[0.007377,0.062741,1.069524]
Pinned vertex 163: set to xconst=[0.007414,0.083650,1.069335]
Pinned vertex 164: set to xconst=[0.003165,0.107671,1.069341]
Pinned vertex 165: set to xconst=[-0.005152,0.132561,1.069562]
Pinned vertex 166: set to xconst=[-0.017211,0.156963,1.069994]
Pinned vertex 167: set to xconst=[-0.031525,0.177812,1.070582]
Pinned vertex 168: set to xconst=[-0.046626,0.193273,1.071252]
Pinned vertex 169: set to xconst=[-0.060748,0.202154,1.071873]
Pinned vertex 170: set to xconst=[-0.072236,0.204408,1.072286]
Pinned vertex 171: set to xconst=[-0.089999,0.196810,1.072743]
Pinned vertex 172: set to xconst=[-0.113039,0.179132,1.073204]
Pinned vertex 173: set to xconst=[-0.130174,0.156472,1.073597]
Pinned vertex 174: set to xconst=[-0.140057,0.130741,1.073857]
Pinned vertex 175: set to xconst=[-0.141971,0.104045,1.073915]
8. Pinned vertices updated
=== Physics step 0.800000 ===
1. Constraints setup completed
2. Forces cleared
cloth_calc_force called - mvert_num: 198, avg_spring_len: 0.036831
Gravity enabled: [0.000000, 0.000000, -0.019620]
cloth_calc_force completed - processed 726 springs
3. Forces calculated
4. Velocities solved - status: 1, iterations: 8
5. Collisions processed
6. Positions solved
7. Results applied
Pinned vertex 154: set to xconst=[-0.135840,0.078511,1.073723]
Pinned vertex 155: set to xconst=[-0.122557,0.057010,1.073291]
Pinned vertex 156: set to xconst=[-0.102748,0.040287,1.072660]
Pinned vertex 157: set to xconst=[-0.077888,0.029702,1.071925]
Pinned vertex 158: set to xconst=[-0.049934,0.026216,1.071190]
Pinned vertex 159: set to xconst=[-0.021189,0.030219,1.070552]
Pinned vertex 160: set to xconst=[-0.003175,0.037429,1.070089]
Pinned vertex 161: set to xconst=[0.003483,0.046956,1.069822]
Pinned vertex 162: set to xconst=[0.007377,0.062741,1.069524]
Pinned vertex 163: set to xconst=[0.007414,0.083650,1.069335]
Pinned vertex 164: set to xconst=[0.003165,0.107671,1.069341]
Pinned vertex 165: set to xconst=[-0.005152,0.132561,1.069562]
Pinned vertex 166: set to xconst=[-0.017211,0.156963,1.069994]
Pinned vertex 167: set to xconst=[-0.031525,0.177812,1.070582]
Pinned vertex 168: set to xconst=[-0.046626,0.193273,1.071252]
Pinned vertex 169: set to xconst=[-0.060748,0.202154,1.071873]
Pinned vertex 170: set to xconst=[-0.072236,0.204408,1.072286]
Pinned vertex 171: set to xconst=[-0.089999,0.196810,1.072743]
Pinned vertex 172: set to xconst=[-0.113039,0.179132,1.073204]
Pinned vertex 173: set to xconst=[-0.130174,0.156472,1.073597]
Pinned vertex 174: set to xconst=[-0.140057,0.130741,1.073857]
Pinned vertex 175: set to xconst=[-0.141971,0.104045,1.073915]
8. Pinned vertices updated
=== Final vertex states ===
Vertex 0 (free): pos=[-0.149828,0.085155,1.022369] vel=[-0.040873,0.047866,-0.040127]
Vertex 1 (free): pos=[-0.134773,0.062338,1.022919] vel=[-0.045709,0.043037,-0.039139]
Vertex 2 (free): pos=[-0.112939,0.044791,1.023166] vel=[-0.049256,0.037492,-0.038474]
Vertex 3 (free): pos=[-0.085886,0.033841,1.023252] vel=[-0.051753,0.031949,-0.037815]
Vertex 4 (free): pos=[-0.055533,0.030391,1.023435] vel=[-0.053122,0.026652,-0.037512]
Vertex 5 (free): pos=[-0.024283,0.034154,1.024056] vel=[-0.053839,0.021448,-0.038296]
Vertex 6 (free): pos=[0.000833,0.040386,1.022739] vel=[-0.039870,0.033446,-0.063555]
Vertex 7 (free): pos=[0.008368,0.056999,1.027082] vel=[-0.048136,0.040351,-0.064740]
Vertex 8 (free): pos=[0.011131,0.081349,1.029549] vel=[-0.060612,0.034823,-0.055170]
Vertex 9 (free): pos=[0.002598,0.107640,1.032084] vel=[-0.051470,0.043516,-0.042327]
cloth_to_object: copying 198 vertices to mesh
  Free Vertex 0: using x=[-0.149828,0.085155,1.022369] -> mesh_pos=[-0.149828,0.085155,0.983047]
  Free Vertex 1: using x=[-0.134773,0.062338,1.022919] -> mesh_pos=[-0.134773,0.062338,0.983576]
  Free Vertex 2: using x=[-0.112939,0.044791,1.023166] -> mesh_pos=[-0.112939,0.044791,0.983814]
  Free Vertex 3: using x=[-0.085886,0.033841,1.023252] -> mesh_pos=[-0.085886,0.033841,0.983896]
  Free Vertex 4: using x=[-0.055533,0.030391,1.023435] -> mesh_pos=[-0.055533,0.030391,0.984073]
  Free Vertex 5: using x=[-0.024283,0.034154,1.024056] -> mesh_pos=[-0.024283,0.034154,0.984669]
  Free Vertex 6: using x=[0.000833,0.040386,1.022739] -> mesh_pos=[0.000833,0.040386,0.983403]
  Free Vertex 7: using x=[0.008368,0.056999,1.027082] -> mesh_pos=[0.008368,0.056999,0.987579]
  Free Vertex 8: using x=[0.011131,0.081349,1.029549] -> mesh_pos=[0.011131,0.081349,0.989951]
  Free Vertex 9: using x=[0.002598,0.107640,1.032084] -> mesh_pos=[0.002598,0.107640,0.992388]
  Free Vertex 10: using x=[-0.000482,0.147800,1.029462] -> mesh_pos=[-0.000482,0.147800,0.989867]
  Free Vertex 11: using x=[-0.004900,0.179351,1.030926] -> mesh_pos=[-0.004900,0.179351,0.991275]
  Free Vertex 12: using x=[-0.018434,0.209533,1.027772] -> mesh_pos=[-0.018434,0.209533,0.988243]
  Free Vertex 13: using x=[-0.031590,0.236948,1.030683] -> mesh_pos=[-0.031590,0.236948,0.991041]
  Free Vertex 14: using x=[-0.055982,0.254537,1.029083] -> mesh_pos=[-0.055982,0.254537,0.989502]
  Free Vertex 15: using x=[-0.075733,0.261622,1.029637] -> mesh_pos=[-0.075733,0.261622,0.990036]
  Free Vertex 16: using x=[-0.094122,0.255237,1.030566] -> mesh_pos=[-0.094122,0.255237,0.990929]
  Free Vertex 17: using x=[-0.110972,0.240673,1.027613] -> mesh_pos=[-0.110972,0.240673,0.988090]
  Free Vertex 18: using x=[-0.134593,0.209835,1.027097] -> mesh_pos=[-0.134593,0.209835,0.987594]
  Free Vertex 19: using x=[-0.154572,0.180732,1.022281] -> mesh_pos=[-0.154572,0.180732,0.982963]
  Free Vertex 20: using x=[-0.152027,0.146882,1.021898] -> mesh_pos=[-0.152027,0.146882,0.982594]
  Free Vertex 21: using x=[-0.154857,0.113969,1.021644] -> mesh_pos=[-0.154857,0.113969,0.982350]
  Free Vertex 22: using x=[-0.126980,0.050013,1.008399] -> mesh_pos=[-0.126980,0.050013,0.969615]
  Free Vertex 23: using x=[-0.107835,0.030925,1.009066] -> mesh_pos=[-0.107835,0.030925,0.970255]
  Free Vertex 24: using x=[-0.082505,0.017518,1.009385] -> mesh_pos=[-0.082505,0.017518,0.970563]
  Free Vertex 25: using x=[-0.052570,0.011047,1.009444] -> mesh_pos=[-0.052570,0.011047,0.970620]
  Free Vertex 26: using x=[-0.020073,0.012371,1.009532] -> mesh_pos=[-0.020073,0.012371,0.970703]
  Free Vertex 27: using x=[0.012313,0.020960,1.010103] -> mesh_pos=[0.012313,0.020960,0.971253]
  Free Vertex 28: using x=[0.036676,0.027283,1.013413] -> mesh_pos=[0.036676,0.027283,0.974435]
  Free Vertex 29: using x=[0.045867,0.048008,1.016399] -> mesh_pos=[0.045867,0.048008,0.977307]
  Free Vertex 30: using x=[0.048740,0.074355,1.017588] -> mesh_pos=[0.048740,0.074355,0.978450]
  Free Vertex 31: using x=[0.035115,0.091050,1.015243] -> mesh_pos=[0.035115,0.091050,0.976196]
  Free Vertex 32: using x=[0.010342,0.110109,1.011137] -> mesh_pos=[0.010342,0.110109,0.972248]
  Free Vertex 33: using x=[-0.010267,0.142790,1.012854] -> mesh_pos=[-0.010267,0.142790,0.973898]
  Free Vertex 34: using x=[-0.031207,0.176131,1.009420] -> mesh_pos=[-0.031207,0.176131,0.970596]
  Free Vertex 35: using x=[-0.050474,0.207855,1.010956] -> mesh_pos=[-0.050474,0.207855,0.972073]
  Free Vertex 36: using x=[-0.077404,0.227335,1.010907] -> mesh_pos=[-0.077404,0.227335,0.972026]
  Free Vertex 37: using x=[-0.097733,0.232586,1.013066] -> mesh_pos=[-0.097733,0.232586,0.974102]
  Free Vertex 38: using x=[-0.112075,0.220936,1.018189] -> mesh_pos=[-0.112075,0.220936,0.979028]
  Free Vertex 39: using x=[-0.121282,0.202253,1.019318] -> mesh_pos=[-0.121282,0.202253,0.980113]
  Free Vertex 40: using x=[-0.132279,0.172201,1.019769] -> mesh_pos=[-0.132279,0.172201,0.980547]
  Free Vertex 41: using x=[-0.150794,0.139798,1.012606] -> mesh_pos=[-0.150794,0.139798,0.973660]
  Free Vertex 42: using x=[-0.139153,0.107010,1.010815] -> mesh_pos=[-0.139153,0.107010,0.971937]
  Free Vertex 43: using x=[-0.136331,0.074556,1.007885] -> mesh_pos=[-0.136331,0.074556,0.969120]
  Free Vertex 44: using x=[-0.095963,0.004290,0.997611] -> mesh_pos=[-0.095963,0.004290,0.959241]
  Free Vertex 45: using x=[-0.072133,-0.009430,0.998358] -> mesh_pos=[-0.072133,-0.009430,0.959960]
  Free Vertex 46: using x=[-0.043490,-0.016792,0.998520] -> mesh_pos=[-0.043490,-0.016792,0.960115]
  Free Vertex 47: using x=[-0.011550,-0.016819,0.998113] -> mesh_pos=[-0.011550,-0.016819,0.959724]
  Free Vertex 48: using x=[0.021619,-0.008856,0.997237] -> mesh_pos=[0.021619,-0.008856,0.958882]
  Free Vertex 49: using x=[0.053120,0.006392,0.995826] -> mesh_pos=[0.053120,0.006392,0.957525]
  Free Vertex 50: using x=[0.075355,0.021151,0.996225] -> mesh_pos=[0.075355,0.021151,0.957909]
  Free Vertex 51: using x=[0.079640,0.047096,0.991486] -> mesh_pos=[0.079640,0.047096,0.953352]
  Free Vertex 52: using x=[0.072355,0.074074,0.986497] -> mesh_pos=[0.072355,0.074074,0.948554]
  Free Vertex 53: using x=[0.047593,0.099877,0.979335] -> mesh_pos=[0.047593,0.099877,0.941669]
  Free Vertex 54: using x=[0.024016,0.135613,0.982519] -> mesh_pos=[0.024016,0.135613,0.944730]
  Free Vertex 55: using x=[-0.006910,0.171593,0.984446] -> mesh_pos=[-0.006910,0.171593,0.946583]
  Free Vertex 56: using x=[-0.037728,0.213187,0.988305] -> mesh_pos=[-0.037728,0.213187,0.950293]
  Free Vertex 57: using x=[-0.082373,0.230622,0.991112] -> mesh_pos=[-0.082373,0.230622,0.952992]
  Free Vertex 58: using x=[-0.113510,0.214670,0.992264] -> mesh_pos=[-0.113510,0.214670,0.954100]
  Free Vertex 59: using x=[-0.129618,0.204234,0.995978] -> mesh_pos=[-0.129618,0.204234,0.957671]
  Free Vertex 60: using x=[-0.136029,0.177982,1.004261] -> mesh_pos=[-0.136029,0.177982,0.965636]
  Free Vertex 61: using x=[-0.141831,0.153727,1.010551] -> mesh_pos=[-0.141831,0.153727,0.971684]
  Free Vertex 62: using x=[-0.140105,0.119185,1.010915] -> mesh_pos=[-0.140105,0.119185,0.972034]
  Free Vertex 63: using x=[-0.151213,0.079443,1.007096] -> mesh_pos=[-0.151213,0.079443,0.968362]
  Free Vertex 64: using x=[-0.122225,0.053410,0.999397] -> mesh_pos=[-0.122225,0.053410,0.960958]
  Free Vertex 65: using x=[-0.110518,0.023946,0.996987] -> mesh_pos=[-0.110518,0.023946,0.958642]
  Free Vertex 66: using x=[-0.061545,-0.047960,0.980129] -> mesh_pos=[-0.061545,-0.047960,0.942432]
  Free Vertex 67: using x=[-0.032621,-0.054942,0.980690] -> mesh_pos=[-0.032621,-0.054942,0.942971]
  Free Vertex 68: using x=[-0.000746,-0.054794,0.980322] -> mesh_pos=[-0.000746,-0.054794,0.942617]
  Free Vertex 69: using x=[0.032559,-0.046839,0.978854] -> mesh_pos=[0.032559,-0.046839,0.941205]
  Free Vertex 70: using x=[0.065018,-0.030743,0.976095] -> mesh_pos=[0.065018,-0.030743,0.938553]
  Free Vertex 71: using x=[0.093322,-0.006692,0.971388] -> mesh_pos=[0.093322,-0.006692,0.934027]
  Free Vertex 72: using x=[0.108293,0.021705,0.965963] -> mesh_pos=[0.108293,0.021705,0.928810]
  Free Vertex 73: using x=[0.104519,0.050765,0.953405] -> mesh_pos=[0.104519,0.050765,0.916736]
  Free Vertex 74: using x=[0.083203,0.078821,0.948596] -> mesh_pos=[0.083203,0.078821,0.912111]
  Free Vertex 75: using x=[0.054536,0.109206,0.944980] -> mesh_pos=[0.054536,0.109206,0.908635]
  Free Vertex 76: using x=[0.031067,0.150886,0.945225] -> mesh_pos=[0.031067,0.150886,0.908870]
  Free Vertex 77: using x=[0.006936,0.195552,0.946396] -> mesh_pos=[0.006936,0.195552,0.909996]
  Free Vertex 78: using x=[-0.026885,0.240231,0.949664] -> mesh_pos=[-0.026885,0.240231,0.913139]
  Free Vertex 79: using x=[-0.088964,0.260904,0.947233] -> mesh_pos=[-0.088964,0.260904,0.910801]
  Free Vertex 80: using x=[-0.144508,0.224677,0.961283] -> mesh_pos=[-0.144508,0.224677,0.924310]
  Free Vertex 81: using x=[-0.166208,0.176812,0.960575] -> mesh_pos=[-0.166208,0.176812,0.923630]
  Free Vertex 82: using x=[-0.174038,0.133837,0.976115] -> mesh_pos=[-0.174038,0.133837,0.938572]
  Free Vertex 83: using x=[-0.184860,0.090939,0.985034] -> mesh_pos=[-0.184860,0.090939,0.947148]
  Free Vertex 84: using x=[-0.159754,0.030067,0.982993] -> mesh_pos=[-0.159754,0.030067,0.945185]
  Free Vertex 85: using x=[-0.145351,0.016711,0.969259] -> mesh_pos=[-0.145351,0.016711,0.931980]
  Free Vertex 86: using x=[-0.108423,-0.010813,0.980279] -> mesh_pos=[-0.108423,-0.010813,0.942576]
  Free Vertex 87: using x=[-0.082423,-0.034508,0.979539] -> mesh_pos=[-0.082423,-0.034508,0.941865]
  Free Vertex 88: using x=[-0.031588,-0.097199,0.951786] -> mesh_pos=[-0.031588,-0.097199,0.915179]
  Free Vertex 89: using x=[0.002981,-0.097966,0.952431] -> mesh_pos=[0.002981,-0.097966,0.915799]
  Free Vertex 90: using x=[0.037835,-0.090189,0.951517] -> mesh_pos=[0.037835,-0.090189,0.914920]
  Free Vertex 91: using x=[0.071584,-0.073795,0.948829] -> mesh_pos=[0.071584,-0.073795,0.912336]
  Free Vertex 92: using x=[0.101547,-0.048944,0.944031] -> mesh_pos=[0.101547,-0.048944,0.907722]
  Free Vertex 93: using x=[0.124176,-0.014506,0.936517] -> mesh_pos=[0.124176,-0.014506,0.900497]
  Free Vertex 94: using x=[0.129173,0.023280,0.928130] -> mesh_pos=[0.129173,0.023280,0.892433]
  Free Vertex 95: using x=[0.118459,0.054937,0.914991] -> mesh_pos=[0.118459,0.054937,0.879799]
  Free Vertex 96: using x=[0.091637,0.084139,0.909530] -> mesh_pos=[0.091637,0.084139,0.874548]
  Free Vertex 97: using x=[0.061259,0.117221,0.908953] -> mesh_pos=[0.061259,0.117221,0.873994]
  Free Vertex 98: using x=[0.038137,0.161288,0.907243] -> mesh_pos=[0.038137,0.161288,0.872349]
  Free Vertex 99: using x=[0.007987,0.206481,0.904564] -> mesh_pos=[0.007987,0.206481,0.869773]
  Free Vertex 100: using x=[-0.025141,0.253833,0.887707] -> mesh_pos=[-0.025141,0.253833,0.853564]
  Free Vertex 101: using x=[-0.105951,0.264247,0.898532] -> mesh_pos=[-0.105951,0.264247,0.863973]
  Free Vertex 102: using x=[-0.167634,0.238049,0.898819] -> mesh_pos=[-0.167634,0.238049,0.864249]
  Free Vertex 103: using x=[-0.195533,0.169420,0.921679] -> mesh_pos=[-0.195533,0.169420,0.886230]
  Free Vertex 104: using x=[-0.198324,0.115639,0.912937] -> mesh_pos=[-0.198324,0.115639,0.877824]
  Free Vertex 105: using x=[-0.194810,0.049359,0.926452] -> mesh_pos=[-0.194810,0.049359,0.890819]
  Free Vertex 106: using x=[-0.161170,0.003298,0.927358] -> mesh_pos=[-0.161170,0.003298,0.891690]
  Free Vertex 107: using x=[-0.131757,-0.039746,0.928788] -> mesh_pos=[-0.131757,-0.039746,0.893065]
  Free Vertex 108: using x=[-0.098969,-0.063208,0.944475] -> mesh_pos=[-0.098969,-0.063208,0.908150]
  Free Vertex 109: using x=[-0.064939,-0.087205,0.949476] -> mesh_pos=[-0.064939,-0.087205,0.912958]
  Free Vertex 110: using x=[-0.013613,-0.134269,0.913188] -> mesh_pos=[-0.013613,-0.134269,0.878065]
  Free Vertex 111: using x=[0.027586,-0.131021,0.914398] -> mesh_pos=[0.027586,-0.131021,0.879229]
  Free Vertex 112: using x=[0.064814,-0.116946,0.913229] -> mesh_pos=[0.064814,-0.116946,0.878105]
  Free Vertex 113: using x=[0.098288,-0.093222,0.909948] -> mesh_pos=[0.098288,-0.093222,0.874950]
  Free Vertex 114: using x=[0.125081,-0.060730,0.904453] -> mesh_pos=[0.125081,-0.060730,0.869666]
  Free Vertex 115: using x=[0.142008,-0.018833,0.896659] -> mesh_pos=[0.142008,-0.018833,0.862172]
  Free Vertex 116: using x=[0.142658,0.022141,0.889083] -> mesh_pos=[0.142658,0.022141,0.854887]
  Free Vertex 117: using x=[0.129764,0.055971,0.877674] -> mesh_pos=[0.129764,0.055971,0.843918]
  Free Vertex 118: using x=[0.101290,0.085707,0.874089] -> mesh_pos=[0.101290,0.085707,0.840470]
  Free Vertex 119: using x=[0.065355,0.114695,0.872530] -> mesh_pos=[0.065355,0.114695,0.838972]
  Free Vertex 120: using x=[0.035877,0.156668,0.868345] -> mesh_pos=[0.035877,0.156668,0.834947]
  Free Vertex 121: using x=[0.005921,0.204975,0.860858] -> mesh_pos=[0.005921,0.204975,0.827748]
  Free Vertex 122: using x=[-0.037284,0.250720,0.854137] -> mesh_pos=[-0.037284,0.250720,0.821285]
  Free Vertex 123: using x=[-0.126257,0.261771,0.855004] -> mesh_pos=[-0.126257,0.261771,0.822119]
  Free Vertex 124: using x=[-0.179875,0.234879,0.870524] -> mesh_pos=[-0.179875,0.234879,0.837043]
  Free Vertex 125: using x=[-0.198831,0.171067,0.869395] -> mesh_pos=[-0.198831,0.171067,0.835957]
  Free Vertex 126: using x=[-0.204168,0.106293,0.876265] -> mesh_pos=[-0.204168,0.106293,0.842563]
  Free Vertex 127: using x=[-0.183171,0.035130,0.873277] -> mesh_pos=[-0.183171,0.035130,0.839689]
  Free Vertex 128: using x=[-0.144952,-0.009921,0.883532] -> mesh_pos=[-0.144952,-0.009921,0.849550]
  Free Vertex 129: using x=[-0.124028,-0.059368,0.886455] -> mesh_pos=[-0.124028,-0.059368,0.852361]
  Free Vertex 130: using x=[-0.095362,-0.096369,0.902967] -> mesh_pos=[-0.095362,-0.096369,0.868238]
  Free Vertex 131: using x=[-0.057609,-0.123367,0.909399] -> mesh_pos=[-0.057609,-0.123367,0.874423]
  Free Vertex 132: using x=[-0.008190,-0.156295,0.871135] -> mesh_pos=[-0.008190,-0.156295,0.837629]
  Free Vertex 133: using x=[0.039557,-0.151560,0.872738] -> mesh_pos=[0.039557,-0.151560,0.839171]
  Free Vertex 134: using x=[0.078821,-0.133645,0.871772] -> mesh_pos=[0.078821,-0.133645,0.838242]
  Free Vertex 135: using x=[0.112521,-0.105101,0.868758] -> mesh_pos=[0.112521,-0.105101,0.835344]
  Free Vertex 136: using x=[0.137839,-0.067555,0.863735] -> mesh_pos=[0.137839,-0.067555,0.830514]
  Free Vertex 137: using x=[0.152218,-0.021037,0.856767] -> mesh_pos=[0.152218,-0.021037,0.823815]
  Free Vertex 138: using x=[0.151209,0.022319,0.850260] -> mesh_pos=[0.151209,0.022319,0.817558]
  Free Vertex 139: using x=[0.136118,0.057680,0.840170] -> mesh_pos=[0.136118,0.057680,0.807856]
  Free Vertex 140: using x=[0.105721,0.088169,0.836849] -> mesh_pos=[0.105721,0.088169,0.804662]
  Free Vertex 141: using x=[0.066061,0.114488,0.835265] -> mesh_pos=[0.066061,0.114488,0.803140]
  Free Vertex 142: using x=[0.028741,0.152636,0.830567] -> mesh_pos=[0.028741,0.152636,0.798622]
  Free Vertex 143: using x=[-0.009046,0.196520,0.821763] -> mesh_pos=[-0.009046,0.196520,0.790157]
  Free Vertex 144: using x=[-0.045478,0.245406,0.817728] -> mesh_pos=[-0.045478,0.245406,0.786277]
  Free Vertex 145: using x=[-0.134818,0.264108,0.816252] -> mesh_pos=[-0.134818,0.264108,0.784858]
  Free Vertex 146: using x=[-0.190160,0.228924,0.824014] -> mesh_pos=[-0.190160,0.228924,0.792321]
  Free Vertex 147: using x=[-0.215206,0.156738,0.847502] -> mesh_pos=[-0.215206,0.156738,0.814905]
  Free Vertex 148: using x=[-0.195934,0.087599,0.837861] -> mesh_pos=[-0.195934,0.087599,0.805635]
  Free Vertex 149: using x=[-0.181073,0.034231,0.841522] -> mesh_pos=[-0.181073,0.034231,0.809156]
  Free Vertex 150: using x=[-0.148807,-0.022294,0.845604] -> mesh_pos=[-0.148807,-0.022294,0.813081]
  Free Vertex 151: using x=[-0.126362,-0.074070,0.848566] -> mesh_pos=[-0.126362,-0.074070,0.815929]
  Free Vertex 152: using x=[-0.095478,-0.115317,0.861228] -> mesh_pos=[-0.095478,-0.115317,0.828103]
  Free Vertex 153: using x=[-0.055174,-0.144368,0.867551] -> mesh_pos=[-0.055174,-0.144368,0.834183]
  Pinned Vertex 154: using xconst=[-0.135840,0.078511,1.073723] -> mesh_pos=[-0.135840,0.078511,1.032426]
  Pinned Vertex 155: using xconst=[-0.122557,0.057010,1.073291] -> mesh_pos=[-0.122557,0.057010,1.032010]
  Pinned Vertex 156: using xconst=[-0.102748,0.040287,1.072660] -> mesh_pos=[-0.102748,0.040287,1.031404]
  Pinned Vertex 157: using xconst=[-0.077888,0.029702,1.071925] -> mesh_pos=[-0.077888,0.029702,1.030697]
  Pinned Vertex 158: using xconst=[-0.049934,0.026216,1.071190] -> mesh_pos=[-0.049934,0.026216,1.029991]
  Pinned Vertex 159: using xconst=[-0.021189,0.030219,1.070552] -> mesh_pos=[-0.021189,0.030219,1.029377]
  Pinned Vertex 160: using xconst=[-0.003175,0.037429,1.070089] -> mesh_pos=[-0.003175,0.037429,1.028932]
  Pinned Vertex 161: using xconst=[0.003483,0.046956,1.069822] -> mesh_pos=[0.003483,0.046956,1.028675]
  Pinned Vertex 162: using xconst=[0.007377,0.062741,1.069524] -> mesh_pos=[0.007377,0.062741,1.028388]
  Pinned Vertex 163: using xconst=[0.007414,0.083650,1.069335] -> mesh_pos=[0.007414,0.083650,1.028207]
  Pinned Vertex 164: using xconst=[0.003165,0.107671,1.069341] -> mesh_pos=[0.003165,0.107671,1.028212]
  Pinned Vertex 165: using xconst=[-0.005152,0.132561,1.069562] -> mesh_pos=[-0.005152,0.132561,1.028425]
  Pinned Vertex 166: using xconst=[-0.017211,0.156963,1.069994] -> mesh_pos=[-0.017211,0.156963,1.028840]
  Pinned Vertex 167: using xconst=[-0.031525,0.177812,1.070582] -> mesh_pos=[-0.031525,0.177812,1.029406]
  Pinned Vertex 168: using xconst=[-0.046626,0.193273,1.071252] -> mesh_pos=[-0.046626,0.193273,1.030050]
  Pinned Vertex 169: using xconst=[-0.060748,0.202154,1.071873] -> mesh_pos=[-0.060748,0.202154,1.030647]
  Pinned Vertex 170: using xconst=[-0.072236,0.204408,1.072286] -> mesh_pos=[-0.072236,0.204408,1.031044]
  Pinned Vertex 171: using xconst=[-0.089999,0.196810,1.072743] -> mesh_pos=[-0.089999,0.196810,1.031484]
  Pinned Vertex 172: using xconst=[-0.113039,0.179132,1.073204] -> mesh_pos=[-0.113039,0.179132,1.031927]
  Pinned Vertex 173: using xconst=[-0.130174,0.156472,1.073597] -> mesh_pos=[-0.130174,0.156472,1.032305]
  Pinned Vertex 174: using xconst=[-0.140057,0.130741,1.073857] -> mesh_pos=[-0.140057,0.130741,1.032555]
  Pinned Vertex 175: using xconst=[-0.141971,0.104045,1.073915] -> mesh_pos=[-0.141971,0.104045,1.032610]
  Free Vertex 176: using x=[-0.007013,-0.169269,0.831255] -> mesh_pos=[-0.007013,-0.169269,0.799283]
  Free Vertex 177: using x=[0.044245,-0.163950,0.832945] -> mesh_pos=[0.044245,-0.163950,0.800909]
  Free Vertex 178: using x=[0.085322,-0.144060,0.832138] -> mesh_pos=[0.085322,-0.144060,0.800132]
  Free Vertex 179: using x=[0.119947,-0.112739,0.829388] -> mesh_pos=[0.119947,-0.112739,0.797488]
  Free Vertex 180: using x=[0.145379,-0.072075,0.824786] -> mesh_pos=[0.145379,-0.072075,0.793064]
  Free Vertex 181: using x=[0.159425,-0.024493,0.818554] -> mesh_pos=[0.159425,-0.024493,0.787071]
  Free Vertex 182: using x=[0.159904,0.019729,0.812945] -> mesh_pos=[0.159904,0.019729,0.781678]
  Free Vertex 183: using x=[0.147917,0.055776,0.804057] -> mesh_pos=[0.147917,0.055776,0.773132]
  Free Vertex 184: using x=[0.122305,0.087607,0.802545] -> mesh_pos=[0.122305,0.087607,0.771678]
  Free Vertex 185: using x=[0.086391,0.112146,0.802529] -> mesh_pos=[0.086391,0.112146,0.771663]
  Free Vertex 186: using x=[0.043506,0.127628,0.799836] -> mesh_pos=[0.043506,0.127628,0.769073]
  Free Vertex 187: using x=[-0.009979,0.166201,0.795793] -> mesh_pos=[-0.009979,0.166201,0.765185]
  Free Vertex 188: using x=[-0.039114,0.220475,0.788466] -> mesh_pos=[-0.039114,0.220475,0.758141]
  Free Vertex 189: using x=[-0.084823,0.261554,0.787271] -> mesh_pos=[-0.084823,0.261554,0.756991]
  Free Vertex 190: using x=[-0.186474,0.241644,0.812998] -> mesh_pos=[-0.186474,0.241644,0.781729]
  Free Vertex 191: using x=[-0.218591,0.175430,0.816004] -> mesh_pos=[-0.218591,0.175430,0.784619]
  Free Vertex 192: using x=[-0.190573,0.093682,0.808735] -> mesh_pos=[-0.190573,0.093682,0.777629]
  Free Vertex 193: using x=[-0.185019,0.031096,0.811069] -> mesh_pos=[-0.185019,0.031096,0.779874]
  Free Vertex 194: using x=[-0.172407,-0.031466,0.814117] -> mesh_pos=[-0.172407,-0.031466,0.782804]
  Free Vertex 195: using x=[-0.129992,-0.080765,0.810673] -> mesh_pos=[-0.129992,-0.080765,0.779493]
  Free Vertex 196: using x=[-0.098010,-0.125550,0.821696] -> mesh_pos=[-0.098010,-0.125550,0.790092]
  Free Vertex 197: using x=[-0.055935,-0.156569,0.827826] -> mesh_pos=[-0.055935,-0.156569,0.795986]
UPBGE single frame mode: using pinned vertex constraint positions
UPBGE: forcing object update after cloth simulation
MOD_cloth: copying cloth results back to mesh positions
  MOD_cloth Vertex 0: pos=[-0.149828,0.085155,0.983047]
  MOD_cloth Vertex 1: pos=[-0.134773,0.062338,0.983576]
  MOD_cloth Vertex 2: pos=[-0.112939,0.044791,0.983814]
  MOD_cloth Vertex 3: pos=[-0.085886,0.033841,0.983896]
  MOD_cloth Vertex 4: pos=[-0.055533,0.030391,0.984073]
MOD_cloth: forced object data re-evaluation
璋冪敤鎴愬姛---BKE_rigidbody_update_for_skeletal_animation
璋冪敤鎴愬姛---BKE_rigidbody_update_for_skeletal_animation
璋冪敤鎴愬姛---BKE_rigidbody_update_for_skeletal_animation
璋冪敤鎴愬姛---BKE_rigidbody_update_for_skeletal_animation
璋冪敤鎴愬姛---BKE_rigidbody_update_for_skeletal_animation
use_interactive_rb=false,should_simulate=false----
Should simulate...
need_first_init = 0
cache_flag_baked = 1 --- is_single_frame_mode = 1 --- can_simulate = 1
framenr = 1, last_frame = 1, timescale = 1.000000
UPBGE single frame mode: skipping cache, forcing simulation
Object matrix unchanged
Vertex 0: local=[-0.000018,-0.111819,1.024277] world=[-0.000018,-0.111819,1.065248]
Vertex 0: old_rest=[-0.000018,-0.111819,1.065248] current_mesh=[-0.000018,-0.111819,1.065248] delta=[0.000000,0.000000,0.000000]
Vertex 1: old_rest=[0.028666,-0.107355,1.065187] current_mesh=[0.028666,-0.107355,1.065187] delta=[0.000000,0.000000,0.000000]
Vertex 2: old_rest=[0.055114,-0.094275,1.065030] current_mesh=[0.055114,-0.094275,1.065030] delta=[0.000000,0.000000,0.000000]
SIM_cloth_solve: tf=1.000000, dt_base=0.200000, timescale=1.000000, dt_final=0.200000
cloth_solve--tf-- 1.000000
=== Physics step 0.000000 ===
1. Constraints setup completed
2. Forces cleared
cloth_calc_force called - mvert_num: 198, avg_spring_len: 0.036831
Gravity enabled: [0.000000, 0.000000, -0.019620]
cloth_calc_force completed - processed 726 springs
3. Forces calculated
4. Velocities solved - status: 1, iterations: 8
5. Collisions processed
6. Positions solved
7. Results applied
Pinned vertex 154: set to xconst=[-0.135840,0.078511,1.073723]
Pinned vertex 155: set to xconst=[-0.122557,0.057010,1.073291]
Pinned vertex 156: set to xconst=[-0.102748,0.040287,1.072660]
Pinned vertex 157: set to xconst=[-0.077888,0.029702,1.071925]
Pinned vertex 158: set to xconst=[-0.049934,0.026216,1.071190]
Pinned vertex 159: set to xconst=[-0.021189,0.030219,1.070552]
Pinned vertex 160: set to xconst=[-0.003175,0.037429,1.070089]
Pinned vertex 161: set to xconst=[0.003483,0.046956,1.069822]
Pinned vertex 162: set to xconst=[0.007377,0.062741,1.069524]
Pinned vertex 163: set to xconst=[0.007414,0.083650,1.069335]
Pinned vertex 164: set to xconst=[0.003165,0.107671,1.069341]
Pinned vertex 165: set to xconst=[-0.005152,0.132561,1.069562]
Pinned vertex 166: set to xconst=[-0.017211,0.156963,1.069994]
Pinned vertex 167: set to xconst=[-0.031525,0.177812,1.070582]
Pinned vertex 168: set to xconst=[-0.046626,0.193273,1.071252]
Pinned vertex 169: set to xconst=[-0.060748,0.202154,1.071873]
Pinned vertex 170: set to xconst=[-0.072236,0.204408,1.072286]
Pinned vertex 171: set to xconst=[-0.089999,0.196810,1.072743]
Pinned vertex 172: set to xconst=[-0.113039,0.179132,1.073204]
Pinned vertex 173: set to xconst=[-0.130174,0.156472,1.073597]
Pinned vertex 174: set to xconst=[-0.140057,0.130741,1.073857]
Pinned vertex 175: set to xconst=[-0.141971,0.104045,1.073915]
8. Pinned vertices updated
=== Physics step 0.200000 ===
1. Constraints setup completed
2. Forces cleared
cloth_calc_force called - mvert_num: 198, avg_spring_len: 0.036831
Gravity enabled: [0.000000, 0.000000, -0.019620]
cloth_calc_force completed - processed 726 springs
3. Forces calculated
4. Velocities solved - status: 1, iterations: 5
5. Collisions processed
6. Positions solved
7. Results applied
Pinned vertex 154: set to xconst=[-0.135840,0.078511,1.073723]
Pinned vertex 155: set to xconst=[-0.122557,0.057010,1.073291]
Pinned vertex 156: set to xconst=[-0.102748,0.040287,1.072660]
Pinned vertex 157: set to xconst=[-0.077888,0.029702,1.071925]
Pinned vertex 158: set to xconst=[-0.049934,0.026216,1.071190]
Pinned vertex 159: set to xconst=[-0.021189,0.030219,1.070552]
Pinned vertex 160: set to xconst=[-0.003175,0.037429,1.070089]
Pinned vertex 161: set to xconst=[0.003483,0.046956,1.069822]
Pinned vertex 162: set to xconst=[0.007377,0.062741,1.069524]
Pinned vertex 163: set to xconst=[0.007414,0.083650,1.069335]
Pinned vertex 164: set to xconst=[0.003165,0.107671,1.069341]
Pinned vertex 165: set to xconst=[-0.005152,0.132561,1.069562]
Pinned vertex 166: set to xconst=[-0.017211,0.156963,1.069994]
Pinned vertex 167: set to xconst=[-0.031525,0.177812,1.070582]
Pinned vertex 168: set to xconst=[-0.046626,0.193273,1.071252]
Pinned vertex 169: set to xconst=[-0.060748,0.202154,1.071873]
Pinned vertex 170: set to xconst=[-0.072236,0.204408,1.072286]
Pinned vertex 171: set to xconst=[-0.089999,0.196810,1.072743]
Pinned vertex 172: set to xconst=[-0.113039,0.179132,1.073204]
Pinned vertex 173: set to xconst=[-0.130174,0.156472,1.073597]
Pinned vertex 174: set to xconst=[-0.140057,0.130741,1.073857]
Pinned vertex 175: set to xconst=[-0.141971,0.104045,1.073915]
8. Pinned vertices updated
=== Physics step 0.400000 ===
1. Constraints setup completed
2. Forces cleared
cloth_calc_force called - mvert_num: 198, avg_spring_len: 0.036831
Gravity enabled: [0.000000, 0.000000, -0.019620]
cloth_calc_force completed - processed 726 springs
3. Forces calculated
4. Velocities solved - status: 1, iterations: 5
5. Collisions processed
6. Positions solved
7. Results applied
Pinned vertex 154: set to xconst=[-0.135840,0.078511,1.073723]
Pinned vertex 155: set to xconst=[-0.122557,0.057010,1.073291]
Pinned vertex 156: set to xconst=[-0.102748,0.040287,1.072660]
Pinned vertex 157: set to xconst=[-0.077888,0.029702,1.071925]
Pinned vertex 158: set to xconst=[-0.049934,0.026216,1.071190]
Pinned vertex 159: set to xconst=[-0.021189,0.030219,1.070552]
Pinned vertex 160: set to xconst=[-0.003175,0.037429,1.070089]
Pinned vertex 161: set to xconst=[0.003483,0.046956,1.069822]
Pinned vertex 162: set to xconst=[0.007377,0.062741,1.069524]
Pinned vertex 163: set to xconst=[0.007414,0.083650,1.069335]
Pinned vertex 164: set to xconst=[0.003165,0.107671,1.069341]
Pinned vertex 165: set to xconst=[-0.005152,0.132561,1.069562]
Pinned vertex 166: set to xconst=[-0.017211,0.156963,1.069994]
Pinned vertex 167: set to xconst=[-0.031525,0.177812,1.070582]
Pinned vertex 168: set to xconst=[-0.046626,0.193273,1.071252]
Pinned vertex 169: set to xconst=[-0.060748,0.202154,1.071873]
Pinned vertex 170: set to xconst=[-0.072236,0.204408,1.072286]
Pinned vertex 171: set to xconst=[-0.089999,0.196810,1.072743]
Pinned vertex 172: set to xconst=[-0.113039,0.179132,1.073204]
Pinned vertex 173: set to xconst=[-0.130174,0.156472,1.073597]
Pinned vertex 174: set to xconst=[-0.140057,0.130741,1.073857]
Pinned vertex 175: set to xconst=[-0.141971,0.104045,1.073915]
8. Pinned vertices updated
=== Physics step 0.600000 ===
1. Constraints setup completed
2. Forces cleared
cloth_calc_force called - mvert_num: 198, avg_spring_len: 0.036831
Gravity enabled: [0.000000, 0.000000, -0.019620]
cloth_calc_force completed - processed 726 springs
3. Forces calculated
4. Velocities solved - status: 1, iterations: 7
5. Collisions processed
6. Positions solved
7. Results applied
Pinned vertex 154: set to xconst=[-0.135840,0.078511,1.073723]
Pinned vertex 155: set to xconst=[-0.122557,0.057010,1.073291]
Pinned vertex 156: set to xconst=[-0.102748,0.040287,1.072660]
Pinned vertex 157: set to xconst=[-0.077888,0.029702,1.071925]
Pinned vertex 158: set to xconst=[-0.049934,0.026216,1.071190]
Pinned vertex 159: set to xconst=[-0.021189,0.030219,1.070552]
Pinned vertex 160: set to xconst=[-0.003175,0.037429,1.070089]
Pinned vertex 161: set to xconst=[0.003483,0.046956,1.069822]
Pinned vertex 162: set to xconst=[0.007377,0.062741,1.069524]
Pinned vertex 163: set to xconst=[0.007414,0.083650,1.069335]
Pinned vertex 164: set to xconst=[0.003165,0.107671,1.069341]
Pinned vertex 165: set to xconst=[-0.005152,0.132561,1.069562]
Pinned vertex 166: set to xconst=[-0.017211,0.156963,1.069994]
Pinned vertex 167: set to xconst=[-0.031525,0.177812,1.070582]
Pinned vertex 168: set to xconst=[-0.046626,0.193273,1.071252]
Pinned vertex 169: set to xconst=[-0.060748,0.202154,1.071873]
Pinned vertex 170: set to xconst=[-0.072236,0.204408,1.072286]
Pinned vertex 171: set to xconst=[-0.089999,0.196810,1.072743]
Pinned vertex 172: set to xconst=[-0.113039,0.179132,1.073204]
Pinned vertex 173: set to xconst=[-0.130174,0.156472,1.073597]
Pinned vertex 174: set to xconst=[-0.140057,0.130741,1.073857]
Pinned vertex 175: set to xconst=[-0.141971,0.104045,1.073915]
8. Pinned vertices updated
=== Physics step 0.800000 ===
1. Constraints setup completed
2. Forces cleared
cloth_calc_force called - mvert_num: 198, avg_spring_len: 0.036831
Gravity enabled: [0.000000, 0.000000, -0.019620]
cloth_calc_force completed - processed 726 springs
3. Forces calculated
4. Velocities solved - status: 1, iterations: 7
5. Collisions processed
6. Positions solved
7. Results applied
Pinned vertex 154: set to xconst=[-0.135840,0.078511,1.073723]
Pinned vertex 155: set to xconst=[-0.122557,0.057010,1.073291]
Pinned vertex 156: set to xconst=[-0.102748,0.040287,1.072660]
Pinned vertex 157: set to xconst=[-0.077888,0.029702,1.071925]
Pinned vertex 158: set to xconst=[-0.049934,0.026216,1.071190]
Pinned vertex 159: set to xconst=[-0.021189,0.030219,1.070552]
Pinned vertex 160: set to xconst=[-0.003175,0.037429,1.070089]
Pinned vertex 161: set to xconst=[0.003483,0.046956,1.069822]
Pinned vertex 162: set to xconst=[0.007377,0.062741,1.069524]
Pinned vertex 163: set to xconst=[0.007414,0.083650,1.069335]
Pinned vertex 164: set to xconst=[0.003165,0.107671,1.069341]
Pinned vertex 165: set to xconst=[-0.005152,0.132561,1.069562]
Pinned vertex 166: set to xconst=[-0.017211,0.156963,1.069994]
Pinned vertex 167: set to xconst=[-0.031525,0.177812,1.070582]
Pinned vertex 168: set to xconst=[-0.046626,0.193273,1.071252]
Pinned vertex 169: set to xconst=[-0.060748,0.202154,1.071873]
Pinned vertex 170: set to xconst=[-0.072236,0.204408,1.072286]
Pinned vertex 171: set to xconst=[-0.089999,0.196810,1.072743]
Pinned vertex 172: set to xconst=[-0.113039,0.179132,1.073204]
Pinned vertex 173: set to xconst=[-0.130174,0.156472,1.073597]
Pinned vertex 174: set to xconst=[-0.140057,0.130741,1.073857]
Pinned vertex 175: set to xconst=[-0.141971,0.104045,1.073915]
8. Pinned vertices updated
=== Final vertex states ===
Vertex 0 (free): pos=[-0.149112,0.084167,1.022501] vel=[-0.039756,0.046818,-0.037883]
Vertex 1 (free): pos=[-0.133908,0.061445,1.023108] vel=[-0.044971,0.042638,-0.036471]
Vertex 2 (free): pos=[-0.112016,0.044055,1.023359] vel=[-0.048714,0.037247,-0.036089]
Vertex 3 (free): pos=[-0.085014,0.033302,1.023413] vel=[-0.051164,0.031645,-0.036211]
Vertex 4 (free): pos=[-0.054865,0.030048,1.023564] vel=[-0.052510,0.026305,-0.036905]
Vertex 5 (free): pos=[-0.023697,0.033973,1.024238] vel=[-0.052726,0.021116,-0.038439]
Vertex 6 (free): pos=[0.001545,0.039966,1.023005] vel=[-0.038218,0.031934,-0.063198]
Vertex 7 (free): pos=[0.009949,0.056286,1.026988] vel=[-0.042516,0.037925,-0.066407]
Vertex 8 (free): pos=[0.013762,0.080341,1.028759] vel=[-0.051211,0.031392,-0.058427]
Vertex 9 (free): pos=[0.005967,0.106560,1.030446] vel=[-0.039448,0.040322,-0.049711]
cloth_to_object: copying 198 vertices to mesh
  Free Vertex 0: using x=[-0.149112,0.084167,1.022501] -> mesh_pos=[-0.149112,0.084167,0.983174]
  Free Vertex 1: using x=[-0.133908,0.061445,1.023108] -> mesh_pos=[-0.133908,0.061445,0.983757]
  Free Vertex 2: using x=[-0.112016,0.044055,1.023359] -> mesh_pos=[-0.112016,0.044055,0.983999]
  Free Vertex 3: using x=[-0.085014,0.033302,1.023413] -> mesh_pos=[-0.085014,0.033302,0.984051]
  Free Vertex 4: using x=[-0.054865,0.030048,1.023564] -> mesh_pos=[-0.054865,0.030048,0.984196]
  Free Vertex 5: using x=[-0.023697,0.033973,1.024238] -> mesh_pos=[-0.023697,0.033973,0.984844]
  Free Vertex 6: using x=[0.001545,0.039966,1.023005] -> mesh_pos=[0.001545,0.039966,0.983659]
  Free Vertex 7: using x=[0.009949,0.056286,1.026988] -> mesh_pos=[0.009949,0.056286,0.987488]
  Free Vertex 8: using x=[0.013762,0.080341,1.028759] -> mesh_pos=[0.013762,0.080341,0.989191]
  Free Vertex 9: using x=[0.005967,0.106560,1.030446] -> mesh_pos=[0.005967,0.106560,0.990814]
  Free Vertex 10: using x=[0.004619,0.136377,1.030796] -> mesh_pos=[0.004619,0.136377,0.991150]
  Free Vertex 11: using x=[0.009219,0.167352,1.032823] -> mesh_pos=[0.009219,0.167352,0.993099]
  Free Vertex 12: using x=[-0.012501,0.189534,1.026286] -> mesh_pos=[-0.012501,0.189534,0.986813]
  Free Vertex 13: using x=[-0.041821,0.198816,1.033844] -> mesh_pos=[-0.041821,0.198816,0.994080]
  Free Vertex 14: using x=[-0.070507,0.202064,1.032044] -> mesh_pos=[-0.070507,0.202064,0.992350]
  Free Vertex 15: using x=[-0.079690,0.226916,1.024991] -> mesh_pos=[-0.079690,0.226916,0.985568]
  Free Vertex 16: using x=[-0.094957,0.238347,1.026471] -> mesh_pos=[-0.094957,0.238347,0.986991]
  Free Vertex 17: using x=[-0.110827,0.217749,1.029315] -> mesh_pos=[-0.110827,0.217749,0.989726]
  Free Vertex 18: using x=[-0.127547,0.198655,1.027068] -> mesh_pos=[-0.127547,0.198655,0.987565]
  Free Vertex 19: using x=[-0.156237,0.175471,1.021101] -> mesh_pos=[-0.156237,0.175471,0.981828]
  Free Vertex 20: using x=[-0.148689,0.142881,1.021634] -> mesh_pos=[-0.148689,0.142881,0.982340]
  Free Vertex 21: using x=[-0.156410,0.112445,1.021523] -> mesh_pos=[-0.156410,0.112445,0.982234]
  Free Vertex 22: using x=[-0.126164,0.048842,1.008275] -> mesh_pos=[-0.126164,0.048842,0.969495]
  Free Vertex 23: using x=[-0.106853,0.029864,1.008983] -> mesh_pos=[-0.106853,0.029864,0.970176]
  Free Vertex 24: using x=[-0.081447,0.016654,1.009303] -> mesh_pos=[-0.081447,0.016654,0.970483]
  Free Vertex 25: using x=[-0.051585,0.010443,1.009348] -> mesh_pos=[-0.051585,0.010443,0.970527]
  Free Vertex 26: using x=[-0.019368,0.012018,1.009395] -> mesh_pos=[-0.019368,0.012018,0.970572]
  Free Vertex 27: using x=[0.012881,0.020826,1.009940] -> mesh_pos=[0.012881,0.020826,0.971096]
  Free Vertex 28: using x=[0.037205,0.026356,1.013309] -> mesh_pos=[0.037205,0.026356,0.974335]
  Free Vertex 29: using x=[0.047502,0.047093,1.015844] -> mesh_pos=[0.047502,0.047093,0.976773]
  Free Vertex 30: using x=[0.051979,0.072836,1.016517] -> mesh_pos=[0.051979,0.072836,0.977421]
  Free Vertex 31: using x=[0.039770,0.089672,1.013189] -> mesh_pos=[0.039770,0.089672,0.974220]
  Free Vertex 32: using x=[0.016170,0.098783,1.010927] -> mesh_pos=[0.016170,0.098783,0.972046]
  Free Vertex 33: using x=[0.014507,0.128537,1.019026] -> mesh_pos=[0.014507,0.128537,0.979833]
  Free Vertex 34: using x=[-0.014257,0.144689,1.027821] -> mesh_pos=[-0.014257,0.144689,0.988290]
  Free Vertex 35: using x=[-0.046877,0.155289,1.034989] -> mesh_pos=[-0.046877,0.155289,0.995181]
  Free Vertex 36: using x=[-0.080459,0.156165,1.032409] -> mesh_pos=[-0.080459,0.156165,0.992701]
  Free Vertex 37: using x=[-0.093215,0.182392,1.026983] -> mesh_pos=[-0.093215,0.182392,0.987484]
  Free Vertex 38: using x=[-0.107233,0.195896,1.022743] -> mesh_pos=[-0.107233,0.195896,0.983407]
  Free Vertex 39: using x=[-0.118674,0.173672,1.026209] -> mesh_pos=[-0.118674,0.173672,0.986740]
  Free Vertex 40: using x=[-0.125091,0.156199,1.021097] -> mesh_pos=[-0.125091,0.156199,0.981824]
  Free Vertex 41: using x=[-0.152357,0.131881,1.016736] -> mesh_pos=[-0.152357,0.131881,0.977631]
  Free Vertex 42: using x=[-0.139243,0.099112,1.017967] -> mesh_pos=[-0.139243,0.099112,0.978815]
  Free Vertex 43: using x=[-0.138437,0.073136,1.007201] -> mesh_pos=[-0.138437,0.073136,0.968463]
  Free Vertex 44: using x=[-0.095023,0.003014,0.997429] -> mesh_pos=[-0.095023,0.003014,0.959066]
  Free Vertex 45: using x=[-0.071070,-0.010590,0.998221] -> mesh_pos=[-0.071070,-0.010590,0.959828]
  Free Vertex 46: using x=[-0.042339,-0.017740,0.998362] -> mesh_pos=[-0.042339,-0.017740,0.959964]
  Free Vertex 47: using x=[-0.010484,-0.017475,0.997923] -> mesh_pos=[-0.010484,-0.017475,0.959542]
  Free Vertex 48: using x=[0.022352,-0.009266,0.997013] -> mesh_pos=[0.022352,-0.009266,0.958667]
  Free Vertex 49: using x=[0.053731,0.006156,0.995729] -> mesh_pos=[0.053731,0.006156,0.957432]
  Free Vertex 50: using x=[0.076155,0.019213,0.996648] -> mesh_pos=[0.076155,0.019213,0.958316]
  Free Vertex 51: using x=[0.082690,0.044524,0.991542] -> mesh_pos=[0.082690,0.044524,0.953405]
  Free Vertex 52: using x=[0.081643,0.067425,0.988653] -> mesh_pos=[0.081643,0.067425,0.950628]
  Free Vertex 53: using x=[0.065950,0.083256,0.980895] -> mesh_pos=[0.065950,0.083256,0.943168]
  Free Vertex 54: using x=[0.048907,0.080994,0.992339] -> mesh_pos=[0.048907,0.080994,0.954173]
  Free Vertex 55: using x=[0.011156,0.093964,0.993833] -> mesh_pos=[0.011156,0.093964,0.955608]
  Free Vertex 56: using x=[-0.025750,0.096345,1.015363] -> mesh_pos=[-0.025750,0.096345,0.976310]
  Free Vertex 57: using x=[-0.066021,0.101029,1.028527] -> mesh_pos=[-0.066021,0.101029,0.988969]
  Free Vertex 58: using x=[-0.111667,0.083297,1.027292] -> mesh_pos=[-0.111667,0.083297,0.987780]
  Free Vertex 59: using x=[-0.125412,0.124249,1.030736] -> mesh_pos=[-0.125412,0.124249,0.991092]
  Free Vertex 60: using x=[-0.128140,0.142130,1.015554] -> mesh_pos=[-0.128140,0.142130,0.976494]
  Free Vertex 61: using x=[-0.137067,0.107481,1.032661] -> mesh_pos=[-0.137067,0.107481,0.992943]
  Free Vertex 62: using x=[-0.130865,0.095887,1.018163] -> mesh_pos=[-0.130865,0.095887,0.979003]
  Free Vertex 63: using x=[-0.140704,0.078115,0.995425] -> mesh_pos=[-0.140704,0.078115,0.957139]
  Free Vertex 64: using x=[-0.120450,0.044700,1.007407] -> mesh_pos=[-0.120450,0.044700,0.968661]
  Free Vertex 65: using x=[-0.113521,0.023004,0.996039] -> mesh_pos=[-0.113521,0.023004,0.957730]
  Free Vertex 66: using x=[-0.060287,-0.049516,0.979853] -> mesh_pos=[-0.060287,-0.049516,0.942167]
  Free Vertex 67: using x=[-0.031406,-0.056291,0.980490] -> mesh_pos=[-0.031406,-0.056291,0.942779]
  Free Vertex 68: using x=[0.000574,-0.055899,0.980148] -> mesh_pos=[0.000574,-0.055899,0.942450]
  Free Vertex 69: using x=[0.033822,-0.047634,0.978704] -> mesh_pos=[0.033822,-0.047634,0.941061]
  Free Vertex 70: using x=[0.065976,-0.031242,0.975945] -> mesh_pos=[0.065976,-0.031242,0.938409]
  Free Vertex 71: using x=[0.094110,-0.007455,0.971456] -> mesh_pos=[0.094110,-0.007455,0.934092]
  Free Vertex 72: using x=[0.110685,0.015575,0.967544] -> mesh_pos=[0.110685,0.015575,0.930331]
  Free Vertex 73: using x=[0.112772,0.043191,0.954920] -> mesh_pos=[0.112772,0.043191,0.918193]
  Free Vertex 74: using x=[0.099246,0.071712,0.951682] -> mesh_pos=[0.099246,0.071712,0.915079]
  Free Vertex 75: using x=[0.072461,0.092747,0.943056] -> mesh_pos=[0.072461,0.092747,0.906785]
  Free Vertex 76: using x=[0.050472,0.098018,0.960103] -> mesh_pos=[0.050472,0.098018,0.923176]
  Free Vertex 77: using x=[0.008833,0.112160,0.963904] -> mesh_pos=[0.008833,0.112160,0.926831]
  Free Vertex 78: using x=[-0.031094,0.111523,0.974425] -> mesh_pos=[-0.031094,0.111523,0.936947]
  Free Vertex 79: using x=[-0.080960,0.085001,0.976460] -> mesh_pos=[-0.080960,0.085001,0.938904]
  Free Vertex 80: using x=[-0.125559,0.058933,0.973095] -> mesh_pos=[-0.125559,0.058933,0.935668]
  Free Vertex 81: using x=[-0.140245,0.041134,0.971097] -> mesh_pos=[-0.140245,0.041134,0.933747]
  Free Vertex 82: using x=[-0.162334,0.062928,0.991810] -> mesh_pos=[-0.162334,0.062928,0.953664]
  Free Vertex 83: using x=[-0.148098,0.040996,0.991240] -> mesh_pos=[-0.148098,0.040996,0.953116]
  Free Vertex 84: using x=[-0.125909,0.023037,0.980169] -> mesh_pos=[-0.125909,0.023037,0.942470]
  Free Vertex 85: using x=[-0.132244,-0.000852,0.961515] -> mesh_pos=[-0.132244,-0.000852,0.924534]
  Free Vertex 86: using x=[-0.093937,-0.018467,0.981293] -> mesh_pos=[-0.093937,-0.018467,0.943551]
  Free Vertex 87: using x=[-0.085571,-0.035629,0.978459] -> mesh_pos=[-0.085571,-0.035629,0.940826]
  Free Vertex 88: using x=[-0.029960,-0.098955,0.950920] -> mesh_pos=[-0.029960,-0.098955,0.914346]
  Free Vertex 89: using x=[0.004135,-0.099382,0.951576] -> mesh_pos=[0.004135,-0.099382,0.914976]
  Free Vertex 90: using x=[0.039100,-0.091332,0.950722] -> mesh_pos=[0.039100,-0.091332,0.914156]
  Free Vertex 91: using x=[0.072790,-0.074596,0.948122] -> mesh_pos=[0.072790,-0.074596,0.911656]
  Free Vertex 92: using x=[0.102504,-0.049416,0.943480] -> mesh_pos=[0.102504,-0.049416,0.907192]
  Free Vertex 93: using x=[0.124769,-0.016787,0.936432] -> mesh_pos=[0.124769,-0.016787,0.900415]
  Free Vertex 94: using x=[0.132989,0.015955,0.929923] -> mesh_pos=[0.132989,0.015955,0.894157]
  Free Vertex 95: using x=[0.130020,0.047242,0.917753] -> mesh_pos=[0.130020,0.047242,0.882455]
  Free Vertex 96: using x=[0.109911,0.076348,0.909399] -> mesh_pos=[0.109911,0.076348,0.874422]
  Free Vertex 97: using x=[0.078962,0.098514,0.908693] -> mesh_pos=[0.078962,0.098514,0.873743]
  Free Vertex 98: using x=[0.064957,0.106866,0.925176] -> mesh_pos=[0.064957,0.106866,0.889592]
  Free Vertex 99: using x=[0.020775,0.119598,0.926603] -> mesh_pos=[0.020775,0.119598,0.890965]
  Free Vertex 100: using x=[-0.029338,0.118700,0.931670] -> mesh_pos=[-0.029338,0.118700,0.895837]
  Free Vertex 101: using x=[-0.093139,0.103932,0.939432] -> mesh_pos=[-0.093139,0.103932,0.903300]
  Free Vertex 102: using x=[-0.130293,0.059273,0.930757] -> mesh_pos=[-0.130293,0.059273,0.894958]
  Free Vertex 103: using x=[-0.158679,0.039674,0.914893] -> mesh_pos=[-0.158679,0.039674,0.879705]
  Free Vertex 104: using x=[-0.165396,0.014380,0.942063] -> mesh_pos=[-0.165396,0.014380,0.905830]
  Free Vertex 105: using x=[-0.143677,-0.015926,0.935016] -> mesh_pos=[-0.143677,-0.015926,0.899054]
  Free Vertex 106: using x=[-0.134460,-0.063118,0.909959] -> mesh_pos=[-0.134460,-0.063118,0.874961]
  Free Vertex 107: using x=[-0.119503,-0.079211,0.934431] -> mesh_pos=[-0.119503,-0.079211,0.898491]
  Free Vertex 108: using x=[-0.080556,-0.080114,0.951518] -> mesh_pos=[-0.080556,-0.080114,0.914921]
  Free Vertex 109: using x=[-0.062355,-0.090032,0.949186] -> mesh_pos=[-0.062355,-0.090032,0.912679]
  Free Vertex 110: using x=[-0.010129,-0.136537,0.912280] -> mesh_pos=[-0.010129,-0.136537,0.877193]
  Free Vertex 111: using x=[0.028474,-0.132273,0.913043] -> mesh_pos=[0.028474,-0.132273,0.877926]
  Free Vertex 112: using x=[0.065790,-0.117944,0.911963] -> mesh_pos=[0.065790,-0.117944,0.876888]
  Free Vertex 113: using x=[0.099224,-0.093909,0.908855] -> mesh_pos=[0.099224,-0.093909,0.873899]
  Free Vertex 114: using x=[0.125828,-0.061110,0.903629] -> mesh_pos=[0.125828,-0.061110,0.868874]
  Free Vertex 115: using x=[0.142463,-0.021345,0.896391] -> mesh_pos=[0.142463,-0.021345,0.861915]
  Free Vertex 116: using x=[0.145351,0.017657,0.890015] -> mesh_pos=[0.145351,0.017657,0.855783]
  Free Vertex 117: using x=[0.138229,0.050486,0.874583] -> mesh_pos=[0.138229,0.050486,0.840946]
  Free Vertex 118: using x=[0.115381,0.080544,0.873926] -> mesh_pos=[0.115381,0.080544,0.840314]
  Free Vertex 119: using x=[0.082618,0.104136,0.873587] -> mesh_pos=[0.082618,0.104136,0.839988]
  Free Vertex 120: using x=[0.065544,0.107784,0.880992] -> mesh_pos=[0.065544,0.107784,0.847107]
  Free Vertex 121: using x=[0.024532,0.126589,0.881709] -> mesh_pos=[0.024532,0.126589,0.847797]
  Free Vertex 122: using x=[-0.028108,0.121191,0.882634] -> mesh_pos=[-0.028108,0.121191,0.848687]
  Free Vertex 123: using x=[-0.100138,0.099957,0.880150] -> mesh_pos=[-0.100138,0.099957,0.846298]
  Free Vertex 124: using x=[-0.139075,0.075019,0.878593] -> mesh_pos=[-0.139075,0.075019,0.844801]
  Free Vertex 125: using x=[-0.161092,0.039916,0.881279] -> mesh_pos=[-0.161092,0.039916,0.847384]
  Free Vertex 126: using x=[-0.170957,0.003799,0.892764] -> mesh_pos=[-0.170957,0.003799,0.858427]
  Free Vertex 127: using x=[-0.153108,-0.023050,0.874100] -> mesh_pos=[-0.153108,-0.023050,0.840481]
  Free Vertex 128: using x=[-0.140502,-0.067142,0.874119] -> mesh_pos=[-0.140502,-0.067142,0.840500]
  Free Vertex 129: using x=[-0.116513,-0.105418,0.875063] -> mesh_pos=[-0.116513,-0.105418,0.841407]
  Free Vertex 130: using x=[-0.080422,-0.117215,0.908196] -> mesh_pos=[-0.080422,-0.117215,0.873265]
  Free Vertex 131: using x=[-0.048558,-0.130539,0.910127] -> mesh_pos=[-0.048558,-0.130539,0.875122]
  Free Vertex 132: using x=[-0.002073,-0.158779,0.870380] -> mesh_pos=[-0.002073,-0.158779,0.836904]
  Free Vertex 133: using x=[0.040026,-0.152366,0.871329] -> mesh_pos=[0.040026,-0.152366,0.837817]
  Free Vertex 134: using x=[0.079349,-0.134283,0.870438] -> mesh_pos=[0.079349,-0.134283,0.836959]
  Free Vertex 135: using x=[0.113015,-0.105520,0.867583] -> mesh_pos=[0.113015,-0.105520,0.834214]
  Free Vertex 136: using x=[0.138211,-0.067759,0.862817] -> mesh_pos=[0.138211,-0.067759,0.829631]
  Free Vertex 137: using x=[0.152484,-0.023507,0.856420] -> mesh_pos=[0.152484,-0.023507,0.823480]
  Free Vertex 138: using x=[0.153712,0.018236,0.850885] -> mesh_pos=[0.153712,0.018236,0.818159]
  Free Vertex 139: using x=[0.143921,0.052749,0.838305] -> mesh_pos=[0.143921,0.052749,0.806063]
  Free Vertex 140: using x=[0.119787,0.083959,0.838056] -> mesh_pos=[0.119787,0.083959,0.805823]
  Free Vertex 141: using x=[0.085901,0.108189,0.837908] -> mesh_pos=[0.085901,0.108189,0.805681]
  Free Vertex 142: using x=[0.066992,0.116353,0.846258] -> mesh_pos=[0.066992,0.116353,0.813710]
  Free Vertex 143: using x=[0.020904,0.134407,0.840764] -> mesh_pos=[0.020904,0.134407,0.808427]
  Free Vertex 144: using x=[-0.029658,0.121952,0.827877] -> mesh_pos=[-0.029658,0.121952,0.796035]
  Free Vertex 145: using x=[-0.098957,0.099823,0.825946] -> mesh_pos=[-0.098957,0.099823,0.794179]
  Free Vertex 146: using x=[-0.141939,0.076814,0.827870] -> mesh_pos=[-0.141939,0.076814,0.796029]
  Free Vertex 147: using x=[-0.160826,0.044345,0.845515] -> mesh_pos=[-0.160826,0.044345,0.812995]
  Free Vertex 148: using x=[-0.164571,0.008578,0.851837] -> mesh_pos=[-0.164571,0.008578,0.819074]
  Free Vertex 149: using x=[-0.158603,-0.024276,0.838099] -> mesh_pos=[-0.158603,-0.024276,0.805865]
  Free Vertex 150: using x=[-0.145664,-0.070797,0.838080] -> mesh_pos=[-0.145664,-0.070797,0.805846]
  Free Vertex 151: using x=[-0.120507,-0.111910,0.838256] -> mesh_pos=[-0.120507,-0.111910,0.806015]
  Free Vertex 152: using x=[-0.084513,-0.135120,0.864973] -> mesh_pos=[-0.084513,-0.135120,0.831705]
  Free Vertex 153: using x=[-0.044812,-0.153020,0.868012] -> mesh_pos=[-0.044812,-0.153020,0.834627]
  Pinned Vertex 154: using xconst=[-0.135840,0.078511,1.073723] -> mesh_pos=[-0.135840,0.078511,1.032426]
  Pinned Vertex 155: using xconst=[-0.122557,0.057010,1.073291] -> mesh_pos=[-0.122557,0.057010,1.032010]
  Pinned Vertex 156: using xconst=[-0.102748,0.040287,1.072660] -> mesh_pos=[-0.102748,0.040287,1.031404]
  Pinned Vertex 157: using xconst=[-0.077888,0.029702,1.071925] -> mesh_pos=[-0.077888,0.029702,1.030697]
  Pinned Vertex 158: using xconst=[-0.049934,0.026216,1.071190] -> mesh_pos=[-0.049934,0.026216,1.029991]
  Pinned Vertex 159: using xconst=[-0.021189,0.030219,1.070552] -> mesh_pos=[-0.021189,0.030219,1.029377]
  Pinned Vertex 160: using xconst=[-0.003175,0.037429,1.070089] -> mesh_pos=[-0.003175,0.037429,1.028932]
  Pinned Vertex 161: using xconst=[0.003483,0.046956,1.069822] -> mesh_pos=[0.003483,0.046956,1.028675]
  Pinned Vertex 162: using xconst=[0.007377,0.062741,1.069524] -> mesh_pos=[0.007377,0.062741,1.028388]
  Pinned Vertex 163: using xconst=[0.007414,0.083650,1.069335] -> mesh_pos=[0.007414,0.083650,1.028207]
  Pinned Vertex 164: using xconst=[0.003165,0.107671,1.069341] -> mesh_pos=[0.003165,0.107671,1.028212]
  Pinned Vertex 165: using xconst=[-0.005152,0.132561,1.069562] -> mesh_pos=[-0.005152,0.132561,1.028425]
  Pinned Vertex 166: using xconst=[-0.017211,0.156963,1.069994] -> mesh_pos=[-0.017211,0.156963,1.028840]
  Pinned Vertex 167: using xconst=[-0.031525,0.177812,1.070582] -> mesh_pos=[-0.031525,0.177812,1.029406]
  Pinned Vertex 168: using xconst=[-0.046626,0.193273,1.071252] -> mesh_pos=[-0.046626,0.193273,1.030050]
  Pinned Vertex 169: using xconst=[-0.060748,0.202154,1.071873] -> mesh_pos=[-0.060748,0.202154,1.030647]
  Pinned Vertex 170: using xconst=[-0.072236,0.204408,1.072286] -> mesh_pos=[-0.072236,0.204408,1.031044]
  Pinned Vertex 171: using xconst=[-0.089999,0.196810,1.072743] -> mesh_pos=[-0.089999,0.196810,1.031484]
  Pinned Vertex 172: using xconst=[-0.113039,0.179132,1.073204] -> mesh_pos=[-0.113039,0.179132,1.031927]
  Pinned Vertex 173: using xconst=[-0.130174,0.156472,1.073597] -> mesh_pos=[-0.130174,0.156472,1.032305]
  Pinned Vertex 174: using xconst=[-0.140057,0.130741,1.073857] -> mesh_pos=[-0.140057,0.130741,1.032555]
  Pinned Vertex 175: using xconst=[-0.141971,0.104045,1.073915] -> mesh_pos=[-0.141971,0.104045,1.032610]
  Free Vertex 176: using x=[-0.000153,-0.171575,0.830633] -> mesh_pos=[-0.000153,-0.171575,0.798686]
  Free Vertex 177: using x=[0.044422,-0.164432,0.831618] -> mesh_pos=[0.044422,-0.164432,0.799633]
  Free Vertex 178: using x=[0.085562,-0.144455,0.830861] -> mesh_pos=[0.085562,-0.144455,0.798905]
  Free Vertex 179: using x=[0.120200,-0.113004,0.828260] -> mesh_pos=[0.120200,-0.113004,0.796404]
  Free Vertex 180: using x=[0.145595,-0.072206,0.823906] -> mesh_pos=[0.145595,-0.072206,0.792217]
  Free Vertex 181: using x=[0.159565,-0.024952,0.818105] -> mesh_pos=[0.159565,-0.024952,0.786639]
  Free Vertex 182: using x=[0.160417,0.018835,0.813035] -> mesh_pos=[0.160417,0.018835,0.781765]
  Free Vertex 183: using x=[0.149492,0.054936,0.802006] -> mesh_pos=[0.149492,0.054936,0.771160]
  Free Vertex 184: using x=[0.124426,0.087186,0.801901] -> mesh_pos=[0.124426,0.087186,0.771059]
  Free Vertex 185: using x=[0.089218,0.112337,0.801823] -> mesh_pos=[0.089218,0.112337,0.770983]
  Free Vertex 186: using x=[0.048953,0.127227,0.806444] -> mesh_pos=[0.048953,0.127227,0.775427]
  Free Vertex 187: using x=[0.014076,0.131547,0.801856] -> mesh_pos=[0.014076,0.131547,0.771016]
  Free Vertex 188: using x=[-0.034276,0.125764,0.788509] -> mesh_pos=[-0.034276,0.125764,0.758182]
  Free Vertex 189: using x=[-0.081550,0.100704,0.789700] -> mesh_pos=[-0.081550,0.100704,0.759327]
  Free Vertex 190: using x=[-0.133829,0.085113,0.797383] -> mesh_pos=[-0.133829,0.085113,0.766714]
  Free Vertex 191: using x=[-0.159568,0.054895,0.803809] -> mesh_pos=[-0.159568,0.054895,0.772894]
  Free Vertex 192: using x=[-0.166450,0.019046,0.810135] -> mesh_pos=[-0.166450,0.019046,0.778976]
  Free Vertex 193: using x=[-0.163827,-0.024585,0.802015] -> mesh_pos=[-0.163827,-0.024585,0.771168]
  Free Vertex 194: using x=[-0.150797,-0.073460,0.801877] -> mesh_pos=[-0.150797,-0.073460,0.771035]
  Free Vertex 195: using x=[-0.125210,-0.116803,0.802031] -> mesh_pos=[-0.125210,-0.116803,0.771183]
  Free Vertex 196: using x=[-0.088299,-0.145078,0.824901] -> mesh_pos=[-0.088299,-0.145078,0.793174]
  Free Vertex 197: using x=[-0.045417,-0.165291,0.828320] -> mesh_pos=[-0.045417,-0.165291,0.796462]
UPBGE single frame mode: using pinned vertex constraint positions
UPBGE: forcing object update after cloth simulation
MOD_cloth: copying cloth results back to mesh positions
  MOD_cloth Vertex 0: pos=[-0.149112,0.084167,0.983174]
  MOD_cloth Vertex 1: pos=[-0.133908,0.061445,0.983757]
  MOD_cloth Vertex 2: pos=[-0.112016,0.044055,0.983999]
  MOD_cloth Vertex 3: pos=[-0.085014,0.033302,0.984051]
  MOD_cloth Vertex 4: pos=[-0.054865,0.030048,0.984196]
MOD_cloth: forced object data re-evaluation
Blender Game Engine Finished
