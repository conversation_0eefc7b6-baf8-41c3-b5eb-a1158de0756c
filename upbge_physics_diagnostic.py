#!/usr/bin/env python3
"""
UPBGE物理系统诊断脚本
用于检查物理世界状态和我们的函数是否正常工作
"""

def diagnose_physics_system():
    """诊断物理系统状态"""
    
    print("=== UPBGE 物理系统诊断 ===\n")
    
    # 1. 检查基本模块
    print("1. 检查基本模块:")
    try:
        import bpy
        print("   ✓ bpy模块可用")
        
        import _bpy
        print("   ✓ _bpy模块可用")
        
        import bge
        print("   ✓ bge模块可用")
        
    except ImportError as e:
        print(f"   ✗ 模块导入失败: {e}")
        return
    
    # 2. 检查当前场景
    print("\n2. 检查当前场景:")
    try:
        scene = bpy.context.scene
        print(f"   ✓ 当前场景: {scene.name}")
        
        # 检查刚体世界
        rbw = scene.rigidbody_world
        if rbw:
            print(f"   ✓ 刚体世界存在")
            print(f"     - 时间缩放: {rbw.time_scale}")
            print(f"     - 子步数: {rbw.substeps_per_frame}")
            print(f"     - 对象组: {rbw.group.name if rbw.group else 'None'}")
            print(f"     - 约束组: {rbw.constraints.name if rbw.constraints else 'None'}")
            
            # 检查物理世界状态
            if hasattr(rbw, 'shared') and rbw.shared:
                print(f"     - 共享数据存在: ✓")
                if hasattr(rbw.shared, 'runtime') and rbw.shared.runtime:
                    print(f"     - 运行时数据存在: ✓")
                    if hasattr(rbw.shared.runtime, 'physics_world') and rbw.shared.runtime.physics_world:
                        print(f"     - 物理世界存在: ✓")
                    else:
                        print(f"     - 物理世界存在: ✗")
                else:
                    print(f"     - 运行时数据存在: ✗")
            else:
                print(f"     - 共享数据存在: ✗")
        else:
            print(f"   ✗ 刚体世界不存在")
            
    except Exception as e:
        print(f"   ✗ 场景检查失败: {e}")
    
    # 3. 检查刚体对象
    print("\n3. 检查刚体对象:")
    try:
        rigidbody_objects = []
        for obj in bpy.context.scene.objects:
            if obj.rigidbody_object:
                rigidbody_objects.append(obj)
        
        print(f"   找到 {len(rigidbody_objects)} 个刚体对象:")
        for obj in rigidbody_objects:
            rbo = obj.rigidbody_object
            print(f"     - {obj.name}: 类型={rbo.type}, 质量={rbo.mass}")
            
    except Exception as e:
        print(f"   ✗ 刚体对象检查失败: {e}")
    
    # 4. 测试我们的函数
    print("\n4. 测试我们的函数:")
    try:
        # 检查函数是否存在
        if hasattr(_bpy, 'update_for_skeletal_animation'):
            print("   ✓ update_for_skeletal_animation 函数存在")
            
            # 尝试调用
            try:
                result = _bpy.update_for_skeletal_animation()
                print(f"   ✓ 函数调用成功，返回: {result}")
            except Exception as e:
                print(f"   ✗ 函数调用失败: {e}")
                
        else:
            print("   ✗ update_for_skeletal_animation 函数不存在")
        
        if hasattr(_bpy, 'update_fallback'):
            print("   ✓ update_fallback 函数存在")
            
            # 尝试调用
            try:
                result = _bpy.update_fallback()
                print(f"   ✓ 备用函数调用成功，返回: {result}")
            except Exception as e:
                print(f"   ✗ 备用函数调用失败: {e}")
                
        else:
            print("   ✗ update_fallback 函数不存在")
            
    except Exception as e:
        print(f"   ✗ 函数测试失败: {e}")

def create_test_rigidbody_world():
    """创建测试用的刚体世界"""
    
    print("\n=== 创建测试刚体世界 ===")
    
    try:
        import bpy
        
        scene = bpy.context.scene
        
        # 如果没有刚体世界，创建一个
        if not scene.rigidbody_world:
            print("创建新的刚体世界...")
            bpy.ops.rigidbody.world_add()
            print("✓ 刚体世界创建成功")
        else:
            print("✓ 刚体世界已存在")
        
        # 创建一个测试立方体
        if "TestCube" not in bpy.data.objects:
            print("创建测试立方体...")
            bpy.ops.mesh.primitive_cube_add(location=(0, 0, 5))
            cube = bpy.context.active_object
            cube.name = "TestCube"
            
            # 添加刚体属性
            bpy.ops.rigidbody.object_add(type='ACTIVE')
            print("✓ 测试立方体创建成功")
        else:
            print("✓ 测试立方体已存在")
            
        # 创建地面
        if "Ground" not in bpy.data.objects:
            print("创建地面...")
            bpy.ops.mesh.primitive_plane_add(size=10, location=(0, 0, 0))
            ground = bpy.context.active_object
            ground.name = "Ground"
            
            # 添加被动刚体属性
            bpy.ops.rigidbody.object_add(type='PASSIVE')
            print("✓ 地面创建成功")
        else:
            print("✓ 地面已存在")
            
        return True
        
    except Exception as e:
        print(f"✗ 创建测试环境失败: {e}")
        return False

def test_physics_update():
    """测试物理更新功能"""
    
    print("\n=== 测试物理更新功能 ===")
    
    try:
        import _bpy
        import bpy
        
        scene = bpy.context.scene
        
        # 确保有刚体世界
        if not scene.rigidbody_world:
            print("没有刚体世界，无法测试")
            return False
        
        print("测试前状态:")
        if "TestCube" in bpy.data.objects:
            cube = bpy.data.objects["TestCube"]
            print(f"  立方体位置: {cube.location}")
        
        # 调用我们的物理更新函数
        print("调用物理更新函数...")
        try:
            _bpy.update_for_skeletal_animation()
            print("✓ 主要函数调用成功")
        except Exception as e:
            print(f"✗ 主要函数调用失败: {e}")
            try:
                _bpy.update_fallback()
                print("✓ 备用函数调用成功")
            except Exception as e2:
                print(f"✗ 备用函数也失败: {e2}")
                return False
        
        print("测试后状态:")
        if "TestCube" in bpy.data.objects:
            cube = bpy.data.objects["TestCube"]
            print(f"  立方体位置: {cube.location}")
        
        return True
        
    except Exception as e:
        print(f"✗ 物理更新测试失败: {e}")
        return False

def main():
    """主函数"""
    
    # 运行诊断
    diagnose_physics_system()
    
    # 创建测试环境
    if create_test_rigidbody_world():
        # 重新诊断
        print("\n" + "="*50)
        diagnose_physics_system()
        
        # 测试物理更新
        test_physics_update()
    
    print("\n=== 诊断完成 ===")
    print("如果物理世界不存在或函数调用失败，可能需要:")
    print("1. 在场景中手动添加刚体世界 (Properties > Physics Properties > Rigid Body World)")
    print("2. 确保场景中有刚体对象")
    print("3. 检查UPBGE是否正确编译了我们的修改")

if __name__ == "__main__":
    main() 