/*
    Copyright (c) 2008-2009 NetAllied Systems GmbH

    This file is part of COLLADAFramework.

    Licensed under the MIT Open Source License,
    for details please see LICENSE file or the website
    http://www.opensource.org/licenses/mit-license.php
*/

#ifndef __COLLADABU_PREREQUISITES_H__
#define __COLLADABU_PREREQUISITES_H__

#include "COLLADABUPlatform.h"

#include <string>
#include <string.h>

namespace COLLADABU
{
    typedef std::string String;
    typedef std::wstring WideString;
}

#endif //__COLLADABU_PREREQUISITES_H__
