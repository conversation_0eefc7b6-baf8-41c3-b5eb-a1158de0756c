/*
    Copyright (c) 2008-2009 NetAllied Systems GmbH

	This file is part of COLLADABaseUtils.

    Licensed under the MIT Open Source License,
    for details please see LICENSE file or the website
    http://www.opensource.org/licenses/mit-license.php
*/

#ifndef __COLLADABU_STABLE_HEADERS_H__
#define __COLLADABU_STABLE_HEADERS_H__

#include "COLLADABUPrerequisites.h"

//STL
#include <vector>
#include <sstream>
#include <algorithm>
#include <cmath>
#include <string>
#include <iostream>

#ifdef COLLADABU_OS_WIN
#include <direct.h>
#else
#include <unistd.h>
#include <sys/stat.h>
#include <sys/types.h>
#endif



#endif //__COLLADABU_STABLE_HEADERS_H__
