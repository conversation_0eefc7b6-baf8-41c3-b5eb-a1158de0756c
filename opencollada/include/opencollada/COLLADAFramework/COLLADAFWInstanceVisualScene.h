/*
    Copyright (c) 2008-2009 NetAllied Systems GmbH

    This file is part of COLLADAFramework.

    Licensed under the MIT Open Source License, 
    for details please see LICENSE file or the website
    http://www.opensource.org/licenses/mit-license.php
*/

#ifndef __COLLADAFW_INSTANCEVISUALSCENE_H__
#define __COLLADAFW_INSTANCEVISUALSCENE_H__

#include "COLLADAFWPrerequisites.h"
#include "COLLADAFWInstanceBase.h"


namespace COLLADAFW
{

    /** Instantiates a node within a scene graph.*/
    typedef InstanceBase<COLLADA_TYPE::INSTANCE_VISUAL_SCENE> InstanceVisualScene;


} // namespace COLLADAFW

#endif // __COLLADAFW_INSTANCEVISUALSCENE_H__
