/*
    Copyright (c) 2008-2009 NetAllied Systems GmbH

	This file is part of COLLADAFramework.

    Licensed under the MIT Open Source License,
    for details please see LICENSE file or the website
    http://www.opensource.org/licenses/mit-license.php
*/

#ifndef __COLLADAFW_STABLE_HEADERS_H__
#define __COLLADAFW_STABLE_HEADERS_H__

//STL
#include <vector>
#include <sstream>
#include <algorithm>
#include <math.h>
#include <string>
#include <iostream>
#include <map>
#include <list>
#include <set>
#include <cassert>


// base utils headers
#include "COLLADABU.h"


#endif //__COLLADAFW_STABLE_HEADERS_H__
