!<arch>
/               -1                      0       134415    `
  � � � 2 -� -� J� J� Lj Lj � � 贅 贅 钖 钖 �  �  K, K, K� K� J
 J
 J� J� 1� 1� 涡 涡 i� i� jL jL 营 营 訬 訬 � � 轍 轍 铊 铊 砥 砥 頥 頥 � � 鸫 鸫 �  �  H H 牙 牙   � � 2 2 � �   � � 	( 	( 
f 
f � � l l ~ ~ � � � � � � < < : :     � � : : 
� 
� � � � � R R � �   2 2 J J 
x 
x $� $� #� #� %� %� !Z !Z "� "� ' ' (T (T '� '� *@ *@ (� (� )� )� #> #> !� !� &j &j %$ %$ � � 赦 赦 ;� ;� 祸 祸 {6 {6   �& �& 畖 畖   � � _� _� `� `� c� c� B� B� a> a> a� a� C� C� *� *� Hf Hf I� I� H� H� 斣 斣 時 時 �< �< C. C. B� B� 坐 坐 貏 貏 $� $� $R $R #� #� DT DT C� C� &x &x %� %� %f %f Ex Ex D� D� B B A~ A~ 蚡 蚡 4� 4� =� =� 濰 濰 涗 涗 湴 湴 潀 潀 � � �* �* 棎 棎 榥 榥 橪 橪 柌 柌 2� 2� -N -N .  .  .� .� 1� 1� 0� 0� /� /� ,^ ,^ &� &� '� '� (� (� +v +v *� *� )f )f _` _` }� }� |� |� ~� ~� j� j� k� k� g( g( h� h� d� d� l� l� h h i� i� f f m� m� z� z� T� T� u  u  s< s< t t r r u� u� n� n� {� {� U� U� : : o~ o~ 襩 襩 � � � � 衊 衊 蝠 蝠 �& �& 罄 罄 鱎 鱎 鰟 鰟 �6 �6       鷃 鷃 鶔 鶔 � � �� ��      �  � B B   魾 魾 箐 箐 7 7 挽 挽 臽 臽 7� 7� 8� 8� 篇 篇 荭 荭 夫 夫 稰 稰 �: �: 塟 塟 � � � � J J � �   Ζ Ζ � � � � 穴 穴 懶 懶 捀 捀   蟦 蟦 析 析 ,� ,� 溪 溪 -h -h M M O� O� :� :� 禚 禚     < < 醤 醤 � � �  �  .� .� K@ K@ O8 O8 Wn Wn X X 悸 悸 ( ( '� '� &� &� F� F� F F 荝 荝 鍴 鍴 � � 挂 挂 籂 籂 籸 籸 � � �0 �0 : : ;: ;: :� :� e� e� �� �� 龕 龕   OV OV � � @� @� ?� ?� 逗 逗 塌 塌 �. �. 謏 謏   -B -B 9� 9� ,� ,� +� +� A( A( 濡 濡 � � s. s. 6� 6� 4� 4� 5� 5� │ │ f\ f\ 3` 3` �  �  曛 曛 � � �6 �6 嶒 嶒 +l +l J J �  盚 � t� 阈 趭 `� 诇 q� >� >6 亴 �� Dn C� s� 揶 L@ 杈 � �" �: c� ]R `: b� 痔 譩 *� 2^ pZ  T 鏧 �4 Th @d 馤 � �0 Г 亵 鰾 � A� 皲 � 厝  �$ �0 g�  � 尷 � 飩 � gH e$ 嗵 Z F� 竴 L� � 瞶 �
 ￢ =� 薾 陰  .� r  �* � 礈 獢 <n = 琤  篰 � 
x  � > 閄 /� 轲 0� 峍 錠 屦 煾 ?^ �> 嘍 �4 儂 肼 靄 i hx 刖 /N +t .  � �, 瓥 4� r� d� v� 賌 a� WL dF wt x� \� S� R
 P� Qr 絧 摖 , W� @� M� 祅 祅 a` S> ^� R� � p� �b c V� \& x
 y� y8 鉚 鉚 鈽 鈽 � � 錅 錅 � � 3� 3� = = 跄 跄 �( �( 魥 魥 駄 駄 騴 騴 魻 魻  l  l � � 葮 葮 窽 窽 J J   编 编 T� T� 啃 啃 鄱 鄱 p p   � � � � v� v� u� u� � � � � �, �,   /� /� � � � � � � T T � � l l � � 瞑 瞑 7 7 禦 禦 﨎 﨎 ╊ ╊   4 4 �( �( %� %� !� !� "� "� 9J 9J IL IL   鷟 鷟 燹 燹 $� $� #� #� � � 4& 4&   ≈ ≈ � � 燫 燫 �, �, � � � � )� )� 8� 8� ), ), (� (� G� G� G4 G4 !� !� 剮 剮  �  � � � ?� ?� ?> ?> 7� 7� � � 噁 噁 喥 喥 兢 兢 儢 儢 � � � � > > ! !  �  � >� >� #, #, �" �" ㄐ ㄐ 繴 繴 蚌 蚌 m� m� l� l� mF mF 髫 髫   關 關 厐 厐 � � 8@ 8@ "� "� " " @� @� @\ @\ 鉃 鉃 醔 醔 :R :R   *D *D 9H 9H 3F 3F <~ <~ 1  1  q` q` j� j� � � �< �< 坂 坂 诎 诎 芤 芤 跠 跠 輰 輰 X� X� � � 则 则 p� p� 霷 霷 Y� Y� 飗 飗 UB UB 鰻 鰻 埨 埨 2� 2� 嗥 嗥 珙 珙 4( 4( 鹉 鹉 tH tH 镐 镐 Ej Ej �. �. 鎶 鎶   ]� ]� o� o� 擂 擂 眫 眫 BP BP 翀 翀 � � 胘 胘 巢 巢 G~ G~ 暮 暮 蹿 蹿 _< _< cB cB ^ ^ ^� ^� 绿 绿 �( �( 忯 忯 �4 �4 幖 幖 廣 廣 諛 諛 悗 悗 鋒 鋒 撯 撯 攔 攔 � � 晼 晼 � � 碆 碆 ;� ;� b� b� 助 助 �, �, 矠 矠 V� V� V$ V$ P
 P
 S$ S$ S� S� t t � � � � � �  �  � 	� 	� H� H� 楠 楠 \ \ < < P� P� R~ R~ N� N� Mn Mn Q� Q� 权 权 �< �< 莗 莗 � � �  �  脼 脼 舲 舲 � � 聊 聊 耣 耣 内 内 �$ �$ 纻 纻 �0 �0 蒁 蒁 誊 誊 蛜 蛜 屎 屎 � � 欣 欣 N N n� n� ]p ]p \� \� [� [� \( \( D" D" 鴢 鴢   �0 �0 ?� ?� o6 o6 贫 贫 9� 9� 7� 7� ;  ;  =V =V <� <� :x :x 8� 8� 94 94 6t 6t 艰 艰 皆 皆 忽 忽 >� >� D� D� 韭 韭 5� 5� xB xB w� w� �
 �
 |$ |$ z: z: =� =�   皒 皒 Z� Z� 躧 躧 � � ~� ~� � � � � 梔 梔 �( �( 柶 柶 懘 懘 囓 囓 「 「   挒 挒   澆 澆 X� X� Yl Yl ZT ZT [< [< y� y� Ⅱ Ⅱ 殀 殀 �> �> 仩 仩 槧 槧 妀 妀 孿 孿 啚 啚 嫸 嫸 ~R ~R }� }� � � 咊 咊 � � } } 浉 浉 の の 烖 烖 煔 煔 � � 瀂 瀂   x� x� 嶵 嶵 劥 劥 � � 壢 壢 傏 傏 欆 欆 �( �( 揃 揃 垐 垐 � � 匳 匳 崿 崿 B& B& 偩 偩 縥 縥   あ あ k� k� 選 選 	� 	� 
0 
0 b b zd zd 琤 琤   � � \ \ 彣 彣 � � 怷 怷 �8 �8 狓 狓 淰 淰 �� �� �D �D 掼 掼 邔 邔 ;� ;� 2� 2� 22 22 K� K� > > 6 6 5d 5d PB PB ;h ;h 0� 0� 0* 0* 76 76 蓯 蓯 虗 虗 �, �, 蔨 蔨 招 招 嫗 嫗 B� B� N� N( A� A� 6� 6� 骛 骛 缰 缰 1` 1` 襀 襀 灶 灶 � � �( �( .Z .Z �, �, 范 范__IMPORT_DESCRIPTOR_oslexec __NULL_IMPORT_DESCRIPTOR oslexec_NULL_THUNK_DATA ??0PerThreadInfo@LLVM_Util@pvt@v1_14_4@OSL@@QEAA@XZ __imp_??0PerThreadInfo@LLVM_Util@pvt@v1_14_4@OSL@@QEAA@XZ ??4PerThreadInfo@LLVM_Util@pvt@v1_14_4@OSL@@QEAAAEAU01234@AEBU01234@@Z __imp_??4PerThreadInfo@LLVM_Util@pvt@v1_14_4@OSL@@QEAAAEAU01234@AEBU01234@@Z ??4ScopedJitMemoryUser@LLVM_Util@pvt@v1_14_4@OSL@@QEAAAEAU01234@AEBU01234@@Z __imp_??4ScopedJitMemoryUser@LLVM_Util@pvt@v1_14_4@OSL@@QEAAAEAU01234@AEBU01234@@Z ?debug@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXH@Z __imp_?debug@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXH@Z ?debug@LLVM_Util@pvt@v1_14_4@OSL@@QEBAHXZ __imp_?debug@LLVM_Util@pvt@v1_14_4@OSL@@QEBAHXZ ?dumpasm@LLVM_Util@pvt@v1_14_4@OSL@@QEAAX_N@Z __imp_?dumpasm@LLVM_Util@pvt@v1_14_4@OSL@@QEAAX_N@Z ?dumpasm@LLVM_Util@pvt@v1_14_4@OSL@@QEBA_NXZ __imp_?dumpasm@LLVM_Util@pvt@v1_14_4@OSL@@QEBA_NXZ ?jit_fma@LLVM_Util@pvt@v1_14_4@OSL@@QEAAX_N@Z __imp_?jit_fma@LLVM_Util@pvt@v1_14_4@OSL@@QEAAX_N@Z ?jit_fma@LLVM_Util@pvt@v1_14_4@OSL@@QEBA_NXZ __imp_?jit_fma@LLVM_Util@pvt@v1_14_4@OSL@@QEBA_NXZ ?jit_aggressive@LLVM_Util@pvt@v1_14_4@OSL@@QEAAX_N@Z __imp_?jit_aggressive@LLVM_Util@pvt@v1_14_4@OSL@@QEAAX_N@Z ?jit_aggressive@LLVM_Util@pvt@v1_14_4@OSL@@QEBA_NXZ __imp_?jit_aggressive@LLVM_Util@pvt@v1_14_4@OSL@@QEBA_NXZ ?ustring_rep@LLVM_Util@pvt@v1_14_4@OSL@@QEBA?AW4UstringRep@1234@XZ __imp_?ustring_rep@LLVM_Util@pvt@v1_14_4@OSL@@QEBA?AW4UstringRep@1234@XZ ?context@LLVM_Util@pvt@v1_14_4@OSL@@QEBAAEAVLLVMContext@llvm@@XZ __imp_?context@LLVM_Util@pvt@v1_14_4@OSL@@QEBAAEAVLLVMContext@llvm@@XZ ?module@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVModule@llvm@@XZ __imp_?module@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVModule@llvm@@XZ ?module@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVModule@llvm@@@Z __imp_?module@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVModule@llvm@@@Z ?current_function@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVFunction@llvm@@@Z __imp_?current_function@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVFunction@llvm@@@Z ?current_function@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVFunction@llvm@@XZ __imp_?current_function@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVFunction@llvm@@XZ ?target_isa@LLVM_Util@pvt@v1_14_4@OSL@@QEBA?AW4TargetISA@234@XZ __imp_?target_isa@LLVM_Util@pvt@v1_14_4@OSL@@QEBA?AW4TargetISA@234@XZ ?set_target_isa@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXW4TargetISA@234@@Z __imp_?set_target_isa@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXW4TargetISA@234@@Z ?supports_avx@LLVM_Util@pvt@v1_14_4@OSL@@QEBA_NXZ __imp_?supports_avx@LLVM_Util@pvt@v1_14_4@OSL@@QEBA_NXZ ?supports_avx2@LLVM_Util@pvt@v1_14_4@OSL@@QEBA_NXZ __imp_?supports_avx2@LLVM_Util@pvt@v1_14_4@OSL@@QEBA_NXZ ?supports_avx512f@LLVM_Util@pvt@v1_14_4@OSL@@QEBA_NXZ __imp_?supports_avx512f@LLVM_Util@pvt@v1_14_4@OSL@@QEBA_NXZ ?supports_llvm_bit_masks_natively@LLVM_Util@pvt@v1_14_4@OSL@@QEBA_NXZ __imp_?supports_llvm_bit_masks_natively@LLVM_Util@pvt@v1_14_4@OSL@@QEBA_NXZ ?supports_masked_stores@LLVM_Util@pvt@v1_14_4@OSL@@QEBA_NXZ __imp_?supports_masked_stores@LLVM_Util@pvt@v1_14_4@OSL@@QEBA_NXZ ?execengine@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVExecutionEngine@llvm@@XZ __imp_?execengine@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVExecutionEngine@llvm@@XZ ?is_masking_required@LLVM_Util@pvt@v1_14_4@OSL@@QEBA_NXZ __imp_?is_masking_required@LLVM_Util@pvt@v1_14_4@OSL@@QEBA_NXZ ?create_masking_scope@LLVM_Util@pvt@v1_14_4@OSL@@QEAA?AUScopedMasking@1234@_N@Z __imp_?create_masking_scope@LLVM_Util@pvt@v1_14_4@OSL@@QEAA?AUScopedMasking@1234@_N@Z ?type_float@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ __imp_?type_float@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ ?type_double@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ __imp_?type_double@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ ?type_int@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ __imp_?type_int@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ ?type_int8@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ __imp_?type_int8@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ ?type_int16@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ __imp_?type_int16@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ ?type_int64@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ __imp_?type_int64@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ ?type_addrint@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ __imp_?type_addrint@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ ?type_bool@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ __imp_?type_bool@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ ?type_char@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ __imp_?type_char@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ ?type_longlong@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ __imp_?type_longlong@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ ?type_void@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ __imp_?type_void@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ ?type_triple@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ __imp_?type_triple@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ ?type_matrix@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ __imp_?type_matrix@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ ?type_typedesc@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ __imp_?type_typedesc@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ ?type_ustring@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVType@llvm@@XZ __imp_?type_ustring@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVType@llvm@@XZ ?type_real_ustring@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVType@llvm@@XZ __imp_?type_real_ustring@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVType@llvm@@XZ ?type_void_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVPointerType@llvm@@XZ __imp_?type_void_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVPointerType@llvm@@XZ ?type_ustring_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVPointerType@llvm@@XZ __imp_?type_ustring_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVPointerType@llvm@@XZ ?type_char_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVPointerType@llvm@@XZ __imp_?type_char_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVPointerType@llvm@@XZ ?type_bool_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVPointerType@llvm@@XZ __imp_?type_bool_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVPointerType@llvm@@XZ ?type_int_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVPointerType@llvm@@XZ __imp_?type_int_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVPointerType@llvm@@XZ ?type_int8_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVPointerType@llvm@@XZ __imp_?type_int8_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVPointerType@llvm@@XZ ?type_int64_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVPointerType@llvm@@XZ __imp_?type_int64_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVPointerType@llvm@@XZ ?type_float_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVPointerType@llvm@@XZ __imp_?type_float_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVPointerType@llvm@@XZ ?type_longlong_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVPointerType@llvm@@XZ __imp_?type_longlong_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVPointerType@llvm@@XZ ?type_triple_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVPointerType@llvm@@XZ __imp_?type_triple_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVPointerType@llvm@@XZ ?type_matrix_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVPointerType@llvm@@XZ __imp_?type_matrix_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVPointerType@llvm@@XZ ?type_double_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVPointerType@llvm@@XZ __imp_?type_double_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVPointerType@llvm@@XZ ?type_wide_float@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ __imp_?type_wide_float@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ ?type_wide_double@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ __imp_?type_wide_double@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ ?type_wide_int@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ __imp_?type_wide_int@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ ?type_wide_bool@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ __imp_?type_wide_bool@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ ?type_wide_char@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ __imp_?type_wide_char@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ ?type_wide_longlong@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ __imp_?type_wide_longlong@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ ?type_wide_triple@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ __imp_?type_wide_triple@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ ?type_wide_matrix@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ __imp_?type_wide_matrix@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ ?type_wide_void_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ __imp_?type_wide_void_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ ?type_wide_ustring@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ __imp_?type_wide_ustring@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ ?type_wide_ustring_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVPointerType@llvm@@XZ __imp_?type_wide_ustring_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVPointerType@llvm@@XZ ?type_wide_char_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVPointerType@llvm@@XZ __imp_?type_wide_char_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVPointerType@llvm@@XZ ?type_wide_bool_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVPointerType@llvm@@XZ __imp_?type_wide_bool_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVPointerType@llvm@@XZ ?type_wide_int_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVPointerType@llvm@@XZ __imp_?type_wide_int_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVPointerType@llvm@@XZ ?type_wide_float_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVPointerType@llvm@@XZ __imp_?type_wide_float_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVPointerType@llvm@@XZ ?type_native_mask@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ __imp_?type_native_mask@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ ?constant@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@@Z __imp_?constant@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@@Z ?wide_constant@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@@Z __imp_?wide_constant@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@@Z ?ptr_cast@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@PEAVPointerType@6@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z __imp_?ptr_cast@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@PEAVPointerType@6@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z ?op_alloca@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAVPointerType@6@HAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@H@Z __imp_?op_alloca@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAVPointerType@6@HAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@H@Z ?call_function@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEBDPEAV56@@Z __imp_?call_function@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEBDPEAV56@@Z ?call_function@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEBDPEAV56@1@Z __imp_?call_function@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEBDPEAV56@1@Z ?call_function@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEBDPEAV56@11@Z __imp_?call_function@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEBDPEAV56@11@Z ?call_function@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEBDPEAV56@111@Z __imp_?call_function@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEBDPEAV56@111@Z ?set_masking_required@LLVM_Util@pvt@v1_14_4@OSL@@AEAAX_N@Z __imp_?set_masking_required@LLVM_Util@pvt@v1_14_4@OSL@@AEAAX_N@Z ?masked_function_context@LLVM_Util@pvt@v1_14_4@OSL@@AEAAAEAUMaskedSubroutineContext@1234@XZ __imp_?masked_function_context@LLVM_Util@pvt@v1_14_4@OSL@@AEAAAEAUMaskedSubroutineContext@1234@XZ ?masked_function_context@LLVM_Util@pvt@v1_14_4@OSL@@AEBAAEBUMaskedSubroutineContext@1234@XZ __imp_?masked_function_context@LLVM_Util@pvt@v1_14_4@OSL@@AEBAAEBUMaskedSubroutineContext@1234@XZ ?masked_shader_context@LLVM_Util@pvt@v1_14_4@OSL@@AEAAAEAUMaskedSubroutineContext@1234@XZ __imp_?masked_shader_context@LLVM_Util@pvt@v1_14_4@OSL@@AEAAAEAUMaskedSubroutineContext@1234@XZ ?inside_of_inlined_masked_function_call@LLVM_Util@pvt@v1_14_4@OSL@@AEBA_NXZ __imp_?inside_of_inlined_masked_function_call@LLVM_Util@pvt@v1_14_4@OSL@@AEBA_NXZ ?masked_loop_context@LLVM_Util@pvt@v1_14_4@OSL@@AEAAAEAUMaskedLoopContext@1234@XZ __imp_?masked_loop_context@LLVM_Util@pvt@v1_14_4@OSL@@AEAAAEAUMaskedLoopContext@1234@XZ ?masked_loop_context@LLVM_Util@pvt@v1_14_4@OSL@@AEBAAEBUMaskedLoopContext@1234@XZ __imp_?masked_loop_context@LLVM_Util@pvt@v1_14_4@OSL@@AEBAAEBUMaskedLoopContext@1234@XZ ?inside_of_masked_loop@LLVM_Util@pvt@v1_14_4@OSL@@AEBA_NXZ __imp_?inside_of_masked_loop@LLVM_Util@pvt@v1_14_4@OSL@@AEBA_NXZ ??0LLVM_Util@pvt@v1_14_4@OSL@@QEAA@AEBV0123@@Z __imp_??0LLVM_Util@pvt@v1_14_4@OSL@@QEAA@AEBV0123@@Z ??4LLVM_Util@pvt@v1_14_4@OSL@@QEAAAEAV0123@AEBV0123@@Z __imp_??4LLVM_Util@pvt@v1_14_4@OSL@@QEAAAEAV0123@AEBV0123@@Z ??4Labels@v1_14_4@OSL@@QEAAAEAV012@AEBV012@@Z __imp_??4Labels@v1_14_4@OSL@@QEAAAEAV012@AEBV012@@Z ??4Labels@v1_14_4@OSL@@QEAAAEAV012@$$QEAV012@@Z __imp_??4Labels@v1_14_4@OSL@@QEAAAEAV012@$$QEAV012@@Z ?as_comp@ClosureColor@v1_14_4@OSL@@QEBAPEBUClosureComponent@23@XZ __imp_?as_comp@ClosureColor@v1_14_4@OSL@@QEBAPEBUClosureComponent@23@XZ ?as_mul@ClosureColor@v1_14_4@OSL@@QEBAPEBUClosureMul@23@XZ __imp_?as_mul@ClosureColor@v1_14_4@OSL@@QEBAPEBUClosureMul@23@XZ ?as_add@ClosureColor@v1_14_4@OSL@@QEBAPEBUClosureAdd@23@XZ __imp_?as_add@ClosureColor@v1_14_4@OSL@@QEBAPEBUClosureAdd@23@XZ ??4ClosureColor@v1_14_4@OSL@@QEAAAEAU012@AEBU012@@Z __imp_??4ClosureColor@v1_14_4@OSL@@QEAAAEAU012@AEBU012@@Z ??4ClosureColor@v1_14_4@OSL@@QEAAAEAU012@$$QEAU012@@Z __imp_??4ClosureColor@v1_14_4@OSL@@QEAAAEAU012@$$QEAU012@@Z ?data@ClosureComponent@v1_14_4@OSL@@QEAAPEAXXZ __imp_?data@ClosureComponent@v1_14_4@OSL@@QEAAPEAXXZ ?data@ClosureComponent@v1_14_4@OSL@@QEBAPEBXXZ __imp_?data@ClosureComponent@v1_14_4@OSL@@QEBAPEBXXZ ??0ClosureComponent@v1_14_4@OSL@@QEAA@XZ __imp_??0ClosureComponent@v1_14_4@OSL@@QEAA@XZ ??0ClosureComponent@v1_14_4@OSL@@QEAA@AEBU012@@Z __imp_??0ClosureComponent@v1_14_4@OSL@@QEAA@AEBU012@@Z ??0ClosureComponent@v1_14_4@OSL@@QEAA@$$QEAU012@@Z __imp_??0ClosureComponent@v1_14_4@OSL@@QEAA@$$QEAU012@@Z ??4ClosureComponent@v1_14_4@OSL@@QEAAAEAU012@AEBU012@@Z __imp_??4ClosureComponent@v1_14_4@OSL@@QEAAAEAU012@AEBU012@@Z ??4ClosureComponent@v1_14_4@OSL@@QEAAAEAU012@$$QEAU012@@Z __imp_??4ClosureComponent@v1_14_4@OSL@@QEAAAEAU012@$$QEAU012@@Z ??0ClosureMul@v1_14_4@OSL@@QEAA@XZ __imp_??0ClosureMul@v1_14_4@OSL@@QEAA@XZ ??0ClosureMul@v1_14_4@OSL@@QEAA@AEBU012@@Z __imp_??0ClosureMul@v1_14_4@OSL@@QEAA@AEBU012@@Z ??0ClosureMul@v1_14_4@OSL@@QEAA@$$QEAU012@@Z __imp_??0ClosureMul@v1_14_4@OSL@@QEAA@$$QEAU012@@Z ??4ClosureMul@v1_14_4@OSL@@QEAAAEAU012@AEBU012@@Z __imp_??4ClosureMul@v1_14_4@OSL@@QEAAAEAU012@AEBU012@@Z ??4ClosureMul@v1_14_4@OSL@@QEAAAEAU012@$$QEAU012@@Z __imp_??4ClosureMul@v1_14_4@OSL@@QEAAAEAU012@$$QEAU012@@Z ??4ClosureAdd@v1_14_4@OSL@@QEAAAEAU012@AEBU012@@Z __imp_??4ClosureAdd@v1_14_4@OSL@@QEAAAEAU012@AEBU012@@Z ??4ClosureAdd@v1_14_4@OSL@@QEAAAEAU012@$$QEAU012@@Z __imp_??4ClosureAdd@v1_14_4@OSL@@QEAAAEAU012@$$QEAU012@@Z ?register_JIT_Global@v1_14_4@OSL@@YAXPEBDPEAX@Z __imp_?register_JIT_Global@v1_14_4@OSL@@YAXPEBDPEAX@Z ??0ShadingSystem@v1_14_4@OSL@@QEAA@PEAVRendererServices@12@PEAVTextureSystem@OpenImageIO_v3_0@@PEAVErrorHandler@5@@Z __imp_??0ShadingSystem@v1_14_4@OSL@@QEAA@PEAVRendererServices@12@PEAVTextureSystem@OpenImageIO_v3_0@@PEAVErrorHandler@5@@Z ??1ShadingSystem@v1_14_4@OSL@@QEAA@XZ __imp_??1ShadingSystem@v1_14_4@OSL@@QEAA@XZ ?attribute@ShadingSystem@v1_14_4@OSL@@QEAA_NV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@UTypeDesc@5@PEBX@Z __imp_?attribute@ShadingSystem@v1_14_4@OSL@@QEAA_NV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@UTypeDesc@5@PEBX@Z ?attribute@ShadingSystem@v1_14_4@OSL@@QEAA_NV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@H@Z __imp_?attribute@ShadingSystem@v1_14_4@OSL@@QEAA_NV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@H@Z ?attribute@ShadingSystem@v1_14_4@OSL@@QEAA_NV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@M@Z __imp_?attribute@ShadingSystem@v1_14_4@OSL@@QEAA_NV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@M@Z ?attribute@ShadingSystem@v1_14_4@OSL@@QEAA_NV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@N@Z __imp_?attribute@ShadingSystem@v1_14_4@OSL@@QEAA_NV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@N@Z ?attribute@ShadingSystem@v1_14_4@OSL@@QEAA_NV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@0@Z __imp_?attribute@ShadingSystem@v1_14_4@OSL@@QEAA_NV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@0@Z ?attribute@ShadingSystem@v1_14_4@OSL@@QEAA_NPEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@UTypeDesc@6@PEBX@Z __imp_?attribute@ShadingSystem@v1_14_4@OSL@@QEAA_NPEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@UTypeDesc@6@PEBX@Z ?attribute@ShadingSystem@v1_14_4@OSL@@QEAA_NPEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@H@Z __imp_?attribute@ShadingSystem@v1_14_4@OSL@@QEAA_NPEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@H@Z ?attribute@ShadingSystem@v1_14_4@OSL@@QEAA_NPEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@M@Z __imp_?attribute@ShadingSystem@v1_14_4@OSL@@QEAA_NPEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@M@Z ?attribute@ShadingSystem@v1_14_4@OSL@@QEAA_NPEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@N@Z __imp_?attribute@ShadingSystem@v1_14_4@OSL@@QEAA_NPEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@N@Z ?attribute@ShadingSystem@v1_14_4@OSL@@QEAA_NPEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@1@Z __imp_?attribute@ShadingSystem@v1_14_4@OSL@@QEAA_NPEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@1@Z ?getattribute@ShadingSystem@v1_14_4@OSL@@QEAA_NV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@UTypeDesc@5@PEAX@Z __imp_?getattribute@ShadingSystem@v1_14_4@OSL@@QEAA_NV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@UTypeDesc@5@PEAX@Z ?getattribute@ShadingSystem@v1_14_4@OSL@@QEAA_NV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@AEAH@Z __imp_?getattribute@ShadingSystem@v1_14_4@OSL@@QEAA_NV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@AEAH@Z ?getattribute@ShadingSystem@v1_14_4@OSL@@QEAA_NV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@AEAM@Z __imp_?getattribute@ShadingSystem@v1_14_4@OSL@@QEAA_NV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@AEAM@Z ?getattribute@ShadingSystem@v1_14_4@OSL@@QEAA_NV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@AEAN@Z __imp_?getattribute@ShadingSystem@v1_14_4@OSL@@QEAA_NV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@AEAN@Z ?getattribute@ShadingSystem@v1_14_4@OSL@@QEAA_NV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@PEAPEAD@Z __imp_?getattribute@ShadingSystem@v1_14_4@OSL@@QEAA_NV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@PEAPEAD@Z ?getattribute@ShadingSystem@v1_14_4@OSL@@QEAA_NV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@AEAVustring@5@@Z __imp_?getattribute@ShadingSystem@v1_14_4@OSL@@QEAA_NV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@AEAVustring@5@@Z ?getattribute@ShadingSystem@v1_14_4@OSL@@QEAA_NV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@AEAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z __imp_?getattribute@ShadingSystem@v1_14_4@OSL@@QEAA_NV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@AEAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z ?getattribute@ShadingSystem@v1_14_4@OSL@@QEAA_NPEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@UTypeDesc@6@PEAX@Z __imp_?getattribute@ShadingSystem@v1_14_4@OSL@@QEAA_NPEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@UTypeDesc@6@PEAX@Z ?getattribute@ShadingSystem@v1_14_4@OSL@@QEAA_NPEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@AEAH@Z __imp_?getattribute@ShadingSystem@v1_14_4@OSL@@QEAA_NPEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@AEAH@Z ?getattribute@ShadingSystem@v1_14_4@OSL@@QEAA_NPEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@AEAM@Z __imp_?getattribute@ShadingSystem@v1_14_4@OSL@@QEAA_NPEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@AEAM@Z ?getattribute@ShadingSystem@v1_14_4@OSL@@QEAA_NPEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@AEAN@Z __imp_?getattribute@ShadingSystem@v1_14_4@OSL@@QEAA_NPEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@AEAN@Z ?getattribute@ShadingSystem@v1_14_4@OSL@@QEAA_NPEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@PEAPEAD@Z __imp_?getattribute@ShadingSystem@v1_14_4@OSL@@QEAA_NPEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@PEAPEAD@Z ?getattribute@ShadingSystem@v1_14_4@OSL@@QEAA_NPEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@AEAVustring@6@@Z __imp_?getattribute@ShadingSystem@v1_14_4@OSL@@QEAA_NPEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@AEAVustring@6@@Z ?getattribute@ShadingSystem@v1_14_4@OSL@@QEAA_NPEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@AEAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z __imp_?getattribute@ShadingSystem@v1_14_4@OSL@@QEAA_NPEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@AEAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z ?LoadMemoryCompiledShader@ShadingSystem@v1_14_4@OSL@@QEAA_NV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@0@Z __imp_?LoadMemoryCompiledShader@ShadingSystem@v1_14_4@OSL@@QEAA_NV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@0@Z ?ShaderGroupBegin@ShadingSystem@v1_14_4@OSL@@QEAA?AV?$shared_ptr@VShaderGroup@v1_14_4@OSL@@@std@@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@@Z __imp_?ShaderGroupBegin@ShadingSystem@v1_14_4@OSL@@QEAA?AV?$shared_ptr@VShaderGroup@v1_14_4@OSL@@@std@@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@@Z ?ShaderGroupBegin@ShadingSystem@v1_14_4@OSL@@QEAA?AV?$shared_ptr@VShaderGroup@v1_14_4@OSL@@@std@@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@00@Z __imp_?ShaderGroupBegin@ShadingSystem@v1_14_4@OSL@@QEAA?AV?$shared_ptr@VShaderGroup@v1_14_4@OSL@@@std@@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@00@Z ?ShaderGroupEnd@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShaderGroup@23@@Z __imp_?ShaderGroupEnd@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShaderGroup@23@@Z ?Parameter@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@UTypeDesc@6@PEBXW4ParamHints@23@@Z __imp_?Parameter@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@UTypeDesc@6@PEBXW4ParamHints@23@@Z ?Parameter@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@UTypeDesc@6@PEBX_N@Z __imp_?Parameter@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@UTypeDesc@6@PEBX_N@Z ?Parameter@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@HW4ParamHints@23@@Z __imp_?Parameter@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@HW4ParamHints@23@@Z ?Parameter@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@MW4ParamHints@23@@Z __imp_?Parameter@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@MW4ParamHints@23@@Z ?Parameter@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@W4ParamHints@23@@Z __imp_?Parameter@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@W4ParamHints@23@@Z ?Parameter@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@Vustring@6@W4ParamHints@23@@Z __imp_?Parameter@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@Vustring@6@W4ParamHints@23@@Z ?Parameter@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@H_N@Z __imp_?Parameter@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@H_N@Z ?Parameter@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@M_N@Z __imp_?Parameter@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@M_N@Z ?Parameter@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@_N@Z __imp_?Parameter@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@_N@Z ?Parameter@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@Vustring@6@_N@Z __imp_?Parameter@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@Vustring@6@_N@Z ?Shader@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@11@Z __imp_?Shader@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@11@Z ?ConnectShaders@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@111@Z __imp_?ConnectShaders@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@111@Z ?ReParameter@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@1UTypeDesc@6@PEBX@Z __imp_?ReParameter@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@1UTypeDesc@6@PEBX@Z ?ReParameter@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@1H@Z __imp_?ReParameter@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@1H@Z ?ReParameter@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@1M@Z __imp_?ReParameter@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@1M@Z ?ReParameter@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@1AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z __imp_?ReParameter@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@1AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z ?ReParameter@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@1Vustring@6@@Z __imp_?ReParameter@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@1Vustring@6@@Z ?Parameter@ShadingSystem@v1_14_4@OSL@@QEAA_NV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@UTypeDesc@5@PEBXW4ParamHints@23@@Z __imp_?Parameter@ShadingSystem@v1_14_4@OSL@@QEAA_NV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@UTypeDesc@5@PEBXW4ParamHints@23@@Z ?Shader@ShadingSystem@v1_14_4@OSL@@QEAA_NV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@00@Z __imp_?Shader@ShadingSystem@v1_14_4@OSL@@QEAA_NV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@00@Z ?ConnectShaders@ShadingSystem@v1_14_4@OSL@@QEAA_NV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@000@Z __imp_?ConnectShaders@ShadingSystem@v1_14_4@OSL@@QEAA_NV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@000@Z ?ShaderGroupEnd@ShadingSystem@v1_14_4@OSL@@QEAA_NXZ __imp_?ShaderGroupEnd@ShadingSystem@v1_14_4@OSL@@QEAA_NXZ ?Parameter@ShadingSystem@v1_14_4@OSL@@QEAA_NV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@UTypeDesc@5@PEBX_N@Z __imp_?Parameter@ShadingSystem@v1_14_4@OSL@@QEAA_NV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@UTypeDesc@5@PEBX_N@Z ?create_thread_info@ShadingSystem@v1_14_4@OSL@@QEAAPEAUPerThreadInfo@23@XZ __imp_?create_thread_info@ShadingSystem@v1_14_4@OSL@@QEAAPEAUPerThreadInfo@23@XZ ?destroy_thread_info@ShadingSystem@v1_14_4@OSL@@QEAAXPEAUPerThreadInfo@23@@Z __imp_?destroy_thread_info@ShadingSystem@v1_14_4@OSL@@QEAAXPEAUPerThreadInfo@23@@Z ?get_context@ShadingSystem@v1_14_4@OSL@@QEAAPEAVShadingContext@23@PEAUPerThreadInfo@23@PEAVPerthread@TextureSystem@OpenImageIO_v3_0@@@Z __imp_?get_context@ShadingSystem@v1_14_4@OSL@@QEAAPEAVShadingContext@23@PEAUPerThreadInfo@23@PEAVPerthread@TextureSystem@OpenImageIO_v3_0@@@Z ?release_context@ShadingSystem@v1_14_4@OSL@@QEAAXPEAVShadingContext@23@@Z __imp_?release_context@ShadingSystem@v1_14_4@OSL@@QEAAXPEAVShadingContext@23@@Z ?execute@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShadingContext@23@AEAVShaderGroup@23@HHAEAUShaderGlobals@23@PEAX3_N@Z __imp_?execute@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShadingContext@23@AEAVShaderGroup@23@HHAEAUShaderGlobals@23@PEAX3_N@Z ?execute@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShadingContext@23@AEAVShaderGroup@23@AEAUShaderGlobals@23@_N@Z __imp_?execute@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShadingContext@23@AEAVShaderGroup@23@AEAUShaderGlobals@23@_N@Z ?execute@ShadingSystem@v1_14_4@OSL@@QEAA_NPEAVShadingContext@23@AEAVShaderGroup@23@AEAUShaderGlobals@23@_N@Z __imp_?execute@ShadingSystem@v1_14_4@OSL@@QEAA_NPEAVShadingContext@23@AEAVShaderGroup@23@AEAUShaderGlobals@23@_N@Z ?execute_init@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShadingContext@23@AEAVShaderGroup@23@HHAEAUShaderGlobals@23@PEAX3_N@Z __imp_?execute_init@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShadingContext@23@AEAVShaderGroup@23@HHAEAUShaderGlobals@23@PEAX3_N@Z ?execute_init@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShadingContext@23@AEAVShaderGroup@23@AEAUShaderGlobals@23@_N@Z __imp_?execute_init@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShadingContext@23@AEAVShaderGroup@23@AEAUShaderGlobals@23@_N@Z ?execute_layer@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShadingContext@23@HHAEAUShaderGlobals@23@PEAX2H@Z __imp_?execute_layer@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShadingContext@23@HHAEAUShaderGlobals@23@PEAX2H@Z ?execute_layer@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShadingContext@23@HHAEAUShaderGlobals@23@PEAX2Vustring@OpenImageIO_v3_0@@@Z __imp_?execute_layer@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShadingContext@23@HHAEAUShaderGlobals@23@PEAX2Vustring@OpenImageIO_v3_0@@@Z ?execute_layer@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShadingContext@23@HHAEAUShaderGlobals@23@PEAX2PEBVShaderSymbol@23@@Z __imp_?execute_layer@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShadingContext@23@HHAEAUShaderGlobals@23@PEAX2PEBVShaderSymbol@23@@Z ?execute_layer@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShadingContext@23@AEAUShaderGlobals@23@H@Z __imp_?execute_layer@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShadingContext@23@AEAUShaderGlobals@23@H@Z ?execute_layer@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShadingContext@23@AEAUShaderGlobals@23@Vustring@OpenImageIO_v3_0@@@Z __imp_?execute_layer@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShadingContext@23@AEAUShaderGlobals@23@Vustring@OpenImageIO_v3_0@@@Z ?execute_layer@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShadingContext@23@AEAUShaderGlobals@23@PEBVShaderSymbol@23@@Z __imp_?execute_layer@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShadingContext@23@AEAUShaderGlobals@23@PEBVShaderSymbol@23@@Z ?execute_cleanup@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShadingContext@23@@Z __imp_?execute_cleanup@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShadingContext@23@@Z ?find_layer@ShadingSystem@v1_14_4@OSL@@QEBAHAEBVShaderGroup@23@Vustring@OpenImageIO_v3_0@@@Z __imp_?find_layer@ShadingSystem@v1_14_4@OSL@@QEBAHAEBVShaderGroup@23@Vustring@OpenImageIO_v3_0@@@Z ?get_symbol@ShadingSystem@v1_14_4@OSL@@QEBAPEBXAEBVShadingContext@23@Vustring@OpenImageIO_v3_0@@1AEAUTypeDesc@6@@Z __imp_?get_symbol@ShadingSystem@v1_14_4@OSL@@QEBAPEBXAEBVShadingContext@23@Vustring@OpenImageIO_v3_0@@1AEAUTypeDesc@6@@Z ?get_symbol@ShadingSystem@v1_14_4@OSL@@QEBAPEBXAEBVShadingContext@23@Vustring@OpenImageIO_v3_0@@AEAUTypeDesc@6@@Z __imp_?get_symbol@ShadingSystem@v1_14_4@OSL@@QEBAPEBXAEBVShadingContext@23@Vustring@OpenImageIO_v3_0@@AEAUTypeDesc@6@@Z ?find_symbol@ShadingSystem@v1_14_4@OSL@@QEBAPEBVShaderSymbol@23@AEBVShaderGroup@23@Vustring@OpenImageIO_v3_0@@1@Z __imp_?find_symbol@ShadingSystem@v1_14_4@OSL@@QEBAPEBVShaderSymbol@23@AEBVShaderGroup@23@Vustring@OpenImageIO_v3_0@@1@Z ?find_symbol@ShadingSystem@v1_14_4@OSL@@QEBAPEBVShaderSymbol@23@AEBVShaderGroup@23@Vustring@OpenImageIO_v3_0@@@Z __imp_?find_symbol@ShadingSystem@v1_14_4@OSL@@QEBAPEBVShaderSymbol@23@AEBVShaderGroup@23@Vustring@OpenImageIO_v3_0@@@Z ?symbol_typedesc@ShadingSystem@v1_14_4@OSL@@QEBA?AUTypeDesc@OpenImageIO_v3_0@@PEBVShaderSymbol@23@@Z __imp_?symbol_typedesc@ShadingSystem@v1_14_4@OSL@@QEBA?AUTypeDesc@OpenImageIO_v3_0@@PEBVShaderSymbol@23@@Z ?symbol_address@ShadingSystem@v1_14_4@OSL@@QEBAPEBXAEBVShadingContext@23@PEBVShaderSymbol@23@@Z __imp_?symbol_address@ShadingSystem@v1_14_4@OSL@@QEBAPEBXAEBVShadingContext@23@PEBVShaderSymbol@23@@Z ?getstats@ShadingSystem@v1_14_4@OSL@@QEBA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@H@Z __imp_?getstats@ShadingSystem@v1_14_4@OSL@@QEBA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@H@Z ?register_closure@ShadingSystem@v1_14_4@OSL@@QEAAXV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@HPEBUClosureParam@23@P6AXPEAVRendererServices@23@HPEAX@Z4@Z __imp_?register_closure@ShadingSystem@v1_14_4@OSL@@QEAAXV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@HPEBUClosureParam@23@P6AXPEAVRendererServices@23@HPEAX@Z4@Z ?query_closure@ShadingSystem@v1_14_4@OSL@@QEAA_NPEAPEBDPEAHPEAPEBUClosureParam@23@@Z __imp_?query_closure@ShadingSystem@v1_14_4@OSL@@QEAA_NPEAPEBDPEAHPEAPEBUClosureParam@23@@Z ?globals_bit@ShadingSystem@v1_14_4@OSL@@SA?AW4SGBits@23@Vustring@OpenImageIO_v3_0@@@Z __imp_?globals_bit@ShadingSystem@v1_14_4@OSL@@SA?AW4SGBits@23@Vustring@OpenImageIO_v3_0@@@Z ?globals_name@ShadingSystem@v1_14_4@OSL@@SA?AVustring@OpenImageIO_v3_0@@W4SGBits@23@@Z __imp_?globals_name@ShadingSystem@v1_14_4@OSL@@SA?AVustring@OpenImageIO_v3_0@@W4SGBits@23@@Z ?raytype_bit@ShadingSystem@v1_14_4@OSL@@QEAAHVustring@OpenImageIO_v3_0@@@Z __imp_?raytype_bit@ShadingSystem@v1_14_4@OSL@@QEAAHVustring@OpenImageIO_v3_0@@@Z ?set_raytypes@ShadingSystem@v1_14_4@OSL@@QEAAXPEAVShaderGroup@23@HH@Z __imp_?set_raytypes@ShadingSystem@v1_14_4@OSL@@QEAAXPEAVShaderGroup@23@HH@Z ?clear_symlocs@ShadingSystem@v1_14_4@OSL@@QEAAXXZ __imp_?clear_symlocs@ShadingSystem@v1_14_4@OSL@@QEAAXXZ ?clear_symlocs@ShadingSystem@v1_14_4@OSL@@QEAAXPEAVShaderGroup@23@@Z __imp_?clear_symlocs@ShadingSystem@v1_14_4@OSL@@QEAAXPEAVShaderGroup@23@@Z ?add_symlocs@ShadingSystem@v1_14_4@OSL@@QEAAXV?$span@$$CBUSymLocationDesc@v1_14_4@OSL@@$0?0@OpenImageIO_v3_0@@@Z __imp_?add_symlocs@ShadingSystem@v1_14_4@OSL@@QEAAXV?$span@$$CBUSymLocationDesc@v1_14_4@OSL@@$0?0@OpenImageIO_v3_0@@@Z ?add_symlocs@ShadingSystem@v1_14_4@OSL@@QEAAXPEAVShaderGroup@23@V?$span@$$CBUSymLocationDesc@v1_14_4@OSL@@$0?0@OpenImageIO_v3_0@@@Z __imp_?add_symlocs@ShadingSystem@v1_14_4@OSL@@QEAAXPEAVShaderGroup@23@V?$span@$$CBUSymLocationDesc@v1_14_4@OSL@@$0?0@OpenImageIO_v3_0@@@Z ?find_symloc@ShadingSystem@v1_14_4@OSL@@QEBAPEBUSymLocationDesc@23@Vustring@OpenImageIO_v3_0@@@Z __imp_?find_symloc@ShadingSystem@v1_14_4@OSL@@QEBAPEBUSymLocationDesc@23@Vustring@OpenImageIO_v3_0@@@Z ?find_symloc@ShadingSystem@v1_14_4@OSL@@QEBAPEBUSymLocationDesc@23@PEBVShaderGroup@23@Vustring@OpenImageIO_v3_0@@@Z __imp_?find_symloc@ShadingSystem@v1_14_4@OSL@@QEBAPEBUSymLocationDesc@23@PEBVShaderGroup@23@Vustring@OpenImageIO_v3_0@@@Z ?find_symloc@ShadingSystem@v1_14_4@OSL@@QEBAPEBUSymLocationDesc@23@Vustring@OpenImageIO_v3_0@@W4SymArena@23@@Z __imp_?find_symloc@ShadingSystem@v1_14_4@OSL@@QEBAPEBUSymLocationDesc@23@Vustring@OpenImageIO_v3_0@@W4SymArena@23@@Z ?find_symloc@ShadingSystem@v1_14_4@OSL@@QEBAPEBUSymLocationDesc@23@PEBVShaderGroup@23@Vustring@OpenImageIO_v3_0@@W4SymArena@23@@Z __imp_?find_symloc@ShadingSystem@v1_14_4@OSL@@QEBAPEBUSymLocationDesc@23@PEBVShaderGroup@23@Vustring@OpenImageIO_v3_0@@W4SymArena@23@@Z ?optimize_group@ShadingSystem@v1_14_4@OSL@@QEAAXPEAVShaderGroup@23@PEAVShadingContext@23@_N@Z __imp_?optimize_group@ShadingSystem@v1_14_4@OSL@@QEAAXPEAVShaderGroup@23@PEAVShadingContext@23@_N@Z ?optimize_group@ShadingSystem@v1_14_4@OSL@@QEAAXPEAVShaderGroup@23@HHPEAVShadingContext@23@_N@Z __imp_?optimize_group@ShadingSystem@v1_14_4@OSL@@QEAAXPEAVShaderGroup@23@HHPEAVShadingContext@23@_N@Z ?optimize_all_groups@ShadingSystem@v1_14_4@OSL@@QEAAXH_N@Z __imp_?optimize_all_groups@ShadingSystem@v1_14_4@OSL@@QEAAXH_N@Z ?texturesys@ShadingSystem@v1_14_4@OSL@@QEBAPEAVTextureSystem@OpenImageIO_v3_0@@XZ __imp_?texturesys@ShadingSystem@v1_14_4@OSL@@QEBAPEAVTextureSystem@OpenImageIO_v3_0@@XZ ?renderer@ShadingSystem@v1_14_4@OSL@@QEBAPEAVRendererServices@23@XZ __imp_?renderer@ShadingSystem@v1_14_4@OSL@@QEBAPEAVRendererServices@23@XZ ?archive_shadergroup@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@@Z __imp_?archive_shadergroup@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@@Z ?archive_shadergroup@ShadingSystem@v1_14_4@OSL@@QEAA_NPEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@@Z __imp_?archive_shadergroup@ShadingSystem@v1_14_4@OSL@@QEAA_NPEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@@Z ?oslquery@ShadingSystem@v1_14_4@OSL@@QEAA?AVOSLQuery@23@AEBVShaderGroup@23@H@Z __imp_?oslquery@ShadingSystem@v1_14_4@OSL@@QEAA?AVOSLQuery@23@AEBVShaderGroup@23@H@Z ?convert_value@ShadingSystem@v1_14_4@OSL@@SA_NPEAXUTypeDesc@OpenImageIO_v3_0@@PEBX1@Z __imp_?convert_value@ShadingSystem@v1_14_4@OSL@@SA_NPEAXUTypeDesc@OpenImageIO_v3_0@@PEBX1@Z ?register_inline_function@ShadingSystem@v1_14_4@OSL@@QEAAXVustring@OpenImageIO_v3_0@@@Z __imp_?register_inline_function@ShadingSystem@v1_14_4@OSL@@QEAAXVustring@OpenImageIO_v3_0@@@Z ?unregister_inline_function@ShadingSystem@v1_14_4@OSL@@QEAAXVustring@OpenImageIO_v3_0@@@Z __imp_?unregister_inline_function@ShadingSystem@v1_14_4@OSL@@QEAAXVustring@OpenImageIO_v3_0@@@Z ?register_noinline_function@ShadingSystem@v1_14_4@OSL@@QEAAXVustring@OpenImageIO_v3_0@@@Z __imp_?register_noinline_function@ShadingSystem@v1_14_4@OSL@@QEAAXVustring@OpenImageIO_v3_0@@@Z ?unregister_noinline_function@ShadingSystem@v1_14_4@OSL@@QEAAXVustring@OpenImageIO_v3_0@@@Z __imp_?unregister_noinline_function@ShadingSystem@v1_14_4@OSL@@QEAAXVustring@OpenImageIO_v3_0@@@Z ??4ShadingSystem@v1_14_4@OSL@@QEAAAEAV012@AEBV012@@Z __imp_??4ShadingSystem@v1_14_4@OSL@@QEAAAEAV012@AEBV012@@Z ??_FShadingSystem@v1_14_4@OSL@@QEAAXXZ __imp_??_FShadingSystem@v1_14_4@OSL@@QEAAXXZ ??1RendererServices@v1_14_4@OSL@@UEAA@XZ __imp_??1RendererServices@v1_14_4@OSL@@UEAA@XZ ?supports@RendererServices@v1_14_4@OSL@@UEBAHV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@@Z __imp_?supports@RendererServices@v1_14_4@OSL@@UEBAHV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@@Z ?get_matrix@RendererServices@v1_14_4@OSL@@UEAA_NPEAUShaderGlobals@23@AEAV?$Matrix44@M@Imath_3_1@@PEBXM@Z __imp_?get_matrix@RendererServices@v1_14_4@OSL@@UEAA_NPEAUShaderGlobals@23@AEAV?$Matrix44@M@Imath_3_1@@PEBXM@Z ?get_matrix@RendererServices@v1_14_4@OSL@@UEAA_NPEAUShaderGlobals@23@AEAV?$Matrix44@M@Imath_3_1@@PEBX@Z __imp_?get_matrix@RendererServices@v1_14_4@OSL@@UEAA_NPEAUShaderGlobals@23@AEAV?$Matrix44@M@Imath_3_1@@PEBX@Z ?device_alloc@RendererServices@v1_14_4@OSL@@UEAAPEAX_K@Z __imp_?device_alloc@RendererServices@v1_14_4@OSL@@UEAAPEAX_K@Z ?device_free@RendererServices@v1_14_4@OSL@@UEAAXPEAX@Z __imp_?device_free@RendererServices@v1_14_4@OSL@@UEAAXPEAX@Z ?copy_to_device@RendererServices@v1_14_4@OSL@@UEAAPEAXPEAXPEBX_K@Z __imp_?copy_to_device@RendererServices@v1_14_4@OSL@@UEAAPEAXPEAXPEBX_K@Z ??0RendererServices@v1_14_4@OSL@@QEAA@AEBV012@@Z __imp_??0RendererServices@v1_14_4@OSL@@QEAA@AEBV012@@Z ??4RendererServices@v1_14_4@OSL@@QEAAAEAV012@AEBV012@@Z __imp_??4RendererServices@v1_14_4@OSL@@QEAAAEAV012@AEBV012@@Z ??_FRendererServices@v1_14_4@OSL@@QEAAXXZ __imp_??_FRendererServices@v1_14_4@OSL@@QEAAXXZ ?luminance@ColorSystem@pvt@v1_14_4@OSL@@QEBAMAEBV?$Color3@M@Imath_3_1@@@Z __imp_?luminance@ColorSystem@pvt@v1_14_4@OSL@@QEBAMAEBV?$Color3@M@Imath_3_1@@@Z ?luminance_scale@ColorSystem@pvt@v1_14_4@OSL@@QEBAAEBV?$Color3@M@Imath_3_1@@XZ __imp_?luminance_scale@ColorSystem@pvt@v1_14_4@OSL@@QEBAAEBV?$Color3@M@Imath_3_1@@XZ ?colorspace@ColorSystem@pvt@v1_14_4@OSL@@QEBAAEBVustringhash@OpenImageIO_v3_0@@XZ __imp_?colorspace@ColorSystem@pvt@v1_14_4@OSL@@QEBAAEBVustringhash@OpenImageIO_v3_0@@XZ ??0ColorSystem@pvt@v1_14_4@OSL@@QEAA@XZ __imp_??0ColorSystem@pvt@v1_14_4@OSL@@QEAA@XZ ??0ColorSystem@pvt@v1_14_4@OSL@@QEAA@AEBV0123@@Z __imp_??0ColorSystem@pvt@v1_14_4@OSL@@QEAA@AEBV0123@@Z ??0ColorSystem@pvt@v1_14_4@OSL@@QEAA@$$QEAV0123@@Z __imp_??0ColorSystem@pvt@v1_14_4@OSL@@QEAA@$$QEAV0123@@Z ??4ColorSystem@pvt@v1_14_4@OSL@@QEAAAEAV0123@AEBV0123@@Z __imp_??4ColorSystem@pvt@v1_14_4@OSL@@QEAAAEAV0123@AEBV0123@@Z ??4ColorSystem@pvt@v1_14_4@OSL@@QEAAAEAV0123@$$QEAV0123@@Z __imp_??4ColorSystem@pvt@v1_14_4@OSL@@QEAAAEAV0123@$$QEAV0123@@Z ?raytype_bit@ShadingSystemImpl@pvt@v1_14_4@OSL@@QEAAHVustring@OpenImageIO_v3_0@@@Z __imp_?raytype_bit@ShadingSystemImpl@pvt@v1_14_4@OSL@@QEAAHVustring@OpenImageIO_v3_0@@@Z ?shadingsys@ShadingContext@v1_14_4@OSL@@QEBAAEAVShadingSystemImpl@pvt@23@XZ __imp_?shadingsys@ShadingContext@v1_14_4@OSL@@QEBAAEAVShadingSystemImpl@pvt@23@XZ ?renderer@ShadingContext@v1_14_4@OSL@@QEBAPEAVRendererServices@23@XZ __imp_?renderer@ShadingContext@v1_14_4@OSL@@QEBAPEAVRendererServices@23@XZ ?closure_component_allot@ShadingContext@v1_14_4@OSL@@QEAAPEAUClosureComponent@23@H_KAEBV?$Color3@M@Imath_3_1@@@Z __imp_?closure_component_allot@ShadingContext@v1_14_4@OSL@@QEAAPEAUClosureComponent@23@H_KAEBV?$Color3@M@Imath_3_1@@@Z ?closure_mul_allot@ShadingContext@v1_14_4@OSL@@QEAAPEAUClosureMul@23@AEBV?$Color3@M@Imath_3_1@@PEBUClosureColor@23@@Z __imp_?closure_mul_allot@ShadingContext@v1_14_4@OSL@@QEAAPEAUClosureMul@23@AEBV?$Color3@M@Imath_3_1@@PEBUClosureColor@23@@Z ?closure_mul_allot@ShadingContext@v1_14_4@OSL@@QEAAPEAUClosureMul@23@MPEBUClosureColor@23@@Z __imp_?closure_mul_allot@ShadingContext@v1_14_4@OSL@@QEAAPEAUClosureMul@23@MPEBUClosureColor@23@@Z ?closure_add_allot@ShadingContext@v1_14_4@OSL@@QEAAPEAUClosureAdd@23@PEBUClosureColor@23@0@Z __imp_?closure_add_allot@ShadingContext@v1_14_4@OSL@@QEAAPEAUClosureAdd@23@PEBUClosureColor@23@0@Z ?symbol@ShadingContext@v1_14_4@OSL@@QEBAPEBVSymbol@pvt@23@Vustring@OpenImageIO_v3_0@@@Z __imp_?symbol@ShadingContext@v1_14_4@OSL@@QEBAPEBVSymbol@pvt@23@Vustring@OpenImageIO_v3_0@@@Z ?group@ShadingContext@v1_14_4@OSL@@QEAAPEAVShaderGroup@23@XZ __imp_?group@ShadingContext@v1_14_4@OSL@@QEAAPEAVShaderGroup@23@XZ ?group@ShadingContext@v1_14_4@OSL@@QEBAPEBVShaderGroup@23@XZ __imp_?group@ShadingContext@v1_14_4@OSL@@QEBAPEBVShaderGroup@23@XZ ?group@ShadingContext@v1_14_4@OSL@@QEAAXPEAVShaderGroup@23@@Z __imp_?group@ShadingContext@v1_14_4@OSL@@QEAAXPEAVShaderGroup@23@@Z ?messages@ShadingContext@v1_14_4@OSL@@QEAAAEAUMessageList@pvt@23@XZ __imp_?messages@ShadingContext@v1_14_4@OSL@@QEAAAEAUMessageList@pvt@23@XZ ?thread_info@ShadingContext@v1_14_4@OSL@@QEBAPEAUPerThreadInfo@23@XZ __imp_?thread_info@ShadingContext@v1_14_4@OSL@@QEBAPEAUPerThreadInfo@23@XZ ?texture_thread_info@ShadingContext@v1_14_4@OSL@@QEBAPEAVPerthread@TextureSystem@OpenImageIO_v3_0@@XZ __imp_?texture_thread_info@ShadingContext@v1_14_4@OSL@@QEBAPEAVPerthread@TextureSystem@OpenImageIO_v3_0@@XZ ?texture_thread_info@ShadingContext@v1_14_4@OSL@@QEAAXPEAVPerthread@TextureSystem@OpenImageIO_v3_0@@@Z __imp_?texture_thread_info@ShadingContext@v1_14_4@OSL@@QEAAXPEAVPerthread@TextureSystem@OpenImageIO_v3_0@@@Z ?llvm_thread_info@ShadingContext@v1_14_4@OSL@@QEBAAEBUPerThreadInfo@LLVM_Util@pvt@23@XZ __imp_?llvm_thread_info@ShadingContext@v1_14_4@OSL@@QEBAAEBUPerThreadInfo@LLVM_Util@pvt@23@XZ ?alloc_scratch@ShadingContext@v1_14_4@OSL@@QEAAPEAX_K0@Z __imp_?alloc_scratch@ShadingContext@v1_14_4@OSL@@QEAAPEAX_K0@Z ?incr_layers_executed@ShadingContext@v1_14_4@OSL@@QEAAXXZ __imp_?incr_layers_executed@ShadingContext@v1_14_4@OSL@@QEAAXXZ ?incr_get_userdata_calls@ShadingContext@v1_14_4@OSL@@QEAAXXZ __imp_?incr_get_userdata_calls@ShadingContext@v1_14_4@OSL@@QEAAXXZ ?clear_runtime_stats@ShadingContext@v1_14_4@OSL@@QEAAXXZ __imp_?clear_runtime_stats@ShadingContext@v1_14_4@OSL@@QEAAXXZ ?record_runtime_stats@ShadingContext@v1_14_4@OSL@@QEAAXXZ __imp_?record_runtime_stats@ShadingContext@v1_14_4@OSL@@QEAAXXZ ?allow_warnings@ShadingContext@v1_14_4@OSL@@QEAA_NXZ __imp_?allow_warnings@ShadingContext@v1_14_4@OSL@@QEAA_NXZ ?reserve_heap@ShadingContext@v1_14_4@OSL@@QEAAX_K@Z __imp_?reserve_heap@ShadingContext@v1_14_4@OSL@@QEAAX_K@Z ?execution_is_batched@ShadingContext@v1_14_4@OSL@@AEBA_NXZ __imp_?execution_is_batched@ShadingContext@v1_14_4@OSL@@AEBA_NXZ ??0Parameter@OSLQuery@v1_14_4@OSL@@QEAA@XZ __imp_??0Parameter@OSLQuery@v1_14_4@OSL@@QEAA@XZ ??1Parameter@OSLQuery@v1_14_4@OSL@@QEAA@XZ __imp_??1Parameter@OSLQuery@v1_14_4@OSL@@QEAA@XZ ??0OSLQuery@v1_14_4@OSL@@QEAA@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@0@Z __imp_??0OSLQuery@v1_14_4@OSL@@QEAA@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@0@Z ??0OSLQuery@v1_14_4@OSL@@QEAA@PEBVShaderGroup@12@H@Z __imp_??0OSLQuery@v1_14_4@OSL@@QEAA@PEBVShaderGroup@12@H@Z ?init@OSLQuery@v1_14_4@OSL@@QEAA_NPEBVShaderGroup@23@H@Z __imp_?init@OSLQuery@v1_14_4@OSL@@QEAA_NPEBVShaderGroup@23@H@Z ?shadertype@OSLQuery@v1_14_4@OSL@@QEBA?BVustring@OpenImageIO_v3_0@@XZ __imp_?shadertype@OSLQuery@v1_14_4@OSL@@QEBA?BVustring@OpenImageIO_v3_0@@XZ ?shadername@OSLQuery@v1_14_4@OSL@@QEBA?BVustring@OpenImageIO_v3_0@@XZ __imp_?shadername@OSLQuery@v1_14_4@OSL@@QEBA?BVustring@OpenImageIO_v3_0@@XZ ?nparams@OSLQuery@v1_14_4@OSL@@QEBA_KXZ __imp_?nparams@OSLQuery@v1_14_4@OSL@@QEBA_KXZ ?getparam@OSLQuery@v1_14_4@OSL@@QEBAPEBUParameter@123@_K@Z __imp_?getparam@OSLQuery@v1_14_4@OSL@@QEBAPEBUParameter@123@_K@Z ?getparam@OSLQuery@v1_14_4@OSL@@QEBAPEBUParameter@123@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z __imp_?getparam@OSLQuery@v1_14_4@OSL@@QEBAPEBUParameter@123@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z ?getparam@OSLQuery@v1_14_4@OSL@@QEBAPEBUParameter@123@Vustring@OpenImageIO_v3_0@@@Z __imp_?getparam@OSLQuery@v1_14_4@OSL@@QEBAPEBUParameter@123@Vustring@OpenImageIO_v3_0@@@Z ?parameters@OSLQuery@v1_14_4@OSL@@QEBAAEBV?$vector@UParameter@OSLQuery@v1_14_4@OSL@@V?$allocator@UParameter@OSLQuery@v1_14_4@OSL@@@std@@@std@@XZ __imp_?parameters@OSLQuery@v1_14_4@OSL@@QEBAAEBV?$vector@UParameter@OSLQuery@v1_14_4@OSL@@V?$allocator@UParameter@OSLQuery@v1_14_4@OSL@@@std@@@std@@XZ ?metadata@OSLQuery@v1_14_4@OSL@@QEBAAEBV?$vector@UParameter@OSLQuery@v1_14_4@OSL@@V?$allocator@UParameter@OSLQuery@v1_14_4@OSL@@@std@@@std@@XZ __imp_?metadata@OSLQuery@v1_14_4@OSL@@QEBAAEBV?$vector@UParameter@OSLQuery@v1_14_4@OSL@@V?$allocator@UParameter@OSLQuery@v1_14_4@OSL@@@std@@@std@@XZ ?geterror@OSLQuery@v1_14_4@OSL@@QEAA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@_N@Z __imp_?geterror@OSLQuery@v1_14_4@OSL@@QEAA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@_N@Z ?begin@OSLQuery@v1_14_4@OSL@@QEAA?AV?$_Vector_iterator@V?$_Vector_val@U?$_Simple_types@UParameter@OSLQuery@v1_14_4@OSL@@@std@@@std@@@std@@XZ __imp_?begin@OSLQuery@v1_14_4@OSL@@QEAA?AV?$_Vector_iterator@V?$_Vector_val@U?$_Simple_types@UParameter@OSLQuery@v1_14_4@OSL@@@std@@@std@@@std@@XZ ?end@OSLQuery@v1_14_4@OSL@@QEAA?AV?$_Vector_iterator@V?$_Vector_val@U?$_Simple_types@UParameter@OSLQuery@v1_14_4@OSL@@@std@@@std@@@std@@XZ __imp_?end@OSLQuery@v1_14_4@OSL@@QEAA?AV?$_Vector_iterator@V?$_Vector_val@U?$_Simple_types@UParameter@OSLQuery@v1_14_4@OSL@@@std@@@std@@@std@@XZ ?cbegin@OSLQuery@v1_14_4@OSL@@QEBA?AV?$_Vector_const_iterator@V?$_Vector_val@U?$_Simple_types@UParameter@OSLQuery@v1_14_4@OSL@@@std@@@std@@@std@@XZ __imp_?cbegin@OSLQuery@v1_14_4@OSL@@QEBA?AV?$_Vector_const_iterator@V?$_Vector_val@U?$_Simple_types@UParameter@OSLQuery@v1_14_4@OSL@@@std@@@std@@@std@@XZ ?cend@OSLQuery@v1_14_4@OSL@@QEBA?AV?$_Vector_const_iterator@V?$_Vector_val@U?$_Simple_types@UParameter@OSLQuery@v1_14_4@OSL@@@std@@@std@@@std@@XZ __imp_?cend@OSLQuery@v1_14_4@OSL@@QEBA?AV?$_Vector_const_iterator@V?$_Vector_val@U?$_Simple_types@UParameter@OSLQuery@v1_14_4@OSL@@@std@@@std@@@std@@XZ ?append_error@OSLQuery@v1_14_4@OSL@@AEBAXAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z __imp_?append_error@OSLQuery@v1_14_4@OSL@@AEBAXAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z ??0OSLQuery@v1_14_4@OSL@@QEAA@AEBV012@@Z __imp_??0OSLQuery@v1_14_4@OSL@@QEAA@AEBV012@@Z ??4OSLQuery@v1_14_4@OSL@@QEAAAEAV012@AEBV012@@Z __imp_??4OSLQuery@v1_14_4@OSL@@QEAAAEAV012@AEBV012@@Z __imp_?_emptystring_@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?camera@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?common@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?object@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?shader@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?screen@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?NDC@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?rgb@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?RGB@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?hsv@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?hsl@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?YIQ@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?XYZ@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?xyz@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?xyY@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?null@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?default_@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?label@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?sidedness@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?front@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?back@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?both@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?P@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?I@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?N@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?Ng@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?dPdu@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?dPdv@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?u@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?v@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?Ps@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?time@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?dtime@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?dPdtime@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?Ci@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?width@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?swidth@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?twidth@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?rwidth@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?blur@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?sblur@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?tblur@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?rblur@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?wrap@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?swrap@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?twrap@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?rwrap@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?black@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?clamp@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?periodic@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?mirror@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?firstchannel@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?fill@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?alpha@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?error@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?errormessage@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?trace@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?mindist@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?maxdist@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?shade@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?traceset@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?interp@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?closest@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?linear@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?cubic@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?catmullrom@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?bezier@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?bspline@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?hermite@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?constant@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?smartcubic@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?perlin@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?uperlin@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?noise@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?snoise@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?cell@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?cellnoise@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?pcellnoise@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?hash@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?hashnoise@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?phashnoise@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?pnoise@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?psnoise@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?genericnoise@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?genericpnoise@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?gabor@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?gabornoise@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?gaborpnoise@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?simplex@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?usimplex@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?simplexnoise@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?usimplexnoise@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?anisotropic@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?direction@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?do_filter@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?bandwidth@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?impulses@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?op_dowhile@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?op_for@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?op_while@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?op_exit@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?subimage@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?subimagename@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?missingcolor@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?missingalpha@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?end@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?useparam@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?uninitialized_string@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?unull@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?raytype@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?color@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?point@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?vector@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?normal@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?matrix@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?Rec709@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?sRGB@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?NTSC@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?EBU@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?PAL@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?SECAM@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?SMPTE@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?HDTV@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?CIE@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?AdobeRGB@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?ACES2065_1@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?ACEScg@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?colorsystem@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?arraylength@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?unknown@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?ErrorColorTransform@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?world@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_??_7RendererServices@v1_14_4@OSL@@6B@ ?print_closure@pvt@v1_14_4@OSL@@YAXAEAV?$basic_ostream@DU?$char_traits@D@std@@@std@@PEBUClosureColor@23@PEAVShadingSystemImpl@123@_N@Z __imp_?print_closure@pvt@v1_14_4@OSL@@YAXAEAV?$basic_ostream@DU?$char_traits@D@std@@@std@@PEBUClosureColor@23@PEAVShadingSystemImpl@123@_N@Z __imp_?NONE@Labels@v1_14_4@OSL@@2Vustring@OpenImageIO_v3_0@@B __imp_?CAMERA@Labels@v1_14_4@OSL@@2Vustring@OpenImageIO_v3_0@@B __imp_?LIGHT@Labels@v1_14_4@OSL@@2Vustring@OpenImageIO_v3_0@@B __imp_?BACKGROUND@Labels@v1_14_4@OSL@@2Vustring@OpenImageIO_v3_0@@B __imp_?TRANSMIT@Labels@v1_14_4@OSL@@2Vustring@OpenImageIO_v3_0@@B __imp_?REFLECT@Labels@v1_14_4@OSL@@2Vustring@OpenImageIO_v3_0@@B __imp_?VOLUME@Labels@v1_14_4@OSL@@2Vustring@OpenImageIO_v3_0@@B __imp_?OBJECT@Labels@v1_14_4@OSL@@2Vustring@OpenImageIO_v3_0@@B __imp_?DIFFUSE@Labels@v1_14_4@OSL@@2Vustring@OpenImageIO_v3_0@@B __imp_?GLOSSY@Labels@v1_14_4@OSL@@2Vustring@OpenImageIO_v3_0@@B __imp_?SINGULAR@Labels@v1_14_4@OSL@@2Vustring@OpenImageIO_v3_0@@B __imp_?STRAIGHT@Labels@v1_14_4@OSL@@2Vustring@OpenImageIO_v3_0@@B __imp_?STOP@Labels@v1_14_4@OSL@@2Vustring@OpenImageIO_v3_0@@B ?dict_find@ShadingContext@v1_14_4@OSL@@QEAAHPEAUShaderGlobals@23@Vustring@OpenImageIO_v3_0@@1@Z __imp_?dict_find@ShadingContext@v1_14_4@OSL@@QEAAHPEAUShaderGlobals@23@Vustring@OpenImageIO_v3_0@@1@Z ?dict_find@ShadingContext@v1_14_4@OSL@@QEAAHPEAUShaderGlobals@23@HVustring@OpenImageIO_v3_0@@@Z __imp_?dict_find@ShadingContext@v1_14_4@OSL@@QEAAHPEAUShaderGlobals@23@HVustring@OpenImageIO_v3_0@@@Z ?dict_next@ShadingContext@v1_14_4@OSL@@QEAAHH@Z __imp_?dict_next@ShadingContext@v1_14_4@OSL@@QEAAHH@Z ?dict_value@ShadingContext@v1_14_4@OSL@@QEAAHHVustring@OpenImageIO_v3_0@@UTypeDesc@5@PEAX_N@Z __imp_?dict_value@ShadingContext@v1_14_4@OSL@@QEAAHHVustring@OpenImageIO_v3_0@@UTypeDesc@5@PEAX_N@Z ?free_dict_resources@ShadingContext@v1_14_4@OSL@@AEAAXXZ __imp_?free_dict_resources@ShadingContext@v1_14_4@OSL@@AEAAXXZ ??0ShadingContext@v1_14_4@OSL@@QEAA@AEAVShadingSystemImpl@pvt@12@PEAUPerThreadInfo@12@@Z __imp_??0ShadingContext@v1_14_4@OSL@@QEAA@AEAVShadingSystemImpl@pvt@12@PEAUPerThreadInfo@12@@Z ??1ShadingContext@v1_14_4@OSL@@QEAA@XZ __imp_??1ShadingContext@v1_14_4@OSL@@QEAA@XZ ?execute_init@ShadingContext@v1_14_4@OSL@@QEAA_NAEAVShaderGroup@23@HHAEAUShaderGlobals@23@PEAX2_N@Z __imp_?execute_init@ShadingContext@v1_14_4@OSL@@QEAA_NAEAVShaderGroup@23@HHAEAUShaderGlobals@23@PEAX2_N@Z ?execute_layer@ShadingContext@v1_14_4@OSL@@QEAA_NHHAEAUShaderGlobals@23@PEAX1H@Z __imp_?execute_layer@ShadingContext@v1_14_4@OSL@@QEAA_NHHAEAUShaderGlobals@23@PEAX1H@Z ?execute_cleanup@ShadingContext@v1_14_4@OSL@@QEAA_NXZ __imp_?execute_cleanup@ShadingContext@v1_14_4@OSL@@QEAA_NXZ ?execute@ShadingContext@v1_14_4@OSL@@QEAA_NAEAVShaderGroup@23@HHAEAUShaderGlobals@23@PEAX2_N@Z __imp_?execute@ShadingContext@v1_14_4@OSL@@QEAA_NAEAVShaderGroup@23@HHAEAUShaderGlobals@23@PEAX2_N@Z ?symbol@ShadingContext@v1_14_4@OSL@@QEBAPEBVSymbol@pvt@23@Vustring@OpenImageIO_v3_0@@0@Z __imp_?symbol@ShadingContext@v1_14_4@OSL@@QEBAPEBVSymbol@pvt@23@Vustring@OpenImageIO_v3_0@@0@Z ?symbol_data@ShadingContext@v1_14_4@OSL@@QEBAPEBXAEBVSymbol@pvt@23@@Z __imp_?symbol_data@ShadingContext@v1_14_4@OSL@@QEBAPEBXAEBVSymbol@pvt@23@@Z ?find_regex@ShadingContext@v1_14_4@OSL@@QEAAAEBV?$basic_regex@DV?$regex_traits@D@std@@@std@@Vustring@OpenImageIO_v3_0@@@Z __imp_?find_regex@ShadingContext@v1_14_4@OSL@@QEAAAEBV?$basic_regex@DV?$regex_traits@D@std@@@std@@Vustring@OpenImageIO_v3_0@@@Z ?osl_get_attribute@ShadingContext@v1_14_4@OSL@@QEAA_NPEAUShaderGlobals@23@PEAXHVustringhash@OpenImageIO_v3_0@@2HHUTypeDesc@6@1@Z __imp_?osl_get_attribute@ShadingContext@v1_14_4@OSL@@QEAA_NPEAUShaderGlobals@23@PEAXHVustringhash@OpenImageIO_v3_0@@2HHUTypeDesc@6@1@Z ?record_error@ShadingContext@v1_14_4@OSL@@QEBAXW4ErrCode@ErrorHandler@OpenImageIO_v3_0@@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z __imp_?record_error@ShadingContext@v1_14_4@OSL@@QEBAXW4ErrCode@ErrorHandler@OpenImageIO_v3_0@@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z ?process_errors@ShadingContext@v1_14_4@OSL@@QEBAXXZ __imp_?process_errors@ShadingContext@v1_14_4@OSL@@QEBAXXZ ?fromString@ColorSystem@pvt@v1_14_4@OSL@@SAPEBUChroma@1234@Vustringhash@OpenImageIO_v3_0@@@Z __imp_?fromString@ColorSystem@pvt@v1_14_4@OSL@@SAPEBUChroma@1234@Vustringhash@OpenImageIO_v3_0@@@Z ?blackbody_rgb@ColorSystem@pvt@v1_14_4@OSL@@QEBA?AV?$Color3@M@Imath_3_1@@M@Z __imp_?blackbody_rgb@ColorSystem@pvt@v1_14_4@OSL@@QEBA?AV?$Color3@M@Imath_3_1@@M@Z ?can_lookup_blackbody@ColorSystem@pvt@v1_14_4@OSL@@QEBA_NM@Z __imp_?can_lookup_blackbody@ColorSystem@pvt@v1_14_4@OSL@@QEBA_NM@Z ?lookup_blackbody_rgb@ColorSystem@pvt@v1_14_4@OSL@@QEBA?AV?$Color3@M@Imath_3_1@@M@Z __imp_?lookup_blackbody_rgb@ColorSystem@pvt@v1_14_4@OSL@@QEBA?AV?$Color3@M@Imath_3_1@@M@Z ?compute_blackbody_rgb@ColorSystem@pvt@v1_14_4@OSL@@QEBA?AV?$Color3@M@Imath_3_1@@M@Z __imp_?compute_blackbody_rgb@ColorSystem@pvt@v1_14_4@OSL@@QEBA?AV?$Color3@M@Imath_3_1@@M@Z ?set_colorspace@ColorSystem@pvt@v1_14_4@OSL@@QEAA_NVustringhash@OpenImageIO_v3_0@@@Z __imp_?set_colorspace@ColorSystem@pvt@v1_14_4@OSL@@QEAA_NVustringhash@OpenImageIO_v3_0@@@Z ?to_rgb@ColorSystem@pvt@v1_14_4@OSL@@QEBA?AV?$Color3@M@Imath_3_1@@Vustringhash@OpenImageIO_v3_0@@AEBV56@PEAVShadingContext@34@PEAUShaderGlobals@34@@Z __imp_?to_rgb@ColorSystem@pvt@v1_14_4@OSL@@QEBA?AV?$Color3@M@Imath_3_1@@Vustringhash@OpenImageIO_v3_0@@AEBV56@PEAVShadingContext@34@PEAUShaderGlobals@34@@Z ?from_rgb@ColorSystem@pvt@v1_14_4@OSL@@QEBA?AV?$Color3@M@Imath_3_1@@Vustringhash@OpenImageIO_v3_0@@AEBV56@PEAVShadingContext@34@PEAUShaderGlobals@34@@Z __imp_?from_rgb@ColorSystem@pvt@v1_14_4@OSL@@QEBA?AV?$Color3@M@Imath_3_1@@Vustringhash@OpenImageIO_v3_0@@AEBV56@PEAVShadingContext@34@PEAUShaderGlobals@34@@Z ?transformc@ColorSystem@pvt@v1_14_4@OSL@@QEBA?AV?$Dual@V?$Color3@M@Imath_3_1@@$01@34@Vustringhash@OpenImageIO_v3_0@@0AEBV534@PEAVShadingContext@34@PEAUShaderGlobals@34@@Z __imp_?transformc@ColorSystem@pvt@v1_14_4@OSL@@QEBA?AV?$Dual@V?$Color3@M@Imath_3_1@@$01@34@Vustringhash@OpenImageIO_v3_0@@0AEBV534@PEAVShadingContext@34@PEAUShaderGlobals@34@@Z ?transformc@ColorSystem@pvt@v1_14_4@OSL@@QEBA?AV?$Color3@M@Imath_3_1@@Vustringhash@OpenImageIO_v3_0@@0AEBV56@PEAVShadingContext@34@PEAUShaderGlobals@34@@Z __imp_?transformc@ColorSystem@pvt@v1_14_4@OSL@@QEBA?AV?$Color3@M@Imath_3_1@@Vustringhash@OpenImageIO_v3_0@@0AEBV56@PEAVShadingContext@34@PEAUShaderGlobals@34@@Z ?ocio_transform@ColorSystem@pvt@v1_14_4@OSL@@QEBA?AV?$Dual@V?$Color3@M@Imath_3_1@@$01@34@Vustringhash@OpenImageIO_v3_0@@0AEBV534@PEAVShadingContext@34@PEAUShaderGlobals@34@@Z __imp_?ocio_transform@ColorSystem@pvt@v1_14_4@OSL@@QEBA?AV?$Dual@V?$Color3@M@Imath_3_1@@$01@34@Vustringhash@OpenImageIO_v3_0@@0AEBV534@PEAVShadingContext@34@PEAUShaderGlobals@34@@Z ?ocio_transform@ColorSystem@pvt@v1_14_4@OSL@@QEBA?AV?$Color3@M@Imath_3_1@@Vustringhash@OpenImageIO_v3_0@@0AEBV56@PEAVShadingContext@34@PEAUShaderGlobals@34@@Z __imp_?ocio_transform@ColorSystem@pvt@v1_14_4@OSL@@QEBA?AV?$Color3@M@Imath_3_1@@Vustringhash@OpenImageIO_v3_0@@0AEBV56@PEAVShadingContext@34@PEAUShaderGlobals@34@@Z ?get_max_warnings_per_thread@pvt@v1_14_4@OSL@@YAHQEAX@Z __imp_?get_max_warnings_per_thread@pvt@v1_14_4@OSL@@YAHQEAX@Z ?pointcloud_search@RendererServices@v1_14_4@OSL@@UEAAHPEAUShaderGlobals@23@Vustringhash@OpenImageIO_v3_0@@AEBV?$Vec3@M@Imath_3_1@@MH_NPEAHPEAMH@Z __imp_?pointcloud_search@RendererServices@v1_14_4@OSL@@UEAAHPEAUShaderGlobals@23@Vustringhash@OpenImageIO_v3_0@@AEBV?$Vec3@M@Imath_3_1@@MH_NPEAHPEAMH@Z ?pointcloud_get@RendererServices@v1_14_4@OSL@@UEAAHPEAUShaderGlobals@23@Vustringhash@OpenImageIO_v3_0@@PEBHH1UTypeDesc@6@PEAX@Z __imp_?pointcloud_get@RendererServices@v1_14_4@OSL@@UEAAHPEAUShaderGlobals@23@Vustringhash@OpenImageIO_v3_0@@PEBHH1UTypeDesc@6@PEAX@Z ?pointcloud_write@RendererServices@v1_14_4@OSL@@UEAA_NPEAUShaderGlobals@23@Vustringhash@OpenImageIO_v3_0@@AEBV?$Vec3@M@Imath_3_1@@HPEBV56@PEBUTypeDesc@6@PEAPEBX@Z __imp_?pointcloud_write@RendererServices@v1_14_4@OSL@@UEAA_NPEAUShaderGlobals@23@Vustringhash@OpenImageIO_v3_0@@AEBV?$Vec3@M@Imath_3_1@@HPEBV56@PEBUTypeDesc@6@PEAPEBX@Z ??0RendererServices@v1_14_4@OSL@@QEAA@PEAVTextureSystem@OpenImageIO_v3_0@@@Z __imp_??0RendererServices@v1_14_4@OSL@@QEAA@PEAVTextureSystem@OpenImageIO_v3_0@@@Z ?get_inverse_matrix@RendererServices@v1_14_4@OSL@@UEAA_NPEAUShaderGlobals@23@AEAV?$Matrix44@M@Imath_3_1@@PEBXM@Z __imp_?get_inverse_matrix@RendererServices@v1_14_4@OSL@@UEAA_NPEAUShaderGlobals@23@AEAV?$Matrix44@M@Imath_3_1@@PEBXM@Z ?get_inverse_matrix@RendererServices@v1_14_4@OSL@@UEAA_NPEAUShaderGlobals@23@AEAV?$Matrix44@M@Imath_3_1@@PEBX@Z __imp_?get_inverse_matrix@RendererServices@v1_14_4@OSL@@UEAA_NPEAUShaderGlobals@23@AEAV?$Matrix44@M@Imath_3_1@@PEBX@Z ?get_matrix@RendererServices@v1_14_4@OSL@@UEAA_NPEAUShaderGlobals@23@AEAV?$Matrix44@M@Imath_3_1@@Vustringhash@OpenImageIO_v3_0@@M@Z __imp_?get_matrix@RendererServices@v1_14_4@OSL@@UEAA_NPEAUShaderGlobals@23@AEAV?$Matrix44@M@Imath_3_1@@Vustringhash@OpenImageIO_v3_0@@M@Z ?get_inverse_matrix@RendererServices@v1_14_4@OSL@@UEAA_NPEAUShaderGlobals@23@AEAV?$Matrix44@M@Imath_3_1@@Vustringhash@OpenImageIO_v3_0@@M@Z __imp_?get_inverse_matrix@RendererServices@v1_14_4@OSL@@UEAA_NPEAUShaderGlobals@23@AEAV?$Matrix44@M@Imath_3_1@@Vustringhash@OpenImageIO_v3_0@@M@Z ?get_matrix@RendererServices@v1_14_4@OSL@@UEAA_NPEAUShaderGlobals@23@AEAV?$Matrix44@M@Imath_3_1@@Vustringhash@OpenImageIO_v3_0@@@Z __imp_?get_matrix@RendererServices@v1_14_4@OSL@@UEAA_NPEAUShaderGlobals@23@AEAV?$Matrix44@M@Imath_3_1@@Vustringhash@OpenImageIO_v3_0@@@Z ?get_inverse_matrix@RendererServices@v1_14_4@OSL@@UEAA_NPEAUShaderGlobals@23@AEAV?$Matrix44@M@Imath_3_1@@Vustringhash@OpenImageIO_v3_0@@@Z __imp_?get_inverse_matrix@RendererServices@v1_14_4@OSL@@UEAA_NPEAUShaderGlobals@23@AEAV?$Matrix44@M@Imath_3_1@@Vustringhash@OpenImageIO_v3_0@@@Z ?transform_points@RendererServices@v1_14_4@OSL@@UEAA_NPEAUShaderGlobals@23@Vustringhash@OpenImageIO_v3_0@@1MPEBV?$Vec3@M@Imath_3_1@@PEAV78@HW4VECSEMANTICS@TypeDesc@6@@Z __imp_?transform_points@RendererServices@v1_14_4@OSL@@UEAA_NPEAUShaderGlobals@23@Vustringhash@OpenImageIO_v3_0@@1MPEBV?$Vec3@M@Imath_3_1@@PEAV78@HW4VECSEMANTICS@TypeDesc@6@@Z ?errorfmt@RendererServices@v1_14_4@OSL@@UEAAXPEAUShaderGlobals@23@Vustringhash@OpenImageIO_v3_0@@HPEBW4EncodedType@23@IPEAE@Z __imp_?errorfmt@RendererServices@v1_14_4@OSL@@UEAAXPEAUShaderGlobals@23@Vustringhash@OpenImageIO_v3_0@@HPEBW4EncodedType@23@IPEAE@Z ?warningfmt@RendererServices@v1_14_4@OSL@@UEAAXPEAUShaderGlobals@23@Vustringhash@OpenImageIO_v3_0@@HPEBW4EncodedType@23@IPEAE@Z __imp_?warningfmt@RendererServices@v1_14_4@OSL@@UEAAXPEAUShaderGlobals@23@Vustringhash@OpenImageIO_v3_0@@HPEBW4EncodedType@23@IPEAE@Z ?printfmt@RendererServices@v1_14_4@OSL@@UEAAXPEAUShaderGlobals@23@Vustringhash@OpenImageIO_v3_0@@HPEBW4EncodedType@23@IPEAE@Z __imp_?printfmt@RendererServices@v1_14_4@OSL@@UEAAXPEAUShaderGlobals@23@Vustringhash@OpenImageIO_v3_0@@HPEBW4EncodedType@23@IPEAE@Z ?filefmt@RendererServices@v1_14_4@OSL@@UEAAXPEAUShaderGlobals@23@Vustringhash@OpenImageIO_v3_0@@1HPEBW4EncodedType@23@IPEAE@Z __imp_?filefmt@RendererServices@v1_14_4@OSL@@UEAAXPEAUShaderGlobals@23@Vustringhash@OpenImageIO_v3_0@@1HPEBW4EncodedType@23@IPEAE@Z ?build_attribute_getter@RendererServices@v1_14_4@OSL@@UEAAXAEBVShaderGroup@23@_NPEBVustring@OpenImageIO_v3_0@@21PEBHUTypeDesc@6@1AEAV?$FunctionSpec@V?$ArgVariant@W4AttributeSpecBuiltinArg@v1_14_4@OSL@@@v1_14_4@OSL@@@23@@Z __imp_?build_attribute_getter@RendererServices@v1_14_4@OSL@@UEAAXAEBVShaderGroup@23@_NPEBVustring@OpenImageIO_v3_0@@21PEBHUTypeDesc@6@1AEAV?$FunctionSpec@V?$ArgVariant@W4AttributeSpecBuiltinArg@v1_14_4@OSL@@@v1_14_4@OSL@@@23@@Z ?get_attribute@RendererServices@v1_14_4@OSL@@UEAA_NPEAUShaderGlobals@23@_NVustringhash@OpenImageIO_v3_0@@UTypeDesc@6@2PEAX@Z __imp_?get_attribute@RendererServices@v1_14_4@OSL@@UEAA_NPEAUShaderGlobals@23@_NVustringhash@OpenImageIO_v3_0@@UTypeDesc@6@2PEAX@Z ?get_array_attribute@RendererServices@v1_14_4@OSL@@UEAA_NPEAUShaderGlobals@23@_NVustringhash@OpenImageIO_v3_0@@UTypeDesc@6@2HPEAX@Z __imp_?get_array_attribute@RendererServices@v1_14_4@OSL@@UEAA_NPEAUShaderGlobals@23@_NVustringhash@OpenImageIO_v3_0@@UTypeDesc@6@2HPEAX@Z ?build_interpolated_getter@RendererServices@v1_14_4@OSL@@UEAAXAEBVShaderGroup@23@AEBVustring@OpenImageIO_v3_0@@UTypeDesc@6@_NAEAV?$FunctionSpec@V?$ArgVariant@W4InterpolatedSpecBuiltinArg@v1_14_4@OSL@@@v1_14_4@OSL@@@23@@Z __imp_?build_interpolated_getter@RendererServices@v1_14_4@OSL@@UEAAXAEBVShaderGroup@23@AEBVustring@OpenImageIO_v3_0@@UTypeDesc@6@_NAEAV?$FunctionSpec@V?$ArgVariant@W4InterpolatedSpecBuiltinArg@v1_14_4@OSL@@@v1_14_4@OSL@@@23@@Z ?get_userdata@RendererServices@v1_14_4@OSL@@UEAA_N_NVustringhash@OpenImageIO_v3_0@@UTypeDesc@5@PEAUShaderGlobals@23@PEAX@Z __imp_?get_userdata@RendererServices@v1_14_4@OSL@@UEAA_N_NVustringhash@OpenImageIO_v3_0@@UTypeDesc@5@PEAUShaderGlobals@23@PEAX@Z ?get_texture_handle@RendererServices@v1_14_4@OSL@@UEAAPEAVTextureHandle@TextureSystem@OpenImageIO_v3_0@@Vustring@6@PEAVShadingContext@23@PEBVTextureOpt_v2@6@@Z __imp_?get_texture_handle@RendererServices@v1_14_4@OSL@@UEAAPEAVTextureHandle@TextureSystem@OpenImageIO_v3_0@@Vustring@6@PEAVShadingContext@23@PEBVTextureOpt_v2@6@@Z ?get_texture_handle@RendererServices@v1_14_4@OSL@@UEAAPEAVTextureHandle@TextureSystem@OpenImageIO_v3_0@@Vustringhash@6@PEAVShadingContext@23@PEBVTextureOpt_v2@6@@Z __imp_?get_texture_handle@RendererServices@v1_14_4@OSL@@UEAAPEAVTextureHandle@TextureSystem@OpenImageIO_v3_0@@Vustringhash@6@PEAVShadingContext@23@PEBVTextureOpt_v2@6@@Z ?good@RendererServices@v1_14_4@OSL@@UEAA_NPEAVTextureHandle@TextureSystem@OpenImageIO_v3_0@@@Z __imp_?good@RendererServices@v1_14_4@OSL@@UEAA_NPEAVTextureHandle@TextureSystem@OpenImageIO_v3_0@@@Z ?is_udim@RendererServices@v1_14_4@OSL@@UEAA_NPEAVTextureHandle@TextureSystem@OpenImageIO_v3_0@@@Z __imp_?is_udim@RendererServices@v1_14_4@OSL@@UEAA_NPEAVTextureHandle@TextureSystem@OpenImageIO_v3_0@@@Z ?texture@RendererServices@v1_14_4@OSL@@UEAA_NVustringhash@OpenImageIO_v3_0@@PEAVTextureHandle@TextureSystem@5@PEAVPerthread@75@AEAVTextureOpt_v2@5@PEAUShaderGlobals@23@MMMMMMHPEAM55PEAV45@@Z __imp_?texture@RendererServices@v1_14_4@OSL@@UEAA_NVustringhash@OpenImageIO_v3_0@@PEAVTextureHandle@TextureSystem@5@PEAVPerthread@75@AEAVTextureOpt_v2@5@PEAUShaderGlobals@23@MMMMMMHPEAM55PEAV45@@Z ?texture3d@RendererServices@v1_14_4@OSL@@UEAA_NVustringhash@OpenImageIO_v3_0@@PEAVTextureHandle@TextureSystem@5@PEAVPerthread@75@AEAVTextureOpt_v2@5@PEAUShaderGlobals@23@AEBV?$Vec3@M@Imath_3_1@@555HPEAM666PEAV45@@Z __imp_?texture3d@RendererServices@v1_14_4@OSL@@UEAA_NVustringhash@OpenImageIO_v3_0@@PEAVTextureHandle@TextureSystem@5@PEAVPerthread@75@AEAVTextureOpt_v2@5@PEAUShaderGlobals@23@AEBV?$Vec3@M@Imath_3_1@@555HPEAM666PEAV45@@Z ?environment@RendererServices@v1_14_4@OSL@@UEAA_NVustringhash@OpenImageIO_v3_0@@PEAVTextureHandle@TextureSystem@5@PEAVPerthread@75@AEAVTextureOpt_v2@5@PEAUShaderGlobals@23@AEBV?$Vec3@M@Imath_3_1@@55HPEAM66PEAV45@@Z __imp_?environment@RendererServices@v1_14_4@OSL@@UEAA_NVustringhash@OpenImageIO_v3_0@@PEAVTextureHandle@TextureSystem@5@PEAVPerthread@75@AEAVTextureOpt_v2@5@PEAUShaderGlobals@23@AEBV?$Vec3@M@Imath_3_1@@55HPEAM66PEAV45@@Z ?get_texture_info@RendererServices@v1_14_4@OSL@@UEAA_NVustringhash@OpenImageIO_v3_0@@PEAVTextureHandle@TextureSystem@5@PEAVPerthread@75@PEAUShaderGlobals@23@H0UTypeDesc@5@PEAXPEAV45@@Z __imp_?get_texture_info@RendererServices@v1_14_4@OSL@@UEAA_NVustringhash@OpenImageIO_v3_0@@PEAVTextureHandle@TextureSystem@5@PEAVPerthread@75@PEAUShaderGlobals@23@H0UTypeDesc@5@PEAXPEAV45@@Z ?get_texture_info@RendererServices@v1_14_4@OSL@@UEAA_NVustringhash@OpenImageIO_v3_0@@PEAVTextureHandle@TextureSystem@5@MMPEAVPerthread@75@PEAUShaderGlobals@23@H0UTypeDesc@5@PEAXPEAV45@@Z __imp_?get_texture_info@RendererServices@v1_14_4@OSL@@UEAA_NVustringhash@OpenImageIO_v3_0@@PEAVTextureHandle@TextureSystem@5@MMPEAVPerthread@75@PEAUShaderGlobals@23@H0UTypeDesc@5@PEAXPEAV45@@Z ?trace@RendererServices@v1_14_4@OSL@@UEAA_NAEAUTraceOpt@23@PEAUShaderGlobals@23@AEBV?$Vec3@M@Imath_3_1@@22222@Z __imp_?trace@RendererServices@v1_14_4@OSL@@UEAA_NAEAUTraceOpt@23@PEAUShaderGlobals@23@AEBV?$Vec3@M@Imath_3_1@@22222@Z ?getmessage@RendererServices@v1_14_4@OSL@@UEAA_NPEAUShaderGlobals@23@Vustringhash@OpenImageIO_v3_0@@1UTypeDesc@6@PEAX_N@Z __imp_?getmessage@RendererServices@v1_14_4@OSL@@UEAA_NPEAUShaderGlobals@23@Vustringhash@OpenImageIO_v3_0@@1UTypeDesc@6@PEAX_N@Z ?texturesys@RendererServices@v1_14_4@OSL@@UEBAPEAVTextureSystem@OpenImageIO_v3_0@@XZ __imp_?texturesys@RendererServices@v1_14_4@OSL@@UEBAPEAVTextureSystem@OpenImageIO_v3_0@@XZ ?batched@RendererServices@v1_14_4@OSL@@UEAAPEAV?$BatchedRendererServices@$0BA@@23@V?$WidthOf@$0BA@@23@@Z __imp_?batched@RendererServices@v1_14_4@OSL@@UEAAPEAV?$BatchedRendererServices@$0BA@@23@V?$WidthOf@$0BA@@23@@Z ?batched@RendererServices@v1_14_4@OSL@@UEAAPEAV?$BatchedRendererServices@$07@23@V?$WidthOf@$07@23@@Z __imp_?batched@RendererServices@v1_14_4@OSL@@UEAAPEAV?$BatchedRendererServices@$07@23@V?$WidthOf@$07@23@@Z ?batched@RendererServices@v1_14_4@OSL@@UEAAPEAV?$BatchedRendererServices@$03@23@V?$WidthOf@$03@23@@Z __imp_?batched@RendererServices@v1_14_4@OSL@@UEAAPEAV?$BatchedRendererServices@$03@23@V?$WidthOf@$03@23@@Z ?compileFrom@DfOptimizedAutomata@v1_14_4@OSL@@QEAAXAEBVDfAutomata@23@@Z __imp_?compileFrom@DfOptimizedAutomata@v1_14_4@OSL@@QEAAXAEBVDfAutomata@23@@Z ?getTransition@DfOptimizedAutomata@v1_14_4@OSL@@QEBAHHVustring@OpenImageIO_v3_0@@@Z __imp_?getTransition@DfOptimizedAutomata@v1_14_4@OSL@@QEBAHHVustring@OpenImageIO_v3_0@@@Z ?getRules@DfOptimizedAutomata@v1_14_4@OSL@@QEBAPEBQEAXHAEAH@Z __imp_?getRules@DfOptimizedAutomata@v1_14_4@OSL@@QEBAPEBQEAXHAEAH@Z ??0DfOptimizedAutomata@v1_14_4@OSL@@QEAA@XZ __imp_??0DfOptimizedAutomata@v1_14_4@OSL@@QEAA@XZ ??1DfOptimizedAutomata@v1_14_4@OSL@@QEAA@XZ __imp_??1DfOptimizedAutomata@v1_14_4@OSL@@QEAA@XZ ??0DfOptimizedAutomata@v1_14_4@OSL@@QEAA@AEBV012@@Z __imp_??0DfOptimizedAutomata@v1_14_4@OSL@@QEAA@AEBV012@@Z ??0DfOptimizedAutomata@v1_14_4@OSL@@QEAA@$$QEAV012@@Z __imp_??0DfOptimizedAutomata@v1_14_4@OSL@@QEAA@$$QEAV012@@Z ??4DfOptimizedAutomata@v1_14_4@OSL@@QEAAAEAV012@AEBV012@@Z __imp_??4DfOptimizedAutomata@v1_14_4@OSL@@QEAAAEAV012@AEBV012@@Z ??4DfOptimizedAutomata@v1_14_4@OSL@@QEAAAEAV012@$$QEAV012@@Z __imp_??4DfOptimizedAutomata@v1_14_4@OSL@@QEAAAEAV012@$$QEAV012@@Z ??0AccumRule@v1_14_4@OSL@@QEAA@H_N@Z __imp_??0AccumRule@v1_14_4@OSL@@QEAA@H_N@Z ?accum@AccumRule@v1_14_4@OSL@@QEBAXAEBV?$Color3@M@Imath_3_1@@AEAV?$vector@UAovOutput@v1_14_4@OSL@@V?$allocator@UAovOutput@v1_14_4@OSL@@@std@@@std@@@Z __imp_?accum@AccumRule@v1_14_4@OSL@@QEBAXAEBV?$Color3@M@Imath_3_1@@AEAV?$vector@UAovOutput@v1_14_4@OSL@@V?$allocator@UAovOutput@v1_14_4@OSL@@@std@@@std@@@Z ?toAlpha@AccumRule@v1_14_4@OSL@@QEBA_NXZ __imp_?toAlpha@AccumRule@v1_14_4@OSL@@QEBA_NXZ ?getOutputIndex@AccumRule@v1_14_4@OSL@@QEBAHXZ __imp_?getOutputIndex@AccumRule@v1_14_4@OSL@@QEBAHXZ ??4AccumRule@v1_14_4@OSL@@QEAAAEAV012@AEBV012@@Z __imp_??4AccumRule@v1_14_4@OSL@@QEAAAEAV012@AEBV012@@Z ??4AccumRule@v1_14_4@OSL@@QEAAAEAV012@$$QEAV012@@Z __imp_??4AccumRule@v1_14_4@OSL@@QEAAAEAV012@$$QEAV012@@Z ??1AccumAutomata@v1_14_4@OSL@@QEAA@XZ __imp_??1AccumAutomata@v1_14_4@OSL@@QEAA@XZ ?addEventType@AccumAutomata@v1_14_4@OSL@@QEAAXVustring@OpenImageIO_v3_0@@@Z __imp_?addEventType@AccumAutomata@v1_14_4@OSL@@QEAAXVustring@OpenImageIO_v3_0@@@Z ?addScatteringType@AccumAutomata@v1_14_4@OSL@@QEAAXVustring@OpenImageIO_v3_0@@@Z __imp_?addScatteringType@AccumAutomata@v1_14_4@OSL@@QEAAXVustring@OpenImageIO_v3_0@@@Z ?addRule@AccumAutomata@v1_14_4@OSL@@QEAAPEAVAccumRule@23@PEBDH_N@Z __imp_?addRule@AccumAutomata@v1_14_4@OSL@@QEAAPEAVAccumRule@23@PEBDH_N@Z ?compile@AccumAutomata@v1_14_4@OSL@@QEAAXXZ __imp_?compile@AccumAutomata@v1_14_4@OSL@@QEAAXXZ ?accum@AccumAutomata@v1_14_4@OSL@@QEBAXHAEBV?$Color3@M@Imath_3_1@@AEAV?$vector@UAovOutput@v1_14_4@OSL@@V?$allocator@UAovOutput@v1_14_4@OSL@@@std@@@std@@@Z __imp_?accum@AccumAutomata@v1_14_4@OSL@@QEBAXHAEBV?$Color3@M@Imath_3_1@@AEAV?$vector@UAovOutput@v1_14_4@OSL@@V?$allocator@UAovOutput@v1_14_4@OSL@@@std@@@std@@@Z ?getTransition@AccumAutomata@v1_14_4@OSL@@QEBAHHVustring@OpenImageIO_v3_0@@@Z __imp_?getTransition@AccumAutomata@v1_14_4@OSL@@QEBAHHVustring@OpenImageIO_v3_0@@@Z ?getRuleList@AccumAutomata@v1_14_4@OSL@@QEBAAEBV?$list@VAccumRule@v1_14_4@OSL@@V?$allocator@VAccumRule@v1_14_4@OSL@@@std@@@std@@XZ __imp_?getRuleList@AccumAutomata@v1_14_4@OSL@@QEBAAEBV?$list@VAccumRule@v1_14_4@OSL@@V?$allocator@VAccumRule@v1_14_4@OSL@@@std@@@std@@XZ ?getRulesInState@AccumAutomata@v1_14_4@OSL@@QEBAPEBQEAXHAEAH@Z __imp_?getRulesInState@AccumAutomata@v1_14_4@OSL@@QEBAPEBQEAXHAEAH@Z ??0AccumAutomata@v1_14_4@OSL@@QEAA@XZ __imp_??0AccumAutomata@v1_14_4@OSL@@QEAA@XZ ??0AccumAutomata@v1_14_4@OSL@@QEAA@AEBV012@@Z __imp_??0AccumAutomata@v1_14_4@OSL@@QEAA@AEBV012@@Z ??4AccumAutomata@v1_14_4@OSL@@QEAAAEAV012@AEBV012@@Z __imp_??4AccumAutomata@v1_14_4@OSL@@QEAAAEAV012@AEBV012@@Z ??0Accumulator@v1_14_4@OSL@@QEAA@PEBVAccumAutomata@12@@Z __imp_??0Accumulator@v1_14_4@OSL@@QEAA@PEBVAccumAutomata@12@@Z ?setAov@Accumulator@v1_14_4@OSL@@QEAAXHPEAVAov@23@_N1@Z __imp_?setAov@Accumulator@v1_14_4@OSL@@QEAAXHPEAVAov@23@_N1@Z ?broken@Accumulator@v1_14_4@OSL@@QEBA_NXZ __imp_?broken@Accumulator@v1_14_4@OSL@@QEBA_NXZ ?pushState@Accumulator@v1_14_4@OSL@@QEAAXXZ __imp_?pushState@Accumulator@v1_14_4@OSL@@QEAAXXZ ?popState@Accumulator@v1_14_4@OSL@@QEAAXXZ __imp_?popState@Accumulator@v1_14_4@OSL@@QEAAXXZ ?move@Accumulator@v1_14_4@OSL@@QEAAXVustring@OpenImageIO_v3_0@@@Z __imp_?move@Accumulator@v1_14_4@OSL@@QEAAXVustring@OpenImageIO_v3_0@@@Z ?move@Accumulator@v1_14_4@OSL@@QEAAXPEBVustring@OpenImageIO_v3_0@@@Z __imp_?move@Accumulator@v1_14_4@OSL@@QEAAXPEBVustring@OpenImageIO_v3_0@@@Z ?move@Accumulator@v1_14_4@OSL@@QEAAXVustring@OpenImageIO_v3_0@@0PEBV45@0@Z __imp_?move@Accumulator@v1_14_4@OSL@@QEAAXVustring@OpenImageIO_v3_0@@0PEBV45@0@Z ?test@Accumulator@v1_14_4@OSL@@QEAA_NVustring@OpenImageIO_v3_0@@0PEBV45@0@Z __imp_?test@Accumulator@v1_14_4@OSL@@QEAA_NVustring@OpenImageIO_v3_0@@0PEBV45@0@Z ?begin@Accumulator@v1_14_4@OSL@@QEAAXXZ __imp_?begin@Accumulator@v1_14_4@OSL@@QEAAXXZ ?end@Accumulator@v1_14_4@OSL@@QEAAXPEAX@Z __imp_?end@Accumulator@v1_14_4@OSL@@QEAAXPEAX@Z ?accum@Accumulator@v1_14_4@OSL@@QEAAXAEBV?$Color3@M@Imath_3_1@@@Z __imp_?accum@Accumulator@v1_14_4@OSL@@QEAAXAEBV?$Color3@M@Imath_3_1@@@Z ?getOutput@Accumulator@v1_14_4@OSL@@QEBAAEBUAovOutput@23@H@Z __imp_?getOutput@Accumulator@v1_14_4@OSL@@QEBAAEBUAovOutput@23@H@Z ??1Accumulator@v1_14_4@OSL@@QEAA@XZ __imp_??1Accumulator@v1_14_4@OSL@@QEAA@XZ ??0Accumulator@v1_14_4@OSL@@QEAA@AEBV012@@Z __imp_??0Accumulator@v1_14_4@OSL@@QEAA@AEBV012@@Z ??0Accumulator@v1_14_4@OSL@@QEAA@$$QEAV012@@Z __imp_??0Accumulator@v1_14_4@OSL@@QEAA@$$QEAV012@@Z ??4Accumulator@v1_14_4@OSL@@QEAAAEAV012@AEBV012@@Z __imp_??4Accumulator@v1_14_4@OSL@@QEAAAEAV012@AEBV012@@Z ??4Accumulator@v1_14_4@OSL@@QEAAAEAV012@$$QEAV012@@Z __imp_??4Accumulator@v1_14_4@OSL@@QEAAAEAV012@$$QEAV012@@Z ?shade_image@v1_14_4@OSL@@YA_NAEAVShadingSystem@12@AEAVShaderGroup@12@PEBUShaderGlobals@12@AEAVImageBuf@OpenImageIO_v3_0@@V?$span@$$CBVustring@OpenImageIO_v3_0@@$0?0@7@W4ShadeImageLocations@12@UROI@7@Vparopt@7@@Z __imp_?shade_image@v1_14_4@OSL@@YA_NAEAVShadingSystem@12@AEAVShaderGroup@12@PEBUShaderGlobals@12@AEAVImageBuf@OpenImageIO_v3_0@@V?$span@$$CBVustring@OpenImageIO_v3_0@@$0?0@7@W4ShadeImageLocations@12@UROI@7@Vparopt@7@@Z ?shade_image@v1_14_4@OSL@@YA_NAEAVShadingSystem@12@AEAVShaderGroup@12@PEBUShaderGlobals@12@AEAVImageBuf@OpenImageIO_v3_0@@V?$span@$$CBVustring@OpenImageIO_v3_0@@$0?0@7@W4ShadeImageLocations@12@UROI@7@Vparallel_options@7@@Z __imp_?shade_image@v1_14_4@OSL@@YA_NAEAVShadingSystem@12@AEAVShaderGroup@12@PEBUShaderGlobals@12@AEAVImageBuf@OpenImageIO_v3_0@@V?$span@$$CBVustring@OpenImageIO_v3_0@@$0?0@7@W4ShadeImageLocations@12@UROI@7@Vparallel_options@7@@Z ??1PerThreadInfo@LLVM_Util@pvt@v1_14_4@OSL@@QEAA@XZ __imp_??1PerThreadInfo@LLVM_Util@pvt@v1_14_4@OSL@@QEAA@XZ ?get@PerThreadInfo@LLVM_Util@pvt@v1_14_4@OSL@@AEBAPEAUImpl@12345@XZ __imp_?get@PerThreadInfo@LLVM_Util@pvt@v1_14_4@OSL@@AEBAPEAUImpl@12345@XZ ??0LLVM_Util@pvt@v1_14_4@OSL@@QEAA@AEBUPerThreadInfo@0123@HH@Z __imp_??0LLVM_Util@pvt@v1_14_4@OSL@@QEAA@AEBUPerThreadInfo@0123@HH@Z ??1LLVM_Util@pvt@v1_14_4@OSL@@QEAA@XZ __imp_??1LLVM_Util@pvt@v1_14_4@OSL@@QEAA@XZ ??0ScopedJitMemoryUser@LLVM_Util@pvt@v1_14_4@OSL@@QEAA@XZ __imp_??0ScopedJitMemoryUser@LLVM_Util@pvt@v1_14_4@OSL@@QEAA@XZ ??1ScopedJitMemoryUser@LLVM_Util@pvt@v1_14_4@OSL@@QEAA@XZ __imp_??1ScopedJitMemoryUser@LLVM_Util@pvt@v1_14_4@OSL@@QEAA@XZ ?ustring_rep@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXW4UstringRep@1234@@Z __imp_?ustring_rep@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXW4UstringRep@1234@@Z ?new_module@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVModule@llvm@@PEBD@Z __imp_?new_module@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVModule@llvm@@PEBD@Z ?module_from_bitcode@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVModule@llvm@@PEBD_KAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@PEAV78@@Z __imp_?module_from_bitcode@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVModule@llvm@@PEBD_KAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@PEAV78@@Z ?debug_is_enabled@LLVM_Util@pvt@v1_14_4@OSL@@QEBA_NXZ __imp_?debug_is_enabled@LLVM_Util@pvt@v1_14_4@OSL@@QEBA_NXZ ?debug_setup_compilation_unit@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEBD@Z __imp_?debug_setup_compilation_unit@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEBD@Z ?debug_push_function@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@Vustring@OpenImageIO_v3_0@@H@Z __imp_?debug_push_function@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@Vustring@OpenImageIO_v3_0@@H@Z ?debug_pop_function@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXXZ __imp_?debug_pop_function@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXXZ ?debug_push_inlined_function@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXVustring@OpenImageIO_v3_0@@0H@Z __imp_?debug_push_inlined_function@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXVustring@OpenImageIO_v3_0@@0H@Z ?debug_pop_inlined_function@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXXZ __imp_?debug_pop_inlined_function@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXXZ ?debug_set_location@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXVustring@OpenImageIO_v3_0@@H@Z __imp_?debug_set_location@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXVustring@OpenImageIO_v3_0@@H@Z ?make_function@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVFunction@llvm@@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@_NPEAVType@6@V?$span@QEAVType@llvm@@$0?0@OpenImageIO_v3_0@@1@Z __imp_?make_function@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVFunction@llvm@@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@_NPEAVType@6@V?$span@QEAVType@llvm@@$0?0@OpenImageIO_v3_0@@1@Z ?add_function_mapping@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVFunction@llvm@@PEAX@Z __imp_?add_function_mapping@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVFunction@llvm@@PEAX@Z ?current_function_arg@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@H@Z __imp_?current_function_arg@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@H@Z ?new_builder@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVBasicBlock@llvm@@@Z __imp_?new_builder@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVBasicBlock@llvm@@@Z ?end_builder@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXXZ __imp_?end_builder@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXXZ ?make_jit_execengine@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVExecutionEngine@llvm@@PEAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@W4TargetISA@234@_N2@Z __imp_?make_jit_execengine@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVExecutionEngine@llvm@@PEAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@W4TargetISA@234@_N2@Z ?supports_isa@LLVM_Util@pvt@v1_14_4@OSL@@SA_NW4TargetISA@234@@Z __imp_?supports_isa@LLVM_Util@pvt@v1_14_4@OSL@@SA_NW4TargetISA@234@@Z ?lookup_isa_by_name@LLVM_Util@pvt@v1_14_4@OSL@@SA?AW4TargetISA@234@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@@Z __imp_?lookup_isa_by_name@LLVM_Util@pvt@v1_14_4@OSL@@SA?AW4TargetISA@234@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@@Z ?target_isa_name@LLVM_Util@pvt@v1_14_4@OSL@@SAPEBDW4TargetISA@234@@Z __imp_?target_isa_name@LLVM_Util@pvt@v1_14_4@OSL@@SAPEBDW4TargetISA@234@@Z ?add_global_mapping@LLVM_Util@pvt@v1_14_4@OSL@@SAXPEBDPEAX@Z __imp_?add_global_mapping@LLVM_Util@pvt@v1_14_4@OSL@@SAXPEBDPEAX@Z ?validate_global_mappings@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXAEAV?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@@Z __imp_?validate_global_mappings@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXAEAV?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@@Z ?detect_cpu_features@LLVM_Util@pvt@v1_14_4@OSL@@QEAA_NW4TargetISA@234@_N@Z __imp_?detect_cpu_features@LLVM_Util@pvt@v1_14_4@OSL@@QEAA_NW4TargetISA@234@_N@Z ?dump_struct_data_layout@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVType@llvm@@@Z __imp_?dump_struct_data_layout@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVType@llvm@@@Z ?validate_struct_data_layout@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVType@llvm@@AEBV?$vector@IV?$allocator@I@std@@@std@@@Z __imp_?validate_struct_data_layout@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVType@llvm@@AEBV?$vector@IV?$allocator@I@std@@@std@@@Z ?execengine@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVExecutionEngine@llvm@@@Z __imp_?execengine@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVExecutionEngine@llvm@@@Z ?nvptx_target_machine@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVTargetMachine@llvm@@XZ __imp_?nvptx_target_machine@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVTargetMachine@llvm@@XZ ?prune_and_internalize_module@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXV?$unordered_set@PEAVFunction@llvm@@U?$hash@PEAVFunction@llvm@@@std@@U?$equal_to@PEAVFunction@llvm@@@4@V?$allocator@PEAVFunction@llvm@@@4@@std@@W4Linkage@1234@PEAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@6@@Z __imp_?prune_and_internalize_module@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXV?$unordered_set@PEAVFunction@llvm@@U?$hash@PEAVFunction@llvm@@@std@@U?$equal_to@PEAVFunction@llvm@@@4@V?$allocator@PEAVFunction@llvm@@@4@@std@@W4Linkage@1234@PEAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@6@@Z ?internalize_module_functions@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEBV?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@6@1@Z __imp_?internalize_module_functions@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEBV?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@6@1@Z ?setup_optimization_passes@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXH_N@Z __imp_?setup_optimization_passes@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXH_N@Z ?do_optimize@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z __imp_?do_optimize@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z ?getPointerToFunction@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAXPEAVFunction@llvm@@@Z __imp_?getPointerToFunction@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAXPEAVFunction@llvm@@@Z ?InstallLazyFunctionCreator@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXP6APEAXAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z@Z __imp_?InstallLazyFunctionCreator@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXP6APEAXAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z@Z ?new_basic_block@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVBasicBlock@llvm@@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z __imp_?new_basic_block@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVBasicBlock@llvm@@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z ?push_function@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVBasicBlock@llvm@@PEAV56@@Z __imp_?push_function@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVBasicBlock@llvm@@PEAV56@@Z ?pop_function@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXXZ __imp_?pop_function@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXXZ ?inside_function@LLVM_Util@pvt@v1_14_4@OSL@@QEBA_NXZ __imp_?inside_function@LLVM_Util@pvt@v1_14_4@OSL@@QEBA_NXZ ?push_function_mask@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVValue@llvm@@@Z __imp_?push_function_mask@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVValue@llvm@@@Z ?pop_function_mask@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXXZ __imp_?pop_function_mask@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXXZ ?push_masked_loop@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVValue@llvm@@0@Z __imp_?push_masked_loop@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVValue@llvm@@0@Z ?pop_masked_loop@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXXZ __imp_?pop_masked_loop@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXXZ ?is_innermost_loop_masked@LLVM_Util@pvt@v1_14_4@OSL@@QEBA_NXZ __imp_?is_innermost_loop_masked@LLVM_Util@pvt@v1_14_4@OSL@@QEBA_NXZ ?push_shader_instance@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVValue@llvm@@@Z __imp_?push_shader_instance@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVValue@llvm@@@Z ?pop_shader_instance@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXXZ __imp_?pop_shader_instance@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXXZ ?masked_exit_count@LLVM_Util@pvt@v1_14_4@OSL@@QEBAHXZ __imp_?masked_exit_count@LLVM_Util@pvt@v1_14_4@OSL@@QEBAHXZ ?masked_return_count@LLVM_Util@pvt@v1_14_4@OSL@@QEBAHXZ __imp_?masked_return_count@LLVM_Util@pvt@v1_14_4@OSL@@QEBAHXZ ?masked_break_count@LLVM_Util@pvt@v1_14_4@OSL@@QEBAHXZ __imp_?masked_break_count@LLVM_Util@pvt@v1_14_4@OSL@@QEBAHXZ ?masked_continue_count@LLVM_Util@pvt@v1_14_4@OSL@@QEBAHXZ __imp_?masked_continue_count@LLVM_Util@pvt@v1_14_4@OSL@@QEBAHXZ ?push_mask@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVValue@llvm@@_N1@Z __imp_?push_mask@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVValue@llvm@@_N1@Z ?pop_mask@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXXZ __imp_?pop_mask@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXXZ ?apply_exit_to_mask_stack@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXXZ __imp_?apply_exit_to_mask_stack@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXXZ ?apply_return_to_mask_stack@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXXZ __imp_?apply_return_to_mask_stack@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXXZ ?apply_break_to_mask_stack@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXXZ __imp_?apply_break_to_mask_stack@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXXZ ?apply_continue_to_mask_stack@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXXZ __imp_?apply_continue_to_mask_stack@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXXZ ?current_mask@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@XZ __imp_?current_mask@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@XZ ?apply_return_to@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z __imp_?apply_return_to@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z ?shader_mask@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@XZ __imp_?shader_mask@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@XZ ?op_masked_break@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXXZ __imp_?op_masked_break@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXXZ ?op_masked_continue@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXXZ __imp_?op_masked_continue@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXXZ ?op_masked_exit@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXXZ __imp_?op_masked_exit@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXXZ ?op_masked_return@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXXZ __imp_?op_masked_return@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXXZ ?push_masked_return_block@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVBasicBlock@llvm@@@Z __imp_?push_masked_return_block@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVBasicBlock@llvm@@@Z ?pop_masked_return_block@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXXZ __imp_?pop_masked_return_block@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXXZ ?has_masked_return_block@LLVM_Util@pvt@v1_14_4@OSL@@QEBA_NXZ __imp_?has_masked_return_block@LLVM_Util@pvt@v1_14_4@OSL@@QEBA_NXZ ?masked_return_block@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVBasicBlock@llvm@@XZ __imp_?masked_return_block@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVBasicBlock@llvm@@XZ ?return_block@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVBasicBlock@llvm@@XZ __imp_?return_block@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVBasicBlock@llvm@@XZ ?push_loop@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVBasicBlock@llvm@@0@Z __imp_?push_loop@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVBasicBlock@llvm@@0@Z ?pop_loop@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXXZ __imp_?pop_loop@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXXZ ?loop_step_block@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVBasicBlock@llvm@@XZ __imp_?loop_step_block@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVBasicBlock@llvm@@XZ ?loop_after_block@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVBasicBlock@llvm@@XZ __imp_?loop_after_block@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVBasicBlock@llvm@@XZ ?llvm_type@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVType@llvm@@AEBUTypeDesc@OpenImageIO_v3_0@@@Z __imp_?llvm_type@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVType@llvm@@AEBUTypeDesc@OpenImageIO_v3_0@@@Z ?llvm_vector_type@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVType@llvm@@AEBUTypeDesc@OpenImageIO_v3_0@@@Z __imp_?llvm_vector_type@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVType@llvm@@AEBUTypeDesc@OpenImageIO_v3_0@@@Z ?llvm_vector_type@LLVM_Util@pvt@v1_14_4@OSL@@SAPEAVVectorType@llvm@@PEAVType@6@I@Z __imp_?llvm_vector_type@LLVM_Util@pvt@v1_14_4@OSL@@SAPEAVVectorType@llvm@@PEAVType@6@I@Z ?type_union@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVType@llvm@@V?$span@QEAVType@llvm@@$0?0@OpenImageIO_v3_0@@@Z __imp_?type_union@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVType@llvm@@V?$span@QEAVType@llvm@@$0?0@OpenImageIO_v3_0@@@Z ?type_struct@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVType@llvm@@V?$span@QEAVType@llvm@@$0?0@OpenImageIO_v3_0@@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@_N@Z __imp_?type_struct@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVType@llvm@@V?$span@QEAVType@llvm@@$0?0@OpenImageIO_v3_0@@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@_N@Z ?type_struct_field_at_index@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVType@llvm@@PEAV56@H@Z __imp_?type_struct_field_at_index@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVType@llvm@@PEAV56@H@Z ?type_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVPointerType@llvm@@PEAVType@6@@Z __imp_?type_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVPointerType@llvm@@PEAVType@6@@Z ?type_wide@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVType@llvm@@PEAV56@@Z __imp_?type_wide@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVType@llvm@@PEAV56@@Z ?type_array@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVType@llvm@@PEAV56@H@Z __imp_?type_array@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVType@llvm@@PEAV56@H@Z ?is_type_array@LLVM_Util@pvt@v1_14_4@OSL@@QEAA_NPEAVType@llvm@@@Z __imp_?is_type_array@LLVM_Util@pvt@v1_14_4@OSL@@QEAA_NPEAVType@llvm@@@Z ?element_type_of@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVType@llvm@@PEAV56@@Z __imp_?element_type_of@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVType@llvm@@PEAV56@@Z ?type_function@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVFunctionType@llvm@@PEAVType@6@V?$span@QEAVType@llvm@@$0?0@OpenImageIO_v3_0@@_N@Z __imp_?type_function@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVFunctionType@llvm@@PEAVType@6@V?$span@QEAVType@llvm@@$0?0@OpenImageIO_v3_0@@_N@Z ?type_function_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVPointerType@llvm@@PEAVType@6@V?$span@QEAVType@llvm@@$0?0@OpenImageIO_v3_0@@_N@Z __imp_?type_function_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVPointerType@llvm@@PEAVType@6@V?$span@QEAVType@llvm@@$0?0@OpenImageIO_v3_0@@_N@Z ?llvm_typename@LLVM_Util@pvt@v1_14_4@OSL@@QEBA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@PEAVType@llvm@@@Z __imp_?llvm_typename@LLVM_Util@pvt@v1_14_4@OSL@@QEBA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@PEAVType@llvm@@@Z ?llvm_typeof@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@PEAVValue@6@@Z __imp_?llvm_typeof@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@PEAVValue@6@@Z ?llvm_sizeof@LLVM_Util@pvt@v1_14_4@OSL@@QEBA_KPEAVType@llvm@@@Z __imp_?llvm_sizeof@LLVM_Util@pvt@v1_14_4@OSL@@QEBA_KPEAVType@llvm@@@Z ?llvm_alignmentof@LLVM_Util@pvt@v1_14_4@OSL@@QEBA_KPEAVType@llvm@@@Z __imp_?llvm_alignmentof@LLVM_Util@pvt@v1_14_4@OSL@@QEBA_KPEAVType@llvm@@@Z ?llvm_typenameof@LLVM_Util@pvt@v1_14_4@OSL@@QEBA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@PEAVValue@llvm@@@Z __imp_?llvm_typenameof@LLVM_Util@pvt@v1_14_4@OSL@@QEBA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@PEAVValue@llvm@@@Z ?constant@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@M@Z __imp_?constant@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@M@Z ?constant64@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@N@Z __imp_?constant64@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@N@Z ?constant@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@H@Z __imp_?constant@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@H@Z ?constant@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@I@Z __imp_?constant@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@I@Z ?constant4@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@C@Z __imp_?constant4@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@C@Z ?constant4@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@E@Z __imp_?constant4@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@E@Z ?constant8@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@C@Z __imp_?constant8@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@C@Z ?constant8@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@E@Z __imp_?constant8@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@E@Z ?constant16@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@F@Z __imp_?constant16@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@F@Z ?constant16@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@G@Z __imp_?constant16@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@G@Z ?constant64@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@_K@Z __imp_?constant64@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@_K@Z ?constant128@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@_K@Z __imp_?constant128@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@_K@Z ?constant128@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@_K0@Z __imp_?constant128@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@_K0@Z ?constanti64@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@_J@Z __imp_?constanti64@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@_J@Z ?constant@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@_K@Z __imp_?constant@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@_K@Z ?constant_bool@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@_N@Z __imp_?constant_bool@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@_N@Z ?constant_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAXPEAVPointerType@6@@Z __imp_?constant_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAXPEAVPointerType@6@@Z ?constant@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@Vustring@OpenImageIO_v3_0@@@Z __imp_?constant@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@Vustring@OpenImageIO_v3_0@@@Z ?constant_array@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@V?$span@QEAVConstant@llvm@@$0?0@OpenImageIO_v3_0@@@Z __imp_?constant_array@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@V?$span@QEAVConstant@llvm@@$0?0@OpenImageIO_v3_0@@@Z ?create_global_constant@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVGlobalVariable@llvm@@PEAVConstant@6@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z __imp_?create_global_constant@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVGlobalVariable@llvm@@PEAVConstant@6@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z ?llvm_mask_to_native@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z __imp_?llvm_mask_to_native@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z ?native_to_llvm_mask@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z __imp_?native_to_llvm_mask@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z ?mask_as_int@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z __imp_?mask_as_int@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z ?mask_as_int8@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z __imp_?mask_as_int8@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z ?mask4_as_int8@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z __imp_?mask4_as_int8@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z ?mask_as_int16@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z __imp_?mask_as_int16@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z ?int_as_mask@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z __imp_?int_as_mask@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z ?test_if_mask_is_non_zero@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z __imp_?test_if_mask_is_non_zero@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z ?test_mask_lane@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@H@Z __imp_?test_mask_lane@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@H@Z ?test_mask_lane@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@0@Z __imp_?test_mask_lane@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@0@Z ?widen_value@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z __imp_?widen_value@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z ?negate_mask@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z __imp_?negate_mask@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z ?constant@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@AEBUTypeDesc@OpenImageIO_v3_0@@@Z __imp_?constant@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@AEBUTypeDesc@OpenImageIO_v3_0@@@Z ?wide_constant@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@M@Z __imp_?wide_constant@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@M@Z ?wide_constant@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@H@Z __imp_?wide_constant@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@H@Z ?wide_constant@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@_K@Z __imp_?wide_constant@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@_K@Z ?wide_constant_bool@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@_N@Z __imp_?wide_constant_bool@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@_N@Z ?wide_constant@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@Vustring@OpenImageIO_v3_0@@@Z __imp_?wide_constant@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@Vustring@OpenImageIO_v3_0@@@Z ?wide_constant@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@PEAV56@@Z __imp_?wide_constant@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@PEAV56@@Z ?wide_constant@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@HH@Z __imp_?wide_constant@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@HH@Z ?wide_constant@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@HM@Z __imp_?wide_constant@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@HM@Z ?void_ptr_null@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@XZ __imp_?void_ptr_null@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@XZ ?ptr_cast@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@PEAVType@6@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z __imp_?ptr_cast@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@PEAVType@6@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z ?ptr_to_cast@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@PEAVType@6@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z __imp_?ptr_to_cast@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@PEAVType@6@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z ?ptr_cast@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@AEBUTypeDesc@OpenImageIO_v3_0@@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z __imp_?ptr_cast@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@AEBUTypeDesc@OpenImageIO_v3_0@@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z ?wide_ptr_cast@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@AEBUTypeDesc@OpenImageIO_v3_0@@@Z __imp_?wide_ptr_cast@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@AEBUTypeDesc@OpenImageIO_v3_0@@@Z ?int_to_ptr_cast@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z __imp_?int_to_ptr_cast@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z ?ptr_to_int64_cast@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z __imp_?ptr_to_int64_cast@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z ?void_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z __imp_?void_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z ?offset_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@HPEAVType@6@@Z __imp_?offset_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@HPEAVType@6@@Z ?offset_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@0PEAVType@6@@Z __imp_?offset_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@0PEAVType@6@@Z ?assume_ptr_is_aligned@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVValue@llvm@@I@Z __imp_?assume_ptr_is_aligned@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVValue@llvm@@I@Z ?op_alloca@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAVType@6@HAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@H@Z __imp_?op_alloca@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAVType@6@HAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@H@Z ?op_alloca@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@AEBUTypeDesc@OpenImageIO_v3_0@@HAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@H@Z __imp_?op_alloca@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@AEBUTypeDesc@OpenImageIO_v3_0@@HAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@H@Z ?wide_op_alloca@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@AEBUTypeDesc@OpenImageIO_v3_0@@HAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@H@Z __imp_?wide_op_alloca@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@AEBUTypeDesc@OpenImageIO_v3_0@@HAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@H@Z ?call_function@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@V?$span@QEAVValue@llvm@@$0?0@OpenImageIO_v3_0@@@Z __imp_?call_function@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@V?$span@QEAVValue@llvm@@$0?0@OpenImageIO_v3_0@@@Z ?call_function@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEBDV?$span@QEAVValue@llvm@@$0?0@OpenImageIO_v3_0@@@Z __imp_?call_function@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEBDV?$span@QEAVValue@llvm@@$0?0@OpenImageIO_v3_0@@@Z ?mark_fast_func_call@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVValue@llvm@@@Z __imp_?mark_fast_func_call@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVValue@llvm@@@Z ?set_insert_point@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVBasicBlock@llvm@@@Z __imp_?set_insert_point@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVBasicBlock@llvm@@@Z ?op_return@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVValue@llvm@@@Z __imp_?op_return@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVValue@llvm@@@Z ?op_branch@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVBasicBlock@llvm@@@Z __imp_?op_branch@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVBasicBlock@llvm@@@Z ?op_branch@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVValue@llvm@@PEAVBasicBlock@6@1@Z __imp_?op_branch@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVValue@llvm@@PEAVBasicBlock@6@1@Z ?op_memset@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVValue@llvm@@HHH@Z __imp_?op_memset@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVValue@llvm@@HHH@Z ?op_memset@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVValue@llvm@@H0H@Z __imp_?op_memset@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVValue@llvm@@H0H@Z ?op_memcpy@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVValue@llvm@@0HH@Z __imp_?op_memcpy@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVValue@llvm@@0HH@Z ?op_memcpy@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVValue@llvm@@H0HH@Z __imp_?op_memcpy@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVValue@llvm@@H0HH@Z ?op_load@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAVType@6@PEAV56@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z __imp_?op_load@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAVType@6@PEAV56@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z ?op_gather@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAVType@6@PEAV56@1@Z __imp_?op_gather@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAVType@6@PEAV56@1@Z ?op_store@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVValue@llvm@@0@Z __imp_?op_store@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVValue@llvm@@0@Z ?op_unmasked_store@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVValue@llvm@@0@Z __imp_?op_unmasked_store@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVValue@llvm@@0@Z ?op_load_mask@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z __imp_?op_load_mask@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z ?op_store_mask@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVValue@llvm@@0@Z __imp_?op_store_mask@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVValue@llvm@@0@Z ?op_scatter@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVValue@llvm@@PEAVType@6@00@Z __imp_?op_scatter@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVValue@llvm@@PEAVType@6@00@Z ?GEP@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAVType@6@PEAV56@1AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z __imp_?GEP@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAVType@6@PEAV56@1AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z ?GEP@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAVType@6@PEAV56@HAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z __imp_?GEP@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAVType@6@PEAV56@HAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z ?GEP@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAVType@6@PEAV56@HHAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z __imp_?GEP@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAVType@6@PEAV56@HHAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z ?GEP@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAVType@6@PEAV56@HHHAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z __imp_?GEP@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAVType@6@PEAV56@HHHAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z ?op_add@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@0@Z __imp_?op_add@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@0@Z ?op_sub@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@0@Z __imp_?op_sub@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@0@Z ?op_neg@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z __imp_?op_neg@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z ?op_mul@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@0@Z __imp_?op_mul@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@0@Z ?op_div@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@0@Z __imp_?op_div@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@0@Z ?op_mod@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@0@Z __imp_?op_mod@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@0@Z ?op_int8_to_int@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z __imp_?op_int8_to_int@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z ?op_int_to_int8@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z __imp_?op_int_to_int8@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z ?op_float_to_int@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z __imp_?op_float_to_int@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z ?op_int_to_float@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z __imp_?op_int_to_float@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z ?op_bool_to_int@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z __imp_?op_bool_to_int@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z ?op_bool_to_float@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z __imp_?op_bool_to_float@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z ?op_int_to_bool@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z __imp_?op_int_to_bool@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z ?op_float_to_double@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z __imp_?op_float_to_double@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z ?op_int_to_longlong@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z __imp_?op_int_to_longlong@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z ?op_and@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@0@Z __imp_?op_and@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@0@Z ?op_or@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@0@Z __imp_?op_or@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@0@Z ?op_xor@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@0@Z __imp_?op_xor@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@0@Z ?op_shl@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@0@Z __imp_?op_shl@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@0@Z ?op_shr@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@0@Z __imp_?op_shr@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@0@Z ?op_not@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z __imp_?op_not@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z ?op_select@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@00@Z __imp_?op_select@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@00@Z ?op_zero_if@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@0@Z __imp_?op_zero_if@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@0@Z ?op_1st_active_lane_of@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z __imp_?op_1st_active_lane_of@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z ?op_lanes_that_match_masked@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@00@Z __imp_?op_lanes_that_match_masked@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@00@Z ?op_extract@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@H@Z __imp_?op_extract@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@H@Z ?op_extract@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@0@Z __imp_?op_extract@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@0@Z ?op_insert@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@0H@Z __imp_?op_insert@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@0H@Z ?op_eq@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@0_N@Z __imp_?op_eq@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@0_N@Z ?op_ne@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@0_N@Z __imp_?op_ne@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@0_N@Z ?op_gt@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@0_N@Z __imp_?op_gt@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@0_N@Z ?op_lt@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@0_N@Z __imp_?op_lt@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@0_N@Z ?op_ge@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@0_N@Z __imp_?op_ge@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@0_N@Z ?op_le@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@0_N@Z __imp_?op_le@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@0_N@Z ?op_fabs@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z __imp_?op_fabs@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z ?op_is_not_finite@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z __imp_?op_is_not_finite@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z ?write_bitcode_file@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEBDPEAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z __imp_?write_bitcode_file@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEBDPEAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z ?absorb_module@LLVM_Util@pvt@v1_14_4@OSL@@QEAA_NV?$unique_ptr@VModule@llvm@@U?$default_delete@VModule@llvm@@@std@@@std@@@Z __imp_?absorb_module@LLVM_Util@pvt@v1_14_4@OSL@@QEAA_NV?$unique_ptr@VModule@llvm@@U?$default_delete@VModule@llvm@@@std@@@std@@@Z ?ptx_compile_group@LLVM_Util@pvt@v1_14_4@OSL@@QEAA_NPEAVModule@llvm@@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAV78@@Z __imp_?ptx_compile_group@LLVM_Util@pvt@v1_14_4@OSL@@QEAA_NPEAVModule@llvm@@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAV78@@Z ?bitcode_string@LLVM_Util@pvt@v1_14_4@OSL@@QEAA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@PEAVModule@llvm@@@Z __imp_?bitcode_string@LLVM_Util@pvt@v1_14_4@OSL@@QEAA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@PEAVModule@llvm@@@Z ?bitcode_string@LLVM_Util@pvt@v1_14_4@OSL@@QEAA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@PEAVFunction@llvm@@@Z __imp_?bitcode_string@LLVM_Util@pvt@v1_14_4@OSL@@QEAA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@PEAVFunction@llvm@@@Z ?module_string@LLVM_Util@pvt@v1_14_4@OSL@@QEAA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ __imp_?module_string@LLVM_Util@pvt@v1_14_4@OSL@@QEAA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ ?delete_func_body@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVFunction@llvm@@@Z __imp_?delete_func_body@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVFunction@llvm@@@Z ?func_is_empty@LLVM_Util@pvt@v1_14_4@OSL@@QEAA_NPEAVFunction@llvm@@@Z __imp_?func_is_empty@LLVM_Util@pvt@v1_14_4@OSL@@QEAA_NPEAVFunction@llvm@@@Z ?func_name@LLVM_Util@pvt@v1_14_4@OSL@@QEAA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@PEAVFunction@llvm@@@Z __imp_?func_name@LLVM_Util@pvt@v1_14_4@OSL@@QEAA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@PEAVFunction@llvm@@@Z ?total_jit_memory_held@LLVM_Util@pvt@v1_14_4@OSL@@SA_KXZ __imp_?total_jit_memory_held@LLVM_Util@pvt@v1_14_4@OSL@@SA_KXZ ?SetupLLVM@LLVM_Util@pvt@v1_14_4@OSL@@AEAAXXZ __imp_?SetupLLVM@LLVM_Util@pvt@v1_14_4@OSL@@AEAAXXZ ?builder@LLVM_Util@pvt@v1_14_4@OSL@@AEAAAEAVIRBuilder@1234@XZ __imp_?builder@LLVM_Util@pvt@v1_14_4@OSL@@AEAAAEAVIRBuilder@1234@XZ ?getOrCreateDebugFileFor@LLVM_Util@pvt@v1_14_4@OSL@@AEAAPEAVDIFile@llvm@@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z __imp_?getOrCreateDebugFileFor@LLVM_Util@pvt@v1_14_4@OSL@@AEAAPEAVDIFile@llvm@@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z ?getCurrentDebugScope@LLVM_Util@pvt@v1_14_4@OSL@@AEBAPEAVDIScope@llvm@@XZ __imp_?getCurrentDebugScope@LLVM_Util@pvt@v1_14_4@OSL@@AEBAPEAVDIScope@llvm@@XZ ?getCurrentInliningSite@LLVM_Util@pvt@v1_14_4@OSL@@AEBAPEAVDILocation@llvm@@XZ __imp_?getCurrentInliningSite@LLVM_Util@pvt@v1_14_4@OSL@@AEBAPEAVDILocation@llvm@@XZ ?op_linearize_16x_indices@LLVM_Util@pvt@v1_14_4@OSL@@AEAAPEAVValue@llvm@@PEAV56@@Z __imp_?op_linearize_16x_indices@LLVM_Util@pvt@v1_14_4@OSL@@AEAAPEAVValue@llvm@@PEAV56@@Z ?op_linearize_8x_indices@LLVM_Util@pvt@v1_14_4@OSL@@AEAAPEAVValue@llvm@@PEAV56@@Z __imp_?op_linearize_8x_indices@LLVM_Util@pvt@v1_14_4@OSL@@AEAAPEAVValue@llvm@@PEAV56@@Z ?op_linearize_4x_indices@LLVM_Util@pvt@v1_14_4@OSL@@AEAAPEAVValue@llvm@@PEAV56@@Z __imp_?op_linearize_4x_indices@LLVM_Util@pvt@v1_14_4@OSL@@AEAAPEAVValue@llvm@@PEAV56@@Z ?op_split_16x@LLVM_Util@pvt@v1_14_4@OSL@@AEAA?AV?$array@PEAVValue@llvm@@$01@std@@PEAVValue@llvm@@@Z __imp_?op_split_16x@LLVM_Util@pvt@v1_14_4@OSL@@AEAA?AV?$array@PEAVValue@llvm@@$01@std@@PEAVValue@llvm@@@Z ?op_split_8x@LLVM_Util@pvt@v1_14_4@OSL@@AEAA?AV?$array@PEAVValue@llvm@@$01@std@@PEAVValue@llvm@@@Z __imp_?op_split_8x@LLVM_Util@pvt@v1_14_4@OSL@@AEAA?AV?$array@PEAVValue@llvm@@$01@std@@PEAVValue@llvm@@@Z ?op_quarter_16x@LLVM_Util@pvt@v1_14_4@OSL@@AEAA?AV?$array@PEAVValue@llvm@@$03@std@@PEAVValue@llvm@@@Z __imp_?op_quarter_16x@LLVM_Util@pvt@v1_14_4@OSL@@AEAA?AV?$array@PEAVValue@llvm@@$03@std@@PEAVValue@llvm@@@Z ?op_combine_8x_vectors@LLVM_Util@pvt@v1_14_4@OSL@@AEAAPEAVValue@llvm@@PEAV56@0@Z __imp_?op_combine_8x_vectors@LLVM_Util@pvt@v1_14_4@OSL@@AEAAPEAVValue@llvm@@PEAV56@0@Z ?op_combine_4x_vectors@LLVM_Util@pvt@v1_14_4@OSL@@AEAAPEAVValue@llvm@@PEAV56@0@Z __imp_?op_combine_4x_vectors@LLVM_Util@pvt@v1_14_4@OSL@@AEAAPEAVValue@llvm@@PEAV56@0@Z ?setup_legacy_optimization_passes@LLVM_Util@pvt@v1_14_4@OSL@@AEAAXH_N@Z __imp_?setup_legacy_optimization_passes@LLVM_Util@pvt@v1_14_4@OSL@@AEAAXH_N@Z ?setup_new_optimization_passes@LLVM_Util@pvt@v1_14_4@OSL@@AEAAXH_N@Z __imp_?setup_new_optimization_passes@LLVM_Util@pvt@v1_14_4@OSL@@AEAAXH_N@Z ??1Reporter@journal@v1_14_4@OSL@@UEAA@XZ __imp_??1Reporter@journal@v1_14_4@OSL@@UEAA@XZ ??0Reporter@journal@v1_14_4@OSL@@QEAA@XZ __imp_??0Reporter@journal@v1_14_4@OSL@@QEAA@XZ ??0Reporter@journal@v1_14_4@OSL@@QEAA@AEBV0123@@Z __imp_??0Reporter@journal@v1_14_4@OSL@@QEAA@AEBV0123@@Z ??4Reporter@journal@v1_14_4@OSL@@QEAAAEAV0123@AEBV0123@@Z __imp_??4Reporter@journal@v1_14_4@OSL@@QEAAAEAV0123@AEBV0123@@Z ??1TrackRecentlyReported@journal@v1_14_4@OSL@@QEAA@XZ __imp_??1TrackRecentlyReported@journal@v1_14_4@OSL@@QEAA@XZ ??0TrackRecentlyReported@journal@v1_14_4@OSL@@QEAA@AEBV0123@@Z __imp_??0TrackRecentlyReported@journal@v1_14_4@OSL@@QEAA@AEBV0123@@Z ??0TrackRecentlyReported@journal@v1_14_4@OSL@@QEAA@$$QEAV0123@@Z __imp_??0TrackRecentlyReported@journal@v1_14_4@OSL@@QEAA@$$QEAV0123@@Z ??_FTrackRecentlyReported@journal@v1_14_4@OSL@@QEAAXXZ __imp_??_FTrackRecentlyReported@journal@v1_14_4@OSL@@QEAAXXZ ??1Report2ErrorHandler@journal@v1_14_4@OSL@@UEAA@XZ __imp_??1Report2ErrorHandler@journal@v1_14_4@OSL@@UEAA@XZ ??0Report2ErrorHandler@journal@v1_14_4@OSL@@QEAA@AEBV0123@@Z __imp_??0Report2ErrorHandler@journal@v1_14_4@OSL@@QEAA@AEBV0123@@Z ??0Report2ErrorHandler@journal@v1_14_4@OSL@@QEAA@$$QEAV0123@@Z __imp_??0Report2ErrorHandler@journal@v1_14_4@OSL@@QEAA@$$QEAV0123@@Z ??0Writer@journal@v1_14_4@OSL@@QEAA@PEAX@Z __imp_??0Writer@journal@v1_14_4@OSL@@QEAA@PEAX@Z ?record_errorfmt@Writer@journal@v1_14_4@OSL@@QEAA_NHHVustringhash@OpenImageIO_v3_0@@HPEBW4EncodedType@34@IPEAE@Z __imp_?record_errorfmt@Writer@journal@v1_14_4@OSL@@QEAA_NHHVustringhash@OpenImageIO_v3_0@@HPEBW4EncodedType@34@IPEAE@Z ?record_warningfmt@Writer@journal@v1_14_4@OSL@@QEAA_NHHHVustringhash@OpenImageIO_v3_0@@HPEBW4EncodedType@34@IPEAE@Z __imp_?record_warningfmt@Writer@journal@v1_14_4@OSL@@QEAA_NHHHVustringhash@OpenImageIO_v3_0@@HPEBW4EncodedType@34@IPEAE@Z ?record_printfmt@Writer@journal@v1_14_4@OSL@@QEAA_NHHVustringhash@OpenImageIO_v3_0@@HPEBW4EncodedType@34@IPEAE@Z __imp_?record_printfmt@Writer@journal@v1_14_4@OSL@@QEAA_NHHVustringhash@OpenImageIO_v3_0@@HPEBW4EncodedType@34@IPEAE@Z ?record_filefmt@Writer@journal@v1_14_4@OSL@@QEAA_NHHVustringhash@OpenImageIO_v3_0@@0HPEBW4EncodedType@34@IPEAE@Z __imp_?record_filefmt@Writer@journal@v1_14_4@OSL@@QEAA_NHHVustringhash@OpenImageIO_v3_0@@0HPEBW4EncodedType@34@IPEAE@Z ?requiredForPageTransition@Writer@journal@v1_14_4@OSL@@CA_KXZ __imp_?requiredForPageTransition@Writer@journal@v1_14_4@OSL@@CA_KXZ ?allocatePage@Writer@journal@v1_14_4@OSL@@AEAA_NH@Z __imp_?allocatePage@Writer@journal@v1_14_4@OSL@@AEAA_NH@Z ?write_entry@Writer@journal@v1_14_4@OSL@@AEAA_NHHW4Content@pvt@234@Vustringhash@OpenImageIO_v3_0@@1HPEBW4EncodedType@34@IPEAE@Z __imp_?write_entry@Writer@journal@v1_14_4@OSL@@AEAA_NHHW4Content@pvt@234@Vustringhash@OpenImageIO_v3_0@@1HPEBW4EncodedType@34@IPEAE@Z __imp_??_7Reporter@journal@v1_14_4@OSL@@6B@ __imp_??_7Report2ErrorHandler@journal@v1_14_4@OSL@@6B@ ?initialize_buffer@journal@v1_14_4@OSL@@YA_NQEAEIIH@Z __imp_?initialize_buffer@journal@v1_14_4@OSL@@YA_NQEAEIIH@Z ??0TrackRecentlyReported@journal@v1_14_4@OSL@@QEAA@_NH0H@Z __imp_??0TrackRecentlyReported@journal@v1_14_4@OSL@@QEAA@_NH0H@Z ?shouldReportError@TrackRecentlyReported@journal@v1_14_4@OSL@@QEAA_NAEBV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@@Z __imp_?shouldReportError@TrackRecentlyReported@journal@v1_14_4@OSL@@QEAA_NAEBV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@@Z ?shouldReportWarning@TrackRecentlyReported@journal@v1_14_4@OSL@@QEAA_NAEBV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@@Z __imp_?shouldReportWarning@TrackRecentlyReported@journal@v1_14_4@OSL@@QEAA_NAEBV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@@Z ??0Report2ErrorHandler@journal@v1_14_4@OSL@@QEAA@PEAVErrorHandler@OpenImageIO_v3_0@@AEAVTrackRecentlyReported@123@@Z __imp_??0Report2ErrorHandler@journal@v1_14_4@OSL@@QEAA@PEAVErrorHandler@OpenImageIO_v3_0@@AEAVTrackRecentlyReported@123@@Z ?report_error@Report2ErrorHandler@journal@v1_14_4@OSL@@UEAAXHHAEBV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@@Z __imp_?report_error@Report2ErrorHandler@journal@v1_14_4@OSL@@UEAAXHHAEBV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@@Z ?report_warning@Report2ErrorHandler@journal@v1_14_4@OSL@@UEAAXHHAEBV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@@Z __imp_?report_warning@Report2ErrorHandler@journal@v1_14_4@OSL@@UEAAXHHAEBV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@@Z ?report_print@Report2ErrorHandler@journal@v1_14_4@OSL@@UEAAXHHAEBV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@@Z __imp_?report_print@Report2ErrorHandler@journal@v1_14_4@OSL@@UEAAXHHAEBV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@@Z ?report_file_print@Report2ErrorHandler@journal@v1_14_4@OSL@@UEAAXHHAEBV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@0@Z __imp_?report_file_print@Report2ErrorHandler@journal@v1_14_4@OSL@@UEAAXHHAEBV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@0@Z ??0Reader@journal@v1_14_4@OSL@@QEAA@PEBEAEAVReporter@123@@Z __imp_??0Reader@journal@v1_14_4@OSL@@QEAA@PEBEAEAVReporter@123@@Z ?process@Reader@journal@v1_14_4@OSL@@QEAAXXZ __imp_?process@Reader@journal@v1_14_4@OSL@@QEAAXXZ ?process_entries_for_thread@Reader@journal@v1_14_4@OSL@@AEAAXH@Z __imp_?process_entries_for_thread@Reader@journal@v1_14_4@OSL@@AEAAXH@Z 
/               -1                      0       134721    `
%  � � 2 �- 淛 jL � 樫 栬  � ,K 禟 
J 淛 �1 形 磇 Lj  N� � H� 桀 祈 V� � 答  � H 姥  � 2 �  � (	 f
 � l ~ � � � < :   � : �
 � � R �  2 J x
 �$ �# �% Z! �" ' T( �' @* �( �) ># �! j& $% � 馍 �; 龌 6{ 携 &� |� 协 � 蝊 哷 謈 釨 >a 靉 奀 �* fH 咺 鶫 詳 r� <� .C 淏  勜 �$ R$ �# TD 綜 x& �% f% xE 鐳 B ~A `� �4 �= H� 錄 皽 |� � *� 悧 n� L� 矕 �2 N-  . �. �1 �0 �/ ^, �& �' �( v+ �* f) `_ 杴 攟 杶 膉 耴 (g 鰄 赿 瞝 h 鋓 f 猰 顉 黅  u <s t r 饀 攏 蕒 釻 : ~o l� � � `� 痱 &� 荔 R� 匂 6� 吸  著 `� 掶 � �   �  B  B� 潴 7 焱 `� �7 �8   蚍 P� :� Z� � � J � b� Ζ � � ㄑ 袘 笒  n� 鑫 �,  h- M 綩 �:    < n� �  � �. @K 8O nW X 录 ( �' �& 濬 F R� H� � 夜 牶 r� � 0� : :; �: 糴 � 慅 厅 VO � 扏 �? 憾  .� j�  B- �9 �, �+ (A ﹀ � .s �6 �4 �5 Ι \f `3  � 株 � 6� 驆 l+ J  � H� � 魌 秀 娳 蘞 溩 唓 �> 6> 寔 鴢 nD 贑 瞫 揶 @L 捐 � "� :� 碿 R] :` 奲 讨 b� �* ^2 Zp T  X� 4� hT d@ L� � 0� ぇ 糍 B� � 怉 漶 � 蓉 d� $� 0� 鄃  � 缹 � 傦 � Hg $e 锑 Z 鍲 �� 諰 � z� 
� V� �= n� 庩 诗 �.  r *� � 湸 敧 n< = b�  ^� � x
  � > X� �/ 痖 �0 V� V� 疱 笩 ^? >� D� 4� z� 码 \� i xh 倦 N/ t+  . � ,� 柇 �4 杛 宒 躹 ^� 鬭 LW Fd tw  糪 許 
R 諴 rQ p� 爴 , 郬 鶣  n� `a >S 蔨  � 頿 b� c 碫 &\ 
x 蘺 8y T� 樷 � 滀 � �3 = 孽 (� 婔 j� z� 狋 l  � 樔 T� J  啾 扵 锌 钝 p  � � 坴 寀 � � ,� 霪 �/ � � � T � l �  7 R� B� 瞟  4 (� �% �! �" J9 LI 胞 |� 揿 �$ �# � &4 R� 帧 � R� ,� � � �) �8 ,) �( 蜧 4G �! 巹 �  � �? >? �7 � f� 茊 ぞ 杻 � � > ! �  �> ,# "� 楔 V� 霭 頼  Fm 伧 湤 P� �� � @8 �" " 頏 \@ 炩 b� R:  D* H9 F3 ~<  1 `q 鋔 � <� 噗 摆 臆 D� 屳 繶 � 蛟 緋 R� 轞 v� BU 狏 缊 �2 凄 铉 (4 酿 Ht 涓 jE .� 婃  鋆 豲 蘩 ~� PB 埩 � j� 渤 ~G 耗 诖 <_ Bc ^  搪 (� 魪 4� 紟 V� 樥 帎 h� 鈸 r� � 枙 � B� �; 歜  ,� 灢 蔞 $V 
P $S 釹 t � � � �  �	 瓾  \ < 翽 ~R 篘 nM 濹 ㄈ <� p� �  � 灻 z� � 牧 b� 谀 $� 偫 0� D� 芴 ~� 菏 � 佬 N 宯 p] 蘚 刐 (\ "D �� 柱 0� �? 6o 镀 �9 �7  ; V= �< x: �8 49 t6 杓 越 龊 �> 腄 戮 �5 Bx 攚 
� $| :z �=  x� 郱 h� � 鴡 � � d� (� 茤 磻 車 浮 悾 瀿 R� 矟 刋 lY TZ <[ 測 颌 |� >� 爜 牁 j� \� 瀱 秼 R~ 獇 � 魠 � } 笡 韦 鼮 殶 � Z� l� 饃 T� 磩 � 葔 趥 軝 (� B� 垐 � V� 瑣 &B 緜 j� 劌 ⅳ 謐 x� �	 0
 b dz b�  � \ ◤ � X� 8� 鵂 V� 騹 D� 柁 屵 �; �2 22 訩 > 6 d5 BP h; �0 *0 67 惿 愄 ,� ^� 姓 瀷 﨎 禢 (N 続 �6 疰 昼 `1 H� 钤 � (� Z. ,� 斗 �  �
h g f m l k � � � ��� [ 	 #� �"�s 
��!� 
#�t 		��q p c b j i o n � � ��\ ^ ]  �  � � � � � ����C� � � � � � � � � � � � � � � � � � �� � � � � � ��� /7� � � WXUZV� � a _ ` �~ { | } z y v w x u ����������R Q P O ��� � � � � � � �������|����}~{�K ����� � � � �   0Yd e   '*,)+-(�� 9� � ����A:	 
 s2�� <�� � � �� �� � �� � � � � � �� �� � � � � � ���������B����� ����� � ���� � ������ � � � � � � � � � � � �  �� � � �� � � b � 
GW Z ��?L r�
    y�x� ivzwjk�5hg� � .3�����QROT U X Y cPV �   &���D1%
=�������N �������������������������������\]^_���������� �������������� � � �� ���FIfTKaN��$%�>�M ����
EHeSJ`M� � � �r � � � � � � "! d��S �  @[� �    4  �� � ��  6������ � �� � �������% q& 2 ' 1   :  6 tu# $ 5 " 4 ! 3 ( 7 + 9 J o. mn* 8 , l- 0 ) / p> G ? F < ; I = H @ B A D E C � � $ 8;���������L ������  �
h g f m l k � � � ��� [ 	 #� �"�s 
��!� 
#�t 		��q p c b j i o n � � ��\ ^ ]  �  � �� � ������=� � ���������2C�� 3!��4�1�� � � � � � � � � � � � 9�#� � � � � �������� � � � � � ��'&��� /7� � � PwWXUZV� � �a _ ` �~ { | } z y v w x u /z���]��J�B0^����R Q P O ��\fgK� � Y� � � � �� ��������|����}~{�K �`���� � � � � [  0Y<56d e   '*,)+-(+�� 9� � ����xyA;:	 
 s�2�Q�R <�� � � �� �� � �� � � � � � �O� �� � � � � � N���.��pqrno����B����� ����� � ���� � ������ � � � � � � � � � � � �  �� � � �� � � bij_%${ � 
GW Z ��?XL r�
    ,Zy�x� ivzwjk�5hg� � .3�����QROT U X Y cPV �U� TM��  &���D1%d�
*=�������N �������|������}������������������\]^_���������� �����������~��� � � �� hLbkl����FIfTKaN��$%�>m�M ����
EHeSJ`M� �� � E�r � � � � � � "! d"AI�C ��S �  @V[� -suae���    4  ?G�� � ��  6D������ � �� � :����SW���@H% q& 2 ' 1   :  6 tu# $ 5 " 4 ! 3 ( 7 + 9 J o. mn* 8 , l- 0 ) / p> G ? F < ; I = H @ B A D E C 7��� � �c�tv$ 88;����������L �����>�F�)( ??0AccumAutomata@v1_14_4@OSL@@QEAA@AEBV012@@Z ??0AccumAutomata@v1_14_4@OSL@@QEAA@XZ ??0AccumRule@v1_14_4@OSL@@QEAA@H_N@Z ??0Accumulator@v1_14_4@OSL@@QEAA@$$QEAV012@@Z ??0Accumulator@v1_14_4@OSL@@QEAA@AEBV012@@Z ??0Accumulator@v1_14_4@OSL@@QEAA@PEBVAccumAutomata@12@@Z ??0ClosureComponent@v1_14_4@OSL@@QEAA@$$QEAU012@@Z ??0ClosureComponent@v1_14_4@OSL@@QEAA@AEBU012@@Z ??0ClosureComponent@v1_14_4@OSL@@QEAA@XZ ??0ClosureMul@v1_14_4@OSL@@QEAA@$$QEAU012@@Z ??0ClosureMul@v1_14_4@OSL@@QEAA@AEBU012@@Z ??0ClosureMul@v1_14_4@OSL@@QEAA@XZ ??0ColorSystem@pvt@v1_14_4@OSL@@QEAA@$$QEAV0123@@Z ??0ColorSystem@pvt@v1_14_4@OSL@@QEAA@AEBV0123@@Z ??0ColorSystem@pvt@v1_14_4@OSL@@QEAA@XZ ??0DfOptimizedAutomata@v1_14_4@OSL@@QEAA@$$QEAV012@@Z ??0DfOptimizedAutomata@v1_14_4@OSL@@QEAA@AEBV012@@Z ??0DfOptimizedAutomata@v1_14_4@OSL@@QEAA@XZ ??0LLVM_Util@pvt@v1_14_4@OSL@@QEAA@AEBUPerThreadInfo@0123@HH@Z ??0LLVM_Util@pvt@v1_14_4@OSL@@QEAA@AEBV0123@@Z ??0OSLQuery@v1_14_4@OSL@@QEAA@AEBV012@@Z ??0OSLQuery@v1_14_4@OSL@@QEAA@PEBVShaderGroup@12@H@Z ??0OSLQuery@v1_14_4@OSL@@QEAA@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@0@Z ??0Parameter@OSLQuery@v1_14_4@OSL@@QEAA@XZ ??0PerThreadInfo@LLVM_Util@pvt@v1_14_4@OSL@@QEAA@XZ ??0Reader@journal@v1_14_4@OSL@@QEAA@PEBEAEAVReporter@123@@Z ??0RendererServices@v1_14_4@OSL@@QEAA@AEBV012@@Z ??0RendererServices@v1_14_4@OSL@@QEAA@PEAVTextureSystem@OpenImageIO_v3_0@@@Z ??0Report2ErrorHandler@journal@v1_14_4@OSL@@QEAA@$$QEAV0123@@Z ??0Report2ErrorHandler@journal@v1_14_4@OSL@@QEAA@AEBV0123@@Z ??0Report2ErrorHandler@journal@v1_14_4@OSL@@QEAA@PEAVErrorHandler@OpenImageIO_v3_0@@AEAVTrackRecentlyReported@123@@Z ??0Reporter@journal@v1_14_4@OSL@@QEAA@AEBV0123@@Z ??0Reporter@journal@v1_14_4@OSL@@QEAA@XZ ??0ScopedJitMemoryUser@LLVM_Util@pvt@v1_14_4@OSL@@QEAA@XZ ??0ShadingContext@v1_14_4@OSL@@QEAA@AEAVShadingSystemImpl@pvt@12@PEAUPerThreadInfo@12@@Z ??0ShadingSystem@v1_14_4@OSL@@QEAA@PEAVRendererServices@12@PEAVTextureSystem@OpenImageIO_v3_0@@PEAVErrorHandler@5@@Z ??0TrackRecentlyReported@journal@v1_14_4@OSL@@QEAA@$$QEAV0123@@Z ??0TrackRecentlyReported@journal@v1_14_4@OSL@@QEAA@AEBV0123@@Z ??0TrackRecentlyReported@journal@v1_14_4@OSL@@QEAA@_NH0H@Z ??0Writer@journal@v1_14_4@OSL@@QEAA@PEAX@Z ??1AccumAutomata@v1_14_4@OSL@@QEAA@XZ ??1Accumulator@v1_14_4@OSL@@QEAA@XZ ??1DfOptimizedAutomata@v1_14_4@OSL@@QEAA@XZ ??1LLVM_Util@pvt@v1_14_4@OSL@@QEAA@XZ ??1Parameter@OSLQuery@v1_14_4@OSL@@QEAA@XZ ??1PerThreadInfo@LLVM_Util@pvt@v1_14_4@OSL@@QEAA@XZ ??1RendererServices@v1_14_4@OSL@@UEAA@XZ ??1Report2ErrorHandler@journal@v1_14_4@OSL@@UEAA@XZ ??1Reporter@journal@v1_14_4@OSL@@UEAA@XZ ??1ScopedJitMemoryUser@LLVM_Util@pvt@v1_14_4@OSL@@QEAA@XZ ??1ShadingContext@v1_14_4@OSL@@QEAA@XZ ??1ShadingSystem@v1_14_4@OSL@@QEAA@XZ ??1TrackRecentlyReported@journal@v1_14_4@OSL@@QEAA@XZ ??4AccumAutomata@v1_14_4@OSL@@QEAAAEAV012@AEBV012@@Z ??4AccumRule@v1_14_4@OSL@@QEAAAEAV012@$$QEAV012@@Z ??4AccumRule@v1_14_4@OSL@@QEAAAEAV012@AEBV012@@Z ??4Accumulator@v1_14_4@OSL@@QEAAAEAV012@$$QEAV012@@Z ??4Accumulator@v1_14_4@OSL@@QEAAAEAV012@AEBV012@@Z ??4ClosureAdd@v1_14_4@OSL@@QEAAAEAU012@$$QEAU012@@Z ??4ClosureAdd@v1_14_4@OSL@@QEAAAEAU012@AEBU012@@Z ??4ClosureColor@v1_14_4@OSL@@QEAAAEAU012@$$QEAU012@@Z ??4ClosureColor@v1_14_4@OSL@@QEAAAEAU012@AEBU012@@Z ??4ClosureComponent@v1_14_4@OSL@@QEAAAEAU012@$$QEAU012@@Z ??4ClosureComponent@v1_14_4@OSL@@QEAAAEAU012@AEBU012@@Z ??4ClosureMul@v1_14_4@OSL@@QEAAAEAU012@$$QEAU012@@Z ??4ClosureMul@v1_14_4@OSL@@QEAAAEAU012@AEBU012@@Z ??4ColorSystem@pvt@v1_14_4@OSL@@QEAAAEAV0123@$$QEAV0123@@Z ??4ColorSystem@pvt@v1_14_4@OSL@@QEAAAEAV0123@AEBV0123@@Z ??4DfOptimizedAutomata@v1_14_4@OSL@@QEAAAEAV012@$$QEAV012@@Z ??4DfOptimizedAutomata@v1_14_4@OSL@@QEAAAEAV012@AEBV012@@Z ??4LLVM_Util@pvt@v1_14_4@OSL@@QEAAAEAV0123@AEBV0123@@Z ??4Labels@v1_14_4@OSL@@QEAAAEAV012@$$QEAV012@@Z ??4Labels@v1_14_4@OSL@@QEAAAEAV012@AEBV012@@Z ??4OSLQuery@v1_14_4@OSL@@QEAAAEAV012@AEBV012@@Z ??4PerThreadInfo@LLVM_Util@pvt@v1_14_4@OSL@@QEAAAEAU01234@AEBU01234@@Z ??4RendererServices@v1_14_4@OSL@@QEAAAEAV012@AEBV012@@Z ??4Reporter@journal@v1_14_4@OSL@@QEAAAEAV0123@AEBV0123@@Z ??4ScopedJitMemoryUser@LLVM_Util@pvt@v1_14_4@OSL@@QEAAAEAU01234@AEBU01234@@Z ??4ShadingSystem@v1_14_4@OSL@@QEAAAEAV012@AEBV012@@Z ??_FRendererServices@v1_14_4@OSL@@QEAAXXZ ??_FShadingSystem@v1_14_4@OSL@@QEAAXXZ ??_FTrackRecentlyReported@journal@v1_14_4@OSL@@QEAAXXZ ?ConnectShaders@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@111@Z ?ConnectShaders@ShadingSystem@v1_14_4@OSL@@QEAA_NV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@000@Z ?GEP@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAVType@6@PEAV56@1AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z ?GEP@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAVType@6@PEAV56@HAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z ?GEP@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAVType@6@PEAV56@HHAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z ?GEP@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAVType@6@PEAV56@HHHAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z ?InstallLazyFunctionCreator@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXP6APEAXAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z@Z ?LoadMemoryCompiledShader@ShadingSystem@v1_14_4@OSL@@QEAA_NV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@0@Z ?Parameter@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@W4ParamHints@23@@Z ?Parameter@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@_N@Z ?Parameter@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@HW4ParamHints@23@@Z ?Parameter@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@H_N@Z ?Parameter@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@MW4ParamHints@23@@Z ?Parameter@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@M_N@Z ?Parameter@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@UTypeDesc@6@PEBXW4ParamHints@23@@Z ?Parameter@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@UTypeDesc@6@PEBX_N@Z ?Parameter@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@Vustring@6@W4ParamHints@23@@Z ?Parameter@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@Vustring@6@_N@Z ?Parameter@ShadingSystem@v1_14_4@OSL@@QEAA_NV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@UTypeDesc@5@PEBXW4ParamHints@23@@Z ?Parameter@ShadingSystem@v1_14_4@OSL@@QEAA_NV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@UTypeDesc@5@PEBX_N@Z ?ReParameter@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@1AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z ?ReParameter@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@1H@Z ?ReParameter@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@1M@Z ?ReParameter@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@1UTypeDesc@6@PEBX@Z ?ReParameter@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@1Vustring@6@@Z ?SetupLLVM@LLVM_Util@pvt@v1_14_4@OSL@@AEAAXXZ ?Shader@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@11@Z ?Shader@ShadingSystem@v1_14_4@OSL@@QEAA_NV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@00@Z ?ShaderGroupBegin@ShadingSystem@v1_14_4@OSL@@QEAA?AV?$shared_ptr@VShaderGroup@v1_14_4@OSL@@@std@@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@00@Z ?ShaderGroupBegin@ShadingSystem@v1_14_4@OSL@@QEAA?AV?$shared_ptr@VShaderGroup@v1_14_4@OSL@@@std@@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@@Z ?ShaderGroupEnd@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShaderGroup@23@@Z ?ShaderGroupEnd@ShadingSystem@v1_14_4@OSL@@QEAA_NXZ ?absorb_module@LLVM_Util@pvt@v1_14_4@OSL@@QEAA_NV?$unique_ptr@VModule@llvm@@U?$default_delete@VModule@llvm@@@std@@@std@@@Z ?accum@AccumAutomata@v1_14_4@OSL@@QEBAXHAEBV?$Color3@M@Imath_3_1@@AEAV?$vector@UAovOutput@v1_14_4@OSL@@V?$allocator@UAovOutput@v1_14_4@OSL@@@std@@@std@@@Z ?accum@AccumRule@v1_14_4@OSL@@QEBAXAEBV?$Color3@M@Imath_3_1@@AEAV?$vector@UAovOutput@v1_14_4@OSL@@V?$allocator@UAovOutput@v1_14_4@OSL@@@std@@@std@@@Z ?accum@Accumulator@v1_14_4@OSL@@QEAAXAEBV?$Color3@M@Imath_3_1@@@Z ?addEventType@AccumAutomata@v1_14_4@OSL@@QEAAXVustring@OpenImageIO_v3_0@@@Z ?addRule@AccumAutomata@v1_14_4@OSL@@QEAAPEAVAccumRule@23@PEBDH_N@Z ?addScatteringType@AccumAutomata@v1_14_4@OSL@@QEAAXVustring@OpenImageIO_v3_0@@@Z ?add_function_mapping@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVFunction@llvm@@PEAX@Z ?add_global_mapping@LLVM_Util@pvt@v1_14_4@OSL@@SAXPEBDPEAX@Z ?add_symlocs@ShadingSystem@v1_14_4@OSL@@QEAAXPEAVShaderGroup@23@V?$span@$$CBUSymLocationDesc@v1_14_4@OSL@@$0?0@OpenImageIO_v3_0@@@Z ?add_symlocs@ShadingSystem@v1_14_4@OSL@@QEAAXV?$span@$$CBUSymLocationDesc@v1_14_4@OSL@@$0?0@OpenImageIO_v3_0@@@Z ?alloc_scratch@ShadingContext@v1_14_4@OSL@@QEAAPEAX_K0@Z ?allocatePage@Writer@journal@v1_14_4@OSL@@AEAA_NH@Z ?allow_warnings@ShadingContext@v1_14_4@OSL@@QEAA_NXZ ?append_error@OSLQuery@v1_14_4@OSL@@AEBAXAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z ?apply_break_to_mask_stack@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXXZ ?apply_continue_to_mask_stack@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXXZ ?apply_exit_to_mask_stack@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXXZ ?apply_return_to@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z ?apply_return_to_mask_stack@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXXZ ?archive_shadergroup@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@@Z ?archive_shadergroup@ShadingSystem@v1_14_4@OSL@@QEAA_NPEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@@Z ?as_add@ClosureColor@v1_14_4@OSL@@QEBAPEBUClosureAdd@23@XZ ?as_comp@ClosureColor@v1_14_4@OSL@@QEBAPEBUClosureComponent@23@XZ ?as_mul@ClosureColor@v1_14_4@OSL@@QEBAPEBUClosureMul@23@XZ ?assume_ptr_is_aligned@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVValue@llvm@@I@Z ?attribute@ShadingSystem@v1_14_4@OSL@@QEAA_NPEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@1@Z ?attribute@ShadingSystem@v1_14_4@OSL@@QEAA_NPEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@H@Z ?attribute@ShadingSystem@v1_14_4@OSL@@QEAA_NPEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@M@Z ?attribute@ShadingSystem@v1_14_4@OSL@@QEAA_NPEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@N@Z ?attribute@ShadingSystem@v1_14_4@OSL@@QEAA_NPEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@UTypeDesc@6@PEBX@Z ?attribute@ShadingSystem@v1_14_4@OSL@@QEAA_NV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@0@Z ?attribute@ShadingSystem@v1_14_4@OSL@@QEAA_NV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@H@Z ?attribute@ShadingSystem@v1_14_4@OSL@@QEAA_NV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@M@Z ?attribute@ShadingSystem@v1_14_4@OSL@@QEAA_NV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@N@Z ?attribute@ShadingSystem@v1_14_4@OSL@@QEAA_NV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@UTypeDesc@5@PEBX@Z ?batched@RendererServices@v1_14_4@OSL@@UEAAPEAV?$BatchedRendererServices@$03@23@V?$WidthOf@$03@23@@Z ?batched@RendererServices@v1_14_4@OSL@@UEAAPEAV?$BatchedRendererServices@$07@23@V?$WidthOf@$07@23@@Z ?batched@RendererServices@v1_14_4@OSL@@UEAAPEAV?$BatchedRendererServices@$0BA@@23@V?$WidthOf@$0BA@@23@@Z ?begin@Accumulator@v1_14_4@OSL@@QEAAXXZ ?begin@OSLQuery@v1_14_4@OSL@@QEAA?AV?$_Vector_iterator@V?$_Vector_val@U?$_Simple_types@UParameter@OSLQuery@v1_14_4@OSL@@@std@@@std@@@std@@XZ ?bitcode_string@LLVM_Util@pvt@v1_14_4@OSL@@QEAA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@PEAVFunction@llvm@@@Z ?bitcode_string@LLVM_Util@pvt@v1_14_4@OSL@@QEAA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@PEAVModule@llvm@@@Z ?blackbody_rgb@ColorSystem@pvt@v1_14_4@OSL@@QEBA?AV?$Color3@M@Imath_3_1@@M@Z ?broken@Accumulator@v1_14_4@OSL@@QEBA_NXZ ?build_attribute_getter@RendererServices@v1_14_4@OSL@@UEAAXAEBVShaderGroup@23@_NPEBVustring@OpenImageIO_v3_0@@21PEBHUTypeDesc@6@1AEAV?$FunctionSpec@V?$ArgVariant@W4AttributeSpecBuiltinArg@v1_14_4@OSL@@@v1_14_4@OSL@@@23@@Z ?build_interpolated_getter@RendererServices@v1_14_4@OSL@@UEAAXAEBVShaderGroup@23@AEBVustring@OpenImageIO_v3_0@@UTypeDesc@6@_NAEAV?$FunctionSpec@V?$ArgVariant@W4InterpolatedSpecBuiltinArg@v1_14_4@OSL@@@v1_14_4@OSL@@@23@@Z ?builder@LLVM_Util@pvt@v1_14_4@OSL@@AEAAAEAVIRBuilder@1234@XZ ?call_function@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@V?$span@QEAVValue@llvm@@$0?0@OpenImageIO_v3_0@@@Z ?call_function@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEBDPEAV56@111@Z ?call_function@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEBDPEAV56@11@Z ?call_function@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEBDPEAV56@1@Z ?call_function@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEBDPEAV56@@Z ?call_function@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEBDV?$span@QEAVValue@llvm@@$0?0@OpenImageIO_v3_0@@@Z ?can_lookup_blackbody@ColorSystem@pvt@v1_14_4@OSL@@QEBA_NM@Z ?cbegin@OSLQuery@v1_14_4@OSL@@QEBA?AV?$_Vector_const_iterator@V?$_Vector_val@U?$_Simple_types@UParameter@OSLQuery@v1_14_4@OSL@@@std@@@std@@@std@@XZ ?cend@OSLQuery@v1_14_4@OSL@@QEBA?AV?$_Vector_const_iterator@V?$_Vector_val@U?$_Simple_types@UParameter@OSLQuery@v1_14_4@OSL@@@std@@@std@@@std@@XZ ?clear_runtime_stats@ShadingContext@v1_14_4@OSL@@QEAAXXZ ?clear_symlocs@ShadingSystem@v1_14_4@OSL@@QEAAXPEAVShaderGroup@23@@Z ?clear_symlocs@ShadingSystem@v1_14_4@OSL@@QEAAXXZ ?closure_add_allot@ShadingContext@v1_14_4@OSL@@QEAAPEAUClosureAdd@23@PEBUClosureColor@23@0@Z ?closure_component_allot@ShadingContext@v1_14_4@OSL@@QEAAPEAUClosureComponent@23@H_KAEBV?$Color3@M@Imath_3_1@@@Z ?closure_mul_allot@ShadingContext@v1_14_4@OSL@@QEAAPEAUClosureMul@23@AEBV?$Color3@M@Imath_3_1@@PEBUClosureColor@23@@Z ?closure_mul_allot@ShadingContext@v1_14_4@OSL@@QEAAPEAUClosureMul@23@MPEBUClosureColor@23@@Z ?colorspace@ColorSystem@pvt@v1_14_4@OSL@@QEBAAEBVustringhash@OpenImageIO_v3_0@@XZ ?compile@AccumAutomata@v1_14_4@OSL@@QEAAXXZ ?compileFrom@DfOptimizedAutomata@v1_14_4@OSL@@QEAAXAEBVDfAutomata@23@@Z ?compute_blackbody_rgb@ColorSystem@pvt@v1_14_4@OSL@@QEBA?AV?$Color3@M@Imath_3_1@@M@Z ?constant128@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@_K0@Z ?constant128@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@_K@Z ?constant16@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@F@Z ?constant16@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@G@Z ?constant4@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@C@Z ?constant4@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@E@Z ?constant64@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@N@Z ?constant64@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@_K@Z ?constant8@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@C@Z ?constant8@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@E@Z ?constant@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@AEBUTypeDesc@OpenImageIO_v3_0@@@Z ?constant@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@H@Z ?constant@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@I@Z ?constant@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@M@Z ?constant@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@_K@Z ?constant@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@@Z ?constant@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@Vustring@OpenImageIO_v3_0@@@Z ?constant_array@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@V?$span@QEAVConstant@llvm@@$0?0@OpenImageIO_v3_0@@@Z ?constant_bool@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@_N@Z ?constant_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAXPEAVPointerType@6@@Z ?constanti64@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@_J@Z ?context@LLVM_Util@pvt@v1_14_4@OSL@@QEBAAEAVLLVMContext@llvm@@XZ ?convert_value@ShadingSystem@v1_14_4@OSL@@SA_NPEAXUTypeDesc@OpenImageIO_v3_0@@PEBX1@Z ?copy_to_device@RendererServices@v1_14_4@OSL@@UEAAPEAXPEAXPEBX_K@Z ?create_global_constant@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVGlobalVariable@llvm@@PEAVConstant@6@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z ?create_masking_scope@LLVM_Util@pvt@v1_14_4@OSL@@QEAA?AUScopedMasking@1234@_N@Z ?create_thread_info@ShadingSystem@v1_14_4@OSL@@QEAAPEAUPerThreadInfo@23@XZ ?current_function@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVFunction@llvm@@@Z ?current_function@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVFunction@llvm@@XZ ?current_function_arg@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@H@Z ?current_mask@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@XZ ?data@ClosureComponent@v1_14_4@OSL@@QEAAPEAXXZ ?data@ClosureComponent@v1_14_4@OSL@@QEBAPEBXXZ ?debug@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXH@Z ?debug@LLVM_Util@pvt@v1_14_4@OSL@@QEBAHXZ ?debug_is_enabled@LLVM_Util@pvt@v1_14_4@OSL@@QEBA_NXZ ?debug_pop_function@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXXZ ?debug_pop_inlined_function@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXXZ ?debug_push_function@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@Vustring@OpenImageIO_v3_0@@H@Z ?debug_push_inlined_function@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXVustring@OpenImageIO_v3_0@@0H@Z ?debug_set_location@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXVustring@OpenImageIO_v3_0@@H@Z ?debug_setup_compilation_unit@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEBD@Z ?delete_func_body@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVFunction@llvm@@@Z ?destroy_thread_info@ShadingSystem@v1_14_4@OSL@@QEAAXPEAUPerThreadInfo@23@@Z ?detect_cpu_features@LLVM_Util@pvt@v1_14_4@OSL@@QEAA_NW4TargetISA@234@_N@Z ?device_alloc@RendererServices@v1_14_4@OSL@@UEAAPEAX_K@Z ?device_free@RendererServices@v1_14_4@OSL@@UEAAXPEAX@Z ?dict_find@ShadingContext@v1_14_4@OSL@@QEAAHPEAUShaderGlobals@23@HVustring@OpenImageIO_v3_0@@@Z ?dict_find@ShadingContext@v1_14_4@OSL@@QEAAHPEAUShaderGlobals@23@Vustring@OpenImageIO_v3_0@@1@Z ?dict_next@ShadingContext@v1_14_4@OSL@@QEAAHH@Z ?dict_value@ShadingContext@v1_14_4@OSL@@QEAAHHVustring@OpenImageIO_v3_0@@UTypeDesc@5@PEAX_N@Z ?do_optimize@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z ?dump_struct_data_layout@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVType@llvm@@@Z ?dumpasm@LLVM_Util@pvt@v1_14_4@OSL@@QEAAX_N@Z ?dumpasm@LLVM_Util@pvt@v1_14_4@OSL@@QEBA_NXZ ?element_type_of@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVType@llvm@@PEAV56@@Z ?end@Accumulator@v1_14_4@OSL@@QEAAXPEAX@Z ?end@OSLQuery@v1_14_4@OSL@@QEAA?AV?$_Vector_iterator@V?$_Vector_val@U?$_Simple_types@UParameter@OSLQuery@v1_14_4@OSL@@@std@@@std@@@std@@XZ ?end_builder@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXXZ ?environment@RendererServices@v1_14_4@OSL@@UEAA_NVustringhash@OpenImageIO_v3_0@@PEAVTextureHandle@TextureSystem@5@PEAVPerthread@75@AEAVTextureOpt_v2@5@PEAUShaderGlobals@23@AEBV?$Vec3@M@Imath_3_1@@55HPEAM66PEAV45@@Z ?errorfmt@RendererServices@v1_14_4@OSL@@UEAAXPEAUShaderGlobals@23@Vustringhash@OpenImageIO_v3_0@@HPEBW4EncodedType@23@IPEAE@Z ?execengine@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVExecutionEngine@llvm@@XZ ?execengine@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVExecutionEngine@llvm@@@Z ?execute@ShadingContext@v1_14_4@OSL@@QEAA_NAEAVShaderGroup@23@HHAEAUShaderGlobals@23@PEAX2_N@Z ?execute@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShadingContext@23@AEAVShaderGroup@23@AEAUShaderGlobals@23@_N@Z ?execute@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShadingContext@23@AEAVShaderGroup@23@HHAEAUShaderGlobals@23@PEAX3_N@Z ?execute@ShadingSystem@v1_14_4@OSL@@QEAA_NPEAVShadingContext@23@AEAVShaderGroup@23@AEAUShaderGlobals@23@_N@Z ?execute_cleanup@ShadingContext@v1_14_4@OSL@@QEAA_NXZ ?execute_cleanup@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShadingContext@23@@Z ?execute_init@ShadingContext@v1_14_4@OSL@@QEAA_NAEAVShaderGroup@23@HHAEAUShaderGlobals@23@PEAX2_N@Z ?execute_init@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShadingContext@23@AEAVShaderGroup@23@AEAUShaderGlobals@23@_N@Z ?execute_init@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShadingContext@23@AEAVShaderGroup@23@HHAEAUShaderGlobals@23@PEAX3_N@Z ?execute_layer@ShadingContext@v1_14_4@OSL@@QEAA_NHHAEAUShaderGlobals@23@PEAX1H@Z ?execute_layer@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShadingContext@23@AEAUShaderGlobals@23@H@Z ?execute_layer@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShadingContext@23@AEAUShaderGlobals@23@PEBVShaderSymbol@23@@Z ?execute_layer@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShadingContext@23@AEAUShaderGlobals@23@Vustring@OpenImageIO_v3_0@@@Z ?execute_layer@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShadingContext@23@HHAEAUShaderGlobals@23@PEAX2H@Z ?execute_layer@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShadingContext@23@HHAEAUShaderGlobals@23@PEAX2PEBVShaderSymbol@23@@Z ?execute_layer@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShadingContext@23@HHAEAUShaderGlobals@23@PEAX2Vustring@OpenImageIO_v3_0@@@Z ?execution_is_batched@ShadingContext@v1_14_4@OSL@@AEBA_NXZ ?filefmt@RendererServices@v1_14_4@OSL@@UEAAXPEAUShaderGlobals@23@Vustringhash@OpenImageIO_v3_0@@1HPEBW4EncodedType@23@IPEAE@Z ?find_layer@ShadingSystem@v1_14_4@OSL@@QEBAHAEBVShaderGroup@23@Vustring@OpenImageIO_v3_0@@@Z ?find_regex@ShadingContext@v1_14_4@OSL@@QEAAAEBV?$basic_regex@DV?$regex_traits@D@std@@@std@@Vustring@OpenImageIO_v3_0@@@Z ?find_symbol@ShadingSystem@v1_14_4@OSL@@QEBAPEBVShaderSymbol@23@AEBVShaderGroup@23@Vustring@OpenImageIO_v3_0@@1@Z ?find_symbol@ShadingSystem@v1_14_4@OSL@@QEBAPEBVShaderSymbol@23@AEBVShaderGroup@23@Vustring@OpenImageIO_v3_0@@@Z ?find_symloc@ShadingSystem@v1_14_4@OSL@@QEBAPEBUSymLocationDesc@23@PEBVShaderGroup@23@Vustring@OpenImageIO_v3_0@@@Z ?find_symloc@ShadingSystem@v1_14_4@OSL@@QEBAPEBUSymLocationDesc@23@PEBVShaderGroup@23@Vustring@OpenImageIO_v3_0@@W4SymArena@23@@Z ?find_symloc@ShadingSystem@v1_14_4@OSL@@QEBAPEBUSymLocationDesc@23@Vustring@OpenImageIO_v3_0@@@Z ?find_symloc@ShadingSystem@v1_14_4@OSL@@QEBAPEBUSymLocationDesc@23@Vustring@OpenImageIO_v3_0@@W4SymArena@23@@Z ?free_dict_resources@ShadingContext@v1_14_4@OSL@@AEAAXXZ ?fromString@ColorSystem@pvt@v1_14_4@OSL@@SAPEBUChroma@1234@Vustringhash@OpenImageIO_v3_0@@@Z ?from_rgb@ColorSystem@pvt@v1_14_4@OSL@@QEBA?AV?$Color3@M@Imath_3_1@@Vustringhash@OpenImageIO_v3_0@@AEBV56@PEAVShadingContext@34@PEAUShaderGlobals@34@@Z ?func_is_empty@LLVM_Util@pvt@v1_14_4@OSL@@QEAA_NPEAVFunction@llvm@@@Z ?func_name@LLVM_Util@pvt@v1_14_4@OSL@@QEAA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@PEAVFunction@llvm@@@Z ?get@PerThreadInfo@LLVM_Util@pvt@v1_14_4@OSL@@AEBAPEAUImpl@12345@XZ ?getCurrentDebugScope@LLVM_Util@pvt@v1_14_4@OSL@@AEBAPEAVDIScope@llvm@@XZ ?getCurrentInliningSite@LLVM_Util@pvt@v1_14_4@OSL@@AEBAPEAVDILocation@llvm@@XZ ?getOrCreateDebugFileFor@LLVM_Util@pvt@v1_14_4@OSL@@AEAAPEAVDIFile@llvm@@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z ?getOutput@Accumulator@v1_14_4@OSL@@QEBAAEBUAovOutput@23@H@Z ?getOutputIndex@AccumRule@v1_14_4@OSL@@QEBAHXZ ?getPointerToFunction@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAXPEAVFunction@llvm@@@Z ?getRuleList@AccumAutomata@v1_14_4@OSL@@QEBAAEBV?$list@VAccumRule@v1_14_4@OSL@@V?$allocator@VAccumRule@v1_14_4@OSL@@@std@@@std@@XZ ?getRules@DfOptimizedAutomata@v1_14_4@OSL@@QEBAPEBQEAXHAEAH@Z ?getRulesInState@AccumAutomata@v1_14_4@OSL@@QEBAPEBQEAXHAEAH@Z ?getTransition@AccumAutomata@v1_14_4@OSL@@QEBAHHVustring@OpenImageIO_v3_0@@@Z ?getTransition@DfOptimizedAutomata@v1_14_4@OSL@@QEBAHHVustring@OpenImageIO_v3_0@@@Z ?get_array_attribute@RendererServices@v1_14_4@OSL@@UEAA_NPEAUShaderGlobals@23@_NVustringhash@OpenImageIO_v3_0@@UTypeDesc@6@2HPEAX@Z ?get_attribute@RendererServices@v1_14_4@OSL@@UEAA_NPEAUShaderGlobals@23@_NVustringhash@OpenImageIO_v3_0@@UTypeDesc@6@2PEAX@Z ?get_context@ShadingSystem@v1_14_4@OSL@@QEAAPEAVShadingContext@23@PEAUPerThreadInfo@23@PEAVPerthread@TextureSystem@OpenImageIO_v3_0@@@Z ?get_inverse_matrix@RendererServices@v1_14_4@OSL@@UEAA_NPEAUShaderGlobals@23@AEAV?$Matrix44@M@Imath_3_1@@PEBX@Z ?get_inverse_matrix@RendererServices@v1_14_4@OSL@@UEAA_NPEAUShaderGlobals@23@AEAV?$Matrix44@M@Imath_3_1@@PEBXM@Z ?get_inverse_matrix@RendererServices@v1_14_4@OSL@@UEAA_NPEAUShaderGlobals@23@AEAV?$Matrix44@M@Imath_3_1@@Vustringhash@OpenImageIO_v3_0@@@Z ?get_inverse_matrix@RendererServices@v1_14_4@OSL@@UEAA_NPEAUShaderGlobals@23@AEAV?$Matrix44@M@Imath_3_1@@Vustringhash@OpenImageIO_v3_0@@M@Z ?get_matrix@RendererServices@v1_14_4@OSL@@UEAA_NPEAUShaderGlobals@23@AEAV?$Matrix44@M@Imath_3_1@@PEBX@Z ?get_matrix@RendererServices@v1_14_4@OSL@@UEAA_NPEAUShaderGlobals@23@AEAV?$Matrix44@M@Imath_3_1@@PEBXM@Z ?get_matrix@RendererServices@v1_14_4@OSL@@UEAA_NPEAUShaderGlobals@23@AEAV?$Matrix44@M@Imath_3_1@@Vustringhash@OpenImageIO_v3_0@@@Z ?get_matrix@RendererServices@v1_14_4@OSL@@UEAA_NPEAUShaderGlobals@23@AEAV?$Matrix44@M@Imath_3_1@@Vustringhash@OpenImageIO_v3_0@@M@Z ?get_max_warnings_per_thread@pvt@v1_14_4@OSL@@YAHQEAX@Z ?get_symbol@ShadingSystem@v1_14_4@OSL@@QEBAPEBXAEBVShadingContext@23@Vustring@OpenImageIO_v3_0@@1AEAUTypeDesc@6@@Z ?get_symbol@ShadingSystem@v1_14_4@OSL@@QEBAPEBXAEBVShadingContext@23@Vustring@OpenImageIO_v3_0@@AEAUTypeDesc@6@@Z ?get_texture_handle@RendererServices@v1_14_4@OSL@@UEAAPEAVTextureHandle@TextureSystem@OpenImageIO_v3_0@@Vustring@6@PEAVShadingContext@23@PEBVTextureOpt_v2@6@@Z ?get_texture_handle@RendererServices@v1_14_4@OSL@@UEAAPEAVTextureHandle@TextureSystem@OpenImageIO_v3_0@@Vustringhash@6@PEAVShadingContext@23@PEBVTextureOpt_v2@6@@Z ?get_texture_info@RendererServices@v1_14_4@OSL@@UEAA_NVustringhash@OpenImageIO_v3_0@@PEAVTextureHandle@TextureSystem@5@MMPEAVPerthread@75@PEAUShaderGlobals@23@H0UTypeDesc@5@PEAXPEAV45@@Z ?get_texture_info@RendererServices@v1_14_4@OSL@@UEAA_NVustringhash@OpenImageIO_v3_0@@PEAVTextureHandle@TextureSystem@5@PEAVPerthread@75@PEAUShaderGlobals@23@H0UTypeDesc@5@PEAXPEAV45@@Z ?get_userdata@RendererServices@v1_14_4@OSL@@UEAA_N_NVustringhash@OpenImageIO_v3_0@@UTypeDesc@5@PEAUShaderGlobals@23@PEAX@Z ?getattribute@ShadingSystem@v1_14_4@OSL@@QEAA_NPEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@AEAH@Z ?getattribute@ShadingSystem@v1_14_4@OSL@@QEAA_NPEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@AEAM@Z ?getattribute@ShadingSystem@v1_14_4@OSL@@QEAA_NPEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@AEAN@Z ?getattribute@ShadingSystem@v1_14_4@OSL@@QEAA_NPEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@AEAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z ?getattribute@ShadingSystem@v1_14_4@OSL@@QEAA_NPEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@AEAVustring@6@@Z ?getattribute@ShadingSystem@v1_14_4@OSL@@QEAA_NPEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@PEAPEAD@Z ?getattribute@ShadingSystem@v1_14_4@OSL@@QEAA_NPEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@UTypeDesc@6@PEAX@Z ?getattribute@ShadingSystem@v1_14_4@OSL@@QEAA_NV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@AEAH@Z ?getattribute@ShadingSystem@v1_14_4@OSL@@QEAA_NV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@AEAM@Z ?getattribute@ShadingSystem@v1_14_4@OSL@@QEAA_NV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@AEAN@Z ?getattribute@ShadingSystem@v1_14_4@OSL@@QEAA_NV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@AEAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z ?getattribute@ShadingSystem@v1_14_4@OSL@@QEAA_NV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@AEAVustring@5@@Z ?getattribute@ShadingSystem@v1_14_4@OSL@@QEAA_NV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@PEAPEAD@Z ?getattribute@ShadingSystem@v1_14_4@OSL@@QEAA_NV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@UTypeDesc@5@PEAX@Z ?geterror@OSLQuery@v1_14_4@OSL@@QEAA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@_N@Z ?getmessage@RendererServices@v1_14_4@OSL@@UEAA_NPEAUShaderGlobals@23@Vustringhash@OpenImageIO_v3_0@@1UTypeDesc@6@PEAX_N@Z ?getparam@OSLQuery@v1_14_4@OSL@@QEBAPEBUParameter@123@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z ?getparam@OSLQuery@v1_14_4@OSL@@QEBAPEBUParameter@123@Vustring@OpenImageIO_v3_0@@@Z ?getparam@OSLQuery@v1_14_4@OSL@@QEBAPEBUParameter@123@_K@Z ?getstats@ShadingSystem@v1_14_4@OSL@@QEBA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@H@Z ?globals_bit@ShadingSystem@v1_14_4@OSL@@SA?AW4SGBits@23@Vustring@OpenImageIO_v3_0@@@Z ?globals_name@ShadingSystem@v1_14_4@OSL@@SA?AVustring@OpenImageIO_v3_0@@W4SGBits@23@@Z ?good@RendererServices@v1_14_4@OSL@@UEAA_NPEAVTextureHandle@TextureSystem@OpenImageIO_v3_0@@@Z ?group@ShadingContext@v1_14_4@OSL@@QEAAPEAVShaderGroup@23@XZ ?group@ShadingContext@v1_14_4@OSL@@QEAAXPEAVShaderGroup@23@@Z ?group@ShadingContext@v1_14_4@OSL@@QEBAPEBVShaderGroup@23@XZ ?has_masked_return_block@LLVM_Util@pvt@v1_14_4@OSL@@QEBA_NXZ ?incr_get_userdata_calls@ShadingContext@v1_14_4@OSL@@QEAAXXZ ?incr_layers_executed@ShadingContext@v1_14_4@OSL@@QEAAXXZ ?init@OSLQuery@v1_14_4@OSL@@QEAA_NPEBVShaderGroup@23@H@Z ?initialize_buffer@journal@v1_14_4@OSL@@YA_NQEAEIIH@Z ?inside_function@LLVM_Util@pvt@v1_14_4@OSL@@QEBA_NXZ ?inside_of_inlined_masked_function_call@LLVM_Util@pvt@v1_14_4@OSL@@AEBA_NXZ ?inside_of_masked_loop@LLVM_Util@pvt@v1_14_4@OSL@@AEBA_NXZ ?int_as_mask@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z ?int_to_ptr_cast@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z ?internalize_module_functions@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEBV?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@6@1@Z ?is_innermost_loop_masked@LLVM_Util@pvt@v1_14_4@OSL@@QEBA_NXZ ?is_masking_required@LLVM_Util@pvt@v1_14_4@OSL@@QEBA_NXZ ?is_type_array@LLVM_Util@pvt@v1_14_4@OSL@@QEAA_NPEAVType@llvm@@@Z ?is_udim@RendererServices@v1_14_4@OSL@@UEAA_NPEAVTextureHandle@TextureSystem@OpenImageIO_v3_0@@@Z ?jit_aggressive@LLVM_Util@pvt@v1_14_4@OSL@@QEAAX_N@Z ?jit_aggressive@LLVM_Util@pvt@v1_14_4@OSL@@QEBA_NXZ ?jit_fma@LLVM_Util@pvt@v1_14_4@OSL@@QEAAX_N@Z ?jit_fma@LLVM_Util@pvt@v1_14_4@OSL@@QEBA_NXZ ?llvm_alignmentof@LLVM_Util@pvt@v1_14_4@OSL@@QEBA_KPEAVType@llvm@@@Z ?llvm_mask_to_native@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z ?llvm_sizeof@LLVM_Util@pvt@v1_14_4@OSL@@QEBA_KPEAVType@llvm@@@Z ?llvm_thread_info@ShadingContext@v1_14_4@OSL@@QEBAAEBUPerThreadInfo@LLVM_Util@pvt@23@XZ ?llvm_type@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVType@llvm@@AEBUTypeDesc@OpenImageIO_v3_0@@@Z ?llvm_typename@LLVM_Util@pvt@v1_14_4@OSL@@QEBA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@PEAVType@llvm@@@Z ?llvm_typenameof@LLVM_Util@pvt@v1_14_4@OSL@@QEBA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@PEAVValue@llvm@@@Z ?llvm_typeof@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@PEAVValue@6@@Z ?llvm_vector_type@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVType@llvm@@AEBUTypeDesc@OpenImageIO_v3_0@@@Z ?llvm_vector_type@LLVM_Util@pvt@v1_14_4@OSL@@SAPEAVVectorType@llvm@@PEAVType@6@I@Z ?lookup_blackbody_rgb@ColorSystem@pvt@v1_14_4@OSL@@QEBA?AV?$Color3@M@Imath_3_1@@M@Z ?lookup_isa_by_name@LLVM_Util@pvt@v1_14_4@OSL@@SA?AW4TargetISA@234@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@@Z ?loop_after_block@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVBasicBlock@llvm@@XZ ?loop_step_block@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVBasicBlock@llvm@@XZ ?luminance@ColorSystem@pvt@v1_14_4@OSL@@QEBAMAEBV?$Color3@M@Imath_3_1@@@Z ?luminance_scale@ColorSystem@pvt@v1_14_4@OSL@@QEBAAEBV?$Color3@M@Imath_3_1@@XZ ?make_function@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVFunction@llvm@@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@_NPEAVType@6@V?$span@QEAVType@llvm@@$0?0@OpenImageIO_v3_0@@1@Z ?make_jit_execengine@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVExecutionEngine@llvm@@PEAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@W4TargetISA@234@_N2@Z ?mark_fast_func_call@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVValue@llvm@@@Z ?mask4_as_int8@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z ?mask_as_int16@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z ?mask_as_int8@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z ?mask_as_int@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z ?masked_break_count@LLVM_Util@pvt@v1_14_4@OSL@@QEBAHXZ ?masked_continue_count@LLVM_Util@pvt@v1_14_4@OSL@@QEBAHXZ ?masked_exit_count@LLVM_Util@pvt@v1_14_4@OSL@@QEBAHXZ ?masked_function_context@LLVM_Util@pvt@v1_14_4@OSL@@AEAAAEAUMaskedSubroutineContext@1234@XZ ?masked_function_context@LLVM_Util@pvt@v1_14_4@OSL@@AEBAAEBUMaskedSubroutineContext@1234@XZ ?masked_loop_context@LLVM_Util@pvt@v1_14_4@OSL@@AEAAAEAUMaskedLoopContext@1234@XZ ?masked_loop_context@LLVM_Util@pvt@v1_14_4@OSL@@AEBAAEBUMaskedLoopContext@1234@XZ ?masked_return_block@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVBasicBlock@llvm@@XZ ?masked_return_count@LLVM_Util@pvt@v1_14_4@OSL@@QEBAHXZ ?masked_shader_context@LLVM_Util@pvt@v1_14_4@OSL@@AEAAAEAUMaskedSubroutineContext@1234@XZ ?messages@ShadingContext@v1_14_4@OSL@@QEAAAEAUMessageList@pvt@23@XZ ?metadata@OSLQuery@v1_14_4@OSL@@QEBAAEBV?$vector@UParameter@OSLQuery@v1_14_4@OSL@@V?$allocator@UParameter@OSLQuery@v1_14_4@OSL@@@std@@@std@@XZ ?module@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVModule@llvm@@XZ ?module@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVModule@llvm@@@Z ?module_from_bitcode@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVModule@llvm@@PEBD_KAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@PEAV78@@Z ?module_string@LLVM_Util@pvt@v1_14_4@OSL@@QEAA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ ?move@Accumulator@v1_14_4@OSL@@QEAAXPEBVustring@OpenImageIO_v3_0@@@Z ?move@Accumulator@v1_14_4@OSL@@QEAAXVustring@OpenImageIO_v3_0@@0PEBV45@0@Z ?move@Accumulator@v1_14_4@OSL@@QEAAXVustring@OpenImageIO_v3_0@@@Z ?native_to_llvm_mask@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z ?negate_mask@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z ?new_basic_block@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVBasicBlock@llvm@@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z ?new_builder@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVBasicBlock@llvm@@@Z ?new_module@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVModule@llvm@@PEBD@Z ?nparams@OSLQuery@v1_14_4@OSL@@QEBA_KXZ ?nvptx_target_machine@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVTargetMachine@llvm@@XZ ?ocio_transform@ColorSystem@pvt@v1_14_4@OSL@@QEBA?AV?$Color3@M@Imath_3_1@@Vustringhash@OpenImageIO_v3_0@@0AEBV56@PEAVShadingContext@34@PEAUShaderGlobals@34@@Z ?ocio_transform@ColorSystem@pvt@v1_14_4@OSL@@QEBA?AV?$Dual@V?$Color3@M@Imath_3_1@@$01@34@Vustringhash@OpenImageIO_v3_0@@0AEBV534@PEAVShadingContext@34@PEAUShaderGlobals@34@@Z ?offset_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@0PEAVType@6@@Z ?offset_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@HPEAVType@6@@Z ?op_1st_active_lane_of@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z ?op_add@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@0@Z ?op_alloca@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@AEBUTypeDesc@OpenImageIO_v3_0@@HAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@H@Z ?op_alloca@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAVPointerType@6@HAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@H@Z ?op_alloca@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAVType@6@HAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@H@Z ?op_and@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@0@Z ?op_bool_to_float@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z ?op_bool_to_int@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z ?op_branch@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVBasicBlock@llvm@@@Z ?op_branch@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVValue@llvm@@PEAVBasicBlock@6@1@Z ?op_combine_4x_vectors@LLVM_Util@pvt@v1_14_4@OSL@@AEAAPEAVValue@llvm@@PEAV56@0@Z ?op_combine_8x_vectors@LLVM_Util@pvt@v1_14_4@OSL@@AEAAPEAVValue@llvm@@PEAV56@0@Z ?op_div@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@0@Z ?op_eq@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@0_N@Z ?op_extract@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@0@Z ?op_extract@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@H@Z ?op_fabs@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z ?op_float_to_double@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z ?op_float_to_int@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z ?op_gather@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAVType@6@PEAV56@1@Z ?op_ge@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@0_N@Z ?op_gt@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@0_N@Z ?op_insert@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@0H@Z ?op_int8_to_int@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z ?op_int_to_bool@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z ?op_int_to_float@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z ?op_int_to_int8@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z ?op_int_to_longlong@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z ?op_is_not_finite@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z ?op_lanes_that_match_masked@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@00@Z ?op_le@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@0_N@Z ?op_linearize_16x_indices@LLVM_Util@pvt@v1_14_4@OSL@@AEAAPEAVValue@llvm@@PEAV56@@Z ?op_linearize_4x_indices@LLVM_Util@pvt@v1_14_4@OSL@@AEAAPEAVValue@llvm@@PEAV56@@Z ?op_linearize_8x_indices@LLVM_Util@pvt@v1_14_4@OSL@@AEAAPEAVValue@llvm@@PEAV56@@Z ?op_load@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAVType@6@PEAV56@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z ?op_load_mask@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z ?op_lt@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@0_N@Z ?op_masked_break@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXXZ ?op_masked_continue@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXXZ ?op_masked_exit@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXXZ ?op_masked_return@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXXZ ?op_memcpy@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVValue@llvm@@0HH@Z ?op_memcpy@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVValue@llvm@@H0HH@Z ?op_memset@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVValue@llvm@@H0H@Z ?op_memset@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVValue@llvm@@HHH@Z ?op_mod@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@0@Z ?op_mul@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@0@Z ?op_ne@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@0_N@Z ?op_neg@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z ?op_not@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z ?op_or@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@0@Z ?op_quarter_16x@LLVM_Util@pvt@v1_14_4@OSL@@AEAA?AV?$array@PEAVValue@llvm@@$03@std@@PEAVValue@llvm@@@Z ?op_return@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVValue@llvm@@@Z ?op_scatter@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVValue@llvm@@PEAVType@6@00@Z ?op_select@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@00@Z ?op_shl@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@0@Z ?op_shr@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@0@Z ?op_split_16x@LLVM_Util@pvt@v1_14_4@OSL@@AEAA?AV?$array@PEAVValue@llvm@@$01@std@@PEAVValue@llvm@@@Z ?op_split_8x@LLVM_Util@pvt@v1_14_4@OSL@@AEAA?AV?$array@PEAVValue@llvm@@$01@std@@PEAVValue@llvm@@@Z ?op_store@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVValue@llvm@@0@Z ?op_store_mask@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVValue@llvm@@0@Z ?op_sub@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@0@Z ?op_unmasked_store@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVValue@llvm@@0@Z ?op_xor@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@0@Z ?op_zero_if@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@0@Z ?optimize_all_groups@ShadingSystem@v1_14_4@OSL@@QEAAXH_N@Z ?optimize_group@ShadingSystem@v1_14_4@OSL@@QEAAXPEAVShaderGroup@23@HHPEAVShadingContext@23@_N@Z ?optimize_group@ShadingSystem@v1_14_4@OSL@@QEAAXPEAVShaderGroup@23@PEAVShadingContext@23@_N@Z ?osl_get_attribute@ShadingContext@v1_14_4@OSL@@QEAA_NPEAUShaderGlobals@23@PEAXHVustringhash@OpenImageIO_v3_0@@2HHUTypeDesc@6@1@Z ?oslquery@ShadingSystem@v1_14_4@OSL@@QEAA?AVOSLQuery@23@AEBVShaderGroup@23@H@Z ?parameters@OSLQuery@v1_14_4@OSL@@QEBAAEBV?$vector@UParameter@OSLQuery@v1_14_4@OSL@@V?$allocator@UParameter@OSLQuery@v1_14_4@OSL@@@std@@@std@@XZ ?pointcloud_get@RendererServices@v1_14_4@OSL@@UEAAHPEAUShaderGlobals@23@Vustringhash@OpenImageIO_v3_0@@PEBHH1UTypeDesc@6@PEAX@Z ?pointcloud_search@RendererServices@v1_14_4@OSL@@UEAAHPEAUShaderGlobals@23@Vustringhash@OpenImageIO_v3_0@@AEBV?$Vec3@M@Imath_3_1@@MH_NPEAHPEAMH@Z ?pointcloud_write@RendererServices@v1_14_4@OSL@@UEAA_NPEAUShaderGlobals@23@Vustringhash@OpenImageIO_v3_0@@AEBV?$Vec3@M@Imath_3_1@@HPEBV56@PEBUTypeDesc@6@PEAPEBX@Z ?popState@Accumulator@v1_14_4@OSL@@QEAAXXZ ?pop_function@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXXZ ?pop_function_mask@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXXZ ?pop_loop@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXXZ ?pop_mask@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXXZ ?pop_masked_loop@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXXZ ?pop_masked_return_block@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXXZ ?pop_shader_instance@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXXZ ?print_closure@pvt@v1_14_4@OSL@@YAXAEAV?$basic_ostream@DU?$char_traits@D@std@@@std@@PEBUClosureColor@23@PEAVShadingSystemImpl@123@_N@Z ?printfmt@RendererServices@v1_14_4@OSL@@UEAAXPEAUShaderGlobals@23@Vustringhash@OpenImageIO_v3_0@@HPEBW4EncodedType@23@IPEAE@Z ?process@Reader@journal@v1_14_4@OSL@@QEAAXXZ ?process_entries_for_thread@Reader@journal@v1_14_4@OSL@@AEAAXH@Z ?process_errors@ShadingContext@v1_14_4@OSL@@QEBAXXZ ?prune_and_internalize_module@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXV?$unordered_set@PEAVFunction@llvm@@U?$hash@PEAVFunction@llvm@@@std@@U?$equal_to@PEAVFunction@llvm@@@4@V?$allocator@PEAVFunction@llvm@@@4@@std@@W4Linkage@1234@PEAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@6@@Z ?ptr_cast@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@AEBUTypeDesc@OpenImageIO_v3_0@@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z ?ptr_cast@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@PEAVPointerType@6@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z ?ptr_cast@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@PEAVType@6@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z ?ptr_to_cast@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@PEAVType@6@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z ?ptr_to_int64_cast@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z ?ptx_compile_group@LLVM_Util@pvt@v1_14_4@OSL@@QEAA_NPEAVModule@llvm@@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAV78@@Z ?pushState@Accumulator@v1_14_4@OSL@@QEAAXXZ ?push_function@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVBasicBlock@llvm@@PEAV56@@Z ?push_function_mask@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVValue@llvm@@@Z ?push_loop@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVBasicBlock@llvm@@0@Z ?push_mask@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVValue@llvm@@_N1@Z ?push_masked_loop@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVValue@llvm@@0@Z ?push_masked_return_block@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVBasicBlock@llvm@@@Z ?push_shader_instance@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVValue@llvm@@@Z ?query_closure@ShadingSystem@v1_14_4@OSL@@QEAA_NPEAPEBDPEAHPEAPEBUClosureParam@23@@Z ?raytype_bit@ShadingSystem@v1_14_4@OSL@@QEAAHVustring@OpenImageIO_v3_0@@@Z ?raytype_bit@ShadingSystemImpl@pvt@v1_14_4@OSL@@QEAAHVustring@OpenImageIO_v3_0@@@Z ?record_error@ShadingContext@v1_14_4@OSL@@QEBAXW4ErrCode@ErrorHandler@OpenImageIO_v3_0@@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z ?record_errorfmt@Writer@journal@v1_14_4@OSL@@QEAA_NHHVustringhash@OpenImageIO_v3_0@@HPEBW4EncodedType@34@IPEAE@Z ?record_filefmt@Writer@journal@v1_14_4@OSL@@QEAA_NHHVustringhash@OpenImageIO_v3_0@@0HPEBW4EncodedType@34@IPEAE@Z ?record_printfmt@Writer@journal@v1_14_4@OSL@@QEAA_NHHVustringhash@OpenImageIO_v3_0@@HPEBW4EncodedType@34@IPEAE@Z ?record_runtime_stats@ShadingContext@v1_14_4@OSL@@QEAAXXZ ?record_warningfmt@Writer@journal@v1_14_4@OSL@@QEAA_NHHHVustringhash@OpenImageIO_v3_0@@HPEBW4EncodedType@34@IPEAE@Z ?register_JIT_Global@v1_14_4@OSL@@YAXPEBDPEAX@Z ?register_closure@ShadingSystem@v1_14_4@OSL@@QEAAXV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@HPEBUClosureParam@23@P6AXPEAVRendererServices@23@HPEAX@Z4@Z ?register_inline_function@ShadingSystem@v1_14_4@OSL@@QEAAXVustring@OpenImageIO_v3_0@@@Z ?register_noinline_function@ShadingSystem@v1_14_4@OSL@@QEAAXVustring@OpenImageIO_v3_0@@@Z ?release_context@ShadingSystem@v1_14_4@OSL@@QEAAXPEAVShadingContext@23@@Z ?renderer@ShadingContext@v1_14_4@OSL@@QEBAPEAVRendererServices@23@XZ ?renderer@ShadingSystem@v1_14_4@OSL@@QEBAPEAVRendererServices@23@XZ ?report_error@Report2ErrorHandler@journal@v1_14_4@OSL@@UEAAXHHAEBV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@@Z ?report_file_print@Report2ErrorHandler@journal@v1_14_4@OSL@@UEAAXHHAEBV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@0@Z ?report_print@Report2ErrorHandler@journal@v1_14_4@OSL@@UEAAXHHAEBV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@@Z ?report_warning@Report2ErrorHandler@journal@v1_14_4@OSL@@UEAAXHHAEBV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@@Z ?requiredForPageTransition@Writer@journal@v1_14_4@OSL@@CA_KXZ ?reserve_heap@ShadingContext@v1_14_4@OSL@@QEAAX_K@Z ?return_block@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVBasicBlock@llvm@@XZ ?setAov@Accumulator@v1_14_4@OSL@@QEAAXHPEAVAov@23@_N1@Z ?set_colorspace@ColorSystem@pvt@v1_14_4@OSL@@QEAA_NVustringhash@OpenImageIO_v3_0@@@Z ?set_insert_point@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVBasicBlock@llvm@@@Z ?set_masking_required@LLVM_Util@pvt@v1_14_4@OSL@@AEAAX_N@Z ?set_raytypes@ShadingSystem@v1_14_4@OSL@@QEAAXPEAVShaderGroup@23@HH@Z ?set_target_isa@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXW4TargetISA@234@@Z ?setup_legacy_optimization_passes@LLVM_Util@pvt@v1_14_4@OSL@@AEAAXH_N@Z ?setup_new_optimization_passes@LLVM_Util@pvt@v1_14_4@OSL@@AEAAXH_N@Z ?setup_optimization_passes@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXH_N@Z ?shade_image@v1_14_4@OSL@@YA_NAEAVShadingSystem@12@AEAVShaderGroup@12@PEBUShaderGlobals@12@AEAVImageBuf@OpenImageIO_v3_0@@V?$span@$$CBVustring@OpenImageIO_v3_0@@$0?0@7@W4ShadeImageLocations@12@UROI@7@Vparallel_options@7@@Z ?shade_image@v1_14_4@OSL@@YA_NAEAVShadingSystem@12@AEAVShaderGroup@12@PEBUShaderGlobals@12@AEAVImageBuf@OpenImageIO_v3_0@@V?$span@$$CBVustring@OpenImageIO_v3_0@@$0?0@7@W4ShadeImageLocations@12@UROI@7@Vparopt@7@@Z ?shader_mask@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@XZ ?shadername@OSLQuery@v1_14_4@OSL@@QEBA?BVustring@OpenImageIO_v3_0@@XZ ?shadertype@OSLQuery@v1_14_4@OSL@@QEBA?BVustring@OpenImageIO_v3_0@@XZ ?shadingsys@ShadingContext@v1_14_4@OSL@@QEBAAEAVShadingSystemImpl@pvt@23@XZ ?shouldReportError@TrackRecentlyReported@journal@v1_14_4@OSL@@QEAA_NAEBV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@@Z ?shouldReportWarning@TrackRecentlyReported@journal@v1_14_4@OSL@@QEAA_NAEBV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@@Z ?supports@RendererServices@v1_14_4@OSL@@UEBAHV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@@Z ?supports_avx2@LLVM_Util@pvt@v1_14_4@OSL@@QEBA_NXZ ?supports_avx512f@LLVM_Util@pvt@v1_14_4@OSL@@QEBA_NXZ ?supports_avx@LLVM_Util@pvt@v1_14_4@OSL@@QEBA_NXZ ?supports_isa@LLVM_Util@pvt@v1_14_4@OSL@@SA_NW4TargetISA@234@@Z ?supports_llvm_bit_masks_natively@LLVM_Util@pvt@v1_14_4@OSL@@QEBA_NXZ ?supports_masked_stores@LLVM_Util@pvt@v1_14_4@OSL@@QEBA_NXZ ?symbol@ShadingContext@v1_14_4@OSL@@QEBAPEBVSymbol@pvt@23@Vustring@OpenImageIO_v3_0@@0@Z ?symbol@ShadingContext@v1_14_4@OSL@@QEBAPEBVSymbol@pvt@23@Vustring@OpenImageIO_v3_0@@@Z ?symbol_address@ShadingSystem@v1_14_4@OSL@@QEBAPEBXAEBVShadingContext@23@PEBVShaderSymbol@23@@Z ?symbol_data@ShadingContext@v1_14_4@OSL@@QEBAPEBXAEBVSymbol@pvt@23@@Z ?symbol_typedesc@ShadingSystem@v1_14_4@OSL@@QEBA?AUTypeDesc@OpenImageIO_v3_0@@PEBVShaderSymbol@23@@Z ?target_isa@LLVM_Util@pvt@v1_14_4@OSL@@QEBA?AW4TargetISA@234@XZ ?target_isa_name@LLVM_Util@pvt@v1_14_4@OSL@@SAPEBDW4TargetISA@234@@Z ?test@Accumulator@v1_14_4@OSL@@QEAA_NVustring@OpenImageIO_v3_0@@0PEBV45@0@Z ?test_if_mask_is_non_zero@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z ?test_mask_lane@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@0@Z ?test_mask_lane@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@H@Z ?texture3d@RendererServices@v1_14_4@OSL@@UEAA_NVustringhash@OpenImageIO_v3_0@@PEAVTextureHandle@TextureSystem@5@PEAVPerthread@75@AEAVTextureOpt_v2@5@PEAUShaderGlobals@23@AEBV?$Vec3@M@Imath_3_1@@555HPEAM666PEAV45@@Z ?texture@RendererServices@v1_14_4@OSL@@UEAA_NVustringhash@OpenImageIO_v3_0@@PEAVTextureHandle@TextureSystem@5@PEAVPerthread@75@AEAVTextureOpt_v2@5@PEAUShaderGlobals@23@MMMMMMHPEAM55PEAV45@@Z ?texture_thread_info@ShadingContext@v1_14_4@OSL@@QEAAXPEAVPerthread@TextureSystem@OpenImageIO_v3_0@@@Z ?texture_thread_info@ShadingContext@v1_14_4@OSL@@QEBAPEAVPerthread@TextureSystem@OpenImageIO_v3_0@@XZ ?texturesys@RendererServices@v1_14_4@OSL@@UEBAPEAVTextureSystem@OpenImageIO_v3_0@@XZ ?texturesys@ShadingSystem@v1_14_4@OSL@@QEBAPEAVTextureSystem@OpenImageIO_v3_0@@XZ ?thread_info@ShadingContext@v1_14_4@OSL@@QEBAPEAUPerThreadInfo@23@XZ ?toAlpha@AccumRule@v1_14_4@OSL@@QEBA_NXZ ?to_rgb@ColorSystem@pvt@v1_14_4@OSL@@QEBA?AV?$Color3@M@Imath_3_1@@Vustringhash@OpenImageIO_v3_0@@AEBV56@PEAVShadingContext@34@PEAUShaderGlobals@34@@Z ?total_jit_memory_held@LLVM_Util@pvt@v1_14_4@OSL@@SA_KXZ ?trace@RendererServices@v1_14_4@OSL@@UEAA_NAEAUTraceOpt@23@PEAUShaderGlobals@23@AEBV?$Vec3@M@Imath_3_1@@22222@Z ?transform_points@RendererServices@v1_14_4@OSL@@UEAA_NPEAUShaderGlobals@23@Vustringhash@OpenImageIO_v3_0@@1MPEBV?$Vec3@M@Imath_3_1@@PEAV78@HW4VECSEMANTICS@TypeDesc@6@@Z ?transformc@ColorSystem@pvt@v1_14_4@OSL@@QEBA?AV?$Color3@M@Imath_3_1@@Vustringhash@OpenImageIO_v3_0@@0AEBV56@PEAVShadingContext@34@PEAUShaderGlobals@34@@Z ?transformc@ColorSystem@pvt@v1_14_4@OSL@@QEBA?AV?$Dual@V?$Color3@M@Imath_3_1@@$01@34@Vustringhash@OpenImageIO_v3_0@@0AEBV534@PEAVShadingContext@34@PEAUShaderGlobals@34@@Z ?type_addrint@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ ?type_array@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVType@llvm@@PEAV56@H@Z ?type_bool@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ ?type_bool_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVPointerType@llvm@@XZ ?type_char@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ ?type_char_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVPointerType@llvm@@XZ ?type_double@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ ?type_double_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVPointerType@llvm@@XZ ?type_float@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ ?type_float_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVPointerType@llvm@@XZ ?type_function@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVFunctionType@llvm@@PEAVType@6@V?$span@QEAVType@llvm@@$0?0@OpenImageIO_v3_0@@_N@Z ?type_function_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVPointerType@llvm@@PEAVType@6@V?$span@QEAVType@llvm@@$0?0@OpenImageIO_v3_0@@_N@Z ?type_int16@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ ?type_int64@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ ?type_int64_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVPointerType@llvm@@XZ ?type_int8@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ ?type_int8_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVPointerType@llvm@@XZ ?type_int@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ ?type_int_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVPointerType@llvm@@XZ ?type_longlong@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ ?type_longlong_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVPointerType@llvm@@XZ ?type_matrix@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ ?type_matrix_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVPointerType@llvm@@XZ ?type_native_mask@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ ?type_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVPointerType@llvm@@PEAVType@6@@Z ?type_real_ustring@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVType@llvm@@XZ ?type_struct@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVType@llvm@@V?$span@QEAVType@llvm@@$0?0@OpenImageIO_v3_0@@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@_N@Z ?type_struct_field_at_index@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVType@llvm@@PEAV56@H@Z ?type_triple@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ ?type_triple_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVPointerType@llvm@@XZ ?type_typedesc@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ ?type_union@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVType@llvm@@V?$span@QEAVType@llvm@@$0?0@OpenImageIO_v3_0@@@Z ?type_ustring@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVType@llvm@@XZ ?type_ustring_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVPointerType@llvm@@XZ ?type_void@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ ?type_void_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVPointerType@llvm@@XZ ?type_wide@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVType@llvm@@PEAV56@@Z ?type_wide_bool@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ ?type_wide_bool_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVPointerType@llvm@@XZ ?type_wide_char@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ ?type_wide_char_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVPointerType@llvm@@XZ ?type_wide_double@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ ?type_wide_float@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ ?type_wide_float_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVPointerType@llvm@@XZ ?type_wide_int@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ ?type_wide_int_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVPointerType@llvm@@XZ ?type_wide_longlong@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ ?type_wide_matrix@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ ?type_wide_triple@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ ?type_wide_ustring@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ ?type_wide_ustring_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVPointerType@llvm@@XZ ?type_wide_void_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ ?unregister_inline_function@ShadingSystem@v1_14_4@OSL@@QEAAXVustring@OpenImageIO_v3_0@@@Z ?unregister_noinline_function@ShadingSystem@v1_14_4@OSL@@QEAAXVustring@OpenImageIO_v3_0@@@Z ?ustring_rep@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXW4UstringRep@1234@@Z ?ustring_rep@LLVM_Util@pvt@v1_14_4@OSL@@QEBA?AW4UstringRep@1234@XZ ?validate_global_mappings@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXAEAV?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@@Z ?validate_struct_data_layout@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVType@llvm@@AEBV?$vector@IV?$allocator@I@std@@@std@@@Z ?void_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z ?void_ptr_null@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@XZ ?warningfmt@RendererServices@v1_14_4@OSL@@UEAAXPEAUShaderGlobals@23@Vustringhash@OpenImageIO_v3_0@@HPEBW4EncodedType@23@IPEAE@Z ?wide_constant@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@H@Z ?wide_constant@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@HH@Z ?wide_constant@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@HM@Z ?wide_constant@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@M@Z ?wide_constant@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@PEAV56@@Z ?wide_constant@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@_K@Z ?wide_constant@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@@Z ?wide_constant@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@Vustring@OpenImageIO_v3_0@@@Z ?wide_constant_bool@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@_N@Z ?wide_op_alloca@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@AEBUTypeDesc@OpenImageIO_v3_0@@HAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@H@Z ?wide_ptr_cast@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@AEBUTypeDesc@OpenImageIO_v3_0@@@Z ?widen_value@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z ?write_bitcode_file@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEBDPEAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z ?write_entry@Writer@journal@v1_14_4@OSL@@AEAA_NHHW4Content@pvt@234@Vustringhash@OpenImageIO_v3_0@@1HPEBW4EncodedType@34@IPEAE@Z __IMPORT_DESCRIPTOR_oslexec __NULL_IMPORT_DESCRIPTOR __imp_??0AccumAutomata@v1_14_4@OSL@@QEAA@AEBV012@@Z __imp_??0AccumAutomata@v1_14_4@OSL@@QEAA@XZ __imp_??0AccumRule@v1_14_4@OSL@@QEAA@H_N@Z __imp_??0Accumulator@v1_14_4@OSL@@QEAA@$$QEAV012@@Z __imp_??0Accumulator@v1_14_4@OSL@@QEAA@AEBV012@@Z __imp_??0Accumulator@v1_14_4@OSL@@QEAA@PEBVAccumAutomata@12@@Z __imp_??0ClosureComponent@v1_14_4@OSL@@QEAA@$$QEAU012@@Z __imp_??0ClosureComponent@v1_14_4@OSL@@QEAA@AEBU012@@Z __imp_??0ClosureComponent@v1_14_4@OSL@@QEAA@XZ __imp_??0ClosureMul@v1_14_4@OSL@@QEAA@$$QEAU012@@Z __imp_??0ClosureMul@v1_14_4@OSL@@QEAA@AEBU012@@Z __imp_??0ClosureMul@v1_14_4@OSL@@QEAA@XZ __imp_??0ColorSystem@pvt@v1_14_4@OSL@@QEAA@$$QEAV0123@@Z __imp_??0ColorSystem@pvt@v1_14_4@OSL@@QEAA@AEBV0123@@Z __imp_??0ColorSystem@pvt@v1_14_4@OSL@@QEAA@XZ __imp_??0DfOptimizedAutomata@v1_14_4@OSL@@QEAA@$$QEAV012@@Z __imp_??0DfOptimizedAutomata@v1_14_4@OSL@@QEAA@AEBV012@@Z __imp_??0DfOptimizedAutomata@v1_14_4@OSL@@QEAA@XZ __imp_??0LLVM_Util@pvt@v1_14_4@OSL@@QEAA@AEBUPerThreadInfo@0123@HH@Z __imp_??0LLVM_Util@pvt@v1_14_4@OSL@@QEAA@AEBV0123@@Z __imp_??0OSLQuery@v1_14_4@OSL@@QEAA@AEBV012@@Z __imp_??0OSLQuery@v1_14_4@OSL@@QEAA@PEBVShaderGroup@12@H@Z __imp_??0OSLQuery@v1_14_4@OSL@@QEAA@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@0@Z __imp_??0Parameter@OSLQuery@v1_14_4@OSL@@QEAA@XZ __imp_??0PerThreadInfo@LLVM_Util@pvt@v1_14_4@OSL@@QEAA@XZ __imp_??0Reader@journal@v1_14_4@OSL@@QEAA@PEBEAEAVReporter@123@@Z __imp_??0RendererServices@v1_14_4@OSL@@QEAA@AEBV012@@Z __imp_??0RendererServices@v1_14_4@OSL@@QEAA@PEAVTextureSystem@OpenImageIO_v3_0@@@Z __imp_??0Report2ErrorHandler@journal@v1_14_4@OSL@@QEAA@$$QEAV0123@@Z __imp_??0Report2ErrorHandler@journal@v1_14_4@OSL@@QEAA@AEBV0123@@Z __imp_??0Report2ErrorHandler@journal@v1_14_4@OSL@@QEAA@PEAVErrorHandler@OpenImageIO_v3_0@@AEAVTrackRecentlyReported@123@@Z __imp_??0Reporter@journal@v1_14_4@OSL@@QEAA@AEBV0123@@Z __imp_??0Reporter@journal@v1_14_4@OSL@@QEAA@XZ __imp_??0ScopedJitMemoryUser@LLVM_Util@pvt@v1_14_4@OSL@@QEAA@XZ __imp_??0ShadingContext@v1_14_4@OSL@@QEAA@AEAVShadingSystemImpl@pvt@12@PEAUPerThreadInfo@12@@Z __imp_??0ShadingSystem@v1_14_4@OSL@@QEAA@PEAVRendererServices@12@PEAVTextureSystem@OpenImageIO_v3_0@@PEAVErrorHandler@5@@Z __imp_??0TrackRecentlyReported@journal@v1_14_4@OSL@@QEAA@$$QEAV0123@@Z __imp_??0TrackRecentlyReported@journal@v1_14_4@OSL@@QEAA@AEBV0123@@Z __imp_??0TrackRecentlyReported@journal@v1_14_4@OSL@@QEAA@_NH0H@Z __imp_??0Writer@journal@v1_14_4@OSL@@QEAA@PEAX@Z __imp_??1AccumAutomata@v1_14_4@OSL@@QEAA@XZ __imp_??1Accumulator@v1_14_4@OSL@@QEAA@XZ __imp_??1DfOptimizedAutomata@v1_14_4@OSL@@QEAA@XZ __imp_??1LLVM_Util@pvt@v1_14_4@OSL@@QEAA@XZ __imp_??1Parameter@OSLQuery@v1_14_4@OSL@@QEAA@XZ __imp_??1PerThreadInfo@LLVM_Util@pvt@v1_14_4@OSL@@QEAA@XZ __imp_??1RendererServices@v1_14_4@OSL@@UEAA@XZ __imp_??1Report2ErrorHandler@journal@v1_14_4@OSL@@UEAA@XZ __imp_??1Reporter@journal@v1_14_4@OSL@@UEAA@XZ __imp_??1ScopedJitMemoryUser@LLVM_Util@pvt@v1_14_4@OSL@@QEAA@XZ __imp_??1ShadingContext@v1_14_4@OSL@@QEAA@XZ __imp_??1ShadingSystem@v1_14_4@OSL@@QEAA@XZ __imp_??1TrackRecentlyReported@journal@v1_14_4@OSL@@QEAA@XZ __imp_??4AccumAutomata@v1_14_4@OSL@@QEAAAEAV012@AEBV012@@Z __imp_??4AccumRule@v1_14_4@OSL@@QEAAAEAV012@$$QEAV012@@Z __imp_??4AccumRule@v1_14_4@OSL@@QEAAAEAV012@AEBV012@@Z __imp_??4Accumulator@v1_14_4@OSL@@QEAAAEAV012@$$QEAV012@@Z __imp_??4Accumulator@v1_14_4@OSL@@QEAAAEAV012@AEBV012@@Z __imp_??4ClosureAdd@v1_14_4@OSL@@QEAAAEAU012@$$QEAU012@@Z __imp_??4ClosureAdd@v1_14_4@OSL@@QEAAAEAU012@AEBU012@@Z __imp_??4ClosureColor@v1_14_4@OSL@@QEAAAEAU012@$$QEAU012@@Z __imp_??4ClosureColor@v1_14_4@OSL@@QEAAAEAU012@AEBU012@@Z __imp_??4ClosureComponent@v1_14_4@OSL@@QEAAAEAU012@$$QEAU012@@Z __imp_??4ClosureComponent@v1_14_4@OSL@@QEAAAEAU012@AEBU012@@Z __imp_??4ClosureMul@v1_14_4@OSL@@QEAAAEAU012@$$QEAU012@@Z __imp_??4ClosureMul@v1_14_4@OSL@@QEAAAEAU012@AEBU012@@Z __imp_??4ColorSystem@pvt@v1_14_4@OSL@@QEAAAEAV0123@$$QEAV0123@@Z __imp_??4ColorSystem@pvt@v1_14_4@OSL@@QEAAAEAV0123@AEBV0123@@Z __imp_??4DfOptimizedAutomata@v1_14_4@OSL@@QEAAAEAV012@$$QEAV012@@Z __imp_??4DfOptimizedAutomata@v1_14_4@OSL@@QEAAAEAV012@AEBV012@@Z __imp_??4LLVM_Util@pvt@v1_14_4@OSL@@QEAAAEAV0123@AEBV0123@@Z __imp_??4Labels@v1_14_4@OSL@@QEAAAEAV012@$$QEAV012@@Z __imp_??4Labels@v1_14_4@OSL@@QEAAAEAV012@AEBV012@@Z __imp_??4OSLQuery@v1_14_4@OSL@@QEAAAEAV012@AEBV012@@Z __imp_??4PerThreadInfo@LLVM_Util@pvt@v1_14_4@OSL@@QEAAAEAU01234@AEBU01234@@Z __imp_??4RendererServices@v1_14_4@OSL@@QEAAAEAV012@AEBV012@@Z __imp_??4Reporter@journal@v1_14_4@OSL@@QEAAAEAV0123@AEBV0123@@Z __imp_??4ScopedJitMemoryUser@LLVM_Util@pvt@v1_14_4@OSL@@QEAAAEAU01234@AEBU01234@@Z __imp_??4ShadingSystem@v1_14_4@OSL@@QEAAAEAV012@AEBV012@@Z __imp_??_7RendererServices@v1_14_4@OSL@@6B@ __imp_??_7Report2ErrorHandler@journal@v1_14_4@OSL@@6B@ __imp_??_7Reporter@journal@v1_14_4@OSL@@6B@ __imp_??_FRendererServices@v1_14_4@OSL@@QEAAXXZ __imp_??_FShadingSystem@v1_14_4@OSL@@QEAAXXZ __imp_??_FTrackRecentlyReported@journal@v1_14_4@OSL@@QEAAXXZ __imp_?ACES2065_1@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?ACEScg@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?AdobeRGB@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?BACKGROUND@Labels@v1_14_4@OSL@@2Vustring@OpenImageIO_v3_0@@B __imp_?CAMERA@Labels@v1_14_4@OSL@@2Vustring@OpenImageIO_v3_0@@B __imp_?CIE@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?Ci@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?ConnectShaders@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@111@Z __imp_?ConnectShaders@ShadingSystem@v1_14_4@OSL@@QEAA_NV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@000@Z __imp_?DIFFUSE@Labels@v1_14_4@OSL@@2Vustring@OpenImageIO_v3_0@@B __imp_?EBU@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?ErrorColorTransform@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?GEP@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAVType@6@PEAV56@1AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z __imp_?GEP@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAVType@6@PEAV56@HAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z __imp_?GEP@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAVType@6@PEAV56@HHAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z __imp_?GEP@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAVType@6@PEAV56@HHHAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z __imp_?GLOSSY@Labels@v1_14_4@OSL@@2Vustring@OpenImageIO_v3_0@@B __imp_?HDTV@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?I@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?InstallLazyFunctionCreator@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXP6APEAXAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z@Z __imp_?LIGHT@Labels@v1_14_4@OSL@@2Vustring@OpenImageIO_v3_0@@B __imp_?LoadMemoryCompiledShader@ShadingSystem@v1_14_4@OSL@@QEAA_NV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@0@Z __imp_?N@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?NDC@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?NONE@Labels@v1_14_4@OSL@@2Vustring@OpenImageIO_v3_0@@B __imp_?NTSC@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?Ng@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?OBJECT@Labels@v1_14_4@OSL@@2Vustring@OpenImageIO_v3_0@@B __imp_?P@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?PAL@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?Parameter@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@W4ParamHints@23@@Z __imp_?Parameter@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@_N@Z __imp_?Parameter@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@HW4ParamHints@23@@Z __imp_?Parameter@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@H_N@Z __imp_?Parameter@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@MW4ParamHints@23@@Z __imp_?Parameter@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@M_N@Z __imp_?Parameter@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@UTypeDesc@6@PEBXW4ParamHints@23@@Z __imp_?Parameter@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@UTypeDesc@6@PEBX_N@Z __imp_?Parameter@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@Vustring@6@W4ParamHints@23@@Z __imp_?Parameter@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@Vustring@6@_N@Z __imp_?Parameter@ShadingSystem@v1_14_4@OSL@@QEAA_NV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@UTypeDesc@5@PEBXW4ParamHints@23@@Z __imp_?Parameter@ShadingSystem@v1_14_4@OSL@@QEAA_NV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@UTypeDesc@5@PEBX_N@Z __imp_?Ps@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?REFLECT@Labels@v1_14_4@OSL@@2Vustring@OpenImageIO_v3_0@@B __imp_?RGB@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?ReParameter@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@1AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z __imp_?ReParameter@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@1H@Z __imp_?ReParameter@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@1M@Z __imp_?ReParameter@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@1UTypeDesc@6@PEBX@Z __imp_?ReParameter@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@1Vustring@6@@Z __imp_?Rec709@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?SECAM@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?SINGULAR@Labels@v1_14_4@OSL@@2Vustring@OpenImageIO_v3_0@@B __imp_?SMPTE@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?STOP@Labels@v1_14_4@OSL@@2Vustring@OpenImageIO_v3_0@@B __imp_?STRAIGHT@Labels@v1_14_4@OSL@@2Vustring@OpenImageIO_v3_0@@B __imp_?SetupLLVM@LLVM_Util@pvt@v1_14_4@OSL@@AEAAXXZ __imp_?Shader@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@11@Z __imp_?Shader@ShadingSystem@v1_14_4@OSL@@QEAA_NV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@00@Z __imp_?ShaderGroupBegin@ShadingSystem@v1_14_4@OSL@@QEAA?AV?$shared_ptr@VShaderGroup@v1_14_4@OSL@@@std@@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@00@Z __imp_?ShaderGroupBegin@ShadingSystem@v1_14_4@OSL@@QEAA?AV?$shared_ptr@VShaderGroup@v1_14_4@OSL@@@std@@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@@Z __imp_?ShaderGroupEnd@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShaderGroup@23@@Z __imp_?ShaderGroupEnd@ShadingSystem@v1_14_4@OSL@@QEAA_NXZ __imp_?TRANSMIT@Labels@v1_14_4@OSL@@2Vustring@OpenImageIO_v3_0@@B __imp_?VOLUME@Labels@v1_14_4@OSL@@2Vustring@OpenImageIO_v3_0@@B __imp_?XYZ@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?YIQ@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?_emptystring_@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?absorb_module@LLVM_Util@pvt@v1_14_4@OSL@@QEAA_NV?$unique_ptr@VModule@llvm@@U?$default_delete@VModule@llvm@@@std@@@std@@@Z __imp_?accum@AccumAutomata@v1_14_4@OSL@@QEBAXHAEBV?$Color3@M@Imath_3_1@@AEAV?$vector@UAovOutput@v1_14_4@OSL@@V?$allocator@UAovOutput@v1_14_4@OSL@@@std@@@std@@@Z __imp_?accum@AccumRule@v1_14_4@OSL@@QEBAXAEBV?$Color3@M@Imath_3_1@@AEAV?$vector@UAovOutput@v1_14_4@OSL@@V?$allocator@UAovOutput@v1_14_4@OSL@@@std@@@std@@@Z __imp_?accum@Accumulator@v1_14_4@OSL@@QEAAXAEBV?$Color3@M@Imath_3_1@@@Z __imp_?addEventType@AccumAutomata@v1_14_4@OSL@@QEAAXVustring@OpenImageIO_v3_0@@@Z __imp_?addRule@AccumAutomata@v1_14_4@OSL@@QEAAPEAVAccumRule@23@PEBDH_N@Z __imp_?addScatteringType@AccumAutomata@v1_14_4@OSL@@QEAAXVustring@OpenImageIO_v3_0@@@Z __imp_?add_function_mapping@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVFunction@llvm@@PEAX@Z __imp_?add_global_mapping@LLVM_Util@pvt@v1_14_4@OSL@@SAXPEBDPEAX@Z __imp_?add_symlocs@ShadingSystem@v1_14_4@OSL@@QEAAXPEAVShaderGroup@23@V?$span@$$CBUSymLocationDesc@v1_14_4@OSL@@$0?0@OpenImageIO_v3_0@@@Z __imp_?add_symlocs@ShadingSystem@v1_14_4@OSL@@QEAAXV?$span@$$CBUSymLocationDesc@v1_14_4@OSL@@$0?0@OpenImageIO_v3_0@@@Z __imp_?alloc_scratch@ShadingContext@v1_14_4@OSL@@QEAAPEAX_K0@Z __imp_?allocatePage@Writer@journal@v1_14_4@OSL@@AEAA_NH@Z __imp_?allow_warnings@ShadingContext@v1_14_4@OSL@@QEAA_NXZ __imp_?alpha@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?anisotropic@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?append_error@OSLQuery@v1_14_4@OSL@@AEBAXAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z __imp_?apply_break_to_mask_stack@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXXZ __imp_?apply_continue_to_mask_stack@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXXZ __imp_?apply_exit_to_mask_stack@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXXZ __imp_?apply_return_to@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z __imp_?apply_return_to_mask_stack@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXXZ __imp_?archive_shadergroup@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@@Z __imp_?archive_shadergroup@ShadingSystem@v1_14_4@OSL@@QEAA_NPEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@@Z __imp_?arraylength@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?as_add@ClosureColor@v1_14_4@OSL@@QEBAPEBUClosureAdd@23@XZ __imp_?as_comp@ClosureColor@v1_14_4@OSL@@QEBAPEBUClosureComponent@23@XZ __imp_?as_mul@ClosureColor@v1_14_4@OSL@@QEBAPEBUClosureMul@23@XZ __imp_?assume_ptr_is_aligned@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVValue@llvm@@I@Z __imp_?attribute@ShadingSystem@v1_14_4@OSL@@QEAA_NPEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@1@Z __imp_?attribute@ShadingSystem@v1_14_4@OSL@@QEAA_NPEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@H@Z __imp_?attribute@ShadingSystem@v1_14_4@OSL@@QEAA_NPEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@M@Z __imp_?attribute@ShadingSystem@v1_14_4@OSL@@QEAA_NPEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@N@Z __imp_?attribute@ShadingSystem@v1_14_4@OSL@@QEAA_NPEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@UTypeDesc@6@PEBX@Z __imp_?attribute@ShadingSystem@v1_14_4@OSL@@QEAA_NV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@0@Z __imp_?attribute@ShadingSystem@v1_14_4@OSL@@QEAA_NV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@H@Z __imp_?attribute@ShadingSystem@v1_14_4@OSL@@QEAA_NV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@M@Z __imp_?attribute@ShadingSystem@v1_14_4@OSL@@QEAA_NV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@N@Z __imp_?attribute@ShadingSystem@v1_14_4@OSL@@QEAA_NV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@UTypeDesc@5@PEBX@Z __imp_?back@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?bandwidth@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?batched@RendererServices@v1_14_4@OSL@@UEAAPEAV?$BatchedRendererServices@$03@23@V?$WidthOf@$03@23@@Z __imp_?batched@RendererServices@v1_14_4@OSL@@UEAAPEAV?$BatchedRendererServices@$07@23@V?$WidthOf@$07@23@@Z __imp_?batched@RendererServices@v1_14_4@OSL@@UEAAPEAV?$BatchedRendererServices@$0BA@@23@V?$WidthOf@$0BA@@23@@Z __imp_?begin@Accumulator@v1_14_4@OSL@@QEAAXXZ __imp_?begin@OSLQuery@v1_14_4@OSL@@QEAA?AV?$_Vector_iterator@V?$_Vector_val@U?$_Simple_types@UParameter@OSLQuery@v1_14_4@OSL@@@std@@@std@@@std@@XZ __imp_?bezier@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?bitcode_string@LLVM_Util@pvt@v1_14_4@OSL@@QEAA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@PEAVFunction@llvm@@@Z __imp_?bitcode_string@LLVM_Util@pvt@v1_14_4@OSL@@QEAA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@PEAVModule@llvm@@@Z __imp_?black@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?blackbody_rgb@ColorSystem@pvt@v1_14_4@OSL@@QEBA?AV?$Color3@M@Imath_3_1@@M@Z __imp_?blur@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?both@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?broken@Accumulator@v1_14_4@OSL@@QEBA_NXZ __imp_?bspline@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?build_attribute_getter@RendererServices@v1_14_4@OSL@@UEAAXAEBVShaderGroup@23@_NPEBVustring@OpenImageIO_v3_0@@21PEBHUTypeDesc@6@1AEAV?$FunctionSpec@V?$ArgVariant@W4AttributeSpecBuiltinArg@v1_14_4@OSL@@@v1_14_4@OSL@@@23@@Z __imp_?build_interpolated_getter@RendererServices@v1_14_4@OSL@@UEAAXAEBVShaderGroup@23@AEBVustring@OpenImageIO_v3_0@@UTypeDesc@6@_NAEAV?$FunctionSpec@V?$ArgVariant@W4InterpolatedSpecBuiltinArg@v1_14_4@OSL@@@v1_14_4@OSL@@@23@@Z __imp_?builder@LLVM_Util@pvt@v1_14_4@OSL@@AEAAAEAVIRBuilder@1234@XZ __imp_?call_function@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@V?$span@QEAVValue@llvm@@$0?0@OpenImageIO_v3_0@@@Z __imp_?call_function@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEBDPEAV56@111@Z __imp_?call_function@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEBDPEAV56@11@Z __imp_?call_function@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEBDPEAV56@1@Z __imp_?call_function@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEBDPEAV56@@Z __imp_?call_function@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEBDV?$span@QEAVValue@llvm@@$0?0@OpenImageIO_v3_0@@@Z __imp_?camera@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?can_lookup_blackbody@ColorSystem@pvt@v1_14_4@OSL@@QEBA_NM@Z __imp_?catmullrom@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?cbegin@OSLQuery@v1_14_4@OSL@@QEBA?AV?$_Vector_const_iterator@V?$_Vector_val@U?$_Simple_types@UParameter@OSLQuery@v1_14_4@OSL@@@std@@@std@@@std@@XZ __imp_?cell@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?cellnoise@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?cend@OSLQuery@v1_14_4@OSL@@QEBA?AV?$_Vector_const_iterator@V?$_Vector_val@U?$_Simple_types@UParameter@OSLQuery@v1_14_4@OSL@@@std@@@std@@@std@@XZ __imp_?clamp@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?clear_runtime_stats@ShadingContext@v1_14_4@OSL@@QEAAXXZ __imp_?clear_symlocs@ShadingSystem@v1_14_4@OSL@@QEAAXPEAVShaderGroup@23@@Z __imp_?clear_symlocs@ShadingSystem@v1_14_4@OSL@@QEAAXXZ __imp_?closest@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?closure_add_allot@ShadingContext@v1_14_4@OSL@@QEAAPEAUClosureAdd@23@PEBUClosureColor@23@0@Z __imp_?closure_component_allot@ShadingContext@v1_14_4@OSL@@QEAAPEAUClosureComponent@23@H_KAEBV?$Color3@M@Imath_3_1@@@Z __imp_?closure_mul_allot@ShadingContext@v1_14_4@OSL@@QEAAPEAUClosureMul@23@AEBV?$Color3@M@Imath_3_1@@PEBUClosureColor@23@@Z __imp_?closure_mul_allot@ShadingContext@v1_14_4@OSL@@QEAAPEAUClosureMul@23@MPEBUClosureColor@23@@Z __imp_?color@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?colorspace@ColorSystem@pvt@v1_14_4@OSL@@QEBAAEBVustringhash@OpenImageIO_v3_0@@XZ __imp_?colorsystem@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?common@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?compile@AccumAutomata@v1_14_4@OSL@@QEAAXXZ __imp_?compileFrom@DfOptimizedAutomata@v1_14_4@OSL@@QEAAXAEBVDfAutomata@23@@Z __imp_?compute_blackbody_rgb@ColorSystem@pvt@v1_14_4@OSL@@QEBA?AV?$Color3@M@Imath_3_1@@M@Z __imp_?constant128@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@_K0@Z __imp_?constant128@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@_K@Z __imp_?constant16@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@F@Z __imp_?constant16@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@G@Z __imp_?constant4@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@C@Z __imp_?constant4@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@E@Z __imp_?constant64@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@N@Z __imp_?constant64@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@_K@Z __imp_?constant8@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@C@Z __imp_?constant8@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@E@Z __imp_?constant@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@AEBUTypeDesc@OpenImageIO_v3_0@@@Z __imp_?constant@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@H@Z __imp_?constant@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@I@Z __imp_?constant@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@M@Z __imp_?constant@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@_K@Z __imp_?constant@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@@Z __imp_?constant@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@Vustring@OpenImageIO_v3_0@@@Z __imp_?constant@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?constant_array@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@V?$span@QEAVConstant@llvm@@$0?0@OpenImageIO_v3_0@@@Z __imp_?constant_bool@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@_N@Z __imp_?constant_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAXPEAVPointerType@6@@Z __imp_?constanti64@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@_J@Z __imp_?context@LLVM_Util@pvt@v1_14_4@OSL@@QEBAAEAVLLVMContext@llvm@@XZ __imp_?convert_value@ShadingSystem@v1_14_4@OSL@@SA_NPEAXUTypeDesc@OpenImageIO_v3_0@@PEBX1@Z __imp_?copy_to_device@RendererServices@v1_14_4@OSL@@UEAAPEAXPEAXPEBX_K@Z __imp_?create_global_constant@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVGlobalVariable@llvm@@PEAVConstant@6@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z __imp_?create_masking_scope@LLVM_Util@pvt@v1_14_4@OSL@@QEAA?AUScopedMasking@1234@_N@Z __imp_?create_thread_info@ShadingSystem@v1_14_4@OSL@@QEAAPEAUPerThreadInfo@23@XZ __imp_?cubic@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?current_function@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVFunction@llvm@@@Z __imp_?current_function@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVFunction@llvm@@XZ __imp_?current_function_arg@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@H@Z __imp_?current_mask@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@XZ __imp_?dPdtime@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?dPdu@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?dPdv@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?data@ClosureComponent@v1_14_4@OSL@@QEAAPEAXXZ __imp_?data@ClosureComponent@v1_14_4@OSL@@QEBAPEBXXZ __imp_?debug@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXH@Z __imp_?debug@LLVM_Util@pvt@v1_14_4@OSL@@QEBAHXZ __imp_?debug_is_enabled@LLVM_Util@pvt@v1_14_4@OSL@@QEBA_NXZ __imp_?debug_pop_function@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXXZ __imp_?debug_pop_inlined_function@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXXZ __imp_?debug_push_function@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@Vustring@OpenImageIO_v3_0@@H@Z __imp_?debug_push_inlined_function@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXVustring@OpenImageIO_v3_0@@0H@Z __imp_?debug_set_location@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXVustring@OpenImageIO_v3_0@@H@Z __imp_?debug_setup_compilation_unit@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEBD@Z __imp_?default_@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?delete_func_body@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVFunction@llvm@@@Z __imp_?destroy_thread_info@ShadingSystem@v1_14_4@OSL@@QEAAXPEAUPerThreadInfo@23@@Z __imp_?detect_cpu_features@LLVM_Util@pvt@v1_14_4@OSL@@QEAA_NW4TargetISA@234@_N@Z __imp_?device_alloc@RendererServices@v1_14_4@OSL@@UEAAPEAX_K@Z __imp_?device_free@RendererServices@v1_14_4@OSL@@UEAAXPEAX@Z __imp_?dict_find@ShadingContext@v1_14_4@OSL@@QEAAHPEAUShaderGlobals@23@HVustring@OpenImageIO_v3_0@@@Z __imp_?dict_find@ShadingContext@v1_14_4@OSL@@QEAAHPEAUShaderGlobals@23@Vustring@OpenImageIO_v3_0@@1@Z __imp_?dict_next@ShadingContext@v1_14_4@OSL@@QEAAHH@Z __imp_?dict_value@ShadingContext@v1_14_4@OSL@@QEAAHHVustring@OpenImageIO_v3_0@@UTypeDesc@5@PEAX_N@Z __imp_?direction@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?do_filter@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?do_optimize@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z __imp_?dtime@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?dump_struct_data_layout@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVType@llvm@@@Z __imp_?dumpasm@LLVM_Util@pvt@v1_14_4@OSL@@QEAAX_N@Z __imp_?dumpasm@LLVM_Util@pvt@v1_14_4@OSL@@QEBA_NXZ __imp_?element_type_of@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVType@llvm@@PEAV56@@Z __imp_?end@Accumulator@v1_14_4@OSL@@QEAAXPEAX@Z __imp_?end@OSLQuery@v1_14_4@OSL@@QEAA?AV?$_Vector_iterator@V?$_Vector_val@U?$_Simple_types@UParameter@OSLQuery@v1_14_4@OSL@@@std@@@std@@@std@@XZ __imp_?end@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?end_builder@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXXZ __imp_?environment@RendererServices@v1_14_4@OSL@@UEAA_NVustringhash@OpenImageIO_v3_0@@PEAVTextureHandle@TextureSystem@5@PEAVPerthread@75@AEAVTextureOpt_v2@5@PEAUShaderGlobals@23@AEBV?$Vec3@M@Imath_3_1@@55HPEAM66PEAV45@@Z __imp_?error@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?errorfmt@RendererServices@v1_14_4@OSL@@UEAAXPEAUShaderGlobals@23@Vustringhash@OpenImageIO_v3_0@@HPEBW4EncodedType@23@IPEAE@Z __imp_?errormessage@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?execengine@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVExecutionEngine@llvm@@XZ __imp_?execengine@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVExecutionEngine@llvm@@@Z __imp_?execute@ShadingContext@v1_14_4@OSL@@QEAA_NAEAVShaderGroup@23@HHAEAUShaderGlobals@23@PEAX2_N@Z __imp_?execute@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShadingContext@23@AEAVShaderGroup@23@AEAUShaderGlobals@23@_N@Z __imp_?execute@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShadingContext@23@AEAVShaderGroup@23@HHAEAUShaderGlobals@23@PEAX3_N@Z __imp_?execute@ShadingSystem@v1_14_4@OSL@@QEAA_NPEAVShadingContext@23@AEAVShaderGroup@23@AEAUShaderGlobals@23@_N@Z __imp_?execute_cleanup@ShadingContext@v1_14_4@OSL@@QEAA_NXZ __imp_?execute_cleanup@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShadingContext@23@@Z __imp_?execute_init@ShadingContext@v1_14_4@OSL@@QEAA_NAEAVShaderGroup@23@HHAEAUShaderGlobals@23@PEAX2_N@Z __imp_?execute_init@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShadingContext@23@AEAVShaderGroup@23@AEAUShaderGlobals@23@_N@Z __imp_?execute_init@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShadingContext@23@AEAVShaderGroup@23@HHAEAUShaderGlobals@23@PEAX3_N@Z __imp_?execute_layer@ShadingContext@v1_14_4@OSL@@QEAA_NHHAEAUShaderGlobals@23@PEAX1H@Z __imp_?execute_layer@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShadingContext@23@AEAUShaderGlobals@23@H@Z __imp_?execute_layer@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShadingContext@23@AEAUShaderGlobals@23@PEBVShaderSymbol@23@@Z __imp_?execute_layer@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShadingContext@23@AEAUShaderGlobals@23@Vustring@OpenImageIO_v3_0@@@Z __imp_?execute_layer@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShadingContext@23@HHAEAUShaderGlobals@23@PEAX2H@Z __imp_?execute_layer@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShadingContext@23@HHAEAUShaderGlobals@23@PEAX2PEBVShaderSymbol@23@@Z __imp_?execute_layer@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShadingContext@23@HHAEAUShaderGlobals@23@PEAX2Vustring@OpenImageIO_v3_0@@@Z __imp_?execution_is_batched@ShadingContext@v1_14_4@OSL@@AEBA_NXZ __imp_?filefmt@RendererServices@v1_14_4@OSL@@UEAAXPEAUShaderGlobals@23@Vustringhash@OpenImageIO_v3_0@@1HPEBW4EncodedType@23@IPEAE@Z __imp_?fill@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?find_layer@ShadingSystem@v1_14_4@OSL@@QEBAHAEBVShaderGroup@23@Vustring@OpenImageIO_v3_0@@@Z __imp_?find_regex@ShadingContext@v1_14_4@OSL@@QEAAAEBV?$basic_regex@DV?$regex_traits@D@std@@@std@@Vustring@OpenImageIO_v3_0@@@Z __imp_?find_symbol@ShadingSystem@v1_14_4@OSL@@QEBAPEBVShaderSymbol@23@AEBVShaderGroup@23@Vustring@OpenImageIO_v3_0@@1@Z __imp_?find_symbol@ShadingSystem@v1_14_4@OSL@@QEBAPEBVShaderSymbol@23@AEBVShaderGroup@23@Vustring@OpenImageIO_v3_0@@@Z __imp_?find_symloc@ShadingSystem@v1_14_4@OSL@@QEBAPEBUSymLocationDesc@23@PEBVShaderGroup@23@Vustring@OpenImageIO_v3_0@@@Z __imp_?find_symloc@ShadingSystem@v1_14_4@OSL@@QEBAPEBUSymLocationDesc@23@PEBVShaderGroup@23@Vustring@OpenImageIO_v3_0@@W4SymArena@23@@Z __imp_?find_symloc@ShadingSystem@v1_14_4@OSL@@QEBAPEBUSymLocationDesc@23@Vustring@OpenImageIO_v3_0@@@Z __imp_?find_symloc@ShadingSystem@v1_14_4@OSL@@QEBAPEBUSymLocationDesc@23@Vustring@OpenImageIO_v3_0@@W4SymArena@23@@Z __imp_?firstchannel@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?free_dict_resources@ShadingContext@v1_14_4@OSL@@AEAAXXZ __imp_?fromString@ColorSystem@pvt@v1_14_4@OSL@@SAPEBUChroma@1234@Vustringhash@OpenImageIO_v3_0@@@Z __imp_?from_rgb@ColorSystem@pvt@v1_14_4@OSL@@QEBA?AV?$Color3@M@Imath_3_1@@Vustringhash@OpenImageIO_v3_0@@AEBV56@PEAVShadingContext@34@PEAUShaderGlobals@34@@Z __imp_?front@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?func_is_empty@LLVM_Util@pvt@v1_14_4@OSL@@QEAA_NPEAVFunction@llvm@@@Z __imp_?func_name@LLVM_Util@pvt@v1_14_4@OSL@@QEAA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@PEAVFunction@llvm@@@Z __imp_?gabor@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?gabornoise@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?gaborpnoise@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?genericnoise@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?genericpnoise@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?get@PerThreadInfo@LLVM_Util@pvt@v1_14_4@OSL@@AEBAPEAUImpl@12345@XZ __imp_?getCurrentDebugScope@LLVM_Util@pvt@v1_14_4@OSL@@AEBAPEAVDIScope@llvm@@XZ __imp_?getCurrentInliningSite@LLVM_Util@pvt@v1_14_4@OSL@@AEBAPEAVDILocation@llvm@@XZ __imp_?getOrCreateDebugFileFor@LLVM_Util@pvt@v1_14_4@OSL@@AEAAPEAVDIFile@llvm@@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z __imp_?getOutput@Accumulator@v1_14_4@OSL@@QEBAAEBUAovOutput@23@H@Z __imp_?getOutputIndex@AccumRule@v1_14_4@OSL@@QEBAHXZ __imp_?getPointerToFunction@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAXPEAVFunction@llvm@@@Z __imp_?getRuleList@AccumAutomata@v1_14_4@OSL@@QEBAAEBV?$list@VAccumRule@v1_14_4@OSL@@V?$allocator@VAccumRule@v1_14_4@OSL@@@std@@@std@@XZ __imp_?getRules@DfOptimizedAutomata@v1_14_4@OSL@@QEBAPEBQEAXHAEAH@Z __imp_?getRulesInState@AccumAutomata@v1_14_4@OSL@@QEBAPEBQEAXHAEAH@Z __imp_?getTransition@AccumAutomata@v1_14_4@OSL@@QEBAHHVustring@OpenImageIO_v3_0@@@Z __imp_?getTransition@DfOptimizedAutomata@v1_14_4@OSL@@QEBAHHVustring@OpenImageIO_v3_0@@@Z __imp_?get_array_attribute@RendererServices@v1_14_4@OSL@@UEAA_NPEAUShaderGlobals@23@_NVustringhash@OpenImageIO_v3_0@@UTypeDesc@6@2HPEAX@Z __imp_?get_attribute@RendererServices@v1_14_4@OSL@@UEAA_NPEAUShaderGlobals@23@_NVustringhash@OpenImageIO_v3_0@@UTypeDesc@6@2PEAX@Z __imp_?get_context@ShadingSystem@v1_14_4@OSL@@QEAAPEAVShadingContext@23@PEAUPerThreadInfo@23@PEAVPerthread@TextureSystem@OpenImageIO_v3_0@@@Z __imp_?get_inverse_matrix@RendererServices@v1_14_4@OSL@@UEAA_NPEAUShaderGlobals@23@AEAV?$Matrix44@M@Imath_3_1@@PEBX@Z __imp_?get_inverse_matrix@RendererServices@v1_14_4@OSL@@UEAA_NPEAUShaderGlobals@23@AEAV?$Matrix44@M@Imath_3_1@@PEBXM@Z __imp_?get_inverse_matrix@RendererServices@v1_14_4@OSL@@UEAA_NPEAUShaderGlobals@23@AEAV?$Matrix44@M@Imath_3_1@@Vustringhash@OpenImageIO_v3_0@@@Z __imp_?get_inverse_matrix@RendererServices@v1_14_4@OSL@@UEAA_NPEAUShaderGlobals@23@AEAV?$Matrix44@M@Imath_3_1@@Vustringhash@OpenImageIO_v3_0@@M@Z __imp_?get_matrix@RendererServices@v1_14_4@OSL@@UEAA_NPEAUShaderGlobals@23@AEAV?$Matrix44@M@Imath_3_1@@PEBX@Z __imp_?get_matrix@RendererServices@v1_14_4@OSL@@UEAA_NPEAUShaderGlobals@23@AEAV?$Matrix44@M@Imath_3_1@@PEBXM@Z __imp_?get_matrix@RendererServices@v1_14_4@OSL@@UEAA_NPEAUShaderGlobals@23@AEAV?$Matrix44@M@Imath_3_1@@Vustringhash@OpenImageIO_v3_0@@@Z __imp_?get_matrix@RendererServices@v1_14_4@OSL@@UEAA_NPEAUShaderGlobals@23@AEAV?$Matrix44@M@Imath_3_1@@Vustringhash@OpenImageIO_v3_0@@M@Z __imp_?get_max_warnings_per_thread@pvt@v1_14_4@OSL@@YAHQEAX@Z __imp_?get_symbol@ShadingSystem@v1_14_4@OSL@@QEBAPEBXAEBVShadingContext@23@Vustring@OpenImageIO_v3_0@@1AEAUTypeDesc@6@@Z __imp_?get_symbol@ShadingSystem@v1_14_4@OSL@@QEBAPEBXAEBVShadingContext@23@Vustring@OpenImageIO_v3_0@@AEAUTypeDesc@6@@Z __imp_?get_texture_handle@RendererServices@v1_14_4@OSL@@UEAAPEAVTextureHandle@TextureSystem@OpenImageIO_v3_0@@Vustring@6@PEAVShadingContext@23@PEBVTextureOpt_v2@6@@Z __imp_?get_texture_handle@RendererServices@v1_14_4@OSL@@UEAAPEAVTextureHandle@TextureSystem@OpenImageIO_v3_0@@Vustringhash@6@PEAVShadingContext@23@PEBVTextureOpt_v2@6@@Z __imp_?get_texture_info@RendererServices@v1_14_4@OSL@@UEAA_NVustringhash@OpenImageIO_v3_0@@PEAVTextureHandle@TextureSystem@5@MMPEAVPerthread@75@PEAUShaderGlobals@23@H0UTypeDesc@5@PEAXPEAV45@@Z __imp_?get_texture_info@RendererServices@v1_14_4@OSL@@UEAA_NVustringhash@OpenImageIO_v3_0@@PEAVTextureHandle@TextureSystem@5@PEAVPerthread@75@PEAUShaderGlobals@23@H0UTypeDesc@5@PEAXPEAV45@@Z __imp_?get_userdata@RendererServices@v1_14_4@OSL@@UEAA_N_NVustringhash@OpenImageIO_v3_0@@UTypeDesc@5@PEAUShaderGlobals@23@PEAX@Z __imp_?getattribute@ShadingSystem@v1_14_4@OSL@@QEAA_NPEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@AEAH@Z __imp_?getattribute@ShadingSystem@v1_14_4@OSL@@QEAA_NPEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@AEAM@Z __imp_?getattribute@ShadingSystem@v1_14_4@OSL@@QEAA_NPEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@AEAN@Z __imp_?getattribute@ShadingSystem@v1_14_4@OSL@@QEAA_NPEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@AEAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z __imp_?getattribute@ShadingSystem@v1_14_4@OSL@@QEAA_NPEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@AEAVustring@6@@Z __imp_?getattribute@ShadingSystem@v1_14_4@OSL@@QEAA_NPEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@PEAPEAD@Z __imp_?getattribute@ShadingSystem@v1_14_4@OSL@@QEAA_NPEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@UTypeDesc@6@PEAX@Z __imp_?getattribute@ShadingSystem@v1_14_4@OSL@@QEAA_NV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@AEAH@Z __imp_?getattribute@ShadingSystem@v1_14_4@OSL@@QEAA_NV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@AEAM@Z __imp_?getattribute@ShadingSystem@v1_14_4@OSL@@QEAA_NV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@AEAN@Z __imp_?getattribute@ShadingSystem@v1_14_4@OSL@@QEAA_NV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@AEAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z __imp_?getattribute@ShadingSystem@v1_14_4@OSL@@QEAA_NV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@AEAVustring@5@@Z __imp_?getattribute@ShadingSystem@v1_14_4@OSL@@QEAA_NV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@PEAPEAD@Z __imp_?getattribute@ShadingSystem@v1_14_4@OSL@@QEAA_NV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@UTypeDesc@5@PEAX@Z __imp_?geterror@OSLQuery@v1_14_4@OSL@@QEAA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@_N@Z __imp_?getmessage@RendererServices@v1_14_4@OSL@@UEAA_NPEAUShaderGlobals@23@Vustringhash@OpenImageIO_v3_0@@1UTypeDesc@6@PEAX_N@Z __imp_?getparam@OSLQuery@v1_14_4@OSL@@QEBAPEBUParameter@123@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z __imp_?getparam@OSLQuery@v1_14_4@OSL@@QEBAPEBUParameter@123@Vustring@OpenImageIO_v3_0@@@Z __imp_?getparam@OSLQuery@v1_14_4@OSL@@QEBAPEBUParameter@123@_K@Z __imp_?getstats@ShadingSystem@v1_14_4@OSL@@QEBA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@H@Z __imp_?globals_bit@ShadingSystem@v1_14_4@OSL@@SA?AW4SGBits@23@Vustring@OpenImageIO_v3_0@@@Z __imp_?globals_name@ShadingSystem@v1_14_4@OSL@@SA?AVustring@OpenImageIO_v3_0@@W4SGBits@23@@Z __imp_?good@RendererServices@v1_14_4@OSL@@UEAA_NPEAVTextureHandle@TextureSystem@OpenImageIO_v3_0@@@Z __imp_?group@ShadingContext@v1_14_4@OSL@@QEAAPEAVShaderGroup@23@XZ __imp_?group@ShadingContext@v1_14_4@OSL@@QEAAXPEAVShaderGroup@23@@Z __imp_?group@ShadingContext@v1_14_4@OSL@@QEBAPEBVShaderGroup@23@XZ __imp_?has_masked_return_block@LLVM_Util@pvt@v1_14_4@OSL@@QEBA_NXZ __imp_?hash@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?hashnoise@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?hermite@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?hsl@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?hsv@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?impulses@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?incr_get_userdata_calls@ShadingContext@v1_14_4@OSL@@QEAAXXZ __imp_?incr_layers_executed@ShadingContext@v1_14_4@OSL@@QEAAXXZ __imp_?init@OSLQuery@v1_14_4@OSL@@QEAA_NPEBVShaderGroup@23@H@Z __imp_?initialize_buffer@journal@v1_14_4@OSL@@YA_NQEAEIIH@Z __imp_?inside_function@LLVM_Util@pvt@v1_14_4@OSL@@QEBA_NXZ __imp_?inside_of_inlined_masked_function_call@LLVM_Util@pvt@v1_14_4@OSL@@AEBA_NXZ __imp_?inside_of_masked_loop@LLVM_Util@pvt@v1_14_4@OSL@@AEBA_NXZ __imp_?int_as_mask@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z __imp_?int_to_ptr_cast@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z __imp_?internalize_module_functions@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEBV?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@6@1@Z __imp_?interp@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?is_innermost_loop_masked@LLVM_Util@pvt@v1_14_4@OSL@@QEBA_NXZ __imp_?is_masking_required@LLVM_Util@pvt@v1_14_4@OSL@@QEBA_NXZ __imp_?is_type_array@LLVM_Util@pvt@v1_14_4@OSL@@QEAA_NPEAVType@llvm@@@Z __imp_?is_udim@RendererServices@v1_14_4@OSL@@UEAA_NPEAVTextureHandle@TextureSystem@OpenImageIO_v3_0@@@Z __imp_?jit_aggressive@LLVM_Util@pvt@v1_14_4@OSL@@QEAAX_N@Z __imp_?jit_aggressive@LLVM_Util@pvt@v1_14_4@OSL@@QEBA_NXZ __imp_?jit_fma@LLVM_Util@pvt@v1_14_4@OSL@@QEAAX_N@Z __imp_?jit_fma@LLVM_Util@pvt@v1_14_4@OSL@@QEBA_NXZ __imp_?label@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?linear@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?llvm_alignmentof@LLVM_Util@pvt@v1_14_4@OSL@@QEBA_KPEAVType@llvm@@@Z __imp_?llvm_mask_to_native@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z __imp_?llvm_sizeof@LLVM_Util@pvt@v1_14_4@OSL@@QEBA_KPEAVType@llvm@@@Z __imp_?llvm_thread_info@ShadingContext@v1_14_4@OSL@@QEBAAEBUPerThreadInfo@LLVM_Util@pvt@23@XZ __imp_?llvm_type@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVType@llvm@@AEBUTypeDesc@OpenImageIO_v3_0@@@Z __imp_?llvm_typename@LLVM_Util@pvt@v1_14_4@OSL@@QEBA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@PEAVType@llvm@@@Z __imp_?llvm_typenameof@LLVM_Util@pvt@v1_14_4@OSL@@QEBA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@PEAVValue@llvm@@@Z __imp_?llvm_typeof@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@PEAVValue@6@@Z __imp_?llvm_vector_type@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVType@llvm@@AEBUTypeDesc@OpenImageIO_v3_0@@@Z __imp_?llvm_vector_type@LLVM_Util@pvt@v1_14_4@OSL@@SAPEAVVectorType@llvm@@PEAVType@6@I@Z __imp_?lookup_blackbody_rgb@ColorSystem@pvt@v1_14_4@OSL@@QEBA?AV?$Color3@M@Imath_3_1@@M@Z __imp_?lookup_isa_by_name@LLVM_Util@pvt@v1_14_4@OSL@@SA?AW4TargetISA@234@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@@Z __imp_?loop_after_block@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVBasicBlock@llvm@@XZ __imp_?loop_step_block@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVBasicBlock@llvm@@XZ __imp_?luminance@ColorSystem@pvt@v1_14_4@OSL@@QEBAMAEBV?$Color3@M@Imath_3_1@@@Z __imp_?luminance_scale@ColorSystem@pvt@v1_14_4@OSL@@QEBAAEBV?$Color3@M@Imath_3_1@@XZ __imp_?make_function@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVFunction@llvm@@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@_NPEAVType@6@V?$span@QEAVType@llvm@@$0?0@OpenImageIO_v3_0@@1@Z __imp_?make_jit_execengine@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVExecutionEngine@llvm@@PEAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@W4TargetISA@234@_N2@Z __imp_?mark_fast_func_call@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVValue@llvm@@@Z __imp_?mask4_as_int8@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z __imp_?mask_as_int16@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z __imp_?mask_as_int8@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z __imp_?mask_as_int@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z __imp_?masked_break_count@LLVM_Util@pvt@v1_14_4@OSL@@QEBAHXZ __imp_?masked_continue_count@LLVM_Util@pvt@v1_14_4@OSL@@QEBAHXZ __imp_?masked_exit_count@LLVM_Util@pvt@v1_14_4@OSL@@QEBAHXZ __imp_?masked_function_context@LLVM_Util@pvt@v1_14_4@OSL@@AEAAAEAUMaskedSubroutineContext@1234@XZ __imp_?masked_function_context@LLVM_Util@pvt@v1_14_4@OSL@@AEBAAEBUMaskedSubroutineContext@1234@XZ __imp_?masked_loop_context@LLVM_Util@pvt@v1_14_4@OSL@@AEAAAEAUMaskedLoopContext@1234@XZ __imp_?masked_loop_context@LLVM_Util@pvt@v1_14_4@OSL@@AEBAAEBUMaskedLoopContext@1234@XZ __imp_?masked_return_block@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVBasicBlock@llvm@@XZ __imp_?masked_return_count@LLVM_Util@pvt@v1_14_4@OSL@@QEBAHXZ __imp_?masked_shader_context@LLVM_Util@pvt@v1_14_4@OSL@@AEAAAEAUMaskedSubroutineContext@1234@XZ __imp_?matrix@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?maxdist@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?messages@ShadingContext@v1_14_4@OSL@@QEAAAEAUMessageList@pvt@23@XZ __imp_?metadata@OSLQuery@v1_14_4@OSL@@QEBAAEBV?$vector@UParameter@OSLQuery@v1_14_4@OSL@@V?$allocator@UParameter@OSLQuery@v1_14_4@OSL@@@std@@@std@@XZ __imp_?mindist@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?mirror@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?missingalpha@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?missingcolor@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?module@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVModule@llvm@@XZ __imp_?module@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVModule@llvm@@@Z __imp_?module_from_bitcode@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVModule@llvm@@PEBD_KAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@PEAV78@@Z __imp_?module_string@LLVM_Util@pvt@v1_14_4@OSL@@QEAA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ __imp_?move@Accumulator@v1_14_4@OSL@@QEAAXPEBVustring@OpenImageIO_v3_0@@@Z __imp_?move@Accumulator@v1_14_4@OSL@@QEAAXVustring@OpenImageIO_v3_0@@0PEBV45@0@Z __imp_?move@Accumulator@v1_14_4@OSL@@QEAAXVustring@OpenImageIO_v3_0@@@Z __imp_?native_to_llvm_mask@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z __imp_?negate_mask@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z __imp_?new_basic_block@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVBasicBlock@llvm@@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z __imp_?new_builder@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVBasicBlock@llvm@@@Z __imp_?new_module@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVModule@llvm@@PEBD@Z __imp_?noise@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?normal@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?nparams@OSLQuery@v1_14_4@OSL@@QEBA_KXZ __imp_?null@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?nvptx_target_machine@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVTargetMachine@llvm@@XZ __imp_?object@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?ocio_transform@ColorSystem@pvt@v1_14_4@OSL@@QEBA?AV?$Color3@M@Imath_3_1@@Vustringhash@OpenImageIO_v3_0@@0AEBV56@PEAVShadingContext@34@PEAUShaderGlobals@34@@Z __imp_?ocio_transform@ColorSystem@pvt@v1_14_4@OSL@@QEBA?AV?$Dual@V?$Color3@M@Imath_3_1@@$01@34@Vustringhash@OpenImageIO_v3_0@@0AEBV534@PEAVShadingContext@34@PEAUShaderGlobals@34@@Z __imp_?offset_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@0PEAVType@6@@Z __imp_?offset_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@HPEAVType@6@@Z __imp_?op_1st_active_lane_of@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z __imp_?op_add@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@0@Z __imp_?op_alloca@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@AEBUTypeDesc@OpenImageIO_v3_0@@HAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@H@Z __imp_?op_alloca@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAVPointerType@6@HAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@H@Z __imp_?op_alloca@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAVType@6@HAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@H@Z __imp_?op_and@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@0@Z __imp_?op_bool_to_float@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z __imp_?op_bool_to_int@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z __imp_?op_branch@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVBasicBlock@llvm@@@Z __imp_?op_branch@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVValue@llvm@@PEAVBasicBlock@6@1@Z __imp_?op_combine_4x_vectors@LLVM_Util@pvt@v1_14_4@OSL@@AEAAPEAVValue@llvm@@PEAV56@0@Z __imp_?op_combine_8x_vectors@LLVM_Util@pvt@v1_14_4@OSL@@AEAAPEAVValue@llvm@@PEAV56@0@Z __imp_?op_div@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@0@Z __imp_?op_dowhile@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?op_eq@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@0_N@Z __imp_?op_exit@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?op_extract@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@0@Z __imp_?op_extract@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@H@Z __imp_?op_fabs@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z __imp_?op_float_to_double@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z __imp_?op_float_to_int@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z __imp_?op_for@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?op_gather@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAVType@6@PEAV56@1@Z __imp_?op_ge@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@0_N@Z __imp_?op_gt@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@0_N@Z __imp_?op_insert@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@0H@Z __imp_?op_int8_to_int@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z __imp_?op_int_to_bool@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z __imp_?op_int_to_float@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z __imp_?op_int_to_int8@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z __imp_?op_int_to_longlong@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z __imp_?op_is_not_finite@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z __imp_?op_lanes_that_match_masked@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@00@Z __imp_?op_le@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@0_N@Z __imp_?op_linearize_16x_indices@LLVM_Util@pvt@v1_14_4@OSL@@AEAAPEAVValue@llvm@@PEAV56@@Z __imp_?op_linearize_4x_indices@LLVM_Util@pvt@v1_14_4@OSL@@AEAAPEAVValue@llvm@@PEAV56@@Z __imp_?op_linearize_8x_indices@LLVM_Util@pvt@v1_14_4@OSL@@AEAAPEAVValue@llvm@@PEAV56@@Z __imp_?op_load@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAVType@6@PEAV56@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z __imp_?op_load_mask@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z __imp_?op_lt@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@0_N@Z __imp_?op_masked_break@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXXZ __imp_?op_masked_continue@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXXZ __imp_?op_masked_exit@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXXZ __imp_?op_masked_return@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXXZ __imp_?op_memcpy@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVValue@llvm@@0HH@Z __imp_?op_memcpy@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVValue@llvm@@H0HH@Z __imp_?op_memset@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVValue@llvm@@H0H@Z __imp_?op_memset@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVValue@llvm@@HHH@Z __imp_?op_mod@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@0@Z __imp_?op_mul@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@0@Z __imp_?op_ne@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@0_N@Z __imp_?op_neg@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z __imp_?op_not@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z __imp_?op_or@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@0@Z __imp_?op_quarter_16x@LLVM_Util@pvt@v1_14_4@OSL@@AEAA?AV?$array@PEAVValue@llvm@@$03@std@@PEAVValue@llvm@@@Z __imp_?op_return@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVValue@llvm@@@Z __imp_?op_scatter@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVValue@llvm@@PEAVType@6@00@Z __imp_?op_select@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@00@Z __imp_?op_shl@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@0@Z __imp_?op_shr@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@0@Z __imp_?op_split_16x@LLVM_Util@pvt@v1_14_4@OSL@@AEAA?AV?$array@PEAVValue@llvm@@$01@std@@PEAVValue@llvm@@@Z __imp_?op_split_8x@LLVM_Util@pvt@v1_14_4@OSL@@AEAA?AV?$array@PEAVValue@llvm@@$01@std@@PEAVValue@llvm@@@Z __imp_?op_store@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVValue@llvm@@0@Z __imp_?op_store_mask@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVValue@llvm@@0@Z __imp_?op_sub@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@0@Z __imp_?op_unmasked_store@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVValue@llvm@@0@Z __imp_?op_while@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?op_xor@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@0@Z __imp_?op_zero_if@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@0@Z __imp_?optimize_all_groups@ShadingSystem@v1_14_4@OSL@@QEAAXH_N@Z __imp_?optimize_group@ShadingSystem@v1_14_4@OSL@@QEAAXPEAVShaderGroup@23@HHPEAVShadingContext@23@_N@Z __imp_?optimize_group@ShadingSystem@v1_14_4@OSL@@QEAAXPEAVShaderGroup@23@PEAVShadingContext@23@_N@Z __imp_?osl_get_attribute@ShadingContext@v1_14_4@OSL@@QEAA_NPEAUShaderGlobals@23@PEAXHVustringhash@OpenImageIO_v3_0@@2HHUTypeDesc@6@1@Z __imp_?oslquery@ShadingSystem@v1_14_4@OSL@@QEAA?AVOSLQuery@23@AEBVShaderGroup@23@H@Z __imp_?parameters@OSLQuery@v1_14_4@OSL@@QEBAAEBV?$vector@UParameter@OSLQuery@v1_14_4@OSL@@V?$allocator@UParameter@OSLQuery@v1_14_4@OSL@@@std@@@std@@XZ __imp_?pcellnoise@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?periodic@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?perlin@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?phashnoise@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?pnoise@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?point@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?pointcloud_get@RendererServices@v1_14_4@OSL@@UEAAHPEAUShaderGlobals@23@Vustringhash@OpenImageIO_v3_0@@PEBHH1UTypeDesc@6@PEAX@Z __imp_?pointcloud_search@RendererServices@v1_14_4@OSL@@UEAAHPEAUShaderGlobals@23@Vustringhash@OpenImageIO_v3_0@@AEBV?$Vec3@M@Imath_3_1@@MH_NPEAHPEAMH@Z __imp_?pointcloud_write@RendererServices@v1_14_4@OSL@@UEAA_NPEAUShaderGlobals@23@Vustringhash@OpenImageIO_v3_0@@AEBV?$Vec3@M@Imath_3_1@@HPEBV56@PEBUTypeDesc@6@PEAPEBX@Z __imp_?popState@Accumulator@v1_14_4@OSL@@QEAAXXZ __imp_?pop_function@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXXZ __imp_?pop_function_mask@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXXZ __imp_?pop_loop@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXXZ __imp_?pop_mask@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXXZ __imp_?pop_masked_loop@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXXZ __imp_?pop_masked_return_block@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXXZ __imp_?pop_shader_instance@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXXZ __imp_?print_closure@pvt@v1_14_4@OSL@@YAXAEAV?$basic_ostream@DU?$char_traits@D@std@@@std@@PEBUClosureColor@23@PEAVShadingSystemImpl@123@_N@Z __imp_?printfmt@RendererServices@v1_14_4@OSL@@UEAAXPEAUShaderGlobals@23@Vustringhash@OpenImageIO_v3_0@@HPEBW4EncodedType@23@IPEAE@Z __imp_?process@Reader@journal@v1_14_4@OSL@@QEAAXXZ __imp_?process_entries_for_thread@Reader@journal@v1_14_4@OSL@@AEAAXH@Z __imp_?process_errors@ShadingContext@v1_14_4@OSL@@QEBAXXZ __imp_?prune_and_internalize_module@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXV?$unordered_set@PEAVFunction@llvm@@U?$hash@PEAVFunction@llvm@@@std@@U?$equal_to@PEAVFunction@llvm@@@4@V?$allocator@PEAVFunction@llvm@@@4@@std@@W4Linkage@1234@PEAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@6@@Z __imp_?psnoise@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?ptr_cast@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@AEBUTypeDesc@OpenImageIO_v3_0@@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z __imp_?ptr_cast@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@PEAVPointerType@6@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z __imp_?ptr_cast@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@PEAVType@6@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z __imp_?ptr_to_cast@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@PEAVType@6@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z __imp_?ptr_to_int64_cast@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z __imp_?ptx_compile_group@LLVM_Util@pvt@v1_14_4@OSL@@QEAA_NPEAVModule@llvm@@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAV78@@Z __imp_?pushState@Accumulator@v1_14_4@OSL@@QEAAXXZ __imp_?push_function@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVBasicBlock@llvm@@PEAV56@@Z __imp_?push_function_mask@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVValue@llvm@@@Z __imp_?push_loop@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVBasicBlock@llvm@@0@Z __imp_?push_mask@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVValue@llvm@@_N1@Z __imp_?push_masked_loop@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVValue@llvm@@0@Z __imp_?push_masked_return_block@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVBasicBlock@llvm@@@Z __imp_?push_shader_instance@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVValue@llvm@@@Z __imp_?query_closure@ShadingSystem@v1_14_4@OSL@@QEAA_NPEAPEBDPEAHPEAPEBUClosureParam@23@@Z __imp_?raytype@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?raytype_bit@ShadingSystem@v1_14_4@OSL@@QEAAHVustring@OpenImageIO_v3_0@@@Z __imp_?raytype_bit@ShadingSystemImpl@pvt@v1_14_4@OSL@@QEAAHVustring@OpenImageIO_v3_0@@@Z __imp_?rblur@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?record_error@ShadingContext@v1_14_4@OSL@@QEBAXW4ErrCode@ErrorHandler@OpenImageIO_v3_0@@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z __imp_?record_errorfmt@Writer@journal@v1_14_4@OSL@@QEAA_NHHVustringhash@OpenImageIO_v3_0@@HPEBW4EncodedType@34@IPEAE@Z __imp_?record_filefmt@Writer@journal@v1_14_4@OSL@@QEAA_NHHVustringhash@OpenImageIO_v3_0@@0HPEBW4EncodedType@34@IPEAE@Z __imp_?record_printfmt@Writer@journal@v1_14_4@OSL@@QEAA_NHHVustringhash@OpenImageIO_v3_0@@HPEBW4EncodedType@34@IPEAE@Z __imp_?record_runtime_stats@ShadingContext@v1_14_4@OSL@@QEAAXXZ __imp_?record_warningfmt@Writer@journal@v1_14_4@OSL@@QEAA_NHHHVustringhash@OpenImageIO_v3_0@@HPEBW4EncodedType@34@IPEAE@Z __imp_?register_JIT_Global@v1_14_4@OSL@@YAXPEBDPEAX@Z __imp_?register_closure@ShadingSystem@v1_14_4@OSL@@QEAAXV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@HPEBUClosureParam@23@P6AXPEAVRendererServices@23@HPEAX@Z4@Z __imp_?register_inline_function@ShadingSystem@v1_14_4@OSL@@QEAAXVustring@OpenImageIO_v3_0@@@Z __imp_?register_noinline_function@ShadingSystem@v1_14_4@OSL@@QEAAXVustring@OpenImageIO_v3_0@@@Z __imp_?release_context@ShadingSystem@v1_14_4@OSL@@QEAAXPEAVShadingContext@23@@Z __imp_?renderer@ShadingContext@v1_14_4@OSL@@QEBAPEAVRendererServices@23@XZ __imp_?renderer@ShadingSystem@v1_14_4@OSL@@QEBAPEAVRendererServices@23@XZ __imp_?report_error@Report2ErrorHandler@journal@v1_14_4@OSL@@UEAAXHHAEBV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@@Z __imp_?report_file_print@Report2ErrorHandler@journal@v1_14_4@OSL@@UEAAXHHAEBV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@0@Z __imp_?report_print@Report2ErrorHandler@journal@v1_14_4@OSL@@UEAAXHHAEBV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@@Z __imp_?report_warning@Report2ErrorHandler@journal@v1_14_4@OSL@@UEAAXHHAEBV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@@Z __imp_?requiredForPageTransition@Writer@journal@v1_14_4@OSL@@CA_KXZ __imp_?reserve_heap@ShadingContext@v1_14_4@OSL@@QEAAX_K@Z __imp_?return_block@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVBasicBlock@llvm@@XZ __imp_?rgb@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?rwidth@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?rwrap@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?sRGB@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?sblur@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?screen@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?setAov@Accumulator@v1_14_4@OSL@@QEAAXHPEAVAov@23@_N1@Z __imp_?set_colorspace@ColorSystem@pvt@v1_14_4@OSL@@QEAA_NVustringhash@OpenImageIO_v3_0@@@Z __imp_?set_insert_point@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVBasicBlock@llvm@@@Z __imp_?set_masking_required@LLVM_Util@pvt@v1_14_4@OSL@@AEAAX_N@Z __imp_?set_raytypes@ShadingSystem@v1_14_4@OSL@@QEAAXPEAVShaderGroup@23@HH@Z __imp_?set_target_isa@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXW4TargetISA@234@@Z __imp_?setup_legacy_optimization_passes@LLVM_Util@pvt@v1_14_4@OSL@@AEAAXH_N@Z __imp_?setup_new_optimization_passes@LLVM_Util@pvt@v1_14_4@OSL@@AEAAXH_N@Z __imp_?setup_optimization_passes@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXH_N@Z __imp_?shade@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?shade_image@v1_14_4@OSL@@YA_NAEAVShadingSystem@12@AEAVShaderGroup@12@PEBUShaderGlobals@12@AEAVImageBuf@OpenImageIO_v3_0@@V?$span@$$CBVustring@OpenImageIO_v3_0@@$0?0@7@W4ShadeImageLocations@12@UROI@7@Vparallel_options@7@@Z __imp_?shade_image@v1_14_4@OSL@@YA_NAEAVShadingSystem@12@AEAVShaderGroup@12@PEBUShaderGlobals@12@AEAVImageBuf@OpenImageIO_v3_0@@V?$span@$$CBVustring@OpenImageIO_v3_0@@$0?0@7@W4ShadeImageLocations@12@UROI@7@Vparopt@7@@Z __imp_?shader@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?shader_mask@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@XZ __imp_?shadername@OSLQuery@v1_14_4@OSL@@QEBA?BVustring@OpenImageIO_v3_0@@XZ __imp_?shadertype@OSLQuery@v1_14_4@OSL@@QEBA?BVustring@OpenImageIO_v3_0@@XZ __imp_?shadingsys@ShadingContext@v1_14_4@OSL@@QEBAAEAVShadingSystemImpl@pvt@23@XZ __imp_?shouldReportError@TrackRecentlyReported@journal@v1_14_4@OSL@@QEAA_NAEBV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@@Z __imp_?shouldReportWarning@TrackRecentlyReported@journal@v1_14_4@OSL@@QEAA_NAEBV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@@Z __imp_?sidedness@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?simplex@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?simplexnoise@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?smartcubic@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?snoise@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?subimage@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?subimagename@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?supports@RendererServices@v1_14_4@OSL@@UEBAHV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@@Z __imp_?supports_avx2@LLVM_Util@pvt@v1_14_4@OSL@@QEBA_NXZ __imp_?supports_avx512f@LLVM_Util@pvt@v1_14_4@OSL@@QEBA_NXZ __imp_?supports_avx@LLVM_Util@pvt@v1_14_4@OSL@@QEBA_NXZ __imp_?supports_isa@LLVM_Util@pvt@v1_14_4@OSL@@SA_NW4TargetISA@234@@Z __imp_?supports_llvm_bit_masks_natively@LLVM_Util@pvt@v1_14_4@OSL@@QEBA_NXZ __imp_?supports_masked_stores@LLVM_Util@pvt@v1_14_4@OSL@@QEBA_NXZ __imp_?swidth@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?swrap@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?symbol@ShadingContext@v1_14_4@OSL@@QEBAPEBVSymbol@pvt@23@Vustring@OpenImageIO_v3_0@@0@Z __imp_?symbol@ShadingContext@v1_14_4@OSL@@QEBAPEBVSymbol@pvt@23@Vustring@OpenImageIO_v3_0@@@Z __imp_?symbol_address@ShadingSystem@v1_14_4@OSL@@QEBAPEBXAEBVShadingContext@23@PEBVShaderSymbol@23@@Z __imp_?symbol_data@ShadingContext@v1_14_4@OSL@@QEBAPEBXAEBVSymbol@pvt@23@@Z __imp_?symbol_typedesc@ShadingSystem@v1_14_4@OSL@@QEBA?AUTypeDesc@OpenImageIO_v3_0@@PEBVShaderSymbol@23@@Z __imp_?target_isa@LLVM_Util@pvt@v1_14_4@OSL@@QEBA?AW4TargetISA@234@XZ __imp_?target_isa_name@LLVM_Util@pvt@v1_14_4@OSL@@SAPEBDW4TargetISA@234@@Z __imp_?tblur@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?test@Accumulator@v1_14_4@OSL@@QEAA_NVustring@OpenImageIO_v3_0@@0PEBV45@0@Z __imp_?test_if_mask_is_non_zero@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z __imp_?test_mask_lane@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@0@Z __imp_?test_mask_lane@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@H@Z __imp_?texture3d@RendererServices@v1_14_4@OSL@@UEAA_NVustringhash@OpenImageIO_v3_0@@PEAVTextureHandle@TextureSystem@5@PEAVPerthread@75@AEAVTextureOpt_v2@5@PEAUShaderGlobals@23@AEBV?$Vec3@M@Imath_3_1@@555HPEAM666PEAV45@@Z __imp_?texture@RendererServices@v1_14_4@OSL@@UEAA_NVustringhash@OpenImageIO_v3_0@@PEAVTextureHandle@TextureSystem@5@PEAVPerthread@75@AEAVTextureOpt_v2@5@PEAUShaderGlobals@23@MMMMMMHPEAM55PEAV45@@Z __imp_?texture_thread_info@ShadingContext@v1_14_4@OSL@@QEAAXPEAVPerthread@TextureSystem@OpenImageIO_v3_0@@@Z __imp_?texture_thread_info@ShadingContext@v1_14_4@OSL@@QEBAPEAVPerthread@TextureSystem@OpenImageIO_v3_0@@XZ __imp_?texturesys@RendererServices@v1_14_4@OSL@@UEBAPEAVTextureSystem@OpenImageIO_v3_0@@XZ __imp_?texturesys@ShadingSystem@v1_14_4@OSL@@QEBAPEAVTextureSystem@OpenImageIO_v3_0@@XZ __imp_?thread_info@ShadingContext@v1_14_4@OSL@@QEBAPEAUPerThreadInfo@23@XZ __imp_?time@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?toAlpha@AccumRule@v1_14_4@OSL@@QEBA_NXZ __imp_?to_rgb@ColorSystem@pvt@v1_14_4@OSL@@QEBA?AV?$Color3@M@Imath_3_1@@Vustringhash@OpenImageIO_v3_0@@AEBV56@PEAVShadingContext@34@PEAUShaderGlobals@34@@Z __imp_?total_jit_memory_held@LLVM_Util@pvt@v1_14_4@OSL@@SA_KXZ __imp_?trace@RendererServices@v1_14_4@OSL@@UEAA_NAEAUTraceOpt@23@PEAUShaderGlobals@23@AEBV?$Vec3@M@Imath_3_1@@22222@Z __imp_?trace@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?traceset@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?transform_points@RendererServices@v1_14_4@OSL@@UEAA_NPEAUShaderGlobals@23@Vustringhash@OpenImageIO_v3_0@@1MPEBV?$Vec3@M@Imath_3_1@@PEAV78@HW4VECSEMANTICS@TypeDesc@6@@Z __imp_?transformc@ColorSystem@pvt@v1_14_4@OSL@@QEBA?AV?$Color3@M@Imath_3_1@@Vustringhash@OpenImageIO_v3_0@@0AEBV56@PEAVShadingContext@34@PEAUShaderGlobals@34@@Z __imp_?transformc@ColorSystem@pvt@v1_14_4@OSL@@QEBA?AV?$Dual@V?$Color3@M@Imath_3_1@@$01@34@Vustringhash@OpenImageIO_v3_0@@0AEBV534@PEAVShadingContext@34@PEAUShaderGlobals@34@@Z __imp_?twidth@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?twrap@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?type_addrint@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ __imp_?type_array@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVType@llvm@@PEAV56@H@Z __imp_?type_bool@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ __imp_?type_bool_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVPointerType@llvm@@XZ __imp_?type_char@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ __imp_?type_char_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVPointerType@llvm@@XZ __imp_?type_double@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ __imp_?type_double_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVPointerType@llvm@@XZ __imp_?type_float@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ __imp_?type_float_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVPointerType@llvm@@XZ __imp_?type_function@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVFunctionType@llvm@@PEAVType@6@V?$span@QEAVType@llvm@@$0?0@OpenImageIO_v3_0@@_N@Z __imp_?type_function_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVPointerType@llvm@@PEAVType@6@V?$span@QEAVType@llvm@@$0?0@OpenImageIO_v3_0@@_N@Z __imp_?type_int16@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ __imp_?type_int64@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ __imp_?type_int64_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVPointerType@llvm@@XZ __imp_?type_int8@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ __imp_?type_int8_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVPointerType@llvm@@XZ __imp_?type_int@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ __imp_?type_int_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVPointerType@llvm@@XZ __imp_?type_longlong@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ __imp_?type_longlong_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVPointerType@llvm@@XZ __imp_?type_matrix@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ __imp_?type_matrix_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVPointerType@llvm@@XZ __imp_?type_native_mask@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ __imp_?type_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVPointerType@llvm@@PEAVType@6@@Z __imp_?type_real_ustring@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVType@llvm@@XZ __imp_?type_struct@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVType@llvm@@V?$span@QEAVType@llvm@@$0?0@OpenImageIO_v3_0@@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@_N@Z __imp_?type_struct_field_at_index@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVType@llvm@@PEAV56@H@Z __imp_?type_triple@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ __imp_?type_triple_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVPointerType@llvm@@XZ __imp_?type_typedesc@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ __imp_?type_union@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVType@llvm@@V?$span@QEAVType@llvm@@$0?0@OpenImageIO_v3_0@@@Z __imp_?type_ustring@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVType@llvm@@XZ __imp_?type_ustring_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVPointerType@llvm@@XZ __imp_?type_void@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ __imp_?type_void_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVPointerType@llvm@@XZ __imp_?type_wide@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVType@llvm@@PEAV56@@Z __imp_?type_wide_bool@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ __imp_?type_wide_bool_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVPointerType@llvm@@XZ __imp_?type_wide_char@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ __imp_?type_wide_char_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVPointerType@llvm@@XZ __imp_?type_wide_double@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ __imp_?type_wide_float@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ __imp_?type_wide_float_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVPointerType@llvm@@XZ __imp_?type_wide_int@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ __imp_?type_wide_int_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVPointerType@llvm@@XZ __imp_?type_wide_longlong@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ __imp_?type_wide_matrix@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ __imp_?type_wide_triple@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ __imp_?type_wide_ustring@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ __imp_?type_wide_ustring_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVPointerType@llvm@@XZ __imp_?type_wide_void_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ __imp_?u@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?uninitialized_string@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?unknown@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?unregister_inline_function@ShadingSystem@v1_14_4@OSL@@QEAAXVustring@OpenImageIO_v3_0@@@Z __imp_?unregister_noinline_function@ShadingSystem@v1_14_4@OSL@@QEAAXVustring@OpenImageIO_v3_0@@@Z __imp_?unull@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?uperlin@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?useparam@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?usimplex@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?usimplexnoise@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?ustring_rep@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXW4UstringRep@1234@@Z __imp_?ustring_rep@LLVM_Util@pvt@v1_14_4@OSL@@QEBA?AW4UstringRep@1234@XZ __imp_?v@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?validate_global_mappings@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXAEAV?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@@Z __imp_?validate_struct_data_layout@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVType@llvm@@AEBV?$vector@IV?$allocator@I@std@@@std@@@Z __imp_?vector@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?void_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z __imp_?void_ptr_null@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@XZ __imp_?warningfmt@RendererServices@v1_14_4@OSL@@UEAAXPEAUShaderGlobals@23@Vustringhash@OpenImageIO_v3_0@@HPEBW4EncodedType@23@IPEAE@Z __imp_?wide_constant@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@H@Z __imp_?wide_constant@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@HH@Z __imp_?wide_constant@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@HM@Z __imp_?wide_constant@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@M@Z __imp_?wide_constant@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@PEAV56@@Z __imp_?wide_constant@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@_K@Z __imp_?wide_constant@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@@Z __imp_?wide_constant@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@Vustring@OpenImageIO_v3_0@@@Z __imp_?wide_constant_bool@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@_N@Z __imp_?wide_op_alloca@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@AEBUTypeDesc@OpenImageIO_v3_0@@HAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@H@Z __imp_?wide_ptr_cast@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@AEBUTypeDesc@OpenImageIO_v3_0@@@Z __imp_?widen_value@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z __imp_?width@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?world@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?wrap@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?write_bitcode_file@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEBDPEAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z __imp_?write_entry@Writer@journal@v1_14_4@OSL@@AEAA_NHHW4Content@pvt@234@Vustringhash@OpenImageIO_v3_0@@1HPEBW4EncodedType@34@IPEAE@Z __imp_?xyY@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B __imp_?xyz@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec_NULL_THUNK_DATA 
oslexec.dll/    -1                      0       493       `
d� j^�         .debug$S        A   �               @ B.idata$2           �   �          @ 0�.idata$6           �   �           @  �    	     oslexec.dll'    �         膗Microsoft (R) LINK                                          oslexec.dll @comp.id膗��                  .idata$2@  �   h .idata$6        .idata$4@  �    h .idata$5@  �    h                      9            R   __IMPORT_DESCRIPTOR_oslexec __NULL_IMPORT_DESCRIPTOR oslexec_NULL_THUNK_DATA 
oslexec.dll/    -1                      0       250       `
d� =厐芄          .debug$S        A   d               @ B.idata$3           �               @ 0�    	     oslexec.dll'    �         膗Microsoft (R) LINK                    @comp.id膗��                     __NULL_IMPORT_DESCRIPTOR oslexec.dll/    -1                      0       286       `
d� 夺?圯          .debug$S        A   �               @ B.idata$5           �               @ @�.idata$4           �               @ @�    	     oslexec.dll'    �         膗Microsoft (R) LINK                @comp.id膗��                     oslexec_NULL_THUNK_DATA oslexec.dll/    -1                      0       78        `
  ��  d啚簏�:      ??0AccumAutomata@v1_14_4@OSL@@QEAA@AEBV012@@Z oslexec.dll oslexec.dll/    -1                      0       70        `
  ��  d�+�2     ??0AccumAutomata@v1_14_4@OSL@@QEAA@XZ oslexec.dll oslexec.dll/    -1                      0       69        `
  ��  d�1紦�1     ??0AccumRule@v1_14_4@OSL@@QEAA@H_N@Z oslexec.dll 
oslexec.dll/    -1                      0       78        `
  ��  d嗠谔�:     ??0Accumulator@v1_14_4@OSL@@QEAA@$$QEAV012@@Z oslexec.dll oslexec.dll/    -1                      0       76        `
  ��  d哘O�8     ??0Accumulator@v1_14_4@OSL@@QEAA@AEBV012@@Z oslexec.dll oslexec.dll/    -1                      0       89        `
  ��  d咥?O虴     ??0Accumulator@v1_14_4@OSL@@QEAA@PEBVAccumAutomata@12@@Z oslexec.dll 
oslexec.dll/    -1                      0       83        `
  ��  d啣9�?     ??0ClosureComponent@v1_14_4@OSL@@QEAA@$$QEAU012@@Z oslexec.dll 
oslexec.dll/    -1                      0       81        `
  ��  d唈gC�=     ??0ClosureComponent@v1_14_4@OSL@@QEAA@AEBU012@@Z oslexec.dll 
oslexec.dll/    -1                      0       73        `
  ��  d啳>灰5     ??0ClosureComponent@v1_14_4@OSL@@QEAA@XZ oslexec.dll 
oslexec.dll/    -1                      0       77        `
  ��  d�	�*�9   	  ??0ClosureMul@v1_14_4@OSL@@QEAA@$$QEAU012@@Z oslexec.dll 
oslexec.dll/    -1                      0       75        `
  ��  d哢[�7   
  ??0ClosureMul@v1_14_4@OSL@@QEAA@AEBU012@@Z oslexec.dll 
oslexec.dll/    -1                      0       67        `
  ��  d啫�/     ??0ClosureMul@v1_14_4@OSL@@QEAA@XZ oslexec.dll 
oslexec.dll/    -1                      0       83        `
  ��  d� 该?     ??0ColorSystem@pvt@v1_14_4@OSL@@QEAA@$$QEAV0123@@Z oslexec.dll 
oslexec.dll/    -1                      0       81        `
  ��  d啀颠=   
  ??0ColorSystem@pvt@v1_14_4@OSL@@QEAA@AEBV0123@@Z oslexec.dll 
oslexec.dll/    -1                      0       72        `
  ��  d嗇冭�4     ??0ColorSystem@pvt@v1_14_4@OSL@@QEAA@XZ oslexec.dll oslexec.dll/    -1                      0       86        `
  ��  d啫C=褺     ??0DfOptimizedAutomata@v1_14_4@OSL@@QEAA@$$QEAV012@@Z oslexec.dll oslexec.dll/    -1                      0       84        `
  ��  d嗆嘒錊     ??0DfOptimizedAutomata@v1_14_4@OSL@@QEAA@AEBV012@@Z oslexec.dll oslexec.dll/    -1                      0       76        `
  ��  d�3}�8     ??0DfOptimizedAutomata@v1_14_4@OSL@@QEAA@XZ oslexec.dll oslexec.dll/    -1                      0       95        `
  ��  d�*勞K     ??0LLVM_Util@pvt@v1_14_4@OSL@@QEAA@AEBUPerThreadInfo@0123@HH@Z oslexec.dll 
oslexec.dll/    -1                      0       79        `
  ��  d嗸析;     ??0LLVM_Util@pvt@v1_14_4@OSL@@QEAA@AEBV0123@@Z oslexec.dll 
oslexec.dll/    -1                      0       73        `
  ��  d�5i�5     ??0OSLQuery@v1_14_4@OSL@@QEAA@AEBV012@@Z oslexec.dll 
oslexec.dll/    -1                      0       85        `
  ��  d咲O腁     ??0OSLQuery@v1_14_4@OSL@@QEAA@PEBVShaderGroup@12@H@Z oslexec.dll 
oslexec.dll/    -1                      0       129       `
  ��  d喤抏莔     ??0OSLQuery@v1_14_4@OSL@@QEAA@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@0@Z oslexec.dll 
oslexec.dll/    -1                      0       75        `
  ��  d�錓�7     ??0Parameter@OSLQuery@v1_14_4@OSL@@QEAA@XZ oslexec.dll 
oslexec.dll/    -1                      0       84        `
  ��  d喼遪兀     ??0PerThreadInfo@LLVM_Util@pvt@v1_14_4@OSL@@QEAA@XZ oslexec.dll oslexec.dll/    -1                      0       92        `
  ��  d咵訦     ??0Reader@journal@v1_14_4@OSL@@QEAA@PEBEAEAVReporter@123@@Z oslexec.dll oslexec.dll/    -1                      0       81        `
  ��  d嗎K澸=     ??0RendererServices@v1_14_4@OSL@@QEAA@AEBV012@@Z oslexec.dll 
oslexec.dll/    -1                      0       109       `
  ��  d唋G鶼     ??0RendererServices@v1_14_4@OSL@@QEAA@PEAVTextureSystem@OpenImageIO_v3_0@@@Z oslexec.dll 
oslexec.dll/    -1                      0       95        `
  ��  d唂zK     ??0Report2ErrorHandler@journal@v1_14_4@OSL@@QEAA@$$QEAV0123@@Z oslexec.dll 
oslexec.dll/    -1                      0       93        `
  ��  d�6�馡     ??0Report2ErrorHandler@journal@v1_14_4@OSL@@QEAA@AEBV0123@@Z oslexec.dll 
oslexec.dll/    -1                      0       149       `
  ��  d啒累賮     ??0Report2ErrorHandler@journal@v1_14_4@OSL@@QEAA@PEAVErrorHandler@OpenImageIO_v3_0@@AEAVTrackRecentlyReported@123@@Z oslexec.dll 
oslexec.dll/    -1                      0       82        `
  ��  d唟顑�>     ??0Reporter@journal@v1_14_4@OSL@@QEAA@AEBV0123@@Z oslexec.dll oslexec.dll/    -1                      0       73        `
  ��  d唚��5      ??0Reporter@journal@v1_14_4@OSL@@QEAA@XZ oslexec.dll 
oslexec.dll/    -1                      0       90        `
  ��  d啒庖腇   !  ??0ScopedJitMemoryUser@LLVM_Util@pvt@v1_14_4@OSL@@QEAA@XZ oslexec.dll oslexec.dll/    -1                      0       121       `
  ��  d嗭l=鬳   "  ??0ShadingContext@v1_14_4@OSL@@QEAA@AEAVShadingSystemImpl@pvt@12@PEAUPerThreadInfo@12@@Z oslexec.dll 
oslexec.dll/    -1                      0       149       `
  ��  d哸V熓�   #  ??0ShadingSystem@v1_14_4@OSL@@QEAA@PEAVRendererServices@12@PEAVTextureSystem@OpenImageIO_v3_0@@PEAVErrorHandler@5@@Z oslexec.dll 
oslexec.dll/    -1                      0       97        `
  ��  d�<0#騇   $  ??0TrackRecentlyReported@journal@v1_14_4@OSL@@QEAA@$$QEAV0123@@Z oslexec.dll 
oslexec.dll/    -1                      0       95        `
  ��  d�QY錕   %  ??0TrackRecentlyReported@journal@v1_14_4@OSL@@QEAA@AEBV0123@@Z oslexec.dll 
oslexec.dll/    -1                      0       91        `
  ��  d�=斛G   &  ??0TrackRecentlyReported@journal@v1_14_4@OSL@@QEAA@_NH0H@Z oslexec.dll 
oslexec.dll/    -1                      0       75        `
  ��  d啟=6�7   '  ??0Writer@journal@v1_14_4@OSL@@QEAA@PEAX@Z oslexec.dll 
oslexec.dll/    -1                      0       70        `
  ��  d嗞�/�2   (  ??1AccumAutomata@v1_14_4@OSL@@QEAA@XZ oslexec.dll oslexec.dll/    -1                      0       68        `
  ��  d�-&y�0   )  ??1Accumulator@v1_14_4@OSL@@QEAA@XZ oslexec.dll oslexec.dll/    -1                      0       76        `
  ��  d�6��8   *  ??1DfOptimizedAutomata@v1_14_4@OSL@@QEAA@XZ oslexec.dll oslexec.dll/    -1                      0       70        `
  ��  d喩媫�2   +  ??1LLVM_Util@pvt@v1_14_4@OSL@@QEAA@XZ oslexec.dll oslexec.dll/    -1                      0       75        `
  ��  d唦nV�7   ,  ??1Parameter@OSLQuery@v1_14_4@OSL@@QEAA@XZ oslexec.dll 
oslexec.dll/    -1                      0       84        `
  ��  d��-鉆   -  ??1PerThreadInfo@LLVM_Util@pvt@v1_14_4@OSL@@QEAA@XZ oslexec.dll oslexec.dll/    -1                      0       73        `
  ��  d�	诉5   .  ??1RendererServices@v1_14_4@OSL@@UEAA@XZ oslexec.dll 
oslexec.dll/    -1                      0       84        `
  ��  d�贶跕   /  ??1Report2ErrorHandler@journal@v1_14_4@OSL@@UEAA@XZ oslexec.dll oslexec.dll/    -1                      0       73        `
  ��  d啝U侕5   0  ??1Reporter@journal@v1_14_4@OSL@@UEAA@XZ oslexec.dll 
oslexec.dll/    -1                      0       90        `
  ��  d哖犲鸉   1  ??1ScopedJitMemoryUser@LLVM_Util@pvt@v1_14_4@OSL@@QEAA@XZ oslexec.dll oslexec.dll/    -1                      0       71        `
  ��  d嗋J�3   2  ??1ShadingContext@v1_14_4@OSL@@QEAA@XZ oslexec.dll 
oslexec.dll/    -1                      0       70        `
  ��  d�遧�2   3  ??1ShadingSystem@v1_14_4@OSL@@QEAA@XZ oslexec.dll oslexec.dll/    -1                      0       86        `
  ��  d啿�;顱   4  ??1TrackRecentlyReported@journal@v1_14_4@OSL@@QEAA@XZ oslexec.dll oslexec.dll/    -1                      0       85        `
  ��  d唝臎鬉   5  ??4AccumAutomata@v1_14_4@OSL@@QEAAAEAV012@AEBV012@@Z oslexec.dll 
oslexec.dll/    -1                      0       83        `
  ��  d啗e_�?   6  ??4AccumRule@v1_14_4@OSL@@QEAAAEAV012@$$QEAV012@@Z oslexec.dll 
oslexec.dll/    -1                      0       81        `
  ��  d�囪�=   7  ??4AccumRule@v1_14_4@OSL@@QEAAAEAV012@AEBV012@@Z oslexec.dll 
oslexec.dll/    -1                      0       85        `
  ��  d啛�螦   8  ??4Accumulator@v1_14_4@OSL@@QEAAAEAV012@$$QEAV012@@Z oslexec.dll 
oslexec.dll/    -1                      0       83        `
  ��  d嗚|△?   9  ??4Accumulator@v1_14_4@OSL@@QEAAAEAV012@AEBV012@@Z oslexec.dll 
oslexec.dll/    -1                      0       84        `
  ��  d啞堐@   :  ??4ClosureAdd@v1_14_4@OSL@@QEAAAEAU012@$$QEAU012@@Z oslexec.dll oslexec.dll/    -1                      0       82        `
  ��  d�4T�>   ;  ??4ClosureAdd@v1_14_4@OSL@@QEAAAEAU012@AEBU012@@Z oslexec.dll oslexec.dll/    -1                      0       86        `
  ��  d哾繶諦   <  ??4ClosureColor@v1_14_4@OSL@@QEAAAEAU012@$$QEAU012@@Z oslexec.dll oslexec.dll/    -1                      0       84        `
  ��  d啔*贎   =  ??4ClosureColor@v1_14_4@OSL@@QEAAAEAU012@AEBU012@@Z oslexec.dll oslexec.dll/    -1                      0       90        `
  ��  d唫�>颋   >  ??4ClosureComponent@v1_14_4@OSL@@QEAAAEAU012@$$QEAU012@@Z oslexec.dll oslexec.dll/    -1                      0       88        `
  ��  d�攮跠   ?  ??4ClosureComponent@v1_14_4@OSL@@QEAAAEAU012@AEBU012@@Z oslexec.dll oslexec.dll/    -1                      0       84        `
  ��  d咹饩顯   @  ??4ClosureMul@v1_14_4@OSL@@QEAAAEAU012@$$QEAU012@@Z oslexec.dll oslexec.dll/    -1                      0       82        `
  ��  d唊N>   A  ??4ClosureMul@v1_14_4@OSL@@QEAAAEAU012@AEBU012@@Z oslexec.dll oslexec.dll/    -1                      0       91        `
  ��  d啌^蘂   B  ??4ColorSystem@pvt@v1_14_4@OSL@@QEAAAEAV0123@$$QEAV0123@@Z oslexec.dll 
oslexec.dll/    -1                      0       89        `
  ��  d嗠骯鬍   C  ??4ColorSystem@pvt@v1_14_4@OSL@@QEAAAEAV0123@AEBV0123@@Z oslexec.dll 
oslexec.dll/    -1                      0       93        `
  ��  d嗘-曂I   D  ??4DfOptimizedAutomata@v1_14_4@OSL@@QEAAAEAV012@$$QEAV012@@Z oslexec.dll 
oslexec.dll/    -1                      0       91        `
  ��  d�,次鬐   E  ??4DfOptimizedAutomata@v1_14_4@OSL@@QEAAAEAV012@AEBV012@@Z oslexec.dll 
oslexec.dll/    -1                      0       87        `
  ��  d�%
艭   F  ??4LLVM_Util@pvt@v1_14_4@OSL@@QEAAAEAV0123@AEBV0123@@Z oslexec.dll 
oslexec.dll/    -1                      0       80        `
  ��  d�賧�<   G  ??4Labels@v1_14_4@OSL@@QEAAAEAV012@$$QEAV012@@Z oslexec.dll oslexec.dll/    -1                      0       78        `
  ��  d嗹87�:   H  ??4Labels@v1_14_4@OSL@@QEAAAEAV012@AEBV012@@Z oslexec.dll oslexec.dll/    -1                      0       80        `
  ��  d啹�<   I  ??4OSLQuery@v1_14_4@OSL@@QEAAAEAV012@AEBV012@@Z oslexec.dll oslexec.dll/    -1                      0       103       `
  ��  d喗zㄩS   J  ??4PerThreadInfo@LLVM_Util@pvt@v1_14_4@OSL@@QEAAAEAU01234@AEBU01234@@Z oslexec.dll 
oslexec.dll/    -1                      0       88        `
  ��  d咰罝   K  ??4RendererServices@v1_14_4@OSL@@QEAAAEAV012@AEBV012@@Z oslexec.dll oslexec.dll/    -1                      0       90        `
  ��  d喆�馞   L  ??4Reporter@journal@v1_14_4@OSL@@QEAAAEAV0123@AEBV0123@@Z oslexec.dll oslexec.dll/    -1                      0       109       `
  ��  d�%杁鎅   M  ??4ScopedJitMemoryUser@LLVM_Util@pvt@v1_14_4@OSL@@QEAAAEAU01234@AEBU01234@@Z oslexec.dll 
oslexec.dll/    -1                      0       85        `
  ��  d嘄�鬉   N  ??4ShadingSystem@v1_14_4@OSL@@QEAAAEAV012@AEBV012@@Z oslexec.dll 
oslexec.dll/    -1                      0       70        `
  ��  d哘`O�2   O  ??_7RendererServices@v1_14_4@OSL@@6B@ oslexec.dll oslexec.dll/    -1                      0       81        `
  ��  d嗂bQ�=   P  ??_7Report2ErrorHandler@journal@v1_14_4@OSL@@6B@ oslexec.dll 
oslexec.dll/    -1                      0       70        `
  ��  d啍xd�2   Q  ??_7Reporter@journal@v1_14_4@OSL@@6B@ oslexec.dll oslexec.dll/    -1                      0       74        `
  ��  d嗁<3�6   R  ??_FRendererServices@v1_14_4@OSL@@QEAAXXZ oslexec.dll oslexec.dll/    -1                      0       71        `
  ��  d唲3.�3   S  ??_FShadingSystem@v1_14_4@OSL@@QEAAXXZ oslexec.dll 
oslexec.dll/    -1                      0       87        `
  ��  d�(C   T  ??_FTrackRecentlyReported@journal@v1_14_4@OSL@@QEAAXXZ oslexec.dll 
oslexec.dll/    -1                      0       95        `
  ��  d喚
搀K   U  ?ACES2065_1@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll 
oslexec.dll/    -1                      0       91        `
  ��  d�	慮蹽   V  ?ACEScg@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll 
oslexec.dll/    -1                      0       93        `
  ��  d�{婃I   W  ?AdobeRGB@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll 
oslexec.dll/    -1                      0       94        `
  ��  d喠sr镴   X  ?BACKGROUND@Labels@v1_14_4@OSL@@2Vustring@OpenImageIO_v3_0@@B oslexec.dll oslexec.dll/    -1                      0       90        `
  ��  d喐>鼺   Y  ?CAMERA@Labels@v1_14_4@OSL@@2Vustring@OpenImageIO_v3_0@@B oslexec.dll oslexec.dll/    -1                      0       88        `
  ��  d哰�%釪   Z  ?CIE@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll oslexec.dll/    -1                      0       87        `
  ��  d�:cO贑   [  ?Ci@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll 
oslexec.dll/    -1                      0       169       `
  ��  d嗙ら��   \  ?ConnectShaders@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@111@Z oslexec.dll 
oslexec.dll/    -1                      0       150       `
  ��  d嗺g掏�   ]  ?ConnectShaders@ShadingSystem@v1_14_4@OSL@@QEAA_NV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@000@Z oslexec.dll oslexec.dll/    -1                      0       91        `
  ��  d唘�"驡   ^  ?DIFFUSE@Labels@v1_14_4@OSL@@2Vustring@OpenImageIO_v3_0@@B oslexec.dll 
oslexec.dll/    -1                      0       88        `
  ��  d啝u活D   _  ?EBU@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll oslexec.dll/    -1                      0       104       `
  ��  d嗗犤餞   `  ?ErrorColorTransform@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll oslexec.dll/    -1                      0       171       `
  ��  d�&�   a  ?GEP@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAVType@6@PEAV56@1AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z oslexec.dll 
oslexec.dll/    -1                      0       171       `
  ��  d哰_陾   b  ?GEP@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAVType@6@PEAV56@HAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z oslexec.dll 
oslexec.dll/    -1                      0       172       `
  ��  d唖 
顦   c  ?GEP@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAVType@6@PEAV56@HHAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z oslexec.dll oslexec.dll/    -1                      0       173       `
  ��  d��顧   d  ?GEP@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAVType@6@PEAV56@HHHAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z oslexec.dll 
oslexec.dll/    -1                      0       90        `
  ��  d唫操F   e  ?GLOSSY@Labels@v1_14_4@OSL@@2Vustring@OpenImageIO_v3_0@@B oslexec.dll oslexec.dll/    -1                      0       89        `
  ��  d喞E   f  ?HDTV@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll 
oslexec.dll/    -1                      0       86        `
  ��  d嗴`ｕB   g  ?I@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll oslexec.dll/    -1                      0       169       `
  ��  d嗁�葧   h  ?InstallLazyFunctionCreator@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXP6APEAXAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z@Z oslexec.dll 
oslexec.dll/    -1                      0       89        `
  ��  d唲墳袳   i  ?LIGHT@Labels@v1_14_4@OSL@@2Vustring@OpenImageIO_v3_0@@B oslexec.dll 
oslexec.dll/    -1                      0       158       `
  ��  d啞H翃   j  ?LoadMemoryCompiledShader@ShadingSystem@v1_14_4@OSL@@QEAA_NV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@0@Z oslexec.dll oslexec.dll/    -1                      0       86        `
  ��  d�	1撁B   k  ?N@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll oslexec.dll/    -1                      0       88        `
  ��  d�囼錎   l  ?NDC@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll oslexec.dll/    -1                      0       88        `
  ��  d�煻霥   m  ?NONE@Labels@v1_14_4@OSL@@2Vustring@OpenImageIO_v3_0@@B oslexec.dll oslexec.dll/    -1                      0       89        `
  ��  d唟咵逧   n  ?NTSC@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll 
oslexec.dll/    -1                      0       87        `
  ��  d單�證   o  ?Ng@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll 
oslexec.dll/    -1                      0       90        `
  ��  d�扗蔉   p  ?OBJECT@Labels@v1_14_4@OSL@@2Vustring@OpenImageIO_v3_0@@B oslexec.dll oslexec.dll/    -1                      0       86        `
  ��  d哱RH贐   q  ?P@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll oslexec.dll/    -1                      0       88        `
  ��  d�2}D   r  ?PAL@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll oslexec.dll/    -1                      0       242       `
  ��  d咵d=   s  ?Parameter@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@W4ParamHints@23@@Z oslexec.dll oslexec.dll/    -1                      0       228       `
  ��  d哤猖�   t  ?Parameter@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@_N@Z oslexec.dll oslexec.dll/    -1                      0       178       `
  ��  d唲8i詾   u  ?Parameter@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@HW4ParamHints@23@@Z oslexec.dll oslexec.dll/    -1                      0       164       `
  ��  d嘄9陪�   v  ?Parameter@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@H_N@Z oslexec.dll oslexec.dll/    -1                      0       178       `
  ��  d喕 梆�   w  ?Parameter@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@MW4ParamHints@23@@Z oslexec.dll oslexec.dll/    -1                      0       164       `
  ��  d�o蝥�   x  ?Parameter@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@M_N@Z oslexec.dll oslexec.dll/    -1                      0       193       `
  ��  d喿�鳝   y  ?Parameter@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@UTypeDesc@6@PEBXW4ParamHints@23@@Z oslexec.dll 
oslexec.dll/    -1                      0       179       `
  ��  d啓

釤   z  ?Parameter@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@UTypeDesc@6@PEBX_N@Z oslexec.dll 
oslexec.dll/    -1                      0       188       `
  ��  d唚�唯   {  ?Parameter@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@Vustring@6@W4ParamHints@23@@Z oslexec.dll oslexec.dll/    -1                      0       174       `
  ��  d喕@t鞖   |  ?Parameter@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@Vustring@6@_N@Z oslexec.dll oslexec.dll/    -1                      0       174       `
  ��  d唄L=袣   }  ?Parameter@ShadingSystem@v1_14_4@OSL@@QEAA_NV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@UTypeDesc@5@PEBXW4ParamHints@23@@Z oslexec.dll oslexec.dll/    -1                      0       160       `
  ��  d�Z,蹖   ~  ?Parameter@ShadingSystem@v1_14_4@OSL@@QEAA_NV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@UTypeDesc@5@PEBX_N@Z oslexec.dll oslexec.dll/    -1                      0       87        `
  ��  d喤
麾C     ?Ps@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll 
oslexec.dll/    -1                      0       91        `
  ��  d嗴bw驡   �  ?REFLECT@Labels@v1_14_4@OSL@@2Vustring@OpenImageIO_v3_0@@B oslexec.dll 
oslexec.dll/    -1                      0       88        `
  ��  d�,G-蒁   �  ?RGB@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll oslexec.dll/    -1                      0       229       `
  ��  d喐庹恃   �  ?ReParameter@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@1AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z oslexec.dll 
oslexec.dll/    -1                      0       165       `
  ��  d嗧�饝   �  ?ReParameter@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@1H@Z oslexec.dll 
oslexec.dll/    -1                      0       165       `
  ��  d�$�5貞   �  ?ReParameter@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@1M@Z oslexec.dll 
oslexec.dll/    -1                      0       180       `
  ��  d嗮6�   �  ?ReParameter@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@1UTypeDesc@6@PEBX@Z oslexec.dll oslexec.dll/    -1                      0       175       `
  ��  d喎鶷頉   �  ?ReParameter@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@1Vustring@6@@Z oslexec.dll 
oslexec.dll/    -1                      0       91        `
  ��  d唙H勉G   �  ?Rec709@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll 
oslexec.dll/    -1                      0       90        `
  ��  d嗕PM騀   �  ?SECAM@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll oslexec.dll/    -1                      0       92        `
  ��  d哊茄H   �  ?SINGULAR@Labels@v1_14_4@OSL@@2Vustring@OpenImageIO_v3_0@@B oslexec.dll oslexec.dll/    -1                      0       90        `
  ��  d喪虵   �  ?SMPTE@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll oslexec.dll/    -1                      0       88        `
  ��  d喍渣鵇   �  ?STOP@Labels@v1_14_4@OSL@@2Vustring@OpenImageIO_v3_0@@B oslexec.dll oslexec.dll/    -1                      0       92        `
  ��  d單椳H   �  ?STRAIGHT@Labels@v1_14_4@OSL@@2Vustring@OpenImageIO_v3_0@@B oslexec.dll oslexec.dll/    -1                      0       78        `
  ��  d啇旽�:   �  ?SetupLLVM@LLVM_Util@pvt@v1_14_4@OSL@@AEAAXXZ oslexec.dll oslexec.dll/    -1                      0       160       `
  ��  d啑�9麑   �  ?Shader@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@11@Z oslexec.dll oslexec.dll/    -1                      0       141       `
  ��  d�泩鰕   �  ?Shader@ShadingSystem@v1_14_4@OSL@@QEAA_NV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@00@Z oslexec.dll 
oslexec.dll/    -1                      0       197       `
  ��  d哃fL弑   �  ?ShaderGroupBegin@ShadingSystem@v1_14_4@OSL@@QEAA?AV?$shared_ptr@VShaderGroup@v1_14_4@OSL@@@std@@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@00@Z oslexec.dll 
oslexec.dll/    -1                      0       195       `
  ��  d�+V>忒   �  ?ShaderGroupBegin@ShadingSystem@v1_14_4@OSL@@QEAA?AV?$shared_ptr@VShaderGroup@v1_14_4@OSL@@@std@@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@@Z oslexec.dll 
oslexec.dll/    -1                      0       103       `
  ��  d嗮閌馭   �  ?ShaderGroupEnd@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShaderGroup@23@@Z oslexec.dll 
oslexec.dll/    -1                      0       84        `
  ��  d咷�臔   �  ?ShaderGroupEnd@ShadingSystem@v1_14_4@OSL@@QEAA_NXZ oslexec.dll oslexec.dll/    -1                      0       92        `
  ��  d哶鮀罤   �  ?TRANSMIT@Labels@v1_14_4@OSL@@2Vustring@OpenImageIO_v3_0@@B oslexec.dll oslexec.dll/    -1                      0       90        `
  ��  d�>z烄F   �  ?VOLUME@Labels@v1_14_4@OSL@@2Vustring@OpenImageIO_v3_0@@B oslexec.dll oslexec.dll/    -1                      0       88        `
  ��  d�(5鍰   �  ?XYZ@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll oslexec.dll/    -1                      0       88        `
  ��  d嗰鵇   �  ?YIQ@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll oslexec.dll/    -1                      0       98        `
  ��  d喚醻蘊   �  ?_emptystring_@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll oslexec.dll/    -1                      0       155       `
  ��  d咶�泅�   �  ?absorb_module@LLVM_Util@pvt@v1_14_4@OSL@@QEAA_NV?$unique_ptr@VModule@llvm@@U?$default_delete@VModule@llvm@@@std@@@std@@@Z oslexec.dll 
oslexec.dll/    -1                      0       187       `
  ��  d嗙D衽�   �  ?accum@AccumAutomata@v1_14_4@OSL@@QEBAXHAEBV?$Color3@M@Imath_3_1@@AEAV?$vector@UAovOutput@v1_14_4@OSL@@V?$allocator@UAovOutput@v1_14_4@OSL@@@std@@@std@@@Z oslexec.dll 
oslexec.dll/    -1                      0       182       `
  ��  d哋蝀酡   �  ?accum@AccumRule@v1_14_4@OSL@@QEBAXAEBV?$Color3@M@Imath_3_1@@AEAV?$vector@UAovOutput@v1_14_4@OSL@@V?$allocator@UAovOutput@v1_14_4@OSL@@@std@@@std@@@Z oslexec.dll oslexec.dll/    -1                      0       98        `
  ��  d喌l^齆   �  ?accum@Accumulator@v1_14_4@OSL@@QEAAXAEBV?$Color3@M@Imath_3_1@@@Z oslexec.dll oslexec.dll/    -1                      0       108       `
  ��  d咰GつX   �  ?addEventType@AccumAutomata@v1_14_4@OSL@@QEAAXVustring@OpenImageIO_v3_0@@@Z oslexec.dll oslexec.dll/    -1                      0       99        `
  ��  d�贽闛   �  ?addRule@AccumAutomata@v1_14_4@OSL@@QEAAPEAVAccumRule@23@PEBDH_N@Z oslexec.dll 
oslexec.dll/    -1                      0       113       `
  ��  d哯.!齗   �  ?addScatteringType@AccumAutomata@v1_14_4@OSL@@QEAAXVustring@OpenImageIO_v3_0@@@Z oslexec.dll 
oslexec.dll/    -1                      0       112       `
  ��  d啲е蔦   �  ?add_function_mapping@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVFunction@llvm@@PEAX@Z oslexec.dll oslexec.dll/    -1                      0       93        `
  ��  d嘂洦隝   �  ?add_global_mapping@LLVM_Util@pvt@v1_14_4@OSL@@SAXPEBDPEAX@Z oslexec.dll 
oslexec.dll/    -1                      0       164       `
  ��  d�2�惺�   �  ?add_symlocs@ShadingSystem@v1_14_4@OSL@@QEAAXPEAVShaderGroup@23@V?$span@$$CBUSymLocationDesc@v1_14_4@OSL@@$0?0@OpenImageIO_v3_0@@@Z oslexec.dll oslexec.dll/    -1                      0       145       `
  ��  d嗕_榔}   �  ?add_symlocs@ShadingSystem@v1_14_4@OSL@@QEAAXV?$span@$$CBUSymLocationDesc@v1_14_4@OSL@@$0?0@OpenImageIO_v3_0@@@Z oslexec.dll 
oslexec.dll/    -1                      0       89        `
  ��  d�cs驟   �  ?alloc_scratch@ShadingContext@v1_14_4@OSL@@QEAAPEAX_K0@Z oslexec.dll 
oslexec.dll/    -1                      0       84        `
  ��  d唈軙鳣   �  ?allocatePage@Writer@journal@v1_14_4@OSL@@AEAA_NH@Z oslexec.dll oslexec.dll/    -1                      0       85        `
  ��  d啑7|譇   �  ?allow_warnings@ShadingContext@v1_14_4@OSL@@QEAA_NXZ oslexec.dll 
oslexec.dll/    -1                      0       90        `
  ��  d哠谾譌   �  ?alpha@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll oslexec.dll/    -1                      0       96        `
  ��  d哅糆鵏   �  ?anisotropic@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll oslexec.dll/    -1                      0       141       `
  ��  d啎I苋y   �  ?append_error@OSLQuery@v1_14_4@OSL@@AEBAXAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z oslexec.dll 
oslexec.dll/    -1                      0       94        `
  ��  d喲e櫵J   �  ?apply_break_to_mask_stack@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXXZ oslexec.dll oslexec.dll/    -1                      0       97        `
  ��  d�皜鞰   �  ?apply_continue_to_mask_stack@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXXZ oslexec.dll 
oslexec.dll/    -1                      0       93        `
  ��  d嗢蔙罥   �  ?apply_exit_to_mask_stack@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXXZ oslexec.dll 
oslexec.dll/    -1                      0       106       `
  ��  d喼"}馰   �  ?apply_return_to@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z oslexec.dll oslexec.dll/    -1                      0       95        `
  ��  d�－餕   �  ?apply_return_to_mask_stack@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXXZ oslexec.dll 
oslexec.dll/    -1                      0       171       `
  ��  d唅_&詶   �  ?archive_shadergroup@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@@Z oslexec.dll 
oslexec.dll/    -1                      0       171       `
  ��  d嗎�   �  ?archive_shadergroup@ShadingSystem@v1_14_4@OSL@@QEAA_NPEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@@Z oslexec.dll 
oslexec.dll/    -1                      0       96        `
  ��  d唴r4豅   �  ?arraylength@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll oslexec.dll/    -1                      0       91        `
  ��  d唞2鏕   �  ?as_add@ClosureColor@v1_14_4@OSL@@QEBAPEBUClosureAdd@23@XZ oslexec.dll 
oslexec.dll/    -1                      0       98        `
  ��  d�!U剪N   �  ?as_comp@ClosureColor@v1_14_4@OSL@@QEBAPEBUClosureComponent@23@XZ oslexec.dll oslexec.dll/    -1                      0       91        `
  ��  d�0靮誈   �  ?as_mul@ClosureColor@v1_14_4@OSL@@QEBAPEBUClosureMul@23@XZ oslexec.dll 
oslexec.dll/    -1                      0       107       `
  ��  d�偵W   �  ?assume_ptr_is_aligned@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVValue@llvm@@I@Z oslexec.dll 
oslexec.dll/    -1                      0       162       `
  ��  d�
q埬�   �  ?attribute@ShadingSystem@v1_14_4@OSL@@QEAA_NPEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@1@Z oslexec.dll oslexec.dll/    -1                      0       162       `
  ��  d哠
X鷰   �  ?attribute@ShadingSystem@v1_14_4@OSL@@QEAA_NPEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@H@Z oslexec.dll oslexec.dll/    -1                      0       162       `
  ��  d喼Q盅�   �  ?attribute@ShadingSystem@v1_14_4@OSL@@QEAA_NPEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@M@Z oslexec.dll oslexec.dll/    -1                      0       162       `
  ��  d�萭褞   �  ?attribute@ShadingSystem@v1_14_4@OSL@@QEAA_NPEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@N@Z oslexec.dll oslexec.dll/    -1                      0       177       `
  ��  d喣%︾�   �  ?attribute@ShadingSystem@v1_14_4@OSL@@QEAA_NPEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@UTypeDesc@6@PEBX@Z oslexec.dll 
oslexec.dll/    -1                      0       143       `
  ��  d哖�鑬   �  ?attribute@ShadingSystem@v1_14_4@OSL@@QEAA_NV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@0@Z oslexec.dll 
oslexec.dll/    -1                      0       143       `
  ��  d喫C巫{   �  ?attribute@ShadingSystem@v1_14_4@OSL@@QEAA_NV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@H@Z oslexec.dll 
oslexec.dll/    -1                      0       143       `
  ��  d�46碹{   �  ?attribute@ShadingSystem@v1_14_4@OSL@@QEAA_NV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@M@Z oslexec.dll 
oslexec.dll/    -1                      0       143       `
  ��  d��(羬   �  ?attribute@ShadingSystem@v1_14_4@OSL@@QEAA_NV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@N@Z oslexec.dll 
oslexec.dll/    -1                      0       158       `
  ��  d�惋鶌   �  ?attribute@ShadingSystem@v1_14_4@OSL@@QEAA_NV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@UTypeDesc@5@PEBX@Z oslexec.dll oslexec.dll/    -1                      0       89        `
  ��  d嘂�鞥   �  ?back@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll 
oslexec.dll/    -1                      0       94        `
  ��  d�,鐹   �  ?bandwidth@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll oslexec.dll/    -1                      0       133       `
  ��  d唭囧齫   �  ?batched@RendererServices@v1_14_4@OSL@@UEAAPEAV?$BatchedRendererServices@$03@23@V?$WidthOf@$03@23@@Z oslexec.dll 
oslexec.dll/    -1                      0       133       `
  ��  d�~豵   �  ?batched@RendererServices@v1_14_4@OSL@@UEAAPEAV?$BatchedRendererServices@$07@23@V?$WidthOf@$07@23@@Z oslexec.dll 
oslexec.dll/    -1                      0       137       `
  ��  d唅爠踰   �  ?batched@RendererServices@v1_14_4@OSL@@UEAAPEAV?$BatchedRendererServices@$0BA@@23@V?$WidthOf@$0BA@@23@@Z oslexec.dll 
oslexec.dll/    -1                      0       72        `
  ��  d嗏 d�4   �  ?begin@Accumulator@v1_14_4@OSL@@QEAAXXZ oslexec.dll oslexec.dll/    -1                      0       173       `
  ��  d哱滷茩   �  ?begin@OSLQuery@v1_14_4@OSL@@QEAA?AV?$_Vector_iterator@V?$_Vector_val@U?$_Simple_types@UParameter@OSLQuery@v1_14_4@OSL@@@std@@@std@@@std@@XZ oslexec.dll 
oslexec.dll/    -1                      0       91        `
  ��  d�*釭   �  ?bezier@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll 
oslexec.dll/    -1                      0       165       `
  ��  d�(�8迲   �  ?bitcode_string@LLVM_Util@pvt@v1_14_4@OSL@@QEAA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@PEAVFunction@llvm@@@Z oslexec.dll 
oslexec.dll/    -1                      0       163       `
  ��  d唎視訌   �  ?bitcode_string@LLVM_Util@pvt@v1_14_4@OSL@@QEAA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@PEAVModule@llvm@@@Z oslexec.dll 
oslexec.dll/    -1                      0       90        `
  ��  d哛+螰   �  ?black@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll oslexec.dll/    -1                      0       109       `
  ��  d喕.手Y   �  ?blackbody_rgb@ColorSystem@pvt@v1_14_4@OSL@@QEBA?AV?$Color3@M@Imath_3_1@@M@Z oslexec.dll 
oslexec.dll/    -1                      0       89        `
  ��  d哬7a贓   �  ?blur@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll 
oslexec.dll/    -1                      0       89        `
  ��  d嗼(壭E   �  ?both@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll 
oslexec.dll/    -1                      0       74        `
  ��  d唎訑�6   �  ?broken@Accumulator@v1_14_4@OSL@@QEBA_NXZ oslexec.dll oslexec.dll/    -1                      0       92        `
  ��  d�?U喯H   �  ?bspline@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll oslexec.dll/    -1                      0       254       `
  ��  d�&3Q殛   �  ?build_attribute_getter@RendererServices@v1_14_4@OSL@@UEAAXAEBVShaderGroup@23@_NPEBVustring@OpenImageIO_v3_0@@21PEBHUTypeDesc@6@1AEAV?$FunctionSpec@V?$ArgVariant@W4AttributeSpecBuiltinArg@v1_14_4@OSL@@@v1_14_4@OSL@@@23@@Z oslexec.dll oslexec.dll/    -1                      0       253       `
  ��  d�	R衫�   �  ?build_interpolated_getter@RendererServices@v1_14_4@OSL@@UEAAXAEBVShaderGroup@23@AEBVustring@OpenImageIO_v3_0@@UTypeDesc@6@_NAEAV?$FunctionSpec@V?$ArgVariant@W4InterpolatedSpecBuiltinArg@v1_14_4@OSL@@@v1_14_4@OSL@@@23@@Z oslexec.dll 
oslexec.dll/    -1                      0       94        `
  ��  d喗�埳J   �  ?builder@LLVM_Util@pvt@v1_14_4@OSL@@AEAAAEAVIRBuilder@1234@XZ oslexec.dll oslexec.dll/    -1                      0       151       `
  ��  d啺烫鶅   �  ?call_function@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@V?$span@QEAVValue@llvm@@$0?0@OpenImageIO_v3_0@@@Z oslexec.dll 
oslexec.dll/    -1                      0       111       `
  ��  d�R錥   �  ?call_function@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEBDPEAV56@111@Z oslexec.dll 
oslexec.dll/    -1                      0       110       `
  ��  d�&骦   �  ?call_function@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEBDPEAV56@11@Z oslexec.dll oslexec.dll/    -1                      0       109       `
  ��  d咷�?鮕   �  ?call_function@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEBDPEAV56@1@Z oslexec.dll 
oslexec.dll/    -1                      0       108       `
  ��  d唞wd荴   �  ?call_function@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEBDPEAV56@@Z oslexec.dll oslexec.dll/    -1                      0       148       `
  ��  d�鍊   �  ?call_function@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEBDV?$span@QEAVValue@llvm@@$0?0@OpenImageIO_v3_0@@@Z oslexec.dll oslexec.dll/    -1                      0       91        `
  ��  d嗂嫯覩   �  ?camera@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll 
oslexec.dll/    -1                      0       93        `
  ��  d�	R蘒   �  ?can_lookup_blackbody@ColorSystem@pvt@v1_14_4@OSL@@QEBA_NM@Z oslexec.dll 
oslexec.dll/    -1                      0       95        `
  ��  d嗱$r跭   �  ?catmullrom@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll 
oslexec.dll/    -1                      0       180       `
  ��  d喥:凕�   �  ?cbegin@OSLQuery@v1_14_4@OSL@@QEBA?AV?$_Vector_const_iterator@V?$_Vector_val@U?$_Simple_types@UParameter@OSLQuery@v1_14_4@OSL@@@std@@@std@@@std@@XZ oslexec.dll oslexec.dll/    -1                      0       89        `
  ��  d唝t駿   �  ?cell@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll 
oslexec.dll/    -1                      0       94        `
  ��  d咲c}訨   �  ?cellnoise@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll oslexec.dll/    -1                      0       178       `
  ��  d唟減鐬   �  ?cend@OSLQuery@v1_14_4@OSL@@QEBA?AV?$_Vector_const_iterator@V?$_Vector_val@U?$_Simple_types@UParameter@OSLQuery@v1_14_4@OSL@@@std@@@std@@@std@@XZ oslexec.dll oslexec.dll/    -1                      0       90        `
  ��  d嗁宏虵   �  ?clamp@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll oslexec.dll/    -1                      0       89        `
  ��  d哤E   �  ?clear_runtime_stats@ShadingContext@v1_14_4@OSL@@QEAAXXZ oslexec.dll 
oslexec.dll/    -1                      0       101       `
  ��  d啑眿餛   �  ?clear_symlocs@ShadingSystem@v1_14_4@OSL@@QEAAXPEAVShaderGroup@23@@Z oslexec.dll 
oslexec.dll/    -1                      0       82        `
  ��  d唶礁�>   �  ?clear_symlocs@ShadingSystem@v1_14_4@OSL@@QEAAXXZ oslexec.dll oslexec.dll/    -1                      0       92        `
  ��  d�)�!轍   �  ?closest@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll oslexec.dll/    -1                      0       125       `
  ��  d唨
Ⅲi   �  ?closure_add_allot@ShadingContext@v1_14_4@OSL@@QEAAPEAUClosureAdd@23@PEBUClosureColor@23@0@Z oslexec.dll 
oslexec.dll/    -1                      0       145       `
  ��  d唚Z}   �  ?closure_component_allot@ShadingContext@v1_14_4@OSL@@QEAAPEAUClosureComponent@23@H_KAEBV?$Color3@M@Imath_3_1@@@Z oslexec.dll 
oslexec.dll/    -1                      0       150       `
  ��  d�7�飩   �  ?closure_mul_allot@ShadingContext@v1_14_4@OSL@@QEAAPEAUClosureMul@23@AEBV?$Color3@M@Imath_3_1@@PEBUClosureColor@23@@Z oslexec.dll oslexec.dll/    -1                      0       125       `
  ��  d啲PQ黫   �  ?closure_mul_allot@ShadingContext@v1_14_4@OSL@@QEAAPEAUClosureMul@23@MPEBUClosureColor@23@@Z oslexec.dll 
oslexec.dll/    -1                      0       90        `
  ��  d啀0W薋   �  ?color@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll oslexec.dll/    -1                      0       114       `
  ��  d唂]揭^   �  ?colorspace@ColorSystem@pvt@v1_14_4@OSL@@QEBAAEBVustringhash@OpenImageIO_v3_0@@XZ oslexec.dll oslexec.dll/    -1                      0       96        `
  ��  d�/奴芁   �  ?colorsystem@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll oslexec.dll/    -1                      0       91        `
  ��  d喬e惲G   �  ?common@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll 
oslexec.dll/    -1                      0       76        `
  ��  d嗗j酪8   �  ?compile@AccumAutomata@v1_14_4@OSL@@QEAAXXZ oslexec.dll oslexec.dll/    -1                      0       104       `
  ��  d�+vd罷   �  ?compileFrom@DfOptimizedAutomata@v1_14_4@OSL@@QEAAXAEBVDfAutomata@23@@Z oslexec.dll oslexec.dll/    -1                      0       117       `
  ��  d�x嘏a   �  ?compute_blackbody_rgb@ColorSystem@pvt@v1_14_4@OSL@@QEBA?AV?$Color3@M@Imath_3_1@@M@Z oslexec.dll 
oslexec.dll/    -1                      0       101       `
  ��  d嗈G钯Q   �  ?constant128@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@_K0@Z oslexec.dll 
oslexec.dll/    -1                      0       100       `
  ��  d唝湨貾   �  ?constant128@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@_K@Z oslexec.dll oslexec.dll/    -1                      0       98        `
  ��  d唃啰齆   �  ?constant16@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@F@Z oslexec.dll oslexec.dll/    -1                      0       98        `
  ��  d唨嵉逳   �  ?constant16@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@G@Z oslexec.dll oslexec.dll/    -1                      0       97        `
  ��  d�,翧鶰   �  ?constant4@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@C@Z oslexec.dll 
oslexec.dll/    -1                      0       97        `
  ��  d啯誛蠱   �  ?constant4@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@E@Z oslexec.dll 
oslexec.dll/    -1                      0       98        `
  ��  d嗻ZE餘   �  ?constant64@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@N@Z oslexec.dll oslexec.dll/    -1                      0       99        `
  ��  d�桹臤   �  ?constant64@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@_K@Z oslexec.dll 
oslexec.dll/    -1                      0       97        `
  ��  d嗞%蜒M   �  ?constant8@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@C@Z oslexec.dll 
oslexec.dll/    -1                      0       97        `
  ��  d嗿:鶰   �  ?constant8@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@E@Z oslexec.dll 
oslexec.dll/    -1                      0       126       `
  ��  d唨咸胘   �  ?constant@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@AEBUTypeDesc@OpenImageIO_v3_0@@@Z oslexec.dll oslexec.dll/    -1                      0       96        `
  ��  d喴r惹L   �  ?constant@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@H@Z oslexec.dll oslexec.dll/    -1                      0       96        `
  ��  d嗶鰋螸   �  ?constant@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@I@Z oslexec.dll oslexec.dll/    -1                      0       96        `
  ��  d嗈犹L   �  ?constant@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@M@Z oslexec.dll oslexec.dll/    -1                      0       97        `
  ��  d咰{濟M   �  ?constant@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@_K@Z oslexec.dll 
oslexec.dll/    -1                      0       155       `
  ��  d喺辝饑   �  ?constant@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@@Z oslexec.dll 
oslexec.dll/    -1                      0       119       `
  ��  d�,�9衏     ?constant@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@Vustring@OpenImageIO_v3_0@@@Z oslexec.dll 
oslexec.dll/    -1                      0       93        `
  ��  d咥宣鸌    ?constant@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll 
oslexec.dll/    -1                      0       151       `
  ��  d�@軆    ?constant_array@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@V?$span@QEAVConstant@llvm@@$0?0@OpenImageIO_v3_0@@@Z oslexec.dll 
oslexec.dll/    -1                      0       102       `
  ��  d咹�%蟁    ?constant_bool@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@_N@Z oslexec.dll oslexec.dll/    -1                      0       118       `
  ��  d�2伾詁    ?constant_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAXPEAVPointerType@6@@Z oslexec.dll oslexec.dll/    -1                      0       100       `
  ��  d唀�跴    ?constanti64@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@_J@Z oslexec.dll oslexec.dll/    -1                      0       97        `
  ��  d�
N巫M    ?context@LLVM_Util@pvt@v1_14_4@OSL@@QEBAAEAVLLVMContext@llvm@@XZ oslexec.dll 
oslexec.dll/    -1                      0       118       `
  ��  d啝蛘鎎    ?convert_value@ShadingSystem@v1_14_4@OSL@@SA_NPEAXUTypeDesc@OpenImageIO_v3_0@@PEBX1@Z oslexec.dll oslexec.dll/    -1                      0       99        `
  ��  d咮Y軴    ?copy_to_device@RendererServices@v1_14_4@OSL@@UEAAPEAXPEAXPEBX_K@Z oslexec.dll 
oslexec.dll/    -1                      0       195       `
  ��  d唨号莎   	 ?create_global_constant@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVGlobalVariable@llvm@@PEAVConstant@6@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z oslexec.dll 
oslexec.dll/    -1                      0       112       `
  ��  d唍(毨\   
 ?create_masking_scope@LLVM_Util@pvt@v1_14_4@OSL@@QEAA?AUScopedMasking@1234@_N@Z oslexec.dll oslexec.dll/    -1                      0       107       `
  ��  d啓BE鮓    ?create_thread_info@ShadingSystem@v1_14_4@OSL@@QEAAPEAUPerThreadInfo@23@XZ oslexec.dll 
oslexec.dll/    -1                      0       90        `
  ��  d唲,捓F    ?cubic@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll oslexec.dll/    -1                      0       104       `
  ��  d喣憘襎   
 ?current_function@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVFunction@llvm@@@Z oslexec.dll oslexec.dll/    -1                      0       103       `
  ��  d�?o楒S    ?current_function@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVFunction@llvm@@XZ oslexec.dll 
oslexec.dll/    -1                      0       105       `
  ��  d哅0f襏    ?current_function_arg@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@H@Z oslexec.dll 
oslexec.dll/    -1                      0       96        `
  ��  d哊:貺    ?current_mask@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@XZ oslexec.dll oslexec.dll/    -1                      0       92        `
  ��  d哃樣轍    ?dPdtime@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll oslexec.dll/    -1                      0       89        `
  ��  d唺城轊    ?dPdu@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll 
oslexec.dll/    -1                      0       89        `
  ��  d啳b繣    ?dPdv@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll 
oslexec.dll/    -1                      0       79        `
  ��  d啺�0�;    ?data@ClosureComponent@v1_14_4@OSL@@QEAAPEAXXZ oslexec.dll 
oslexec.dll/    -1                      0       79        `
  ��  d哘b民;    ?data@ClosureComponent@v1_14_4@OSL@@QEBAPEBXXZ oslexec.dll 
oslexec.dll/    -1                      0       75        `
  ��  d�(z�7    ?debug@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXH@Z oslexec.dll 
oslexec.dll/    -1                      0       74        `
  ��  d嗈�$�6    ?debug@LLVM_Util@pvt@v1_14_4@OSL@@QEBAHXZ oslexec.dll oslexec.dll/    -1                      0       86        `
  ��  d唍Si覤    ?debug_is_enabled@LLVM_Util@pvt@v1_14_4@OSL@@QEBA_NXZ oslexec.dll oslexec.dll/    -1                      0       87        `
  ��  d哰锴覥    ?debug_pop_function@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXXZ oslexec.dll 
oslexec.dll/    -1                      0       95        `
  ��  d�0bR鱇    ?debug_pop_inlined_function@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXXZ oslexec.dll 
oslexec.dll/    -1                      0       181       `
  ��  d啓,l瘛    ?debug_push_function@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@Vustring@OpenImageIO_v3_0@@H@Z oslexec.dll 
oslexec.dll/    -1                      0       125       `
  ��  d哸腸蝘    ?debug_push_inlined_function@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXVustring@OpenImageIO_v3_0@@0H@Z oslexec.dll 
oslexec.dll/    -1                      0       115       `
  ��  d�_搓_    ?debug_set_location@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXVustring@OpenImageIO_v3_0@@H@Z oslexec.dll 
oslexec.dll/    -1                      0       101       `
  ��  d嗃蜚錛    ?debug_setup_compilation_unit@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEBD@Z oslexec.dll 
oslexec.dll/    -1                      0       93        `
  ��  d�%�箐I    ?default_@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll 
oslexec.dll/    -1                      0       104       `
  ��  d嗀~麻T     ?delete_func_body@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVFunction@llvm@@@Z oslexec.dll oslexec.dll/    -1                      0       109       `
  ��  d哴鬥   ! ?destroy_thread_info@ShadingSystem@v1_14_4@OSL@@QEAAXPEAUPerThreadInfo@23@@Z oslexec.dll 
oslexec.dll/    -1                      0       107       `
  ��  d唚图腤   " ?detect_cpu_features@LLVM_Util@pvt@v1_14_4@OSL@@QEAA_NW4TargetISA@234@_N@Z oslexec.dll 
oslexec.dll/    -1                      0       89        `
  ��  d哾m�鳨   # ?device_alloc@RendererServices@v1_14_4@OSL@@UEAAPEAX_K@Z oslexec.dll 
oslexec.dll/    -1                      0       87        `
  ��  d�ｄ顲   $ ?device_free@RendererServices@v1_14_4@OSL@@UEAAXPEAX@Z oslexec.dll 
oslexec.dll/    -1                      0       128       `
  ��  d啰煿錶   % ?dict_find@ShadingContext@v1_14_4@OSL@@QEAAHPEAUShaderGlobals@23@HVustring@OpenImageIO_v3_0@@@Z oslexec.dll oslexec.dll/    -1                      0       128       `
  ��  d喒Y髄   & ?dict_find@ShadingContext@v1_14_4@OSL@@QEAAHPEAUShaderGlobals@23@Vustring@OpenImageIO_v3_0@@1@Z oslexec.dll oslexec.dll/    -1                      0       80        `
  ��  d�
[�<   ' ?dict_next@ShadingContext@v1_14_4@OSL@@QEAAHH@Z oslexec.dll oslexec.dll/    -1                      0       126       `
  ��  d啹`-頹   ( ?dict_value@ShadingContext@v1_14_4@OSL@@QEAAHHVustring@OpenImageIO_v3_0@@UTypeDesc@5@PEAX_N@Z oslexec.dll oslexec.dll/    -1                      0       94        `
  ��  d唌a麶   ) ?direction@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll oslexec.dll/    -1                      0       94        `
  ��  d啟錔   * ?do_filter@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll oslexec.dll/    -1                      0       145       `
  ��  d啩�&閩   + ?do_optimize@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z oslexec.dll 
oslexec.dll/    -1                      0       90        `
  ��  d啋;呴F   , ?dtime@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll oslexec.dll/    -1                      0       107       `
  ��  d咼�隬   - ?dump_struct_data_layout@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVType@llvm@@@Z oslexec.dll 
oslexec.dll/    -1                      0       78        `
  ��  d�#翈�:   . ?dumpasm@LLVM_Util@pvt@v1_14_4@OSL@@QEAAX_N@Z oslexec.dll oslexec.dll/    -1                      0       77        `
  ��  d哖Z哈9   / ?dumpasm@LLVM_Util@pvt@v1_14_4@OSL@@QEBA_NXZ oslexec.dll 
oslexec.dll/    -1                      0       105       `
  ��  d�%F鉛   0 ?element_type_of@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVType@llvm@@PEAV56@@Z oslexec.dll 
oslexec.dll/    -1                      0       74        `
  ��  d唩菗�6   1 ?end@Accumulator@v1_14_4@OSL@@QEAAXPEAX@Z oslexec.dll oslexec.dll/    -1                      0       171       `
  ��  d喦� 釛   2 ?end@OSLQuery@v1_14_4@OSL@@QEAA?AV?$_Vector_iterator@V?$_Vector_val@U?$_Simple_types@UParameter@OSLQuery@v1_14_4@OSL@@@std@@@std@@@std@@XZ oslexec.dll 
oslexec.dll/    -1                      0       88        `
  ��  d喰&_蹹   3 ?end@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll oslexec.dll/    -1                      0       80        `
  ��  d嗻I蘖<   4 ?end_builder@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXXZ oslexec.dll oslexec.dll/    -1                      0       247       `
  ��  d�5bd   5 ?environment@RendererServices@v1_14_4@OSL@@UEAA_NVustringhash@OpenImageIO_v3_0@@PEAVTextureHandle@TextureSystem@5@PEAVPerthread@75@AEAVTextureOpt_v2@5@PEAUShaderGlobals@23@AEBV?$Vec3@M@Imath_3_1@@55HPEAM66PEAV45@@Z oslexec.dll 
oslexec.dll/    -1                      0       90        `
  ��  d咾z疹F   6 ?error@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll oslexec.dll/    -1                      0       158       `
  ��  d喭?ヨ�   7 ?errorfmt@RendererServices@v1_14_4@OSL@@UEAAXPEAUShaderGlobals@23@Vustringhash@OpenImageIO_v3_0@@HPEBW4EncodedType@23@IPEAE@Z oslexec.dll oslexec.dll/    -1                      0       97        `
  ��  d哹趃贛   8 ?errormessage@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll 
oslexec.dll/    -1                      0       104       `
  ��  d唊|黅   9 ?execengine@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVExecutionEngine@llvm@@XZ oslexec.dll oslexec.dll/    -1                      0       105       `
  ��  d嗺輪襏   : ?execengine@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVExecutionEngine@llvm@@@Z oslexec.dll 
oslexec.dll/    -1                      0       127       `
  ��  d嗙�k   ; ?execute@ShadingContext@v1_14_4@OSL@@QEAA_NAEAVShaderGroup@23@HHAEAUShaderGlobals@23@PEAX2_N@Z oslexec.dll 
oslexec.dll/    -1                      0       141       `
  ��  d�aH纘   < ?execute@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShadingContext@23@AEAVShaderGroup@23@AEAUShaderGlobals@23@_N@Z oslexec.dll 
oslexec.dll/    -1                      0       148       `
  ��  d哠貈   = ?execute@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShadingContext@23@AEAVShaderGroup@23@HHAEAUShaderGlobals@23@PEAX3_N@Z oslexec.dll oslexec.dll/    -1                      0       141       `
  ��  d啱邋遹   > ?execute@ShadingSystem@v1_14_4@OSL@@QEAA_NPEAVShadingContext@23@AEAVShaderGroup@23@AEAUShaderGlobals@23@_N@Z oslexec.dll 
oslexec.dll/    -1                      0       86        `
  ��  d啂J虰   ? ?execute_cleanup@ShadingContext@v1_14_4@OSL@@QEAA_NXZ oslexec.dll oslexec.dll/    -1                      0       107       `
  ��  d��:   @ ?execute_cleanup@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShadingContext@23@@Z oslexec.dll 
oslexec.dll/    -1                      0       132       `
  ��  d唸�騪   A ?execute_init@ShadingContext@v1_14_4@OSL@@QEAA_NAEAVShaderGroup@23@HHAEAUShaderGlobals@23@PEAX2_N@Z oslexec.dll oslexec.dll/    -1                      0       146       `
  ��  d唋�.詞   B ?execute_init@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShadingContext@23@AEAVShaderGroup@23@AEAUShaderGlobals@23@_N@Z oslexec.dll oslexec.dll/    -1                      0       153       `
  ��  d啎OD飬   C ?execute_init@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShadingContext@23@AEAVShaderGroup@23@HHAEAUShaderGlobals@23@PEAX3_N@Z oslexec.dll 
oslexec.dll/    -1                      0       113       `
  ��  d�
7醈   D ?execute_layer@ShadingContext@v1_14_4@OSL@@QEAA_NHHAEAUShaderGlobals@23@PEAX1H@Z oslexec.dll 
oslexec.dll/    -1                      0       127       `
  ��  d唭�<錵   E ?execute_layer@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShadingContext@23@AEAUShaderGlobals@23@H@Z oslexec.dll 
oslexec.dll/    -1                      0       146       `
  ��  d咾鷡   F ?execute_layer@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShadingContext@23@AEAUShaderGlobals@23@PEBVShaderSymbol@23@@Z oslexec.dll oslexec.dll/    -1                      0       153       `
  ��  d�:gI蹍   G ?execute_layer@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShadingContext@23@AEAUShaderGlobals@23@Vustring@OpenImageIO_v3_0@@@Z oslexec.dll 
oslexec.dll/    -1                      0       134       `
  ��  d唶鐁   H ?execute_layer@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShadingContext@23@HHAEAUShaderGlobals@23@PEAX2H@Z oslexec.dll oslexec.dll/    -1                      0       153       `
  ��  d嗭n!蓞   I ?execute_layer@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShadingContext@23@HHAEAUShaderGlobals@23@PEAX2PEBVShaderSymbol@23@@Z oslexec.dll 
oslexec.dll/    -1                      0       160       `
  ��  d�(RD蠈   J ?execute_layer@ShadingSystem@v1_14_4@OSL@@QEAA_NAEAVShadingContext@23@HHAEAUShaderGlobals@23@PEAX2Vustring@OpenImageIO_v3_0@@@Z oslexec.dll oslexec.dll/    -1                      0       91        `
  ��  d哣癿騁   K ?execution_is_batched@ShadingContext@v1_14_4@OSL@@AEBA_NXZ oslexec.dll 
oslexec.dll/    -1                      0       158       `
  ��  d嗘碇銑   L ?filefmt@RendererServices@v1_14_4@OSL@@UEAAXPEAUShaderGlobals@23@Vustringhash@OpenImageIO_v3_0@@1HPEBW4EncodedType@23@IPEAE@Z oslexec.dll oslexec.dll/    -1                      0       89        `
  ��  d哛謰鶨   M ?fill@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll 
oslexec.dll/    -1                      0       125       `
  ��  d唡朜鏸   N ?find_layer@ShadingSystem@v1_14_4@OSL@@QEBAHAEBVShaderGroup@23@Vustring@OpenImageIO_v3_0@@@Z oslexec.dll 
oslexec.dll/    -1                      0       154       `
  ��  d�塺迒   O ?find_regex@ShadingContext@v1_14_4@OSL@@QEAAAEBV?$basic_regex@DV?$regex_traits@D@std@@@std@@Vustring@OpenImageIO_v3_0@@@Z oslexec.dll oslexec.dll/    -1                      0       146       `
  ��  d喕阵~   P ?find_symbol@ShadingSystem@v1_14_4@OSL@@QEBAPEBVShaderSymbol@23@AEBVShaderGroup@23@Vustring@OpenImageIO_v3_0@@1@Z oslexec.dll oslexec.dll/    -1                      0       145       `
  ��  d啺髜聖   Q ?find_symbol@ShadingSystem@v1_14_4@OSL@@QEBAPEBVShaderSymbol@23@AEBVShaderGroup@23@Vustring@OpenImageIO_v3_0@@@Z oslexec.dll 
oslexec.dll/    -1                      0       148       `
  ��  d哾�:鱻   R ?find_symloc@ShadingSystem@v1_14_4@OSL@@QEBAPEBUSymLocationDesc@23@PEBVShaderGroup@23@Vustring@OpenImageIO_v3_0@@@Z oslexec.dll oslexec.dll/    -1                      0       162       `
  ��  d啎S銕   S ?find_symloc@ShadingSystem@v1_14_4@OSL@@QEBAPEBUSymLocationDesc@23@PEBVShaderGroup@23@Vustring@OpenImageIO_v3_0@@W4SymArena@23@@Z oslexec.dll oslexec.dll/    -1                      0       129       `
  ��  d�d钍m   T ?find_symloc@ShadingSystem@v1_14_4@OSL@@QEBAPEBUSymLocationDesc@23@Vustring@OpenImageIO_v3_0@@@Z oslexec.dll 
oslexec.dll/    -1                      0       143       `
  ��  d啘@庁{   U ?find_symloc@ShadingSystem@v1_14_4@OSL@@QEBAPEBUSymLocationDesc@23@Vustring@OpenImageIO_v3_0@@W4SymArena@23@@Z oslexec.dll 
oslexec.dll/    -1                      0       97        `
  ��  d咹�/镸   V ?firstchannel@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll 
oslexec.dll/    -1                      0       89        `
  ��  d啢苛鏓   W ?free_dict_resources@ShadingContext@v1_14_4@OSL@@AEAAXXZ oslexec.dll 
oslexec.dll/    -1                      0       125       `
  ��  d喬x遡   X ?fromString@ColorSystem@pvt@v1_14_4@OSL@@SAPEBUChroma@1234@Vustringhash@OpenImageIO_v3_0@@@Z oslexec.dll 
oslexec.dll/    -1                      0       184       `
  ��  d哯�亭   Y ?from_rgb@ColorSystem@pvt@v1_14_4@OSL@@QEBA?AV?$Color3@M@Imath_3_1@@Vustringhash@OpenImageIO_v3_0@@AEBV56@PEAVShadingContext@34@PEAUShaderGlobals@34@@Z oslexec.dll oslexec.dll/    -1                      0       90        `
  ��  d�%牿鬎   Z ?front@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll oslexec.dll/    -1                      0       102       `
  ��  d啋鑄馬   [ ?func_is_empty@LLVM_Util@pvt@v1_14_4@OSL@@QEAA_NPEAVFunction@llvm@@@Z oslexec.dll oslexec.dll/    -1                      0       160       `
  ��  d唄烓�   \ ?func_name@LLVM_Util@pvt@v1_14_4@OSL@@QEAA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@PEAVFunction@llvm@@@Z oslexec.dll oslexec.dll/    -1                      0       90        `
  ��  d�&'与F   ] ?gabor@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll oslexec.dll/    -1                      0       95        `
  ��  d咲靛K   ^ ?gabornoise@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll 
oslexec.dll/    -1                      0       96        `
  ��  d哷腾L   _ ?gaborpnoise@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll oslexec.dll/    -1                      0       97        `
  ��  d唊�霱   ` ?genericnoise@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll 
oslexec.dll/    -1                      0       98        `
  ��  d嗈,b翹   a ?genericpnoise@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll oslexec.dll/    -1                      0       100       `
  ��  d哃14镻   b ?get@PerThreadInfo@LLVM_Util@pvt@v1_14_4@OSL@@AEBAPEAUImpl@12345@XZ oslexec.dll oslexec.dll/    -1                      0       106       `
  ��  d喦!1釼   c ?getCurrentDebugScope@LLVM_Util@pvt@v1_14_4@OSL@@AEBAPEAVDIScope@llvm@@XZ oslexec.dll oslexec.dll/    -1                      0       111       `
  ��  d�9J圇[   d ?getCurrentInliningSite@LLVM_Util@pvt@v1_14_4@OSL@@AEBAPEAVDILocation@llvm@@XZ oslexec.dll 
oslexec.dll/    -1                      0       173       `
  ��  d唞E�   e ?getOrCreateDebugFileFor@LLVM_Util@pvt@v1_14_4@OSL@@AEAAPEAVDIFile@llvm@@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z oslexec.dll 
oslexec.dll/    -1                      0       93        `
  ��  d�.CD鶬   f ?getOutput@Accumulator@v1_14_4@OSL@@QEBAAEBUAovOutput@23@H@Z oslexec.dll 
oslexec.dll/    -1                      0       79        `
  ��  d唸"君;   g ?getOutputIndex@AccumRule@v1_14_4@OSL@@QEBAHXZ oslexec.dll 
oslexec.dll/    -1                      0       111       `
  ��  d哱�鈁   h ?getPointerToFunction@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAXPEAVFunction@llvm@@@Z oslexec.dll 
oslexec.dll/    -1                      0       163       `
  ��  d嗚魆鋸   i ?getRuleList@AccumAutomata@v1_14_4@OSL@@QEBAAEBV?$list@VAccumRule@v1_14_4@OSL@@V?$allocator@VAccumRule@v1_14_4@OSL@@@std@@@std@@XZ oslexec.dll 
oslexec.dll/    -1                      0       94        `
  ��  d嗏躂   j ?getRules@DfOptimizedAutomata@v1_14_4@OSL@@QEBAPEBQEAXHAEAH@Z oslexec.dll oslexec.dll/    -1                      0       95        `
  ��  d咮橗螷   k ?getRulesInState@AccumAutomata@v1_14_4@OSL@@QEBAPEBQEAXHAEAH@Z oslexec.dll 
oslexec.dll/    -1                      0       110       `
  ��  d�#sz踆   l ?getTransition@AccumAutomata@v1_14_4@OSL@@QEBAHHVustring@OpenImageIO_v3_0@@@Z oslexec.dll oslexec.dll/    -1                      0       116       `
  ��  d唂Q糨`   m ?getTransition@DfOptimizedAutomata@v1_14_4@OSL@@QEBAHHVustring@OpenImageIO_v3_0@@@Z oslexec.dll oslexec.dll/    -1                      0       164       `
  ��  d哠
限�   n ?get_array_attribute@RendererServices@v1_14_4@OSL@@UEAA_NPEAUShaderGlobals@23@_NVustringhash@OpenImageIO_v3_0@@UTypeDesc@6@2HPEAX@Z oslexec.dll oslexec.dll/    -1                      0       157       `
  ��  d喲|ミ�   o ?get_attribute@RendererServices@v1_14_4@OSL@@UEAA_NPEAUShaderGlobals@23@_NVustringhash@OpenImageIO_v3_0@@UTypeDesc@6@2PEAX@Z oslexec.dll 
oslexec.dll/    -1                      0       168       `
  ��  d�)胵龜   p ?get_context@ShadingSystem@v1_14_4@OSL@@QEAAPEAVShadingContext@23@PEAUPerThreadInfo@23@PEAVPerthread@TextureSystem@OpenImageIO_v3_0@@@Z oslexec.dll oslexec.dll/    -1                      0       144       `
  ��  d嘃(褆   q ?get_inverse_matrix@RendererServices@v1_14_4@OSL@@UEAA_NPEAUShaderGlobals@23@AEAV?$Matrix44@M@Imath_3_1@@PEBX@Z oslexec.dll oslexec.dll/    -1                      0       145       `
  ��  d嗎:溆}   r ?get_inverse_matrix@RendererServices@v1_14_4@OSL@@UEAA_NPEAUShaderGlobals@23@AEAV?$Matrix44@M@Imath_3_1@@PEBXM@Z oslexec.dll 
oslexec.dll/    -1                      0       171       `
  ��  d喣3〓�   s ?get_inverse_matrix@RendererServices@v1_14_4@OSL@@UEAA_NPEAUShaderGlobals@23@AEAV?$Matrix44@M@Imath_3_1@@Vustringhash@OpenImageIO_v3_0@@@Z oslexec.dll 
oslexec.dll/    -1                      0       172       `
  ��  d唲�2讟   t ?get_inverse_matrix@RendererServices@v1_14_4@OSL@@UEAA_NPEAUShaderGlobals@23@AEAV?$Matrix44@M@Imath_3_1@@Vustringhash@OpenImageIO_v3_0@@M@Z oslexec.dll oslexec.dll/    -1                      0       136       `
  ��  d唫魳錿   u ?get_matrix@RendererServices@v1_14_4@OSL@@UEAA_NPEAUShaderGlobals@23@AEAV?$Matrix44@M@Imath_3_1@@PEBX@Z oslexec.dll oslexec.dll/    -1                      0       137       `
  ��  d�匦饀   v ?get_matrix@RendererServices@v1_14_4@OSL@@UEAA_NPEAUShaderGlobals@23@AEAV?$Matrix44@M@Imath_3_1@@PEBXM@Z oslexec.dll 
oslexec.dll/    -1                      0       163       `
  ��  d啴�邚   w ?get_matrix@RendererServices@v1_14_4@OSL@@UEAA_NPEAUShaderGlobals@23@AEAV?$Matrix44@M@Imath_3_1@@Vustringhash@OpenImageIO_v3_0@@@Z oslexec.dll 
oslexec.dll/    -1                      0       164       `
  ��  d啽�=饜   x ?get_matrix@RendererServices@v1_14_4@OSL@@UEAA_NPEAUShaderGlobals@23@AEAV?$Matrix44@M@Imath_3_1@@Vustringhash@OpenImageIO_v3_0@@M@Z oslexec.dll oslexec.dll/    -1                      0       88        `
  ��  d�=""虳   y ?get_max_warnings_per_thread@pvt@v1_14_4@OSL@@YAHQEAX@Z oslexec.dll oslexec.dll/    -1                      0       147       `
  ��  d嗞@嬨   z ?get_symbol@ShadingSystem@v1_14_4@OSL@@QEBAPEBXAEBVShadingContext@23@Vustring@OpenImageIO_v3_0@@1AEAUTypeDesc@6@@Z oslexec.dll 
oslexec.dll/    -1                      0       146       `
  ��  d��鴡   { ?get_symbol@ShadingSystem@v1_14_4@OSL@@QEBAPEBXAEBVShadingContext@23@Vustring@OpenImageIO_v3_0@@AEAUTypeDesc@6@@Z oslexec.dll oslexec.dll/    -1                      0       192       `
  ��  d嗹霫喱   | ?get_texture_handle@RendererServices@v1_14_4@OSL@@UEAAPEAVTextureHandle@TextureSystem@OpenImageIO_v3_0@@Vustring@6@PEAVShadingContext@23@PEBVTextureOpt_v2@6@@Z oslexec.dll oslexec.dll/    -1                      0       196       `
  ��  d唂2検�   } ?get_texture_handle@RendererServices@v1_14_4@OSL@@UEAAPEAVTextureHandle@TextureSystem@OpenImageIO_v3_0@@Vustringhash@6@PEAVShadingContext@23@PEBVTextureOpt_v2@6@@Z oslexec.dll oslexec.dll/    -1                      0       219       `
  ��  d嗺莐谇   ~ ?get_texture_info@RendererServices@v1_14_4@OSL@@UEAA_NVustringhash@OpenImageIO_v3_0@@PEAVTextureHandle@TextureSystem@5@MMPEAVPerthread@75@PEAUShaderGlobals@23@H0UTypeDesc@5@PEAXPEAV45@@Z oslexec.dll 
oslexec.dll/    -1                      0       217       `
  ��  d哰喺榕    ?get_texture_info@RendererServices@v1_14_4@OSL@@UEAA_NVustringhash@OpenImageIO_v3_0@@PEAVTextureHandle@TextureSystem@5@PEAVPerthread@75@PEAUShaderGlobals@23@H0UTypeDesc@5@PEAXPEAV45@@Z oslexec.dll 
oslexec.dll/    -1                      0       155       `
  ��  d�4�"聡   � ?get_userdata@RendererServices@v1_14_4@OSL@@UEAA_N_NVustringhash@OpenImageIO_v3_0@@UTypeDesc@5@PEAUShaderGlobals@23@PEAX@Z oslexec.dll 
oslexec.dll/    -1                      0       168       `
  ��  d�5炻�   � ?getattribute@ShadingSystem@v1_14_4@OSL@@QEAA_NPEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@AEAH@Z oslexec.dll oslexec.dll/    -1                      0       168       `
  ��  d唵�8閿   � ?getattribute@ShadingSystem@v1_14_4@OSL@@QEAA_NPEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@AEAM@Z oslexec.dll oslexec.dll/    -1                      0       168       `
  ��  d啀铡鷶   � ?getattribute@ShadingSystem@v1_14_4@OSL@@QEAA_NPEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@AEAN@Z oslexec.dll oslexec.dll/    -1                      0       229       `
  ��  d嗰
萄   � ?getattribute@ShadingSystem@v1_14_4@OSL@@QEAA_NPEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@AEAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z oslexec.dll 
oslexec.dll/    -1                      0       178       `
  ��  d�:露鳛   � ?getattribute@ShadingSystem@v1_14_4@OSL@@QEAA_NPEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@AEAVustring@6@@Z oslexec.dll oslexec.dll/    -1                      0       171       `
  ��  d嗈嵬讞   � ?getattribute@ShadingSystem@v1_14_4@OSL@@QEAA_NPEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@PEAPEAD@Z oslexec.dll 
oslexec.dll/    -1                      0       180       `
  ��  d喆袰駹   � ?getattribute@ShadingSystem@v1_14_4@OSL@@QEAA_NPEAVShaderGroup@23@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@UTypeDesc@6@PEAX@Z oslexec.dll oslexec.dll/    -1                      0       149       `
  ��  d啩R旪�   � ?getattribute@ShadingSystem@v1_14_4@OSL@@QEAA_NV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@AEAH@Z oslexec.dll 
oslexec.dll/    -1                      0       149       `
  ��  d喼a遒�   � ?getattribute@ShadingSystem@v1_14_4@OSL@@QEAA_NV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@AEAM@Z oslexec.dll 
oslexec.dll/    -1                      0       149       `
  ��  d啘崢飦   � ?getattribute@ShadingSystem@v1_14_4@OSL@@QEAA_NV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@AEAN@Z oslexec.dll 
oslexec.dll/    -1                      0       210       `
  ��  d嗢*溍�   � ?getattribute@ShadingSystem@v1_14_4@OSL@@QEAA_NV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@AEAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z oslexec.dll oslexec.dll/    -1                      0       159       `
  ��  d喖冨顙   � ?getattribute@ShadingSystem@v1_14_4@OSL@@QEAA_NV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@AEAVustring@5@@Z oslexec.dll 
oslexec.dll/    -1                      0       152       `
  ��  d唘閤鋭   � ?getattribute@ShadingSystem@v1_14_4@OSL@@QEAA_NV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@PEAPEAD@Z oslexec.dll oslexec.dll/    -1                      0       161       `
  ��  d嘅炬�   � ?getattribute@ShadingSystem@v1_14_4@OSL@@QEAA_NV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@UTypeDesc@5@PEAX@Z oslexec.dll 
oslexec.dll/    -1                      0       137       `
  ��  d�帼閡   � ?geterror@OSLQuery@v1_14_4@OSL@@QEAA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@_N@Z oslexec.dll 
oslexec.dll/    -1                      0       154       `
  ��  d嗠敗顔   � ?getmessage@RendererServices@v1_14_4@OSL@@UEAA_NPEAUShaderGlobals@23@Vustringhash@OpenImageIO_v3_0@@1UTypeDesc@6@PEAX_N@Z oslexec.dll oslexec.dll/    -1                      0       154       `
  ��  d啢謩閱   � ?getparam@OSLQuery@v1_14_4@OSL@@QEBAPEBUParameter@123@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z oslexec.dll oslexec.dll/    -1                      0       116       `
  ��  d嗂曦颼   � ?getparam@OSLQuery@v1_14_4@OSL@@QEBAPEBUParameter@123@Vustring@OpenImageIO_v3_0@@@Z oslexec.dll oslexec.dll/    -1                      0       91        `
  ��  d嗢簴鶪   � ?getparam@OSLQuery@v1_14_4@OSL@@QEBAPEBUParameter@123@_K@Z oslexec.dll 
oslexec.dll/    -1                      0       141       `
  ��  d哢饄   � ?getstats@ShadingSystem@v1_14_4@OSL@@QEBA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@H@Z oslexec.dll 
oslexec.dll/    -1                      0       118       `
  ��  d啒鶬鑒   � ?globals_bit@ShadingSystem@v1_14_4@OSL@@SA?AW4SGBits@23@Vustring@OpenImageIO_v3_0@@@Z oslexec.dll oslexec.dll/    -1                      0       119       `
  ��  d唭dc蝐   � ?globals_name@ShadingSystem@v1_14_4@OSL@@SA?AVustring@OpenImageIO_v3_0@@W4SGBits@23@@Z oslexec.dll 
oslexec.dll/    -1                      0       127       `
  ��  d�詋   � ?good@RendererServices@v1_14_4@OSL@@UEAA_NPEAVTextureHandle@TextureSystem@OpenImageIO_v3_0@@@Z oslexec.dll 
oslexec.dll/    -1                      0       93        `
  ��  d唗驣   � ?group@ShadingContext@v1_14_4@OSL@@QEAAPEAVShaderGroup@23@XZ oslexec.dll 
oslexec.dll/    -1                      0       94        `
  ��  d咼�翵   � ?group@ShadingContext@v1_14_4@OSL@@QEAAXPEAVShaderGroup@23@@Z oslexec.dll oslexec.dll/    -1                      0       93        `
  ��  d唽2翴   � ?group@ShadingContext@v1_14_4@OSL@@QEBAPEBVShaderGroup@23@XZ oslexec.dll 
oslexec.dll/    -1                      0       93        `
  ��  d嘃镮   � ?has_masked_return_block@LLVM_Util@pvt@v1_14_4@OSL@@QEBA_NXZ oslexec.dll 
oslexec.dll/    -1                      0       89        `
  ��  d喩姥E   � ?hash@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll 
oslexec.dll/    -1                      0       94        `
  ��  d喦衜鞪   � ?hashnoise@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll oslexec.dll/    -1                      0       92        `
  ��  d啔{Z誋   � ?hermite@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll oslexec.dll/    -1                      0       88        `
  ��  d哱�餌   � ?hsl@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll oslexec.dll/    -1                      0       88        `
  ��  d喗熎貲   � ?hsv@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll oslexec.dll/    -1                      0       93        `
  ��  d喣�镮   � ?impulses@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll 
oslexec.dll/    -1                      0       93        `
  ��  d�?閧鶬   � ?incr_get_userdata_calls@ShadingContext@v1_14_4@OSL@@QEAAXXZ oslexec.dll 
oslexec.dll/    -1                      0       90        `
  ��  d啒6谾   � ?incr_layers_executed@ShadingContext@v1_14_4@OSL@@QEAAXXZ oslexec.dll oslexec.dll/    -1                      0       89        `
  ��  d嗃麰   � ?init@OSLQuery@v1_14_4@OSL@@QEAA_NPEBVShaderGroup@23@H@Z oslexec.dll 
oslexec.dll/    -1                      0       86        `
  ��  d喺猶麭   � ?initialize_buffer@journal@v1_14_4@OSL@@YA_NQEAEIIH@Z oslexec.dll oslexec.dll/    -1                      0       85        `
  ��  d嘄8翧   � ?inside_function@LLVM_Util@pvt@v1_14_4@OSL@@QEBA_NXZ oslexec.dll 
oslexec.dll/    -1                      0       108       `
  ��  d唃晒鵛   � ?inside_of_inlined_masked_function_call@LLVM_Util@pvt@v1_14_4@OSL@@AEBA_NXZ oslexec.dll oslexec.dll/    -1                      0       91        `
  ��  d喡`@逩   � ?inside_of_masked_loop@LLVM_Util@pvt@v1_14_4@OSL@@AEBA_NXZ oslexec.dll 
oslexec.dll/    -1                      0       102       `
  ��  d啋}�R   � ?int_as_mask@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z oslexec.dll oslexec.dll/    -1                      0       106       `
  ��  d�,S*黇   � ?int_to_ptr_cast@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z oslexec.dll oslexec.dll/    -1                      0       319       `
  ��  d��+  � ?internalize_module_functions@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEBV?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@6@1@Z oslexec.dll 
oslexec.dll/    -1                      0       91        `
  ��  d哻溜G   � ?interp@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll 
oslexec.dll/    -1                      0       94        `
  ��  d唕鸳鯦   � ?is_innermost_loop_masked@LLVM_Util@pvt@v1_14_4@OSL@@QEBA_NXZ oslexec.dll oslexec.dll/    -1                      0       89        `
  ��  d嗠Y]轊   � ?is_masking_required@LLVM_Util@pvt@v1_14_4@OSL@@QEBA_NXZ oslexec.dll 
oslexec.dll/    -1                      0       98        `
  ��  d喐嶟N   � ?is_type_array@LLVM_Util@pvt@v1_14_4@OSL@@QEAA_NPEAVType@llvm@@@Z oslexec.dll oslexec.dll/    -1                      0       130       `
  ��  d唫�賜   � ?is_udim@RendererServices@v1_14_4@OSL@@UEAA_NPEAVTextureHandle@TextureSystem@OpenImageIO_v3_0@@@Z oslexec.dll oslexec.dll/    -1                      0       85        `
  ��  d�-Om鬉   � ?jit_aggressive@LLVM_Util@pvt@v1_14_4@OSL@@QEAAX_N@Z oslexec.dll 
oslexec.dll/    -1                      0       84        `
  ��  d嗭澧隌   � ?jit_aggressive@LLVM_Util@pvt@v1_14_4@OSL@@QEBA_NXZ oslexec.dll oslexec.dll/    -1                      0       78        `
  ��  d咥淒�:   � ?jit_fma@LLVM_Util@pvt@v1_14_4@OSL@@QEAAX_N@Z oslexec.dll oslexec.dll/    -1                      0       77        `
  ��  d啘�┵9   � ?jit_fma@LLVM_Util@pvt@v1_14_4@OSL@@QEBA_NXZ oslexec.dll 
oslexec.dll/    -1                      0       90        `
  ��  d喍S绳F   � ?label@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll oslexec.dll/    -1                      0       91        `
  ��  d嗀�7薌   � ?linear@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll 
oslexec.dll/    -1                      0       101       `
  ��  d�:昣螿   � ?llvm_alignmentof@LLVM_Util@pvt@v1_14_4@OSL@@QEBA_KPEAVType@llvm@@@Z oslexec.dll 
oslexec.dll/    -1                      0       110       `
  ��  d嘃顸繸   � ?llvm_mask_to_native@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z oslexec.dll oslexec.dll/    -1                      0       96        `
  ��  d喰麔豅   � ?llvm_sizeof@LLVM_Util@pvt@v1_14_4@OSL@@QEBA_KPEAVType@llvm@@@Z oslexec.dll oslexec.dll/    -1                      0       120       `
  ��  d�Eもd   � ?llvm_thread_info@ShadingContext@v1_14_4@OSL@@QEBAAEBUPerThreadInfo@LLVM_Util@pvt@23@XZ oslexec.dll oslexec.dll/    -1                      0       123       `
  ��  d�)Ck鏶   � ?llvm_type@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVType@llvm@@AEBUTypeDesc@OpenImageIO_v3_0@@@Z oslexec.dll 
oslexec.dll/    -1                      0       160       `
  ��  d嗶�   � ?llvm_typename@LLVM_Util@pvt@v1_14_4@OSL@@QEBA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@PEAVType@llvm@@@Z oslexec.dll oslexec.dll/    -1                      0       163       `
  ��  d啱碭饛   � ?llvm_typenameof@LLVM_Util@pvt@v1_14_4@OSL@@QEBA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@PEAVValue@llvm@@@Z oslexec.dll 
oslexec.dll/    -1                      0       106       `
  ��  d啌W�鉜   � ?llvm_typeof@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@PEAVValue@6@@Z oslexec.dll oslexec.dll/    -1                      0       130       `
  ��  d啺豱   � ?llvm_vector_type@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVType@llvm@@AEBUTypeDesc@OpenImageIO_v3_0@@@Z oslexec.dll oslexec.dll/    -1                      0       115       `
  ��  d�;慁骭   � ?llvm_vector_type@LLVM_Util@pvt@v1_14_4@OSL@@SAPEAVVectorType@llvm@@PEAVType@6@I@Z oslexec.dll 
oslexec.dll/    -1                      0       116       `
  ��  d喲媘鈆   � ?lookup_blackbody_rgb@ColorSystem@pvt@v1_14_4@OSL@@QEBA?AV?$Color3@M@Imath_3_1@@M@Z oslexec.dll oslexec.dll/    -1                      0       165       `
  ��  d咶鯳艖   � ?lookup_isa_by_name@LLVM_Util@pvt@v1_14_4@OSL@@SA?AW4TargetISA@234@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@@Z oslexec.dll 
oslexec.dll/    -1                      0       105       `
  ��  d啨间譛   � ?loop_after_block@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVBasicBlock@llvm@@XZ oslexec.dll 
oslexec.dll/    -1                      0       104       `
  ��  d啯卭軹   � ?loop_step_block@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVBasicBlock@llvm@@XZ oslexec.dll oslexec.dll/    -1                      0       106       `
  ��  d咢輥騐   � ?luminance@ColorSystem@pvt@v1_14_4@OSL@@QEBAMAEBV?$Color3@M@Imath_3_1@@@Z oslexec.dll oslexec.dll/    -1                      0       111       `
  ��  d啒隇賉   � ?luminance_scale@ColorSystem@pvt@v1_14_4@OSL@@QEBAAEBV?$Color3@M@Imath_3_1@@XZ oslexec.dll 
oslexec.dll/    -1                      0       225       `
  ��  d啍D�萃   � ?make_function@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVFunction@llvm@@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@_NPEAVType@6@V?$span@QEAVType@llvm@@$0?0@OpenImageIO_v3_0@@1@Z oslexec.dll 
oslexec.dll/    -1                      0       197       `
  ��  d�?椦�   � ?make_jit_execengine@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVExecutionEngine@llvm@@PEAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@W4TargetISA@234@_N2@Z oslexec.dll 
oslexec.dll/    -1                      0       104       `
  ��  d啳�覶   � ?mark_fast_func_call@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVValue@llvm@@@Z oslexec.dll oslexec.dll/    -1                      0       104       `
  ��  d問弹耇   � ?mask4_as_int8@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z oslexec.dll oslexec.dll/    -1                      0       104       `
  ��  d�"趮覶   � ?mask_as_int16@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z oslexec.dll oslexec.dll/    -1                      0       103       `
  ��  d哋祥騍   � ?mask_as_int8@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z oslexec.dll 
oslexec.dll/    -1                      0       102       `
  ��  d喓~R釸   � ?mask_as_int@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z oslexec.dll oslexec.dll/    -1                      0       87        `
  ��  d唕飤鉉   � ?masked_break_count@LLVM_Util@pvt@v1_14_4@OSL@@QEBAHXZ oslexec.dll 
oslexec.dll/    -1                      0       90        `
  ��  d喸5鲶F   � ?masked_continue_count@LLVM_Util@pvt@v1_14_4@OSL@@QEBAHXZ oslexec.dll oslexec.dll/    -1                      0       86        `
  ��  d喞�荁   � ?masked_exit_count@LLVM_Util@pvt@v1_14_4@OSL@@QEBAHXZ oslexec.dll oslexec.dll/    -1                      0       124       `
  ��  d� IP胔   � ?masked_function_context@LLVM_Util@pvt@v1_14_4@OSL@@AEAAAEAUMaskedSubroutineContext@1234@XZ oslexec.dll oslexec.dll/    -1                      0       124       `
  ��  d�%d宿h   � ?masked_function_context@LLVM_Util@pvt@v1_14_4@OSL@@AEBAAEBUMaskedSubroutineContext@1234@XZ oslexec.dll oslexec.dll/    -1                      0       114       `
  ��  d唋{鱚   � ?masked_loop_context@LLVM_Util@pvt@v1_14_4@OSL@@AEAAAEAUMaskedLoopContext@1234@XZ oslexec.dll oslexec.dll/    -1                      0       114       `
  ��  d唽�4館   � ?masked_loop_context@LLVM_Util@pvt@v1_14_4@OSL@@AEBAAEBUMaskedLoopContext@1234@XZ oslexec.dll oslexec.dll/    -1                      0       108       `
  ��  d哃{8鞽   � ?masked_return_block@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVBasicBlock@llvm@@XZ oslexec.dll oslexec.dll/    -1                      0       88        `
  ��  d�+銬   � ?masked_return_count@LLVM_Util@pvt@v1_14_4@OSL@@QEBAHXZ oslexec.dll oslexec.dll/    -1                      0       122       `
  ��  d�3�9胒   � ?masked_shader_context@LLVM_Util@pvt@v1_14_4@OSL@@AEAAAEAUMaskedSubroutineContext@1234@XZ oslexec.dll oslexec.dll/    -1                      0       91        `
  ��  d�'紶鞧   � ?matrix@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll 
oslexec.dll/    -1                      0       92        `
  ��  d嗱蹣譎   � ?maxdist@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll oslexec.dll/    -1                      0       100       `
  ��  d嗻臥   � ?messages@ShadingContext@v1_14_4@OSL@@QEAAAEAUMessageList@pvt@23@XZ oslexec.dll oslexec.dll/    -1                      0       175       `
  ��  d哃沖龥   � ?metadata@OSLQuery@v1_14_4@OSL@@QEBAAEBV?$vector@UParameter@OSLQuery@v1_14_4@OSL@@V?$allocator@UParameter@OSLQuery@v1_14_4@OSL@@@std@@@std@@XZ oslexec.dll 
oslexec.dll/    -1                      0       92        `
  ��  d唝櫒蘃   � ?mindist@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll oslexec.dll/    -1                      0       91        `
  ��  d�'M婗G   � ?mirror@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll 
oslexec.dll/    -1                      0       97        `
  ��  d問KM翸   � ?missingalpha@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll 
oslexec.dll/    -1                      0       97        `
  ��  d唶劃譓   � ?missingcolor@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll 
oslexec.dll/    -1                      0       91        `
  ��  d啎聠蹽   � ?module@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVModule@llvm@@XZ oslexec.dll 
oslexec.dll/    -1                      0       92        `
  ��  d哹琏貶   � ?module@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVModule@llvm@@@Z oslexec.dll oslexec.dll/    -1                      0       182       `
  ��  d�ay膦   � ?module_from_bitcode@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVModule@llvm@@PEBD_KAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@PEAV78@@Z oslexec.dll oslexec.dll/    -1                      0       145       `
  ��  d啅濄鈣   � ?module_string@LLVM_Util@pvt@v1_14_4@OSL@@QEAA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ oslexec.dll 
oslexec.dll/    -1                      0       101       `
  ��  d嗭栃誕   � ?move@Accumulator@v1_14_4@OSL@@QEAAXPEBVustring@OpenImageIO_v3_0@@@Z oslexec.dll 
oslexec.dll/    -1                      0       107       `
  ��  d�[鬢   � ?move@Accumulator@v1_14_4@OSL@@QEAAXVustring@OpenImageIO_v3_0@@0PEBV45@0@Z oslexec.dll 
oslexec.dll/    -1                      0       98        `
  ��  d喞S腘   � ?move@Accumulator@v1_14_4@OSL@@QEAAXVustring@OpenImageIO_v3_0@@@Z oslexec.dll oslexec.dll/    -1                      0       110       `
  ��  d唂薀馴   � ?native_to_llvm_mask@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z oslexec.dll oslexec.dll/    -1                      0       102       `
  ��  d啞憜跼   � ?negate_mask@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z oslexec.dll oslexec.dll/    -1                      0       169       `
  ��  d唓踭鶗   � ?new_basic_block@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVBasicBlock@llvm@@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z oslexec.dll 
oslexec.dll/    -1                      0       101       `
  ��  d喫A涿Q   � ?new_builder@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVBasicBlock@llvm@@@Z oslexec.dll 
oslexec.dll/    -1                      0       99        `
  ��  d咾?O   � ?new_module@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVModule@llvm@@PEBD@Z oslexec.dll 
oslexec.dll/    -1                      0       90        `
  ��  d嗰臚   � ?noise@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll oslexec.dll/    -1                      0       91        `
  ��  d唒�鸊   � ?normal@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll 
oslexec.dll/    -1                      0       72        `
  ��  d嗁�3�4   � ?nparams@OSLQuery@v1_14_4@OSL@@QEBA_KXZ oslexec.dll oslexec.dll/    -1                      0       89        `
  ��  d哬鐴   � ?null@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll 
oslexec.dll/    -1                      0       112       `
  ��  d喯趾闬   � ?nvptx_target_machine@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVTargetMachine@llvm@@XZ oslexec.dll oslexec.dll/    -1                      0       91        `
  ��  d咮=2蔊   � ?object@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll 
oslexec.dll/    -1                      0       191       `
  ��  d唕!绔   � ?ocio_transform@ColorSystem@pvt@v1_14_4@OSL@@QEBA?AV?$Color3@M@Imath_3_1@@Vustringhash@OpenImageIO_v3_0@@0AEBV56@PEAVShadingContext@34@PEAUShaderGlobals@34@@Z oslexec.dll 
oslexec.dll/    -1                      0       207       `
  ��  d啞H�   � ?ocio_transform@ColorSystem@pvt@v1_14_4@OSL@@QEBA?AV?$Dual@V?$Color3@M@Imath_3_1@@$01@34@Vustringhash@OpenImageIO_v3_0@@0AEBV534@PEAVShadingContext@34@PEAUShaderGlobals@34@@Z oslexec.dll 
oslexec.dll/    -1                      0       113       `
  ��  d哶�0耛   � ?offset_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@0PEAVType@6@@Z oslexec.dll 
oslexec.dll/    -1                      0       113       `
  ��  d嗰�!芣   � ?offset_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@HPEAVType@6@@Z oslexec.dll 
oslexec.dll/    -1                      0       112       `
  ��  d啑>壡\   � ?op_1st_active_lane_of@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z oslexec.dll oslexec.dll/    -1                      0       98        `
  ��  d哊;溔N   � ?op_add@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@0@Z oslexec.dll oslexec.dll/    -1                      0       191       `
  ��  d嗆32霁   � ?op_alloca@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@AEBUTypeDesc@OpenImageIO_v3_0@@HAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@H@Z oslexec.dll 
oslexec.dll/    -1                      0       178       `
  ��  d�+[┯�   � ?op_alloca@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAVPointerType@6@HAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@H@Z oslexec.dll oslexec.dll/    -1                      0       171       `
  ��  d�*&跅   � ?op_alloca@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAVType@6@HAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@H@Z oslexec.dll 
oslexec.dll/    -1                      0       98        `
  ��  d�"�)錘   � ?op_and@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@0@Z oslexec.dll oslexec.dll/    -1                      0       107       `
  ��  d啓!'餡   � ?op_bool_to_float@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z oslexec.dll 
oslexec.dll/    -1                      0       105       `
  ��  d�>XU   � ?op_bool_to_int@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z oslexec.dll 
oslexec.dll/    -1                      0       99        `
  ��  d嗘�O   � ?op_branch@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVBasicBlock@llvm@@@Z oslexec.dll 
oslexec.dll/    -1                      0       112       `
  ��  d�dX臷   � ?op_branch@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVValue@llvm@@PEAVBasicBlock@6@1@Z oslexec.dll oslexec.dll/    -1                      0       113       `
  ��  d�6烤豜     ?op_combine_4x_vectors@LLVM_Util@pvt@v1_14_4@OSL@@AEAAPEAVValue@llvm@@PEAV56@0@Z oslexec.dll 
oslexec.dll/    -1                      0       113       `
  ��  d哅羻鴀    ?op_combine_8x_vectors@LLVM_Util@pvt@v1_14_4@OSL@@AEAAPEAVValue@llvm@@PEAV56@0@Z oslexec.dll 
oslexec.dll/    -1                      0       98        `
  ��  d喫[訬    ?op_div@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@0@Z oslexec.dll oslexec.dll/    -1                      0       95        `
  ��  d啅vE�K    ?op_dowhile@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll 
oslexec.dll/    -1                      0       99        `
  ��  d咷9蚈    ?op_eq@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@0_N@Z oslexec.dll 
oslexec.dll/    -1                      0       92        `
  ��  d哖蝝鵋    ?op_exit@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll oslexec.dll/    -1                      0       102       `
  ��  d哖^頡    ?op_extract@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@0@Z oslexec.dll oslexec.dll/    -1                      0       102       `
  ��  d嗕bk驲    ?op_extract@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@H@Z oslexec.dll oslexec.dll/    -1                      0       98        `
  ��  d唝4L鞱    ?op_fabs@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z oslexec.dll oslexec.dll/    -1                      0       109       `
  ��  d�,熆靁   	 ?op_float_to_double@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z oslexec.dll 
oslexec.dll/    -1                      0       106       `
  ��  d啙�隫   
 ?op_float_to_int@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z oslexec.dll oslexec.dll/    -1                      0       91        `
  ��  d嗿肐驡    ?op_for@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll 
oslexec.dll/    -1                      0       112       `
  ��  d嗁
箴\    ?op_gather@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAVType@6@PEAV56@1@Z oslexec.dll oslexec.dll/    -1                      0       99        `
  ��  d嗁痖萇   
 ?op_ge@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@0_N@Z oslexec.dll 
oslexec.dll/    -1                      0       99        `
  ��  d喭達跲    ?op_gt@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@0_N@Z oslexec.dll 
oslexec.dll/    -1                      0       102       `
  ��  d�;]ｏR    ?op_insert@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@0H@Z oslexec.dll oslexec.dll/    -1                      0       105       `
  ��  d哅伻襏    ?op_int8_to_int@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z oslexec.dll 
oslexec.dll/    -1                      0       105       `
  ��  d咰竳鵘    ?op_int_to_bool@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z oslexec.dll 
oslexec.dll/    -1                      0       106       `
  ��  d唈笊V    ?op_int_to_float@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z oslexec.dll oslexec.dll/    -1                      0       105       `
  ��  d啺鐽錟    ?op_int_to_int8@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z oslexec.dll 
oslexec.dll/    -1                      0       109       `
  ��  d啨啃Y    ?op_int_to_longlong@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z oslexec.dll 
oslexec.dll/    -1                      0       107       `
  ��  d哬j?躓    ?op_is_not_finite@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z oslexec.dll 
oslexec.dll/    -1                      0       119       `
  ��  d啑瑫    ?op_lanes_that_match_masked@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@00@Z oslexec.dll 
oslexec.dll/    -1                      0       99        `
  ��  d員MO    ?op_le@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@0_N@Z oslexec.dll 
oslexec.dll/    -1                      0       115       `
  ��  d�1崴郷    ?op_linearize_16x_indices@LLVM_Util@pvt@v1_14_4@OSL@@AEAAPEAVValue@llvm@@PEAV56@@Z oslexec.dll 
oslexec.dll/    -1                      0       114       `
  ��  d啟顓韃    ?op_linearize_4x_indices@LLVM_Util@pvt@v1_14_4@OSL@@AEAAPEAVValue@llvm@@PEAV56@@Z oslexec.dll oslexec.dll/    -1                      0       114       `
  ��  d�孙    ?op_linearize_8x_indices@LLVM_Util@pvt@v1_14_4@OSL@@AEAAPEAVValue@llvm@@PEAV56@@Z oslexec.dll oslexec.dll/    -1                      0       174       `
  ��  d唴�鞖    ?op_load@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAVType@6@PEAV56@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z oslexec.dll oslexec.dll/    -1                      0       103       `
  ��  d嗭T剪S    ?op_load_mask@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z oslexec.dll 
oslexec.dll/    -1                      0       99        `
  ��  d唩o蚕O    ?op_lt@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@0_N@Z oslexec.dll 
oslexec.dll/    -1                      0       84        `
  ��  d�:MC霡    ?op_masked_break@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXXZ oslexec.dll oslexec.dll/    -1                      0       87        `
  ��  d�66貱    ?op_masked_continue@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXXZ oslexec.dll 
oslexec.dll/    -1                      0       83        `
  ��  d�傋�?     ?op_masked_exit@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXXZ oslexec.dll 
oslexec.dll/    -1                      0       85        `
  ��  d唋铳銩   ! ?op_masked_return@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXXZ oslexec.dll 
oslexec.dll/    -1                      0       97        `
  ��  d咺z餗   " ?op_memcpy@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVValue@llvm@@0HH@Z oslexec.dll 
oslexec.dll/    -1                      0       98        `
  ��  d嗂�8鍺   # ?op_memcpy@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVValue@llvm@@H0HH@Z oslexec.dll oslexec.dll/    -1                      0       97        `
  ��  d唓$呫M   $ ?op_memset@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVValue@llvm@@H0H@Z oslexec.dll 
oslexec.dll/    -1                      0       97        `
  ��  d�[甾M   % ?op_memset@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVValue@llvm@@HHH@Z oslexec.dll 
oslexec.dll/    -1                      0       98        `
  ��  d喨 掺N   & ?op_mod@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@0@Z oslexec.dll oslexec.dll/    -1                      0       98        `
  ��  d��鍺   ' ?op_mul@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@0@Z oslexec.dll oslexec.dll/    -1                      0       99        `
  ��  d唵0抢O   ( ?op_ne@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@0_N@Z oslexec.dll 
oslexec.dll/    -1                      0       97        `
  ��  d哸�蘉   ) ?op_neg@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z oslexec.dll 
oslexec.dll/    -1                      0       97        `
  ��  d嗕$斄M   * ?op_not@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z oslexec.dll 
oslexec.dll/    -1                      0       97        `
  ��  d唹碝鐼   + ?op_or@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@0@Z oslexec.dll 
oslexec.dll/    -1                      0       134       `
  ��  d哾穥鐁   , ?op_quarter_16x@LLVM_Util@pvt@v1_14_4@OSL@@AEAA?AV?$array@PEAVValue@llvm@@$03@std@@PEAVValue@llvm@@@Z oslexec.dll oslexec.dll/    -1                      0       94        `
  ��  d唵酂�J   - ?op_return@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVValue@llvm@@@Z oslexec.dll oslexec.dll/    -1                      0       108       `
  ��  d嗁k谂X   . ?op_scatter@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVValue@llvm@@PEAVType@6@00@Z oslexec.dll oslexec.dll/    -1                      0       102       `
  ��  d唋Е肦   / ?op_select@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@00@Z oslexec.dll oslexec.dll/    -1                      0       98        `
  ��  d�Z轻N   0 ?op_shl@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@0@Z oslexec.dll oslexec.dll/    -1                      0       98        `
  ��  d嗸�臢   1 ?op_shr@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@0@Z oslexec.dll oslexec.dll/    -1                      0       132       `
  ��  d啍@K雙   2 ?op_split_16x@LLVM_Util@pvt@v1_14_4@OSL@@AEAA?AV?$array@PEAVValue@llvm@@$01@std@@PEAVValue@llvm@@@Z oslexec.dll oslexec.dll/    -1                      0       131       `
  ��  d�嗀駉   3 ?op_split_8x@LLVM_Util@pvt@v1_14_4@OSL@@AEAA?AV?$array@PEAVValue@llvm@@$01@std@@PEAVValue@llvm@@@Z oslexec.dll 
oslexec.dll/    -1                      0       94        `
  ��  d�?V天J   4 ?op_store@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVValue@llvm@@0@Z oslexec.dll oslexec.dll/    -1                      0       99        `
  ��  d啰粫隣   5 ?op_store_mask@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVValue@llvm@@0@Z oslexec.dll 
oslexec.dll/    -1                      0       98        `
  ��  d啒b欅N   6 ?op_sub@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@0@Z oslexec.dll oslexec.dll/    -1                      0       103       `
  ��  d�毯躍   7 ?op_unmasked_store@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVValue@llvm@@0@Z oslexec.dll 
oslexec.dll/    -1                      0       93        `
  ��  d��>鏘   8 ?op_while@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll 
oslexec.dll/    -1                      0       98        `
  ��  d單c凁N   9 ?op_xor@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@0@Z oslexec.dll oslexec.dll/    -1                      0       102       `
  ��  d喌PQ諶   : ?op_zero_if@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@0@Z oslexec.dll oslexec.dll/    -1                      0       91        `
  ��  d喍I闓   ; ?optimize_all_groups@ShadingSystem@v1_14_4@OSL@@QEAAXH_N@Z oslexec.dll 
oslexec.dll/    -1                      0       128       `
  ��  d唸条莑   < ?optimize_group@ShadingSystem@v1_14_4@OSL@@QEAAXPEAVShaderGroup@23@HHPEAVShadingContext@23@_N@Z oslexec.dll oslexec.dll/    -1                      0       126       `
  ��  d嗿鯲鰆   = ?optimize_group@ShadingSystem@v1_14_4@OSL@@QEAAXPEAVShaderGroup@23@PEAVShadingContext@23@_N@Z oslexec.dll oslexec.dll/    -1                      0       161       `
  ��  d喛卐鲘   > ?osl_get_attribute@ShadingContext@v1_14_4@OSL@@QEAA_NPEAUShaderGlobals@23@PEAXHVustringhash@OpenImageIO_v3_0@@2HHUTypeDesc@6@1@Z oslexec.dll 
oslexec.dll/    -1                      0       111       `
  ��  d哛 羟[   ? ?oslquery@ShadingSystem@v1_14_4@OSL@@QEAA?AVOSLQuery@23@AEBVShaderGroup@23@H@Z oslexec.dll 
oslexec.dll/    -1                      0       177       `
  ��  d啢\鬂   @ ?parameters@OSLQuery@v1_14_4@OSL@@QEBAAEBV?$vector@UParameter@OSLQuery@v1_14_4@OSL@@V?$allocator@UParameter@OSLQuery@v1_14_4@OSL@@@std@@@std@@XZ oslexec.dll 
oslexec.dll/    -1                      0       95        `
  ��  d哷-a蒏   A ?pcellnoise@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll 
oslexec.dll/    -1                      0       93        `
  ��  d喩�"蒊   B ?periodic@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll 
oslexec.dll/    -1                      0       91        `
  ��  d嗹'疫G   C ?perlin@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll 
oslexec.dll/    -1                      0       95        `
  ��  d咰q｜K   D ?phashnoise@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll 
oslexec.dll/    -1                      0       91        `
  ��  d唻FG   E ?pnoise@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll 
oslexec.dll/    -1                      0       90        `
  ��  d哯5N蒄   F ?point@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll oslexec.dll/    -1                      0       160       `
  ��  d喴捕虒   G ?pointcloud_get@RendererServices@v1_14_4@OSL@@UEAAHPEAUShaderGlobals@23@Vustringhash@OpenImageIO_v3_0@@PEBHH1UTypeDesc@6@PEAX@Z oslexec.dll oslexec.dll/    -1                      0       178       `
  ��  d啅'栭�   H ?pointcloud_search@RendererServices@v1_14_4@OSL@@UEAAHPEAUShaderGlobals@23@Vustringhash@OpenImageIO_v3_0@@AEBV?$Vec3@M@Imath_3_1@@MH_NPEAHPEAMH@Z oslexec.dll oslexec.dll/    -1                      0       195       `
  ��  d�@撽�   I ?pointcloud_write@RendererServices@v1_14_4@OSL@@UEAA_NPEAUShaderGlobals@23@Vustringhash@OpenImageIO_v3_0@@AEBV?$Vec3@M@Imath_3_1@@HPEBV56@PEBUTypeDesc@6@PEAPEBX@Z oslexec.dll 
oslexec.dll/    -1                      0       75        `
  ��  d喬磕�7   J ?popState@Accumulator@v1_14_4@OSL@@QEAAXXZ oslexec.dll 
oslexec.dll/    -1                      0       81        `
  ��  d哶��=   K ?pop_function@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXXZ oslexec.dll 
oslexec.dll/    -1                      0       86        `
  ��  d啇术﨎   L ?pop_function_mask@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXXZ oslexec.dll oslexec.dll/    -1                      0       77        `
  ��  d唵�9   M ?pop_loop@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXXZ oslexec.dll 
oslexec.dll/    -1                      0       77        `
  ��  d啱搻�9   N ?pop_mask@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXXZ oslexec.dll 
oslexec.dll/    -1                      0       84        `
  ��  d哠紧銨   O ?pop_masked_loop@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXXZ oslexec.dll oslexec.dll/    -1                      0       92        `
  ��  d啨dH   P ?pop_masked_return_block@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXXZ oslexec.dll oslexec.dll/    -1                      0       88        `
  ��  d唍 虳   Q ?pop_shader_instance@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXXZ oslexec.dll oslexec.dll/    -1                      0       167       `
  ��  d咷_驌   R ?print_closure@pvt@v1_14_4@OSL@@YAXAEAV?$basic_ostream@DU?$char_traits@D@std@@@std@@PEBUClosureColor@23@PEAVShadingSystemImpl@123@_N@Z oslexec.dll 
oslexec.dll/    -1                      0       158       `
  ��  d�;邢�   S ?printfmt@RendererServices@v1_14_4@OSL@@UEAAXPEAUShaderGlobals@23@Vustringhash@OpenImageIO_v3_0@@HPEBW4EncodedType@23@IPEAE@Z oslexec.dll oslexec.dll/    -1                      0       77        `
  ��  d啣B厄9   T ?process@Reader@journal@v1_14_4@OSL@@QEAAXXZ oslexec.dll 
oslexec.dll/    -1                      0       97        `
  ��  d咥KM   U ?process_entries_for_thread@Reader@journal@v1_14_4@OSL@@AEAAXH@Z oslexec.dll 
oslexec.dll/    -1                      0       84        `
  ��  d�3�:蹳   V ?process_errors@ShadingContext@v1_14_4@OSL@@QEBAXXZ oslexec.dll oslexec.dll/    -1                      0       318       `
  ��  d啫�*  W ?prune_and_internalize_module@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXV?$unordered_set@PEAVFunction@llvm@@U?$hash@PEAVFunction@llvm@@@std@@U?$equal_to@PEAVFunction@llvm@@@4@V?$allocator@PEAVFunction@llvm@@@4@@std@@W4Linkage@1234@PEAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@6@@Z oslexec.dll oslexec.dll/    -1                      0       92        `
  ��  d喕縅霩   X ?psnoise@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll oslexec.dll/    -1                      0       195       `
  ��  d啙�爷   Y ?ptr_cast@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@AEBUTypeDesc@OpenImageIO_v3_0@@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z oslexec.dll 
oslexec.dll/    -1                      0       182       `
  ��  d啇3邰   Z ?ptr_cast@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@PEAVPointerType@6@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z oslexec.dll oslexec.dll/    -1                      0       175       `
  ��  d哴銢   [ ?ptr_cast@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@PEAVType@6@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z oslexec.dll 
oslexec.dll/    -1                      0       178       `
  ��  d唩*o秊   \ ?ptr_to_cast@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@PEAVType@6@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z oslexec.dll oslexec.dll/    -1                      0       108       `
  ��  d嗻鴰鬤   ] ?ptr_to_int64_cast@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z oslexec.dll oslexec.dll/    -1                      0       176       `
  ��  d喰摰詼   ^ ?ptx_compile_group@LLVM_Util@pvt@v1_14_4@OSL@@QEAA_NPEAVModule@llvm@@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAV78@@Z oslexec.dll oslexec.dll/    -1                      0       76        `
  ��  d嗻c椅8   _ ?pushState@Accumulator@v1_14_4@OSL@@QEAAXXZ oslexec.dll oslexec.dll/    -1                      0       109       `
  ��  d嗏\熻Y   ` ?push_function@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVBasicBlock@llvm@@PEAV56@@Z oslexec.dll 
oslexec.dll/    -1                      0       103       `
  ��  d喤�釹   a ?push_function_mask@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVValue@llvm@@@Z oslexec.dll 
oslexec.dll/    -1                      0       100       `
  ��  d啿仨荘   b ?push_loop@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVBasicBlock@llvm@@0@Z oslexec.dll oslexec.dll/    -1                      0       97        `
  ��  d喺綄馦   c ?push_mask@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVValue@llvm@@_N1@Z oslexec.dll 
oslexec.dll/    -1                      0       102       `
  ��  d哰堗R   d ?push_masked_loop@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVValue@llvm@@0@Z oslexec.dll oslexec.dll/    -1                      0       114       `
  ��  d唽�N羄   e ?push_masked_return_block@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVBasicBlock@llvm@@@Z oslexec.dll oslexec.dll/    -1                      0       105       `
  ��  d啽I}闡   f ?push_shader_instance@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVValue@llvm@@@Z oslexec.dll 
oslexec.dll/    -1                      0       117       `
  ��  d嘃~S蔭   g ?query_closure@ShadingSystem@v1_14_4@OSL@@QEAA_NPEAPEBDPEAHPEAPEBUClosureParam@23@@Z oslexec.dll 
oslexec.dll/    -1                      0       92        `
  ��  d�^袶   h ?raytype@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll oslexec.dll/    -1                      0       107       `
  ��  d唓ん鱓   i ?raytype_bit@ShadingSystem@v1_14_4@OSL@@QEAAHVustring@OpenImageIO_v3_0@@@Z oslexec.dll 
oslexec.dll/    -1                      0       115       `
  ��  d啠鋼鎋   j ?raytype_bit@ShadingSystemImpl@pvt@v1_14_4@OSL@@QEAAHVustring@OpenImageIO_v3_0@@@Z oslexec.dll 
oslexec.dll/    -1                      0       90        `
  ��  d問�绰F   k ?rblur@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll oslexec.dll/    -1                      0       188       `
  ��  d啨J镥�   l ?record_error@ShadingContext@v1_14_4@OSL@@QEBAXW4ErrCode@ErrorHandler@OpenImageIO_v3_0@@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z oslexec.dll oslexec.dll/    -1                      0       145       `
  ��  d啫笌讅   m ?record_errorfmt@Writer@journal@v1_14_4@OSL@@QEAA_NHHVustringhash@OpenImageIO_v3_0@@HPEBW4EncodedType@34@IPEAE@Z oslexec.dll 
oslexec.dll/    -1                      0       145       `
  ��  d喴�/鶀   n ?record_filefmt@Writer@journal@v1_14_4@OSL@@QEAA_NHHVustringhash@OpenImageIO_v3_0@@0HPEBW4EncodedType@34@IPEAE@Z oslexec.dll 
oslexec.dll/    -1                      0       145       `
  ��  d嗼骶駗   o ?record_printfmt@Writer@journal@v1_14_4@OSL@@QEAA_NHHVustringhash@OpenImageIO_v3_0@@HPEBW4EncodedType@34@IPEAE@Z oslexec.dll 
oslexec.dll/    -1                      0       90        `
  ��  d哾}灾F   p ?record_runtime_stats@ShadingContext@v1_14_4@OSL@@QEAAXXZ oslexec.dll oslexec.dll/    -1                      0       148       `
  ��  d喎RO鼆   q ?record_warningfmt@Writer@journal@v1_14_4@OSL@@QEAA_NHHHVustringhash@OpenImageIO_v3_0@@HPEBW4EncodedType@34@IPEAE@Z oslexec.dll oslexec.dll/    -1                      0       80        `
  ��  d唋W�<   r ?register_JIT_Global@v1_14_4@OSL@@YAXPEBDPEAX@Z oslexec.dll oslexec.dll/    -1                      0       205       `
  ��  d哢矸酃   s ?register_closure@ShadingSystem@v1_14_4@OSL@@QEAAXV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@HPEBUClosureParam@23@P6AXPEAVRendererServices@23@HPEAX@Z4@Z oslexec.dll 
oslexec.dll/    -1                      0       120       `
  ��  d唩莦莇   t ?register_inline_function@ShadingSystem@v1_14_4@OSL@@QEAAXVustring@OpenImageIO_v3_0@@@Z oslexec.dll oslexec.dll/    -1                      0       122       `
  ��  d�/遞   u ?register_noinline_function@ShadingSystem@v1_14_4@OSL@@QEAAXVustring@OpenImageIO_v3_0@@@Z oslexec.dll oslexec.dll/    -1                      0       106       `
  ��  d嗐蹮赩   v ?release_context@ShadingSystem@v1_14_4@OSL@@QEAAXPEAVShadingContext@23@@Z oslexec.dll oslexec.dll/    -1                      0       101       `
  ��  d唅△誕   w ?renderer@ShadingContext@v1_14_4@OSL@@QEBAPEAVRendererServices@23@XZ oslexec.dll 
oslexec.dll/    -1                      0       100       `
  ��  d喩鱤釶   x ?renderer@ShadingSystem@v1_14_4@OSL@@QEBAPEAVRendererServices@23@XZ oslexec.dll oslexec.dll/    -1                      0       163       `
  ��  d唌>详�   y ?report_error@Report2ErrorHandler@journal@v1_14_4@OSL@@UEAAXHHAEBV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@@Z oslexec.dll 
oslexec.dll/    -1                      0       169       `
  ��  d��?罆   z ?report_file_print@Report2ErrorHandler@journal@v1_14_4@OSL@@UEAAXHHAEBV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@0@Z oslexec.dll 
oslexec.dll/    -1                      0       163       `
  ��  d啢殄購   { ?report_print@Report2ErrorHandler@journal@v1_14_4@OSL@@UEAAXHHAEBV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@@Z oslexec.dll 
oslexec.dll/    -1                      0       165       `
  ��  d喲筚�   | ?report_warning@Report2ErrorHandler@journal@v1_14_4@OSL@@UEAAXHHAEBV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@@Z oslexec.dll 
oslexec.dll/    -1                      0       94        `
  ��  d哤竿鶭   } ?requiredForPageTransition@Writer@journal@v1_14_4@OSL@@CA_KXZ oslexec.dll oslexec.dll/    -1                      0       84        `
  ��  d啠魼   ~ ?reserve_heap@ShadingContext@v1_14_4@OSL@@QEAAX_K@Z oslexec.dll oslexec.dll/    -1                      0       101       `
  ��  d�"鄿鯭    ?return_block@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVBasicBlock@llvm@@XZ oslexec.dll 
oslexec.dll/    -1                      0       88        `
  ��  d�wX褼   � ?rgb@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll oslexec.dll/    -1                      0       91        `
  ��  d嗆瓆轌   � ?rwidth@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll 
oslexec.dll/    -1                      0       90        `
  ��  d哵蛚菷   � ?rwrap@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll oslexec.dll/    -1                      0       89        `
  ��  d哱馮镋   � ?sRGB@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll 
oslexec.dll/    -1                      0       90        `
  ��  d嗼M属F   � ?sblur@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll oslexec.dll/    -1                      0       91        `
  ��  d咲騁谿   � ?screen@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll 
oslexec.dll/    -1                      0       88        `
  ��  d啢蕍驞   � ?setAov@Accumulator@v1_14_4@OSL@@QEAAXHPEAVAov@23@_N1@Z oslexec.dll oslexec.dll/    -1                      0       117       `
  ��  d�愄馻   � ?set_colorspace@ColorSystem@pvt@v1_14_4@OSL@@QEAA_NVustringhash@OpenImageIO_v3_0@@@Z oslexec.dll 
oslexec.dll/    -1                      0       106       `
  ��  d��┹V   � ?set_insert_point@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVBasicBlock@llvm@@@Z oslexec.dll oslexec.dll/    -1                      0       91        `
  ��  d喡肎   � ?set_masking_required@LLVM_Util@pvt@v1_14_4@OSL@@AEAAX_N@Z oslexec.dll 
oslexec.dll/    -1                      0       102       `
  ��  d嗢礡�R   � ?set_raytypes@ShadingSystem@v1_14_4@OSL@@QEAAXPEAVShaderGroup@23@HH@Z oslexec.dll oslexec.dll/    -1                      0       99        `
  ��  d嘄 ㄛO   � ?set_target_isa@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXW4TargetISA@234@@Z oslexec.dll 
oslexec.dll/    -1                      0       104       `
  ��  d喐骉   � ?setup_legacy_optimization_passes@LLVM_Util@pvt@v1_14_4@OSL@@AEAAXH_N@Z oslexec.dll oslexec.dll/    -1                      0       101       `
  ��  d�蚭肣   � ?setup_new_optimization_passes@LLVM_Util@pvt@v1_14_4@OSL@@AEAAXH_N@Z oslexec.dll 
oslexec.dll/    -1                      0       97        `
  ��  d喼�酠   � ?setup_optimization_passes@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXH_N@Z oslexec.dll 
oslexec.dll/    -1                      0       90        `
  ��  d喪孽F   � ?shade@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll oslexec.dll/    -1                      0       255       `
  ��  d�/矝纂   � ?shade_image@v1_14_4@OSL@@YA_NAEAVShadingSystem@12@AEAVShaderGroup@12@PEBUShaderGlobals@12@AEAVImageBuf@OpenImageIO_v3_0@@V?$span@$$CBVustring@OpenImageIO_v3_0@@$0?0@7@W4ShadeImageLocations@12@UROI@7@Vparallel_options@7@@Z oslexec.dll 
oslexec.dll/    -1                      0       245       `
  ��  d嗃汜   � ?shade_image@v1_14_4@OSL@@YA_NAEAVShadingSystem@12@AEAVShaderGroup@12@PEBUShaderGlobals@12@AEAVImageBuf@OpenImageIO_v3_0@@V?$span@$$CBVustring@OpenImageIO_v3_0@@$0?0@7@W4ShadeImageLocations@12@UROI@7@Vparopt@7@@Z oslexec.dll 
oslexec.dll/    -1                      0       91        `
  ��  d営袷鶪   � ?shader@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll 
oslexec.dll/    -1                      0       95        `
  ��  d哠碌鵎   � ?shader_mask@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@XZ oslexec.dll 
oslexec.dll/    -1                      0       102       `
  ��  d嘂{b翿   � ?shadername@OSLQuery@v1_14_4@OSL@@QEBA?BVustring@OpenImageIO_v3_0@@XZ oslexec.dll oslexec.dll/    -1                      0       102       `
  ��  d啩頡   � ?shadertype@OSLQuery@v1_14_4@OSL@@QEBA?BVustring@OpenImageIO_v3_0@@XZ oslexec.dll oslexec.dll/    -1                      0       108       `
  ��  d哖钌薠   � ?shadingsys@ShadingContext@v1_14_4@OSL@@QEBAAEAVShadingSystemImpl@pvt@23@XZ oslexec.dll oslexec.dll/    -1                      0       169       `
  ��  d啅�詴   � ?shouldReportError@TrackRecentlyReported@journal@v1_14_4@OSL@@QEAA_NAEBV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@@Z oslexec.dll 
oslexec.dll/    -1                      0       171       `
  ��  d�銊項   � ?shouldReportWarning@TrackRecentlyReported@journal@v1_14_4@OSL@@QEAA_NAEBV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@@Z oslexec.dll 
oslexec.dll/    -1                      0       94        `
  ��  d唸vq翵   � ?sidedness@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll oslexec.dll/    -1                      0       92        `
  ��  d啿颜錒   � ?simplex@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll oslexec.dll/    -1                      0       97        `
  ��  d唡l=芃   � ?simplexnoise@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll 
oslexec.dll/    -1                      0       95        `
  ��  d啹u唛K   � ?smartcubic@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll 
oslexec.dll/    -1                      0       91        `
  ��  d喐A罣   � ?snoise@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll 
oslexec.dll/    -1                      0       93        `
  ��  d啘e�鞩   � ?subimage@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll 
oslexec.dll/    -1                      0       97        `
  ��  d啿峔麺   � ?subimagename@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll 
oslexec.dll/    -1                      0       143       `
  ��  d啛6o鋥   � ?supports@RendererServices@v1_14_4@OSL@@UEBAHV?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@@Z oslexec.dll 
oslexec.dll/    -1                      0       83        `
  ��  d唓�
�?   � ?supports_avx2@LLVM_Util@pvt@v1_14_4@OSL@@QEBA_NXZ oslexec.dll 
oslexec.dll/    -1                      0       86        `
  ��  d嗞櫛蹷   � ?supports_avx512f@LLVM_Util@pvt@v1_14_4@OSL@@QEBA_NXZ oslexec.dll oslexec.dll/    -1                      0       82        `
  ��  d�(bL�>   � ?supports_avx@LLVM_Util@pvt@v1_14_4@OSL@@QEBA_NXZ oslexec.dll oslexec.dll/    -1                      0       96        `
  ��  d唘R嚷L   � ?supports_isa@LLVM_Util@pvt@v1_14_4@OSL@@SA_NW4TargetISA@234@@Z oslexec.dll oslexec.dll/    -1                      0       102       `
  ��  d��
芌   � ?supports_llvm_bit_masks_natively@LLVM_Util@pvt@v1_14_4@OSL@@QEBA_NXZ oslexec.dll oslexec.dll/    -1                      0       92        `
  ��  d喥奄跦   � ?supports_masked_stores@LLVM_Util@pvt@v1_14_4@OSL@@QEBA_NXZ oslexec.dll oslexec.dll/    -1                      0       91        `
  ��  d�赤褿   � ?swidth@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll 
oslexec.dll/    -1                      0       90        `
  ��  d�y0騀   � ?swrap@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll oslexec.dll/    -1                      0       121       `
  ��  d�*=L膃   � ?symbol@ShadingContext@v1_14_4@OSL@@QEBAPEBVSymbol@pvt@23@Vustring@OpenImageIO_v3_0@@0@Z oslexec.dll 
oslexec.dll/    -1                      0       120       `
  ��  d喆�踕   � ?symbol@ShadingContext@v1_14_4@OSL@@QEBAPEBVSymbol@pvt@23@Vustring@OpenImageIO_v3_0@@@Z oslexec.dll oslexec.dll/    -1                      0       128       `
  ��  d喫�蘬   � ?symbol_address@ShadingSystem@v1_14_4@OSL@@QEBAPEBXAEBVShadingContext@23@PEBVShaderSymbol@23@@Z oslexec.dll oslexec.dll/    -1                      0       102       `
  ��  d哯�頡   � ?symbol_data@ShadingContext@v1_14_4@OSL@@QEBAPEBXAEBVSymbol@pvt@23@@Z oslexec.dll oslexec.dll/    -1                      0       133       `
  ��  d嘂聘衠   � ?symbol_typedesc@ShadingSystem@v1_14_4@OSL@@QEBA?AUTypeDesc@OpenImageIO_v3_0@@PEBVShaderSymbol@23@@Z oslexec.dll 
oslexec.dll/    -1                      0       96        `
  ��  d�	轑   � ?target_isa@LLVM_Util@pvt@v1_14_4@OSL@@QEBA?AW4TargetISA@234@XZ oslexec.dll oslexec.dll/    -1                      0       101       `
  ��  d�1<^餛   � ?target_isa_name@LLVM_Util@pvt@v1_14_4@OSL@@SAPEBDW4TargetISA@234@@Z oslexec.dll 
oslexec.dll/    -1                      0       90        `
  ��  d�7枭F   � ?tblur@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll oslexec.dll/    -1                      0       108       `
  ��  d哾�3臱   � ?test@Accumulator@v1_14_4@OSL@@QEAA_NVustring@OpenImageIO_v3_0@@0PEBV45@0@Z oslexec.dll oslexec.dll/    -1                      0       115       `
  ��  d啙嘿輄   � ?test_if_mask_is_non_zero@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z oslexec.dll 
oslexec.dll/    -1                      0       106       `
  ��  d喍}炽V   � ?test_mask_lane@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@0@Z oslexec.dll oslexec.dll/    -1                      0       106       `
  ��  d啿�#閂   � ?test_mask_lane@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@H@Z oslexec.dll oslexec.dll/    -1                      0       247       `
  ��  d�b获�   � ?texture3d@RendererServices@v1_14_4@OSL@@UEAA_NVustringhash@OpenImageIO_v3_0@@PEAVTextureHandle@TextureSystem@5@PEAVPerthread@75@AEAVTextureOpt_v2@5@PEAUShaderGlobals@23@AEBV?$Vec3@M@Imath_3_1@@555HPEAM666PEAV45@@Z oslexec.dll 
oslexec.dll/    -1                      0       223       `
  ��  d�-缥嗨   � ?texture@RendererServices@v1_14_4@OSL@@UEAA_NVustringhash@OpenImageIO_v3_0@@PEAVTextureHandle@TextureSystem@5@PEAVPerthread@75@AEAVTextureOpt_v2@5@PEAUShaderGlobals@23@MMMMMMHPEAM55PEAV45@@Z oslexec.dll 
oslexec.dll/    -1                      0       135       `
  ��  d�'J舠   � ?texture_thread_info@ShadingContext@v1_14_4@OSL@@QEAAXPEAVPerthread@TextureSystem@OpenImageIO_v3_0@@@Z oslexec.dll 
oslexec.dll/    -1                      0       134       `
  ��  d喨魊   � ?texture_thread_info@ShadingContext@v1_14_4@OSL@@QEBAPEAVPerthread@TextureSystem@OpenImageIO_v3_0@@XZ oslexec.dll oslexec.dll/    -1                      0       117       `
  ��  d唟鮊鵤   � ?texturesys@RendererServices@v1_14_4@OSL@@UEBAPEAVTextureSystem@OpenImageIO_v3_0@@XZ oslexec.dll 
oslexec.dll/    -1                      0       114       `
  ��  d�娫^   � ?texturesys@ShadingSystem@v1_14_4@OSL@@QEBAPEAVTextureSystem@OpenImageIO_v3_0@@XZ oslexec.dll oslexec.dll/    -1                      0       101       `
  ��  d唻軯繯   � ?thread_info@ShadingContext@v1_14_4@OSL@@QEBAPEAUPerThreadInfo@23@XZ oslexec.dll 
oslexec.dll/    -1                      0       89        `
  ��  d喖W淖E   � ?time@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll 
oslexec.dll/    -1                      0       73        `
  ��  d�匚�5   � ?toAlpha@AccumRule@v1_14_4@OSL@@QEBA_NXZ oslexec.dll 
oslexec.dll/    -1                      0       182       `
  ��  d嗢批   � ?to_rgb@ColorSystem@pvt@v1_14_4@OSL@@QEBA?AV?$Color3@M@Imath_3_1@@Vustringhash@OpenImageIO_v3_0@@AEBV56@PEAVShadingContext@34@PEAUShaderGlobals@34@@Z oslexec.dll oslexec.dll/    -1                      0       89        `
  ��  d�(�(闑   � ?total_jit_memory_held@LLVM_Util@pvt@v1_14_4@OSL@@SA_KXZ oslexec.dll 
oslexec.dll/    -1                      0       144       `
  ��  d唥幰遼   � ?trace@RendererServices@v1_14_4@OSL@@UEAA_NAEAUTraceOpt@23@PEAUShaderGlobals@23@AEBV?$Vec3@M@Imath_3_1@@22222@Z oslexec.dll oslexec.dll/    -1                      0       90        `
  ��  d喥煥郌   � ?trace@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll oslexec.dll/    -1                      0       93        `
  ��  d啩埸I   � ?traceset@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll 
oslexec.dll/    -1                      0       201       `
  ��  d喦襳甑   � ?transform_points@RendererServices@v1_14_4@OSL@@UEAA_NPEAUShaderGlobals@23@Vustringhash@OpenImageIO_v3_0@@1MPEBV?$Vec3@M@Imath_3_1@@PEAV78@HW4VECSEMANTICS@TypeDesc@6@@Z oslexec.dll 
oslexec.dll/    -1                      0       187       `
  ��  d唽4萧�   � ?transformc@ColorSystem@pvt@v1_14_4@OSL@@QEBA?AV?$Color3@M@Imath_3_1@@Vustringhash@OpenImageIO_v3_0@@0AEBV56@PEAVShadingContext@34@PEAUShaderGlobals@34@@Z oslexec.dll 
oslexec.dll/    -1                      0       203       `
  ��  d唙h{穹   � ?transformc@ColorSystem@pvt@v1_14_4@OSL@@QEBA?AV?$Dual@V?$Color3@M@Imath_3_1@@$01@34@Vustringhash@OpenImageIO_v3_0@@0AEBV534@PEAVShadingContext@34@PEAUShaderGlobals@34@@Z oslexec.dll 
oslexec.dll/    -1                      0       91        `
  ��  d唎鱦霨   � ?twidth@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll 
oslexec.dll/    -1                      0       90        `
  ��  d哹r 螰   � ?twrap@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll oslexec.dll/    -1                      0       95        `
  ��  d嗱*蛮K   � ?type_addrint@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ oslexec.dll 
oslexec.dll/    -1                      0       101       `
  ��  d�6Q   � ?type_array@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVType@llvm@@PEAV56@H@Z oslexec.dll 
oslexec.dll/    -1                      0       92        `
  ��  d唌懧鮄   � ?type_bool@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ oslexec.dll oslexec.dll/    -1                      0       103       `
  ��  d咥掠轘   � ?type_bool_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVPointerType@llvm@@XZ oslexec.dll 
oslexec.dll/    -1                      0       92        `
  ��  d嗂V诱H   � ?type_char@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ oslexec.dll oslexec.dll/    -1                      0       103       `
  ��  d唶C骃   � ?type_char_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVPointerType@llvm@@XZ oslexec.dll 
oslexec.dll/    -1                      0       94        `
  ��  d�<腱颙   � ?type_double@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ oslexec.dll oslexec.dll/    -1                      0       105       `
  ��  d啨浺U   � ?type_double_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVPointerType@llvm@@XZ oslexec.dll 
oslexec.dll/    -1                      0       93        `
  ��  d啓律腎   � ?type_float@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ oslexec.dll 
oslexec.dll/    -1                      0       104       `
  ��  d�!T   � ?type_float_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVPointerType@llvm@@XZ oslexec.dll oslexec.dll/    -1                      0       163       `
  ��  d員唶鞆   � ?type_function@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVFunctionType@llvm@@PEAVType@6@V?$span@QEAVType@llvm@@$0?0@OpenImageIO_v3_0@@_N@Z oslexec.dll 
oslexec.dll/    -1                      0       166       `
  ��  d喆鯤覓   � ?type_function_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVPointerType@llvm@@PEAVType@6@V?$span@QEAVType@llvm@@$0?0@OpenImageIO_v3_0@@_N@Z oslexec.dll oslexec.dll/    -1                      0       93        `
  ��  d啍rE薎   � ?type_int16@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ oslexec.dll 
oslexec.dll/    -1                      0       93        `
  ��  d喦悒覫   � ?type_int64@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ oslexec.dll 
oslexec.dll/    -1                      0       104       `
  ��  d�	籎繲   � ?type_int64_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVPointerType@llvm@@XZ oslexec.dll oslexec.dll/    -1                      0       92        `
  ��  d�)f�H   � ?type_int8@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ oslexec.dll oslexec.dll/    -1                      0       103       `
  ��  d嗠[>罶   � ?type_int8_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVPointerType@llvm@@XZ oslexec.dll 
oslexec.dll/    -1                      0       91        `
  ��  d喨蚥頖   � ?type_int@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ oslexec.dll 
oslexec.dll/    -1                      0       102       `
  ��  d哰S
蚏   � ?type_int_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVPointerType@llvm@@XZ oslexec.dll oslexec.dll/    -1                      0       96        `
  ��  d�_錖   � ?type_longlong@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ oslexec.dll oslexec.dll/    -1                      0       107       `
  ��  d唘`肯W   � ?type_longlong_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVPointerType@llvm@@XZ oslexec.dll 
oslexec.dll/    -1                      0       94        `
  ��  d�)}嵝J   � ?type_matrix@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ oslexec.dll oslexec.dll/    -1                      0       105       `
  ��  d啠绾襏   � ?type_matrix_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVPointerType@llvm@@XZ oslexec.dll 
oslexec.dll/    -1                      0       99        `
  ��  d啢榸錙   � ?type_native_mask@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ oslexec.dll 
oslexec.dll/    -1                      0       109       `
  ��  d嗭U臲   � ?type_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVPointerType@llvm@@PEAVType@6@@Z oslexec.dll 
oslexec.dll/    -1                      0       100       `
  ��  d嗚靕鍼   � ?type_real_ustring@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVType@llvm@@XZ oslexec.dll oslexec.dll/    -1                      0       207       `
  ��  d唘2鸹   � ?type_struct@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVType@llvm@@V?$span@QEAVType@llvm@@$0?0@OpenImageIO_v3_0@@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@_N@Z oslexec.dll 
oslexec.dll/    -1                      0       117       `
  ��  d�2v筮a   � ?type_struct_field_at_index@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVType@llvm@@PEAV56@H@Z oslexec.dll 
oslexec.dll/    -1                      0       94        `
  ��  d唫ひ跩   � ?type_triple@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ oslexec.dll oslexec.dll/    -1                      0       105       `
  ��  d唡�(馯   � ?type_triple_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVPointerType@llvm@@XZ oslexec.dll 
oslexec.dll/    -1                      0       96        `
  ��  d啑ㄢ鮈   � ?type_typedesc@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ oslexec.dll oslexec.dll/    -1                      0       139       `
  ��  d啙l樑w   � ?type_union@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVType@llvm@@V?$span@QEAVType@llvm@@$0?0@OpenImageIO_v3_0@@@Z oslexec.dll 
oslexec.dll/    -1                      0       95        `
  ��  d唎鋌薑   � ?type_ustring@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVType@llvm@@XZ oslexec.dll 
oslexec.dll/    -1                      0       106       `
  ��  d�0�
訴   � ?type_ustring_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVPointerType@llvm@@XZ oslexec.dll oslexec.dll/    -1                      0       92        `
  ��  d�藊鉎   � ?type_void@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ oslexec.dll oslexec.dll/    -1                      0       103       `
  ��  d嗱_鱏   � ?type_void_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVPointerType@llvm@@XZ oslexec.dll 
oslexec.dll/    -1                      0       99        `
  ��  d�9繶誒   � ?type_wide@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVType@llvm@@PEAV56@@Z oslexec.dll 
oslexec.dll/    -1                      0       97        `
  ��  d唜^*罬   � ?type_wide_bool@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ oslexec.dll 
oslexec.dll/    -1                      0       108       `
  ��  d嗋W点X   � ?type_wide_bool_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVPointerType@llvm@@XZ oslexec.dll oslexec.dll/    -1                      0       97        `
  ��  d嗞穌襇   � ?type_wide_char@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ oslexec.dll 
oslexec.dll/    -1                      0       108       `
  ��  d啍j4罼   � ?type_wide_char_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVPointerType@llvm@@XZ oslexec.dll oslexec.dll/    -1                      0       99        `
  ��  d�裒翺   � ?type_wide_double@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ oslexec.dll 
oslexec.dll/    -1                      0       98        `
  ��  d嗇艾齆   � ?type_wide_float@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ oslexec.dll oslexec.dll/    -1                      0       109       `
  ��  d�z蚘   � ?type_wide_float_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVPointerType@llvm@@XZ oslexec.dll 
oslexec.dll/    -1                      0       96        `
  ��  d唖蒍蒐   � ?type_wide_int@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ oslexec.dll oslexec.dll/    -1                      0       107       `
  ��  d嗧束W   � ?type_wide_int_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVPointerType@llvm@@XZ oslexec.dll 
oslexec.dll/    -1                      0       101       `
  ��  d咵会裃   � ?type_wide_longlong@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ oslexec.dll 
oslexec.dll/    -1                      0       99        `
  ��  d哘x蘋   � ?type_wide_matrix@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ oslexec.dll 
oslexec.dll/    -1                      0       99        `
  ��  d唺�(轔   � ?type_wide_triple@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ oslexec.dll 
oslexec.dll/    -1                      0       100       `
  ��  d�憊芇   � ?type_wide_ustring@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ oslexec.dll oslexec.dll/    -1                      0       111       `
  ��  d喤央蒣   � ?type_wide_ustring_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVPointerType@llvm@@XZ oslexec.dll 
oslexec.dll/    -1                      0       101       `
  ��  d啺粄蔘   � ?type_wide_void_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEBAPEAVType@llvm@@XZ oslexec.dll 
oslexec.dll/    -1                      0       86        `
  ��  d啌鯜袯   � ?u@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll oslexec.dll/    -1                      0       105       `
  ��  d唃恛郩   � ?uninitialized_string@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll 
oslexec.dll/    -1                      0       92        `
  ��  d唫Bc蘃   � ?unknown@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll oslexec.dll/    -1                      0       122       `
  ��  d啣e踗   � ?unregister_inline_function@ShadingSystem@v1_14_4@OSL@@QEAAXVustring@OpenImageIO_v3_0@@@Z oslexec.dll oslexec.dll/    -1                      0       124       `
  ��  d喡泷踙     ?unregister_noinline_function@ShadingSystem@v1_14_4@OSL@@QEAAXVustring@OpenImageIO_v3_0@@@Z oslexec.dll oslexec.dll/    -1                      0       90        `
  ��  d嗱裮貴    ?unull@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll oslexec.dll/    -1                      0       92        `
  ��  d�5敤譎    ?uperlin@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll oslexec.dll/    -1                      0       93        `
  ��  d哸ム錓    ?useparam@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll 
oslexec.dll/    -1                      0       93        `
  ��  d�氕鉏    ?usimplex@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll 
oslexec.dll/    -1                      0       98        `
  ��  d嗢3'錘    ?usimplexnoise@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll oslexec.dll/    -1                      0       98        `
  ��  d啿bC鍺    ?ustring_rep@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXW4UstringRep@1234@@Z oslexec.dll oslexec.dll/    -1                      0       99        `
  ��  d啨r肜O    ?ustring_rep@LLVM_Util@pvt@v1_14_4@OSL@@QEBA?AW4UstringRep@1234@XZ oslexec.dll 
oslexec.dll/    -1                      0       86        `
  ��  d哱笂驜    ?v@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll oslexec.dll/    -1                      0       252       `
  ��  d啌刊箬   	 ?validate_global_mappings@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXAEAV?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@@Z oslexec.dll oslexec.dll/    -1                      0       151       `
  ��  d�L^蹆   
 ?validate_struct_data_layout@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEAVType@llvm@@AEBV?$vector@IV?$allocator@I@std@@@std@@@Z oslexec.dll 
oslexec.dll/    -1                      0       91        `
  ��  d哫��G    ?vector@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll 
oslexec.dll/    -1                      0       164       `
  ��  d嗊rD蛺    ?void_ptr@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z oslexec.dll oslexec.dll/    -1                      0       100       `
  ��  d啝|1镻   
 ?void_ptr_null@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@XZ oslexec.dll oslexec.dll/    -1                      0       160       `
  ��  d唖欬褜    ?warningfmt@RendererServices@v1_14_4@OSL@@UEAAXPEAUShaderGlobals@23@Vustringhash@OpenImageIO_v3_0@@HPEBW4EncodedType@23@IPEAE@Z oslexec.dll oslexec.dll/    -1                      0       101       `
  ��  d唍(頠    ?wide_constant@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@H@Z oslexec.dll 
oslexec.dll/    -1                      0       102       `
  ��  d唅(.驲    ?wide_constant@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@HH@Z oslexec.dll oslexec.dll/    -1                      0       102       `
  ��  d�0Z疸R    ?wide_constant@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@HM@Z oslexec.dll oslexec.dll/    -1                      0       101       `
  ��  d唍�逹    ?wide_constant@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@M@Z oslexec.dll 
oslexec.dll/    -1                      0       107       `
  ��  d喯#]豔    ?wide_constant@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@PEAV56@@Z oslexec.dll 
oslexec.dll/    -1                      0       102       `
  ��  d唊錱覴    ?wide_constant@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@_K@Z oslexec.dll oslexec.dll/    -1                      0       160       `
  ��  d�
槜鶎    ?wide_constant@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@V?$basic_string_view@DU?$char_traits@D@std@@@OpenImageIO_v3_0@@@Z oslexec.dll oslexec.dll/    -1                      0       124       `
  ��  d� e
詇    ?wide_constant@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@Vustring@OpenImageIO_v3_0@@@Z oslexec.dll oslexec.dll/    -1                      0       107       `
  ��  d嘃|雷W    ?wide_constant_bool@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVConstant@llvm@@_N@Z oslexec.dll 
oslexec.dll/    -1                      0       196       `
  ��  d唘
	    ?wide_op_alloca@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@AEBUTypeDesc@OpenImageIO_v3_0@@HAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@H@Z oslexec.dll oslexec.dll/    -1                      0       135       `
  ��  d啑�8鷖    ?wide_ptr_cast@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@AEBUTypeDesc@OpenImageIO_v3_0@@@Z oslexec.dll 
oslexec.dll/    -1                      0       102       `
  ��  d啺賾鯮    ?widen_value@LLVM_Util@pvt@v1_14_4@OSL@@QEAAPEAVValue@llvm@@PEAV56@@Z oslexec.dll oslexec.dll/    -1                      0       90        `
  ��  d喚"復F    ?width@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll oslexec.dll/    -1                      0       90        `
  ��  d啴嬎鞦    ?world@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll oslexec.dll/    -1                      0       89        `
  ��  d唡4斿E    ?wrap@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll 
oslexec.dll/    -1                      0       156       `
  ��  d嗼 颊�    ?write_bitcode_file@LLVM_Util@pvt@v1_14_4@OSL@@QEAAXPEBDPEAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z oslexec.dll oslexec.dll/    -1                      0       160       `
  ��  d�?%V軐    ?write_entry@Writer@journal@v1_14_4@OSL@@AEAA_NHHW4Content@pvt@234@Vustringhash@OpenImageIO_v3_0@@1HPEBW4EncodedType@34@IPEAE@Z oslexec.dll oslexec.dll/    -1                      0       88        `
  ��  d�|n驞     ?xyY@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll oslexec.dll/    -1                      0       88        `
  ��  d唩4P貲   ! ?xyz@Strings@v1_14_4@OSL@@3Vustring@OpenImageIO_v3_0@@B oslexec.dll 